#!/usr/bin/env python3
"""
Script to check certification count in the database
"""
import os
import sys
from sqlalchemy import create_engine, text

# Set up database connection
DATABASE_URL = os.environ.get('DATABASE_URL')
if not DATABASE_URL:
    print("ERROR: DATABASE_URL environment variable is not set")
    sys.exit(1)

try:
    # Create engine
    engine = create_engine(
        DATABASE_URL,
        connect_args={"sslmode": "require"},
        echo=False
    )
    
    # Connect to database
    with engine.connect() as connection:
        # Check certification count
        cert_count = connection.execute(text("SELECT COUNT(*) FROM certifications WHERE is_deleted = FALSE")).scalar()
        print(f"Number of active certifications in database: {cert_count}")
        
        # Sample certification data
        result = connection.execute(text("SELECT id, name, domain, level FROM certifications WHERE is_deleted = FALSE LIMIT 5"))
        print("\nSample certifications:")
        for row in result:
            print(f"ID: {row[0]}, Name: {row[1]}, Domain: {row[2]}, Level: {row[3]}")
        
        # Check organizations
        org_count = connection.execute(text("SELECT COUNT(*) FROM organizations WHERE is_deleted = FALSE")).scalar()
        print(f"\nNumber of organizations: {org_count}")
        
        # Check domains
        domain_count = connection.execute(text("SELECT COUNT(*) FROM domains WHERE is_deleted = FALSE")).scalar()
        print(f"Number of domains: {domain_count}")
        
except Exception as e:
    print(f"ERROR: {e}")
    sys.exit(1) 