#!/usr/bin/env python3
"""
Automated script to transfer data from certification_explorer to certifications table using SQL
"""
import os
import sys
import logging
from sqlalchemy import create_engine, text

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def transfer_certifications(force=False):
    """Transfer data from certification_explorer to certifications using direct SQL"""
    try:
        # Get database connection from environment or use default
        db_url = os.environ.get('DATABASE_URL', '***************************************************')
        logger.info(f"Connecting to database: {db_url}")
        
        # Create engine and connection
        engine = create_engine(db_url)
        conn = engine.connect()
        
        # Check if certifications table is empty
        result = conn.execute(text("SELECT COUNT(*) FROM certifications"))
        cert_count = result.scalar()
        logger.info(f"Current count in certifications table: {cert_count}")
        
        # Check certification_explorer count
        result = conn.execute(text("SELECT COUNT(*) FROM certification_explorer"))
        explorer_count = result.scalar()
        logger.info(f"Current count in certification_explorer table: {explorer_count}")
        
        if explorer_count == 0:
            logger.warning("No data in certification_explorer table. Nothing to transfer.")
            return
        
        if cert_count > 0 and not force:
            logger.warning(f"Certifications table already has {cert_count} records.")
            logger.info("Add --force to the command to proceed anyway.")
            return
        elif cert_count > 0:
            logger.warning(f"Force mode: proceeding with transfer despite existing {cert_count} records.")
        
        # First get or create organizations from providers
        logger.info("Creating organizations from providers...")
        
        # Get unique providers from certification_explorer
        result = conn.execute(text("SELECT DISTINCT provider FROM certification_explorer"))
        providers = [row[0] for row in result]
        logger.info(f"Found {len(providers)} unique providers")
        
        # Create a transaction
        trans = conn.begin()
        try:
            # Create organizations if they don't exist
            for provider in providers:
                # Check if organization exists
                result = conn.execute(
                    text("SELECT id FROM organizations WHERE name = :name"),
                    {"name": provider}
                )
                org = result.fetchone()
                
                if not org:
                    # Create organization
                    conn.execute(
                        text("""
                            INSERT INTO organizations (name, country, description, is_deleted, created_at, updated_at)
                            VALUES (:name, 'Unknown', :description, false, NOW(), NOW())
                        """),
                        {"name": provider, "description": f"Provider of certifications"}
                    )
                    logger.info(f"Created organization: {provider}")
            
            # Now transfer certifications with organization IDs
            logger.info("Transferring certifications...")
            
            # Get all certifications from explorer that don't exist in certifications table
            result = conn.execute(text("""
                SELECT ce.*, o.id as org_id
                FROM certification_explorer ce
                JOIN organizations o ON ce.provider = o.name
                WHERE NOT EXISTS (
                    SELECT 1 FROM certifications c WHERE c.name = ce.name
                )
            """))
            certs_to_transfer = result.fetchall()
            
            logger.info(f"Found {len(certs_to_transfer)} certifications to transfer")
            
            # Insert certifications
            transferred_count = 0
            error_count = 0
            
            for cert in certs_to_transfer:
                try:
                    # Extract type, level and domain values safely
                    type_value = str(cert.type.value) if hasattr(cert, 'type') and cert.type else "security"
                    level_value = str(cert.level.value) if hasattr(cert, 'level') and cert.level else "beginner"
                    domain_value = str(cert.domain.value) if hasattr(cert, 'domain') and cert.domain else "Security Management"
                    
                    # Format prerequisites as comma-separated string if available
                    prerequisites = None
                    if hasattr(cert, 'prerequisites_list') and cert.prerequisites_list:
                        if isinstance(cert.prerequisites_list, list):
                            prerequisites = ','.join(cert.prerequisites_list)
                        else:
                            prerequisites = str(cert.prerequisites_list)
                    
                    # Insert the certification
                    conn.execute(
                        text("""
                            INSERT INTO certifications (
                                name, category, domain, level, difficulty, cost,
                                description, prerequisites, exam_code, organization_id,
                                url, is_deleted, created_at, last_updated, current_version
                            ) VALUES (
                                :name, :category, :domain, :level, 3, :cost,
                                :description, :prerequisites, :exam_code, :org_id,
                                :url, false, NOW(), NOW(), 1
                            )
                        """),
                        {
                            "name": cert.name,
                            "category": type_value,
                            "domain": domain_value,
                            "level": level_value,
                            "cost": cert.price,
                            "description": cert.description,
                            "prerequisites": prerequisites,
                            "exam_code": cert.exam_code,
                            "org_id": cert.org_id,
                            "url": cert.url
                        }
                    )
                    transferred_count += 1
                    
                    if transferred_count % 50 == 0:
                        logger.info(f"Transferred {transferred_count} certifications so far...")
                
                except Exception as e:
                    logger.error(f"Error transferring certification {cert.name}: {e}")
                    error_count += 1
            
            # Commit the transaction
            trans.commit()
            
            # Final count check
            result = conn.execute(text("SELECT COUNT(*) FROM certifications"))
            final_count = result.scalar()
            
            logger.info(f"Transfer completed. {transferred_count} certifications transferred, {error_count} errors.")
            logger.info(f"Final count in certifications table: {final_count}")
            
        except Exception as e:
            trans.rollback()
            logger.error(f"Transaction error, rolling back: {e}")
            raise
        
    except Exception as e:
        logger.error(f"Error during data transfer: {e}")
    finally:
        conn.close()
        engine.dispose()

if __name__ == "__main__":
    force = "--force" in sys.argv
    transfer_certifications(force=force) 