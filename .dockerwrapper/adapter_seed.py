#!/usr/bin/env python3
"""
Adapted seeding script to ensure certifications are saved to the 'certifications' table
and also provides functionality to copy from certification_explorer to certifications.
"""
import sys
import os
import json
import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Make the current directory accessible for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the original seed functions
try:
    from data.seed import load_certification_data
    from database import init_db, get_db
    from models.certification import Certification, Organization, Domain
    using_models = True
    logger.info("Successfully imported models and seed functions")
except ImportError as e:
    using_models = False
    logger.warning(f"Could not import models: {e}. Will use direct SQL instead.")

def get_connection():
    """Get a database connection either through ORM or direct connection"""
    if using_models:
        return next(get_db())
    else:
        # Direct connection
        db_url = os.environ.get('DATABASE_URL', '***************************************************')
        engine = create_engine(db_url)
        return engine.connect()

def ensure_right_table():
    """
    Ensure data is loaded into the 'certifications' table.
    This function also checks if data exists in certification_explorer but not in certifications,
    and offers to copy it.
    """
    conn = get_connection()
    
    try:
        # Check certifications table count
        if using_models:
            cert_count = conn.query(Certification).count()
        else:
            result = conn.execute(text("SELECT COUNT(*) FROM certifications"))
            cert_count = result.scalar()
        
        logger.info(f"Current count in certifications table: {cert_count}")
        
        # Check certification_explorer table count
        if using_models:
            # Not ideal but safer than importing the model
            result = conn.execute(text("SELECT COUNT(*) FROM certification_explorer"))
            explorer_count = result.scalar()
        else:
            result = conn.execute(text("SELECT COUNT(*) FROM certification_explorer"))
            explorer_count = result.scalar()
        
        logger.info(f"Current count in certification_explorer table: {explorer_count}")
        
        # If certifications table is empty but certification_explorer has data
        if cert_count == 0 and explorer_count > 0:
            copy_confirm = input(f"Found {explorer_count} records in certification_explorer but certifications table is empty. Copy data? (y/n): ")
            if copy_confirm.lower() == 'y':
                copy_from_explorer_to_certifications()
                return True
            
        # If both tables have data, let user choose
        elif cert_count > 0 and explorer_count > 0:
            compare_confirm = input(f"Found {cert_count} certifications and {explorer_count} explorer records. Compare and sync? (y/n): ")
            if compare_confirm.lower() == 'y':
                compare_and_sync_tables()
                return True
    
    except Exception as e:
        logger.error(f"Error checking tables: {e}")
    finally:
        if not using_models and conn:
            conn.close()
    
    return False

def copy_from_explorer_to_certifications():
    """Copy data from certification_explorer to certifications"""
    if using_models:
        logger.info("Using ORM to copy data")
        copy_using_orm()
    else:
        logger.info("Using direct SQL to copy data")
        copy_using_sql()

def copy_using_orm():
    """Copy data using SQLAlchemy ORM"""
    from models.certification_explorer import CertificationExplorer
    
    db = next(get_db())
    try:
        # Get all records from certification_explorer
        explorer_certs = db.query(CertificationExplorer).all()
        logger.info(f"Found {len(explorer_certs)} certifications to copy")
        
        organizations = {}
        success_count = 0
        error_count = 0
        
        # Process organizations
        for cert in explorer_certs:
            org_name = cert.provider
            if org_name not in organizations:
                org = db.query(Organization).filter_by(name=org_name).first()
                if not org:
                    org = Organization(
                        name=org_name,
                        country="Unknown",
                        url=None,
                        description=f"Provider of certifications"
                    )
                    db.add(org)
                    db.flush()
                    logger.info(f"Created organization: {org_name}")
                organizations[org_name] = org
        
        db.commit()
        logger.info(f"Processed {len(organizations)} organizations")
        
        # Process certifications
        for cert in explorer_certs:
            try:
                # Check if certification already exists
                existing = db.query(Certification).filter_by(name=cert.name).first()
                if existing:
                    logger.info(f"Certification {cert.name} already exists. Skipping.")
                    continue
                
                # Extract values safely
                level_value = str(cert.level.value) if hasattr(cert, 'level') and cert.level else "beginner"
                domain_value = str(cert.domain.value) if hasattr(cert, 'domain') and cert.domain else "General Security"
                
                # Format prerequisites
                prerequisites = None
                if hasattr(cert, 'prerequisites_list') and cert.prerequisites_list:
                    if isinstance(cert.prerequisites_list, list):
                        prerequisites = ','.join(cert.prerequisites_list)
                    else:
                        prerequisites = str(cert.prerequisites_list)
                
                # Create certification
                new_cert = Certification(
                    name=cert.name,
                    category=str(cert.type.value) if hasattr(cert, 'type') and cert.type else "security",
                    domain=domain_value,
                    level=level_value,
                    difficulty=3,  # Default value
                    cost=cert.price,
                    description=cert.description,
                    prerequisites=prerequisites,
                    validity_period=None,  # Not available in explorer model
                    exam_code=cert.exam_code,
                    organization_id=organizations[cert.provider].id if cert.provider in organizations else None,
                    url=cert.url,
                    custom_hours=cert.duration_minutes // 60 if hasattr(cert, 'duration_minutes') and cert.duration_minutes else None
                )
                
                db.add(new_cert)
                success_count += 1
                
                if success_count % 100 == 0:
                    db.commit()
                    logger.info(f"Copied {success_count} certifications so far...")
            
            except Exception as e:
                logger.error(f"Error copying certification {cert.name}: {e}")
                error_count += 1
        
        db.commit()
        logger.info(f"Copy completed. {success_count} certifications copied, {error_count} errors.")
        
        # Check final count
        final_count = db.query(Certification).count()
        logger.info(f"Final count in certifications table: {final_count}")
    
    except Exception as e:
        logger.error(f"Error during ORM copy: {e}")
        db.rollback()
    finally:
        db.close()

def copy_using_sql():
    """Copy data using direct SQL queries"""
    db_url = os.environ.get('DATABASE_URL', '***************************************************')
    engine = create_engine(db_url)
    conn = engine.connect()
    
    try:
        # Create transaction
        trans = conn.begin()
        
        # First create organizations
        result = conn.execute(text("SELECT DISTINCT provider FROM certification_explorer"))
        providers = [row[0] for row in result]
        
        orgs_map = {}
        for provider in providers:
            # Check if organization exists
            result = conn.execute(
                text("SELECT id FROM organizations WHERE name = :name"),
                {"name": provider}
            )
            org = result.fetchone()
            
            if not org:
                # Create organization
                result = conn.execute(
                    text("""
                        INSERT INTO organizations 
                        (name, country, description, is_deleted, created_at, updated_at) 
                        VALUES (:name, 'Unknown', :description, false, NOW(), NOW())
                        RETURNING id
                    """),
                    {"name": provider, "description": f"Provider of certifications"}
                )
                org_id = result.fetchone()[0]
                orgs_map[provider] = org_id
                logger.info(f"Created organization: {provider}")
            else:
                orgs_map[provider] = org[0]
        
        # Now copy certifications
        result = conn.execute(text("""
            SELECT ce.*, o.id as org_id
            FROM certification_explorer ce
            JOIN organizations o ON ce.provider = o.name
            WHERE NOT EXISTS (
                SELECT 1 FROM certifications c WHERE c.name = ce.name
            )
        """))
        
        certs_to_copy = result.fetchall()
        logger.info(f"Found {len(certs_to_copy)} certifications to copy")
        
        copied_count = 0
        error_count = 0
        
        for cert in certs_to_copy:
            try:
                # Extract values safely
                type_value = str(cert.type.value) if hasattr(cert, 'type') and cert.type else "security"
                level_value = str(cert.level.value) if hasattr(cert, 'level') and cert.level else "beginner"
                domain_value = str(cert.domain.value) if hasattr(cert, 'domain') and cert.domain else "General Security"
                
                # Format prerequisites
                prerequisites = None
                if hasattr(cert, 'prerequisites_list') and cert.prerequisites_list:
                    if isinstance(cert.prerequisites_list, list):
                        prerequisites = ','.join(cert.prerequisites_list)
                    else:
                        prerequisites = str(cert.prerequisites_list)
                
                conn.execute(
                    text("""
                        INSERT INTO certifications (
                            name, category, domain, level, difficulty, cost,
                            description, prerequisites, exam_code, organization_id,
                            url, is_deleted, created_at, last_updated, current_version
                        ) VALUES (
                            :name, :category, :domain, :level, 3, :cost,
                            :description, :prerequisites, :exam_code, :org_id,
                            :url, false, NOW(), NOW(), 1
                        )
                    """),
                    {
                        "name": cert.name,
                        "category": type_value,
                        "domain": domain_value,
                        "level": level_value,
                        "cost": cert.price,
                        "description": cert.description,
                        "prerequisites": prerequisites,
                        "exam_code": cert.exam_code,
                        "org_id": cert.org_id,
                        "url": cert.url
                    }
                )
                copied_count += 1
                
                if copied_count % 50 == 0:
                    logger.info(f"Copied {copied_count} certifications so far...")
            
            except Exception as e:
                logger.error(f"Error copying certification {cert.name}: {e}")
                error_count += 1
        
        # Commit transaction
        trans.commit()
        
        # Final count check
        result = conn.execute(text("SELECT COUNT(*) FROM certifications"))
        final_count = result.scalar()
        logger.info(f"Copy completed. {copied_count} certifications copied, {error_count} errors.")
        logger.info(f"Final count in certifications table: {final_count}")
    
    except Exception as e:
        trans.rollback()
        logger.error(f"Error during SQL copy: {e}")
    finally:
        conn.close()
        engine.dispose()

def compare_and_sync_tables():
    """Compare certification_explorer and certifications tables and sync missing records"""
    db_url = os.environ.get('DATABASE_URL', '***************************************************')
    engine = create_engine(db_url)
    conn = engine.connect()
    
    try:
        # Find certifications that exist in explorer but not in certifications
        result = conn.execute(text("""
            SELECT COUNT(*) FROM certification_explorer ce
            WHERE NOT EXISTS (
                SELECT 1 FROM certifications c WHERE c.name = ce.name
            )
        """))
        missing_count = result.scalar()
        
        if missing_count == 0:
            logger.info("No missing certifications found. Tables are in sync.")
            return
        
        logger.info(f"Found {missing_count} certifications in explorer that are not in certifications table")
        
        sync_confirm = input(f"Would you like to copy these {missing_count} missing certifications? (y/n): ")
        if sync_confirm.lower() == 'y':
            copy_from_explorer_to_certifications()
    
    except Exception as e:
        logger.error(f"Error comparing tables: {e}")
    finally:
        conn.close()
        engine.dispose()

def load_directly():
    """Load certification data directly from the JSON file into certifications table"""
    try:
        # Direct loading using regular seed process to certifications
        if using_models:
            db = next(get_db())
            from data.seed import import_certifications
            cert_success, cert_errors = import_certifications(db)
            logger.info(f"Database seeded with {cert_success} certifications ({cert_errors} errors)")
        else:
            # Use our direct_seed method
            logger.info("Using direct SQL seeding (models not available)")
            direct_seed()
    except Exception as e:
        logger.error(f"Error during direct loading: {e}")

def direct_seed():
    """Seed directly using SQL (backup option if ORM fails)"""
    db_url = os.environ.get('DATABASE_URL', '***************************************************')
    engine = create_engine(db_url)
    conn = engine.connect()
    
    try:
        # Load JSON data
        json_file = os.path.join('data', 'normalized_certifications.json')
        if not os.path.exists(json_file):
            logger.error(f"JSON file not found at {json_file}")
            return
        
        with open(json_file, 'r', encoding='utf-8') as f:
            certifications_list = json.load(f)
        
        logger.info(f"Loaded {len(certifications_list)} certifications from JSON")
        
        # Check existing count
        result = conn.execute(text("SELECT COUNT(*) FROM certifications"))
        existing_count = result.scalar()
        
        if existing_count > 0:
            proceed = input(f"Found {existing_count} existing certifications. Continue with import? (y/n): ")
            if proceed.lower() != 'y':
                logger.info("Import cancelled by user.")
                return
        
        # Start transaction
        trans = conn.begin()
        
        # Track organizations
        orgs_created = 0
        orgs_map = {}
        
        # Process organizations first
        for cert in certifications_list:
            # Determine organization
            name = cert['name']
            org_name = 'Unknown'
            
            if 'AWS' in name:
                org_name = 'Amazon Web Services'
            elif 'CompTIA' in name:
                org_name = 'CompTIA'
            elif 'Microsoft' in name or 'Azure' in name:
                org_name = 'Microsoft'
            elif 'Cisco' in name:
                org_name = 'Cisco Systems'
            elif 'EC-Council' in name:
                org_name = 'EC-Council'
            elif 'GIAC' in name:
                org_name = 'Global Information Assurance Certification'
            elif 'Oracle' in name:
                org_name = 'Oracle Corporation'
            elif 'ISC2' in name:
                org_name = 'International Information System Security Certification Consortium'
            elif 'ISACA' in name:
                org_name = 'Information Systems Audit and Control Association'
            
            if org_name not in orgs_map:
                # Check if org exists
                result = conn.execute(
                    text("SELECT id FROM organizations WHERE name = :name"),
                    {"name": org_name}
                )
                org = result.fetchone()
                
                if not org:
                    # Create organization
                    result = conn.execute(
                        text("""
                            INSERT INTO organizations 
                            (name, country, description, is_deleted, created_at, updated_at) 
                            VALUES (:name, 'United States', :description, false, NOW(), NOW())
                            RETURNING id
                        """),
                        {"name": org_name, "description": f"Provider of {name} certification"}
                    )
                    org_id = result.fetchone()[0]
                    orgs_map[org_name] = org_id
                    orgs_created += 1
                else:
                    orgs_map[org_name] = org[0]
        
        logger.info(f"Processed {orgs_created} new organizations")
        
        # Process certifications
        cert_success = 0
        cert_errors = 0
        
        for cert in certifications_list:
            try:
                name = cert['name']
                
                # Check if certification already exists
                result = conn.execute(
                    text("SELECT id FROM certifications WHERE name = :name"),
                    {"name": name}
                )
                if result.fetchone():
                    logger.info(f"Certification '{name}' already exists. Skipping.")
                    continue
                
                # Map difficulty to level
                difficulty = cert.get('difficulty', 1)
                level_map = {
                    1: 'Entry Level',
                    2: 'Intermediate', 
                    3: 'Advanced',
                    4: 'Expert'
                }
                level = level_map.get(difficulty, 'Entry Level')
                
                # Determine organization
                org_name = 'Unknown'
                if 'AWS' in name:
                    org_name = 'Amazon Web Services'
                elif 'CompTIA' in name:
                    org_name = 'CompTIA'
                elif 'Microsoft' in name or 'Azure' in name:
                    org_name = 'Microsoft'
                elif 'Cisco' in name:
                    org_name = 'Cisco Systems'
                elif 'EC-Council' in name:
                    org_name = 'EC-Council'
                elif 'GIAC' in name:
                    org_name = 'Global Information Assurance Certification'
                elif 'Oracle' in name:
                    org_name = 'Oracle Corporation'
                elif 'ISC2' in name:
                    org_name = 'International Information System Security Certification Consortium'
                elif 'ISACA' in name:
                    org_name = 'Information Systems Audit and Control Association'
                
                # Join prerequisites if any
                prerequisites = ','.join(cert.get('prerequisites', []))
                
                # Insert certification
                conn.execute(
                    text("""
                        INSERT INTO certifications (
                            name, category, domain, level, difficulty, cost,
                            description, prerequisites, organization_id,
                            url, is_deleted, created_at, last_updated, current_version
                        ) VALUES (
                            :name, :category, :domain, :level, :difficulty, :cost,
                            :description, :prerequisites, :org_id,
                            :url, false, NOW(), NOW(), 1
                        )
                    """),
                    {
                        "name": name,
                        "category": cert.get('category', 'General Security'),
                        "domain": cert.get('domain', 'General Security'),
                        "level": level,
                        "difficulty": difficulty,
                        "cost": cert.get('cost'),
                        "description": cert.get('description', ''),
                        "prerequisites": prerequisites,
                        "org_id": orgs_map.get(org_name),
                        "url": cert.get('url')
                    }
                )
                cert_success += 1
                
                if cert_success % 50 == 0:
                    logger.info(f"Added {cert_success} certifications so far")
            
            except Exception as e:
                logger.error(f"Error adding certification '{name}': {e}")
                cert_errors += 1
        
        # Commit transaction
        trans.commit()
        
        # Get final count
        result = conn.execute(text("SELECT COUNT(*) FROM certifications"))
        final_count = result.scalar()
        
        logger.info(f"Import completed. Added {cert_success} certifications with {cert_errors} errors")
        logger.info(f"Final count in certifications table: {final_count}")
    
    except Exception as e:
        logger.error(f"Error during direct seeding: {e}")
        if 'trans' in locals():
            trans.rollback()
    finally:
        conn.close()
        engine.dispose()

def main():
    """Main function to handle the seeding process"""
    logger.info("Starting adapter seed process...")
    
    # Initialize database if using ORM
    if using_models:
        init_db()
    
    # Check and ensure data is in the right table
    logger.info("Checking tables and data...")
    handled = ensure_right_table()
    
    if not handled:
        # If not handled by synchronization, try direct loading
        logger.info("Proceeding with direct data loading...")
        load_directly()
    
    logger.info("Adapter seed process completed.")

if __name__ == "__main__":
    main() 