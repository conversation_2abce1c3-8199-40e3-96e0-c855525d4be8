#!/usr/bin/env python3
"""
Script to transfer data from certification_explorer table to certifications table
"""
import sys
import os
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Tuple
from sqlalchemy import text

# Add parent directory to sys.path
parent_dir = str(Path(__file__).parent.parent)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from database import get_db
from sqlalchemy.orm import Session

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

def get_or_create_organization(db_session, org_name: str) -> int:
    """Get or create an organization and return its ID"""
    if not org_name:
        return None
    
    # Check if organization exists
    query = text("SELECT id FROM organizations WHERE name = :name")
    result = db_session.execute(query, {"name": org_name}).first()
    
    if result:
        logger.info(f"Found existing organization: {org_name} (ID: {result[0]})")
        return result[0]
    else:
        # Create new organization
        logger.info(f"Creating new organization: {org_name}")
        query = text("INSERT INTO organizations (name, created_at, updated_at) VALUES (:name, :created_at, :updated_at) RETURNING id")
        result = db_session.execute(
            query, 
            {
                "name": org_name, 
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
        ).first()
        
        db_session.commit()
        return result[0]

def transfer_certifications() -> Tuple[int, int, int]:
    """
    Transfer data from certification_explorer table to certifications table using direct SQL
    
    Returns:
        Tuple[int, int, int]: (transferred_count, skipped_count, error_count)
    """
    logger.info("Starting transfer of certifications from certification_explorer to certifications table")
    
    # Get database session
    db_session = next(get_db())
    
    # Check if certifications table is empty
    query = text("SELECT COUNT(*) FROM certifications")
    cert_count = db_session.execute(query).scalar()
    
    query = text("SELECT COUNT(*) FROM certification_explorer")
    explorer_count = db_session.execute(query).scalar()
    
    logger.info(f"Found {cert_count} records in certifications table")
    logger.info(f"Found {explorer_count} records in certification_explorer table")
    
    if cert_count > 0:
        logger.warning("Certifications table already has data. Continuing transfer will add new records only.")
    
    if explorer_count == 0:
        logger.warning("No records found in certification_explorer table. Aborting transfer.")
        return 0, 0, 0
    
    transferred_count = 0
    skipped_count = 0
    error_count = 0
    
    # Get all certification_explorer records
    query = text("""
        SELECT 
            id, name, provider, description, type, level, domain, 
            exam_code, price, duration_minutes, passing_score, url
        FROM certification_explorer
    """)
    
    explorer_certs = db_session.execute(query).fetchall()
    
    for cert in explorer_certs:
        try:
            # Check if certification already exists
            check_query = text("SELECT id FROM certifications WHERE name = :name AND deleted_at IS NULL")
            existing = db_session.execute(check_query, {"name": cert.name}).first()
            
            if existing:
                logger.debug(f"Certification '{cert.name}' already exists in certifications table. Skipping.")
                skipped_count += 1
                continue
            
            # Get or create organization
            org_id = get_or_create_organization(db_session, cert.provider)
            
            # Insert certification
            insert_query = text("""
                INSERT INTO certifications (
                    name, organization_id, category, domain, level, 
                    difficulty, cost, exam_code, description, url, 
                    created_at, updated_at
                ) VALUES (
                    :name, :org_id, :category, :domain, :level,
                    :difficulty, :cost, :exam_code, :description, :url,
                    :created_at, :updated_at
                )
            """)
            
            db_session.execute(
                insert_query,
                {
                    "name": cert.name,
                    "org_id": org_id,
                    "category": cert.type,
                    "domain": cert.domain,
                    "level": cert.level,
                    "difficulty": 0.0,  # Default value
                    "cost": cert.price,
                    "exam_code": cert.exam_code,
                    "description": cert.description,
                    "url": cert.url,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                }
            )
            
            transferred_count += 1
            
            # Commit every 100 records
            if transferred_count % 100 == 0:
                db_session.commit()
                logger.info(f"Transferred {transferred_count} certifications so far")
            
        except Exception as e:
            logger.error(f"Error transferring certification '{cert.name}': {e}")
            error_count += 1
    
    # Final commit
    db_session.commit()
    
    logger.info(f"Transfer completed. Transferred: {transferred_count}, Skipped: {skipped_count}, Errors: {error_count}")
    
    return transferred_count, skipped_count, error_count

def clean_certifications_table():
    """
    Clean the certifications table by removing all records
    Use with caution!
    """
    logger.warning("Cleaning certifications table - all records will be deleted!")
    
    confirm = input("Are you sure you want to delete all records from the certifications table? (y/N): ")
    if confirm.lower() != 'y':
        logger.info("Operation cancelled by user")
        return
    
    db_session = next(get_db())
    query = text("DELETE FROM certifications")
    db_session.execute(query)
    db_session.commit()
    
    logger.info("Cleaned certifications table.")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Transfer data between certification tables")
    parser.add_argument('--clean', action='store_true', help="Clean certifications table before transfer")
    parser.add_argument('--force', action='store_true', help="Skip confirmation prompts")
    
    args = parser.parse_args()
    
    if args.clean:
        if args.force:
            db_session = next(get_db())
            query = text("DELETE FROM certifications")
            db_session.execute(query)
            db_session.commit()
            logger.info("Cleaned certifications table.")
        else:
            clean_certifications_table()
    
    transfer_certifications() 