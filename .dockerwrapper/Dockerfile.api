FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    python3-dev \
    linux-headers-generic \
    procps \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Install Python dependencies, including bcrypt and PyJWT
RUN pip install --no-cache-dir -r requirements.txt \
    && pip install --no-cache-dir bcrypt PyJWT

# Copy necessary files
COPY api/ /app/api/
COPY models/ /app/models/
COPY utils/ /app/utils/
COPY database.py /app/
COPY alembic.ini /app/
COPY migrations/ /app/migrations/

# Expose the port
EXPOSE 3001

# Start the FastAPI server
CMD ["uvicorn", "api.app:app", "--host", "0.0.0.0", "--port", "3001", "--reload"] 