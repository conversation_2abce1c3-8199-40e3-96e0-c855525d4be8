version: '3.8'

services:
  certrats_nginx:
    image: nginx:alpine
    container_name: certrats_nginx
    ports:
      - "8090:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ../frontend/build:/app/frontend/build:ro
    depends_on:
      - certrats_fastapi
      - certrats_react
    networks:
      - certrats-network
    restart: always

  certrats_fastapi:
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.api
      args:
        - REQUIREMENTS=.dockerwrapper/requirements.txt
    container_name: certrats_fastapi
    volumes:
      - ../api:/app/api
      - ../models:/app/models
      - ../utils:/app/utils
      - ../database.py:/app/database.py
      - ../migrations:/app/migrations
      - ../alembic.ini:/app/alembic.ini
      - ../data:/app/data
      - ../scripts:/app/scripts
      - ../attached_assets:/app/attached_assets
      - ./requirements.txt:/app/requirements.txt
      - ../shared:/app/shared
      - ../.dockerwrapper:/app/.dockerwrapper
      - ../submodules:/app/submodules
    ports:
      - "3001:3001"
    environment:
      - DATABASE_URL=***************************************************
      - SECRET_KEY=development_secret_key
      - ENVIRONMENT=development
    depends_on:
      - certrats_db
    networks:
      - certrats-network
    restart: always

  certrats_react:
    build:
      context: ../frontend
      dockerfile: Dockerfile.react
    container_name: certrats_react
    volumes:
      - ../frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    environment:
      - CHOKIDAR_USEPOLLING=true
    command: >
      sh -c "
        cd /app &&
        if [ ! -d 'node_modules/@mui' ]; then
          echo 'MUI dependencies missing. Installing...' &&
          npm install --legacy-peer-deps &&
          npm install --save @mui/icons-material@5.11.16 @mui/material@5.13.0 @emotion/react@11.11.0 @emotion/styled@11.11.0;
        fi &&
        npm start
      "
    networks:
      - certrats-network
    restart: always

  certrats_db:
    image: postgres:14
    container_name: certrats_db
    ports:
      - "5433:5432" # Use different port to avoid conflict
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=certrats
    networks:
      - certrats-network
    restart: always

  # Testing service for running Jest and Playwright tests
  certrats_frontend_tests:
    build:
      context: ../frontend
      dockerfile: Dockerfile.react
    container_name: certrats_frontend_tests
    volumes:
      - ../frontend:/app
      - /app/node_modules
      - ./playwright-results:/app/playwright-report
    environment:
      - CI=true
      - PLAYWRIGHT_BASE_URL=http://certrats_nginx:80
    command: >
      sh -c "
        echo 'Running component tests...' &&
        npm run test:coverage &&
        echo 'Running E2E tests...' &&
        npm run test:e2e
      "
    depends_on:
      - certrats_nginx
      - certrats_fastapi
    networks:
      - certrats-network

networks:
  certrats-network:
    driver: bridge

volumes:
  postgres_data:
