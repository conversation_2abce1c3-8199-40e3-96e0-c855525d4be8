#!/usr/bin/env python3
"""
Fixed seed script that uses a non-SSL database connection
"""
import sys
import os

# Make the current directory accessible for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the original seed functions but use our fixed database
from data.seed import load_certification_data, extract_organization_info
import fixed_database as database
from models.certification import Certification, Organization

def seed_database():
    """Seed the database with initial data"""
    print("Initializing database...")
    database.init_db()

    db = next(database.get_db())

    try:
        print("Loading certification data...")
        certifications = load_certification_data()
        print(f"Loaded {len(certifications)} certifications from data source")
        
        success_count = 0
        error_count = 0
        organizations = {}

        # First, add organizations
        for cert_name, cert_data in certifications.items():
            org_data = cert_data['organization']
            org_name = org_data['name']

            if org_name not in organizations:
                org = db.query(Organization).filter_by(name=org_name).first()
                if not org:
                    print(f"Adding organization: {org_name}")
                    org = Organization(
                        name=org_name,
                        country=org_data['country'],
                        url=org_data['url'],
                        description=f"Issuing organization for {cert_name}"
                    )
                    db.add(org)
                    db.flush()
                organizations[org_name] = org

        db.commit()
        print(f"Added {len(organizations)} organizations")

        # Then add certifications
        cert_objects = {}
        for cert_name, cert_data in certifications.items():
            try:
                existing = db.query(Certification).filter_by(name=cert_name).first()
                if existing:
                    print(f"Skipping existing certification: {cert_name}")
                    cert_objects[cert_name] = existing
                    continue

                org = organizations.get(cert_data['organization']['name'])

                prerequisites_list = cert_data.pop('prerequisites', [])
                cert_data.pop('organization')
                
                print(f"Adding certification: {cert_name}")
                certification = Certification(
                    name=cert_name,
                    prerequisites=','.join(prerequisites_list) if prerequisites_list else None,
                    organization_id=org.id if org else None,
                    **cert_data
                )
                db.add(certification)
                cert_objects[cert_name] = certification
                success_count += 1
            except Exception as e:
                print(f"Error importing certification {cert_name}: {e}")
                error_count += 1

        db.commit()
        print(f"Added {success_count} certifications")

        # Finally, set up prerequisites
        prereq_count = 0
        for cert_name, cert_obj in cert_objects.items():
            try:
                if cert_obj.prerequisites:
                    prereq_names = cert_obj.prerequisites.split(',')
                    for prereq_name in prereq_names:
                        prereq_obj = cert_objects.get(prereq_name.strip())
                        if prereq_obj and prereq_obj not in cert_obj.prerequisite_certifications:
                            cert_obj.prerequisite_certifications.append(prereq_obj)
                            prereq_count += 1
            except Exception as e:
                print(f"Error setting prerequisites for {cert_name}: {e}")

        db.commit()
        print(f"Added {prereq_count} prerequisite relationships")
        print(f"Database seeding completed: {success_count} certifications added, {error_count} errors")
        
        # Verify the number of certifications in database
        cert_count = db.query(Certification).count()
        print(f"Total certifications in database: {cert_count}")
        
    except Exception as e:
        print(f"Error during import: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    seed_database() 