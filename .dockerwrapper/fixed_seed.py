#!/usr/bin/env python3
"""
Fixed seed script that uses a non-SSL database connection
"""
import sys
import os
import logging

# Make the current directory accessible for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

# Import the original seed functions but use our fixed database
from data.seed import load_certification_data, import_certifications, seed_jobs
import fixed_database as database
from sqlalchemy import text
from sqlalchemy.orm import Session
from transfer_certifications import transfer_certifications

def seed_database():
    """Initialize the database with sample data"""
    logger.info("Seeding the database with initial data...")
    
    # Load certification data
    certifications = load_certification_data()
    logger.info(f"Loaded {len(certifications)} certifications from data file")
    
    # Import certifications 
    import_certifications(certifications)
    
    # Seed jobs
    seed_jobs()
    
    # Check if certifications table needs to be populated from certification_explorer
    db = next(database.get_db())
    query = text("SELECT COUNT(*) FROM certifications")
    cert_count = db.execute(query).scalar()
    
    logger.info(f"Current certification count: {cert_count}")
    
    if cert_count == 0:
        logger.info("Certifications table is empty. Transferring data from certification_explorer...")
        db.close()  # Close current session to avoid conflicts
        transfer_certifications()
    
    logger.info("Database seeding completed!")

if __name__ == "__main__":
    seed_database() 