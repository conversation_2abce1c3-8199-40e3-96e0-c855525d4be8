#!/bin/bash

# Navigate to the project root directory
cd "$(dirname "$0")/.."

# Check if docker-compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "Error: docker-compose is not installed. Please install it first."
    exit 1
fi

# Check if Dock<PERSON> is running
if ! docker info &> /dev/null; then
    echo "Error: Docker is not running. Please start Docker first."
    exit 1
fi

# Run docker-compose from the .dockerwrapper directory
docker-compose -f .dockerwrapper/docker-compose.yml "$@" 