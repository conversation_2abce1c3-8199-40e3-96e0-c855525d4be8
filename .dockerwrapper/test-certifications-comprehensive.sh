#!/bin/bash

# Ensure we're in the correct directory
cd "$(dirname "$0")"

echo "========================================================"
echo "    CERTIFICATION FEATURE COMPREHENSIVE TESTING"
echo "========================================================"
echo

# First check if containers are running
echo "Checking if required containers are running..."
if ! docker ps --format '{{.Names}}' | grep -q "certrats_fastapi"; then
  echo "Error: Container 'certrats_fastapi' not found or not running."
  echo "Please make sure the backend container is running before executing tests."
  exit 1
fi

if ! docker ps --format '{{.Names}}' | grep -q "certrats_react"; then
  echo "Error: Container 'certrats_react' not found or not running."
  echo "Please make sure the frontend container is running before executing tests."
  exit 1
fi

# -------------------- API TESTS --------------------
echo "STEP 1: Running API Tests for Certification Endpoints..."
echo "------------------------------------------------------"

# Copy the API test file to the container
echo "Copying API test files to the container..."
docker cp ../api/tests/test_certification_endpoints.py certrats_fastapi:/app/api/tests/test_certification_endpoints.py

# Run the tests inside the API container
echo "Running API tests..."
docker exec -it certrats_fastapi sh -c "cd /app && python -m pytest api/tests/test_certification_endpoints.py -v"

# Capture the API test result
API_TEST_RESULT=$?

# -------------------- CHECK ENDPOINT AVAILABILITY --------------------
echo
echo "Checking if certification endpoint is available..."
docker exec -it certrats_nginx sh -c "curl -s http://certrats_react:3000/certifications"
ENDPOINT_CHECK_RESULT=$?

if [ $ENDPOINT_CHECK_RESULT -ne 0 ]; then
  echo "The certification endpoint is not available. UI tests may fail."
  echo "Please make sure the React app is properly configured and running."
fi

# -------------------- UI TESTS --------------------
echo
echo "STEP 2: Running Playwright UI Tests for Certification Pages..."
echo "------------------------------------------------------------"

# Create the specialized Playwright config for Docker
echo "Creating Playwright config..."
cat > /tmp/docker-playwright.config.ts << 'EOF'
import { defineConfig } from '@playwright/test';

export default defineConfig({
  testDir: '.',
  testMatch: ['**/*.spec.ts'],
  timeout: 120000, // Increased timeout to 2 minutes
  fullyParallel: false,
  retries: 1, // Single retry is sufficient
  workers: 1,
  reporter: [['list'], ['html', { open: 'never' }]],
  use: {
    headless: true,
    baseURL: 'http://certrats_nginx',
    actionTimeout: 30000, // Increased from 15s to 30s
    navigationTimeout: 60000, // Increased from 30s to 60s
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: {
        channel: 'chrome',
        launchOptions: {
          executablePath: '/usr/bin/chromium-browser',
          args: ['--no-sandbox', '--disable-gpu', '--disable-dev-shm-usage']
        }
      },
    },
  ],
});
EOF

# Create a simpler test file to debug connection issues
echo "Creating basic connection test..."
cat > /tmp/basic-connection.spec.ts << 'EOF'
import { expect, test } from '@playwright/test';

test.describe('Basic Connection Test', () => {
  test('should be able to access nginx', async ({ page }) => {
    await page.goto('http://certrats_nginx');
    
    // Take a screenshot to see what's displayed
    await page.screenshot({ path: 'nginx-homepage.png' });
    
    // Wait to ensure page has time to load
    await page.waitForTimeout(5000);
    
    // Check if the page loaded successfully (should not throw an error)
    expect(await page.title()).toBeDefined();
  });

  test('should be able to access React app root', async ({ page }) => {
    await page.goto('http://certrats_nginx:80');
    
    // Take a screenshot to see what's displayed
    await page.screenshot({ path: 'react-homepage.png' });
    
    // Wait to ensure page has time to load
    await page.waitForTimeout(5000);
    
    // Check if the page loaded successfully (should not throw an error)
    expect(await page.title()).toBeDefined();
  });
});
EOF

# Copy the test files to the container
echo "Copying UI test files to the container..."
docker cp /tmp/docker-playwright.config.ts certrats_react:/app/docker-playwright.config.ts
docker cp /tmp/basic-connection.spec.ts certrats_react:/app/e2e/basic-connection.spec.ts
docker cp ../frontend/e2e/certification-explorer-comprehensive.spec.ts certrats_react:/app/e2e/certification-explorer-comprehensive.spec.ts

# Ensure Alpine dependencies for Playwright are installed
echo "Ensuring Playwright dependencies are installed..."
docker exec -it certrats_react sh -c "apk add --no-cache chromium xvfb || true"

# Create directories for test results and screenshots
echo "Setting up test output directories..."
docker exec -it certrats_react sh -c "mkdir -p /app/playwright-report /app/test-results/screenshots"

# Run the basic connection test first
echo "Running basic connection test..."
docker exec -it certrats_react sh -c "cd /app && PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1 npx playwright test e2e/basic-connection.spec.ts --config=docker-playwright.config.ts"

# Capture the basic test result
BASIC_TEST_RESULT=$?

# Run the comprehensive UI tests
echo "Running comprehensive UI tests..."
docker exec -it certrats_react sh -c "cd /app && PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1 npx playwright test e2e/certification-explorer-comprehensive.spec.ts --config=docker-playwright.config.ts"

# Capture the UI test result
UI_TEST_RESULT=$?

# Copy test artifacts back to host
echo "Copying test artifacts to host..."
mkdir -p ./playwright-results/screenshots
docker cp certrats_react:/app/playwright-report ./playwright-results/ || true
docker cp certrats_react:/app/test-results/screenshots ./playwright-results/screenshots || true

# Copy screenshots if they exist
docker cp certrats_react:/app/dark-mode-certifications.png ./playwright-results/screenshots/ 2>/dev/null || echo "No dark mode screenshot available"
docker cp certrats_react:/app/nginx-homepage.png ./playwright-results/screenshots/ 2>/dev/null || echo "No nginx homepage screenshot available"
docker cp certrats_react:/app/react-homepage.png ./playwright-results/screenshots/ 2>/dev/null || echo "No react homepage screenshot available"
for size in mobile tablet desktop; do
  docker cp certrats_react:/app/responsive-${size}.png ./playwright-results/screenshots/ 2>/dev/null || echo "No ${size} screenshot available"
done

# -------------------- TEST RESULTS SUMMARY --------------------
echo
echo "========================================================"
echo "              TEST RESULTS SUMMARY"
echo "========================================================"

if [ $API_TEST_RESULT -eq 0 ]; then
  echo "API Tests: ✅ PASSED"
else
  echo "API Tests: ❌ FAILED"
fi

if [ $BASIC_TEST_RESULT -eq 0 ]; then
  echo "Basic Connection Test: ✅ PASSED"
else
  echo "Basic Connection Test: ❌ FAILED"
fi

if [ $UI_TEST_RESULT -eq 0 ]; then
  echo "UI Tests: ✅ PASSED"
else
  echo "UI Tests: ❌ FAILED"
fi

echo
echo "Test artifacts available at: ./playwright-results/"
echo

# Return overall status (success only if both tests passed)
if [ $API_TEST_RESULT -eq 0 ] && [ $UI_TEST_RESULT -eq 0 ]; then
  echo "Overall Test Status: ✅ PASSED"
  exit 0
else
  echo "Overall Test Status: ❌ FAILED"
  exit 1
fi 