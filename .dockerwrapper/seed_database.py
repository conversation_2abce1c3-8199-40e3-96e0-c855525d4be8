#!/usr/bin/env python3
"""
Script to seed the database with certification data
"""
import os
import sys

# Add parent directory to path so we can import modules
sys.path.append('/app')

try:
    # Import the seed function
    from data.seed import seed_database

    print("Starting database seeding...")
    # Call the seed function
    seed_database()
    print("Database seeding completed successfully!")
except Exception as e:
    print(f"ERROR seeding database: {e}")
    sys.exit(1) 