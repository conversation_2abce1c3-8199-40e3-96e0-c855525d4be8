#!/usr/bin/env python3
"""
Direct seeding script to populate the certifications table from JSON data.
"""
import os
import sys
import json
import logging
from sqlalchemy import create_engine, text, MetaData, Table, Column, Integer, String, Float, Text, DateTime, ForeignKey, insert
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_certification_data():
    """Load certification data from JSON file"""
    logger.info("Loading certification data from JSON file...")
    json_file = os.path.join('data', 'normalized_certifications.json')
    
    if not os.path.exists(json_file):
        logger.error(f"Error: JSON file not found at {json_file}")
        return []

    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            certifications_list = json.load(f)
    except Exception as e:
        logger.error(f"Error loading JSON: {e}")
        return []

    logger.info(f"Found {len(certifications_list)} total certifications to load")
    return certifications_list

def direct_seed():
    """Seed the database directly with certification data"""
    try:
        # Get database connection from environment or use default
        db_url = os.environ.get('DATABASE_URL', '***************************************************')
        logger.info(f"Connecting to database: {db_url}")
        
        # Create engine and connection with autocommit to avoid transaction issues
        engine = create_engine(db_url, isolation_level="AUTOCOMMIT")
        conn = engine.connect()
        
        # Load certification data
        certifications_data = load_certification_data()
        
        if not certifications_data:
            logger.error("No certification data found. Exiting.")
            conn.close()
            engine.dispose()
            return
        
        # Check if certifications table is empty
        result = conn.execute(text("SELECT COUNT(*) FROM certifications"))
        cert_count = result.scalar()
        logger.info(f"Current count in certifications table: {cert_count}")
        
        if cert_count > 0:
            logger.warning(f"Certifications table already has {cert_count} records.")
            user_input = input("Do you want to proceed with adding more certifications? (y/n): ")
            if user_input.lower() != 'y':
                logger.info("Seeding cancelled by user.")
                conn.close()
                engine.dispose()
                return
        
        # Start a new transaction explicitly
        trans = conn.begin()
        try:
            # First, create organizations
            orgs_created = 0
            orgs_map = {}  # Map organization names to IDs
            
            # Extract unique organizations
            unique_orgs = set()
            for cert in certifications_data:
                # Determine organization from name
                org_name = 'Unknown'
                if 'AWS' in cert['name']:
                    org_name = 'Amazon Web Services'
                elif 'CompTIA' in cert['name']:
                    org_name = 'CompTIA'
                elif 'Microsoft' in cert['name'] or 'Azure' in cert['name']:
                    org_name = 'Microsoft'
                elif 'Cisco' in cert['name']:
                    org_name = 'Cisco Systems'
                elif 'EC-Council' in cert['name']:
                    org_name = 'EC-Council'
                elif 'GIAC' in cert['name']:
                    org_name = 'Global Information Assurance Certification'
                elif 'Oracle' in cert['name']:
                    org_name = 'Oracle Corporation'
                elif 'ISC2' in cert['name']:
                    org_name = 'International Information System Security Certification Consortium'
                elif 'ISACA' in cert['name']:
                    org_name = 'Information Systems Audit and Control Association'
                
                unique_orgs.add(org_name)
            
            # Create organizations if they don't exist
            for org_name in unique_orgs:
                # Check if organization exists
                result = conn.execute(
                    text("SELECT id FROM organizations WHERE name = :name"),
                    {"name": org_name}
                )
                org = result.fetchone()
                
                if not org:
                    # Create organization
                    result = conn.execute(
                        text("""
                            INSERT INTO organizations 
                            (name, country, description, is_deleted, created_at, updated_at) 
                            VALUES (:name, 'Unknown', :description, false, NOW(), NOW())
                            RETURNING id
                        """),
                        {"name": org_name, "description": f"Provider of certifications"}
                    )
                    org_id = result.fetchone()[0]
                    orgs_map[org_name] = org_id
                    logger.info(f"Created organization: {org_name} (ID: {org_id})")
                    orgs_created += 1
                else:
                    orgs_map[org_name] = org[0]
            
            logger.info(f"Created {orgs_created} organizations")
            
            # Insert certifications
            cert_success = 0
            cert_error = 0
            
            for cert in certifications_data:
                try:
                    # Check if certification already exists
                    result = conn.execute(
                        text("SELECT id FROM certifications WHERE name = :name"),
                        {"name": cert['name']}
                    )
                    if result.fetchone():
                        logger.info(f"Certification {cert['name']} already exists. Skipping.")
                        continue
                    
                    # Map difficulty to level
                    difficulty = cert.get('difficulty', 1)
                    level_map = {
                        1: 'Entry Level',
                        2: 'Intermediate', 
                        3: 'Advanced',
                        4: 'Expert'
                    }
                    level = level_map.get(difficulty, 'Entry Level')
                    
                    # Determine organization
                    org_name = 'Unknown'
                    if 'AWS' in cert['name']:
                        org_name = 'Amazon Web Services'
                    elif 'CompTIA' in cert['name']:
                        org_name = 'CompTIA'
                    elif 'Microsoft' in cert['name'] or 'Azure' in cert['name']:
                        org_name = 'Microsoft'
                    elif 'Cisco' in cert['name']:
                        org_name = 'Cisco Systems'
                    elif 'EC-Council' in cert['name']:
                        org_name = 'EC-Council'
                    elif 'GIAC' in cert['name']:
                        org_name = 'Global Information Assurance Certification'
                    elif 'Oracle' in cert['name']:
                        org_name = 'Oracle Corporation'
                    elif 'ISC2' in cert['name']:
                        org_name = 'International Information System Security Certification Consortium'
                    elif 'ISACA' in cert['name']:
                        org_name = 'Information Systems Audit and Control Association'
                    
                    # Get organization ID
                    org_id = orgs_map.get(org_name)
                    
                    # Format prerequisites
                    prerequisites = ','.join(cert.get('prerequisites', []))
                    
                    # Insert certification
                    conn.execute(
                        text("""
                            INSERT INTO certifications (
                                name, category, domain, level, difficulty, cost,
                                description, prerequisites, organization_id,
                                url, is_deleted, created_at, last_updated, current_version
                            ) VALUES (
                                :name, :category, :domain, :level, :difficulty, :cost,
                                :description, :prerequisites, :org_id,
                                :url, false, NOW(), NOW(), 1
                            )
                        """),
                        {
                            "name": cert['name'],
                            "category": cert.get('category', 'General Security'),
                            "domain": cert.get('domain', 'General Security'),
                            "level": level,
                            "difficulty": difficulty,
                            "cost": cert.get('cost'),
                            "description": cert.get('description', ''),
                            "prerequisites": prerequisites,
                            "org_id": org_id,
                            "url": cert.get('url')
                        }
                    )
                    cert_success += 1
                    
                    if cert_success % 50 == 0:
                        logger.info(f"Added {cert_success} certifications so far...")
                
                except Exception as e:
                    logger.error(f"Error adding certification {cert['name']}: {e}")
                    cert_error += 1
            
            # Commit transaction
            trans.commit()
            
            # Check final count
            result = conn.execute(text("SELECT COUNT(*) FROM certifications"))
            final_count = result.scalar()
            logger.info(f"Seeding complete. Added {cert_success} certifications with {cert_error} errors.")
            logger.info(f"Final count in certifications table: {final_count}")
        
        except Exception as e:
            trans.rollback()
            logger.error(f"Error during transaction, rolling back: {e}")
        
        finally:
            conn.close()
            engine.dispose()
    
    except Exception as e:
        logger.error(f"Error during seeding: {e}")

def auto_seed():
    """Auto seed the database without prompting for user input"""
    try:
        # Get database connection from environment or use default
        db_url = os.environ.get('DATABASE_URL', '***************************************************')
        logger.info(f"Connecting to database: {db_url}")
        
        # Create engine with autocommit to avoid transaction issues
        engine = create_engine(db_url, isolation_level="AUTOCOMMIT")
        conn = engine.connect()
        
        # Load certification data
        certifications_data = load_certification_data()
        
        if not certifications_data:
            logger.error("No certification data found. Exiting.")
            conn.close()
            engine.dispose()
            return
        
        # Check if certifications table is empty
        result = conn.execute(text("SELECT COUNT(*) FROM certifications"))
        cert_count = result.scalar()
        logger.info(f"Current count in certifications table: {cert_count}")
        
        if cert_count > 0:
            logger.warning(f"Certifications table already has {cert_count} records. Auto-seed will skip.")
            conn.close()
            engine.dispose()
            return
        
        # Start a new transaction explicitly
        trans = conn.begin()
        try:
            # First, create organizations
            orgs_created = 0
            orgs_map = {}  # Map organization names to IDs
            
            # Extract unique organizations
            unique_orgs = set()
            for cert in certifications_data:
                # Determine organization from name
                org_name = 'Unknown'
                if 'AWS' in cert['name']:
                    org_name = 'Amazon Web Services'
                elif 'CompTIA' in cert['name']:
                    org_name = 'CompTIA'
                elif 'Microsoft' in cert['name'] or 'Azure' in cert['name']:
                    org_name = 'Microsoft'
                elif 'Cisco' in cert['name']:
                    org_name = 'Cisco Systems'
                elif 'EC-Council' in cert['name']:
                    org_name = 'EC-Council'
                elif 'GIAC' in cert['name']:
                    org_name = 'Global Information Assurance Certification'
                elif 'Oracle' in cert['name']:
                    org_name = 'Oracle Corporation'
                elif 'ISC2' in cert['name']:
                    org_name = 'International Information System Security Certification Consortium'
                elif 'ISACA' in cert['name']:
                    org_name = 'Information Systems Audit and Control Association'
                
                unique_orgs.add(org_name)
            
            # Create organizations if they don't exist
            for org_name in unique_orgs:
                # Check if organization exists
                result = conn.execute(
                    text("SELECT id FROM organizations WHERE name = :name"),
                    {"name": org_name}
                )
                org = result.fetchone()
                
                if not org:
                    # Create organization
                    result = conn.execute(
                        text("""
                            INSERT INTO organizations 
                            (name, country, description, is_deleted, created_at, updated_at) 
                            VALUES (:name, 'Unknown', :description, false, NOW(), NOW())
                            RETURNING id
                        """),
                        {"name": org_name, "description": f"Provider of certifications"}
                    )
                    org_id = result.fetchone()[0]
                    orgs_map[org_name] = org_id
                    logger.info(f"Created organization: {org_name} (ID: {org_id})")
                    orgs_created += 1
                else:
                    orgs_map[org_name] = org[0]
            
            logger.info(f"Created {orgs_created} organizations")
            
            # Insert certifications
            cert_success = 0
            cert_error = 0
            
            for cert in certifications_data:
                try:
                    # Check if certification already exists
                    result = conn.execute(
                        text("SELECT id FROM certifications WHERE name = :name"),
                        {"name": cert['name']}
                    )
                    if result.fetchone():
                        logger.info(f"Certification {cert['name']} already exists. Skipping.")
                        continue
                    
                    # Map difficulty to level
                    difficulty = cert.get('difficulty', 1)
                    level_map = {
                        1: 'Entry Level',
                        2: 'Intermediate', 
                        3: 'Advanced',
                        4: 'Expert'
                    }
                    level = level_map.get(difficulty, 'Entry Level')
                    
                    # Determine organization
                    org_name = 'Unknown'
                    if 'AWS' in cert['name']:
                        org_name = 'Amazon Web Services'
                    elif 'CompTIA' in cert['name']:
                        org_name = 'CompTIA'
                    elif 'Microsoft' in cert['name'] or 'Azure' in cert['name']:
                        org_name = 'Microsoft'
                    elif 'Cisco' in cert['name']:
                        org_name = 'Cisco Systems'
                    elif 'EC-Council' in cert['name']:
                        org_name = 'EC-Council'
                    elif 'GIAC' in cert['name']:
                        org_name = 'Global Information Assurance Certification'
                    elif 'Oracle' in cert['name']:
                        org_name = 'Oracle Corporation'
                    elif 'ISC2' in cert['name']:
                        org_name = 'International Information System Security Certification Consortium'
                    elif 'ISACA' in cert['name']:
                        org_name = 'Information Systems Audit and Control Association'
                    
                    # Get organization ID
                    org_id = orgs_map.get(org_name)
                    
                    # Format prerequisites
                    prerequisites = ','.join(cert.get('prerequisites', []))
                    
                    # Insert certification
                    conn.execute(
                        text("""
                            INSERT INTO certifications (
                                name, category, domain, level, difficulty, cost,
                                description, prerequisites, organization_id,
                                url, is_deleted, created_at, last_updated, current_version
                            ) VALUES (
                                :name, :category, :domain, :level, :difficulty, :cost,
                                :description, :prerequisites, :org_id,
                                :url, false, NOW(), NOW(), 1
                            )
                        """),
                        {
                            "name": cert['name'],
                            "category": cert.get('category', 'General Security'),
                            "domain": cert.get('domain', 'General Security'),
                            "level": level,
                            "difficulty": difficulty,
                            "cost": cert.get('cost'),
                            "description": cert.get('description', ''),
                            "prerequisites": prerequisites,
                            "org_id": org_id,
                            "url": cert.get('url')
                        }
                    )
                    cert_success += 1
                    
                    if cert_success % 50 == 0:
                        logger.info(f"Added {cert_success} certifications so far...")
                
                except Exception as e:
                    logger.error(f"Error adding certification {cert['name']}: {e}")
                    cert_error += 1
            
            # Commit transaction
            trans.commit()
            
            # Check final count
            result = conn.execute(text("SELECT COUNT(*) FROM certifications"))
            final_count = result.scalar()
            logger.info(f"Seeding complete. Added {cert_success} certifications with {cert_error} errors.")
            logger.info(f"Final count in certifications table: {final_count}")
        
        except Exception as e:
            trans.rollback()
            logger.error(f"Error during transaction, rolling back: {e}")
        
        finally:
            conn.close()
            engine.dispose()
    
    except Exception as e:
        logger.error(f"Error during seeding: {e}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--auto":
        auto_seed()
    else:
        direct_seed() 