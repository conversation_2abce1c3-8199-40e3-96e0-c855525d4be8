#!/usr/bin/env python3
"""
Direct seeding script using psycopg2 to populate the certifications table from JSON data.
This avoids SQLAlchemy transaction issues.
"""
import os
import sys
import json
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_certification_data():
    """Load certification data from JSON file"""
    logger.info("Loading certification data from JSON file...")
    json_file = os.path.join('data', 'normalized_certifications.json')
    
    if not os.path.exists(json_file):
        logger.error(f"Error: JSON file not found at {json_file}")
        return []

    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            certifications_list = json.load(f)
    except Exception as e:
        logger.error(f"Error loading JSON: {e}")
        return []

    logger.info(f"Found {len(certifications_list)} total certifications to load")
    return certifications_list

def psycopg_seed(auto=False):
    """Seed the database directly with certification data using psycopg2"""
    # Database connection parameters
    db_params = {
        'dbname': 'certrats',
        'user': 'postgres',
        'password': 'postgres',
        'host': 'certrats_db',
        'port': '5432'
    }
    
    try:
        # Connect to database
        logger.info(f"Connecting to database: {db_params['host']}:{db_params['port']}/{db_params['dbname']}")
        connection = psycopg2.connect(**db_params)
        connection.autocommit = False  # We'll manage transactions manually
        cursor = connection.cursor(cursor_factory=RealDictCursor)
        
        # Load certification data
        certifications_data = load_certification_data()
        
        if not certifications_data:
            logger.error("No certification data found. Exiting.")
            cursor.close()
            connection.close()
            return
        
        # Check if certifications table is empty
        cursor.execute("SELECT COUNT(*) FROM certifications")
        cert_count = cursor.fetchone()['count']
        logger.info(f"Current count in certifications table: {cert_count}")
        
        if cert_count > 0:
            if auto:
                logger.warning(f"Certifications table already has {cert_count} records. Auto-seed will skip.")
                cursor.close()
                connection.close()
                return
            else:
                logger.warning(f"Certifications table already has {cert_count} records.")
                user_input = input("Do you want to proceed with adding more certifications? (y/n): ")
                if user_input.lower() != 'y':
                    logger.info("Seeding cancelled by user.")
                    cursor.close()
                    connection.close()
                    return
        
        try:
            # First, create organizations
            orgs_created = 0
            orgs_map = {}  # Map organization names to IDs
            
            # Extract unique organizations
            unique_orgs = set()
            for cert in certifications_data:
                # Determine organization from name
                org_name = 'Unknown'
                if 'AWS' in cert['name']:
                    org_name = 'Amazon Web Services'
                elif 'CompTIA' in cert['name']:
                    org_name = 'CompTIA'
                elif 'Microsoft' in cert['name'] or 'Azure' in cert['name']:
                    org_name = 'Microsoft'
                elif 'Cisco' in cert['name']:
                    org_name = 'Cisco Systems'
                elif 'EC-Council' in cert['name']:
                    org_name = 'EC-Council'
                elif 'GIAC' in cert['name']:
                    org_name = 'Global Information Assurance Certification'
                elif 'Oracle' in cert['name']:
                    org_name = 'Oracle Corporation'
                elif 'ISC2' in cert['name']:
                    org_name = 'International Information System Security Certification Consortium'
                elif 'ISACA' in cert['name']:
                    org_name = 'Information Systems Audit and Control Association'
                
                unique_orgs.add(org_name)
            
            # Create organizations if they don't exist
            for org_name in unique_orgs:
                # Check if organization exists
                cursor.execute("SELECT id FROM organizations WHERE name = %s", (org_name,))
                org = cursor.fetchone()
                
                if not org:
                    # Create organization
                    cursor.execute("""
                        INSERT INTO organizations 
                        (name, country, description, is_deleted, created_at, updated_at) 
                        VALUES (%s, 'Unknown', %s, false, NOW(), NOW())
                        RETURNING id
                    """, (org_name, f"Provider of certifications"))
                    org_id = cursor.fetchone()['id']
                    orgs_map[org_name] = org_id
                    logger.info(f"Created organization: {org_name} (ID: {org_id})")
                    orgs_created += 1
                else:
                    orgs_map[org_name] = org['id']
            
            logger.info(f"Created {orgs_created} organizations")
            
            # Insert certifications
            cert_success = 0
            cert_error = 0
            
            for cert in certifications_data:
                try:
                    # Check if certification already exists
                    cursor.execute("SELECT id FROM certifications WHERE name = %s", (cert['name'],))
                    if cursor.fetchone():
                        logger.info(f"Certification {cert['name']} already exists. Skipping.")
                        continue
                    
                    # Map difficulty to level
                    difficulty = cert.get('difficulty', 1)
                    level_map = {
                        1: 'Entry Level',
                        2: 'Intermediate', 
                        3: 'Advanced',
                        4: 'Expert'
                    }
                    level = level_map.get(difficulty, 'Entry Level')
                    
                    # Determine organization
                    org_name = 'Unknown'
                    if 'AWS' in cert['name']:
                        org_name = 'Amazon Web Services'
                    elif 'CompTIA' in cert['name']:
                        org_name = 'CompTIA'
                    elif 'Microsoft' in cert['name'] or 'Azure' in cert['name']:
                        org_name = 'Microsoft'
                    elif 'Cisco' in cert['name']:
                        org_name = 'Cisco Systems'
                    elif 'EC-Council' in cert['name']:
                        org_name = 'EC-Council'
                    elif 'GIAC' in cert['name']:
                        org_name = 'Global Information Assurance Certification'
                    elif 'Oracle' in cert['name']:
                        org_name = 'Oracle Corporation'
                    elif 'ISC2' in cert['name']:
                        org_name = 'International Information System Security Certification Consortium'
                    elif 'ISACA' in cert['name']:
                        org_name = 'Information Systems Audit and Control Association'
                    
                    # Get organization ID
                    org_id = orgs_map.get(org_name)
                    
                    # Format prerequisites
                    prerequisites = ','.join(cert.get('prerequisites', []))
                    
                    # Insert certification
                    cursor.execute("""
                        INSERT INTO certifications (
                            name, category, domain, level, difficulty, cost,
                            description, prerequisites, organization_id,
                            url, is_deleted, created_at, last_updated, current_version
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s,
                            %s, %s, %s,
                            %s, false, NOW(), NOW(), 1
                        )
                    """, (
                        cert['name'],
                        cert.get('category', 'General Security'),
                        cert.get('domain', 'General Security'),
                        level,
                        difficulty,
                        cert.get('cost'),
                        cert.get('description', ''),
                        prerequisites,
                        org_id,
                        cert.get('url')
                    ))
                    
                    cert_success += 1
                    
                    if cert_success % 50 == 0:
                        connection.commit()  # Commit in batches
                        logger.info(f"Added {cert_success} certifications so far...")
                
                except Exception as e:
                    logger.error(f"Error adding certification {cert['name']}: {e}")
                    cert_error += 1
            
            # Commit final changes
            connection.commit()
            
            # Check final count
            cursor.execute("SELECT COUNT(*) FROM certifications")
            final_count = cursor.fetchone()['count']
            logger.info(f"Seeding complete. Added {cert_success} certifications with {cert_error} errors.")
            logger.info(f"Final count in certifications table: {final_count}")
        
        except Exception as e:
            connection.rollback()
            logger.error(f"Error during seeding, rolling back: {e}")
        
        finally:
            cursor.close()
            connection.close()
    
    except Exception as e:
        logger.error(f"Error establishing database connection: {e}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--auto":
        psycopg_seed(auto=True)
    else:
        psycopg_seed() 