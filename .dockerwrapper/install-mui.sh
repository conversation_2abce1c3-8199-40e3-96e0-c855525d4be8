#!/bin/bash

# Script to install Material UI dependencies in the React container
echo "Installing Material UI dependencies..."

# Enter the frontend directory
cd /app/frontend

# Clean the node_modules
echo "Cleaning node_modules..."
rm -rf node_modules

# Install dependencies with specific flags to ensure MUI is properly installed
echo "Installing dependencies..."
npm install --legacy-peer-deps

# Force install MUI packages
echo "Force installing MUI packages..."
npm install --save @mui/icons-material@5.11.16 @mui/material@5.13.0 @emotion/react@11.11.0 @emotion/styled@11.11.0

# Rebuild the app
echo "Rebuilding the application..."
npm run build

echo "MUI dependencies installed and app rebuilt!" 