#!/bin/bash

# Navigate to the project root directory
cd "$(dirname "$0")/.."

# Function to display usage
function display_usage {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  --check       Check if all features have been migrated"
    echo "  --remove      Remove Streamlit and update configuration"
    echo "  --force       Force removal of Streamlit without checking migration status"
    echo "  --help        Display this help message"
    echo ""
    echo "Example:"
    echo "  $0 --check"
    echo "  $0 --remove"
    echo "  $0 --force"
}

# Function to check if all features have been migrated
function check_migration {
    echo "Checking migration status..."
    
    # Get the number of features with UI status not marked as Complete
    incomplete_features=$(grep -A 100 "## Migration Tracking" MIGRATION.md | grep -B 100 "## Development Workflow" | grep "|" | grep -v "Feature" | grep -v "Complete.*|.*Complete.*|.*Complete" | wc -l)
    
    if [ "$incomplete_features" -gt 0 ]; then
        echo "Error: Not all features have been migrated to React."
        echo "Please complete the migration of the following features:"
        grep -A 100 "## Migration Tracking" MIGRATION.md | grep -B 100 "## Development Workflow" | grep "|" | grep -v "Feature" | grep -v "Complete.*|.*Complete.*|.*Complete"
        exit 1
    else
        echo "All features have been migrated to React. You can safely remove Streamlit."
    fi
}

# Function to remove Streamlit
function remove_streamlit {
    echo "Removing Streamlit..."
    
    # Check if all features have been migrated (unless forced)
    if [ "$1" != "force" ]; then
        check_migration
    else
        echo "Forcing Streamlit removal without migration check..."
    fi
    
    # Update docker-compose.yml to remove Streamlit service
    echo "Updating docker-compose.yml..."
    sed -i '/streamlit:/,/networks:/d' .dockerwrapper/docker-compose.yml
    sed -i 's/- streamlit//g' .dockerwrapper/docker-compose.yml
    
    # Update Nginx configuration to route all traffic to React
    echo "Updating Nginx configuration..."
    cp .dockerwrapper/nginx.conf .dockerwrapper/nginx.conf.bak
    
    # Replace the Streamlit location block with React
    sed -i 's|proxy_pass http://streamlit:8501/;|proxy_pass http://react:3000/;|g' .dockerwrapper/nginx.conf
    
    # Remove Streamlit Dockerfile
    echo "Removing Streamlit Dockerfile..."
    mv .dockerwrapper/Dockerfile.streamlit .dockerwrapper/Dockerfile.streamlit.bak
    
    # Remove Streamlit requirements
    echo "Removing Streamlit requirements..."
    if [ -f requirements.txt ]; then
        cp requirements.txt requirements.txt.bak
        grep -v "streamlit" requirements.txt > requirements.txt.new
        mv requirements.txt.new requirements.txt
    fi
    
    # Remove Streamlit pages
    echo "Removing Streamlit pages..."
    if [ -d pages ]; then
        mv pages pages.bak
    fi
    
    # Update README
    echo "Updating README..."
    if [ -f README.md ]; then
        cp README.md README.md.bak
        sed -i 's/Streamlit and FastAPI/React and FastAPI/g' README.md
        sed -i 's/Streamlit UI/React UI/g' README.md
    fi
    
    echo "Streamlit has been successfully removed."
    echo "Please restart the services with: ./.dockerwrapper/run.sh restart"
}

# Parse command line arguments
if [ $# -eq 0 ]; then
    display_usage
    exit 1
fi

while [ $# -gt 0 ]; do
    case "$1" in
        --check)
            check_migration
            shift
            ;;
        --remove)
            remove_streamlit "normal"
            shift
            ;;
        --force)
            remove_streamlit "force"
            shift
            ;;
        --help)
            display_usage
            exit 0
            ;;
        *)
            echo "Error: Unknown option $1"
            display_usage
            exit 1
            ;;
    esac
done 