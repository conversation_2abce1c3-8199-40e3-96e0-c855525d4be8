# CertRats Testing Infrastructure

This document provides comprehensive information about the testing infrastructure for the CertRats application, focusing specifically on the Certification Explorer feature.

## Testing Approach

The testing approach for the CertRats application follows a comprehensive strategy combining:

1. **API Testing**: Using pytest to test backend API endpoints
2. **UI Testing**: Using <PERSON>wright to test frontend UI components and interactions
3. **Integration Testing**: Testing the interaction between frontend and backend
4. **Docker-based Testing**: Running tests in containers to ensure consistency

## Test Structure

### API Tests

- **Location**: `api/tests/test_certification_endpoints.py`
- **Technology**: pytest
- **Coverage**:
  - GET endpoint for listing certifications
  - GET endpoint for certification details
  - POST endpoint for creating new certifications
  - DELETE endpoint for removing certifications
  - Filtering by provider, type, level, domain
  - Search functionality
  - Pagination
  - Error handling

### UI Tests

- **Location**: `frontend/e2e/certification-explorer-comprehensive.spec.ts`
- **Technology**: Playwright
- **Coverage**:
  - Page structure and layout
  - Search and filtering functionality
  - Certification detail view
  - Comparison feature
  - Theme switching (dark/light mode)
  - Responsive design
  - Accessibility features

## Running Tests

### Prerequisites

- Docker and Docker Compose must be installed
- The CertRats application containers must be running

### Running All Tests

```bash
cd .dockerwrapper
./test-certifications-comprehensive.sh
```

This script will:
1. Check if required containers are running
2. Run API tests against the backend
3. Check API endpoint availability
4. Run basic connection tests to verify connectivity
5. Run comprehensive UI tests with Playwright
6. Generate test reports and collect artifacts

### Running API Tests Only

```bash
docker exec -it certrats_fastapi sh -c "cd /app && python -m pytest api/tests/test_certification_endpoints.py -v"
```

### Running UI Tests Only

```bash
docker exec -it certrats_react sh -c "cd /app && PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1 npx playwright test e2e/certification-explorer-comprehensive.spec.ts --config=docker-playwright.config.ts"
```

## Test Artifacts

All test artifacts are collected in the `.dockerwrapper/playwright-results/` directory:

- `playwright-report/`: HTML test reports
- `screenshots/`: Screenshots taken during test runs
- Individual screenshots for responsive design and dark mode testing

## Troubleshooting

### API Tests Failing

Common issues:
- Incorrect import paths: The test file has fallbacks for different import structures
- Database connectivity: Ensure the database container is running
- Missing models: The test can create mock models if imports fail

### UI Tests Failing

Common issues:
- Missing components: Ensure the React app is properly built and running
- Selectors not found: The tests use multiple selector strategies for resilience
- Container connectivity: Verify the nginx proxy is properly configured

## CI/CD Integration

To integrate these tests into a CI/CD pipeline:

1. Run the Docker containers in the CI environment
2. Execute the `.dockerwrapper/test-certifications-comprehensive.sh` script
3. Collect the test results and artifacts
4. Parse the exit code to determine test success or failure

## Test Maintenance

When adding new features:

1. Add corresponding API tests in `api/tests/`
2. Add UI tests in `frontend/e2e/`
3. Update the comprehensive test script if necessary
4. Run the tests locally to ensure they pass before committing

When updating existing features:

1. Run the existing tests to ensure they still pass
2. Update tests as needed to reflect changed functionality
3. Maintain selector resilience by using multiple selector strategies

## Best Practices

- **Isolation**: Tests should be independent and not rely on each other
- **Idempotency**: Tests should be repeatable with the same result
- **Resilience**: Tests should handle temporary failures gracefully
- **Performance**: Tests should be optimized to run as quickly as possible
- **Coverage**: Tests should cover all critical functionality
- **Maintainability**: Tests should be easy to update as the application evolves 