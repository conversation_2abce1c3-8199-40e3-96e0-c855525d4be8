#!/usr/bin/env python3
"""
Unit tests to verify successful certification explorer database seeding
"""
import pytest
import psycopg2
import os
import sys
from typing import Dict, Any, List
from psycopg2.extras import RealDictCursor

# Database connection string
DB_URL = "***************************************************"

def get_db_connection():
    """Get a database connection with dictionary cursor"""
    return psycopg2.connect(DB_URL, cursor_factory=RealDictCursor)

def test_certification_table_exists():
    """Test that the certification_explorer table exists"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute("""
            SELECT EXISTS(
                SELECT 1 FROM information_schema.tables 
                WHERE table_name = 'certification_explorer'
            )
        """)
        result = cursor.fetchone()
        assert result['exists'], "certification_explorer table does not exist"
    finally:
        cursor.close()
        conn.close()

def test_certification_count():
    """Test that we have a reasonable number of certifications"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute("SELECT COUNT(*) FROM certification_explorer")
        result = cursor.fetchone()
        count = result['count']
        assert count >= 5, f"Expected >=5 certifications, but found {count}"
        print(f"Found {count} certifications in the database")
    finally:
        cursor.close()
        conn.close()

def test_certification_providers():
    """Test that we have certifications from major providers"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    expected_providers = [
        "CompTIA", 
        "Amazon Web Services", 
        "Microsoft", 
        "Cisco Systems",
        "EC-Council"
    ]
    
    try:
        cursor.execute("SELECT DISTINCT provider FROM certification_explorer")
        results = cursor.fetchall()
        providers = [r['provider'] for r in results]
        
        print(f"Found providers: {providers}")
        
        for provider in expected_providers:
            assert provider in providers, f"Expected provider {provider} not found"
    finally:
        cursor.close()
        conn.close()

def test_certification_types():
    """Test that we have certifications of different types"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    expected_types = ["SECURITY", "CLOUD", "NETWORKING"]
    
    try:
        cursor.execute("SELECT DISTINCT type FROM certification_explorer")
        results = cursor.fetchall()
        types = [r['type'] for r in results]
        
        print(f"Found types: {types}")
        
        for cert_type in expected_types:
            assert cert_type in types, f"Expected type {cert_type} not found"
    finally:
        cursor.close()
        conn.close()

def test_certification_fields():
    """Test that certifications have the expected fields with data"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Get a well-known certification like CompTIA Security+
        cursor.execute("""
            SELECT * FROM certification_explorer 
            WHERE name LIKE '%Security+%' OR name LIKE '%CompTIA%Security%'
            LIMIT 1
        """)
        cert = cursor.fetchone()
        
        assert cert is not None, "Could not find Security+ certification"
        
        expected_fields = [
            'id', 'name', 'provider', 'description', 'type', 'level', 
            'domain', 'price', 'prerequisites_list', 'url'
        ]
        
        for field in expected_fields:
            assert field in cert, f"Field {field} missing from certification"
            assert cert[field] is not None, f"Field {field} is None"
        
        print(f"Found Security+ certification with fields: {list(cert.keys())}")
    finally:
        cursor.close()
        conn.close()

def test_filtering_certifications():
    """Test that we can filter certifications by type, level, and provider"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Test filtering by type
        cursor.execute("SELECT COUNT(*) FROM certification_explorer WHERE type = 'SECURITY'")
        security_count = cursor.fetchone()['count']
        assert security_count > 0, "No security certifications found"
        
        # Test filtering by level
        cursor.execute("SELECT COUNT(*) FROM certification_explorer WHERE level = 'BEGINNER'")
        beginner_count = cursor.fetchone()['count']
        assert beginner_count > 0, "No beginner certifications found"
        
        # Test filtering by provider
        cursor.execute("SELECT COUNT(*) FROM certification_explorer WHERE provider = 'CompTIA'")
        comptia_count = cursor.fetchone()['count']
        assert comptia_count > 0, "No CompTIA certifications found"
        
        # Test combined filtering
        cursor.execute("""
            SELECT COUNT(*) FROM certification_explorer 
            WHERE type = 'SECURITY' AND level = 'BEGINNER'
        """)
        combined_count = cursor.fetchone()['count']
        assert combined_count > 0, "No beginner security certifications found"
        
        print(f"Found {security_count} security certifications")
        print(f"Found {beginner_count} beginner certifications")
        print(f"Found {comptia_count} CompTIA certifications")
        print(f"Found {combined_count} beginner security certifications")
    finally:
        cursor.close()
        conn.close()

def test_certification_prerequisites():
    """Test that some certifications have prerequisites"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute("SELECT COUNT(*) FROM certification_explorer WHERE prerequisites_list IS NOT NULL AND prerequisites_list::text != '[]'")
        count = cursor.fetchone()['count']
        
        assert count > 0, "No certifications with prerequisites found"
        print(f"Found {count} certifications with prerequisites")
    finally:
        cursor.close()
        conn.close()

def run_all_tests():
    """Run all tests and print results"""
    tests = [
        test_certification_table_exists,
        test_certification_count,
        test_certification_providers,
        test_certification_types,
        test_certification_fields,
        test_filtering_certifications,
        test_certification_prerequisites
    ]
    
    print("Running certification seeding tests...\n")
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            print(f"Running {test.__name__}...")
            test()
            passed += 1
            print(f"✅ {test.__name__} PASSED\n")
        except Exception as e:
            failed += 1
            print(f"❌ {test.__name__} FAILED: {str(e)}\n")
    
    print(f"Test results: {passed} passed, {failed} failed")
    print("-" * 50)
    
    return passed, failed

if __name__ == "__main__":
    passed, failed = run_all_tests()
    sys.exit(1 if failed > 0 else 0) 