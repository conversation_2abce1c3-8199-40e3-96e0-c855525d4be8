"""Import and seeding functionality for certification data"""
import sys
import os
import json
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import init_db, get_db
from models.certification import Certification, Organization
from models.job import SecurityJob
from models.user import User
from utils.html_processor import process_certification_html
from scripts.populate_security_jobs import parse_job_titles, is_valid_job_title
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_organization_info(cert_name, cert_data):
    """Extract organization information from certification data"""
    org_sources = [
        cert_data.get('issuing_organization'),
        cert_name.split()[0],  # First word of cert name
        cert_data.get('category', '').split()[0]  # First word of category
    ]

    org_name = next((source for source in org_sources if source), 'Unknown')

    org_mapping = {
        'AWS': ('Amazon Web Services', 'United States', 'https://aws.amazon.com/certification/'),
        'CompTIA': ('CompTIA', 'United States', 'https://www.comptia.org/certifications'),
        'ISC2': ('International Information System Security Certification Consortium', 'United States', 'https://www.isc2.org/'),
        'ISACA': ('Information Systems Audit and Control Association', 'United States', 'https://www.isaca.org/credentialing'),
        'MS': ('Microsoft', 'United States', 'https://learn.microsoft.com/en-us/certifications/'),
        'CISCO': ('Cisco Systems', 'United States', 'https://www.cisco.com/c/en/us/training-events/training-certifications.html'),
        'EC-Council': ('EC-Council', 'United States', 'https://www.eccouncil.org/programs/'),
        'GIAC': ('Global Information Assurance Certification', 'United States', 'https://www.giac.org/certifications'),
        'Oracle': ('Oracle Corporation', 'United States', 'https://education.oracle.com/certification'),
    }

    if org_name.upper() in org_mapping:
        return org_mapping[org_name.upper()]
    else:
        return (org_name, 'Unknown', None)

def load_certification_data():
    """Load certification data from JSON file"""
    print("Loading certification data from JSON file...")
    json_file = os.path.join('data', 'normalized_certifications.json')
    
    if not os.path.exists(json_file):
        print(f"Error: JSON file not found at {json_file}")
        return {}

    with open(json_file, 'r', encoding='utf-8') as f:
        certifications_list = json.load(f)

    certifications = {}
    print(f"Found {len(certifications_list)} total certifications to load")

    for cert in certifications_list:
        name = cert['name']
        if name in certifications:
            print(f"Warning: Duplicate certification found: {name}")
            continue

        difficulty = cert.get('difficulty', 1)
        level_map = {
            1: 'Entry Level',
            2: 'Intermediate', 
            3: 'Advanced',
            4: 'Expert'
        }
        level = level_map.get(difficulty, 'Entry Level')

        domain = cert.get('domain', 'General Security')

        # Extract organization from name or description
        org_name = 'Unknown'
        if 'AWS' in name:
            org_name = 'Amazon Web Services'
        elif 'CompTIA' in name:
            org_name = 'CompTIA'
        elif 'Microsoft' in name or 'Azure' in name:
            org_name = 'Microsoft'
        elif 'Cisco' in name:
            org_name = 'Cisco Systems'
        elif 'EC-Council' in name:
            org_name = 'EC-Council'
        elif 'GIAC' in name:
            org_name = 'Global Information Assurance Certification'
        elif 'Oracle' in name:
            org_name = 'Oracle Corporation'
        elif 'ISC2' in name:
            org_name = 'International Information System Security Certification Consortium'
        elif 'ISACA' in name:
            org_name = 'Information Systems Audit and Control Association'

        org_country = 'United States'  # Most security certs are from US
        org_url = None  # We can add URLs later if needed

        certifications[name] = {
            'category': cert.get('category', 'General Security'),
            'domain': domain,
            'level': level,
            'difficulty': difficulty,
            'cost': cert.get('cost'),
            'description': cert.get('description', ''),
            'prerequisites': cert.get('prerequisites', []),
            'validity_period': cert.get('validity_period', 36),
            'exam_code': None,
            'organization': {
                'name': org_name,
                'country': org_country,
                'url': org_url
            },
            'url': cert.get('url')
        }

    print(f"Total unique certifications loaded: {len(certifications)}")
    return certifications

def import_certifications(db=None):
    """Import certifications into database, returns (success_count, error_count)"""
    if not db:
        db = next(get_db())

    try:
        certifications = load_certification_data()
        success_count = 0
        error_count = 0

        organizations = {}

        for cert_name, cert_data in certifications.items():
            org_data = cert_data['organization']
            org_name = org_data['name']

            if org_name not in organizations:
                org = db.query(Organization).filter_by(name=org_name).first()
                if not org:
                    org = Organization(
                        name=org_name,
                        country=org_data['country'],
                        url=org_data['url'],
                        description=f"Issuing organization for {cert_name}"
                    )
                    db.add(org)
                    db.flush()
                organizations[org_name] = org

        db.commit()

        cert_objects = {}
        for cert_name, cert_data in certifications.items():
            try:
                existing = db.query(Certification).filter_by(name=cert_name).first()
                if existing:
                    print(f"Skipping existing certification: {cert_name}")
                    cert_objects[cert_name] = existing
                    continue

                org = organizations.get(cert_data['organization']['name'])

                prerequisites_list = cert_data.pop('prerequisites', [])
                cert_data.pop('organization')
                certification = Certification(
                    name=cert_name,
                    prerequisites=','.join(prerequisites_list) if prerequisites_list else None,
                    organization_id=org.id if org else None,
                    **cert_data
                )
                db.add(certification)
                cert_objects[cert_name] = certification
                success_count += 1
            except Exception as e:
                print(f"Error importing certification {cert_name}: {e}")
                error_count += 1

        db.commit()

        for cert_name, cert_obj in cert_objects.items():
            try:
                if cert_obj.prerequisites:
                    prereq_names = cert_obj.prerequisites.split(',')
                    for prereq_name in prereq_names:
                        prereq_obj = cert_objects.get(prereq_name.strip())
                        if prereq_obj and prereq_obj not in cert_obj.prerequisite_certifications:
                            cert_obj.prerequisite_certifications.append(prereq_obj)
            except Exception as e:
                print(f"Error setting prerequisites for {cert_name}: {e}")
                error_count += 1

        db.commit()
        return success_count, error_count

    except Exception as e:
        print(f"Error during import: {e}")
        db.rollback()
        return 0, 0

def seed_jobs(db=None):
    """Seed security jobs data with improved validation"""
    if not db:
        db = next(get_db())

    try:
        file_path = 'attached_assets/Pasted-Cybersecurity-Job-Titles-Based-on-an-exhaustive-search-of-job-boards-and-websites-like-Indeed-Linke-1740515734230.txt'
        with open(file_path, 'r') as f:
            content = f.read()

        jobs = parse_job_titles(content)
        success_count = 0
        error_count = 0
        skipped_count = 0

        for job_data in jobs:
            try:
                title = job_data['title']

                if not is_valid_job_title(title):
                    logger.warning(f"Skipping invalid job title format: {title}")
                    skipped_count += 1
                    continue

                existing = db.query(SecurityJob).filter_by(
                    title=title,
                    domain=job_data['domain']
                ).first()

                if not existing:
                    job = SecurityJob(
                        title=title,
                        domain=job_data['domain'],
                        description=job_data['description']
                    )
                    db.add(job)
                    success_count += 1
                else:
                    logger.info(f"Skipping existing job: {title}")
                    skipped_count += 1
            except Exception as e:
                logger.error(f"Error seeding job {job_data['title']}: {str(e)}")
                error_count += 1

        db.commit()
        logger.info(f"Successfully seeded {success_count} jobs")
        logger.info(f"Skipped {skipped_count} jobs (invalid or existing)")
        logger.info(f"Encountered {error_count} errors")
        return success_count, error_count
    except Exception as e:
        logger.error(f"Error during job seeding: {str(e)}")
        db.rollback()
        return 0, 0

def seed_database():
    """Seed the database with initial data"""
    print("Initializing database...")
    init_db()

    db = next(get_db())

    try:
        cert_success, cert_errors = import_certifications(db)
        print(f"Database seeded with {cert_success} certifications ({cert_errors} errors)")

        job_success, job_errors = seed_jobs(db)
        print(f"Database seeded with {job_success} security jobs ({job_errors} errors)")

        print("Database seeding completed successfully!")
    except Exception as e:
        print(f"Error seeding database: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    seed_database()