"""
Certification data structure and loading from JSON file
"""
import json
import os
import logging
from utils.roadmap_parser import parse_roadmap_html, merge_roadmap_data

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_certifications():
    """Load certifications from JSON file and roadmap data"""
    json_path = os.path.join(os.path.dirname(__file__), 'certifications.json')
    try:
        with open(json_path, 'r') as f:
            data = json.load(f)
            if not isinstance(data, dict) or 'categories' not in data:
                logger.error("Invalid JSON structure: expected object with 'categories' key")
                return {}

            categories = data['categories']
            logger.info(f"Loaded {len(categories)} categories from JSON")

            if not isinstance(categories, list):
                logger.error(f"Expected list of categories, got {type(categories)}")
                return {}

        # Create a flat dictionary of certifications from JSON
        certifications = {}
        for category_data in categories:
            if not isinstance(category_data, dict):
                logger.warning(f"Skipping invalid category data: {category_data}")
                continue

            category_name = category_data.get('category')
            cert_list = category_data.get('certifications', [])

            if not isinstance(cert_list, list):
                logger.warning(f"Invalid certifications list for category {category_name}")
                continue

            logger.info(f"Processing {len(cert_list)} certifications in category {category_name}")

            for cert in cert_list:
                if not isinstance(cert, dict):
                    logger.warning(f"Skipping invalid certification data: {cert}")
                    continue

                # Map category to difficulty level
                difficulty_map = {
                    'Entry Level': 'Low',
                    'Intermediate': 'Medium',
                    'Advanced': 'High',
                    'Expert': 'High'
                }

                name = cert.get('name')
                if not name:
                    logger.warning("Skipping certification with no name")
                    continue

                certifications[name] = {
                    'level': cert.get('category', 'Entry Level'),
                    'domain': cert.get('focus', 'General Security'),
                    'difficulty': difficulty_map.get(cert.get('category'), 'Medium'),
                    'cost': None,  # Will be determined by roadmap data
                    'prerequisites': None,  # Will be determined by roadmap data
                    'description': f"{cert.get('focus', 'Security')} certification at {cert.get('category', 'Entry')} level",
                    'url': None,  # Will be determined by roadmap data
                    'related_certs': {'prerequisites': [], 'leads_to': [], 'related': []}
                }

        logger.info(f"Successfully loaded {len(certifications)} certifications")

        # Load and merge roadmap data
        try:
            roadmap_data = parse_roadmap_html()
            certifications = merge_roadmap_data(certifications, roadmap_data)
        except Exception as e:
            logger.warning(f"Could not load roadmap data: {e}")

        return certifications

    except FileNotFoundError:
        logger.error(f"Certifications file not found: {json_path}")
        return {}
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in certifications file: {e}")
        return {}
    except Exception as e:
        logger.error(f"Unexpected error loading certifications: {e}")
        return {}

# Load certification data
CERTIFICATIONS = load_certifications()

# Extract unique domains, levels, and difficulties
DOMAINS = sorted(list(set(cert['domain'] for cert in CERTIFICATIONS.values())))
LEVELS = ["Entry Level", "Intermediate", "Advanced"]
DIFFICULTIES = ["Low", "Medium", "High"]