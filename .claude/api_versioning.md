# API Versioning Guide

## Overview

The CertRats application has implemented a comprehensive API versioning system to improve API structure and future-proof the application for API evolution. This document serves as a guide for developers working with the versioned API.

## Versioning Structure

All API endpoints follow the pattern:

```
/api/v{version_number}/{resource}/{action}
```

Example: `/api/v1/study/estimate`

## Current API Version

The current API version is **v1**. All requests should use this version prefix.

## Key Benefits

- **Future-Proof Architecture**: Allows backward compatibility while evolving the API
- **Consistent Endpoint Definitions**: Standardized patterns between frontend and backend
- **Centralized Route Management**: Simplified management of API endpoints
- **Developer Experience**: Improved clarity and documentation

## Implementation Details

### Backend Structure

```
api/
├── v1/
│   ├── auth/
│   │   ├── login.py
│   │   ├── logout.py
│   │   └── me.py
│   ├── users/
│   │   ├── profile.py
│   │   ├── preferences.py
│   │   └── certifications.py
│   ├── study/
│   │   ├── estimate.py
│   │   ├── progress.py
│   │   └── roi.py
│   └── ...
└── route_manager.py
```

### Frontend Integration

The frontend React application uses custom hooks to access versioned API endpoints:

```typescript
// Example usage of versioned API hooks
import { useStudyApi } from '../hooks/api/useStudyApi';

function StudyComponent() {
  const { getEstimate, getProgress, getRoi } = useStudyApi();
  
  // Use the versioned API methods
  const handleEstimate = async () => {
    const result = await getEstimate(params);
    // Process result
  };
  
  // ...
}
```

## Fully Implemented Endpoints

The following endpoints have been fully implemented with backend logic and tests:

| Endpoint | Description | Status |
|----------|-------------|--------|
| `/api/v1/users/profile` | User profile management | ✅ Complete |
| `/api/v1/users/preferences` | User preferences | ✅ Complete |
| `/api/v1/users/certifications` | User certification management | ✅ Complete |
| `/api/v1/auth/login` | Authentication - login | ✅ Complete |
| `/api/v1/auth/logout` | Authentication - logout | ✅ Complete |
| `/api/v1/auth/me` | Current user information | ✅ Complete |
| `/api/v1/study/estimate` | Study time estimation | ✅ Complete |
| `/api/v1/study/progress` | Study progress tracking | ✅ Complete |
| `/api/v1/study/roi` | Return on investment calculations | ✅ Complete |

## Endpoints With Mock Data

The following endpoints have been defined with mock data implementations:

| Endpoint | Description | Status |
|----------|-------------|--------|
| `/api/v1/currency/rates` | Currency conversion rates | ⚠️ Needs implementation |
| `/api/v1/faqs` | Frequently asked questions | ✅ Mock data |
| `/api/v1/career/roles` | Career role information | ✅ Mock data |
| `/api/v1/certifications` | Certification data | ✅ Mock data |
| `/api/v1/user/journey` | User journey information | ✅ Mock data |
| `/api/v1/user/journey/events` | User journey events | ✅ Mock data |
| `/api/v1/jobs` | Job listings | ✅ Mock data |
| `/api/v1/skills` | Skill information | ✅ Mock data |
| `/api/v1/feedback` | User feedback | ✅ Mock data |
| `/api/v1/users/tutorial` | User tutorial status | ✅ Mock data |

## Version Migration Plan

When introducing a new API version:

1. Create a new directory structure (e.g., `api/v2/`)
2. Implement new endpoints or modified endpoints in the new version
3. Keep previous version endpoints functional for backward compatibility
4. Update frontend to use new version endpoints where needed
5. Document changes and migration path for developers

## Testing

All API endpoints should have comprehensive tests covering:

- Happy path scenarios
- Error handling
- Edge cases
- Authentication requirements
- Authorization requirements

## Documentation

API documentation is available through the OpenAPI specification at `/api/docs`. 