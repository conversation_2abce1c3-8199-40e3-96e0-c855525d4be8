# Main Page Components QA Documentation

## Overview

This document provides details about the main page components implemented according to the Main Page PRD. It serves as a reference for QA testing and future maintenance.

## Components

### DashboardPreview

**Functionality:** 
- Visualizes certification progress using Chart.js doughnut chart
- Displays study hours over time using Chart.js bar chart
- Shows ROI metrics including salary increase, cost savings, and completion time

**Dependencies:**
- Chart.js library
- LanguageContext for translation
- Responsive to theme changes via CSS variables

**Testing Considerations:**
- Verify correct rendering of charts with different data sets
- Check responsiveness on various screen sizes
- Ensure proper theme switching behavior
- Verify accessibility of chart elements
- Test internationalization of all text elements

### CareerPathNavigator

**Functionality:**
- Interactive D3.js force-directed graph showing certification paths
- Displays certification nodes organized by experience level
- Shows popular career paths with metrics
- Supports dragging nodes for interaction

**Dependencies:**
- D3.js library
- LanguageContext for translation
- SVG rendering capabilities

**Testing Considerations:**
- Verify graph initialization and node positioning
- Test interactive behavior (dragging, clicking)
- Check responsiveness across different screen sizes
- Verify accessibility of interactive elements
- Test proper force simulation behavior

### CertificationCatalogPreview

**Functionality:**
- Provides filtering by domain and level
- Displays certification cards with visual categorization
- Shows certification details including provider, domain, level, and price

**Dependencies:**
- LanguageContext for translation
- React Router for navigation links

**Testing Considerations:**
- Verify filter functionality
- Test empty state handling
- Check responsive grid layout
- Verify correct styling of different certification types
- Test filter combinations for expected results

### SuccessStories

**Functionality:**
- Carousel display of user testimonials
- Shows before/after career progression
- Displays key metrics (salary increase, completion time, job offers)
- Supports touch and keyboard navigation

**Dependencies:**
- LanguageContext for translation
- Touch event handling

**Testing Considerations:**
- Verify carousel navigation (prev/next)
- Test touch swipe gestures
- Check keyboard accessibility
- Test indicator dots functionality
- Verify responsive behavior on different devices

### GettingStartedGuide

**Functionality:**
- Step-by-step guide with interactive navigation
- Detailed information for each step
- Tutorial launcher functionality
- Contextual CTAs for each step

**Dependencies:**
- LanguageContext for translation
- React Router for navigation links
- AnalyticsContext for event tracking

**Testing Considerations:**
- Verify step navigation functionality
- Test tutorial mode behavior
- Check responsive layout on different screen sizes
- Verify analytics event firing
- Test accessibility of all interactive elements

## Integration Points

- **MainPage Component**: Integrates all the above components
- **Flip Sections Container**: Manages the animated transitions between sections
- **AnalyticsContext**: Tracks user interactions across all components
- **LazyLoad Component**: Defers loading of components until needed

## Performance Considerations

- Monitor Chart.js and D3.js rendering performance
- Check for animation smoothness on lower-end devices
- Verify lazy loading behavior works as expected
- Test swipe gestures for responsiveness

## Accessibility Requirements

- All interactive elements must be keyboard navigable
- Appropriate ARIA attributes should be present
- Color contrast should meet WCAG AA standards in both light and dark themes
- Touch targets should be appropriate size for mobile use 