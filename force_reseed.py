#!/usr/bin/env python3
"""
Force reseed script for certification_explorer table
"""
import sys
import os
import psycopg2

# Add the app directory to sys.path
sys.path.append('/app')

# Now import from direct_seed
from direct_seed import seed_certifications, DB_URL

def force_reseed():
    """Force reseed the certification_explorer table"""
    try:
        # Connect to the database
        conn = psycopg2.connect(DB_URL)
        cursor = conn.cursor()
        
        # Drop existing data
        print("Dropping existing certification data...")
        cursor.execute("TRUNCATE certification_explorer CASCADE")
        conn.commit()
        print("Existing data dropped successfully.")
        
        # Close connection
        cursor.close()
        conn.close()
        
        # Now run the seed function
        print("Reseeding certification data...")
        seed_certifications()
        
    except Exception as e:
        print(f"Error during force reseed: {e}")

if __name__ == "__main__":
    force_reseed() 