{ pkgs ? import <nixpkgs> {} }:

let
  nodejs = pkgs.nodejs-18_x;
  npm = pkgs.nodePackages.npm;
in
pkgs.mkShell {
  buildInputs = with pkgs; [
    # Node.js and npm
    nodejs
    npm

    # Testing tools
    nodePackages.typescript
    nodePackages.typescript-language-server

    # Development tools
    git
    curl
    wget
  ];

  shellHook = ''
    # Set up Node.js environment
    export NODE_PATH="${nodejs}/lib/node_modules:$NODE_PATH"
    export PATH="$PWD/node_modules/.bin:$PATH"

    # Install dependencies if package.json exists
    if [ -f frontend/package.json ]; then
      echo "Installing npm dependencies..."
      cd frontend && npm install && cd ..
    fi

    # Install Playwright browsers if needed
    if ! command -v playwright &> /dev/null; then
      echo "Installing Playwright..."
      cd frontend && npm install -g playwright && npx playwright install && cd ..
    fi
  '';
} 