// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Certification {
  id          String   @id @default(cuid())
  name        String
  description String
  category    String
  level       String
  provider    String
  aliases     String[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([name])
  @@index([category])
  @@index([level])
  @@index([provider])
} 