"""
SQLAlchemy models for learning paths and user experience
"""
from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, Enum, JSON, Table, Boolean, Float
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import List, Dict
from models.base import Base


class UserExperience(Base):
    """
    Stores user's background and experience level
    """
    __tablename__ = 'user_experiences'

    id = Column(Integer, primary_key=True)
    user_id = Column(String(100), nullable=False)  # Can be email or session ID
    years_experience = Column(Integer, nullable=False)
    user_role = Column(String(100))  # Changed from current_role
    desired_role = Column(String(100))  # Changed from target_role
    expertise_areas = Column(JSON)  # Store as JSON array
    # e.g., "Visual", "Hands-on", "Reading"
    preferred_learning_style = Column(String(50))
    study_time_available = Column(Integer)  # Hours per week
    # Added tutorial tracking
    tutorial_completed = Column(<PERSON><PERSON><PERSON>, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow,
                        onupdate=datetime.utcnow)

    # One-to-many relationship with learning paths
    learning_paths = relationship(
        "LearningPath", back_populates="user_experience")

    def to_dict(self) -> Dict:
        """Convert user experience to dictionary with proper datetime serialization"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'years_experience': self.years_experience,
            'user_role': self.user_role,
            'desired_role': self.desired_role,
            'expertise_areas': self.expertise_areas,
            'preferred_learning_style': self.preferred_learning_style,
            'study_time_available': self.study_time_available,
            'tutorial_completed': self.tutorial_completed,  # Added tutorial tracking to dict
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class LearningPath(Base):
    """
    Represents a personalized certification learning path for a user
    """
    __tablename__ = 'learning_paths'

    id = Column(Integer, primary_key=True)
    user_experience_id = Column(Integer, ForeignKey(
        'user_experiences.id'), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    estimated_duration_weeks = Column(Integer)
    # "Beginner", "Intermediate", "Advanced"
    difficulty_level = Column(String(20))
    # "active", "completed", "abandoned"
    status = Column(String(20), default="active")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow,
                        onupdate=datetime.utcnow)

    # Relationships
    user_experience = relationship(
        "UserExperience", back_populates="learning_paths")
    path_items = relationship("LearningPathItem", back_populates="learning_path",
                              order_by="LearningPathItem.sequence")

    def to_dict(self) -> Dict:
        """Convert learning path to dictionary with proper datetime serialization"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'estimated_duration_weeks': self.estimated_duration_weeks,
            'difficulty_level': self.difficulty_level,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'items': [item.to_dict() for item in self.path_items]
        }


class LearningPathItem(Base):
    """
    Individual item in a learning path (certification or prerequisite study)
    """
    __tablename__ = 'learning_path_items'

    id = Column(Integer, primary_key=True)
    learning_path_id = Column(Integer, ForeignKey(
        'learning_paths.id'), nullable=False)
    certification_id = Column(Integer, ForeignKey(
        'certifications.id'), nullable=True)
    sequence = Column(Integer, nullable=False)  # Order in the path
    # "certification", "prerequisite", "practice"
    item_type = Column(String(20), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    estimated_hours = Column(Integer)
    # "pending", "in_progress", "completed"
    status = Column(String(20), default="pending")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow,
                        onupdate=datetime.utcnow)

    # Relationships
    learning_path = relationship("LearningPath", back_populates="path_items")
    certification = relationship("Certification")
    study_resources = relationship(
        "StudyResource", back_populates="learning_path_item")

    def to_dict(self) -> Dict:
        """Convert learning path item to dictionary with proper datetime serialization"""
        return {
            'id': self.id,
            'sequence': self.sequence,
            'item_type': self.item_type,
            'name': self.name,
            'description': self.description,
            'estimated_hours': self.estimated_hours,
            'status': self.status,
            'study_resources': [resource.to_dict() for resource in self.study_resources],
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'certification': self.certification.to_dict() if self.certification else None
        }


class StudyResource(Base):
    """
    Stores study resources for certifications and learning paths
    """
    __tablename__ = 'study_resources'

    id = Column(Integer, primary_key=True)
    learning_path_item_id = Column(Integer, ForeignKey(
        'learning_path_items.id'), nullable=False)
    # 'documentation', 'practice_exam', 'video', 'tutorial'
    resource_type = Column(String(50), nullable=False)
    title = Column(String(200), nullable=False)
    url = Column(String(500), nullable=False)
    description = Column(Text)
    # Whether it's official certification material
    is_official = Column(Boolean, default=False)
    rating = Column(Float)  # Community rating
    access_type = Column(String(50))  # 'free', 'paid', 'subscription'
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow,
                        onupdate=datetime.utcnow)

    # Relationships
    learning_path_item = relationship(
        "LearningPathItem", back_populates="study_resources")

    def to_dict(self) -> Dict:
        """Convert study resource to dictionary with proper datetime serialization"""
        return {
            'id': self.id,
            'resource_type': self.resource_type,
            'title': self.title,
            'url': self.url,
            'description': self.description,
            'is_official': self.is_official,
            'rating': self.rating,
            'access_type': self.access_type,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
