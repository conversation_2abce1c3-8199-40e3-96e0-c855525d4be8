from typing import TypedDict, List, Dict, Any, Optional
from datetime import datetime

class CertificationCost(TypedDict):
    exam: float
    materials: float
    training: float

class CareerPathStage(TypedDict):
    stage: str
    timeline: str
    certifications: List[str]
    certification_costs: Optional[Dict[str, CertificationCost]]

class CareerPathResponse(TypedDict):
    career_path: List[CareerPathStage]
    generated_at: datetime
    total_stages: int
