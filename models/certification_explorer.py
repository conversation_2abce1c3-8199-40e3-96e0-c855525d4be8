"""
Models for certification explorer feature
"""
import enum
from models.base import Base
from sqlalchemy.orm import relationship
from sqlalchemy import <PERSON>umn, Integer, String, Float, DateTime, Enum as SQLE<PERSON>, JSON, ForeignKey, Table
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class CertificationType(str, Enum):
    """Certification type enum"""
    SECURITY = "security"
    CLOUD = "cloud"
    NETWORKING = "networking"
    DEVELOPMENT = "development"
    DEVOPS = "devops"
    OTHER = "other"


class CertificationLevel(str, Enum):
    """Certification level enum"""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class SecurityDomain(str, Enum):
    """Security domain enum"""
    SECURITY_ENGINEERING = "Security Engineering"
    DEFENSIVE_SECURITY = "Defensive Security"
    SECURITY_MANAGEMENT = "Security Management"
    NETWORK_SECURITY = "Network Security"
    IDENTITY_ACCESS_MANAGEMENT = "Identity & Access Management"
    ASSET_SECURITY = "Asset Security"
    SECURITY_TESTING = "Security Testing"
    OFFENSIVE_SECURITY = "Offensive Security"


class CertificationBase(BaseModel):
    """Base certification model"""
    name: str
    provider: str
    description: Optional[str] = None
    type: CertificationType
    level: CertificationLevel
    domain: Optional[SecurityDomain] = None
    exam_code: Optional[str] = None
    price: Optional[float] = None
    duration_minutes: Optional[int] = None
    passing_score: Optional[int] = None
    prerequisites: Optional[List[str]] = None
    skills_covered: Optional[List[str]] = None
    url: Optional[str] = None


class CertificationCreate(CertificationBase):
    """Certification creation model"""
    pass


class CertificationUpdate(BaseModel):
    """Certification update model"""
    name: Optional[str] = None
    provider: Optional[str] = None
    description: Optional[str] = None
    type: Optional[CertificationType] = None
    level: Optional[CertificationLevel] = None
    domain: Optional[SecurityDomain] = None
    exam_code: Optional[str] = None
    price: Optional[float] = None
    duration_minutes: Optional[int] = None
    passing_score: Optional[int] = None
    prerequisites: Optional[List[str]] = None
    skills_covered: Optional[List[str]] = None
    url: Optional[str] = None


class CertificationModel(CertificationBase):
    """Certification response model"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


class CertificationFilterParams(BaseModel):
    """Parameters for filtering certifications"""
    provider: Optional[str] = None
    type: Optional[CertificationType] = None
    level: Optional[CertificationLevel] = None
    domain: Optional[SecurityDomain] = None
    search: Optional[str] = None
    min_price: Optional[float] = None
    max_price: Optional[float] = None


class CertificationListResponse(BaseModel):
    """Response model for certification list"""
    certifications: List[CertificationModel]
    total: int
    page: int
    page_size: int


# SQLAlchemy models

# Association table for certification prerequisites
certification_prerequisites = Table(
    'certification_explorer_prerequisites',
    Base.metadata,
    Column('certification_id', Integer, ForeignKey(
        'certification_explorer.id'), primary_key=True),
    Column('prerequisite_id', Integer, ForeignKey(
        'certification_explorer.id'), primary_key=True),
    extend_existing=True
)


class CertificationTypeEnum(enum.Enum):
    """Certification type enum for SQLAlchemy"""
    SECURITY = "security"
    CLOUD = "cloud"
    NETWORKING = "networking"
    DEVELOPMENT = "development"
    DEVOPS = "devops"
    OTHER = "other"


class CertificationLevelEnum(enum.Enum):
    """Certification level enum for SQLAlchemy"""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class SecurityDomainEnum(enum.Enum):
    """Security domain enum for SQLAlchemy"""
    SECURITY_ENGINEERING = "Security Engineering"
    DEFENSIVE_SECURITY = "Defensive Security"
    SECURITY_MANAGEMENT = "Security Management"
    NETWORK_SECURITY = "Network Security"
    IDENTITY_ACCESS_MANAGEMENT = "Identity & Access Management"
    ASSET_SECURITY = "Asset Security"
    SECURITY_TESTING = "Security Testing"
    OFFENSIVE_SECURITY = "Offensive Security"


class CertificationExplorer(Base):
    """Certification SQLAlchemy model for the explorer feature"""
    __tablename__ = "certification_explorer"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    provider = Column(String(100), nullable=False)
    description = Column(String(1000), nullable=True)
    type = Column(SQLEnum(CertificationTypeEnum), nullable=False)
    level = Column(SQLEnum(CertificationLevelEnum), nullable=False)
    domain = Column(SQLEnum(SecurityDomainEnum), nullable=True)
    exam_code = Column(String(50), nullable=True)
    price = Column(Float, nullable=True)
    duration_minutes = Column(Integer, nullable=True)
    passing_score = Column(Integer, nullable=True)
    # Store prerequisites as JSON array
    prerequisites_list = Column(JSON, nullable=True)
    skills_covered = Column(JSON, nullable=True)
    url = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow,
                        onupdate=datetime.utcnow)

    # Relationships
    prerequisite_certs = relationship(
        "CertificationExplorer",
        secondary=certification_prerequisites,
        primaryjoin=(certification_prerequisites.c.certification_id == id),
        secondaryjoin=(certification_prerequisites.c.prerequisite_id == id),
        backref="required_for"
    )

    def __repr__(self):
        return f"<CertificationExplorer(id={self.id}, name='{self.name}', provider='{self.provider}')>"
