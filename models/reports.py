"""
Models for reports feature
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class ReportType(str, Enum):
    """Report type enum"""
    CERTIFICATION_PROGRESS = "certification_progress"
    USER_ACTIVITY = "user_activity"
    CERTIFICATION_POPULARITY = "certification_popularity"
    SKILL_GAPS = "skill_gaps"
    CUSTOM = "custom"

class ReportFormat(str, Enum):
    """Report format enum"""
    PDF = "pdf"
    CSV = "csv"
    JSON = "json"
    HTML = "html"

class ReportFrequency(str, Enum):
    """Report frequency enum for scheduled reports"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"
    ONCE = "once"

class ReportBase(BaseModel):
    """Base report model"""
    title: str
    description: Optional[str] = None
    type: ReportType
    parameters: Dict[str, Any] = Field(default_factory=dict)
    format: ReportFormat = ReportFormat.PDF
    is_scheduled: bool = False
    frequency: Optional[ReportFrequency] = None
    next_run_date: Optional[datetime] = None
    user_id: Optional[int] = None

class ReportCreate(ReportBase):
    """Report creation model"""
    pass

class ReportUpdate(BaseModel):
    """Report update model"""
    title: Optional[str] = None
    description: Optional[str] = None
    type: Optional[ReportType] = None
    parameters: Optional[Dict[str, Any]] = None
    format: Optional[ReportFormat] = None
    is_scheduled: Optional[bool] = None
    frequency: Optional[ReportFrequency] = None
    next_run_date: Optional[datetime] = None

class ReportModel(ReportBase):
    """Report response model"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_run_at: Optional[datetime] = None
    
    class Config:
        orm_mode = True

class ReportListResponse(BaseModel):
    """Response model for report list"""
    reports: List[ReportModel]
    total: int
    page: int
    page_size: int

class ReportFilterParams(BaseModel):
    """Parameters for filtering reports"""
    type: Optional[ReportType] = None
    is_scheduled: Optional[bool] = None
    frequency: Optional[ReportFrequency] = None
    search: Optional[str] = None
    user_id: Optional[int] = None

class ReportResult(BaseModel):
    """Report generation result model"""
    report_id: int
    title: str
    type: ReportType
    format: ReportFormat
    generated_at: datetime
    data: Dict[str, Any]
    file_url: Optional[str] = None

# SQLAlchemy models
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Enum, JSON, ForeignKey
from sqlalchemy.orm import relationship
from database import Base
from datetime import datetime
import enum

class ReportTypeEnum(enum.Enum):
    """Report type enum for SQLAlchemy"""
    CERTIFICATION_PROGRESS = "certification_progress"
    USER_ACTIVITY = "user_activity"
    CERTIFICATION_POPULARITY = "certification_popularity"
    SKILL_GAPS = "skill_gaps"
    CUSTOM = "custom"

class ReportFormatEnum(enum.Enum):
    """Report format enum for SQLAlchemy"""
    PDF = "pdf"
    CSV = "csv"
    JSON = "json"
    HTML = "html"

class ReportFrequencyEnum(enum.Enum):
    """Report frequency enum for SQLAlchemy"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"
    ONCE = "once"

class Report(Base):
    """Report SQLAlchemy model"""
    __tablename__ = "reports"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    description = Column(String(1000), nullable=True)
    type = Column(Enum(ReportTypeEnum), nullable=False)
    parameters = Column(JSON, nullable=True)
    format = Column(Enum(ReportFormatEnum), nullable=False, default=ReportFormatEnum.PDF)
    is_scheduled = Column(Boolean, default=False, nullable=False)
    frequency = Column(Enum(ReportFrequencyEnum), nullable=True)
    next_run_date = Column(DateTime, nullable=True)
    last_run_at = Column(DateTime, nullable=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="reports")

    def __repr__(self):
        return f"<Report(id={self.id}, title='{self.title}', type='{self.type}')>"
