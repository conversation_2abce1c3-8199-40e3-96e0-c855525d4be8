from sqlalchemy import Column, Integer, String, Float, Text, Table, ForeignKey, DateTime, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime
from models.mixins import SoftDeleteMixin
from models.base import Base
from models.user import User

class Organization(Base, SoftDeleteMixin):
    """
    Organization that issues certifications
    """
    __tablename__ = 'organizations'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), unique=True, nullable=False)
    country = Column(String(100), nullable=False)
    url = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # One-to-many relationship with certifications
    certifications = relationship("Certification", back_populates="organization")

    def to_dict(self):
        """Convert organization to dictionary representation"""
        return {
            'id': self.id,
            'name': self.name,
            'country': self.country,
            'url': self.url,
            'description': self.description,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'is_deleted': self.is_deleted,
            'deleted_at': self.deleted_at
        }

class Domain(Base, SoftDeleteMixin):
    """
    Domain for certifications (e.g., Network Security, Cloud Security)
    """
    __tablename__ = 'domains'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Many-to-many relationship with certifications
    certifications = relationship("Certification", secondary="certification_domains", back_populates="domains")

    def to_dict(self):
        """Convert domain to dictionary representation"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'is_deleted': self.is_deleted,
            'deleted_at': self.deleted_at
        }

# Association table for certification prerequisites
certification_prerequisites = Table(
    'certification_prerequisites',
    Base.metadata,
    Column('certification_id', Integer, ForeignKey('certifications.id'), primary_key=True),
    Column('prerequisite_id', Integer, ForeignKey('certifications.id'), primary_key=True)
)

# Association table for certification domains
certification_domains = Table(
    'certification_domains',
    Base.metadata,
    Column('certification_id', Integer, ForeignKey('certifications.id'), primary_key=True),
    Column('domain_id', Integer, ForeignKey('domains.id'), primary_key=True)
)

class CertificationVersion(Base, SoftDeleteMixin):
    """
    Historical version of a certification
    """
    __tablename__ = 'certification_versions'

    id = Column(Integer, primary_key=True)
    certification_id = Column(Integer, ForeignKey('certifications.id'), nullable=False)
    version = Column(Integer, nullable=False)
    name = Column(String(100), nullable=False)
    category = Column(String(100), nullable=False)
    domain = Column(String(50), nullable=False)
    level = Column(String(50), nullable=False)
    difficulty = Column(Integer, nullable=False)
    cost = Column(Float, nullable=True)
    description = Column(Text, nullable=True)
    prerequisites = Column(Text, nullable=True)
    validity_period = Column(Integer, nullable=True)
    exam_code = Column(String(50), nullable=True)
    organization_id = Column(Integer, ForeignKey('organizations.id'), nullable=True)
    url = Column(String(255), nullable=True)
    custom_hours = Column(Integer, nullable=True)
    study_notes = Column(Text, nullable=True)
    course_content = Column(Text, nullable=True)
    changed_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    change_reason = Column(Text, nullable=True)
    change_source = Column(String(100), nullable=True)
    reference_url = Column(String(255), nullable=True)

    def to_dict(self):
        """Convert version to dictionary representation"""
        base_dict = {
            'version': self.version,
            'name': self.name,
            'category': self.category,
            'domain': self.domain,
            'level': self.level,
            'difficulty': self.difficulty,
            'cost': self.cost,
            'description': self.description,
            'prerequisites': self.prerequisites,
            'validity_period': self.validity_period,
            'exam_code': self.exam_code,
            'organization_id': self.organization_id,
            'url': self.url,
            'custom_hours': self.custom_hours,
            'study_notes': self.study_notes,
            'course_content': self.course_content,
            'changed_at': self.changed_at,
            'change_reason': self.change_reason,
            'change_source': self.change_source,
            'reference_url': self.reference_url
        }
        # Add soft delete info
        base_dict.update({
            'is_deleted': self.is_deleted,
            'deleted_at': self.deleted_at
        })
        return base_dict

class Certification(Base, SoftDeleteMixin):
    """
    Certification model representing security certifications in the database
    """
    __tablename__ = 'certifications'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), unique=True, nullable=False)
    category = Column(String(100), nullable=False)
    domain = Column(String(50), nullable=False)
    level = Column(String(50), nullable=False)
    difficulty = Column(Integer, nullable=False)
    cost = Column(Float, nullable=True)
    description = Column(Text, nullable=True)
    prerequisites = Column(Text, nullable=True)
    validity_period = Column(Integer, nullable=True)
    exam_code = Column(String(50), nullable=True)
    organization_id = Column(Integer, ForeignKey('organizations.id'), nullable=True)
    url = Column(String(255), nullable=True)
    custom_hours = Column(Integer, nullable=True)
    study_notes = Column(Text, nullable=True)
    course_content = Column(Text, nullable=True)
    current_version = Column(Integer, default=1, nullable=False)
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Define relationships
    prerequisite_certifications = relationship(
        'Certification',
        secondary=certification_prerequisites,
        primaryjoin=id==certification_prerequisites.c.certification_id,
        secondaryjoin=id==certification_prerequisites.c.prerequisite_id,
        backref='required_for'
    )

    # Many-to-many relationship with domains
    domains = relationship("Domain", secondary=certification_domains, back_populates="certifications")

    versions = relationship("CertificationVersion", backref="certification", order_by="CertificationVersion.version.desc()")
    organization = relationship("Organization", back_populates="certifications")

    def to_dict(self):
        """Convert certification to dictionary representation"""
        base_dict = {
            'name': self.name,
            'category': self.category,
            'domain': self.domain,
            'level': self.level,
            'difficulty': self.difficulty,
            'cost': self.cost,
            'description': self.description,
            'prerequisites': self.prerequisites,
            'validity_period': self.validity_period,
            'exam_code': self.exam_code,
            'organization_id': self.organization_id,
            'url': self.url,
            'custom_hours': self.custom_hours,
            'study_notes': self.study_notes,
            'course_content': self.course_content,
            'current_version': self.current_version,
            'last_updated': self.last_updated,
            'created_at': self.created_at
        }
        # Add soft delete info
        base_dict.update({
            'is_deleted': self.is_deleted,
            'deleted_at': self.deleted_at
        })
        return base_dict

    def create_version(self, reason=None, source='manual', reference_url=None):
        """Create a new version record for the current state"""
        version = CertificationVersion(
            certification_id=self.id,
            version=self.current_version,
            name=self.name,
            category=self.category,
            domain=self.domain,
            level=self.level,
            difficulty=self.difficulty,
            cost=self.cost,
            description=self.description,
            prerequisites=self.prerequisites,
            validity_period=self.validity_period,
            exam_code=self.exam_code,
            organization_id=self.organization_id,
            url=self.url,
            custom_hours=self.custom_hours,
            study_notes=self.study_notes,
            course_content=self.course_content,
            change_reason=reason,
            change_source=source,
            reference_url=reference_url
        )
        self.current_version += 1
        return version

class UserCertification(Base, SoftDeleteMixin):
    """
    Association between users and certifications
    Tracks which certifications a user has obtained or is pursuing
    """
    __tablename__ = 'user_certifications'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    certification_id = Column(Integer, ForeignKey('certifications.id'), nullable=False)
    status = Column(String(20), default='pursuing', nullable=False)  # pursuing, obtained, expired, etc.
    obtained_date = Column(DateTime, nullable=True)
    expiry_date = Column(DateTime, nullable=True)
    progress = Column(Integer, default=0, nullable=False)  # Progress percentage (0-100)
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Define relationships
    certification = relationship("Certification")
    user = relationship("User")

    def to_dict(self):
        """Convert user certification to dictionary representation"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'certification_id': self.certification_id,
            'status': self.status,
            'obtained_date': self.obtained_date,
            'expiry_date': self.expiry_date,
            'progress': self.progress,
            'notes': self.notes,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'is_deleted': self.is_deleted,
            'deleted_at': self.deleted_at
        }