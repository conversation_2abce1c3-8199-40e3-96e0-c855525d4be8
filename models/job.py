"""
SQLAlchemy model for security jobs
"""
from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, Boolean, CheckConstraint, UniqueConstraint
from sqlalchemy.orm import relationship
from datetime import datetime
from models.mixins import SoftDeleteMixin
from models.base import Base


class SecurityJob(Base, SoftDeleteMixin):
    """
    Security job model representing cybersecurity positions across different domains
    """
    __tablename__ = 'security_jobs'

    id = Column(Integer, primary_key=True)
    title = Column(String(255), nullable=False)
    # e.g., "Defensive Security", "Network Security"
    domain = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    # e.g., "Entry Level", "Senior"
    experience_level = Column(String(50), nullable=True)
    salary_range_min = Column(Integer, nullable=True)
    salary_range_max = Column(Integer, nullable=True)
    # Stored as comma-separated values
    skills_required = Column(Text, nullable=True)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False,
                        default=datetime.utcnow, onupdate=datetime.utcnow)

    # Add constraints
    __table_args__ = (
        CheckConstraint(
            'salary_range_min IS NULL OR salary_range_max IS NULL OR salary_range_min <= salary_range_max'),
        CheckConstraint(
            "experience_level IN ('Entry Level', 'Mid Level', 'Senior', 'Lead', 'Principal')"),
        CheckConstraint("title <> ''"),  # Ensure title is not empty
        CheckConstraint("domain <> ''"),  # Ensure domain is not empty
        UniqueConstraint('title', 'domain', name='uix_job_title_domain')
    )

    def __init__(self, **kwargs):
        now = datetime.utcnow()
        kwargs.setdefault('created_at', now)
        kwargs.setdefault('updated_at', now)
        super().__init__(**kwargs)

    def to_dict(self):
        """Convert job to dictionary representation"""
        base_dict = {
            'id': self.id,
            'title': self.title,
            'domain': self.domain,
            'description': self.description,
            'experience_level': self.experience_level,
            'salary_range_min': self.salary_range_min,
            'salary_range_max': self.salary_range_max,
            'skills_required': self.skills_required.split(',') if self.skills_required else [],
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
        # Add soft delete info
        base_dict.update({
            'is_deleted': self.is_deleted,
            'deleted_at': self.deleted_at
        })
        return base_dict
