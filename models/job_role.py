"""Job role and domain relationship models"""
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, ForeignKey, Table
from sqlalchemy.orm import relationship
from models.base import Base

# Association table for job roles and their responsibilities
job_responsibilities = Table(
    'job_responsibilities',
    Base.metadata,
    Column('job_id', Integer, Foreign<PERSON><PERSON>('job_roles.id'), primary_key=True),
    Column('responsibility', String, primary_key=True)
)


class JobRole(Base):
    """Job role model"""
    __tablename__ = 'job_roles'

    id = Column(Integer, primary_key=True)
    title = Column(String, nullable=False, unique=True)
    description = Column(String)
    domain = Column(String, nullable=False)

    # Relationship to responsibilities through association table
    responsibilities = relationship(
        "String",
        secondary=job_responsibilities,
        collection_class=list
    )

    def to_dict(self):
        """Convert job role to dictionary"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'domain': self.domain,
            'key_responsibilities': self.responsibilities if self.responsibilities else []
        }
