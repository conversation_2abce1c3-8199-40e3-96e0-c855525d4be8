"""
Models for user management feature
"""
from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List, Dict
from datetime import datetime
from enum import Enum

class UserRole(str, Enum):
    """User role enum"""
    ADMIN = "admin"
    USER = "user"
    GUEST = "guest"

class UserStatus(str, Enum):
    """User status enum"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    SUSPENDED = "suspended"

class UserBase(BaseModel):
    """Base user model"""
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    role: UserRole = UserRole.USER
    status: UserStatus = UserStatus.ACTIVE

class UserCreate(UserBase):
    """User creation model"""
    password: str = Field(..., min_length=8)

class UserUpdate(BaseModel):
    """User update model"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    role: Optional[UserRole] = None
    status: Optional[UserStatus] = None
    password: Optional[str] = Field(None, min_length=8)

class UserResponse(UserBase):
    """User response model"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    
    class Config:
        orm_mode = True

class UserPreference(BaseModel):
    """User preference model"""
    user_id: int
    theme: Optional[str] = "light"
    language: Optional[str] = "en"
    notifications_enabled: Optional[bool] = True
    email_notifications: Optional[bool] = True
    
    class Config:
        orm_mode = True

class UserActivity(BaseModel):
    """User activity model"""
    user_id: int
    action: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    details: Optional[Dict] = None
    
    class Config:
        orm_mode = True

class UserStats(BaseModel):
    """User statistics model"""
    total_users: int
    active_users: int
    users_by_role: Dict[str, int]
    users_by_status: Dict[str, int]
    new_users_last_30_days: int
    
    class Config:
        orm_mode = True

class UserListResponse(BaseModel):
    """User list response model"""
    users: List[UserResponse]
    total: int
    page: int
    page_size: int
    
    class Config:
        orm_mode = True
