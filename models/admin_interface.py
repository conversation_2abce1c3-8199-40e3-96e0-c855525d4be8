"""
Models for admin interface feature
"""
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime

class AdminSettingBase(BaseModel):
    """Base admin setting model"""
    key: str
    value: Any
    description: Optional[str] = None
    category: str

class AdminSettingCreate(AdminSettingBase):
    """Admin setting creation model"""
    pass

class AdminSettingUpdate(BaseModel):
    """Admin setting update model"""
    value: Any
    description: Optional[str] = None
    category: Optional[str] = None

class AdminSetting(AdminSettingBase):
    """Admin setting response model"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        orm_mode = True

class SystemStatusBase(BaseModel):
    """System status information"""
    component: str
    status: str
    message: Optional[str] = None
    last_checked: datetime

class SystemStatus(BaseModel):
    """Overall system status"""
    status: str
    components: List[SystemStatusBase]
    resources: Dict[str, Any]
    uptime: int  # in seconds

class AuditLogBase(BaseModel):
    """Base audit log model"""
    user_id: Optional[int] = None
    action: str
    resource_type: str
    resource_id: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None

class AuditLogCreate(AuditLogBase):
    """Audit log creation model"""
    pass

class AuditLog(AuditLogBase):
    """Audit log response model"""
    id: int
    timestamp: datetime
    
    class Config:
        orm_mode = True
