"""
Models for admin interface feature
"""
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON
from models.base import Base


class AdminSettingBase(BaseModel):
    """Base admin setting model"""
    key: str
    value: Any
    description: Optional[str] = None
    category: str


class AdminSettingCreate(AdminSettingBase):
    """Admin setting creation model"""
    pass


class AdminSettingUpdate(BaseModel):
    """Admin setting update model"""
    value: Any
    description: Optional[str] = None
    category: Optional[str] = None


class AdminSettingResponse(AdminSettingBase):
    """Admin setting response model"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class SystemStatusBase(BaseModel):
    """System status information"""
    component: str
    status: str
    message: Optional[str] = None
    last_checked: datetime


class SystemStatus(BaseModel):
    """Overall system status"""
    status: str
    components: List[SystemStatusBase]
    resources: Dict[str, Any]
    uptime: int  # in seconds


class AuditLogBase(BaseModel):
    """Base audit log model"""
    user_id: Optional[int] = None
    action: str
    resource_type: str
    resource_id: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None


class AuditLogCreate(AuditLogBase):
    """Audit log creation model"""
    pass


class AuditLogResponse(AuditLogBase):
    """Audit log response model"""
    id: int
    timestamp: datetime

    class Config:
        from_attributes = True


# SQLAlchemy models
class AdminSetting(Base):
    """SQLAlchemy model for admin settings"""
    __tablename__ = "admin_settings"

    id = Column(Integer, primary_key=True)
    key = Column(String(100), unique=True, nullable=False)
    value = Column(Text, nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String(50), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow,
                        onupdate=datetime.utcnow)


class AuditLog(Base):
    """SQLAlchemy model for audit logs"""
    __tablename__ = "audit_logs"

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, nullable=True)
    action = Column(String(100), nullable=False)
    resource_type = Column(String(50), nullable=False)
    resource_id = Column(String(100), nullable=True)
    details = Column(JSON, nullable=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 max length
    timestamp = Column(DateTime, default=datetime.utcnow)
