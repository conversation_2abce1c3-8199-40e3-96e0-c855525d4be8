"""
Certification data schema and validation.
"""
from typing import Dict, List, Optional, Union
from pydantic import BaseModel, Field, HttpUrl, validator
from datetime import datetime
from decimal import Decimal

class CertificationBase(BaseModel):
    """Base Certification Schema"""
    name: str = Field(..., description="Certification name/identifier")
    category: str = Field(..., description="Category of certification")
    description: str = Field(..., description="Detailed description")
    url: HttpUrl = Field(..., description="Official certification URL")
    cost: Optional[Decimal] = Field(None, description="Cost in USD")
    difficulty: int = Field(..., ge=1, le=4, description="Difficulty level (1-4)")
    focus: str = Field(..., description="Primary focus area")
    domain: str = Field(..., description="Security domain")
    prerequisites: List[str] = Field(default_factory=list, description="Required prerequisites")
    validity_period: Optional[int] = Field(None, description="Validity period in months")
    related_certs: Optional[Dict[str, List[str]]] = Field(
        None,
        description="Related certifications mapping"
    )
    custom_hours: Optional[int] = Field(None, description="Custom study hours if specified")
    study_notes: Optional[str] = Field(None, description="Additional study notes")
    last_updated: datetime = Field(default_factory=datetime.utcnow)

    @validator('domain')
    def validate_domain(cls, v):
        """Validate and normalize security domain."""
        valid_domains = {
            'Security Engineering',
            'Defensive Security',
            'Security Management',
            'Network Security',
            'Identity & Access Management',
            'Asset Security',
            'Security Testing',
            'Offensive Security'
        }
        if v not in valid_domains:
            raise ValueError(f'Invalid domain. Must be one of: {", ".join(valid_domains)}')
        return v

    @validator('focus')
    def validate_focus(cls, v):
        """Validate and normalize focus area."""
        valid_focuses = {
            'Engineer',
            'Blueops',
            'Mgmt',
            'Network',
            'Iam',
            'Asset',
            'Test',
            'Redops'
        }
        if v not in valid_focuses:
            raise ValueError(f'Invalid focus. Must be one of: {", ".join(valid_focuses)}')
        return v

    class Config:
        """Pydantic model configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            Decimal: lambda v: str(v)
        }
        extra = "forbid"  # Prevent additional fields

    def normalize(self) -> Dict:
        """
        Normalize certification data to standardized format.
        
        Returns:
            Dict: Normalized certification data
        """
        return {
            "name": self.name.strip(),
            "category": self.category.strip(),
            "description": self.description.strip(),
            "url": str(self.url),
            "cost": float(self.cost) if self.cost else None,
            "difficulty": self.difficulty,
            "focus": self.focus,
            "domain": self.domain,
            "prerequisites": [p.strip() for p in self.prerequisites],
            "validity_period": self.validity_period,
            "related_certs": self.related_certs,
            "custom_hours": self.custom_hours,
            "study_notes": self.study_notes.strip() if self.study_notes else None,
            "last_updated": self.last_updated.isoformat()
        }
