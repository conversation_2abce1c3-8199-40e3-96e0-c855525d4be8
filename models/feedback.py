"""
SQLAlchemy model for user feedback
"""
from sqlalchemy import Column, Integer, String, Text, DateTime
from sqlalchemy.sql import func
from models.certification import Base

class Feedback(Base):
    """
    Feedback model for storing user feedback
    """
    __tablename__ = 'feedback'

    id = Column(Integer, primary_key=True)
    feedback_type = Column(String(50), nullable=False)
    feedback_text = Column(Text, nullable=False)
    rating = Column(Integer, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    def to_dict(self):
        """Convert feedback to dictionary representation"""
        return {
            'id': self.id,
            'feedback_type': self.feedback_type,
            'feedback_text': self.feedback_text,
            'rating': self.rating,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
