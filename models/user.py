"""
SQLAlchemy models for user management
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Enum, ForeignKey, Table, Text, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from models.base import Base
from models.mixins import SoftDeleteMixin


class UserRole(enum.Enum):
    """User role enum"""
    ADMIN = "ADMIN"
    USER = "USER"
    GUEST = "GUEST"


class UserStatus(enum.Enum):
    """User status enum"""
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    PENDING = "PENDING"
    SUSPENDED = "SUSPENDED"


class User(Base, SoftDeleteMixin):
    """User model"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    password = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=True)
    role = Column(Enum(UserRole, name='userrole'),
                  default=UserRole.USER, nullable=False)
    status = Column(Enum(UserStatus, name='userstatus'),
                    default=UserStatus.ACTIVE, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow,
                        onupdate=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)

    # Relationships
    preferences = relationship(
        "UserPreference", back_populates="user", uselist=False)
    activities = relationship("UserActivity", back_populates="user")
    reports = relationship("Report", back_populates="user")

    @property
    def is_active(self) -> bool:
        """Check if user is active"""
        return self.status == UserStatus.ACTIVE

    @property
    def is_admin(self) -> bool:
        """Check if user has admin role"""
        return self.role == UserRole.ADMIN

    def __repr__(self):
        return f"<User {self.username}>"


class UserPreference(Base):
    """User preference model"""
    __tablename__ = "user_preferences"

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"),
                     unique=True, nullable=False)
    theme = Column(String(20), default="light", nullable=False)
    language = Column(String(10), default="en", nullable=False)
    notifications_enabled = Column(Boolean, default=True, nullable=False)
    email_notifications = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow,
                        onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="preferences")

    def __repr__(self):
        return f"<UserPreference user_id={self.user_id}>"


class UserActivity(Base):
    """User activity model"""
    __tablename__ = "user_activities"

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    action = Column(String(50), nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    details = Column(JSON, nullable=True)

    # Relationships
    user = relationship("User", back_populates="activities")

    def __repr__(self):
        return f"<UserActivity user_id={self.user_id} action={self.action}>"
