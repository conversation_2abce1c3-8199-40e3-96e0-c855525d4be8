"""
Models for dashboard feature
"""
from pydantic import BaseModel
from typing import Optional, List, Dict
from datetime import datetime

class CertificationSummary(BaseModel):
    """Summary information about a certification"""
    id: int
    name: str
    organization: str
    domain: str
    level: str
    cost: Optional[float] = None
    popularity: Optional[int] = None

class OrganizationStat(BaseModel):
    """Statistics for an organization"""
    name: str
    certification_count: int
    logo_url: Optional[str] = None

class DomainStat(BaseModel):
    """Statistics for a security domain"""
    name: str
    certification_count: int
    description: Optional[str] = None

class DashboardStats(BaseModel):
    """Dashboard statistics"""
    total_certifications: int
    total_organizations: int
    total_domains: int
    certifications_by_level: Dict[str, int]
    average_cost: Optional[float] = None

class DashboardData(BaseModel):
    """Dashboard data model"""
    stats: DashboardStats
    recent_certifications: List[CertificationSummary]
    popular_certifications: List[CertificationSummary]
    organizations: List[OrganizationStat]
    domains: List[DomainStat]
    
    class Config:
        orm_mode = True

class DashboardFilterParams(BaseModel):
    """Parameters for filtering dashboard data"""
    organization_id: Optional[int] = None
    domain: Optional[str] = None
    level: Optional[str] = None
    cost_min: Optional[float] = None
    cost_max: Optional[float] = None

class DashboardBase(BaseModel):
    """Base dashboard model"""
    name: str
    description: Optional[str] = None

class DashboardCreate(DashboardBase):
    """Dashboard creation model"""
    pass

class DashboardUpdate(DashboardBase):
    """Dashboard update model"""
    name: Optional[str] = None

class DashboardModel(DashboardBase):
    """Dashboard response model"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        orm_mode = True
