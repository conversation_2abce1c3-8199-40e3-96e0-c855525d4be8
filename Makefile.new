# CertRats Production-Ready Development Makefile
# ==============================================

.PHONY: help install install-dev install-prod test test-backend test-frontend test-e2e test-coverage \
        run run-backend run-frontend run-dev run-prod build build-backend build-frontend \
        lint lint-backend lint-frontend format format-backend format-frontend \
        clean clean-all docker-build docker-run docker-stop docker-clean \
        db-migrate db-upgrade db-downgrade db-reset security-scan setup-dev

# Colors for output
BLUE := \033[0;34m
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

# Default target
.DEFAULT_GOAL := help

# Help target
help: ## Show this help message
	@echo "$(BLUE)CertRats Development Commands$(NC)"
	@echo "=============================="
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "$(GREEN)%-20s$(NC) %s\n", $$1, $$2}'

# =============================================================================
# INSTALLATION TARGETS
# =============================================================================

install: install-backend install-frontend ## Install all dependencies

install-dev: ## Install development dependencies
	@echo "$(BLUE)Installing development dependencies...$(NC)"
	pip install -r requirements.txt
	npm install
	cd frontend && npm install
	@echo "$(GREEN)Development dependencies installed!$(NC)"

install-backend: ## Install Python backend dependencies
	@echo "$(BLUE)Installing backend dependencies...$(NC)"
	pip install -r requirements.txt
	@echo "$(GREEN)Backend dependencies installed!$(NC)"

install-frontend: ## Install Node.js frontend dependencies
	@echo "$(BLUE)Installing frontend dependencies...$(NC)"
	npm install
	cd frontend && npm install
	@echo "$(GREEN)Frontend dependencies installed!$(NC)"

# =============================================================================
# TESTING TARGETS
# =============================================================================

test: test-backend test-frontend ## Run all tests

test-backend: ## Run backend tests
	@echo "$(BLUE)Running backend tests...$(NC)"
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/ -v --tb=short

test-frontend: ## Run Next.js frontend tests
	@echo "$(BLUE)Running Next.js frontend tests...$(NC)"
	npm test --if-present

test-react: ## Run React frontend tests
	@echo "$(BLUE)Running React frontend tests...$(NC)"
	cd frontend && npm test

test-e2e: ## Run end-to-end tests
	@echo "$(BLUE)Running E2E tests...$(NC)"
	cd frontend && npm run test:e2e

test-coverage: ## Run tests with coverage report
	@echo "$(BLUE)Running tests with coverage...$(NC)"
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/ --cov=. --cov-report=html --cov-report=term --cov-report=xml
	@echo "$(GREEN)Coverage report generated in htmlcov/$(NC)"

test-integration: ## Run integration tests
	@echo "$(BLUE)Running integration tests...$(NC)"
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/test_api_integration/ -v

# =============================================================================
# DEVELOPMENT TARGETS
# =============================================================================

run: run-backend run-frontend ## Run both backend and frontend in development mode

run-backend: ## Run backend development server
	@echo "$(BLUE)Starting backend development server...$(NC)"
	python -m uvicorn api.app:app --reload --host 0.0.0.0 --port 8000

run-frontend: ## Run Next.js frontend development server
	@echo "$(BLUE)Starting frontend development server...$(NC)"
	npm run dev

run-react: ## Run React frontend development server
	@echo "$(BLUE)Starting React frontend development server...$(NC)"
	cd frontend && npm start

run-dev: ## Run development environment with Docker
	@echo "$(BLUE)Starting development environment...$(NC)"
	docker-compose -f docker/docker-compose.dev.yml up

run-prod: ## Run production environment with Docker
	@echo "$(BLUE)Starting production environment...$(NC)"
	docker-compose -f docker/docker-compose.yml up

# =============================================================================
# BUILD TARGETS
# =============================================================================

build: build-backend build-frontend ## Build all components

build-backend: ## Build backend (prepare for production)
	@echo "$(BLUE)Building backend...$(NC)"
	python -m compileall api/ models/ utils/
	@echo "$(GREEN)Backend build complete!$(NC)"

build-frontend: ## Build Next.js frontend for production
	@echo "$(BLUE)Building frontend...$(NC)"
	npm run build
	@echo "$(GREEN)Frontend build complete!$(NC)"

build-react: ## Build React frontend for production
	@echo "$(BLUE)Building React frontend...$(NC)"
	cd frontend && npm run build
	@echo "$(GREEN)React frontend build complete!$(NC)"

# =============================================================================
# CODE QUALITY TARGETS
# =============================================================================

lint: lint-backend lint-frontend ## Run all linting

lint-backend: ## Run backend linting
	@echo "$(BLUE)Linting backend code...$(NC)"
	flake8 api/ models/ utils/ tests/ --max-line-length=100
	mypy api/ models/ utils/ --ignore-missing-imports

lint-frontend: ## Run frontend linting
	@echo "$(BLUE)Linting frontend code...$(NC)"
	npm run lint --if-present
	cd frontend && npm run lint --if-present

format: format-backend format-frontend ## Format all code

format-backend: ## Format backend code
	@echo "$(BLUE)Formatting backend code...$(NC)"
	black api/ models/ utils/ tests/
	@echo "$(GREEN)Backend code formatted!$(NC)"

format-frontend: ## Format frontend code
	@echo "$(BLUE)Formatting frontend code...$(NC)"
	npm run format --if-present
	cd frontend && npm run format --if-present
	@echo "$(GREEN)Frontend code formatted!$(NC)"

security-scan: ## Run security scans
	@echo "$(BLUE)Running security scans...$(NC)"
	bandit -r api/ models/ utils/ -f json -o bandit-report.json
	safety check --json --output safety-report.json
	@echo "$(GREEN)Security scan complete! Check reports.$(NC)"

# =============================================================================
# DATABASE TARGETS
# =============================================================================

db-migrate: ## Create new database migration
	@echo "$(BLUE)Creating database migration...$(NC)"
	python -m alembic revision --autogenerate -m "$(msg)"

db-upgrade: ## Upgrade database to latest migration
	@echo "$(BLUE)Upgrading database...$(NC)"
	python -m alembic upgrade head

db-downgrade: ## Downgrade database by one migration
	@echo "$(BLUE)Downgrading database...$(NC)"
	python -m alembic downgrade -1

db-reset: ## Reset database (WARNING: destroys all data)
	@echo "$(RED)WARNING: This will destroy all database data!$(NC)"
	@read -p "Are you sure? [y/N] " -n 1 -r; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		echo "$(BLUE)Resetting database...$(NC)"; \
		python -m alembic downgrade base; \
		python -m alembic upgrade head; \
		echo "$(GREEN)Database reset complete!$(NC)"; \
	else \
		echo "$(YELLOW)Database reset cancelled.$(NC)"; \
	fi

# =============================================================================
# DOCKER TARGETS
# =============================================================================

docker-build: ## Build Docker images
	@echo "$(BLUE)Building Docker images...$(NC)"
	docker-compose -f docker/docker-compose.yml build
	@echo "$(GREEN)Docker images built!$(NC)"

docker-run: ## Run application with Docker
	@echo "$(BLUE)Starting application with Docker...$(NC)"
	docker-compose -f docker/docker-compose.yml up -d

docker-stop: ## Stop Docker containers
	@echo "$(BLUE)Stopping Docker containers...$(NC)"
	docker-compose -f docker/docker-compose.yml down

docker-clean: ## Clean Docker containers and images
	@echo "$(BLUE)Cleaning Docker containers and images...$(NC)"
	docker-compose -f docker/docker-compose.yml down -v --rmi all
	docker system prune -f

# =============================================================================
# UTILITY TARGETS
# =============================================================================

setup-dev: ## Setup development environment
	@echo "$(BLUE)Setting up development environment...$(NC)"
	./scripts/setup-dev.sh

clean: ## Clean temporary files
	@echo "$(BLUE)Cleaning temporary files...$(NC)"
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	rm -rf .pytest_cache
	rm -rf htmlcov
	rm -rf .coverage
	rm -rf .mypy_cache
	rm -rf bandit-report.json
	rm -rf safety-report.json
	@echo "$(GREEN)Cleanup complete!$(NC)"

clean-all: clean ## Clean all generated files including builds
	@echo "$(BLUE)Cleaning all generated files...$(NC)"
	rm -rf .next
	rm -rf node_modules
	cd frontend && rm -rf node_modules build
	rm -rf venv
	@echo "$(GREEN)Deep cleanup complete!$(NC)"

# =============================================================================
# CI/CD TARGETS
# =============================================================================

ci-test: ## Run CI test suite
	@echo "$(BLUE)Running CI test suite...$(NC)"
	make lint
	make test-coverage
	make security-scan
	@echo "$(GREEN)CI tests complete!$(NC)"

pre-commit: ## Run pre-commit checks
	@echo "$(BLUE)Running pre-commit checks...$(NC)"
	make format
	make lint
	make test
	@echo "$(GREEN)Pre-commit checks complete!$(NC)"
