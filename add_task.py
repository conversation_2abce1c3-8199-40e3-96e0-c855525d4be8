#!/usr/bin/env python3
import json
from datetime import datetime

# Read existing tasks
with open('tasks.json', 'r') as f:
    tasks_data = json.load(f)

# Read new task
with open('T12_certification_page.json', 'r') as f:
    new_task = json.load(f)

# Add new task to tasks list
tasks_data['tasks'].append(new_task)

# Update the timestamp
tasks_data['updated_at'] = datetime.now().isoformat()

# Write back to tasks.json
with open('tasks.json', 'w') as f:
    json.dump(tasks_data, f, indent=2)

print('Task T12 added successfully!') 