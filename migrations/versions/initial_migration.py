"""Initial migration

Revision ID: initial_migration_001
Create Date: 2025-02-24 21:35:00.000000
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# Revision identifiers
revision = 'initial_migration_001'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # Create user_experiences table
    op.create_table('user_experiences',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('user_id', sa.String(length=100), nullable=False),
        sa.Column('years_experience', sa.Integer(), nullable=False),
        sa.Column('user_role', sa.String(length=100), nullable=True),
        sa.Column('desired_role', sa.String(length=100), nullable=True),
        sa.Column('expertise_areas', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('preferred_learning_style', sa.String(length=50), nullable=True),
        sa.Column('study_time_available', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

def downgrade():
    op.drop_table('user_experiences')