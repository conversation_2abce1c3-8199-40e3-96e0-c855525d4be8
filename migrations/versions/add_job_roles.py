"""Add job roles and responsibilities

Revision ID: add_job_roles
Create Date: 2025-02-26 12:04:00.000000
"""
from alembic import op
import sqlalchemy as sa

# Revision identifiers
revision = 'add_job_roles_001'  # unique identifier
down_revision = 'initial_migration_001'  # depends on initial migration
branch_labels = None
depends_on = None

def upgrade():
    # Create job_roles table
    op.create_table(
        'job_roles',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('title', sa.String(), nullable=False),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('domain', sa.String(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('title')
    )

    # Create job_responsibilities table as an association table
    op.create_table(
        'job_responsibilities',
        sa.Column('job_id', sa.Integer(), nullable=False),
        sa.Column('responsibility', sa.String(), nullable=False),
        sa.ForeignKeyConstraint(['job_id'], ['job_roles.id'], ),
        sa.PrimaryKeyConstraint('job_id', 'responsibility')
    )

def downgrade():
    op.drop_table('job_responsibilities')
    op.drop_table('job_roles')