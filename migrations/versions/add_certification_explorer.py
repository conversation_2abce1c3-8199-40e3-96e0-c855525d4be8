"""add certification explorer

Revision ID: a64e920df7fa
Revises: 930c9296b38e
Create Date: 2025-04-12 10:15:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'a64e920df7fa'
down_revision = '930c9296b38e'
branch_labels = None
depends_on = None

def upgrade():
    # Create enum types first
    certification_type_enum = sa.Enum('SECURITY', 'CLOUD', 'NETWORKING', 'DEVELOPMENT', 'DEVOPS', 'OTHER', name='certificationtypeenum')
    certification_level_enum = sa.Enum('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT', name='certificationlevelenum')
    security_domain_enum = sa.Enum(
        'SECURITY_ENGINEERING', 'DEFENSIVE_SECURITY', 'SECURITY_MANAGEMENT', 
        'NETWORK_SECURITY', 'IDENTITY_ACCESS_MANAGEMENT', 'ASSET_SECURITY', 
        'SECURITY_TESTING', 'OFFENSIVE_SECURITY', 
        name='securitydomainenum'
    )
    
    certification_type_enum.create(op.get_bind(), checkfirst=True)
    certification_level_enum.create(op.get_bind(), checkfirst=True)
    security_domain_enum.create(op.get_bind(), checkfirst=True)
    
    # Create certification_explorer table
    op.create_table(
        'certification_explorer',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('provider', sa.String(length=100), nullable=False),
        sa.Column('description', sa.String(length=1000), nullable=True),
        sa.Column('type', certification_type_enum, nullable=False),
        sa.Column('level', certification_level_enum, nullable=False),
        sa.Column('domain', security_domain_enum, nullable=True),
        sa.Column('exam_code', sa.String(length=50), nullable=True),
        sa.Column('price', sa.Float(), nullable=True),
        sa.Column('duration_minutes', sa.Integer(), nullable=True),
        sa.Column('passing_score', sa.Integer(), nullable=True),
        sa.Column('prerequisites_list', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('skills_covered', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('url', sa.String(length=255), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create certification prerequisites table
    op.create_table(
        'certification_explorer_prerequisites',
        sa.Column('certification_id', sa.Integer(), nullable=False),
        sa.Column('prerequisite_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['certification_id'], ['certification_explorer.id'], ),
        sa.ForeignKeyConstraint(['prerequisite_id'], ['certification_explorer.id'], ),
        sa.PrimaryKeyConstraint('certification_id', 'prerequisite_id')
    )
    
    # Create index on name
    op.create_index(op.f('ix_certification_explorer_id'), 'certification_explorer', ['id'], unique=False)
    op.create_index(op.f('ix_certification_explorer_name'), 'certification_explorer', ['name'], unique=False)
    op.create_index(op.f('ix_certification_explorer_provider'), 'certification_explorer', ['provider'], unique=False)


def downgrade():
    # Drop tables
    op.drop_index(op.f('ix_certification_explorer_provider'), table_name='certification_explorer')
    op.drop_index(op.f('ix_certification_explorer_name'), table_name='certification_explorer')
    op.drop_index(op.f('ix_certification_explorer_id'), table_name='certification_explorer')
    op.drop_table('certification_explorer_prerequisites')
    op.drop_table('certification_explorer')
    
    # Drop enums
    sa.Enum(name='certificationtypeenum').drop(op.get_bind(), checkfirst=True)
    sa.Enum(name='certificationlevelenum').drop(op.get_bind(), checkfirst=True)
    sa.Enum(name='securitydomainenum').drop(op.get_bind(), checkfirst=True) 