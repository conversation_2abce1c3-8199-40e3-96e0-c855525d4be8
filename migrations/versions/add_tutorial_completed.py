"""Add tutorial_completed to user_experiences

Revision ID: add_tutorial_completed
Create Date: 2025-02-27 12:04:00.000000
"""
from alembic import op
import sqlalchemy as sa

# Revision identifiers
revision = 'add_tutorial_completed_001'  # unique identifier
down_revision = 'add_job_roles_001'  # depends on the job roles migration
branch_labels = None
depends_on = None

def upgrade():
    # Add tutorial_completed column with default value False
    op.add_column(
        'user_experiences',
        sa.Column('tutorial_completed', sa.<PERSON>(), nullable=False, server_default='false')
    )

def downgrade():
    # Remove tutorial_completed column
    op.drop_column('user_experiences', 'tutorial_completed')
