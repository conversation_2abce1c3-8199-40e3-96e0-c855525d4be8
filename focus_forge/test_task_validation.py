#!/usr/bin/env python3
"""
Test script for task validation functionality.
"""
import sys
import json
import os
from pathlib import Path
import argparse
import unittest
from typing import Dict, List, Any

# Add parent directory to path to import focus_forge
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from focus_forge.task_validation import TaskValidator, validate_tasks_file, TaskValidationError

# Sample valid task for testing
SAMPLE_VALID_TASK = {
    "id": "T1",
    "title": "Implement Authentication Service",
    "description": "Create user authentication system for the application",
    "status": "pending",
    "priority": "high",
    "dependencies": [],
    "details": "Implement user registration, login, and password reset functionality.",
    "test_strategy": "Test user registration, login flows, and security aspects.",
    "subtasks": [
        {
            "id": "T1.1",
            "title": "Design Authentication API",
            "description": "Create API endpoints and data models for authentication",
            "status": "pending"
        },
        {
            "id": "T1.2",
            "title": "Implement User Storage",
            "description": "Create storage mechanisms for user credentials",
            "status": "pending"
        }
    ]
}

# Sample invalid task for testing
SAMPLE_INVALID_TASK = {
    "id": "T2",
    "title": "UI",  # Too short
    "description": "UI",  # Too short
    "status": "started",  # Invalid status
    "priority": "critical",  # Invalid priority
    "dependencies": ["T99"],  # Non-existent dependency
    "details": "Implement user interface components.",
    "subtasks": [
        {
            "id": "T2.1",
            "title": "Design UI Components",
            "description": "Create designs for UI components",
            # Missing status field
        }
    ]
}

# Sample tasks with circular dependency
SAMPLE_CIRCULAR_TASKS = [
    {
        "id": "T1",
        "title": "Implement Feature A",
        "description": "Create feature A functionality",
        "status": "pending",
        "priority": "high",
        "dependencies": ["T2"],
        "details": "Implement feature A"
    },
    {
        "id": "T2",
        "title": "Implement Feature B",
        "description": "Create feature B functionality",
        "status": "pending",
        "priority": "medium",
        "dependencies": ["T3"],
        "details": "Implement feature B"
    },
    {
        "id": "T3",
        "title": "Implement Feature C",
        "description": "Create feature C functionality",
        "status": "pending",
        "priority": "low",
        "dependencies": ["T1"],
        "details": "Implement feature C"
    }
]

# Sample tasks with long dependency chain
SAMPLE_CHAIN_TASKS = [
    {
        "id": "T1",
        "title": "Implement Feature A",
        "description": "Create feature A functionality",
        "status": "pending",
        "priority": "high",
        "dependencies": [],
        "details": "Implement feature A"
    },
    {
        "id": "T2",
        "title": "Implement Feature B",
        "description": "Create feature B functionality",
        "status": "pending",
        "priority": "medium",
        "dependencies": ["T1"],
        "details": "Implement feature B"
    },
    {
        "id": "T3",
        "title": "Implement Feature C",
        "description": "Create feature C functionality",
        "status": "pending",
        "priority": "low",
        "dependencies": ["T2"],
        "details": "Implement feature C"
    },
    {
        "id": "T4",
        "title": "Implement Feature D",
        "description": "Create feature D functionality",
        "status": "pending",
        "priority": "low",
        "dependencies": ["T3"],
        "details": "Implement feature D"
    },
    {
        "id": "T5",
        "title": "Implement Feature E",
        "description": "Create feature E functionality",
        "status": "pending",
        "priority": "low",
        "dependencies": ["T4"],
        "details": "Implement feature E"
    }
]

# Sample tasks with a bottleneck
SAMPLE_BOTTLENECK_TASKS = [
    {
        "id": "T1",
        "title": "Core System Design",
        "description": "Design the core system architecture",
        "status": "pending",
        "priority": "high",
        "dependencies": [],
        "details": "Create architectural diagrams and API specifications"
    },
    {
        "id": "T2",
        "title": "User Interface",
        "description": "Implement user interface components",
        "status": "pending",
        "priority": "medium",
        "dependencies": ["T1"],
        "details": "Create UI components based on the core design"
    },
    {
        "id": "T3",
        "title": "Backend Services",
        "description": "Implement backend services",
        "status": "pending",
        "priority": "high",
        "dependencies": ["T1"],
        "details": "Create backend services based on the core design"
    },
    {
        "id": "T4",
        "title": "Database Layer",
        "description": "Implement database layer",
        "status": "pending",
        "priority": "medium",
        "dependencies": ["T1"],
        "details": "Create database schema and access layer based on core design"
    },
    {
        "id": "T5",
        "title": "API Integration",
        "description": "Implement API integration",
        "status": "pending",
        "priority": "low",
        "dependencies": ["T1"],
        "details": "Create API integration based on core design"
    }
]

# Sample PRD for coverage testing
SAMPLE_PRD = """
# Authentication System

The system must provide secure user authentication.
Users should be able to register and log in.

## Requirements

* Password reset functionality
* Multi-factor authentication
* Rate limiting for failed attempts
"""

# More comprehensive sample PRD
COMPREHENSIVE_PRD = """
# Task Management System

## Overview
The Task Management System will allow users to create, organize, and track tasks efficiently.

## Functional Requirements

### User Authentication
1. Users must be able to register with email and password
2. Users must be able to log in securely
3. Users must be able to reset passwords
4. System should support multi-factor authentication

### Task Management
1. Users must be able to create new tasks
2. Tasks must have title, description, due date, and priority
3. Users should be able to assign tasks to other users
4. Users must be able to mark tasks as complete
5. Users should be able to set recurring tasks

### Organization
1. Users must be able to categorize tasks
2. Users should be able to create task dependencies
3. System must support task filtering and sorting
4. Users should be able to search for tasks

### Notifications
1. System must send reminders for upcoming tasks
2. Users should receive notifications when tasks are assigned to them
3. Users should receive notifications when dependent tasks are completed

## Non-functional Requirements

### Performance
1. Task operations must complete within 1 second
2. System must support at least 1000 concurrent users

### Security
1. All communications must be encrypted
2. Passwords must be securely hashed
3. Rate limiting must be applied to prevent brute force attacks

### Usability
1. Interface must be responsive and work on mobile devices
2. System should be accessible to users with disabilities
"""


class TestTaskValidator(unittest.TestCase):
    """Unit tests for TaskValidator class."""
    
    def setUp(self):
        """Set up test environment."""
        self.validator = TaskValidator()
    
    def test_validate_valid_task(self):
        """Test validation of a valid task."""
        errors = self.validator.validate_task(SAMPLE_VALID_TASK)
        self.assertEqual(len(errors), 0, "Valid task should have no validation errors")
    
    def test_validate_invalid_task(self):
        """Test validation of an invalid task."""
        errors = self.validator.validate_task(SAMPLE_INVALID_TASK)
        self.assertGreater(len(errors), 0, "Invalid task should have validation errors")
        
        # Check for specific expected errors
        error_texts = " ".join(errors)
        self.assertIn("title", error_texts.lower(), "Should detect short title")
        self.assertIn("description", error_texts.lower(), "Should detect short description")
        self.assertIn("status", error_texts.lower(), "Should detect invalid status")
        self.assertIn("priority", error_texts.lower(), "Should detect invalid priority")
    
    def test_validate_minimal_task(self):
        """Test validation of a minimal valid task with only required fields."""
        minimal_task = {
            "id": "T-MIN",
            "title": "Minimal Valid Task",
            "description": "This is a minimal valid task",
            "status": "pending",
            "priority": "medium",
            "dependencies": [],
            "details": "Minimal valid task details"
        }
        errors = self.validator.validate_task(minimal_task)
        self.assertEqual(len(errors), 0, "Minimal valid task should have no validation errors")
    
    def test_validate_task_with_missing_fields(self):
        """Test validation of a task with missing required fields."""
        missing_fields_task = {
            "id": "T-MISS",
            "title": "Missing Fields Task",
            # Missing description
            "status": "pending",
            # Missing priority
            "dependencies": []
            # Missing details
        }
        errors = self.validator.validate_task(missing_fields_task)
        self.assertGreater(len(errors), 0, "Task with missing fields should have validation errors")
        self.assertEqual(len(errors), 3, "Should detect exactly 3 missing fields")
    
    def test_dependency_validation_circular(self):
        """Test validation of tasks with circular dependencies."""
        is_consistent, errors = self.validator.check_dependency_consistency(SAMPLE_CIRCULAR_TASKS)
        self.assertFalse(is_consistent, "Circular dependencies should be detected")
        self.assertGreater(len(errors), 0, "Circular dependencies should generate errors")
        self.assertTrue(any("circular" in error.lower() for error in errors), 
                       "Error message should mention circular dependency")
    
    def test_dependency_validation_nonexistent(self):
        """Test validation of tasks with non-existent dependencies."""
        tasks = [
            {
                "id": "T1",
                "title": "Task with non-existent dependency",
                "description": "This task depends on a non-existent task",
                "status": "pending",
                "priority": "high",
                "dependencies": ["NON-EXISTENT"],
                "details": "Task details"
            }
        ]
        is_consistent, errors = self.validator.check_dependency_consistency(tasks)
        self.assertFalse(is_consistent, "Non-existent dependencies should be detected")
        self.assertGreater(len(errors), 0, "Non-existent dependencies should generate errors")
    
    def test_dependency_validation_self(self):
        """Test validation of task that depends on itself."""
        tasks = [
            {
                "id": "T1",
                "title": "Self-dependent Task",
                "description": "This task depends on itself",
                "status": "pending",
                "priority": "high",
                "dependencies": ["T1"],
                "details": "Task details"
            }
        ]
        is_consistent, errors = self.validator.check_dependency_consistency(tasks)
        self.assertFalse(is_consistent, "Self dependencies should be detected")
        self.assertGreater(len(errors), 0, "Self dependencies should generate errors")
        self.assertTrue(any("itself" in error.lower() for error in errors),
                       "Error message should mention 'itself'")
    
    def test_dependency_structure_chain(self):
        """Test dependency structure analysis for long chains."""
        structure_score, issues = self.validator.check_dependency_structure(SAMPLE_CHAIN_TASKS)
        self.assertLess(structure_score, 1.0, "Long chains should reduce structure score")
        chain_issues = [issue for issue in issues if "chain" in issue.get("type", "").lower()]
        self.assertGreater(len(chain_issues), 0, "Should detect long dependency chains")
    
    def test_dependency_structure_bottleneck(self):
        """Test dependency structure analysis for bottlenecks."""
        structure_score, issues = self.validator.check_dependency_structure(SAMPLE_BOTTLENECK_TASKS)
        self.assertLess(structure_score, 1.0, "Bottlenecks should reduce structure score")
        bottleneck_issues = [issue for issue in issues if "bottleneck" in issue.get("type", "").lower()]
        self.assertGreater(len(bottleneck_issues), 0, "Should detect dependency bottlenecks")
    
    def test_prd_coverage_basic(self):
        """Test basic PRD coverage validation."""
        coverage_score, uncovered_items = self.validator.check_prd_coverage([SAMPLE_VALID_TASK], SAMPLE_PRD)
        self.assertGreaterEqual(coverage_score, 0.0, "Coverage score should be in range [0, 1]")
        self.assertLessEqual(coverage_score, 1.0, "Coverage score should be in range [0, 1]")
        
        # There should be uncovered items for multi-factor authentication and rate limiting
        self.assertGreater(len(uncovered_items), 0, "Should detect uncovered PRD items")
        
        # Check that uncovered items have the expected structure
        for item in uncovered_items:
            self.assertIn("phrase", item, "Uncovered items should have 'phrase' field")
            self.assertIn("severity", item, "Uncovered items should have 'severity' field")
            self.assertIn("semantic_area", item, "Uncovered items should have 'semantic_area' field")
    
    def test_comprehensive_validation(self):
        """Test comprehensive validation report."""
        # Create a mixed set of tasks
        mixed_tasks = [
            SAMPLE_VALID_TASK,
            SAMPLE_INVALID_TASK,
            {
                "id": "T3",
                "title": "Implement Logging System",
                "description": "Create a system-wide logging mechanism",
                "status": "pending",
                "priority": "medium",
                "dependencies": [],
                "details": "Implement logging functionality",
                "test_strategy": "Test logging levels and output"
            }
        ]
        
        report = self.validator.validate_and_report(mixed_tasks, SAMPLE_PRD)
        
        # Verify report structure and contents
        self.assertIn("validation_time", report, "Report should include validation time")
        self.assertIn("num_tasks", report, "Report should include number of tasks")
        self.assertEqual(report["num_tasks"], 3, "Report should show 3 tasks analyzed")
        
        self.assertIn("schema_compliance", report, "Report should include schema compliance")
        self.assertFalse(report["schema_compliance"]["is_compliant"], 
                        "Schema compliance should be false due to invalid task")
        
        self.assertIn("dependency_validation", report, "Report should include dependency validation")
        self.assertIn("task_structure", report, "Report should include task structure analysis")
        self.assertIn("prd_coverage", report, "Report should include PRD coverage")
        
        # Check recommendations
        self.assertIn("recommendations", report, "Report should include recommendations")
        self.assertIsInstance(report["recommendations"], list, "Recommendations should be a list")
    
    def test_file_handling(self):
        """Test validation from file."""
        # Create a temporary tasks file
        temp_tasks_file = "temp_tasks_for_test.json"
        temp_prd_file = "temp_prd_for_test.md"
        
        try:
            # Write tasks to file
            with open(temp_tasks_file, 'w') as f:
                json.dump({"tasks": [SAMPLE_VALID_TASK, SAMPLE_INVALID_TASK]}, f)
            
            # Write PRD to file
            with open(temp_prd_file, 'w') as f:
                f.write(SAMPLE_PRD)
            
            # Test validation from file
            report = validate_tasks_file(temp_tasks_file, temp_prd_file)
            
            # Verify report structure
            self.assertIn("validation_time", report, "Report should include validation time")
            self.assertEqual(report["num_tasks"], 2, "Report should show 2 tasks analyzed")
            self.assertFalse(report["schema_compliance"]["is_compliant"], 
                            "Schema compliance should be false due to invalid task")
        
        finally:
            # Clean up temporary files
            for file in [temp_tasks_file, temp_prd_file]:
                if os.path.exists(file):
                    os.remove(file)


def print_test_results(test_results):
    """Print test results in a formatted way."""
    print("\n" + "=" * 70)
    print(f"Task Validation Test Results")
    print("=" * 70)
    
    tests_run = test_results.testsRun
    failures = len(test_results.failures)
    errors = len(test_results.errors)
    skipped = len(test_results.skipped)
    
    print(f"Tests Run: {tests_run}")
    print(f"Successes: {tests_run - failures - errors - skipped}")
    print(f"Failures: {failures}")
    print(f"Errors: {errors}")
    print(f"Skipped: {skipped}")
    
    if failures > 0 or errors > 0:
        print("\nDetails:")
        if failures > 0:
            print("\nFailures:")
            for i, (test, error) in enumerate(test_results.failures, 1):
                print(f"\n{i}. {test}")
                print("-" * 50)
                print(error)
        
        if errors > 0:
            print("\nErrors:")
            for i, (test, error) in enumerate(test_results.errors, 1):
                print(f"\n{i}. {test}")
                print("-" * 50)
                print(error)
    
    print("\n" + "=" * 70)


def run_original_tests():
    """Run the original tests that use print statements for output."""
    print("\nRunning Original Tests")
    print("=" * 70)
    test_schema_validation()
    test_dependency_validation()
    test_prd_coverage()
    test_comprehensive_validation()


def test_schema_validation():
    """Test schema validation functionality."""
    print("Testing Schema Validation")
    print("=" * 50)
    
    validator = TaskValidator()
    
    # Test valid task
    print("\nValidating sample valid task:")
    errors = validator.validate_task(SAMPLE_VALID_TASK)
    if errors:
        print(f"  Unexpected errors: {errors}")
    else:
        print("  Valid task passed validation successfully ✅")
    
    # Test invalid task
    print("\nValidating sample invalid task:")
    errors = validator.validate_task(SAMPLE_INVALID_TASK)
    print(f"  Found {len(errors)} errors:")
    for error in errors:
        print(f"  - {error}")
    
    print("\nSchema validation test complete.\n")


def test_dependency_validation():
    """Test dependency validation functionality."""
    print("Testing Dependency Validation")
    print("=" * 50)
    
    validator = TaskValidator()
    
    # Test circular dependencies
    print("\nChecking for circular dependencies:")
    is_consistent, errors = validator.check_dependency_consistency(SAMPLE_CIRCULAR_TASKS)
    
    if is_consistent:
        print("  Unexpected result: No circular dependencies detected ❌")
    else:
        print(f"  Found {len(errors)} dependency issues:")
        for error in errors:
            print(f"  - {error}")
    
    print("\nDependency validation test complete.\n")


def test_prd_coverage():
    """Test PRD coverage validation functionality."""
    print("Testing PRD Coverage Validation")
    print("=" * 50)
    
    validator = TaskValidator()
    
    # Test coverage against sample PRD
    print("\nChecking PRD coverage with sample tasks:")
    coverage_score, uncovered_items = validator.check_prd_coverage([SAMPLE_VALID_TASK], SAMPLE_PRD)
    
    print(f"  Coverage score: {coverage_score:.2f} (0.0 - 1.0 scale)")
    print(f"  Uncovered items: {len(uncovered_items)}")
    
    # Group by severity
    high_severity = [item for item in uncovered_items if item.get("severity") == "high"]
    medium_severity = [item for item in uncovered_items if item.get("severity") == "medium"]
    
    print(f"  High severity uncovered items: {len(high_severity)}")
    print(f"  Medium severity uncovered items: {len(medium_severity)}")
    
    # Group by semantic area
    by_area = {}
    for item in uncovered_items:
        area = item.get("semantic_area", "unknown")
        if area not in by_area:
            by_area[area] = []
        by_area[area].append(item)
    
    print("\n  Coverage by semantic area:")
    for area, items in by_area.items():
        print(f"    {area}: {len(items)} uncovered items")
    
    if uncovered_items:
        print("\n  Sample of uncovered items:")
        for item in uncovered_items[:3]:
            severity = item.get("severity", "unknown")
            area = item.get("semantic_area", "unknown")
            print(f"    - [{severity}] {item['phrase']} (area: {area})")
            if "possible_related_tasks" in item and item["possible_related_tasks"]:
                print(f"      Possible related tasks: {', '.join(item['possible_related_tasks'])}")
    
    print("\nPRD coverage test complete.\n")


def test_comprehensive_validation():
    """Test comprehensive validation report."""
    print("Testing Comprehensive Validation")
    print("=" * 50)
    
    validator = TaskValidator()
    
    # Create a mixed set of tasks
    mixed_tasks = [
        SAMPLE_VALID_TASK,
        SAMPLE_INVALID_TASK,
        # Add another valid task
        {
            "id": "T3",
            "title": "Implement Logging System",
            "description": "Create a system-wide logging mechanism",
            "status": "pending",
            "priority": "medium",
            "dependencies": [],
            "details": "Implement logging functionality",
            "test_strategy": "Test logging levels and output"
        }
    ]
    
    # Test comprehensive validation
    print("\nGenerating comprehensive validation report:")
    report = validator.validate_and_report(mixed_tasks, SAMPLE_PRD)
    
    # Print summary
    print(f"  Validation time: {report['validation_time']}")
    print(f"  Tasks analyzed: {report['num_tasks']}")
    print(f"  Subtasks analyzed: {report['num_subtasks']}")
    print(f"  Schema compliance: {'✅' if report['schema_compliance']['is_compliant'] else '❌'}")
    print(f"  Schema errors: {report['schema_compliance']['num_errors']}")
    print(f"  Dependency consistency: {'✅' if report['dependency_validation']['is_consistent'] else '❌'}")
    print(f"  Dependency errors: {report['dependency_validation']['num_errors']}")
    
    # Task quality metrics
    if "average_score" in report.get("task_quality", {}):
        print(f"  Task quality score: {report['task_quality']['average_score']:.2f}")
        if "poor_quality_tasks" in report["task_quality"]:
            print(f"  Poor quality tasks: {len(report['task_quality']['poor_quality_tasks'])}")
    
    # Orphaned tasks
    if "orphaned_tasks" in report and report["orphaned_tasks"]:
        print(f"  Orphaned tasks: {len(report['orphaned_tasks'])}")
        for task in report["orphaned_tasks"]:
            print(f"    - {task['id']}: {task['title']}")
    
    # PRD coverage
    if "coverage_score" in report.get("prd_coverage", {}):
        print(f"  PRD coverage score: {report['prd_coverage']['coverage_score']:.2f}")
        print(f"  Uncovered items: {report['prd_coverage']['num_uncovered_items']}")
    
    print("\nComprehensive validation test complete.\n")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run task validation tests")
    parser.add_argument("--mode", choices=["unit", "original", "all"], default="all",
                      help="Test mode: unit (unittest), original (print-based), or all (both)")
    args = parser.parse_args()
    
    if args.mode in ["unit", "all"]:
        print("\nRunning Unit Tests")
        print("=" * 70)
        suite = unittest.TestLoader().loadTestsFromTestCase(TestTaskValidator)
        result = unittest.TextTestRunner(verbosity=2).run(suite)
        print_test_results(result)
    
    if args.mode in ["original", "all"]:
        run_original_tests()
    
    print("\nTask Validation Testing Complete") 