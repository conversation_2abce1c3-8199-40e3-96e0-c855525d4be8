#!/usr/bin/env python3
"""
Test script for PRD parser integration with the task management system.
This script demonstrates the usage of the integration functionality.
"""
import os
import sys
import json
from pathlib import Path

# Add the current directory to the path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Try to import the integration modules
try:
    from focus_forge.task_management_integration import (
        TaskManagementAdapter,
        parse_prd_to_tasks,
        integrate_prd_with_existing,
        validate_tasks_with_prd
    )
    print("✅ Successfully imported integration modules")
except ImportError as e:
    print(f"❌ Error importing integration modules: {e}")
    print("Please make sure the focus_forge module is available.")
    sys.exit(1)

# Sample PRD text for testing
SAMPLE_PRD = """# Sample PRD for Testing

## Overview
This is a test PRD for the integration system.

## Features
1. Feature One: This is the first feature
2. Feature Two: This depends on Feature One
3. Feature Three: This is independent

## Technical Requirements
- Must be implemented in Python
- Should have error handling
- Database integration is required
"""

# Create a temporary PRD file
def create_test_prd(content=SAMPLE_PRD):
    """Create a temporary PRD file for testing."""
    prd_file = "test_prd.md"
    with open(prd_file, "w") as f:
        f.write(content)
    return prd_file

# Create a temporary tasks file
def create_test_tasks():
    """Create a temporary tasks file for testing."""
    tasks_file = "test_tasks.json"
    tasks = [
        {
            "id": "T1",
            "title": "Implement Feature One",
            "description": "Create the first feature mentioned in the PRD",
            "status": "pending",
            "priority": "high",
            "dependencies": [],
            "details": "Feature One implementation details",
            "test_strategy": "Test Feature One thoroughly",
            "subtasks": []
        },
        {
            "id": "T2",
            "title": "Implement Feature Two",
            "description": "Create the second feature that depends on Feature One",
            "status": "pending",
            "priority": "medium",
            "dependencies": ["T1"],
            "details": "Feature Two implementation details",
            "test_strategy": "Test Feature Two with Feature One",
            "subtasks": []
        }
    ]
    with open(tasks_file, "w") as f:
        json.dump({"tasks": tasks}, f, indent=2)
    return tasks_file

# Test functions
def test_parse_prd():
    """Test parsing a PRD file."""
    print("\n--- Testing parse_prd_to_tasks ---")
    prd_file = create_test_prd()
    try:
        tasks = parse_prd_to_tasks(prd_file)
        print(f"✅ Generated {len(tasks)} tasks from PRD")
        for i, task in enumerate(tasks, 1):
            print(f"  Task {i}: {task['id']} - {task['title']}")
        return tasks
    except Exception as e:
        print(f"❌ Error parsing PRD: {e}")
        return None

def test_integrate():
    """Test integrating a PRD with existing tasks."""
    print("\n--- Testing integrate_prd_with_existing ---")
    prd_file = create_test_prd()
    tasks_file = create_test_tasks()
    output_file = "integrated_tasks.json"
    
    try:
        report = integrate_prd_with_existing(prd_file, tasks_file, output_file)
        if report["status"] == "success":
            print(f"✅ Successfully integrated PRD with existing tasks")
            print(f"  New tasks: {report.get('new_tasks', 0)}")
            print(f"  Existing tasks: {report.get('existing_tasks', 0)}")
            print(f"  Merged tasks: {report.get('merged_tasks', 0)}")
            print(f"  Output saved to: {output_file}")
        else:
            print(f"❌ Integration failed: {report.get('error_message', 'Unknown error')}")
        return report
    except Exception as e:
        print(f"❌ Error during integration: {e}")
        return None

def test_validate():
    """Test validating tasks against a PRD."""
    print("\n--- Testing validate_tasks_with_prd ---")
    prd_file = create_test_prd()
    tasks_file = create_test_tasks()
    
    try:
        report = validate_tasks_with_prd(tasks_file, prd_file)
        if report.get("tasks_valid", False):
            print(f"✅ Tasks are valid!")
            print(f"  Confidence score: {report.get('confidence_score', 'N/A')}")
            print(f"  Clarity score: {report.get('clarity_score', 'N/A')}")
            print(f"  Coverage score: {report.get('coverage_score', 'N/A')}")
        else:
            print(f"❌ Tasks validation failed")
            print(f"  Reason: {report.get('error_message', 'Unknown reason')}")
        return report
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        return None

def test_adapter():
    """Test the TaskManagementAdapter class."""
    print("\n--- Testing TaskManagementAdapter ---")
    prd_file = create_test_prd()
    adapter = TaskManagementAdapter()
    
    try:
        tasks, validation = adapter.parse_prd_file(prd_file)
        print(f"✅ Adapter successfully parsed PRD and generated {len(tasks)} tasks")
        
        system_format = adapter.convert_to_system_format(tasks)
        print(f"✅ Adapter successfully converted tasks to system format")
        
        return True
    except Exception as e:
        print(f"❌ Error in adapter test: {e}")
        return False

def cleanup():
    """Clean up temporary files."""
    for file in ["test_prd.md", "test_tasks.json", "integrated_tasks.json"]:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"Cleaned up {file}")
            except Exception:
                pass

def main():
    """Run all tests."""
    print("\n=== PRD Parser Integration Test ===\n")
    
    # Run tests
    test_parse_prd()
    test_integrate()
    test_validate()
    test_adapter()
    
    # Clean up
    print("\n--- Cleaning up ---")
    cleanup()
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    main()
