#!/usr/bin/env python3

"""
Verify that focus_forge can find tasks directory at the project root.
"""

import os
import sys
from pathlib import Path
import importlib.util

# Check if focus_forge.py exists
focus_forge_path = Path('./focus_forge.py')
if not focus_forge_path.exists():
    print("❌ focus_forge.py not found")
    sys.exit(1)

# Check if tasks directory exists at the root level
tasks_dir = Path('./tasks')
if not tasks_dir.exists():
    print("❌ tasks directory not found at project root")
    sys.exit(1)

print(f"✅ Found tasks directory at: {tasks_dir.absolute()}")

# Check for task files
task_files = list(tasks_dir.glob("task_*.txt"))
print(f"✅ Found {len(task_files)} task files in tasks directory")

# Check if there's a tasks directory inside focus_forge subdirectory
focus_forge_tasks_dir = Path('./focus_forge/tasks')
if focus_forge_tasks_dir.exists():
    print(f"⚠️ Warning: There's also a tasks directory inside focus_forge: {focus_forge_tasks_dir.absolute()}")
    ff_task_files = list(focus_forge_tasks_dir.glob("task_*.txt"))
    print(f"⚠️ Found {len(ff_task_files)} task files in focus_forge/tasks directory")
else:
    print("✅ No tasks directory inside focus_forge (good!)")

# Get TASKS_DIR value directly from focus_forge.py
with open('focus_forge.py', 'r') as f:
    content = f.read()
    if 'TASKS_DIR = "tasks"' in content:
        print("✅ TASKS_DIR is correctly set to 'tasks' in focus_forge.py")
    else:
        print("❌ Could not confirm TASKS_DIR value in focus_forge.py")

# Check constants.py in focus_forge directory
constants_path = Path('./focus_forge/constants.py')
if constants_path.exists():
    with open(constants_path, 'r') as f:
        content = f.read()
        if 'TASKS_DIR = "tasks"' in content:
            print("✅ TASKS_DIR is correctly set to 'tasks' in focus_forge/constants.py")
        else:
            print("❌ Could not confirm TASKS_DIR value in focus_forge/constants.py")

print("\nConclusion:")
print("✅ The current setup is correct for projects using focus_forge as a submodule.")
print("✅ The tasks directory is at the project root, not inside the focus_forge submodule.")
print("✅ This provides proper separation between the framework and project-specific tasks.") 