#!/usr/bin/env python3
"""
Test script to verify that focus_forge can correctly find the tasks directory
when used as a submodule in another project.
"""

import os
import sys
from pathlib import Path

# Add the current directory to sys.path to import focus_forge
sys.path.insert(0, os.path.abspath('.'))

# Import constants and path utilities
from focus_forge.constants import TASKS_DIR, get_tasks_dir
from focus_forge.focus_forge import get_tasks_dir_path

def simulate_submodule_scenario():
    """
    Test that focus_forge correctly handles the tasks directory location
    when used as a submodule.
    
    This simulates:
    /project_root/
       |-- focus_forge/  (submodule)
       |-- tasks/        (should be here)
    """
    print("\n=== Testing tasks directory resolution for submodule usage ===\n")
    
    # Current directory structure
    project_root = Path.cwd()
    focus_forge_dir = project_root / 'focus_forge'
    tasks_dir = project_root / 'tasks'
    
    print(f"Project root:     {project_root}")
    print(f"focus_forge dir:  {focus_forge_dir}")
    print(f"tasks dir:        {tasks_dir}")
    
    # Test constant and function results
    print(f"\nTASKS_DIR value:  {TASKS_DIR}")
    print(f"get_tasks_dir():  {get_tasks_dir()}")
    
    # Test path resolution
    tasks_path = get_tasks_dir_path()
    print(f"get_tasks_dir_path() result: {tasks_path}")
    
    # Validation
    if tasks_path == tasks_dir:
        print("\n✅ SUCCESS: tasks directory is correctly resolved to the project root")
    else:
        print(f"\n❌ ERROR: tasks directory resolved to {tasks_path}, expected {tasks_dir}")
    
    # Check if tasks directory exists
    if tasks_dir.exists():
        print(f"✅ Tasks directory exists at: {tasks_dir}")
        task_files = list(tasks_dir.glob("task_*.txt"))
        print(f"📁 Found {len(task_files)} task files in tasks directory")
    else:
        print(f"❌ Tasks directory does not exist at: {tasks_dir}")
    
    print("\n=== Test complete ===\n")

if __name__ == "__main__":
    simulate_submodule_scenario() 