{"tasks": [{"id": "T1", "title": "Implement Feature One", "description": "Create the first feature mentioned in the PRD", "status": "pending", "priority": "high", "dependencies": [], "details": "Feature One implementation details", "test_strategy": "Test Feature One thoroughly", "subtasks": []}, {"id": "T2", "title": "Implement Feature Two", "description": "Create the second feature that depends on Feature One", "status": "pending", "priority": "medium", "dependencies": ["T1"], "details": "Feature Two implementation details", "test_strategy": "Test Feature Two with Feature One", "subtasks": []}]}