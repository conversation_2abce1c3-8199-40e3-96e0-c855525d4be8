#!/usr/bin/env python3

import json
import sys

def update_task_status(task_id, new_status):
    # Load tasks from tasks.json
    try:
        with open('tasks.json', 'r') as f:
            data = json.load(f)
    except Exception as e:
        print(f"Error loading tasks.json: {e}")
        return False
    
    # Find and update the task
    tasks = data.get("tasks", [])
    task_found = False
    
    # Check top-level tasks
    for task in tasks:
        if task["id"] == task_id:
            old_status = task["status"]
            task["status"] = new_status
            task_found = True
            print(f"Updated task {task_id} status: {old_status} -> {new_status}")
            break
        
        # Check subtasks
        if not task_found and "subtasks" in task:
            for subtask in task["subtasks"]:
                if subtask["id"] == task_id:
                    old_status = subtask["status"]
                    subtask["status"] = new_status
                    task_found = True
                    print(f"Updated subtask {task_id} status: {old_status} -> {new_status}")
                    break
            if task_found:
                break
    
    if not task_found:
        print(f"Task {task_id} not found.")
        return False
    
    # Save the updated tasks
    try:
        with open('tasks.json', 'w') as f:
            json.dump(data, f, indent=2)
        return True
    except Exception as e:
        print(f"Error saving tasks.json: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python update_task_status.py <task_id> <new_status>")
        sys.exit(1)
    
    task_id = sys.argv[1]
    new_status = sys.argv[2]
    
    if new_status not in ["pending", "in-progress", "done", "deferred"]:
        print(f"Invalid status: {new_status}")
        print("Valid statuses: pending, in-progress, done, deferred")
        sys.exit(1)
    
    success = update_task_status(task_id, new_status)
    sys.exit(0 if success else 1) 