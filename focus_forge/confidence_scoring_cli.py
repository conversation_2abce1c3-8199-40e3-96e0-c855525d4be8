#!/usr/bin/env python3
"""
Command-line interface for confidence scoring and PRD review.

This module provides a CLI for analyzing PRDs and tasks to generate confidence scores,
identify ambiguities, and suggest improvements.
"""
import os
import sys
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    # First try to import from the local focus_forge directory
    from my_focus_forge.confidence_scoring import ConfidenceScorer, ReviewSystem
except ImportError:
    try:
        # Then try to import from the actual module
        from focus_forge.confidence_scoring import ConfidenceScorer, ReviewSystem
    except ImportError:
        print("Error: Could not import confidence scoring module.")
        print("Make sure you have the confidence_scoring.py file in either the 'my_focus_forge' or 'focus_forge' directory.")
        sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_prd_file(prd_file: str) -> str:
    """
    Load PRD text from a file.
    
    Args:
        prd_file: Path to the PRD file
        
    Returns:
        str: PRD text content
    """
    try:
        with open(prd_file, 'r') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error loading PRD file {prd_file}: {e}")
        raise ValueError(f"Could not load PRD file: {e}")


def load_tasks_file(tasks_file: str) -> List[Dict[str, Any]]:
    """
    Load tasks from a JSON file.
    
    Args:
        tasks_file: Path to the tasks JSON file
        
    Returns:
        List[Dict[str, Any]]: List of tasks
    """
    try:
        with open(tasks_file, 'r') as f:
            data = json.load(f)
            
        # Handle both formats: {"tasks": [...]} and direct list of tasks [...]
        if isinstance(data, dict) and "tasks" in data:
            return data["tasks"]
        elif isinstance(data, list):
            return data
        else:
            raise ValueError(f"Invalid tasks file format: {tasks_file}")
    except Exception as e:
        logger.error(f"Error loading tasks file {tasks_file}: {e}")
        raise ValueError(f"Could not load tasks file: {e}")


def save_report(report: Dict[str, Any], output_file: str) -> None:
    """
    Save report to a JSON file.
    
    Args:
        report: The report data to save
        output_file: Path to save the report to
    """
    try:
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
            
        logger.info(f"Report saved to {output_file}")
    except Exception as e:
        logger.error(f"Error saving report to {output_file}: {e}")
        raise ValueError(f"Could not save report: {e}")


def print_confidence_scores(report: Dict[str, Any]) -> None:
    """
    Print confidence scores in a user-friendly format.
    
    Args:
        report: The confidence report
    """
    print("\n" + "=" * 70)
    print("CONFIDENCE SCORE SUMMARY")
    print("=" * 70)
    
    # Overall confidence
    confidence = report["confidence_score"]
    confidence_str = f"{confidence:.2f}"
    if confidence >= 0.8:
        confidence_level = "HIGH"
    elif confidence >= 0.5:
        confidence_level = "MEDIUM"
    else:
        confidence_level = "LOW"
    
    print(f"Overall Confidence: {confidence_str} ({confidence_level})")
    print(f"Clarity Score:      {report['clarity_score']:.2f}")
    print(f"Coverage Score:     {report['coverage_score']:.2f}")
    print(f"Complexity Score:   {report['complexity_score']:.2f}")
    print("\n" + "-" * 70)
    
    # Issue counts
    ambiguous_count = report["ambiguous_requirements"]["count"]
    ambiguous_high = report["ambiguous_requirements"]["high_severity_count"]
    
    missing_count = report["missing_requirements"]["count"]
    missing_high = report["missing_requirements"]["high_severity_count"]
    
    print(f"Ambiguous Requirements: {ambiguous_count} (High severity: {ambiguous_high})")
    print(f"Missing Requirements:   {missing_count} (High severity: {missing_high})")
    
    print("\n" + "=" * 70)


def print_recommendations(report: Dict[str, Any]) -> None:
    """
    Print recommendations in a user-friendly format.
    
    Args:
        report: The confidence report
    """
    recommendations = report.get("recommendations", [])
    if not recommendations:
        print("No recommendations available.")
        return
    
    print("\n" + "=" * 70)
    print("RECOMMENDATIONS")
    print("=" * 70)
    
    # Sort by priority
    priority_order = {"high": 0, "medium": 1, "low": 2}
    recommendations.sort(key=lambda x: priority_order.get(x.get("priority", "low"), 3))
    
    for i, rec in enumerate(recommendations, 1):
        priority = rec.get("priority", "").upper()
        print(f"{i}. [{priority}] {rec['description']}")
        print(f"   {rec['details']}")
        print()
    
    print("=" * 70)


def print_ambiguous_requirements(report: Dict[str, Any], max_items: int = 5) -> None:
    """
    Print ambiguous requirements in a user-friendly format.
    
    Args:
        report: The confidence report
        max_items: Maximum number of items to print
    """
    ambiguous_requirements = report["ambiguous_requirements"]["items"]
    if not ambiguous_requirements:
        print("No ambiguous requirements detected.")
        return
    
    print("\n" + "=" * 70)
    print("AMBIGUOUS REQUIREMENTS")
    print("=" * 70)
    
    # Sort by severity
    severity_order = {"high": 0, "medium": 1, "low": 2}
    ambiguous_requirements.sort(key=lambda x: (severity_order.get(x.get("severity", "low"), 3), -x.get("ambiguity_score", 0)))
    
    # Limit to max_items
    items_to_show = ambiguous_requirements[:max_items]
    
    for i, req in enumerate(items_to_show, 1):
        severity = req.get("severity", "").upper()
        phrases = ", ".join(req.get("ambiguous_phrases", []))
        
        print(f"{i}. [{severity}] {req['requirement']}")
        if phrases:
            print(f"   Ambiguous phrases: {phrases}")
        if req.get("missing_specifics", False):
            print("   Issue: Requirement is too brief or lacks specifics")
        print()
    
    if len(ambiguous_requirements) > max_items:
        print(f"... and {len(ambiguous_requirements) - max_items} more ambiguous requirements.")
    
    print("=" * 70)


def print_missing_requirements(report: Dict[str, Any], max_items: int = 5) -> None:
    """
    Print missing requirements in a user-friendly format.
    
    Args:
        report: The confidence report
        max_items: Maximum number of items to print
    """
    missing_requirements = report["missing_requirements"]["items"]
    if not missing_requirements:
        print("No missing requirements detected.")
        return
    
    print("\n" + "=" * 70)
    print("POTENTIALLY MISSING REQUIREMENTS")
    print("=" * 70)
    
    # Sort by severity
    severity_order = {"high": 0, "medium": 1, "low": 2}
    missing_requirements.sort(key=lambda x: severity_order.get(x.get("severity", "low"), 3))
    
    # Limit to max_items
    items_to_show = missing_requirements[:max_items]
    
    for i, req in enumerate(items_to_show, 1):
        severity = req.get("severity", "").upper()
        
        print(f"{i}. [{severity}] {req['requirement']}")
        
        related_tasks = req.get("related_tasks", [])
        if related_tasks:
            task_ids = [task["id"] for task in related_tasks]
            print(f"   Possibly related tasks: {', '.join(task_ids)}")
        print()
    
    if len(missing_requirements) > max_items:
        print(f"... and {len(missing_requirements) - max_items} more potentially missing requirements.")
    
    print("=" * 70)


def print_clarification_suggestions(prd_text: str, tasks: List[Dict[str, Any]], max_items: int = 5) -> None:
    """
    Print suggested clarifications for ambiguous requirements.
    
    Args:
        prd_text: The PRD text
        tasks: List of tasks
        max_items: Maximum number of items to print
    """
    review_system = ReviewSystem()
    clarifications = review_system.suggest_clarifications(prd_text)
    
    if not clarifications:
        print("No clarification suggestions available.")
        return
    
    print("\n" + "=" * 70)
    print("CLARIFICATION SUGGESTIONS")
    print("=" * 70)
    
    # Sort by severity
    severity_order = {"high": 0, "medium": 1, "low": 2}
    clarifications.sort(key=lambda x: severity_order.get(x.get("severity", "low"), 3))
    
    # Limit to max_items
    items_to_show = clarifications[:max_items]
    
    for i, suggestion in enumerate(items_to_show, 1):
        severity = suggestion.get("severity", "").upper()
        
        print(f"{i}. [{severity}] Original:")
        print(f"   {suggestion['original']}")
        print("   Suggested:")
        print(f"   {suggestion['suggestion']}")
        print(f"   Rationale: {suggestion['rationale']}")
        print()
    
    if len(clarifications) > max_items:
        print(f"... and {len(clarifications) - max_items} more clarification suggestions.")
    
    print("=" * 70)


def print_task_improvement_suggestions(prd_text: str, tasks: List[Dict[str, Any]], max_items: int = 5) -> None:
    """
    Print suggested improvements for tasks.
    
    Args:
        prd_text: The PRD text
        tasks: List of tasks
        max_items: Maximum number of items to print
    """
    review_system = ReviewSystem()
    improvements = review_system.suggest_task_improvements(prd_text, tasks)
    
    if not improvements:
        print("No task improvement suggestions available.")
        return
    
    print("\n" + "=" * 70)
    print("TASK IMPROVEMENT SUGGESTIONS")
    print("=" * 70)
    
    # Sort by severity and type
    severity_order = {"high": 0, "medium": 1, "low": 2}
    improvements.sort(key=lambda x: (severity_order.get(x.get("severity", "low"), 3), x.get("type", "")))
    
    # Limit to max_items
    items_to_show = improvements[:max_items]
    
    for i, improvement in enumerate(items_to_show, 1):
        severity = improvement.get("severity", "").upper()
        
        if improvement["type"] == "new_task":
            task = improvement["suggested_task"]
            print(f"{i}. [{severity}] New Task: {task['title']}")
            print(f"   Description: {task['description']}")
            if task["dependencies"]:
                print(f"   Dependencies: {', '.join(task['dependencies'])}")
            print(f"   Rationale: {improvement['rationale']}")
        else:
            print(f"{i}. [{severity}] {improvement['suggestion']}")
            print(f"   For task: {improvement['task_id']} - {improvement['task_title']}")
            print(f"   Rationale: {improvement['rationale']}")
        print()
    
    if len(improvements) > max_items:
        print(f"... and {len(improvements) - max_items} more improvement suggestions.")
    
    print("=" * 70)


def generate_report(prd_file: str, tasks_file: str, output_file: Optional[str] = None, verbose: bool = False) -> None:
    """
    Generate a confidence report from PRD and tasks files.
    
    Args:
        prd_file: Path to the PRD file
        tasks_file: Path to the tasks JSON file
        output_file: Optional path to save the report
        verbose: Whether to print detailed information
    """
    try:
        # Load data
        prd_text = load_prd_file(prd_file)
        tasks = load_tasks_file(tasks_file)
        
        # Generate report
        confidence_scorer = ConfidenceScorer()
        report = confidence_scorer.generate_review_report(prd_text, tasks)
        
        # Print summary
        print_confidence_scores(report)
        
        if verbose:
            print_recommendations(report)
            print_ambiguous_requirements(report)
            print_missing_requirements(report)
        
        # Save report if output file specified
        if output_file:
            save_report(report, output_file)
            print(f"\nReport saved to: {output_file}")
    
    except Exception as e:
        logger.error(f"Error generating report: {e}")
        print(f"Error: {e}")
        sys.exit(1)


def review_prd_and_tasks(prd_file: str, tasks_file: str, output_file: Optional[str] = None) -> None:
    """
    Review PRD and tasks with detailed suggestions.
    
    Args:
        prd_file: Path to the PRD file
        tasks_file: Path to the tasks JSON file
        output_file: Optional path to save the report
    """
    try:
        # Load data
        prd_text = load_prd_file(prd_file)
        tasks = load_tasks_file(tasks_file)
        
        # Generate report
        review_system = ReviewSystem()
        interface = review_system.generate_review_interface(prd_text, tasks)
        
        # Print information
        report = interface["report"]
        print_confidence_scores(report)
        print_recommendations(report)
        print_ambiguous_requirements(report)
        print_missing_requirements(report)
        print_clarification_suggestions(prd_text, tasks)
        print_task_improvement_suggestions(prd_text, tasks)
        
        # Save interface if output file specified
        if output_file:
            save_report(interface, output_file)
            print(f"\nComplete review interface saved to: {output_file}")
    
    except Exception as e:
        logger.error(f"Error reviewing PRD and tasks: {e}")
        print(f"Error: {e}")
        sys.exit(1)


def parse_args() -> argparse.Namespace:
    """
    Parse command-line arguments.
    
    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(description="Confidence scoring and PRD review tool")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # Score command
    score_parser = subparsers.add_parser("score", help="Generate confidence scores for PRD and tasks")
    score_parser.add_argument("prd_file", help="Path to PRD file")
    score_parser.add_argument("tasks_file", help="Path to tasks JSON file")
    score_parser.add_argument("-o", "--output", help="Path to save the report (optional)")
    score_parser.add_argument("-v", "--verbose", action="store_true", help="Print detailed information")
    
    # Review command
    review_parser = subparsers.add_parser("review", help="Review PRD and tasks with detailed suggestions")
    review_parser.add_argument("prd_file", help="Path to PRD file")
    review_parser.add_argument("tasks_file", help="Path to tasks JSON file")
    review_parser.add_argument("-o", "--output", help="Path to save the review interface (optional)")
    
    return parser.parse_args()


def main() -> None:
    """Main entry point for the CLI."""
    args = parse_args()
    
    if args.command == "score":
        generate_report(args.prd_file, args.tasks_file, args.output, args.verbose)
    elif args.command == "review":
        review_prd_and_tasks(args.prd_file, args.tasks_file, args.output)
    else:
        print("Please specify a command: score or review")
        print("Run with --help for more information")
        sys.exit(1)


if __name__ == "__main__":
    main()
