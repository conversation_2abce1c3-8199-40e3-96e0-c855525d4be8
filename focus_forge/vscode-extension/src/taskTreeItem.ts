import * as vscode from 'vscode';
import * as path from 'path';
import { Task } from './taskProvider';

export class TaskTreeItem extends vscode.TreeItem {
    constructor(
        public readonly task: Task,
        public readonly relevance: number,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState
    ) {
        super(task.title, collapsibleState);

        // Set the item description (task ID and priority)
        this.description = `${task.id} (${task.priority})`;

        // Set tooltip with full task info
        this.tooltip = this.createTooltip(task);

        // Set the appropriate icon for the task status
        this.iconPath = this.getIconForStatus(task.status);

        // Set the command that gets executed when the item is clicked
        this.command = {
            command: 'focus-forge.setTaskStatus',
            title: 'Update Task Status',
            arguments: [task.id]
        };

        // Set context value for context menu filtering
        this.contextValue = `task-${task.status}`;
    }

    private createTooltip(task: Task): string {
        // Build a rich tooltip with task details
        let tooltip = `ID: ${task.id}\n`;
        tooltip += `Title: ${task.title}\n`;
        tooltip += `Status: ${task.status}\n`;
        tooltip += `Priority: ${task.priority}\n`;
        
        if (task.description) {
            tooltip += `\nDescription: ${task.description}\n`;
        }
        
        if (task.dependencies && task.dependencies.length > 0) {
            tooltip += `\nDependencies: ${task.dependencies.join(', ')}\n`;
        }
        
        if (task.codeReferences && task.codeReferences.length > 0) {
            tooltip += `\nCode References:\n`;
            task.codeReferences.forEach(ref => {
                tooltip += `- ${path.basename(ref.filePath)}`;
                if (ref.position) {
                    tooltip += ` (line ${ref.position.line + 1})`;
                } else if (ref.selection) {
                    tooltip += ` (lines ${ref.selection.startLine + 1}-${ref.selection.endLine + 1})`;
                }
                tooltip += '\n';
            });
        }
        
        return tooltip;
    }

    private getIconForStatus(status: string): { light: string; dark: string } {
        // Path to the resources folder
        const resourcesPath = path.join(__filename, '..', '..', 'resources');
        
        // Map status to icon files
        const iconName = (() => {
            switch (status) {
                case 'in-progress': return 'in-progress';
                case 'done': return 'done';
                case 'deferred': return 'deferred';
                case 'pending':
                default: return 'pending';
            }
        })();
        
        return {
            light: path.join(resourcesPath, 'light', `${iconName}.svg`),
            dark: path.join(resourcesPath, 'dark', `${iconName}.svg`)
        };
    }
} 