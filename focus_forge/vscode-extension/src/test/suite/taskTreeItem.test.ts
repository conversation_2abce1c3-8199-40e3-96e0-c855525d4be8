import * as assert from 'assert';
import * as vscode from 'vscode';
import * as path from 'path';
import { Task } from '../../taskProvider';
import { TaskTreeItem } from '../../taskTreeItem';

suite('TaskTreeItem Test Suite', () => {
    test('TaskTreeItem should correctly format a task', () => {
        const task: Task = {
            id: 'T-123456',
            title: 'Test Task',
            description: 'A test task description',
            status: 'pending',
            priority: 'medium',
            dependencies: ['T-789012'],
            codeReferences: [
                {
                    filePath: '/path/to/file.ts',
                    selection: {
                        startLine: 10,
                        endLine: 20
                    }
                }
            ]
        };
        
        const treeItem = new TaskTreeItem(
            task,
            0.7,
            vscode.TreeItemCollapsibleState.None
        );
        
        // Check label and description
        assert.strictEqual(treeItem.label, 'Test Task');
        assert.strictEqual(treeItem.description, 'T-123456 (medium)');
        
        // Check command
        assert.strictEqual(treeItem.command?.command, 'focus-forge.setTaskStatus');
        assert.strictEqual(treeItem.command?.title, 'Update Task Status');
        assert.deepStrictEqual(treeItem.command?.arguments, ['T-123456']);
        
        // Check context value for filtering
        assert.strictEqual(treeItem.contextValue, 'task-pending');
    });
    
    test('TaskTreeItem should format tooltips correctly', () => {
        const task: Task = {
            id: 'T-123456',
            title: 'Test Task',
            description: 'A test task description',
            status: 'in-progress',
            priority: 'high',
            dependencies: ['T-789012', 'T-345678'],
            codeReferences: [
                {
                    filePath: '/path/to/file.ts',
                    selection: {
                        startLine: 10,
                        endLine: 20
                    }
                },
                {
                    filePath: '/path/to/another.ts',
                    position: {
                        line: 5,
                        character: 10
                    }
                }
            ]
        };
        
        const treeItem = new TaskTreeItem(
            task,
            0.9,
            vscode.TreeItemCollapsibleState.None
        );
        
        // Check that tooltip contains all the required information
        const tooltip = treeItem.tooltip as string;
        
        assert.ok(tooltip.includes('ID: T-123456'));
        assert.ok(tooltip.includes('Title: Test Task'));
        assert.ok(tooltip.includes('Status: in-progress'));
        assert.ok(tooltip.includes('Priority: high'));
        assert.ok(tooltip.includes('Description: A test task description'));
        assert.ok(tooltip.includes('Dependencies: T-789012, T-345678'));
        assert.ok(tooltip.includes('Code References:'));
        assert.ok(tooltip.includes('file.ts (lines 11-21)')); // 1-indexed in display
        assert.ok(tooltip.includes('another.ts (line 6)')); // 1-indexed in display
    });
    
    test('TaskTreeItem should use the correct icons for different statuses', () => {
        // Test each status
        const statuses: Array<'pending' | 'in-progress' | 'done' | 'deferred'> = [
            'pending', 'in-progress', 'done', 'deferred'
        ];
        
        for (const status of statuses) {
            const task: Task = {
                id: `T-${status}`,
                title: `${status} Task`,
                description: '',
                status: status,
                priority: 'medium',
                dependencies: [],
                codeReferences: []
            };
            
            const treeItem = new TaskTreeItem(
                task,
                0.5,
                vscode.TreeItemCollapsibleState.None
            );
            
            // Check that the icon path includes the status name
            const iconPath = treeItem.iconPath as { light: string; dark: string };
            assert.ok(iconPath.light.includes(status));
            assert.ok(iconPath.dark.includes(status));
        }
    });
}); 