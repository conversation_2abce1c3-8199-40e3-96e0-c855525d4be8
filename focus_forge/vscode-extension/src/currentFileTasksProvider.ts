import * as vscode from 'vscode';
import * as path from 'path';
import { Task, TaskProvider } from './taskProvider';
import { TaskTreeItem } from './taskTreeItem';

export class CurrentFileTasksProvider implements vscode.TreeDataProvider<TaskTreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<TaskTreeItem | undefined | null | void> = 
        new vscode.EventEmitter<TaskTreeItem | undefined | null | void>();
    
    readonly onDidChangeTreeData: vscode.Event<TaskTreeItem | undefined | null | void> = 
        this._onDidChangeTreeData.event;

    constructor(private taskProvider: TaskProvider) {
        this.taskProvider.onDidChangeTaskData(() => this.refresh());
    }

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    getTreeItem(element: TaskTreeItem): vscode.TreeItem {
        return element;
    }

    async getChildren(element?: TaskTreeItem): Promise<TaskTreeItem[]> {
        if (element) {
            return []; // No child items for task items
        }

        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return [];
        }

        const currentFilePath = editor.document.uri.fsPath;
        const relevanceThreshold = this.getRelevanceThreshold();
        const tasks = await this.taskProvider.getAllTasks();
        const relevantTasks: {task: Task, relevance: number}[] = [];

        // Calculate relevance for each task and filter by threshold
        for (const task of tasks) {
            const relevance = await this.taskProvider.calculateTaskRelevance(task, currentFilePath);
            if (relevance >= relevanceThreshold) {
                relevantTasks.push({ task, relevance });
            }
        }

        // Sort by relevance (higher first) and then by status (in-progress before pending)
        relevantTasks.sort((a, b) => {
            if (Math.abs(a.relevance - b.relevance) > 0.1) {
                return b.relevance - a.relevance;
            }
            
            const statusOrder = {
                'in-progress': 0,
                'pending': 1,
                'deferred': 2,
                'done': 3
            };
            
            return statusOrder[a.task.status] - statusOrder[b.task.status];
        });

        // Apply the showCompleted filter
        const showCompleted = vscode.workspace.getConfiguration('focus-forge').get<boolean>('showCompletedTasks') || false;
        const filteredTasks = showCompleted 
            ? relevantTasks
            : relevantTasks.filter(item => item.task.status !== 'done');

        return filteredTasks.map(item => {
            return new TaskTreeItem(
                item.task,
                item.relevance,
                vscode.TreeItemCollapsibleState.None
            );
        });
    }

    private getRelevanceThreshold(): number {
        const config = vscode.workspace.getConfiguration('focus-forge');
        return config.get<number>('relevanceThreshold') || 0.3;
    }
} 