import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

export interface Task {
    id: string;
    title: string;
    description: string;
    status: 'pending' | 'in-progress' | 'done' | 'deferred';
    priority: 'low' | 'medium' | 'high' | 'critical';
    dependencies: string[];
    codeReferences: CodeReference[];
}

export interface CodeReference {
    filePath: string;
    position?: {
        line: number;
        character: number;
    };
    selection?: {
        startLine: number;
        endLine: number;
    };
}

export class TaskProvider {
    private tasks: Task[] = [];
    private tasksFilePath: string = '';
    private _onDidChangeTaskData: vscode.EventEmitter<void> = new vscode.EventEmitter<void>();
    public readonly onDidChangeTaskData: vscode.Event<void> = this._onDidChangeTaskData.event;

    constructor(private context: vscode.ExtensionContext) {
        this.loadTasks();
        
        // Watch for task file changes
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders) {
            const watcher = vscode.workspace.createFileSystemWatcher(
                new vscode.RelativePattern(workspaceFolders[0], this.getTasksFilePath())
            );
            
            watcher.onDidChange(() => this.loadTasks());
            watcher.onDidCreate(() => this.loadTasks());
            watcher.onDidDelete(() => this.tasks = []);
            
            this.context.subscriptions.push(watcher);
        }
    }

    private getTasksFilePath(): string {
        const config = vscode.workspace.getConfiguration('focus-forge');
        return config.get<string>('tasksFilePath') || 'tasks.json';
    }

    private async loadTasks(): Promise<void> {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) {
            return;
        }

        this.tasksFilePath = path.join(workspaceFolders[0].uri.fsPath, this.getTasksFilePath());
        
        try {
            if (fs.existsSync(this.tasksFilePath)) {
                const content = await fs.promises.readFile(this.tasksFilePath, 'utf-8');
                this.tasks = JSON.parse(content);
                this._onDidChangeTaskData.fire();
            }
        } catch (error) {
            console.error('Error loading tasks:', error);
            vscode.window.showErrorMessage(`Failed to load tasks: ${error}`);
        }
    }

    private async saveTasks(): Promise<void> {
        try {
            await fs.promises.writeFile(this.tasksFilePath, JSON.stringify(this.tasks, null, 2), 'utf-8');
            this._onDidChangeTaskData.fire();
        } catch (error) {
            console.error('Error saving tasks:', error);
            vscode.window.showErrorMessage(`Failed to save tasks: ${error}`);
        }
    }

    public refresh(): void {
        this.loadTasks();
    }

    public async getAllTasks(): Promise<Task[]> {
        return this.tasks;
    }

    public async getTask(id: string): Promise<Task | undefined> {
        return this.tasks.find(task => task.id === id);
    }

    public async createTask(
        title: string, 
        filePath: string, 
        selection?: { startLine: number; endLine: number }
    ): Promise<Task> {
        // Generate a unique task ID (e.g., T-123)
        const taskId = `T-${Date.now().toString().slice(-6)}`;
        
        const newTask: Task = {
            id: taskId,
            title,
            description: '',
            status: 'pending',
            priority: 'medium',
            dependencies: [],
            codeReferences: [{
                filePath,
                selection
            }]
        };
        
        this.tasks.push(newTask);
        await this.saveTasks();
        return newTask;
    }

    public async updateTaskStatus(id: string, status: 'pending' | 'in-progress' | 'done' | 'deferred'): Promise<void> {
        const taskIndex = this.tasks.findIndex(task => task.id === id);
        if (taskIndex >= 0) {
            this.tasks[taskIndex].status = status;
            await this.saveTasks();
        }
    }

    public async linkTaskToCode(
        taskId: string, 
        filePath: string, 
        position: { line: number; character: number }
    ): Promise<void> {
        const taskIndex = this.tasks.findIndex(task => task.id === taskId);
        if (taskIndex >= 0) {
            // Check if there's already a reference to this file
            const existingRefIndex = this.tasks[taskIndex].codeReferences.findIndex(
                ref => ref.filePath === filePath
            );
            
            if (existingRefIndex >= 0) {
                // Update existing reference with the new position
                this.tasks[taskIndex].codeReferences[existingRefIndex].position = position;
            } else {
                // Add a new code reference
                this.tasks[taskIndex].codeReferences.push({
                    filePath,
                    position
                });
            }
            
            await this.saveTasks();
        }
    }

    public async calculateTaskRelevance(task: Task, filePath: string): Promise<number> {
        // Simple relevance calculation based on code references
        // More sophisticated algorithms could analyze file content, imports, etc.
        
        // Direct file reference is highest relevance
        const directReference = task.codeReferences.find(ref => ref.filePath === filePath);
        if (directReference) {
            return 1.0; // Perfect match
        }
        
        // Check for references to files in the same directory
        const fileDir = path.dirname(filePath);
        const hasSameDirReference = task.codeReferences.some(ref => 
            path.dirname(ref.filePath) === fileDir
        );
        
        if (hasSameDirReference) {
            return 0.7; // Same directory match
        }
        
        // Check for filename similarity
        const fileName = path.basename(filePath);
        const similarFileReference = task.codeReferences.some(ref => {
            const refFileName = path.basename(ref.filePath);
            return fileName.includes(refFileName) || refFileName.includes(fileName);
        });
        
        if (similarFileReference) {
            return 0.5; // Similar filename match
        }
        
        // Default low relevance
        return 0.1;
    }
} 