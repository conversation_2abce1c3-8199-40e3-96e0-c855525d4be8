# Focus Forge VS Code Extension

A VS Code extension that provides a dedicated sidebar for displaying tasks from Focus Forge relevant to your current file.

## Features

- **Current File Tasks**: Shows tasks directly related to the file you're editing
- **Module Tasks**: Displays tasks related to the module/package containing your current file
- **Related Tasks**: Shows tasks connected through dependencies to your current file tasks
- **Task Creation**: Create tasks directly from selected code
- **Code-Task Linking**: Bidirectional links between code locations and tasks
- **Status Updates**: Update task status directly from VS Code

## Usage

### Task Sidebar

The extension adds a Focus Forge icon to your activity bar, which opens a sidebar with three task views:

1. **Current File Tasks**: Tasks with direct references to the file you're currently editing
2. **Module Tasks**: Tasks related to files in the same directory (module)
3. **Related Tasks**: Tasks connected through dependencies

### Commands

- **Focus Forge: Refresh Tasks** - Refresh the task views
- **Focus Forge: Create Task from Selection** - Create a new task from selected code
- **Focus Forge: Link Task to Current Position** - Link an existing task to your current cursor position
- **Focus Forge: Update Task Status** - Change the status of a task

### Settings

- **Tasks File Path**: Path to your tasks.json file (default: "tasks.json" in workspace root)
- **Relevance Threshold**: Minimum relevance score for tasks to appear in the current file view (0-1)
- **Show Completed Tasks**: Whether to show completed tasks in the views

## Requirements

- Focus Forge task manager with a valid tasks.json file

## Installation

This extension is currently in development. To install:

1. Clone the repository
2. Run `npm install` in the extension directory
3. Run `npm run compile` to build the extension
4. Press F5 to launch the extension in a new VS Code window

## Testing

The extension includes a comprehensive test suite:

```bash
# Install dependencies
npm install

# Run tests
npm test
```

For detailed information about the test framework, test structure, and ways to run tests, see [TESTING.md](./TESTING.md).

## Contributing

Contributions are welcome! Please feel free to submit issues or pull requests. 