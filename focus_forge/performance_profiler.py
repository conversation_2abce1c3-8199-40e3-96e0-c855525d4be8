#!/usr/bin/env python3
"""
Performance Profiler for Focus Forge.

This script profiles the performance of key focus_forge components when processing
large PRDs, identifying bottlenecks and areas for optimization.
"""
import os
import sys
import time
import cProfile
import pstats
import io
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Callable

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import focus_forge components
try:
    # If running from the main project
    from focus_forge.analyze_prd import analyze_prd_file
    from focus_forge.task_generation import TaskGenerator, TaskGenerationConfig
    from focus_forge.dependency_graph import DependencyGraph, build_graph_from_tasks
    from focus_forge.sequence_analysis import SequenceAnalyzer
    from focus_forge.nlp import RelationshipDetector
    from focus_forge.component_detection import ComponentDetector
except ImportError:
    # If running from the same directory as focus_forge.py
    sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
    try:
        from focus_forge.analyze_prd import analyze_prd_file
        from focus_forge.task_generation import TaskGenerator, TaskGenerationConfig
        from focus_forge.dependency_graph import DependencyGraph, build_graph_from_tasks
        from focus_forge.sequence_analysis import SequenceAnalyzer
        from focus_forge.nlp import RelationshipDetector
        from focus_forge.component_detection import ComponentDetector
    except ImportError:
        logger.warning("Couldn't import focus_forge modules, trying direct imports")
        try:
            # Try direct imports if running from inside a directory
            from analyze_prd import analyze_prd_file
            from task_generation import TaskGenerator, TaskGenerationConfig
            from dependency_graph import DependencyGraph, build_graph_from_tasks
            from sequence_analysis import SequenceAnalyzer
            # These might not be available in direct import
            try:
                from nlp import RelationshipDetector
                from component_detection import ComponentDetector
            except ImportError:
                logger.warning("Could not import NLP and ComponentDetector modules")
                RelationshipDetector = None
                ComponentDetector = None
        except ImportError as e:
            logger.error(f"Failed to import focus_forge modules: {e}")
            sys.exit(1)


def generate_large_prd(size_kb: int = 500, output_file: Optional[str] = None) -> str:
    """
    Generate a large PRD file for testing.
    
    Args:
        size_kb: Target size in KB
        output_file: Optional file to save the PRD
        
    Returns:
        The generated PRD text
    """
    logger.info(f"Generating large PRD (~{size_kb} KB)...")
    
    # Base template for sections
    section_template = """
# {section_title}

## Overview
{overview}

## Requirements
{requirements}

## Dependencies
{dependencies}

## Implementation Details
{implementation}
"""
    
    # Paragraphs to use in sections
    paragraphs = [
        "This component provides core functionality for processing user input and generating appropriate responses. It needs to handle various input formats and validate all data before processing.",
        "Users should be able to submit data in multiple formats including JSON, XML, and plain text. The system must validate this input against defined schemas and provide clear error messages for invalid data.",
        "Real-time processing is essential for this component, with a target response time of under 500ms for typical requests. Performance should be monitored and optimized accordingly.",
        "The API must be well-documented with clear examples of all endpoints. Authentication should use OAuth 2.0 and include rate limiting to prevent abuse.",
        "Data persistence will use a combination of relational database for structured data and document store for unstructured content. Caching should be implemented for frequently accessed data.",
        "Mobile interfaces must be responsive and support both iOS and Android platforms. The UI should adapt to different screen sizes and orientations dynamically.",
        "Analytics tracking should be implemented to monitor user behavior and system performance. This data should be anonymized and comply with relevant privacy regulations.",
        "Integration with third-party services must be done through a unified adapter interface, allowing for easy replacement of service providers as needed.",
        "The deployment pipeline should include automated testing at all levels and support continuous integration. Infrastructure should be defined as code for reproducibility.",
        "The search functionality must support full-text search with relevance ranking. It should include filters for narrowing results and support for faceted navigation."
    ]
    
    # Generate dependency statements
    dependency_statements = [
        "The Authentication Service depends on the User Management System for user validation.",
        "Data Processing requires the Analytics Engine to be available for real-time analysis.",
        "The Mobile Interface needs the API Gateway to be implemented first.",
        "Email Notifications depend on the Template Engine for rendering message content.",
        "The Reporting Module requires both Data Storage and User Permissions to function correctly.",
        "Search Functionality depends on the Indexing Service for maintaining up-to-date search indexes.",
        "Admin Dashboard needs the Authentication Service to ensure proper access control.",
        "Payment Processing depends on the Security Module for encryption of sensitive information.",
        "The Feedback Collection system requires the User Management System to associate feedback with users.",
        "Content Management depends on the File Storage Service for media handling."
    ]
    
    # Generate sections until we reach the target size
    prd_text = "# Product Requirements Document\n\n"
    prd_text += "## Introduction\n\nThis document outlines the requirements for a large-scale application. The implementation should follow best practices for scalability, security, and maintainability.\n\n"
    
    component_names = [
        "User Authentication", "Data Processing", "API Gateway", "Mobile Interface", 
        "Admin Dashboard", "Reporting Module", "Content Management", "Search Functionality",
        "Payment Processing", "Email Notifications", "Analytics Engine", "Security Module",
        "User Management System", "Template Engine", "Data Storage", "File Storage Service",
        "Indexing Service", "User Permissions", "Feedback Collection", "Integration Layer"
    ]
    
    current_size = len(prd_text.encode('utf-8')) / 1024
    section_number = 1
    
    while current_size < size_kb:
        section_title = component_names[(section_number - 1) % len(component_names)]
        section_title = f"Component {section_number}: {section_title}"
        
        # Generate random content for this section
        overview = paragraphs[section_number % len(paragraphs)]
        
        # Requirements - multiple paragraphs
        requirements = "\n\n".join([
            paragraphs[(section_number + i) % len(paragraphs)] 
            for i in range(3)
        ])
        
        # Dependencies - include some dependency statements
        dependencies = "\n\n".join([
            dependency_statements[(section_number + i) % len(dependency_statements)]
            for i in range(2)
        ])
        
        # Implementation details - more paragraphs
        implementation = "\n\n".join([
            paragraphs[(section_number + i + 5) % len(paragraphs)]
            for i in range(4)
        ])
        
        # Format the section
        section = section_template.format(
            section_title=section_title,
            overview=overview,
            requirements=requirements,
            dependencies=dependencies,
            implementation=implementation
        )
        
        prd_text += section
        section_number += 1
        current_size = len(prd_text.encode('utf-8')) / 1024
    
    logger.info(f"Generated PRD with size: {current_size:.2f} KB")
    
    # Save to file if specified
    if output_file:
        with open(output_file, 'w') as f:
            f.write(prd_text)
        logger.info(f"Saved PRD to {output_file}")
    
    return prd_text


def profile_function(func: Callable, *args, **kwargs) -> Tuple[Any, float, Optional[str]]:
    """
    Profile a function's execution time and memory usage.
    
    Args:
        func: Function to profile
        *args, **kwargs: Arguments to pass to the function
    
    Returns:
        Tuple containing function result, execution time, and profiling stats
    """
    # Create a string buffer to capture profiling output
    profile_output = io.StringIO()
    
    # Set up profiler
    profiler = cProfile.Profile()
    
    # Start timing
    start_time = time.time()
    
    # Run with profiler
    profiler.enable()
    result = func(*args, **kwargs)
    profiler.disable()
    
    # Calculate execution time
    execution_time = time.time() - start_time
    
    # Create stats object and sort by cumulative time
    stats = pstats.Stats(profiler, stream=profile_output)
    stats.sort_stats('cumulative')
    stats.print_stats(30)  # Print top 30 functions
    
    return result, execution_time, profile_output.getvalue()


def profile_prd_analysis(prd_file: str) -> Dict[str, Any]:
    """
    Profile PRD analysis performance.
    
    Args:
        prd_file: Path to the PRD file
    
    Returns:
        Dictionary with performance results
    """
    logger.info(f"Profiling PRD analysis for file: {prd_file}")
    
    # Read the PRD file
    with open(prd_file, 'r') as f:
        prd_text = f.read()
    
    prd_size_kb = len(prd_text.encode('utf-8')) / 1024
    logger.info(f"PRD size: {prd_size_kb:.2f} KB")
    
    results = {
        "prd_file": prd_file,
        "prd_size_kb": prd_size_kb,
        "components": {}
    }
    
    # Profile analyze_prd_file
    logger.info("Profiling PRD analysis...")
    analysis_result, analysis_time, analysis_profile = profile_function(
        analyze_prd_file, prd_file
    )
    results["components"]["prd_analysis"] = {
        "execution_time": analysis_time,
        "profile_summary": analysis_profile.split("\n")[:20]
    }
    logger.info(f"PRD analysis completed in {analysis_time:.2f} seconds")
    
    # Profile task generation
    logger.info("Profiling task generation...")
    task_generator = TaskGenerator()
    tasks_result, tasks_time, tasks_profile = profile_function(
        task_generator.generate_tasks_from_prd, prd_text
    )
    results["components"]["task_generation"] = {
        "execution_time": tasks_time,
        "task_count": len(tasks_result),
        "profile_summary": tasks_profile.split("\n")[:20]
    }
    logger.info(f"Task generation completed in {tasks_time:.2f} seconds, generated {len(tasks_result)} tasks")
    
    # Profile dependency graph building
    logger.info("Profiling dependency graph building...")
    graph_result, graph_time, graph_profile = profile_function(
        build_graph_from_tasks, tasks_result
    )
    results["components"]["dependency_graph"] = {
        "execution_time": graph_time,
        "node_count": len(graph_result.get_nodes()),
        "profile_summary": graph_profile.split("\n")[:20]
    }
    logger.info(f"Dependency graph building completed in {graph_time:.2f} seconds")
    
    # Calculate total time
    results["total_execution_time"] = (
        results["components"]["prd_analysis"]["execution_time"] +
        results["components"]["task_generation"]["execution_time"] +
        results["components"]["dependency_graph"]["execution_time"]
    )
    
    logger.info(f"Total execution time: {results['total_execution_time']:.2f} seconds")
    
    return results


def main():
    """Main function to parse arguments and run profiling."""
    parser = argparse.ArgumentParser(
        description="Profile Focus Forge performance with large PRDs."
    )
    parser.add_argument(
        "--prd", 
        help="Path to PRD file to analyze. If not provided, a test PRD will be generated."
    )
    parser.add_argument(
        "--generate-size",
        type=int,
        default=500,
        help="Size in KB for generated test PRD (default: 500KB)"
    )
    parser.add_argument(
        "--output",
        help="Path to save profiling results as JSON"
    )
    parser.add_argument(
        "--save-prd",
        help="Path to save generated PRD (if generating)"
    )
    
    args = parser.parse_args()
    
    # Generate or use provided PRD
    prd_file = args.prd
    if not prd_file:
        logger.info(f"No PRD file provided, generating test PRD of {args.generate_size}KB...")
        prd_text = generate_large_prd(args.generate_size, args.save_prd)
        
        # Save to temporary file if no save path provided
        if not args.save_prd:
            temp_prd = Path("temp_test_prd.md")
            with open(temp_prd, 'w') as f:
                f.write(prd_text)
            prd_file = str(temp_prd)
        else:
            prd_file = args.save_prd
    
    # Run profiling
    results = profile_prd_analysis(prd_file)
    
    # Print summary
    print("\nPerformance Summary:")
    print(f"PRD Size: {results['prd_size_kb']:.2f} KB")
    print(f"Total Execution Time: {results['total_execution_time']:.2f} seconds")
    print("\nComponent Breakdown:")
    for component, data in results["components"].items():
        print(f"  - {component}: {data['execution_time']:.2f} seconds")
    
    # Save results if output path provided
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2)
        logger.info(f"Saved profiling results to {args.output}")
    
    # Clean up temporary file if created
    if not args.prd and not args.save_prd:
        if os.path.exists("temp_test_prd.md"):
            os.remove("temp_test_prd.md")
            logger.info("Removed temporary PRD file")


if __name__ == "__main__":
    main() 