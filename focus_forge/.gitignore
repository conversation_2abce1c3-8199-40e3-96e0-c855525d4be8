# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

# pytest
pytest.ini.bak

# VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Other IDE files
.idea/
*.swp
*.swo

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
*.dot
*.png
tasks.json
component_analysis.json
.focus_forge_config
.claude/
.ai/

# Generated and test files
get_next_task.py
quick_test.py
run_ai_guidance_tests.py
run_basic_tests.py
run_manual_tests.py
test_ai_guidance.py
test_merge_tasks.py
test_merge_tasks_direct.py
test_mermaid.py
test_prd_relationships.py
test_dependency_graph_isolated.py
test_dependency_graph_real.py
test_dependency_graph_extended.py
test_dependency_graph_focus_forge.py
test_dependency_graph_standalone.py
test_sequence_analysis.py
prd_parser_prd.md

# Generated directories
tasks/
diagrams/

# Ruff cache
.ruff_cache/

# Backup files
*.bak
backup/ 