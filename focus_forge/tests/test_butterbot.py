"""Tests for butterbot integration in Focus Forge."""

import os
import sys
import json
from pathlib import Path
from unittest.mock import patch, MagicMock

import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

# Ensure focus_forge is in the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from focus_forge import (
    cli, AI_FOLDER, CLAUDE_FOLDER, CONFIG_FILE, TASKS_FILE,
    check_ai_guidance_folders, get_ai_guidance_path, 
    has_butterbot_integration, get_butterbot_context,
    generate_tasks_from_prd
)


@pytest.fixture
def setup_ai_folders(temp_dir):
    """Set up AI guidance folders for testing."""
    ai_folder = temp_dir / AI_FOLDER
    claude_folder = temp_dir / CLAUDE_FOLDER
    
    # Create required directories
    ai_folder.mkdir(exist_ok=True)
    (ai_folder / "docs").mkdir(exist_ok=True)
    (ai_folder / "docs/1-context").mkdir(exist_ok=True)
    (ai_folder / "docs/1-context/standards").mkdir(exist_ok=True)
    (ai_folder / "docs/2-technical-design").mkdir(exist_ok=True)
    (ai_folder / "docs/2-technical-design/development_workflow").mkdir(exist_ok=True)
    (ai_folder / "docs/3-development").mkdir(exist_ok=True)
    (ai_folder / "docs/3-development/patterns").mkdir(exist_ok=True)
    (ai_folder / "docs/4-acceptance").mkdir(exist_ok=True)
    (ai_folder / "docs/4-acceptance/security").mkdir(exist_ok=True)
    
    # Create structure file
    structure_file = ai_folder / "STRUCTURE.md"
    structure_file.write_text("# Project Structure\n\nThis is a test structure file.")
    
    # Create sample documentation files
    standards_file = ai_folder / "docs/1-context/standards/coding_standards.md"
    standards_file.write_text("# Coding Standards\n\n- Use consistent naming\n- Add tests")
    
    patterns_file = ai_folder / "docs/3-development/patterns/design_patterns.md"
    patterns_file.write_text("# Design Patterns\n\n- Factory Pattern\n- Observer Pattern")
    
    workflows_file = ai_folder / "docs/2-technical-design/development_workflow/workflow.md"
    workflows_file.write_text("# Development Workflow\n\n1. Create tests\n2. Implement feature")
    
    security_file = ai_folder / "docs/4-acceptance/security/checklist.md"
    security_file.write_text("# Security Checklist\n\n- Validate inputs\n- Sanitize outputs")
    
    return ai_folder


def test_check_ai_guidance_folders(temp_dir, setup_ai_folders):
    """Test detecting AI guidance folders."""
    # Test when both folders exist
    with patch('os.path.isdir', lambda path: path.endswith(AI_FOLDER) or path.endswith(CLAUDE_FOLDER)):
        has_ai, has_claude = check_ai_guidance_folders()
        assert has_ai
        assert has_claude
    
    # Test when only .ai folder exists
    with patch('os.path.isdir', lambda path: path.endswith(AI_FOLDER)):
        has_ai, has_claude = check_ai_guidance_folders()
        assert has_ai
        assert not has_claude
    
    # Test when only .claude folder exists
    with patch('os.path.isdir', lambda path: path.endswith(CLAUDE_FOLDER)):
        has_ai, has_claude = check_ai_guidance_folders()
        assert not has_ai
        assert has_claude
    
    # Test when no folders exist
    with patch('os.path.isdir', lambda path: False):
        has_ai, has_claude = check_ai_guidance_folders()
        assert not has_ai
        assert not has_claude


def test_get_ai_guidance_path():
    """Test determining which AI guidance path to use."""
    # Test when both folders exist (should prefer .ai)
    with patch('focus_forge.check_ai_guidance_folders', return_value=(True, True)):
        assert get_ai_guidance_path() == AI_FOLDER
    
    # Test when only .ai folder exists
    with patch('focus_forge.check_ai_guidance_folders', return_value=(True, False)):
        assert get_ai_guidance_path() == AI_FOLDER
    
    # Test when only .claude folder exists
    with patch('focus_forge.check_ai_guidance_folders', return_value=(False, True)):
        assert get_ai_guidance_path() == CLAUDE_FOLDER
    
    # Test when no folders exist
    with patch('focus_forge.check_ai_guidance_folders', return_value=(False, False)):
        assert get_ai_guidance_path() is None


def test_has_butterbot_integration(setup_ai_folders):
    """Test checking for butterbot integration."""
    # Test when STRUCTURE.md exists
    with patch('focus_forge.get_ai_guidance_path', return_value=AI_FOLDER):
        with patch('os.path.isfile', return_value=True):
            assert has_butterbot_integration()
    
    # Test when STRUCTURE.md doesn't exist
    with patch('focus_forge.get_ai_guidance_path', return_value=AI_FOLDER):
        with patch('os.path.isfile', return_value=False):
            assert not has_butterbot_integration()
    
    # Test when no AI guidance path exists
    with patch('focus_forge.get_ai_guidance_path', return_value=None):
        assert not has_butterbot_integration()


def test_get_butterbot_context(setup_ai_folders):
    """Test retrieving context from butterbot."""
    # Set up mocking
    with patch('focus_forge.get_ai_guidance_path', return_value=AI_FOLDER):
        with patch('focus_forge.has_butterbot_integration', return_value=True):
            # Test retrieving patterns context
            patterns_path = os.path.join(AI_FOLDER, "docs/3-development/patterns")
            with patch('os.path.exists', return_value=True):
                with patch('os.path.isdir', return_value=True):
                    with patch('os.listdir', return_value=["design_patterns.md"]):
                        context = get_butterbot_context("patterns")
                        assert context is not None
                        assert "Available patterns documentation" in context
                        assert "design_patterns.md" in context
            
            # Test retrieving specific file content
            with patch('os.path.exists', return_value=True):
                with patch('os.path.isdir', return_value=False):
                    with patch('open', create=True) as mock_open:
                        mock_file = MagicMock()
                        mock_file.read.return_value = "Test standards content"
                        mock_open.return_value.__enter__.return_value = mock_file
                        
                        context = get_butterbot_context("standards")
                        assert context == "Test standards content"
            
            # Test with non-existent context type
            assert get_butterbot_context("nonexistent") is None
            
            # Test with path that doesn't exist
            with patch('os.path.exists', return_value=False):
                assert get_butterbot_context("patterns") is None


def test_generate_tasks_from_prd_with_butterbot(runner, temp_dir, setup_ai_folders, sample_config):
    """Test generating tasks from PRD with butterbot integration."""
    # Create PRD file
    prd_file = temp_dir / "prd.txt"
    prd_file.write_text("Test PRD with requirements")
    
    # Setup config
    config_file = temp_dir / CONFIG_FILE
    config_file.write_text(json.dumps(sample_config))
    
    # Mock butterbot integration
    with patch('focus_forge.has_butterbot_integration', return_value=True):
        with patch('focus_forge.get_butterbot_context') as mock_get_context:
            # Return different context for different types
            mock_get_context.side_effect = lambda context_type: {
                "patterns": "Test patterns context",
                "standards": "Test standards context",
            }.get(context_type)
            
            # Mock Claude API call to verify butterbot context is included
            with patch('focus_forge.call_claude_api') as mock_api:
                mock_api.return_value = json.dumps([{
                    "id": "1",
                    "title": "Task with butterbot context",
                    "description": "Description referencing standards",
                    "status": "pending",
                    "priority": "high",
                    "dependencies": [],
                    "details": "Implementation using patterns from butterbot",
                    "test_strategy": "Testing according to standards"
                }])
                
                # Read PRD and generate tasks
                with open(prd_file, 'r') as f:
                    prd_text = f.read()
                
                tasks = generate_tasks_from_prd(prd_text, sample_config)
    
    # Verify task was created
    assert len(tasks) == 1
    assert tasks[0]["title"] == "Task with butterbot context"
    
    # Verify Claude API was called with butterbot context
    args, kwargs = mock_api.call_args
    assert "Additional context from project AI guidance" in args[0]
    assert "Test patterns context" in args[0]
    assert "Test standards context" in args[0]


def test_parse_prd_command_with_butterbot(runner, temp_dir, setup_ai_folders):
    """Test parse-prd command with butterbot integration."""
    # Create PRD file
    prd_file = temp_dir / "prd.txt"
    prd_file.write_text("Test PRD document")
    
    # Mock butterbot integration
    with patch('focus_forge.has_butterbot_integration', return_value=True):
        with patch('focus_forge.get_butterbot_context') as mock_get_context:
            # Return different context for different types
            mock_get_context.side_effect = lambda context_type: {
                "patterns": "Test patterns context",
                "standards": "Test standards context",
            }.get(context_type)
            
            # Mock API call
            with patch('focus_forge.call_claude_api') as mock_api:
                mock_api.return_value = json.dumps([{
                    "id": "1",
                    "title": "Task with butterbot",
                    "description": "Task description",
                    "status": "pending",
                    "priority": "high",
                    "dependencies": [],
                    "details": "Details with butterbot integration",
                    "test_strategy": "Test strategy"
                }])
                
                # Mock API key prompt
                with patch('click.prompt', return_value="test-api-key"):
                    # Run command
                    result = runner.invoke(cli, ["parse-prd", "--input=prd.txt"])
    
    # Verify command succeeded
    assert result.exit_code == 0
    assert "Successfully generated" in result.output
    
    # Verify tasks file was created
    tasks_file = temp_dir / TASKS_FILE
    assert tasks_file.exists()
    
    # Verify task content
    with open(tasks_file, 'r') as f:
        data = json.load(f)
        tasks = data.get("tasks", [])
        assert len(tasks) == 1
        assert tasks[0]["title"] == "Task with butterbot"


def test_ai_guidance_status_command_with_butterbot(runner, temp_dir, setup_ai_folders):
    """Test ai-guidance-status command with butterbot integration."""
    with patch('focus_forge.check_ai_guidance_folders', return_value=(True, False)):
        with patch('focus_forge.has_butterbot_integration', return_value=True):
            with patch('focus_forge.get_butterbot_context') as mock_get_context:
                # Return "Available" for all context types
                mock_get_context.return_value = "Available"
                
                result = runner.invoke(cli, ["ai-guidance-status"])
    
    # Verify command output
    assert result.exit_code == 0
    assert "AI Guidance Status" in result.output
    assert ".ai folder: Found" in result.output
    assert "Butterbot integration: Available" in result.output
    assert "Patterns: Available" in result.output
    assert "Standards: Available" in result.output
    assert "Workflows: Available" in result.output
    assert "Security: Available" in result.output 