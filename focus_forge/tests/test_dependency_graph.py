#!/usr/bin/env python3
"""
Tests for the dependency graph implementation.
"""
import pytest
from pathlib import Path
import json
import tempfile
from focus_forge.dependency_graph import (
    DependencyGraph,
    Dependency,
    DependencyType
)


def test_empty_graph():
    """Test initialization of an empty graph."""
    graph = DependencyGraph()
    assert len(graph.nodes) == 0
    assert len(graph.edges) == 0
    assert len(graph.reverse_edges) == 0


def test_add_node():
    """Test adding nodes to the graph."""
    graph = DependencyGraph()
    
    # Add a single node
    graph.add_node("T1")
    assert "T1" in graph.nodes
    assert "T1" in graph.edges
    assert "T1" in graph.reverse_edges
    assert len(graph.edges["T1"]) == 0
    assert len(graph.reverse_edges["T1"]) == 0
    
    # Add another node
    graph.add_node("T2")
    assert "T2" in graph.nodes
    assert "T2" in graph.edges
    assert "T2" in graph.reverse_edges


def test_add_dependency():
    """Test adding dependencies to the graph."""
    graph = DependencyGraph()
    
    # Create a dependency
    dep = Dependency(
        source_id="T1",
        target_id="T2",
        dep_type=DependencyType.DEPENDS_ON,
        description="T1 depends on T2"
    )
    
    # Add the dependency
    success = graph.add_dependency(dep)
    assert success is True
    
    # Verify the dependency was added
    assert "T1" in graph.nodes
    assert "T2" in graph.nodes
    assert len(graph.edges["T1"]) == 1
    assert len(graph.reverse_edges["T2"]) == 1
    assert graph.edges["T1"][0] == dep
    assert graph.reverse_edges["T2"][0] == dep


def test_circular_dependency():
    """Test detection and prevention of circular dependencies."""
    graph = DependencyGraph()
    
    # Add first dependency
    dep1 = Dependency(
        source_id="T1",
        target_id="T2",
        dep_type=DependencyType.DEPENDS_ON
    )
    assert graph.add_dependency(dep1) is True
    
    # Add second dependency
    dep2 = Dependency(
        source_id="T2",
        target_id="T3",
        dep_type=DependencyType.DEPENDS_ON
    )
    assert graph.add_dependency(dep2) is True
    
    # Try to add circular dependency
    dep3 = Dependency(
        source_id="T3",
        target_id="T1",
        dep_type=DependencyType.DEPENDS_ON
    )
    assert graph.add_dependency(dep3) is False


def test_remove_dependency():
    """Test removing dependencies from the graph."""
    graph = DependencyGraph()
    
    # Add a dependency
    dep = Dependency(
        source_id="T1",
        target_id="T2",
        dep_type=DependencyType.DEPENDS_ON
    )
    graph.add_dependency(dep)
    
    # Remove the dependency
    success = graph.remove_dependency("T1", "T2")
    assert success is True
    
    # Verify the dependency was removed
    assert len(graph.edges["T1"]) == 0
    assert len(graph.reverse_edges["T2"]) == 0
    
    # Try to remove non-existent dependency
    success = graph.remove_dependency("T1", "T3")
    assert success is False


def test_get_dependencies():
    """Test retrieving dependencies for a task."""
    graph = DependencyGraph()
    
    # Add multiple dependencies
    deps = [
        Dependency("T1", "T2", DependencyType.DEPENDS_ON),
        Dependency("T1", "T3", DependencyType.REQUIRES),
        Dependency("T2", "T3", DependencyType.ENABLES)
    ]
    
    for dep in deps:
        graph.add_dependency(dep)
    
    # Test getting dependencies
    t1_deps = graph.get_dependencies("T1")
    assert len(t1_deps) == 2
    assert all(dep.source_id == "T1" for dep in t1_deps)
    
    t2_deps = graph.get_dependencies("T2")
    assert len(t2_deps) == 1
    assert t2_deps[0].target_id == "T3"


def test_get_reverse_dependencies():
    """Test retrieving reverse dependencies for a task."""
    graph = DependencyGraph()
    
    # Add multiple dependencies
    deps = [
        Dependency("T1", "T3", DependencyType.DEPENDS_ON),
        Dependency("T2", "T3", DependencyType.REQUIRES),
        Dependency("T3", "T4", DependencyType.ENABLES)
    ]
    
    for dep in deps:
        graph.add_dependency(dep)
    
    # Test getting reverse dependencies
    t3_rev_deps = graph.get_reverse_dependencies("T3")
    assert len(t3_rev_deps) == 2
    assert all(dep.target_id == "T3" for dep in t3_rev_deps)


def test_detect_cycles():
    """Test cycle detection in the graph."""
    graph = DependencyGraph()
    
    # Add dependencies that create a cycle
    deps = [
        Dependency("T1", "T2", DependencyType.DEPENDS_ON),
        Dependency("T2", "T3", DependencyType.DEPENDS_ON),
        Dependency("T3", "T1", DependencyType.DEPENDS_ON)
    ]
    
    for dep in deps:
        graph.add_dependency(dep)
    
    # Detect cycles
    cycles = graph.detect_cycles()
    assert len(cycles) == 1
    assert len(cycles[0]) == 3
    assert set(cycles[0]) == {"T1", "T2", "T3"}


def test_get_longest_dependency_chain():
    """Test finding the longest dependency chain."""
    graph = DependencyGraph()
    
    # Add dependencies forming a chain
    deps = [
        Dependency("T1", "T2", DependencyType.DEPENDS_ON),
        Dependency("T2", "T3", DependencyType.DEPENDS_ON),
        Dependency("T3", "T4", DependencyType.DEPENDS_ON),
        Dependency("T1", "T5", DependencyType.DEPENDS_ON)  # Branch
    ]
    
    for dep in deps:
        graph.add_dependency(dep)
    
    # Find longest chain
    chain = graph.get_longest_dependency_chain("T1")
    assert len(chain) == 4
    assert chain == ["T1", "T2", "T3", "T4"]


def test_serialization():
    """Test serialization and deserialization of the graph."""
    graph = DependencyGraph()
    
    # Add some dependencies
    deps = [
        Dependency("T1", "T2", DependencyType.DEPENDS_ON, "Test dependency"),
        Dependency("T2", "T3", DependencyType.REQUIRES)
    ]
    
    for dep in deps:
        graph.add_dependency(dep)
    
    # Convert to dictionary
    graph_dict = graph.to_dict()
    assert "nodes" in graph_dict
    assert "edges" in graph_dict
    assert len(graph_dict["nodes"]) == 3
    assert len(graph_dict["edges"]) == 2
    
    # Create new graph from dictionary
    new_graph = DependencyGraph.from_dict(graph_dict)
    assert len(new_graph.nodes) == 3
    assert len(new_graph.edges) == 3
    assert len(new_graph.reverse_edges) == 3


def test_file_io():
    """Test saving and loading the graph to/from a file."""
    graph = DependencyGraph()
    
    # Add some dependencies
    deps = [
        Dependency("T1", "T2", DependencyType.DEPENDS_ON),
        Dependency("T2", "T3", DependencyType.REQUIRES)
    ]
    
    for dep in deps:
        graph.add_dependency(dep)
    
    # Save to temporary file
    with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
        graph.save(f.name)
        
        # Load from file
        loaded_graph = DependencyGraph.load(f.name)
        assert len(loaded_graph.nodes) == 3
        assert len(loaded_graph.edges) == 3
        assert len(loaded_graph.reverse_edges) == 3
    
    # Clean up
    Path(f.name).unlink()


def test_complex_dependencies():
    """Test handling of complex dependency scenarios."""
    graph = DependencyGraph()
    
    # Add dependencies with different types
    deps = [
        Dependency("T1", "T2", DependencyType.DEPENDS_ON),
        Dependency("T2", "T3", DependencyType.REQUIRES),
        Dependency("T3", "T4", DependencyType.ENABLES),
        Dependency("T4", "T5", DependencyType.PREREQUISITE),
        Dependency("T5", "T6", DependencyType.BLOCKS),
        Dependency("T6", "T7", DependencyType.OPTIONAL)
    ]
    
    for dep in deps:
        graph.add_dependency(dep)
    
    # Verify graph structure
    assert len(graph.nodes) == 7
    assert len(graph.edges) == 7
    assert len(graph.reverse_edges) == 7
    
    # Verify dependency chain
    chain = graph.get_longest_dependency_chain("T1")
    assert len(chain) == 7
    assert chain == ["T1", "T2", "T3", "T4", "T5", "T6", "T7"]
    
    # Verify no cycles
    cycles = graph.detect_cycles()
    assert len(cycles) == 0 