"""Comprehensive tests for Focus Forge functionality."""

import json
import os
import sys
import re
from pathlib import Path
from datetime import datetime
from unittest.mock import patch, MagicMock, call

import pytest
from click.testing import C<PERSON><PERSON>unner
from rich.text import Text

# Ensure focus_forge is in the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from focus_forge import (
    cli, CONFIG_FILE, TASKS_FILE, TASKS_DIR, AI_FOLDER, CLAUDE_FOLDER,
    load_config, save_config, load_tasks, save_tasks, find_task_by_id,
    validate_dependencies, get_next_task, colorize_status, colorize_priority,
    generate_task_files, call_claude_api, check_ai_guidance_folders,
    get_ai_guidance_path, has_butterbot_integration, get_butterbot_context,
    generate_tasks_from_prd, expand_task
)


# --- Test Utility Functions ---

def test_colorize_status():
    """Test colorizing task status."""
    statuses = ["pending", "in-progress", "done", "deferred"]
    
    for status in statuses:
        result = colorize_status(status)
        assert isinstance(result, Text)
        assert result.plain == status
        
        if status == "done":
            assert "green" in result.style
        elif status == "in-progress":
            assert "blue" in result.style
        elif status == "deferred":
            assert "yellow" in result.style
        else:  # pending
            assert "white" in result.style


def test_colorize_priority():
    """Test colorizing task priority."""
    priorities = ["high", "medium", "low"]
    
    for priority in priorities:
        result = colorize_priority(priority)
        assert isinstance(result, Text)
        assert result.plain == priority
        
        if priority == "high":
            assert "red" in result.style
        elif priority == "medium":
            assert "yellow" in result.style
        else:  # low
            assert "green" in result.style


def test_generate_task_files(temp_dir, sample_tasks):
    """Test generating individual task files."""
    tasks_dir = temp_dir / TASKS_DIR
    
    generate_task_files(sample_tasks, str(tasks_dir))
    
    # Check that task files were created
    assert tasks_dir.exists()
    task_files = list(tasks_dir.glob("task_*.txt"))
    assert len(task_files) == len(sample_tasks)
    
    # Check content of one task file
    task_file = tasks_dir / "task_001.txt"
    assert task_file.exists()
    
    content = task_file.read_text()
    assert f"Task 1: {sample_tasks[0]['title']}" in content
    assert f"Status: {sample_tasks[0]['status']}" in content
    assert f"Priority: {sample_tasks[0]['priority']}" in content
    assert sample_tasks[0]['description'] in content
    assert sample_tasks[0]['details'] in content


def test_ai_guidance_functions(temp_dir):
    """Test AI guidance folder detection and integration."""
    # Initially no folders exist
    has_ai, has_claude = check_ai_guidance_folders()
    assert not has_ai
    assert not has_claude
    assert get_ai_guidance_path() is None
    assert not has_butterbot_integration()
    
    # Create .ai folder
    ai_path = temp_dir / AI_FOLDER
    ai_path.mkdir()
    
    with patch('os.path.isdir', return_value=True):
        with patch('os.path.exists', return_value=True):
            has_ai, has_claude = check_ai_guidance_folders()
            assert has_ai
            assert not has_claude
            assert get_ai_guidance_path() == AI_FOLDER
            
            # Test butterbot integration
            with patch('os.path.isfile', return_value=True):
                assert has_butterbot_integration()
                
                # Test getting context
                with patch('os.path.join', return_value=str(ai_path)):
                    with patch('open', create=True) as mock_open:
                        mock_file = MagicMock()
                        mock_file.read.return_value = "Test context"
                        mock_open.return_value.__enter__.return_value = mock_file
                        
                        context = get_butterbot_context("patterns")
                        assert context == "Test context"


# --- Test Command Integration ---

def test_update_command(runner, temp_dir, setup_tasks_file, sample_tasks, sample_config):
    """Test the update command."""
    # Setup config
    Path(CONFIG_FILE).write_text(json.dumps(sample_config))
    
    mock_response = json.dumps([
        {
            "id": "2",
            "title": "Updated task 2",
            "description": "Updated description",
            "dependencies": ["1"],
            "details": "Updated details",
            "test_strategy": "Updated test strategy"
        },
        {
            "id": "3",
            "title": "Updated task 3",
            "description": "Updated description",
            "dependencies": ["2"],
            "details": "Updated details",
            "test_strategy": "Updated test strategy"
        }
    ])
    
    with patch('focus_forge.call_claude_api', return_value=mock_response):
        result = runner.invoke(cli, ["update", "--from=2", "--prompt=Update tasks with new requirements"])
    
    assert result.exit_code == 0
    assert "Successfully updated" in result.output
    
    # Verify tasks were updated
    tasks = load_tasks()
    assert tasks[1]["title"] == "Updated task 2"
    assert tasks[2]["title"] == "Updated task 3"
    
    # Original task status and priority should be preserved
    assert tasks[1]["status"] == sample_tasks[1]["status"]
    assert tasks[1]["priority"] == sample_tasks[1]["priority"]


def test_parse_prd_with_butterbot(runner, temp_dir, sample_config):
    """Test parse-prd command with butterbot integration."""
    # Create PRD file
    prd_path = temp_dir / "prd.txt"
    prd_path.write_text("This is a test PRD")
    
    # Setup config
    Path(CONFIG_FILE).write_text(json.dumps(sample_config))
    
    # Mock butterbot integration
    with patch('focus_forge.has_butterbot_integration', return_value=True):
        with patch('focus_forge.get_butterbot_context', return_value="Test context"):
            with patch('focus_forge.call_claude_api') as mock_api:
                mock_api.return_value = json.dumps([
                    {
                        "id": "1",
                        "title": "Test task with butterbot",
                        "description": "Description with context",
                        "status": "pending",
                        "priority": "high",
                        "dependencies": [],
                        "details": "Details referencing context",
                        "test_strategy": "Test strategy"
                    }
                ])
                
                result = runner.invoke(cli, ["parse-prd", "--input=prd.txt"])
    
    assert result.exit_code == 0
    assert "Successfully generated" in result.output
    
    # Verify task was created with butterbot context influencing it
    tasks = load_tasks()
    assert len(tasks) == 1
    assert tasks[0]["title"] == "Test task with butterbot"


def test_fix_dependencies_command(runner, temp_dir, sample_tasks):
    """Test fix-dependencies command."""
    # Create tasks with invalid dependencies
    tasks = sample_tasks.copy()
    # Add non-existent dependency
    tasks[0]["dependencies"].append("999")
    # Add self-reference
    tasks[1]["dependencies"].append("2")
    
    # Save tasks
    with open(TASKS_FILE, 'w') as f:
        json.dump({"tasks": tasks}, f)
    
    # Run command
    result = runner.invoke(cli, ["fix-dependencies"])
    
    assert result.exit_code == 0
    assert "Fixed dependencies" in result.output
    
    # Verify dependencies were fixed
    fixed_tasks = load_tasks()
    assert "999" not in fixed_tasks[0]["dependencies"]
    assert "2" not in fixed_tasks[1]["dependencies"]


def test_expand_command_with_existing_subtasks(runner, temp_dir, setup_tasks_file, sample_config):
    """Test expand command with existing subtasks."""
    # Setup config
    Path(CONFIG_FILE).write_text(json.dumps(sample_config))
    
    # Mock user confirming overwrite
    with patch('click.confirm', return_value=True):
        with patch('focus_forge.expand_task') as mock_expand:
            mock_expand.return_value = [
                {
                    "id": "3.1",
                    "title": "New subtask 1",
                    "description": "New description 1",
                    "status": "pending"
                },
                {
                    "id": "3.2",
                    "title": "New subtask 2",
                    "description": "New description 2",
                    "status": "pending"
                }
            ]
            
            result = runner.invoke(cli, ["expand", "--id=3", "--num=2"])
    
    assert result.exit_code == 0
    assert "Successfully expanded" in result.output
    
    # Verify subtasks were updated
    tasks = load_tasks()
    task = find_task_by_id(tasks, "3")
    assert task is not None
    assert len(task["subtasks"]) == 2
    assert task["subtasks"][0]["title"] == "New subtask 1"
    assert task["subtasks"][1]["title"] == "New subtask 2"


def test_ai_guidance_status_command(runner):
    """Test ai-guidance-status command."""
    with patch('focus_forge.check_ai_guidance_folders', return_value=(True, True)):
        with patch('focus_forge.has_butterbot_integration', return_value=True):
            with patch('focus_forge.get_butterbot_context', return_value="Available"):
                result = runner.invoke(cli, ["ai-guidance-status"])
    
    assert result.exit_code == 0
    assert "AI Guidance Status" in result.output
    assert "Found" in result.output
    assert "Available" in result.output


# --- Test Edge Cases ---

def test_expand_task_empty_response(sample_tasks, sample_config):
    """Test expand_task with empty API response."""
    task = sample_tasks[0]
    
    with patch('focus_forge.call_claude_api', return_value=""):
        subtasks = expand_task(task, sample_config)
    
    assert isinstance(subtasks, list)
    assert len(subtasks) == 0


def test_expand_task_non_json_response(sample_tasks, sample_config):
    """Test expand_task with non-JSON API response."""
    task = sample_tasks[0]
    
    with patch('focus_forge.call_claude_api', return_value="Not JSON"):
        with patch('rich.console.Console.print'):
            subtasks = expand_task(task, sample_config)
    
    assert isinstance(subtasks, list)
    assert len(subtasks) == 0


def test_circular_dependency_detection(runner, temp_dir, sample_tasks):
    """Test detection of circular dependencies."""
    # Create tasks with circular dependencies
    tasks = sample_tasks.copy()
    tasks[0]["dependencies"] = ["3"]  # 1 depends on 3
    tasks[2]["dependencies"] = ["1"]  # 3 depends on 1 - creates a circle
    
    # Save tasks
    with open(TASKS_FILE, 'w') as f:
        json.dump({"tasks": tasks}, f)
    
    # Try to add another dependency that would create a more complex circle
    result = runner.invoke(cli, ["add-dependency", "--id=2", "--depends-on=1"])
    
    assert result.exit_code == 0
    assert "Added dependency" in result.output
    
    # This would create 1->3->1 circle
    result = runner.invoke(cli, ["add-dependency", "--id=1", "--depends-on=3"])
    
    assert result.exit_code == 0
    assert "circular dependency" in result.output


def test_set_status_nonexistent_task(runner, temp_dir, setup_tasks_file):
    """Test setting status of a non-existent task."""
    result = runner.invoke(cli, ["set-status", "--id=999", "--status=done"])
    
    assert result.exit_code == 0
    assert "not found" in result.output


def test_load_tasks_invalid_json(temp_dir):
    """Test loading tasks from an invalid JSON file."""
    # Create invalid JSON file
    with open(TASKS_FILE, 'w') as f:
        f.write("This is not valid JSON")
    
    with patch('rich.console.Console.print') as mock_print:
        tasks = load_tasks()
    
    assert isinstance(tasks, list)
    assert len(tasks) == 0
    mock_print.assert_called_once()
    assert "is not valid JSON" in str(mock_print.call_args[0][0])


# --- Performance Tests ---

@pytest.mark.performance
def test_find_task_by_id_performance(benchmark):
    """Test performance of find_task_by_id function."""
    # Create a large list of tasks
    tasks = [
        {
            "id": str(i),
            "title": f"Task {i}",
            "description": f"Description {i}",
            "status": "pending",
            "priority": "medium",
            "dependencies": [],
            "details": f"Details {i}",
            "test_strategy": None,
            "subtasks": [
                {
                    "id": f"{i}.{j}",
                    "title": f"Subtask {i}.{j}",
                    "description": f"Subtask description {i}.{j}",
                    "status": "pending"
                } for j in range(1, 6)  # 5 subtasks per task
            ]
        } for i in range(1, 101)  # 100 tasks
    ]
    
    # Benchmark finding the last task
    result = benchmark(find_task_by_id, tasks, "100")
    assert result is not None
    assert result["id"] == "100"
    
    # Benchmark finding the last subtask of the last task
    result = benchmark(find_task_by_id, tasks, "100.5")
    assert result is not None
    assert result["id"] == "100.5"


@pytest.mark.performance
def test_get_next_task_performance(benchmark):
    """Test performance of get_next_task function."""
    # Create a large list of tasks with dependencies
    tasks = []
    for i in range(1, 101):  # 100 tasks
        dependencies = [str(j) for j in range(max(1, i-5), i) if j % 3 == 0]
        status = "done" if i <= 50 else "pending"
        
        tasks.append({
            "id": str(i),
            "title": f"Task {i}",
            "description": f"Description {i}",
            "status": status,
            "priority": "medium" if i % 3 != 0 else "high" if i % 5 == 0 else "low",
            "dependencies": dependencies,
            "details": f"Details {i}",
            "test_strategy": None,
            "subtasks": []
        })
    
    # Benchmark finding the next task
    result = benchmark(get_next_task, tasks)
    assert result is not None  # Should find a task


# --- Security Tests ---

def test_load_config_file_path_traversal():
    """Test that config loading is safe from path traversal."""
    with patch('pathlib.Path.exists', return_value=True):
        with patch('builtins.open', side_effect=Exception("Simulated security error")):
            with patch('rich.console.Console.print') as mock_print:
                config = load_config()
    
    # Should fall back to default config
    assert isinstance(config, dict)
    assert "api_key" in config
    assert "model" in config


def test_call_claude_api_injection_prevention():
    """Test that API calls prevent injection attacks."""
    # Attempt to inject malicious content in prompt
    malicious_prompt = "Prompt with injection \"; rm -rf /; \""
    
    with patch('requests.post') as mock_post:
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"content": [{"text": "Safe response"}]}
        mock_post.return_value = mock_response
        
        call_claude_api(
            prompt=malicious_prompt,
            api_key="test-api-key",
            model="test-model",
            max_tokens=1000,
            temperature=0.5
        )
    
    # Check that request was properly formed and escaped
    args, kwargs = mock_post.call_args
    assert kwargs["json"]["messages"][0]["content"] == malicious_prompt
    # The injection should be sent as a string, not executed
    assert "\"; rm -rf /; \"" in kwargs["json"]["messages"][0]["content"]


# --- Integration of Multiple Commands ---

def test_task_workflow_integration(runner, temp_dir):
    """Test a full workflow of multiple commands working together."""
    # 1. Initialize project
    with patch('click.prompt', side_effect=["Test Project", "0.1.0", "test-api-key"]):
        result_init = runner.invoke(cli, ["init"])
    
    assert result_init.exit_code == 0
    assert "Project initialized successfully" in result_init.output
    
    # 2. Set task 1 to in-progress
    result_set = runner.invoke(cli, ["set-status", "--id=1", "--status=in-progress"])
    assert result_set.exit_code == 0
    
    # 3. Expand task 1
    with patch('focus_forge.call_claude_api') as mock_api:
        mock_api.return_value = json.dumps([
            {
                "id": "1.1",
                "title": "Subtask 1.1",
                "description": "Description 1.1",
                "status": "pending"
            },
            {
                "id": "1.2",
                "title": "Subtask 1.2",
                "description": "Description 1.2",
                "status": "pending"
            }
        ])
        
        result_expand = runner.invoke(cli, ["expand", "--id=1", "--num=2"])
    
    assert result_expand.exit_code == 0
    assert "Successfully expanded" in result_expand.output
    
    # 4. Complete subtask
    result_subtask = runner.invoke(cli, ["set-status", "--id=1.1", "--status=done"])
    assert result_subtask.exit_code == 0
    
    # 5. Complete main task
    result_complete = runner.invoke(cli, ["set-status", "--id=1", "--status=done"])
    assert result_complete.exit_code == 0
    
    # 6. Check that next task shows correctly
    result_next = runner.invoke(cli, ["next"])
    
    # Verify the whole workflow state at the end
    tasks = load_tasks()
    
    # Task 1 and all its subtasks should be marked as done
    task1 = find_task_by_id(tasks, "1")
    assert task1["status"] == "done"
    for subtask in task1["subtasks"]:
        assert subtask["status"] == "done"


# --- Schema Validation Tests ---

def test_task_schema_validation():
    """Test validation of task schema."""
    # Valid task
    valid_task = {
        "id": "1",
        "title": "Valid task",
        "description": "Valid description",
        "status": "pending",
        "priority": "medium",
        "dependencies": [],
        "details": "Valid details",
        "test_strategy": "Valid test strategy",
        "subtasks": []
    }
    
    # Generate tasks with different invalid fields
    invalid_tasks = [
        # Missing required field
        {k: v for k, v in valid_task.items() if k != "id"},
        # Invalid status
        {**valid_task, "status": "invalid-status"},
        # Invalid priority
        {**valid_task, "priority": "invalid-priority"},
        # Dependencies is not a list
        {**valid_task, "dependencies": "not-a-list"},
        # Subtasks is not a list
        {**valid_task, "subtasks": "not-a-list"}
    ]
    
    # Attempt to save invalid tasks
    for i, invalid_task in enumerate(invalid_tasks):
        with patch('json.dump') as mock_dump:
            with patch('builtins.open', create=True):
                # This doesn't raise exceptions, but should log errors
                save_tasks([invalid_task]) 