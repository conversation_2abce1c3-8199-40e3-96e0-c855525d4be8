#!/usr/bin/env python3
"""
Test suite for sequence constraints handling in focus_forge.

This test suite verifies the correct handling of various constraint types and
edge cases in the sequence analysis algorithm implementation (Task T4).
"""
import os
import sys
import unittest
from typing import List, Dict, Any, Optional, Set
import random

# Ensure focus_forge module is in the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import sequence analysis functionality
from focus_forge.sequence_analysis import (
    SequenceAnalyzer,
    SequenceConstraint,
    SequenceConstraintType,
    get_optimized_sequence,
    create_sequence_analyzer_from_tasks
)
from focus_forge.dependency_graph import DependencyGraph, Dependency, DependencyType


def complex_constraints_tasks() -> List[Dict[str, Any]]:
    """Create a complex task structure with multiple interacting constraints."""
    # Create a task hierarchy with 3 layers of dependencies
    # Layer 1: Foundation/infrastructure components (IDs A1-A4)
    # Layer 2: Middle-tier components (IDs B1-B6)
    # Layer 3: User-facing components (IDs C1-C4)
    tasks = []
    
    # Layer 1 - Foundation tasks
    for i in range(1, 5):
        tasks.append({
            "id": f"A{i}",
            "title": f"Foundation Task A{i}",
            "description": f"Foundation component A{i}",
            "status": "pending",
            "priority": "high" if i <= 2 else "medium",
            "dependencies": [],
            "details": f"Foundation task A{i} details",
            "metadata": {
                "component_type": "infrastructure",
                "complexity": "high" if i == 1 else "medium",
                "team": "backend" if i <= 2 else "data"
            }
        })
    
    # Layer 2 - Middle-tier tasks with dependencies on layer 1
    for i in range(1, 7):
        # Define dependencies based on task number
        deps = []
        if i <= 3:  # B1-B3 depend on A1 and A2
            deps = ["A1", "A2"]
        else:       # B4-B6 depend on A3 and A4
            deps = ["A3", "A4"]
        
        # Assign team based on task number
        team = "backend" if i <= 2 else ("frontend" if i <= 4 else "data")
        
        tasks.append({
            "id": f"B{i}",
            "title": f"Middle-tier Task B{i}",
            "description": f"Middle component B{i}",
            "status": "pending",
            "priority": "medium",
            "dependencies": deps,
            "details": f"Middle-tier task B{i} details",
            "metadata": {
                "component_type": "middleware",
                "complexity": "medium",
                "team": team
            }
        })
    
    # Layer 3 - User-facing tasks with dependencies on layer 2
    for i in range(1, 5):
        # Define dependencies based on task number
        if i == 1:      # C1 depends on B1 and B3
            deps = ["B1", "B3"]
        elif i == 2:    # C2 depends on B2 and B4
            deps = ["B2", "B4"]
        elif i == 3:    # C3 depends on B3, B5, and B6
            deps = ["B3", "B5", "B6"]
        else:           # C4 depends on B2, B4, and B5
            deps = ["B2", "B4", "B5"]
        
        tasks.append({
            "id": f"C{i}",
            "title": f"User-facing Task C{i}",
            "description": f"UI component C{i}",
            "status": "pending",
            "priority": "low" if i <= 2 else "medium",
            "dependencies": deps,
            "details": f"User-facing task C{i} details",
            "metadata": {
                "component_type": "frontend",
                "complexity": "medium" if i <= 2 else "high",
                "team": "frontend"
            }
        })
    
    return tasks


def conflicting_constraints_tasks() -> List[Dict[str, Any]]:
    """Create tasks with potentially conflicting constraints."""
    return [
        {
            "id": "T1",
            "title": "Foundation Task",
            "priority": "high",
            "dependencies": [],
            "metadata": {"complexity": "high", "team": "backend"}
        },
        {
            "id": "T2", 
            "title": "Backend Task",
            "priority": "medium",
            "dependencies": ["T1"],
            "metadata": {"complexity": "medium", "team": "backend"}
        },
        {
            "id": "T3",
            "title": "Database Task",
            "priority": "high",  # High priority but should still come after T2
            "dependencies": ["T2"],
            "metadata": {"complexity": "high", "team": "data"}
        },
        {
            "id": "T4",
            "title": "Frontend Task",
            "priority": "medium",
            "dependencies": ["T1"],
            "metadata": {"complexity": "low", "team": "frontend"}
        },
        {
            "id": "T5",
            "title": "API Integration",
            "priority": "medium",
            "dependencies": ["T2", "T4"],
            "metadata": {"complexity": "medium", "team": "backend"}
        }
    ]


def large_task_set() -> List[Dict[str, Any]]:
    """Create a large set of tasks to test algorithm performance and scalability."""
    tasks = []
    
    # Create 50 tasks with various dependencies
    for i in range(1, 51):
        task_id = f"TASK_{i:03d}"
        
        # Set random seed to ensure reproducible tests
        random.seed(i)
        
        # Create dependencies to previous tasks (up to 3)
        dependencies = []
        if i > 1:
            # Find potential dependencies (only depend on previous tasks)
            potential_deps = list(range(1, i))
            random.shuffle(potential_deps)
            # Select 1-3 dependencies (or fewer if not enough previous tasks)
            num_deps = min(random.randint(1, 3), len(potential_deps))
            for j in range(num_deps):
                dependencies.append(f"TASK_{potential_deps[j]:03d}")
        
        # Assign a random priority
        priority = random.choice(["high", "medium", "low"])
        
        # Assign a random team
        team = random.choice(["backend", "frontend", "data", "qa", "devops"])
        
        # Assign a random complexity
        complexity = random.choice(["high", "medium", "low"])
        
        tasks.append({
            "id": task_id,
            "title": f"Task {i}",
            "description": f"Description for task {i}",
            "status": "pending",
            "priority": priority,
            "dependencies": dependencies,
            "details": f"Details for task {i}",
            "metadata": {
                "team": team,
                "complexity": complexity,
                "estimated_hours": random.randint(4, 40)
            }
        })
    
    return tasks


class TestComplexConstraintScenarios(unittest.TestCase):
    """Test complex scenarios with multiple interacting constraints."""
    
    def test_team_based_grouping(self):
        """Test that tasks are grouped by team when possible."""
        analyzer = create_sequence_analyzer_from_tasks(complex_constraints_tasks())
        
        # Add team assignment constraints for each team
        team_constraints = []
        
        # Backend team constraint
        backend_tasks = [t["id"] for t in complex_constraints_tasks() 
                        if t.get("metadata", {}).get("team") == "backend"]
        team_constraints.append(SequenceConstraint(
            constraint_type=SequenceConstraintType.TEAM_ASSIGNMENT,
            task_ids=backend_tasks,
            description="Backend team tasks",
            weight=1.0
        ))
        
        # Frontend team constraint
        frontend_tasks = [t["id"] for t in complex_constraints_tasks() 
                        if t.get("metadata", {}).get("team") == "frontend"]
        team_constraints.append(SequenceConstraint(
            constraint_type=SequenceConstraintType.TEAM_ASSIGNMENT,
            task_ids=frontend_tasks,
            description="Frontend team tasks",
            weight=1.0
        ))
        
        # Data team constraint
        data_tasks = [t["id"] for t in complex_constraints_tasks() 
                     if t.get("metadata", {}).get("team") == "data"]
        team_constraints.append(SequenceConstraint(
            constraint_type=SequenceConstraintType.TEAM_ASSIGNMENT,
            task_ids=data_tasks,
            description="Data team tasks",
            weight=1.0
        ))
        
        # Add all constraints
        for constraint in team_constraints:
            analyzer.add_constraint(constraint)
        
        # Generate the sequence
        sequence = analyzer.get_implementation_sequence()
        task_ids = [task["id"] for task in sequence]
        
        # Helper function to calculate team transitions
        def count_team_transitions(task_ids):
            transitions = 0
            current_team = None
            
            for task_id in task_ids:
                # Find the task
                task = next(t for t in complex_constraints_tasks() if t["id"] == task_id)
                team = task.get("metadata", {}).get("team")
                
                if current_team is not None and team != current_team:
                    transitions += 1
                    
                current_team = team
                
            return transitions
        
        # Count team transitions in the sequence
        transitions = count_team_transitions(task_ids)
        
        # Generate a reference sequence without team constraints
        reference_analyzer = create_sequence_analyzer_from_tasks(complex_constraints_tasks())
        reference_sequence = reference_analyzer.get_implementation_sequence()
        reference_task_ids = [task["id"] for task in reference_sequence]
        reference_transitions = count_team_transitions(reference_task_ids)
        
        # The sequence with team constraints should have fewer team transitions
        self.assertLessEqual(transitions, reference_transitions)
    
    def test_balancing_multiple_constraint_types(self):
        """Test that multiple constraint types are balanced appropriately."""
        analyzer = create_sequence_analyzer_from_tasks(complex_constraints_tasks())
        
        # Add multiple constraint types with different weights
        
        # 1. Foundation first constraint (highest weight)
        foundation_tasks = ["A1", "A2", "A3", "A4"]
        analyzer.add_constraint(SequenceConstraint(
            constraint_type=SequenceConstraintType.FOUNDATION_FIRST,
            task_ids=foundation_tasks,
            description="Foundation tasks",
            weight=3.0  # Highest weight
        ))
        
        # 2. Complexity-based constraint (medium weight)
        complex_tasks = [t["id"] for t in complex_constraints_tasks() 
                        if t.get("metadata", {}).get("complexity") == "high"]
        analyzer.add_constraint(SequenceConstraint(
            constraint_type=SequenceConstraintType.COMPLEXITY_BASED,
            task_ids=complex_tasks,
            description="Complex tasks",
            weight=2.0  # Medium weight
        ))
        
        # 3. Team assignment constraint (lowest weight)
        backend_tasks = [t["id"] for t in complex_constraints_tasks() 
                        if t.get("metadata", {}).get("team") == "backend"]
        analyzer.add_constraint(SequenceConstraint(
            constraint_type=SequenceConstraintType.TEAM_ASSIGNMENT,
            task_ids=backend_tasks,
            description="Backend team tasks",
            weight=1.0  # Lowest weight
        ))
        
        # Generate the sequence
        sequence = analyzer.get_implementation_sequence()
        task_ids = [task["id"] for task in sequence]
        
        # Foundation tasks should be prioritized first
        for task_id in foundation_tasks:
            # Check that foundation tasks are in the first few positions
            self.assertLess(task_ids.index(task_id), len(foundation_tasks) * 2)
        
        # Complex, high-priority tasks should be prioritized within their dependency level
        # For example, A1 (complex foundation task) should come before other foundation tasks
        if "A1" in complex_tasks:
            foundation_indices = [task_ids.index(t) for t in foundation_tasks]
            self.assertEqual(task_ids.index("A1"), min(foundation_indices))
    
    def test_conflicting_constraints_resolution(self):
        """Test resolution of conflicting constraints based on weights."""
        analyzer = create_sequence_analyzer_from_tasks(conflicting_constraints_tasks())
        
        # Add potentially conflicting constraints
        
        # 1. Technical prerequisite: Backend before frontend
        tech_constraint = SequenceConstraint(
            constraint_type=SequenceConstraintType.TECHNICAL_PREREQUISITE,
            task_ids=["T2", "T4"],  # T2 (backend) should come before T4 (frontend)
            description="Backend before frontend",
            weight=2.0  # Higher weight
        )
        analyzer.add_constraint(tech_constraint)
        
        # 2. Team assignment constraint that could conflict with technical prerequisite
        team_constraint = SequenceConstraint(
            constraint_type=SequenceConstraintType.TEAM_ASSIGNMENT,
            task_ids=["T2", "T5"],  # Group backend tasks
            description="Group backend tasks",
            weight=1.0  # Lower weight
        )
        analyzer.add_constraint(team_constraint)
        
        # Generate the sequence
        sequence = analyzer.get_implementation_sequence()
        task_ids = [task["id"] for task in sequence]
        
        # The technical prerequisite should be respected (higher weight)
        # T2 should come before T4
        self.assertLess(task_ids.index("T2"), task_ids.index("T4"))
    
    def test_large_task_set_performance(self):
        """Test algorithm performance and correctness with a large set of tasks."""
        # Set the random seed for reproducibility
        random.seed(42)
        
        # Create the task set
        tasks = large_task_set()
        analyzer = create_sequence_analyzer_from_tasks(tasks)
        
        # Add random constraints of different types
        
        # 1. Add foundation constraint for first 5 tasks
        foundation_tasks = [t["id"] for t in tasks[:5]]
        analyzer.add_constraint(SequenceConstraint(
            constraint_type=SequenceConstraintType.FOUNDATION_FIRST,
            task_ids=foundation_tasks,
            description="Foundation tasks",
            weight=2.0
        ))
        
        # 2. Add team-based grouping for a random team
        random.seed(42)  # Keep consistent random choices
        random_team = random.choice(["backend", "frontend", "data"])
        team_tasks = [t["id"] for t in tasks 
                     if t.get("metadata", {}).get("team") == random_team]
        if team_tasks:
            analyzer.add_constraint(SequenceConstraint(
                constraint_type=SequenceConstraintType.TEAM_ASSIGNMENT,
                task_ids=team_tasks,
                description=f"{random_team} team tasks",
                weight=1.0
            ))
        
        # Generate the sequence
        sequence = analyzer.get_implementation_sequence()
        task_ids = [task["id"] for task in sequence]
        
        # Verify all tasks are in the sequence
        self.assertEqual(len(sequence), len(tasks))
        self.assertEqual(set(task_ids), set(t["id"] for t in tasks))
        
        # Verify dependencies are respected
        for task in tasks:
            task_index = task_ids.index(task["id"])
            for dep_id in task.get("dependencies", []):
                dep_index = task_ids.index(dep_id)
                self.assertLess(dep_index, task_index, 
                             f"Dependency violation: {dep_id} should come before {task['id']}")


class TestConstraintEdgeCases(unittest.TestCase):
    """Test edge cases in constraint handling."""
    
    def test_constraints_on_nonexistent_tasks(self):
        """Test behavior when constraints reference nonexistent tasks."""
        tasks = complex_constraints_tasks()
        analyzer = create_sequence_analyzer_from_tasks(tasks)
        
        # Add constraint with nonexistent task IDs
        invalid_constraint = SequenceConstraint(
            constraint_type=SequenceConstraintType.FOUNDATION_FIRST,
            task_ids=["A1", "NONEXISTENT1", "A2", "NONEXISTENT2"],
            description="Constraint with invalid tasks",
            weight=1.0
        )
        analyzer.add_constraint(invalid_constraint)
        
        # Generate sequence - should ignore nonexistent tasks but use valid ones
        sequence = analyzer.get_implementation_sequence()
        task_ids = [task["id"] for task in sequence]
        
        # A1 and A2 should still be early in the sequence
        self.assertLess(task_ids.index("A1"), len(tasks) // 2)
        self.assertLess(task_ids.index("A2"), len(tasks) // 2)
    
    def test_empty_constraints(self):
        """Test behavior with empty constraints."""
        tasks = complex_constraints_tasks()
        analyzer = create_sequence_analyzer_from_tasks(tasks)
        
        # Add constraint with empty task IDs
        empty_constraint = SequenceConstraint(
            constraint_type=SequenceConstraintType.FOUNDATION_FIRST,
            task_ids=[],
            description="Empty constraint",
            weight=1.0
        )
        analyzer.add_constraint(empty_constraint)
        
        # Generate sequence - should be the same as without constraints
        sequence = analyzer.get_implementation_sequence()
        
        # Create a reference analyzer without the empty constraint
        reference_analyzer = create_sequence_analyzer_from_tasks(tasks)
        reference_sequence = reference_analyzer.get_implementation_sequence()
        
        # Sequences should be identical
        self.assertEqual([task["id"] for task in sequence], 
                       [task["id"] for task in reference_sequence])
    
    def test_contradictory_technical_prerequisites(self):
        """Test behavior with contradictory technical prerequisites."""
        tasks = complex_constraints_tasks()
        analyzer = create_sequence_analyzer_from_tasks(tasks)
        
        # Add two contradictory technical prerequisite constraints
        # 1. A1 -> B1 -> C1 order
        analyzer.add_constraint(SequenceConstraint(
            constraint_type=SequenceConstraintType.TECHNICAL_PREREQUISITE,
            task_ids=["A1", "B1", "C1"],
            description="A1 before B1 before C1",
            weight=1.0
        ))
        
        # 2. C1 -> B1 -> A1 order (contradicts the first one)
        analyzer.add_constraint(SequenceConstraint(
            constraint_type=SequenceConstraintType.TECHNICAL_PREREQUISITE,
            task_ids=["C1", "B1", "A1"],
            description="C1 before B1 before A1",
            weight=1.0
        ))
        
        # Generate sequence - dependency constraints should override technical prerequisites
        sequence = analyzer.get_implementation_sequence()
        task_ids = [task["id"] for task in sequence]
        
        # A1 has no dependencies, so it should come before B1
        self.assertLess(task_ids.index("A1"), task_ids.index("B1"))
        
        # B1 is a dependency of C1, so it should come before C1
        self.assertLess(task_ids.index("B1"), task_ids.index("C1"))
    
    def test_constraint_with_different_weights(self):
        """Test that constraints with higher weights take precedence."""
        tasks = complex_constraints_tasks()
        analyzer = create_sequence_analyzer_from_tasks(tasks)
        
        # Add two competing constraints with different weights
        # 1. Low-weight constraint: Group frontend tasks
        frontend_tasks = [t["id"] for t in tasks 
                         if t.get("metadata", {}).get("team") == "frontend"]
        analyzer.add_constraint(SequenceConstraint(
            constraint_type=SequenceConstraintType.TEAM_ASSIGNMENT,
            task_ids=frontend_tasks,
            description="Group frontend tasks",
            weight=1.0  # Low weight
        ))
        
        # 2. High-weight constraint: Prioritize complex tasks
        complex_tasks = [t["id"] for t in tasks 
                        if t.get("metadata", {}).get("complexity") == "high"]
        analyzer.add_constraint(SequenceConstraint(
            constraint_type=SequenceConstraintType.COMPLEXITY_BASED,
            task_ids=complex_tasks,
            description="Prioritize complex tasks",
            weight=3.0  # High weight
        ))
        
        # Generate sequence
        sequence = analyzer.get_implementation_sequence()
        task_ids = [task["id"] for task in sequence]
        
        # Verify that at least one complex task is prioritized
        # We should see it in the first third of the sequence if possible
        complex_positions = [task_ids.index(t_id) for t_id in complex_tasks if t_id in task_ids]
        if complex_positions:
            # At least one complex task should be in the first half of the sequence
            # if dependency constraints allow
            self.assertTrue(any(pos < len(task_ids) // 2 for pos in complex_positions),
                         "At least one complex task should be in the first half of the sequence")
            
            # Verify complex tasks aren't all grouped at the end
            self.assertLess(min(complex_positions), len(task_ids) - len(complex_positions),
                         "Complex tasks should not all be at the end of the sequence")


class TestUserDefinedConstraints(unittest.TestCase):
    """Test user-defined custom constraints."""
    
    def test_user_defined_sequence_constraints(self):
        """Test user-defined task sequence constraints."""
        tasks = complex_constraints_tasks()
        analyzer = create_sequence_analyzer_from_tasks(tasks)
        
        # Define a specific sequence for certain tasks
        sequence_constraint = SequenceConstraint(
            constraint_type=SequenceConstraintType.USER_DEFINED,
            task_ids=["A2", "B2", "B4", "C2"],  # Custom sequence order
            description="User-defined task sequence",
            weight=5.0  # Very high weight
        )
        analyzer.add_constraint(sequence_constraint)
        
        # Generate sequence
        sequence = analyzer.get_implementation_sequence()
        task_ids = [task["id"] for task in sequence]
        
        # Check that the user-defined ordering is respected where possible
        # This depends on existing dependencies not contradicting the order
        a2_pos = task_ids.index("A2")
        b2_pos = task_ids.index("B2")
        b4_pos = task_ids.index("B4")
        c2_pos = task_ids.index("C2")
        
        # A2 should come before B2, B4, and C2
        self.assertLess(a2_pos, b2_pos)
        self.assertLess(a2_pos, b4_pos)
        self.assertLess(a2_pos, c2_pos)
        
        # B2 should come before C2
        self.assertLess(b2_pos, c2_pos)
        
        # B4 should come before C2
        self.assertLess(b4_pos, c2_pos)


class TestSequenceStabilityAndReproducibility(unittest.TestCase):
    """Test stability and reproducibility of sequence generation."""
    
    def test_sequence_stability(self):
        """Test that the sequence generation is stable across multiple runs."""
        # Generate sequence multiple times and verify they're identical
        sequences = []
        
        for _ in range(5):
            tasks = complex_constraints_tasks()
            analyzer = create_sequence_analyzer_from_tasks(tasks)
            sequence = analyzer.get_implementation_sequence()
            sequences.append([task["id"] for task in sequence])
        
        # All sequences should be identical
        for i in range(1, len(sequences)):
            self.assertEqual(sequences[0], sequences[i])
    
    def test_incremental_constraint_addition(self):
        """Test effect of incrementally adding constraints."""
        # Start with no constraints
        tasks = complex_constraints_tasks()
        analyzer = create_sequence_analyzer_from_tasks(tasks)
        base_sequence = analyzer.get_implementation_sequence()
        base_task_ids = [task["id"] for task in base_sequence]
        
        # Add a foundation constraint
        foundation_constraint = SequenceConstraint(
            constraint_type=SequenceConstraintType.FOUNDATION_FIRST,
            task_ids=["A1", "A2"],
            description="Foundation tasks",
            weight=2.0
        )
        analyzer.add_constraint(foundation_constraint)
        
        # Generate new sequence with one constraint
        sequence_1 = analyzer.get_implementation_sequence()
        task_ids_1 = [task["id"] for task in sequence_1]
        
        # A1 and A2 should be earlier in sequence_1 compared to base_sequence
        self.assertLessEqual(task_ids_1.index("A1"), base_task_ids.index("A1"))
        self.assertLessEqual(task_ids_1.index("A2"), base_task_ids.index("A2"))
        
        # Add a team constraint
        team_constraint = SequenceConstraint(
            constraint_type=SequenceConstraintType.TEAM_ASSIGNMENT,
            task_ids=["B1", "B2"],
            description="Team tasks",
            weight=1.0
        )
        analyzer.add_constraint(team_constraint)
        
        # Generate new sequence with two constraints
        sequence_2 = analyzer.get_implementation_sequence()
        task_ids_2 = [task["id"] for task in sequence_2]
        
        # B1 and B2 should be adjacent or closer together in sequence_2
        b1_pos_1 = task_ids_1.index("B1")
        b2_pos_1 = task_ids_1.index("B2")
        b1_pos_2 = task_ids_2.index("B1")
        b2_pos_2 = task_ids_2.index("B2")
        
        # The distance between B1 and B2 should be smaller or equal in sequence_2
        self.assertLessEqual(abs(b1_pos_2 - b2_pos_2), abs(b1_pos_1 - b2_pos_1))


def run_tests():
    """Run the test suite."""
    print("Running sequence constraints tests for T04")
    test_suite = unittest.TestSuite()
    test_suite.addTest(unittest.makeSuite(TestComplexConstraintScenarios))
    test_suite.addTest(unittest.makeSuite(TestConstraintEdgeCases))
    test_suite.addTest(unittest.makeSuite(TestUserDefinedConstraints))
    test_suite.addTest(unittest.makeSuite(TestSequenceStabilityAndReproducibility))
    
    runner = unittest.TextTestRunner(verbosity=2)
    runner.run(test_suite)


if __name__ == "__main__":
    run_tests() 