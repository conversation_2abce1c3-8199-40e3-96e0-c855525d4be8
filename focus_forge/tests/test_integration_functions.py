#!/usr/bin/env python3
"""
Tests for the integration functions in task_management_integration.py.
"""
import os
import sys
import json
import unittest
import tempfile
from unittest.mock import patch, MagicMock
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Try to import the integration functions
try:
    from focus_forge.task_management_integration import (
        parse_prd_to_tasks,
        integrate_prd_with_existing,
        validate_tasks_with_prd,
        TaskManagementAdapter
    )
    HAS_FUNCTIONS = True
except ImportError:
    print("WARNING: Could not import integration functions. Tests will be skipped.")
    HAS_FUNCTIONS = False
    # Create placeholders for type checking
    def parse_prd_to_tasks(*args, **kwargs): pass
    def integrate_prd_with_existing(*args, **kwargs): pass
    def validate_tasks_with_prd(*args, **kwargs): pass
    class TaskManagementAdapter: pass

# Sample PRD text for testing
SAMPLE_PRD = """# Sample PRD for Testing

## Overview
This is a test PRD for the integration system.

## Features
1. Feature One: This is the first feature
2. Feature Two: This depends on Feature One
3. Feature Three: This is independent

## Technical Requirements
- Must be implemented in Python
- Should have error handling
- Database integration is required
"""

# Sample tasks for testing
SAMPLE_TASKS = [
    {
        "id": "T1",
        "title": "Implement Feature One",
        "description": "Create the first feature mentioned in the PRD",
        "status": "pending",
        "priority": "high",
        "dependencies": [],
        "details": "Feature One implementation details",
        "test_strategy": "Test Feature One thoroughly",
        "subtasks": []
    },
    {
        "id": "T2",
        "title": "Implement Feature Two",
        "description": "Create the second feature that depends on Feature One",
        "status": "pending",
        "priority": "medium",
        "dependencies": ["T1"],
        "details": "Feature Two implementation details",
        "test_strategy": "Test Feature Two with Feature One",
        "subtasks": []
    }
]


@unittest.skipIf(not HAS_FUNCTIONS, "Integration functions not available")
class TestIntegrationFunctions(unittest.TestCase):
    """Test cases for integration functions."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create temporary PRD file
        self.prd_file = tempfile.NamedTemporaryFile(delete=False, suffix=".md")
        with open(self.prd_file.name, 'w') as f:
            f.write(SAMPLE_PRD)
        
        # Create temporary tasks file
        self.tasks_file = tempfile.NamedTemporaryFile(delete=False, suffix=".json")
        with open(self.tasks_file.name, 'w') as f:
            json.dump({"tasks": SAMPLE_TASKS}, f)
        
        # Create temporary output file
        self.output_file = tempfile.NamedTemporaryFile(delete=False, suffix=".json")
        self.output_file.close()
    
    def tearDown(self):
        """Clean up temporary files."""
        try:
            os.unlink(self.prd_file.name)
            os.unlink(self.tasks_file.name)
            os.unlink(self.output_file.name)
        except Exception:
            pass
    
    @patch('focus_forge.task_management_integration.TaskManagementAdapter')
    def test_parse_prd_to_tasks(self, mock_adapter):
        """Test the parse_prd_to_tasks function."""
        # Mock the adapter instance
        mock_adapter_instance = mock_adapter.return_value
        mock_adapter_instance.parse_prd_file.return_value = (SAMPLE_TASKS, {"tasks_valid": True})
        
        # Call the function
        tasks = parse_prd_to_tasks(self.prd_file.name)
        
        # Verify adapter was called correctly
        mock_adapter.assert_called_once()
        mock_adapter_instance.parse_prd_file.assert_called_once_with(self.prd_file.name)
        
        # Verify returned tasks
        self.assertEqual(tasks, SAMPLE_TASKS)
        
        # Test error handling
        mock_adapter_instance.parse_prd_file.side_effect = ValueError("Test error")
        with self.assertRaises(ValueError):
            parse_prd_to_tasks(self.prd_file.name)
    
    @patch('focus_forge.task_management_integration.TaskManagementAdapter')
    def test_integrate_prd_with_existing(self, mock_adapter):
        """Test the integrate_prd_with_existing function."""
        # Mock the adapter instance
        mock_adapter_instance = mock_adapter.return_value
        mock_adapter_instance.task_generator.generate_tasks_from_prd.return_value = [
            {"id": "T1", "title": "Task 1", "description": "Description 1", "status": "pending", "priority": "medium", "dependencies": []}
        ]
        mock_adapter_instance.merge_with_existing.return_value = [
            {"id": "T1", "title": "Task 1", "description": "Description 1", "status": "pending", "priority": "medium", "dependencies": []},
            {"id": "T2", "title": "Task 2", "description": "Description 2", "status": "pending", "priority": "medium", "dependencies": ["T1"]}
        ]
        mock_adapter_instance.validate_tasks_with_prd.return_value = {
            "tasks_valid": True,
            "confidence_score": 0.85
        }
        
        # Call the function
        report = integrate_prd_with_existing(self.prd_file.name, self.tasks_file.name, self.output_file.name)
        
        # Verify the report
        self.assertEqual(report["status"], "success")
        self.assertIn("new_tasks", report)
        self.assertIn("existing_tasks", report)
        self.assertIn("merged_tasks", report)
        
        # Verify the output file was created
        self.assertTrue(os.path.exists(self.output_file.name))
        
        # Test error handling for file not found
        with patch('os.path.exists', return_value=False):
            report = integrate_prd_with_existing(self.prd_file.name, self.tasks_file.name, self.output_file.name)
            self.assertEqual(report["status"], "error")
        
        # Reset the mock
        mock_adapter_instance.validate_tasks_with_prd.side_effect = None
        
        # Test error handling for file open
        with patch('builtins.open', side_effect=IOError("File error")):
            report = integrate_prd_with_existing(self.prd_file.name, self.tasks_file.name, self.output_file.name)
            self.assertEqual(report["status"], "error")
    
    @patch('focus_forge.task_management_integration.TaskManagementAdapter')
    def test_validate_tasks_with_prd(self, mock_adapter):
        """Test the validate_tasks_with_prd function."""
        # Mock the adapter instance
        mock_adapter_instance = mock_adapter.return_value
        validation_report = {
            "tasks_valid": True,
            "confidence_score": 0.85,
            "clarity_score": 0.9,
            "coverage_score": 0.8,
            "ambiguous_requirements": {"count": 0, "items": []},
            "missing_requirements": {"count": 0, "items": []}
        }
        mock_adapter_instance.validate_tasks_with_prd.return_value = validation_report
        
        # Call the function
        report = validate_tasks_with_prd(self.tasks_file.name, self.prd_file.name)
        
        # Verify adapter was called correctly
        mock_adapter.assert_called_once()
        self.assertTrue(mock_adapter_instance.validate_tasks_with_prd.called)
        
        # Verify returned report contains expected values
        self.assertEqual(report["status"], "success")
        self.assertTrue(report["tasks_valid"])
        
        # Test error handling for file not found
        with patch('os.path.exists', return_value=False):
            report = validate_tasks_with_prd(self.tasks_file.name, self.prd_file.name)
            self.assertEqual(report["status"], "error")
            self.assertFalse(report["tasks_valid"])
        
        # Test error handling for file open
        with patch('builtins.open', side_effect=IOError("File error")):
            report = validate_tasks_with_prd(self.tasks_file.name, self.prd_file.name)
            self.assertEqual(report["status"], "error")
            self.assertFalse(report["tasks_valid"])
        
        # Test error handling for validate_tasks_with_prd
        mock_adapter_instance.validate_tasks_with_prd.side_effect = Exception("Validation error")
        report = validate_tasks_with_prd(self.tasks_file.name, self.prd_file.name)
        self.assertEqual(report["status"], "error")
        self.assertFalse(report["tasks_valid"])


if __name__ == "__main__":
    unittest.main() 