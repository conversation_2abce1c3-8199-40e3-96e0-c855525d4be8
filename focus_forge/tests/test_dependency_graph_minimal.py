"""
Minimal tests for the dependency graph implementation.
"""
import pytest
from enum import Enum
from dataclasses import dataclass
from typing import Optional, Dict


class DependencyType(Enum):
    """Types of dependencies that can exist between tasks."""
    REQUIRES = "requires"
    DEPENDS_ON = "depends_on"
    ENABLES = "enables"
    PREREQUISITE = "prerequisite"
    BLOCKS = "blocks"
    OPTIONAL = "optional"


@dataclass
class Dependency:
    """Represents a dependency between two tasks."""
    source_id: str
    target_id: str
    dep_type: DependencyType
    description: Optional[str] = None
    metadata: Optional[Dict] = None


class DependencyGraph:
    """A graph data structure for managing task dependencies."""
    
    def __init__(self):
        """Initialize an empty dependency graph."""
        self.nodes = set()
        self.edges = {}
        self.reverse_edges = {}
    
    def add_node(self, node_id: str) -> None:
        """Add a node to the graph."""
        self.nodes.add(node_id)
        if node_id not in self.edges:
            self.edges[node_id] = []
        if node_id not in self.reverse_edges:
            self.reverse_edges[node_id] = []
    
    def add_dependency(self, dep: Dependency) -> bool:
        """Add a dependency to the graph."""
        # Add nodes if they don't exist
        self.add_node(dep.source_id)
        self.add_node(dep.target_id)
        
        # Add the dependency
        self.edges[dep.source_id].append(dep)
        self.reverse_edges[dep.target_id].append(dep)
        
        return True


def test_empty_graph():
    """Test initialization of an empty graph."""
    graph = DependencyGraph()
    assert len(graph.nodes) == 0
    assert len(graph.edges) == 0
    assert len(graph.reverse_edges) == 0


def test_add_node():
    """Test adding nodes to the graph."""
    graph = DependencyGraph()
    
    # Add a single node
    graph.add_node("T1")
    assert "T1" in graph.nodes
    assert "T1" in graph.edges
    assert "T1" in graph.reverse_edges
    assert len(graph.edges["T1"]) == 0
    assert len(graph.reverse_edges["T1"]) == 0
    
    # Add another node
    graph.add_node("T2")
    assert "T2" in graph.nodes
    assert "T2" in graph.edges
    assert "T2" in graph.reverse_edges


def test_add_dependency():
    """Test adding dependencies to the graph."""
    graph = DependencyGraph()
    
    # Create a dependency
    dep = Dependency(
        source_id="T1",
        target_id="T2",
        dep_type=DependencyType.DEPENDS_ON,
        description="T1 depends on T2"
    )
    
    # Add the dependency
    success = graph.add_dependency(dep)
    assert success is True
    
    # Verify the dependency was added
    assert "T1" in graph.nodes
    assert "T2" in graph.nodes
    assert len(graph.edges["T1"]) == 1
    assert len(graph.reverse_edges["T2"]) == 1
    assert graph.edges["T1"][0] == dep
    assert graph.reverse_edges["T2"][0] == dep