"""Pytest configuration for Focus Forge tests."""

import json
import os
import tempfile
from pathlib import Path
from typing import Dict, List, Any, Generator

import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>


@pytest.fixture
def runner() -> C<PERSON>Runner:
    """Return a Click CLI test runner."""
    return CliRunner()


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for tests."""
    with tempfile.TemporaryDirectory() as tmpdirname:
        old_dir = os.getcwd()
        os.chdir(tmpdirname)
        yield Path(tmpdirname)
        os.chdir(old_dir)


@pytest.fixture
def sample_tasks() -> List[Dict[str, Any]]:
    """Return a sample list of tasks for testing."""
    return [
        {
            "id": "1",
            "title": "Initialize project",
            "description": "Set up the initial project structure and configuration.",
            "status": "pending",
            "priority": "high",
            "dependencies": [],
            "details": "Create necessary directories and files for the project.",
            "test_strategy": "Verify all files and directories are created correctly.",
            "subtasks": []
        },
        {
            "id": "2",
            "title": "Implement core functionality",
            "description": "Implement the core functionality of the application.",
            "status": "pending",
            "priority": "medium",
            "dependencies": ["1"],
            "details": "Create the core functionality including the main data structures and algorithms.",
            "test_strategy": "Write unit tests for each component.",
            "subtasks": []
        },
        {
            "id": "3",
            "title": "Create user interface",
            "description": "Design and implement the user interface.",
            "status": "pending",
            "priority": "low",
            "dependencies": ["2"],
            "details": "Create a user-friendly interface with proper input validation.",
            "test_strategy": "Test the interface with different inputs.",
            "subtasks": [
                {
                    "id": "3.1",
                    "title": "Design UI layout",
                    "description": "Design the layout of the user interface.",
                    "status": "pending"
                },
                {
                    "id": "3.2",
                    "title": "Implement UI components",
                    "description": "Implement the UI components based on the design.",
                    "status": "pending"
                }
            ]
        }
    ]


@pytest.fixture
def sample_config() -> Dict[str, Any]:
    """Return a sample configuration for testing."""
    return {
        "api_key": "test-api-key",
        "model": "test-model",
        "max_tokens": 1000,
        "temperature": 0.5,
        "project_name": "Test Project",
        "project_version": "0.1.0",
        "default_subtasks": 3,
        "default_priority": "medium"
    }


@pytest.fixture
def setup_tasks_file(temp_dir: Path, sample_tasks: List[Dict[str, Any]]) -> Path:
    """Create a tasks.json file with sample tasks."""
    tasks_data = {
        "version": "0.1.0",
        "updated_at": "2023-01-01T00:00:00",
        "tasks": sample_tasks
    }
    
    tasks_file = temp_dir / "tasks.json"
    with open(tasks_file, 'w') as f:
        json.dump(tasks_data, f, indent=2)
    
    return tasks_file


@pytest.fixture
def setup_config_file(temp_dir: Path, sample_config: Dict[str, Any]) -> Path:
    """Create a config file with sample configuration."""
    config_file = temp_dir / ".focus_forge_config"
    with open(config_file, 'w') as f:
        json.dump(sample_config, f, indent=2)
    
    return config_file


@pytest.fixture
def mock_claude_response() -> str:
    """Return a mock Claude API response."""
    return json.dumps([
        {
            "id": "1",
            "title": "Updated task 1",
            "description": "Updated description for task 1",
            "status": "pending",
            "priority": "high",
            "dependencies": [],
            "details": "Updated details for task 1",
            "test_strategy": "Updated test strategy for task 1",
            "subtasks": []
        }
    ]) 