"""Tests for the document structure learning functionality."""

import os
import json
import tempfile
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

import pytest
import sys
from click.testing import C<PERSON><PERSON><PERSON>ner

# Add parent directory to path so we can import focus_forge module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from focus_forge.doc_discovery import (
    Document,
    DocumentType,
    LearningProfile,
    DocDiscovery,
    discover_docs,
    DOCS_CACHE_DIR,
    LEARNING_PROFILES_DIR
)

from focus_forge.cli_extensions import (
    learn_structure_cmd,
    apply_learning_cmd,
    export_learning_cmd,
    import_learning_cmd,
    list_profiles_cmd,
    feedback_cmd
)


# Test Fixtures

@pytest.fixture
def temp_project_dir():
    """Create a temporary project directory with various document types for learning."""
    with tempfile.TemporaryDirectory() as tmpdirname:
        # Create project structure
        project_dir = Path(tmpdirname)
        
        # Create standard docs
        readme = project_dir / "README.md"
        readme.write_text("# Test Project\n\nThis is a test project README.")
        
        changelog = project_dir / "CHANGELOG.md"
        changelog.write_text("# Changelog\n\n## v0.1.0\n\n- Initial release")
        
        # Create docs directory with various document types
        docs_dir = project_dir / "docs"
        docs_dir.mkdir(exist_ok=True)
        
        # Requirements docs
        prd = docs_dir / "PRD-v1.md"
        prd.write_text("# Product Requirements\n\n## Features\n\n1. Feature A\n2. Feature B\n\n## Requirements\n\n- Requirement 1\n- Requirement 2")
        
        spec = docs_dir / "technical-spec.md"
        spec.write_text("# Technical Specification\n\nThis document outlines requirements for implementation.\n\n## User Stories\n\n- As a user, I want to...\n\n## Acceptance Criteria\n\n- The system must...")
        
        # Architecture docs
        arch_dir = docs_dir / "architecture"
        arch_dir.mkdir(exist_ok=True)
        
        arch_doc = arch_dir / "system-architecture.md"
        arch_doc.write_text("# System Architecture\n\n## Components\n\n- Component A\n- Component B\n\n## Interfaces\n\n- Interface 1\n- Interface 2")
        
        design_doc = arch_dir / "design.md"
        design_doc.write_text("# Design Document\n\n## Architecture\n\nThis section describes the architecture.\n\n## Diagrams\n\n[Diagram 1](diagram1.png)")
        
        # API docs
        api_dir = docs_dir / "api"
        api_dir.mkdir(exist_ok=True)
        
        api_doc = api_dir / "api-reference.md"
        api_doc.write_text("# API Reference\n\n## Endpoints\n\n### GET /users\n\nReturns a list of users.\n\n#### Parameters\n\n- `limit`: Maximum number of results\n\n#### Response\n\n```json\n{\"users\": []}\n```")
        
        # Create focus_forge cache dir
        cache_dir = project_dir / DOCS_CACHE_DIR
        cache_dir.mkdir(exist_ok=True)
        
        learning_dir = cache_dir / LEARNING_PROFILES_DIR
        learning_dir.mkdir(exist_ok=True)
        
        # Change to the project directory for the duration of the test
        old_dir = os.getcwd()
        os.chdir(project_dir)
        yield project_dir
        os.chdir(old_dir)


@pytest.fixture
def sample_learning_profile():
    """Create a sample learning profile for testing."""
    profile = LearningProfile("Test Profile")
    
    # Add some filename patterns
    profile.filename_patterns = {
        DocumentType.REQUIREMENTS: ["^PRD.*\\.md$", ".*requirements.*\\.md$"],
        DocumentType.ARCHITECTURE: ["^architecture.*\\.md$", ".*design.*\\.md$"],
        DocumentType.API: ["^api.*\\.md$", ".*endpoints.*\\.md$"]
    }
    
    # Add some directory patterns
    profile.directory_patterns = {
        DocumentType.ARCHITECTURE: ["architecture", "design"],
        DocumentType.API: ["api", "endpoints"]
    }
    
    # Add some keyword patterns
    profile.keyword_patterns = {
        DocumentType.REQUIREMENTS: {
            "requirement": 5,
            "feature": 3,
            "user story": 2
        },
        DocumentType.ARCHITECTURE: {
            "architecture": 4,
            "component": 3,
            "interface": 2
        },
        DocumentType.API: {
            "api": 3,
            "endpoint": 4,
            "request": 2,
            "response": 2
        }
    }
    
    return profile


# Tests for LearningProfile class

class TestLearningProfile:
    """Tests for the LearningProfile class."""
    
    def test_init(self):
        """Test LearningProfile initialization."""
        profile = LearningProfile("Test Profile")
        assert profile.name == "Test Profile"
        assert profile.project_id is not None
        assert isinstance(profile.created, datetime)
        assert isinstance(profile.last_updated, datetime)
        assert profile.filename_patterns == {}
        assert profile.directory_patterns == {}
        assert profile.keyword_patterns == {}
    
    def test_learn_from_documents(self, temp_project_dir):
        """Test learning patterns from documents."""
        profile = LearningProfile("Test Learning")
        
        # Create documents with known types
        docs = [
            Document(temp_project_dir / "docs" / "PRD-v1.md", doc_type=DocumentType.REQUIREMENTS, confidence=0.9),
            Document(temp_project_dir / "docs" / "architecture" / "system-architecture.md", doc_type=DocumentType.ARCHITECTURE, confidence=0.9),
            Document(temp_project_dir / "docs" / "api" / "api-reference.md", doc_type=DocumentType.API, confidence=0.9)
        ]
        
        # Make sure the documents have content
        for doc in docs:
            doc.read_content()
        
        # Learn from documents
        profile.learn_from_documents(docs)
        
        # Verify that patterns were learned
        assert DocumentType.REQUIREMENTS in profile.filename_patterns
        assert len(profile.filename_patterns[DocumentType.REQUIREMENTS]) > 0
        
        assert DocumentType.ARCHITECTURE in profile.directory_patterns
        assert "architecture" in profile.directory_patterns[DocumentType.ARCHITECTURE]
        
        assert DocumentType.API in profile.keyword_patterns
        assert len(profile.keyword_patterns[DocumentType.API]) > 0
    
    def test_apply_to_document(self, temp_project_dir, sample_learning_profile):
        """Test applying a learning profile to classify a document."""
        # Create an unclassified document
        doc = Document(temp_project_dir / "docs" / "technical-spec.md")
        
        # Apply the profile
        doc_type, confidence = sample_learning_profile.apply_to_document(doc)
        
        # Verify classification - should recognize it as requirements based on content
        assert doc_type == DocumentType.REQUIREMENTS
        assert confidence > 0.5
    
    def test_to_dict_and_from_dict(self, sample_learning_profile):
        """Test converting profile to and from dictionary."""
        # Convert to dict
        profile_dict = sample_learning_profile.to_dict()
        
        # Verify dict structure
        assert "name" in profile_dict
        assert "project_id" in profile_dict
        assert "created" in profile_dict
        assert "last_updated" in profile_dict
        assert "filename_patterns" in profile_dict
        assert "directory_patterns" in profile_dict
        assert "keyword_patterns" in profile_dict
        
        # Convert back to profile
        new_profile = LearningProfile.from_dict(profile_dict)
        
        # Verify values
        assert new_profile.name == sample_learning_profile.name
        assert new_profile.project_id == sample_learning_profile.project_id
        assert new_profile.filename_patterns == sample_learning_profile.filename_patterns
        assert new_profile.directory_patterns == sample_learning_profile.directory_patterns
        assert new_profile.keyword_patterns == sample_learning_profile.keyword_patterns
    
    def test_save_and_load(self, temp_project_dir, sample_learning_profile):
        """Test saving and loading a profile."""
        # Save profile
        profile_path = sample_learning_profile.save()
        
        # Verify file was created
        assert os.path.exists(profile_path)
        
        # Load profile
        loaded_profile = LearningProfile.load(profile_path)
        
        # Verify loaded profile
        assert loaded_profile.name == sample_learning_profile.name
        assert loaded_profile.project_id == sample_learning_profile.project_id
        assert loaded_profile.filename_patterns == sample_learning_profile.filename_patterns
        assert loaded_profile.directory_patterns == sample_learning_profile.directory_patterns
        assert loaded_profile.keyword_patterns == sample_learning_profile.keyword_patterns
    
    def test_list_profiles(self, temp_project_dir, sample_learning_profile):
        """Test listing available profiles."""
        # Save profile
        sample_learning_profile.save()
        
        # Create another profile
        profile2 = LearningProfile("Another Profile")
        profile2.save()
        
        # List profiles
        profiles = LearningProfile.list_profiles()
        
        # Verify both profiles are in the list
        assert len(profiles) == 2
        assert any(p.startswith("test_profile_") for p in profiles)
        assert any(p.startswith("another_profile_") for p in profiles)


# Tests for document discovery with learning profiles

def test_discover_with_learning_profile(temp_project_dir, sample_learning_profile):
    """Test document discovery using a learning profile."""
    # Save the learning profile
    sample_learning_profile.save()
    
    # Discover documents with learning
    discovery = DocDiscovery(temp_project_dir)
    docs = discovery.discover_documents(
        max_depth=3,
        use_cache=False,
        include_content=True,
        use_learning=True,
        profile_name="Test Profile"
    )
    
    # Verify documents were discovered and classified
    assert len(docs) > 0
    
    # Check for correctly classified documents using the learning profile
    requirements_docs = [doc for doc in docs if doc.doc_type == DocumentType.REQUIREMENTS]
    architecture_docs = [doc for doc in docs if doc.doc_type == DocumentType.ARCHITECTURE]
    api_docs = [doc for doc in docs if doc.doc_type == DocumentType.API]
    
    assert len(requirements_docs) >= 1
    assert len(architecture_docs) >= 1
    assert len(api_docs) >= 1
    
    # Verify active profile was loaded
    assert discovery.active_profile is not None
    assert discovery.active_profile.name == "Test Profile"


# Tests for CLI commands

@pytest.fixture
def runner():
    """Return a Click CLI test runner."""
    return CliRunner()


def test_learn_structure_cmd(runner, temp_project_dir):
    """Test the learn-structure CLI command."""
    result = runner.invoke(learn_structure_cmd, ["--name", "TestCLI"])
    
    assert result.exit_code == 0
    assert "Learning document structure patterns" in result.output
    assert "Learning profile saved to" in result.output
    
    # Verify the profile was created
    profile_dir = os.path.join(DOCS_CACHE_DIR, LEARNING_PROFILES_DIR)
    profiles = os.listdir(profile_dir)
    assert any(p.startswith("testcli_") for p in profiles)


def test_apply_learning_cmd(runner, temp_project_dir, sample_learning_profile):
    """Test the apply-learning CLI command."""
    # Save profile first
    sample_learning_profile.save()
    
    result = runner.invoke(apply_learning_cmd, ["--profile", "Test Profile"])
    
    assert result.exit_code == 0
    assert "Applying learning profile" in result.output
    # Check for table output
    assert "Document" in result.output
    assert "Type" in result.output
    assert "Confidence" in result.output


def test_export_learning_cmd(runner, temp_project_dir, sample_learning_profile):
    """Test the export-learning CLI command."""
    # Save profile first
    sample_learning_profile.save()
    
    # Export profile
    export_path = os.path.join(temp_project_dir, "exported-profile.json")
    result = runner.invoke(export_learning_cmd, ["--profile", "Test Profile", "--output", export_path])
    
    assert result.exit_code == 0
    assert "Exporting learning profile" in result.output
    assert "Learning profile exported to" in result.output
    
    # Verify the exported file exists
    assert os.path.exists(export_path)
    
    # Verify content
    with open(export_path, 'r') as f:
        exported_data = json.load(f)
    
    assert exported_data["name"] == "Test Profile"
    assert "filename_patterns" in exported_data
    assert "directory_patterns" in exported_data
    assert "keyword_patterns" in exported_data


def test_import_learning_cmd(runner, temp_project_dir):
    """Test the import-learning CLI command."""
    # Create a profile to import
    profile_data = {
        "name": "Imported Profile",
        "project_id": "test123",
        "created": datetime.now().isoformat(),
        "last_updated": datetime.now().isoformat(),
        "filename_patterns": {
            DocumentType.REQUIREMENTS: [".*reqs.*\\.md$"]
        },
        "directory_patterns": {},
        "keyword_patterns": {}
    }
    
    # Save to a file
    import_path = os.path.join(temp_project_dir, "import-profile.json")
    with open(import_path, 'w') as f:
        json.dump(profile_data, f)
    
    # Import the profile
    result = runner.invoke(import_learning_cmd, ["--input", import_path])
    
    assert result.exit_code == 0
    assert "Importing learning profile" in result.output
    assert "Learning profile imported and saved to" in result.output
    
    # Verify profile was imported
    profile_dir = os.path.join(DOCS_CACHE_DIR, LEARNING_PROFILES_DIR)
    profiles = os.listdir(profile_dir)
    assert any(p.startswith("imported_profile_") for p in profiles)


def test_list_profiles_cmd(runner, temp_project_dir, sample_learning_profile):
    """Test the list-profiles CLI command."""
    # Save profile first
    sample_learning_profile.save()
    
    # Create another profile
    profile2 = LearningProfile("Another Profile")
    profile2.save()
    
    # List profiles
    result = runner.invoke(list_profiles_cmd)
    
    assert result.exit_code == 0
    assert "Available learning profiles" in result.output
    assert "Test Profile" in result.output
    assert "Another Profile" in result.output


def test_feedback_cmd(runner, temp_project_dir, sample_learning_profile):
    """Test the feedback CLI command."""
    # Save profile first
    sample_learning_profile.save()
    
    # Create a document to provide feedback for
    doc_path = os.path.join(temp_project_dir, "custom-doc.md")
    with open(doc_path, 'w') as f:
        f.write("# Custom Document\n\nThis is a custom document for testing.")
    
    # Provide feedback
    result = runner.invoke(feedback_cmd, [
        "--doc", doc_path,
        "--type", "requirements",
        "--profile", "Test Profile"
    ])
    
    assert result.exit_code == 0
    assert "Processing feedback for document" in result.output
    assert "Learning profile updated and saved to" in result.output
    
    # Verify profile was updated
    updated_profile = None
    for filename in os.listdir(os.path.join(DOCS_CACHE_DIR, LEARNING_PROFILES_DIR)):
        if filename.startswith("test_profile_"):
            profile_path = os.path.join(DOCS_CACHE_DIR, LEARNING_PROFILES_DIR, filename)
            updated_profile = LearningProfile.load(profile_path)
            break
    
    assert updated_profile is not None
    # The filename should have been added to patterns for the requirements type
    assert any("custom-doc" in pattern.lower() for pattern in updated_profile.filename_patterns.get(DocumentType.REQUIREMENTS, []))


def test_document_relationships(temp_project_dir):
    """Test document relationship detection."""
    # Create documents with relationships
    doc1_path = os.path.join(temp_project_dir, "source-doc.md")
    with open(doc1_path, 'w') as f:
        f.write("# Source Document\n\nThis document links to [Target Document](target-doc.md).")
    
    doc2_path = os.path.join(temp_project_dir, "target-doc.md")
    with open(doc2_path, 'w') as f:
        f.write("# Target Document\n\nThis is the target document.")
    
    # Run discovery
    discovery = DocDiscovery(temp_project_dir)
    discovery.discover_documents(include_content=True)
    
    # Verify relationships were found
    assert len(discovery.document_relationships) > 0
    
    # Check for link relationship
    links = [r for r in discovery.document_relationships if r.get("type") == "link"]
    assert len(links) > 0
    assert any(r["source"].endswith("source-doc.md") and r["target"].endswith("target-doc.md") for r in links)
    
    # The relationships should also appear in the output from discover_docs
    result = discover_docs(
        project_dir=temp_project_dir,
        include_content=True,
        output_format="json"
    )
    
    if isinstance(result, str):
        result = json.loads(result)
    
    assert "relationships" in result
    assert len(result["relationships"]) > 0 