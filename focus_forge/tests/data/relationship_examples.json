{"examples": [{"text": "The user authentication system must be completed before implementing the permission-based access controls.", "relationships": [["user authentication system", "before", "permission-based access controls"]]}, {"text": "Once the access controls are in place, we can proceed with developing the content management features.", "relationships": [["access controls", "enables", "content management features"]]}, {"text": "The dashboard should only display content that the user has permission to view.", "relationships": [["user permission", "required_for", "dashboard content display"]]}, {"text": "The database schema depends on the data model design being completed.", "relationships": [["database schema", "depends_on", "data model design"]]}, {"text": "Backend API implementation is required for frontend components that use this data.", "relationships": [["frontend components", "depends_on", "backend API implementation"]]}, {"text": "After implementing the search indexing, the search results page can be developed.", "relationships": [["search results page", "depends_on", "search indexing"]]}, {"text": "User profile management requires both authentication and database user table implementation.", "relationships": [["user profile management", "depends_on", "authentication"], ["user profile management", "depends_on", "database user table"]]}, {"text": "First create the data models, then implement the API endpoints, and finally build the UI components.", "relationships": [["API endpoints", "depends_on", "data models"], ["UI components", "depends_on", "API endpoints"]]}, {"text": "The system must adhere to security standards and should be tested thoroughly before deployment.", "relationships": [["deployment", "depends_on", "security standards adherence"], ["deployment", "depends_on", "thorough testing"]]}], "complex_examples": ["\n    The authentication module needs to be integrated with the existing user database.\n    User roles and permissions will be defined after the authentication is working.\n    The dashboard will display information based on user permissions and system status.\n    System monitoring depends on the logging infrastructure which should be implemented early.\n    ", "\n    When implementing feature X, developers should first ensure that component Y is working correctly\n    since X relies on functionality provided by Y. Additionally, Z should be thoroughly tested as\n    changes to X might affect <PERSON>'s behavior in unexpected ways.\n    ", "\n    The notification system requires both the user preference service and the message queue to function properly.\n    However, user preferences can be configured independently of notifications being enabled.\n    The admin interface for managing notification templates can be developed in parallel.\n    "]}