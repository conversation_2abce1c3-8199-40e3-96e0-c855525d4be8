"""Tests for edge cases and error handling in Focus Forge."""

import json
import os
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock
import sys

import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON>ner

# Add parent directory to path so we can import focus_forge module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the functions and CLI commands to test
from focus_forge import (
    cli, load_tasks, save_tasks, find_task_by_id, validate_dependencies,
    TASKS_FILE, CONFIG_FILE, TASKS_DIR
)


@pytest.mark.unit
class TestEdgeCases:
    """Test edge cases and error handling."""

    def test_find_task_by_id_nonexistent_parent(self, sample_tasks: list):
        """Test finding a subtask with a non-existent parent ID."""
        # Create a list of sample tasks
        task_list = sample_tasks.copy()
        
        # Try to find a subtask with non-existent parent
        result = find_task_by_id(task_list, "999.1")
        
        assert result is None

    def test_find_task_by_id_invalid_format(self, sample_tasks: list):
        """Test finding a task with invalid ID format."""
        # Create a list of sample tasks
        task_list = sample_tasks.copy()
        
        # Try to find a task with invalid ID format
        result = find_task_by_id(task_list, "invalid-id")
        
        assert result is None

    def test_find_task_by_id_empty_list(self):
        """Test finding a task in an empty list."""
        result = find_task_by_id([], "1")
        
        assert result is None

    def test_validate_dependencies_circular(self, temp_dir: Path, sample_tasks: list):
        """Test validating circular dependencies."""
        # Create circular dependencies
        tasks = sample_tasks.copy()
        
        # Task 1 depends on 2, which depends on 3, which depends on 1
        tasks[0]["dependencies"] = ["2"]  # 1 depends on 2
        tasks[1]["dependencies"] = ["3"]  # 2 depends on 3
        tasks[2]["dependencies"] = ["1"]  # 3 depends on 1
        
        # Save these tasks
        save_tasks(tasks)
        
        # Run the validate-dependencies command
        runner = CliRunner()
        result = runner.invoke(cli, ["validate-dependencies"])
        
        # This shouldn't detect cycles directly in validate-dependencies
        # as it only checks for direct self-dependencies
        assert result.exit_code == 0
        
        # But next command should show no eligible tasks due to circular deps
        result_next = runner.invoke(cli, ["next"])
        assert result_next.exit_code == 0
        assert "No eligible tasks found" in result_next.output

    def test_load_tasks_invalid_json(self, temp_dir: Path):
        """Test loading an invalid JSON file."""
        # Create an invalid JSON file
        with open(TASKS_FILE, 'w') as f:
            f.write("This is not valid JSON")
        
        # Load tasks should handle this gracefully
        tasks = load_tasks()
        
        # Should return an empty list
        assert tasks == []

    def test_large_number_of_tasks(self, temp_dir: Path):
        """Test handling a large number of tasks."""
        # Create a large number of tasks
        large_tasks = []
        for i in range(1, 101):  # Create 100 tasks
            large_tasks.append({
                "id": str(i),
                "title": f"Task {i}",
                "description": f"Description for task {i}",
                "status": "pending",
                "priority": "medium",
                "dependencies": [],
                "details": f"Details for task {i}",
                "test_strategy": None,
                "subtasks": []
            })
        
        # Save these tasks
        save_tasks(large_tasks)
        
        # Load tasks again to verify
        loaded_tasks = load_tasks()
        
        # Check if all tasks were saved and loaded correctly
        assert len(loaded_tasks) == 100
        
        # Test listing tasks
        runner = CliRunner()
        result = runner.invoke(cli, ["list"])
        
        # Should complete successfully
        assert result.exit_code == 0
        
        # Test next task with many options
        result_next = runner.invoke(cli, ["next"])
        assert result_next.exit_code == 0
        assert "Task 1: Task 1" in result_next.output

    def test_deep_subtask_nesting(self, temp_dir: Path):
        """Test handling deeply nested subtasks."""
        # Create tasks with deeply nested subtasks
        tasks = [
            {
                "id": "1",
                "title": "Parent task",
                "description": "A parent task",
                "status": "pending",
                "priority": "high",
                "dependencies": [],
                "details": "Parent task details",
                "test_strategy": None,
                "subtasks": [
                    {
                        "id": "1.1",
                        "title": "First level subtask",
                        "description": "A first level subtask",
                        "status": "pending",
                        "subtasks": [
                            {
                                "id": "1.1.1",
                                "title": "Second level subtask",
                                "description": "A second level subtask",
                                "status": "pending"
                            }
                        ]
                    }
                ]
            }
        ]
        
        # Save these tasks
        save_tasks(tasks)
        
        # Test find task by ID with nested IDs
        loaded_tasks = load_tasks()
        
        # Find task should behave according to its implementation
        # Our implementation only supports one level of nesting, so this should return None
        result = find_task_by_id(loaded_tasks, "1.1.1")
        
        # Current implementation only handles one level of subtasks
        assert result is None

    def test_duplicate_task_ids(self, temp_dir: Path):
        """Test handling duplicate task IDs."""
        # Create tasks with duplicate IDs
        tasks = [
            {
                "id": "1",
                "title": "First task",
                "description": "First task description",
                "status": "pending",
                "priority": "high",
                "dependencies": [],
                "details": "First task details",
                "test_strategy": None,
                "subtasks": []
            },
            {
                "id": "1",  # Duplicate ID
                "title": "Duplicate task",
                "description": "Duplicate task description",
                "status": "pending",
                "priority": "medium",
                "dependencies": [],
                "details": "Duplicate task details",
                "test_strategy": None,
                "subtasks": []
            }
        ]
        
        # Save these tasks
        save_tasks(tasks)
        
        # Load tasks again to verify
        loaded_tasks = load_tasks()
        
        # Both tasks should be loaded
        assert len(loaded_tasks) == 2
        
        # find_task_by_id will return the first matching task
        task = find_task_by_id(loaded_tasks, "1")
        assert task is not None
        assert task["title"] == "First task"  # First one should be found

    def test_missing_required_fields(self, temp_dir: Path):
        """Test handling tasks with missing required fields."""
        # Create tasks with missing required fields
        tasks = [
            {
                "id": "1",
                # Missing title and other required fields
            }
        ]
        
        # Save these tasks
        save_tasks(tasks)
        
        # Load tasks again to verify
        loaded_tasks = load_tasks()
        
        # Task should be loaded
        assert len(loaded_tasks) == 1
        
        # Test that commands handle this gracefully
        runner = CliRunner()
        
        # List command
        result_list = runner.invoke(cli, ["list"])
        assert result_list.exit_code == 0  # Should not crash
        
        # Show command
        result_show = runner.invoke(cli, ["show", "1"])
        assert result_show.exit_code == 0  # Should not crash 