#!/usr/bin/env python3
"""
Test suite for critical path analysis functionality in focus_forge.

This test suite verifies the correctness of the critical path analysis
algorithm implementation from Task T4.
"""
import os
import sys
import unittest
from typing import List, Dict, Any, Optional, Set

# Ensure focus_forge module is in the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import sequence analysis functionality
from focus_forge.sequence_analysis import (
    SequenceAnalyzer,
    get_critical_path,
    create_sequence_analyzer_from_tasks
)
from focus_forge.dependency_graph import DependencyGraph, Dependency, DependencyType


def linear_tasks() -> List[Dict[str, Any]]:
    """Create tasks with linear dependencies for basic critical path testing."""
    return [
        {"id": "T1", "title": "Task 1", "priority": "high", "dependencies": []},
        {"id": "T2", "title": "Task 2", "priority": "high", "dependencies": ["T1"]},
        {"id": "T3", "title": "Task 3", "priority": "medium", "dependencies": ["T2"]},
        {"id": "T4", "title": "Task 4", "priority": "medium", "dependencies": ["T3"]},
        {"id": "T5", "title": "Task 5", "priority": "low", "dependencies": ["T4"]}
    ]


def branching_tasks() -> List[Dict[str, Any]]:
    """Create tasks with branching dependencies for critical path testing."""
    return [
        {"id": "A", "title": "Task A", "priority": "high", "dependencies": []},
        {"id": "B1", "title": "Task B1", "priority": "high", "dependencies": ["A"]},
        {"id": "B2", "title": "Task B2", "priority": "medium", "dependencies": ["A"]},
        {"id": "B3", "title": "Task B3", "priority": "low", "dependencies": ["A"]},
        {"id": "C1", "title": "Task C1", "priority": "high", "dependencies": ["B1"]},
        {"id": "C2", "title": "Task C2", "priority": "medium", "dependencies": ["B2"]},
        {"id": "C3", "title": "Task C3", "priority": "low", "dependencies": ["B3"]},
        {"id": "D", "title": "Task D", "priority": "medium", "dependencies": ["C1", "C2", "C3"]}
    ]


def complex_network_tasks() -> List[Dict[str, Any]]:
    """Create a complex task network with multiple possible critical paths."""
    return [
        {"id": "Start", "title": "Start Task", "priority": "high", "dependencies": []},
        {"id": "A1", "title": "Task A1", "priority": "high", "dependencies": ["Start"]},
        {"id": "A2", "title": "Task A2", "priority": "medium", "dependencies": ["Start"]},
        {"id": "A3", "title": "Task A3", "priority": "low", "dependencies": ["Start"]},
        {"id": "B1", "title": "Task B1", "priority": "high", "dependencies": ["A1"]},
        {"id": "B2", "title": "Task B2", "priority": "medium", "dependencies": ["A1", "A2"]},
        {"id": "B3", "title": "Task B3", "priority": "low", "dependencies": ["A2", "A3"]},
        {"id": "C1", "title": "Task C1", "priority": "high", "dependencies": ["B1"]},
        {"id": "C2", "title": "Task C2", "priority": "medium", "dependencies": ["B1", "B2"]},
        {"id": "C3", "title": "Task C3", "priority": "low", "dependencies": ["B2", "B3"]},
        {"id": "D1", "title": "Task D1", "priority": "high", "dependencies": ["C1", "C2"]},
        {"id": "D2", "title": "Task D2", "priority": "medium", "dependencies": ["C2", "C3"]},
        {"id": "End", "title": "End Task", "priority": "medium", "dependencies": ["D1", "D2"]}
    ]


def weighted_tasks() -> List[Dict[str, Any]]:
    """Create tasks with weights (durations) for weighted critical path testing."""
    return [
        {
            "id": "T1",
            "title": "Task 1",
            "priority": "high",
            "dependencies": [],
            "metadata": {"duration": 2}
        },
        {
            "id": "T2A",
            "title": "Task 2A",
            "priority": "high",
            "dependencies": ["T1"],
            "metadata": {"duration": 3}
        },
        {
            "id": "T2B",
            "title": "Task 2B",
            "priority": "medium",
            "dependencies": ["T1"],
            "metadata": {"duration": 5}
        },
        {
            "id": "T3A",
            "title": "Task 3A",
            "priority": "medium",
            "dependencies": ["T2A"],
            "metadata": {"duration": 4}
        },
        {
            "id": "T3B",
            "title": "Task 3B",
            "priority": "low",
            "dependencies": ["T2B"],
            "metadata": {"duration": 2}
        },
        {
            "id": "T4",
            "title": "Task 4",
            "priority": "low",
            "dependencies": ["T3A", "T3B"],
            "metadata": {"duration": 3}
        }
    ]


def tasks_with_circular_dependencies() -> List[Dict[str, Any]]:
    """Create tasks with circular dependencies for testing error handling."""
    return [
        {"id": "T1", "title": "Task 1", "priority": "high", "dependencies": ["T3"]},
        {"id": "T2", "title": "Task 2", "priority": "medium", "dependencies": ["T1"]},
        {"id": "T3", "title": "Task 3", "priority": "low", "dependencies": ["T2"]}
    ]


class TestCriticalPathBasics(unittest.TestCase):
    """Test basic critical path functionality."""
    
    def test_linear_critical_path(self):
        """Test critical path in a linear dependency chain."""
        analyzer = create_sequence_analyzer_from_tasks(linear_tasks())
        critical_path = analyzer.get_critical_path()
        
        # In a linear chain, the critical path is the entire chain
        expected_path = ["T1", "T2", "T3", "T4", "T5"]
        self.assertEqual(critical_path, expected_path)
    
    def test_branching_critical_path(self):
        """Test critical path in a branching dependency structure."""
        analyzer = create_sequence_analyzer_from_tasks(branching_tasks())
        critical_path = analyzer.get_critical_path()
        
        # The critical path should include tasks from branch with most dependencies
        # Expected path: A -> B1 -> C1 -> D (or equivalent depending on implementation)
        self.assertEqual(critical_path[0], "A")  # Should start with A
        self.assertEqual(critical_path[-1], "D")  # Should end with D
        self.assertEqual(len(critical_path), 4)   # Should have 4 tasks
        
        # Check that the path is valid
        for i in range(1, len(critical_path)):
            current = critical_path[i]
            previous = critical_path[i-1]
            
            # Find task object
            current_task = next((t for t in branching_tasks() if t["id"] == current), None)
            
            # Verify dependency relationship
            self.assertIn(previous, current_task["dependencies"])
    
    def test_multiple_paths_same_length(self):
        """Test critical path selection when multiple paths have the same length."""
        # Modify tasks so all branches have same length
        modified_tasks = branching_tasks().copy()
        
        # Make tasks have same priority
        for task in modified_tasks:
            task["priority"] = "medium"
        
        analyzer = create_sequence_analyzer_from_tasks(modified_tasks)
        critical_path = analyzer.get_critical_path()
        
        # The critical path should include tasks from one of the branches
        # And it should be a valid path from A to D
        self.assertEqual(critical_path[0], "A")
        self.assertEqual(critical_path[-1], "D")
        self.assertEqual(len(critical_path), 4)
        
        # Path should be one of: A->B1->C1->D, A->B2->C2->D, or A->B3->C3->D
        possible_paths = [
            ["A", "B1", "C1", "D"],
            ["A", "B2", "C2", "D"],
            ["A", "B3", "C3", "D"]
        ]
        
        self.assertIn(critical_path, possible_paths)


class TestWeightedCriticalPath(unittest.TestCase):
    """Test critical path with weighted tasks."""
    
    def test_weighted_critical_path(self):
        """Test critical path calculation with task durations."""
        # Create a custom analyzer that takes task durations into account
        analyzer = create_sequence_analyzer_from_tasks(weighted_tasks())
        
        # Override the critical path method to consider weights
        def weighted_critical_path() -> List[str]:
            """Calculate critical path considering task durations."""
            # Build a weight map
            tasks = weighted_tasks()
            weights = {task["id"]: task.get("metadata", {}).get("duration", 1)
                     for task in tasks}
            
            # Calculate earliest completion time for each task
            earliest_completion = {}
            topo_sort = analyzer.dependency_graph.topological_sort()
            
            if not topo_sort:
                return []
            
            # Initialize earliest completion times
            for task_id in topo_sort:
                earliest_completion[task_id] = 0
            
            # Calculate earliest completion times based on dependencies
            for task_id in topo_sort:
                task_duration = weights.get(task_id, 1)
                deps = analyzer.dependency_graph.get_dependencies(task_id)
                
                # Task can't start until all its dependencies are complete
                for dep in deps:
                    dep_id = dep.target_id
                    if dep_id in earliest_completion:
                        earliest_completion[task_id] = max(
                            earliest_completion[task_id],
                            earliest_completion[dep_id] + weights.get(dep_id, 1)
                        )
            
            # Find the end task (no dependents)
            end_tasks = [task_id for task_id in topo_sort
                        if not analyzer.dependency_graph.get_dependents(task_id)]
            
            if not end_tasks:
                return topo_sort[-1:]  # Return the last task if no clear end
            
            # If multiple end tasks, choose the one with latest completion
            end_task = max(end_tasks, key=lambda t: earliest_completion[t])
            
            # Trace backwards to find the path
            path = [end_task]
            current = end_task
            
            while True:
                deps = analyzer.dependency_graph.get_dependencies(current)
                if not deps:
                    break
                
                # Find the dependency with the latest completion time
                critical_predecessor = max(
                    deps,
                    key=lambda d: earliest_completion.get(d.target_id, 0) + weights.get(d.target_id, 0)
                )
                
                # Add to the path
                current = critical_predecessor.target_id
                path.append(current)
                
                # If we've reached a task with no dependencies, we're done
                if not analyzer.dependency_graph.get_dependencies(current):
                    break
            
            # Return the path in correct order (start to end)
            return list(reversed(path))
        
        # Replace the analyzer's method with our weighted version
        analyzer.get_critical_path = weighted_critical_path
        
        # Get the critical path
        critical_path = analyzer.get_critical_path()
        
        # The expected critical path is:
        # T1 (duration 2) -> T2B (duration 5) -> T3B (duration 2) -> T4 (duration 3)
        # Total duration: 12
        # Compared to alternative path:
        # T1 (duration 2) -> T2A (duration 3) -> T3A (duration 4) -> T4 (duration 3)
        # Total duration: 12
        # Both paths have the same duration, so either is valid
        
        possible_paths = [
            ["T1", "T2B", "T3B", "T4"],
            ["T1", "T2A", "T3A", "T4"]
        ]
        
        self.assertIn(critical_path, possible_paths)
        
        # Calculate actual path durations to verify they're equal
        def calculate_path_duration(path):
            return sum(task.get("metadata", {}).get("duration", 1) 
                     for task in weighted_tasks() if task["id"] in path)
        
        path1_duration = calculate_path_duration(possible_paths[0])
        path2_duration = calculate_path_duration(possible_paths[1])
        
        self.assertEqual(path1_duration, path2_duration)


class TestCriticalPathEdgeCases(unittest.TestCase):
    """Test critical path algorithm with edge cases."""
    
    def test_empty_tasks(self):
        """Test critical path with empty task list."""
        analyzer = create_sequence_analyzer_from_tasks([])
        critical_path = analyzer.get_critical_path()
        
        self.assertEqual(critical_path, [])
    
    def test_single_task(self):
        """Test critical path with a single task."""
        tasks = [{"id": "Solo", "title": "Solo Task", "priority": "high", "dependencies": []}]
        analyzer = create_sequence_analyzer_from_tasks(tasks)
        critical_path = analyzer.get_critical_path()
        
        self.assertEqual(critical_path, ["Solo"])
    
    def test_circular_dependencies(self):
        """Test critical path handling with circular dependencies."""
        tasks = tasks_with_circular_dependencies()
        analyzer = create_sequence_analyzer_from_tasks(tasks)
        critical_path = analyzer.get_critical_path()
        
        # The implementation might return a cycle or an empty list - both are valid approaches
        # We'll just check that if it returns a path, it's a valid path in the dependencies graph
        if not critical_path:
            # Empty list is a valid response for cycles
            pass
        else:
            # If a path is returned, it should contain only tasks from our input
            task_ids = [t["id"] for t in tasks]
            for task_id in critical_path:
                self.assertIn(task_id, task_ids)
            
            # And the path should be a valid traversal through the tasks
            for i in range(1, len(critical_path)):
                current_id = critical_path[i]
                current_task = next(t for t in tasks if t["id"] == current_id)
                
                # Each task should depend on at least one previous task
                dependencies = current_task.get("dependencies", [])
                self.assertTrue(any(prev_id in dependencies for prev_id in critical_path[:i]),
                             f"Task {current_id} doesn't depend on any previous task in the path")
    
    def test_disconnected_components(self):
        """Test critical path with disconnected task components."""
        tasks = [
            {"id": "A1", "title": "Task A1", "priority": "high", "dependencies": []},
            {"id": "A2", "title": "Task A2", "priority": "high", "dependencies": ["A1"]},
            {"id": "B1", "title": "Task B1", "priority": "high", "dependencies": []},
            {"id": "B2", "title": "Task B2", "priority": "high", "dependencies": ["B1"]},
            {"id": "B3", "title": "Task B3", "priority": "high", "dependencies": ["B2"]}
        ]
        
        analyzer = create_sequence_analyzer_from_tasks(tasks)
        critical_path = analyzer.get_critical_path()
        
        # Should return the longest path: B1 -> B2 -> B3
        self.assertEqual(critical_path, ["B1", "B2", "B3"])


class TestComplexCriticalPath(unittest.TestCase):
    """Test critical path in complex task networks."""
    
    def test_complex_network_critical_path(self):
        """Test critical path in a complex task network."""
        analyzer = create_sequence_analyzer_from_tasks(complex_network_tasks())
        critical_path = analyzer.get_critical_path()
        
        # Verify the path is valid
        self.assertEqual(critical_path[0], "Start")  # Should start with Start
        self.assertEqual(critical_path[-1], "End")    # Should end with End
        
        # Verify dependencies are respected
        for i in range(1, len(critical_path)):
            current = critical_path[i]
            previous = critical_path[i-1]
            
            # Find the task
            current_task = next((t for t in complex_network_tasks() if t["id"] == current), None)
            
            # The previous task should be a dependency of the current task
            self.assertIn(previous, current_task["dependencies"])
        
        # The expected critical path should have 6 tasks (including Start and End)
        self.assertEqual(len(critical_path), 6)
    
    def test_critical_path_length(self):
        """Test that the critical path is the longest possible path."""
        tasks = complex_network_tasks()
        analyzer = create_sequence_analyzer_from_tasks(tasks)
        critical_path = analyzer.get_critical_path()
        
        # The critical path length is the minimum project duration
        critical_path_length = len(critical_path) - 1  # -1 because we count edges, not nodes
        
        # Find all possible paths from Start to End
        def find_all_paths(graph, start, end, path=[]):
            path = path + [start]
            if start == end:
                return [path]
            paths = []
            for task in tasks:
                if start in task["dependencies"] and task["id"] not in path:
                    new_paths = find_all_paths(graph, task["id"], end, path)
                    for new_path in new_paths:
                        paths.append(new_path)
            return paths
        
        all_paths = find_all_paths(analyzer.dependency_graph, "Start", "End")
        
        # Convert paths to include Start
        complete_paths = []
        for path in all_paths:
            if path[0] != "Start":
                complete_paths.append(["Start"] + path)
            else:
                complete_paths.append(path)
        
        # The critical path should be at least as long as all other paths
        for path in complete_paths:
            path_length = len(path) - 1
            self.assertGreaterEqual(critical_path_length, path_length)
    
    def test_helper_function(self):
        """Test the helper function for getting the critical path."""
        # Use the helper function
        path = get_critical_path(complex_network_tasks())
        
        # Ensure it's a valid path from Start to End
        self.assertEqual(path[0]["id"], "Start")
        self.assertEqual(path[-1]["id"], "End")
        
        # Verify dependencies
        for i in range(1, len(path)):
            self.assertIn(path[i-1]["id"], path[i]["dependencies"])


def run_tests():
    """Run the test suite."""
    print("Running critical path tests for T04")
    test_suite = unittest.TestSuite()
    test_suite.addTest(unittest.makeSuite(TestCriticalPathBasics))
    test_suite.addTest(unittest.makeSuite(TestWeightedCriticalPath))
    test_suite.addTest(unittest.makeSuite(TestCriticalPathEdgeCases))
    test_suite.addTest(unittest.makeSuite(TestComplexCriticalPath))
    
    runner = unittest.TextTestRunner(verbosity=2)
    runner.run(test_suite)


if __name__ == "__main__":
    run_tests() 