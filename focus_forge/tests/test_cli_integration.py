#!/usr/bin/env python3
"""
Tests for the CLI integration module.
"""
import os
import sys
import json
import unittest
import tempfile
from unittest.mock import patch, MagicMock
from pathlib import Path
from typing import Dict, List, Any, Optional
import argparse

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Try to import the CLI integration functions
try:
    from focus_forge.cli_integration import (
        parse_args, 
        load_config, 
        print_report, 
        cmd_parse,
        cmd_integrate,
        cmd_validate
    )
    HAS_CLI = True
except ImportError:
    print("WARNING: Could not import CLI integration. Tests will be skipped.")
    HAS_CLI = False
    # Create placeholders for type checking
    def parse_args(*args, **kwargs): pass
    def load_config(*args, **kwargs): pass
    def print_report(*args, **kwargs): pass
    def cmd_parse(*args, **kwargs): pass
    def cmd_integrate(*args, **kwargs): pass
    def cmd_validate(*args, **kwargs): pass

# Sample PRD text for testing
SAMPLE_PRD = """# Sample PRD for Testing

## Overview
This is a test PRD for the integration system.

## Features
1. Feature One: This is the first feature
2. Feature Two: This depends on Feature One
3. Feature Three: This is independent

## Technical Requirements
- Must be implemented in Python
- Should have error handling
- Database integration is required
"""

# Sample tasks for testing
SAMPLE_TASKS = [
    {
        "id": "T1",
        "title": "Implement Feature One",
        "description": "Create the first feature mentioned in the PRD",
        "status": "pending",
        "priority": "high",
        "dependencies": [],
        "details": "Feature One implementation details",
        "test_strategy": "Test Feature One thoroughly",
        "subtasks": []
    },
    {
        "id": "T2",
        "title": "Implement Feature Two",
        "description": "Create the second feature that depends on Feature One",
        "status": "pending",
        "priority": "medium",
        "dependencies": ["T1"],
        "details": "Feature Two implementation details",
        "test_strategy": "Test Feature Two with Feature One",
        "subtasks": []
    }
]

# Sample report for testing
SAMPLE_REPORT = {
    "status": "success",
    "new_tasks": 1,
    "existing_tasks": 2,
    "merged_tasks": 3,
    "validation": {
        "tasks_valid": True,
        "confidence_score": 0.85,
        "clarity_score": 0.9,
        "coverage_score": 0.8,
        "ambiguous_requirements": {
            "count": 1,
            "items": [
                {
                    "requirement": "Should have error handling",
                    "severity": "medium",
                    "ambiguous_phrases": ["should"]
                }
            ]
        },
        "missing_requirements": {
            "count": 0,
            "items": []
        }
    },
    "output_file": "output.json"
}


@unittest.skipIf(not HAS_CLI, "CLI integration not available")
class TestCliIntegration(unittest.TestCase):
    """Test cases for CLI integration."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create temporary PRD file
        self.prd_file = tempfile.NamedTemporaryFile(delete=False, suffix=".md")
        with open(self.prd_file.name, 'w') as f:
            f.write(SAMPLE_PRD)
        
        # Create temporary tasks file
        self.tasks_file = tempfile.NamedTemporaryFile(delete=False, suffix=".json")
        with open(self.tasks_file.name, 'w') as f:
            json.dump({"tasks": SAMPLE_TASKS}, f)
        
        # Create temporary output file
        self.output_file = tempfile.NamedTemporaryFile(delete=False, suffix=".json")
        self.output_file.close()
        
        # Create temporary config file
        self.config_file = tempfile.NamedTemporaryFile(delete=False, suffix=".json")
        config = {
            "task_generation": {
                "default_priority": "high",
                "generate_subtasks": True
            },
            "task_validation": {
                "validate_dependencies": True
            }
        }
        with open(self.config_file.name, 'w') as f:
            json.dump(config, f)
    
    def tearDown(self):
        """Clean up temporary files."""
        try:
            os.unlink(self.prd_file.name)
            os.unlink(self.tasks_file.name)
            os.unlink(self.output_file.name)
            os.unlink(self.config_file.name)
        except Exception:
            pass
    
    def test_load_config(self):
        """Test loading configuration from a file."""
        # Test loading default config (no file)
        config = load_config()
        self.assertIsInstance(config, dict)
        self.assertIn("task_generation", config)
        self.assertIn("task_validation", config)
        self.assertIn("confidence_scoring", config)
        
        # Test loading custom config
        config = load_config(self.config_file.name)
        self.assertEqual(config["task_generation"]["default_priority"], "high")
        self.assertTrue(config["task_validation"]["validate_dependencies"])
        
        # Test handling invalid config file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".json") as invalid_file:
            invalid_file.write(b"Invalid JSON")
        
        config = load_config(invalid_file.name)
        self.assertIsInstance(config, dict)  # Should fall back to default config
        os.unlink(invalid_file.name)
    
    @patch('builtins.print')
    def test_print_report(self, mock_print):
        """Test printing a report."""
        # Test printing success report
        print_report(SAMPLE_REPORT, verbose=False)
        mock_print.assert_any_call(f"Status: success")
        
        # Reset mock
        mock_print.reset_mock()
        
        # Test printing error report
        error_report = {"status": "error", "error_message": "Test error"}
        print_report(error_report, verbose=False)
        mock_print.assert_any_call("ERROR: Test error")
        
        # Reset mock
        mock_print.reset_mock()
        
        # Test printing with verbose=True
        print_report(SAMPLE_REPORT, verbose=True)
        # Verbose should print more details
        call_args = [call[0][0] for call in mock_print.call_args_list if "Ambiguous requirements" in str(call)]
        self.assertTrue(any(call_args))
    
    @patch('focus_forge.cli_integration.parse_prd_to_tasks')
    def test_cmd_parse(self, mock_parse_prd):
        """Test the parse command handler."""
        # Mock parse_prd_to_tasks to return sample tasks
        mock_parse_prd.return_value = SAMPLE_TASKS
        
        # Create a mock args object
        args = argparse.Namespace()
        args.prd_file = self.prd_file.name
        args.output = self.output_file.name
        args.config = None
        
        # Test successful parsing
        with patch('builtins.print') as mock_print:
            cmd_parse(args)
            mock_print.assert_any_call(f"Successfully parsed PRD and generated {len(SAMPLE_TASKS)} tasks.")
        
        # Verify output file was created
        self.assertTrue(os.path.exists(self.output_file.name))
        
        # Test error handling
        mock_parse_prd.side_effect = ValueError("Test error")
        with patch('builtins.print') as mock_print:
            cmd_parse(args)
            mock_print.assert_any_call("ERROR: Test error")
    
    @patch('focus_forge.cli_integration.integrate_prd_with_existing')
    def test_cmd_integrate(self, mock_integrate):
        """Test the integrate command handler."""
        # Mock integrate_prd_with_existing to return sample report
        mock_integrate.return_value = SAMPLE_REPORT
        
        # Create a mock args object
        args = argparse.Namespace()
        args.prd_file = self.prd_file.name
        args.tasks_file = self.tasks_file.name
        args.output = self.output_file.name
        args.config = None
        args.verbose = False
        
        # Test successful integration with the print_report function
        with patch('focus_forge.cli_integration.print_report') as mock_print_report:
            cmd_integrate(args)
            mock_print_report.assert_called_once_with(SAMPLE_REPORT, False)
        
        # Test verbose output
        args.verbose = True
        with patch('focus_forge.cli_integration.print_report') as mock_print_report:
            cmd_integrate(args)
            mock_print_report.assert_called_once_with(SAMPLE_REPORT, True)
        
        # Test error handling
        mock_integrate.return_value = {"status": "error", "error_message": "Test error"}
        with patch('focus_forge.cli_integration.print_report') as mock_print_report:
            cmd_integrate(args)
            mock_print_report.assert_called_once_with({"status": "error", "error_message": "Test error"}, True)
    
    @patch('focus_forge.cli_integration.validate_tasks_with_prd')
    def test_cmd_validate(self, mock_validate):
        """Test the validate command handler."""
        # Mock validate_tasks_with_prd to return validation report
        validation_report = {
            "status": "success",
            "tasks_valid": True,
            "confidence_score": 0.85,
            "clarity_score": 0.9,
            "coverage_score": 0.8
        }
        mock_validate.return_value = validation_report
        
        # Create a mock args object
        args = argparse.Namespace()
        args.tasks_file = self.tasks_file.name
        args.prd_file = self.prd_file.name
        args.output = self.output_file.name
        args.config = None
        args.verbose = False
        args.fail = False
        
        # Test successful validation with the print_report function
        with patch('focus_forge.cli_integration.print_report') as mock_print_report:
            cmd_validate(args)
            mock_print_report.assert_called_once_with(validation_report, False)
        
        # Check if output file was saved
        with patch('builtins.open') as mock_open:
            with patch('json.dump') as mock_dump:
                cmd_validate(args)
                mock_open.assert_called_once()
                mock_dump.assert_called_once()
        
        # Test verbose output
        args.verbose = True
        with patch('focus_forge.cli_integration.print_report') as mock_print_report:
            cmd_validate(args)
            mock_print_report.assert_called_once_with(validation_report, True)
        
        # Test error handling
        mock_validate.return_value = {"status": "error", "tasks_valid": False, "error_message": "Test error"}
        with patch('focus_forge.cli_integration.print_report') as mock_print_report:
            cmd_validate(args)
            mock_print_report.assert_called_once_with({"status": "error", "tasks_valid": False, "error_message": "Test error"}, True)
        
        # Test fail flag
        args.fail = True
        with patch('sys.exit') as mock_exit:
            cmd_validate(args)
            mock_exit.assert_called_once_with(1)


if __name__ == "__main__":
    unittest.main() 