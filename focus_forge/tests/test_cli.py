"""Tests for CLI commands in Focus Forge."""

import json
import os
from pathlib import Path
from unittest.mock import patch, MagicMock
import sys

import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

# Add parent directory to path so we can import focus_forge module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the CLI commands to test
from focus_forge import cli, load_tasks, TASKS_FILE, CONFIG_FILE, TASKS_DIR


def test_init_command(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>, temp_dir: Path):
    """Test the init command."""
    # Mock user input
    with patch('click.prompt', side_effect=["Test Project", "0.1.0", "test-api-key"]):
        result = runner.invoke(cli, ["init"])
    
    assert result.exit_code == 0
    assert "Project initialized successfully" in result.output
    
    # Check that files were created
    assert Path(TASKS_FILE).exists()
    assert Path(CONFIG_FILE).exists()
    assert Path(TASKS_DIR).exists()
    
    # Check tasks.json content
    tasks = load_tasks()
    assert len(tasks) == 1
    assert tasks[0]["id"] == "1"
    assert tasks[0]["title"] == "Initialize project"


def test_list_command(runner: CliRunner, temp_dir: Path, setup_tasks_file: Path, sample_tasks: list):
    """Test the list command."""
    result = runner.invoke(cli, ["list"])
    
    assert result.exit_code == 0
    
    # Check that task titles are in the output
    for task in sample_tasks:
        assert task["title"] in result.output


def test_list_command_no_tasks(runner: CliRunner, temp_dir: Path):
    """Test the list command when no tasks exist."""
    result = runner.invoke(cli, ["list"])
    
    assert result.exit_code == 0
    assert "No tasks found" in result.output


def test_show_command(runner: CliRunner, temp_dir: Path, setup_tasks_file: Path, sample_tasks: list):
    """Test the show command."""
    result = runner.invoke(cli, ["show", "2"])
    
    assert result.exit_code == 0
    assert "Task 2: Implement core functionality" in result.output
    assert "Implementation Details" in result.output
    assert "Dependencies" in result.output
    assert "Task 1" in result.output  # Task 2 depends on Task 1


def test_show_command_subtask(runner: CliRunner, temp_dir: Path, setup_tasks_file: Path):
    """Test the show command with a subtask."""
    result = runner.invoke(cli, ["show", "3.1"])
    
    assert result.exit_code == 0
    assert "3.1" in result.output
    assert "Design UI layout" in result.output


def test_show_command_nonexistent(runner: CliRunner, temp_dir: Path, setup_tasks_file: Path):
    """Test the show command with a non-existent task."""
    result = runner.invoke(cli, ["show", "999"])
    
    assert result.exit_code == 0
    assert "Task 999 not found" in result.output


def test_next_command(runner: CliRunner, temp_dir: Path, setup_tasks_file: Path):
    """Test the next command."""
    result = runner.invoke(cli, ["next"])
    
    assert result.exit_code == 0
    assert "Task 1: Initialize project" in result.output  # Task 1 should be next
    assert "Suggested Actions" in result.output


def test_set_status_command(runner: CliRunner, temp_dir: Path, setup_tasks_file: Path):
    """Test the set-status command."""
    result = runner.invoke(cli, ["set-status", "--id=1", "--status=done"])
    
    assert result.exit_code == 0
    assert "Updated task 1 status" in result.output
    
    # Verify task status was updated
    tasks = load_tasks()
    task = next((t for t in tasks if t["id"] == "1"), None)
    assert task is not None
    assert task["status"] == "done"


def test_set_status_parent_affects_subtasks(runner: CliRunner, temp_dir: Path, setup_tasks_file: Path):
    """Test that setting a parent task to done also sets subtasks to done."""
    result = runner.invoke(cli, ["set-status", "--id=3", "--status=done"])
    
    assert result.exit_code == 0
    
    # Verify subtasks status was updated
    tasks = load_tasks()
    task = next((t for t in tasks if t["id"] == "3"), None)
    assert task is not None
    
    for subtask in task["subtasks"]:
        assert subtask["status"] == "done"


def test_generate_command(runner: CliRunner, temp_dir: Path, setup_tasks_file: Path):
    """Test the generate command."""
    # Remove tasks directory if it exists
    if Path(TASKS_DIR).exists():
        for file in Path(TASKS_DIR).iterdir():
            file.unlink()
        Path(TASKS_DIR).rmdir()
    
    result = runner.invoke(cli, ["generate"])
    
    assert result.exit_code == 0
    assert "Successfully generated" in result.output
    
    # Verify task files were created
    assert Path(TASKS_DIR).exists()
    task_files = list(Path(TASKS_DIR).glob("task_*.txt"))
    assert len(task_files) == 3  # We have 3 sample tasks


def test_validate_dependencies_command(runner: CliRunner, temp_dir: Path, setup_tasks_file: Path):
    """Test the validate-dependencies command."""
    result = runner.invoke(cli, ["validate-dependencies"])
    
    assert result.exit_code == 0
    assert "All dependencies are valid" in result.output


def test_add_dependency_command(runner: CliRunner, temp_dir: Path, setup_tasks_file: Path):
    """Test the add-dependency command."""
    # Task 3 already depends on 2, add dependency on 1
    result = runner.invoke(cli, ["add-dependency", "--id=3", "--depends-on=1"])
    
    assert result.exit_code == 0
    assert "Added dependency" in result.output
    
    # Verify dependency was added
    tasks = load_tasks()
    task = next((t for t in tasks if t["id"] == "3"), None)
    assert task is not None
    assert "1" in task["dependencies"]
    assert "2" in task["dependencies"]  # Original dependency still exists


def test_remove_dependency_command(runner: CliRunner, temp_dir: Path, setup_tasks_file: Path):
    """Test the remove-dependency command."""
    result = runner.invoke(cli, ["remove-dependency", "--id=3", "--depends-on=2"])
    
    assert result.exit_code == 0
    assert "Removed dependency" in result.output
    
    # Verify dependency was removed
    tasks = load_tasks()
    task = next((t for t in tasks if t["id"] == "3"), None)
    assert task is not None
    assert "2" not in task["dependencies"]


def test_clear_subtasks_command(runner: CliRunner, temp_dir: Path, setup_tasks_file: Path):
    """Test the clear-subtasks command."""
    result = runner.invoke(cli, ["clear-subtasks", "--id=3"])
    
    assert result.exit_code == 0
    assert "Cleared subtasks" in result.output
    
    # Verify subtasks were cleared
    tasks = load_tasks()
    task = next((t for t in tasks if t["id"] == "3"), None)
    assert task is not None
    assert len(task["subtasks"]) == 0


def test_readme_command(runner: CliRunner):
    """Test the readme command."""
    result = runner.invoke(cli, ["readme"])
    
    assert result.exit_code == 0
    assert "FOCUS FORGE" in result.output
    assert "USAGE" in result.output
    assert "Initialize a new project" in result.output


def test_nix_config_command(runner: CliRunner, temp_dir: Path):
    """Test the nix-config command."""
    result = runner.invoke(cli, ["nix-config"])
    
    assert result.exit_code == 0
    assert "Generated nix-shell configuration" in result.output
    
    # Verify nix config was created
    assert Path("default.nix").exists()
    
    # Verify content
    with open("default.nix", 'r') as f:
        content = f.read()
    
    assert "pkgs.mkShell" in content
    assert "python310" in content
    assert "focus-forge" in content