"""
Tests for the task format upgrader system.
"""

import json
import os
import tempfile
import shutil
from pathlib import Path
import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime

from focus_forge.task_upgrader import (
    TaskUpgrader,
    register_schema_version,
    register_upgrader,
    register_validator,
    SCHEMA_VERSIONS,
    UPGRADERS,
    VALIDATORS,
)

# Test data for task format versions
TEST_DATA_V0_7_0 = {
    "version": "0.7.0",
    "updated_at": "2025-03-25T11:54:03.487958",
    "tasks": [
        {
            "id": "T1",
            "title": "Sample Task",
            "description": "A sample task for testing",
            "status": "pending",
            "priority": "medium",
            "dependencies": [],
            "details": "Test details",
            "test_strategy": "Sample test strategy",
            "subtasks": [
                {
                    "id": "T1.1",
                    "title": "Sample Subtask",
                    "description": "A sample subtask for testing",
                    "status": "pending"
                }
            ]
        }
    ]
}

TEST_DATA_V0_8_0 = {
    "version": "0.8.0",
    "updated_at": "2025-03-25T11:54:03.487958",
    "tasks": [
        {
            "id": "T1",
            "title": "Sample Task",
            "description": "A sample task for testing",
            "status": "pending",
            "priority": "medium",
            "dependencies": [],
            "details": "Test details",
            "test_strategy": "Sample test strategy",
            "subtasks": [
                {
                    "id": "T1.1",
                    "title": "Sample Subtask",
                    "description": "A sample subtask for testing",
                    "status": "pending"
                }
            ]
        }
    ]
}


# Mock validators and upgraders for testing
def mock_validate_v0_7_0(data):
    """Mock validator for v0.7.0"""
    if "version" not in data or data["version"] != "0.7.0" or "tasks" not in data:
        return False, ["Invalid format"]
    return True, []


def mock_validate_v0_8_0(data):
    """Mock validator for v0.8.0"""
    if "version" not in data or data["version"] != "0.8.0" or "tasks" not in data:
        return False, ["Invalid format"]
    return True, []


def mock_upgrade_v0_7_0_to_v0_8_0(data):
    """Mock upgrader from v0.7.0 to v0.8.0"""
    new_data = data.copy()
    new_data["version"] = "0.8.0"
    return new_data


@pytest.fixture
def setup_test_upgrader():
    """Setup test validators and upgraders"""
    # Clear registries
    SCHEMA_VERSIONS.clear()
    UPGRADERS.clear()
    VALIDATORS.clear()
    
    # Register test versions, validators, and upgraders
    register_schema_version("0.7.0")
    register_schema_version("0.8.0")
    register_validator("0.7.0", mock_validate_v0_7_0)
    register_validator("0.8.0", mock_validate_v0_8_0)
    register_upgrader("0.7.0", "0.8.0", mock_upgrade_v0_7_0_to_v0_8_0)
    
    yield
    
    # Cleanup
    SCHEMA_VERSIONS.clear()
    UPGRADERS.clear()
    VALIDATORS.clear()


@pytest.fixture
def mock_tasks_file():
    """Create a temporary tasks file for testing"""
    temp_dir = tempfile.mkdtemp()
    temp_file = Path(temp_dir) / "tasks.json"
    
    with open(temp_file, 'w') as f:
        json.dump(TEST_DATA_V0_7_0, f)
    
    yield temp_file
    
    # Cleanup
    shutil.rmtree(temp_dir)


class TestSchemaVersioning:
    """Tests for schema versioning functionality"""
    
    def test_register_schema_version(self):
        """Test registering a schema version"""
        SCHEMA_VERSIONS.clear()
        register_schema_version("1.0.0")
        assert "1.0.0" in SCHEMA_VERSIONS
        
        # Test sorting
        register_schema_version("0.5.0")
        register_schema_version("2.0.0")
        assert SCHEMA_VERSIONS == ["0.5.0", "1.0.0", "2.0.0"]
    
    def test_register_validator(self):
        """Test registering a validator"""
        VALIDATORS.clear()
        SCHEMA_VERSIONS.clear()
        
        mock_validator = lambda data: (True, [])
        register_validator("1.0.0", mock_validator)
        
        assert "1.0.0" in VALIDATORS
        assert "1.0.0" in SCHEMA_VERSIONS
        assert VALIDATORS["1.0.0"] == mock_validator
    
    def test_register_upgrader(self):
        """Test registering an upgrader"""
        UPGRADERS.clear()
        SCHEMA_VERSIONS.clear()
        
        mock_upgrader = lambda data: data
        register_upgrader("1.0.0", "2.0.0", mock_upgrader)
        
        key = "1.0.0->2.0.0"
        assert key in UPGRADERS
        assert "1.0.0" in SCHEMA_VERSIONS
        assert "2.0.0" in SCHEMA_VERSIONS
        assert UPGRADERS[key] == mock_upgrader


class TestTaskUpgrader:
    """Tests for the TaskUpgrader class"""
    
    def test_detect_version(self):
        """Test detecting version from task data"""
        # Test with valid version
        data = {"version": "1.2.3", "other_field": "value"}
        assert TaskUpgrader.detect_version(data) == "1.2.3"
        
        # Test with missing version
        data = {"other_field": "value"}
        with pytest.raises(ValueError):
            TaskUpgrader.detect_version(data)
    
    def test_validate(self, setup_test_upgrader):
        """Test validating task data"""
        # Test valid data
        is_valid, errors = TaskUpgrader.validate(TEST_DATA_V0_7_0)
        assert is_valid is True
        assert errors == []
        
        # Test invalid data
        invalid_data = {"version": "0.7.0"}  # Missing tasks field
        is_valid, errors = TaskUpgrader.validate(invalid_data)
        assert is_valid is False
        assert len(errors) > 0
        
        # Test with unknown version
        unknown_data = {"version": "999.0.0"}
        with pytest.raises(ValueError):
            TaskUpgrader.detect_version(unknown_data)
    
    def test_get_upgrade_path(self, setup_test_upgrader):
        """Test getting an upgrade path"""
        # Test valid upgrade path
        path = TaskUpgrader.get_upgrade_path("0.7.0", "0.8.0")
        assert path == [("0.7.0", "0.8.0")]
        
        # Test no upgrade needed
        path = TaskUpgrader.get_upgrade_path("0.7.0", "0.7.0")
        assert path == []
        
        # Test invalid downgrade
        with pytest.raises(ValueError):
            TaskUpgrader.get_upgrade_path("0.8.0", "0.7.0")
        
        # Test unknown version
        with pytest.raises(ValueError):
            TaskUpgrader.get_upgrade_path("0.7.0", "999.0.0")
    
    def test_upgrade(self, setup_test_upgrader):
        """Test upgrading task data"""
        # Test successful upgrade
        upgraded_data = TaskUpgrader.upgrade(TEST_DATA_V0_7_0, "0.8.0")
        assert upgraded_data["version"] == "0.8.0"
        
        # Test no upgrade needed
        upgraded_data = TaskUpgrader.upgrade(TEST_DATA_V0_8_0, "0.8.0")
        assert upgraded_data["version"] == "0.8.0"
        
        # Test upgrade to latest (when 0.8.0 is latest)
        upgraded_data = TaskUpgrader.upgrade(TEST_DATA_V0_7_0)
        assert upgraded_data["version"] == "0.8.0"


class TestTaskUpgraderCLI:
    """Tests for the TaskUpgrader CLI functionality"""
    
    @patch("focus_forge.task_upgrader.TASKS_FILE", autospec=True)
    def test_backup_tasks_file(self, mock_tasks_file, setup_test_upgrader):
        """Test backing up tasks file"""
        with patch("focus_forge.task_upgrader.Path.exists", return_value=True), \
             patch("focus_forge.task_upgrader.shutil.copy2") as mock_copy, \
             patch("focus_forge.task_upgrader.datetime") as mock_datetime:
            
            # Setup mock
            mock_datetime.now.return_value.strftime.return_value = "20250325_123456"
            
            # Call the method
            TaskUpgrader.backup_tasks_file()
            
            # Verify the copy was called with correct paths
            mock_copy.assert_called_once()
            args = mock_copy.call_args[0]
            assert str(args[0]).endswith("tasks.json")
            assert str(args[1]).endswith("tasks.20250325_123456.backup")
    
    @patch("focus_forge.task_upgrader.TASKS_FILE")
    def test_upgrade_tasks_file(self, mock_tasks_file_path, setup_test_upgrader, mock_tasks_file):
        """Test upgrading tasks file"""
        mock_tasks_file_path.return_value = str(mock_tasks_file)
        
        with patch("focus_forge.task_upgrader.Path.exists", return_value=True), \
             patch("focus_forge.task_upgrader.TaskUpgrader.backup_tasks_file") as mock_backup, \
             patch("focus_forge.task_upgrader.open", create=True) as mock_open, \
             patch("json.load", return_value=TEST_DATA_V0_7_0), \
             patch("json.dump") as mock_dump:
            
            # Setup mock for open
            mock_open.return_value.__enter__.return_value = MagicMock()
            
            # Call the method
            TaskUpgrader.upgrade_tasks_file("0.8.0")
            
            # Verify backup was called
            mock_backup.assert_called_once()
            
            # Verify json.dump was called with upgraded data
            mock_dump.assert_called_once()
            args = mock_dump.call_args[0]
            assert args[0]["version"] == "0.8.0"


class TestAutoUpgrade:
    """Tests for auto-upgrade functionality"""
    
    def test_check_version_and_prompt_upgrade(self, setup_test_upgrader):
        """Test checking version and auto-upgrading if needed"""
        # Test with same version (no upgrade needed)
        data = {"version": "0.8.0", "tasks": []}
        result = TaskUpgrader.check_version_and_prompt_upgrade(data, "0.8.0")
        assert result == data
        
        # Test with older version that needs upgrade
        data = {"version": "0.7.0", "tasks": []}
        result = TaskUpgrader.check_version_and_prompt_upgrade(data, "0.8.0")
        assert result["version"] == "0.8.0"
        
        # Test with unknown version
        data = {"version": "999.0.0", "tasks": []}
        result = TaskUpgrader.check_version_and_prompt_upgrade(data, "0.8.0")
        assert result == data
    
    def test_needs_upgrade(self, setup_test_upgrader):
        """Test checking if upgrade is needed"""
        # Test with upgrade needed
        assert TaskUpgrader.needs_upgrade("0.7.0", "0.8.0") is True
        
        # Test with same version (no upgrade needed)
        assert TaskUpgrader.needs_upgrade("0.8.0", "0.8.0") is False
        
        # Test with downgrade (no upgrade needed)
        assert TaskUpgrader.needs_upgrade("0.8.0", "0.7.0") is False
        
        # Test with unknown versions
        assert TaskUpgrader.needs_upgrade("0.7.0", "999.0.0") is False
        assert TaskUpgrader.needs_upgrade("999.0.0", "0.8.0") is False
    
    def test_auto_upgrade_tasks_decorator(self, setup_test_upgrader):
        """Test the auto_upgrade_tasks decorator"""
        from focus_forge.task_upgrader import auto_upgrade_tasks
        
        # Create a mock load function
        def mock_load():
            return {"version": "0.7.0", "tasks": []}
        
        # Apply the decorator
        decorated_load = auto_upgrade_tasks(mock_load)
        
        # Test the decorated function
        with patch('focus_forge.task_upgrader.VERSION', "0.8.0"):
            result = decorated_load()
            assert result["version"] == "0.8.0"
        
        # Test with empty data
        def mock_load_empty():
            return None
        
        decorated_load_empty = auto_upgrade_tasks(mock_load_empty)
        result = decorated_load_empty()
        assert result is None
        
        # Test with error during upgrade
        def mock_load_error():
            return {"version": "0.7.0", "tasks": []}
        
        decorated_load_error = auto_upgrade_tasks(mock_load_error)
        with patch('focus_forge.task_upgrader.TaskUpgrader.check_version_and_prompt_upgrade', 
                   side_effect=Exception("Test error")):
            result = decorated_load_error()
            assert result["version"] == "0.7.0"  # Original data returned


class TestBackupAndRestore:
    """Tests for backup and restore functionality"""
    
    def test_get_available_backups(self, mock_tasks_file, setup_test_upgrader):
        """Test getting available backups"""
        # Create some mock backup files
        temp_dir = Path(mock_tasks_file).parent
        
        # Create backup files with the expected pattern
        backup_file1 = temp_dir / f"tasks.20250325_120000.json.backup"
        backup_file2 = temp_dir / f"tasks.20250325_120100.json.backup"
        
        # Write some sample data
        with open(backup_file1, 'w') as f:
            json.dump(TEST_DATA_V0_7_0, f)
        
        with open(backup_file2, 'w') as f:
            json.dump(TEST_DATA_V0_8_0, f)
        
        # Patch TASKS_FILE to point to the mock file
        with patch('focus_forge.task_upgrader.TASKS_FILE', str(mock_tasks_file)):
            # Get available backups
            backups = TaskUpgrader.get_available_backups()
            
            # Check that the correct number of backups was found
            assert len(backups) == 2
            
            # Verify backup details
            assert any(b["filename"] == "tasks.20250325_120000.json.backup" for b in backups)
            assert any(b["filename"] == "tasks.20250325_120100.json.backup" for b in backups)
            
            # Check version detection
            for backup in backups:
                if "120000" in backup["filename"]:
                    assert backup["version"] == "0.7.0"
                elif "120100" in backup["filename"]:
                    assert backup["version"] == "0.8.0"
    
    def test_restore_from_backup(self, mock_tasks_file, setup_test_upgrader):
        """Test restoring from a backup"""
        # Create a backup file
        temp_dir = Path(mock_tasks_file).parent
        backup_file = temp_dir / "tasks.backup"
        
        # Write test data to the backup
        with open(backup_file, 'w') as f:
            json.dump(TEST_DATA_V0_7_0, f)
        
        # Patch functions to avoid file system operations
        with patch('focus_forge.task_upgrader.TASKS_FILE', str(mock_tasks_file)), \
             patch('focus_forge.task_upgrader.Path.exists', return_value=True), \
             patch('focus_forge.task_upgrader.shutil.copy2') as mock_copy, \
             patch('focus_forge.task_upgrader.TaskUpgrader.record_upgrade') as mock_record, \
             patch('focus_forge.task_upgrader.open', create=True) as mock_open, \
             patch('json.load', return_value=TEST_DATA_V0_7_0):
            
            # Setup mock for open
            mock_file = MagicMock()
            mock_file.__enter__.return_value = mock_file
            mock_open.return_value = mock_file
            
            # Call the method
            data = TaskUpgrader.restore_from_backup(str(backup_file))
            
            # Verify the restore process
            assert mock_copy.call_count == 2  # Pre-restore backup + actual restore
            assert mock_record.call_count == 1
            assert data == TEST_DATA_V0_7_0
    
    def test_record_upgrade(self, setup_test_upgrader):
        """Test recording an upgrade in the history"""
        # Patch functions to avoid file system operations
        with patch('focus_forge.task_upgrader.Path.exists', return_value=False), \
             patch('focus_forge.task_upgrader.open', create=True) as mock_open, \
             patch('json.load', return_value={"upgrades": []}), \
             patch('json.dump') as mock_dump, \
             patch('uuid.uuid4', return_value="test-uuid"):
            
            # Setup mock for open
            mock_file = MagicMock()
            mock_file.__enter__.return_value = mock_file
            mock_open.return_value = mock_file
            
            # Call the method
            upgrade_id = TaskUpgrader.record_upgrade("0.7.0", "0.8.0", Path("backup.file"))
            
            # Verify the record was created
            assert upgrade_id == "test-uuid"
            assert mock_dump.call_count == 1
            
            # Check the content of the record
            args = mock_dump.call_args[0]
            history = args[0]
            assert "upgrades" in history
            assert len(history["upgrades"]) == 1
            
            record = history["upgrades"][0]
            assert record["id"] == "test-uuid"
            assert record["from_version"] == "0.7.0"
            assert record["to_version"] == "0.8.0"
            assert record["backup_path"] == "backup.file"
    
    def test_get_upgrade_history(self, setup_test_upgrader):
        """Test getting upgrade history"""
        test_history = {
            "upgrades": [
                {
                    "id": "test1",
                    "timestamp": "2025-03-25T12:00:00",
                    "from_version": "0.7.0",
                    "to_version": "0.8.0",
                    "backup_path": "backup1.file"
                },
                {
                    "id": "test2",
                    "timestamp": "2025-03-25T12:30:00",
                    "from_version": "restore",
                    "to_version": "0.7.0",
                    "backup_path": "backup2.file"
                }
            ]
        }
        
        # Patch functions to avoid file system operations
        with patch('focus_forge.task_upgrader.Path.exists', return_value=True), \
             patch('focus_forge.task_upgrader.open', create=True) as mock_open, \
             patch('json.load', return_value=test_history):
            
            # Setup mock for open
            mock_file = MagicMock()
            mock_file.__enter__.return_value = mock_file
            mock_open.return_value = mock_file
            
            # Call the method
            history = TaskUpgrader.get_upgrade_history()
            
            # Verify the history
            assert len(history) == 2
            assert history[0]["id"] == "test1"
            assert history[1]["id"] == "test2" 