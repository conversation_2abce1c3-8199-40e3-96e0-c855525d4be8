"""Tests for the document discovery functionality."""

import os
import json
import tempfile
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

import pytest
import sys

# Add parent directory to path so we can import focus_forge module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from focus_forge.doc_discovery import (
    Document,
    DocumentType,
    GuidanceSystem,
    GuidanceSystemType,
    DocumentCache,
    DocDiscovery,
    discover_docs,
    analyze_guidance,
    cache_docs,
    query_cache
)


# Test Fixtures

@pytest.fixture
def temp_project_dir():
    """Create a temporary project directory with some basic files."""
    with tempfile.TemporaryDirectory() as tmpdirname:
        # Create project structure
        project_dir = Path(tmpdirname)
        
        # Create standard docs
        readme = project_dir / "README.md"
        readme.write_text("# Test Project\n\nThis is a test project README.")
        
        changelog = project_dir / "CHANGELOG.md"
        changelog.write_text("# Changelog\n\n## v0.1.0\n\n- Initial release")
        
        # Create docs directory
        docs_dir = project_dir / "docs"
        docs_dir.mkdir(exist_ok=True)
        
        # Create PRD
        prd = docs_dir / "PRD-v1.md"
        prd.write_text("# Product Requirements\n\n## Features\n\n- Feature 1\n- Feature 2")
        
        # Create architecture doc
        arch = docs_dir / "architecture.md"
        arch.write_text("# System Architecture\n\n## Components\n\n- Component 1\n- Component 2")
        
        # Save the current directory
        old_dir = os.getcwd()
        os.chdir(project_dir)
        
        yield project_dir
        
        # Restore the original directory
        os.chdir(old_dir)


@pytest.fixture
def temp_ai_dir(temp_project_dir):
    """Create a temporary AI guidance structure."""
    project_dir = temp_project_dir
    
    # Create .ai directory
    ai_dir = project_dir / ".ai"
    ai_dir.mkdir(exist_ok=True)
    
    # Create structure file
    structure = ai_dir / "STRUCTURE.md"
    structure.write_text("# Project Structure\n\nThis is the structure file.")
    
    # Create docs directory
    docs_dir = ai_dir / "docs"
    docs_dir.mkdir(exist_ok=True)
    
    # Create context directory
    context_dir = docs_dir / "1-context"
    context_dir.mkdir(exist_ok=True)
    
    # Create technical design directory
    design_dir = docs_dir / "2-technical-design"
    design_dir.mkdir(exist_ok=True)
    
    # Create development directory
    dev_dir = docs_dir / "3-development"
    dev_dir.mkdir(exist_ok=True)
    
    # Create acceptance directory
    accept_dir = docs_dir / "4-acceptance"
    accept_dir.mkdir(exist_ok=True)
    
    # Add some files
    context_file = context_dir / "context.md"
    context_file.write_text("# Project Context\n\nThis is the context file.")
    
    design_file = design_dir / "design.md"
    design_file.write_text("# Technical Design\n\nThis is the design file.")
    
    return ai_dir


# Unit Tests

class TestDocument:
    """Tests for the Document class."""
    
    def test_init(self, temp_project_dir):
        """Test initializing a Document."""
        readme_path = temp_project_dir / "README.md"
        doc = Document(readme_path)
        
        assert doc.path == readme_path
        assert doc.doc_type == DocumentType.UNKNOWN
        assert doc.confidence == 0.0
        assert doc.size > 0
        assert doc.modified is not None
    
    def test_extract_title(self, temp_project_dir):
        """Test extracting title from a document."""
        readme_path = temp_project_dir / "README.md"
        doc = Document(readme_path)
        
        assert doc.title == "Test Project"
    
    def test_read_content(self, temp_project_dir):
        """Test reading document content."""
        readme_path = temp_project_dir / "README.md"
        doc = Document(readme_path)
        
        content = doc.read_content()
        assert "This is a test project README" in content
    
    def test_extract_summary(self, temp_project_dir):
        """Test extracting summary from a document."""
        readme_path = temp_project_dir / "README.md"
        doc = Document(readme_path)
        doc.read_content()
        
        summary = doc.extract_summary()
        assert "This is a test project README" in summary
    
    def test_to_dict(self, temp_project_dir):
        """Test converting document to dictionary."""
        readme_path = temp_project_dir / "README.md"
        doc = Document(readme_path, doc_type=DocumentType.README, confidence=1.0)
        doc.read_content()
        
        # Without content
        doc_dict = doc.to_dict()
        assert doc_dict["path"] == str(readme_path)
        assert doc_dict["type"] == DocumentType.README
        assert doc_dict["confidence"] == 1.0
        assert "content" not in doc_dict
        
        # With content
        doc_dict = doc.to_dict(include_content=True)
        assert "content" in doc_dict
        assert "This is a test project README" in doc_dict["content"]


class TestGuidanceSystem:
    """Tests for the GuidanceSystem class."""
    
    def test_init(self):
        """Test initializing a GuidanceSystem."""
        gs = GuidanceSystem()
        assert gs.system_type == GuidanceSystemType.UNKNOWN
        assert gs.confidence == 0.0
        assert gs.path is None
        
        gs = GuidanceSystem(path="/path/to/ai", system_type=GuidanceSystemType.BUTTERBOT, confidence=0.8)
        assert gs.system_type == GuidanceSystemType.BUTTERBOT
        assert gs.confidence == 0.8
        assert str(gs.path) == "/path/to/ai"
    
    def test_to_dict(self):
        """Test converting guidance system to dictionary."""
        gs = GuidanceSystem(path="/path/to/ai", system_type=GuidanceSystemType.BUTTERBOT, confidence=0.8)
        gs.completeness = 0.7
        gs.markers = ["STRUCTURE.md", "Directory: 1-context"]
        gs.missing = ["Directory: 4-acceptance"]
        
        gs_dict = gs.to_dict()
        assert gs_dict["path"] == "/path/to/ai"
        assert gs_dict["type"] == GuidanceSystemType.BUTTERBOT
        assert gs_dict["confidence"] == 0.8
        assert gs_dict["completeness"] == 0.7
        assert len(gs_dict["markers"]) == 2
        assert len(gs_dict["missing"]) == 1


class TestDocumentCache:
    """Tests for the DocumentCache class."""
    
    def test_init(self, temp_project_dir):
        """Test initializing a DocumentCache."""
        cache = DocumentCache(temp_project_dir)
        assert cache.project_dir == temp_project_dir
        assert len(cache.documents) == 0
        assert cache.guidance_system is None
    
    def test_save_and_load(self, temp_project_dir):
        """Test saving and loading the cache."""
        cache = DocumentCache(temp_project_dir)
        
        # Add some documents
        readme_doc = Document(temp_project_dir / "README.md", 
                             doc_type=DocumentType.README, confidence=1.0)
        readme_doc.read_content()
        
        changelog_doc = Document(temp_project_dir / "CHANGELOG.md",
                                doc_type=DocumentType.CHANGELOG, confidence=1.0)
        
        cache.add_document(readme_doc)
        cache.add_document(changelog_doc)
        
        # Add guidance system
        gs = GuidanceSystem(path=temp_project_dir / ".ai",
                           system_type=GuidanceSystemType.BUTTERBOT,
                           confidence=0.8)
        cache.set_guidance_system(gs)
        
        # Save the cache
        cache.save()
        
        # Verify cache file was created
        cache_dir = temp_project_dir / ".focus_forge"
        cache_file = cache_dir / "docs_cache.json"
        assert cache_file.exists()
        
        # Create new cache and load it
        new_cache = DocumentCache(temp_project_dir)
        assert new_cache.load()
        
        # Verify contents
        assert len(new_cache.documents) == 2
        assert new_cache.documents[0].doc_type == DocumentType.README
        assert new_cache.guidance_system is not None
        assert new_cache.guidance_system.system_type == GuidanceSystemType.BUTTERBOT
    
    def test_is_valid(self, temp_project_dir):
        """Test cache validity check."""
        cache = DocumentCache(temp_project_dir)
        
        # No last_updated, should be invalid
        assert not cache.is_valid()
        
        # Set last_updated to now, should be valid
        cache.last_updated = datetime.now()
        assert cache.is_valid()
        
        # Set last_updated to old date, should be invalid
        cache.last_updated = datetime.now() - timedelta(hours=25)
        assert not cache.is_valid()


class TestDocDiscovery:
    """Tests for the DocDiscovery class."""
    
    def test_discover_standard_docs(self, temp_project_dir):
        """Test discovering standard docs."""
        discovery = DocDiscovery(temp_project_dir)
        discovery._discover_standard_docs()
        
        assert len(discovery.discovered_docs) == 2
        doc_paths = [str(doc.path) for doc in discovery.discovered_docs]
        assert str(temp_project_dir / "README.md") in doc_paths
        assert str(temp_project_dir / "CHANGELOG.md") in doc_paths
    
    def test_discover_in_directory(self, temp_project_dir):
        """Test discovering docs in a directory."""
        discovery = DocDiscovery(temp_project_dir)
        docs_dir = temp_project_dir / "docs"
        discovery._discover_in_directory(docs_dir)
        
        assert len(discovery.discovered_docs) == 2
        doc_paths = [str(doc.path) for doc in discovery.discovered_docs]
        assert str(docs_dir / "PRD-v1.md") in doc_paths
        assert str(docs_dir / "architecture.md") in doc_paths
    
    def test_analyze_butterbot_guidance(self, temp_ai_dir):
        """Test analyzing Butterbot guidance."""
        discovery = DocDiscovery(temp_ai_dir.parent)
        discovery.guidance_system = GuidanceSystem()
        discovery._analyze_butterbot_guidance(temp_ai_dir)
        
        assert discovery.guidance_system.system_type == GuidanceSystemType.BUTTERBOT
        assert discovery.guidance_system.confidence > 0.5
        assert "STRUCTURE.md" in discovery.guidance_system.markers
    
    def test_classify_document(self, temp_project_dir):
        """Test document classification."""
        discovery = DocDiscovery(temp_project_dir)
        
        # Test README classification
        readme_doc = Document(temp_project_dir / "README.md")
        discovery._classify_document(readme_doc)
        assert readme_doc.doc_type == DocumentType.README
        assert readme_doc.confidence == 1.0
        
        # Test PRD classification
        prd_doc = Document(temp_project_dir / "docs/PRD-v1.md")
        discovery._classify_document(prd_doc)
        assert prd_doc.doc_type == DocumentType.REQUIREMENTS
        assert prd_doc.confidence > 0.8
        
        # Test architecture doc classification
        arch_doc = Document(temp_project_dir / "docs/architecture.md")
        discovery._classify_document(arch_doc)
        assert arch_doc.doc_type == DocumentType.ARCHITECTURE
        assert arch_doc.confidence > 0.7


# Integration Tests

def test_discover_documents_integration(temp_project_dir, temp_ai_dir):
    """Integration test for document discovery."""
    # Discovery without AI folder
    docs = discover_docs(temp_project_dir, output_format="dict")
    assert len(docs["documents"]) >= 4  # README, CHANGELOG, PRD, architecture
    
    # Discovery with AI folder
    docs = discover_docs(temp_project_dir, output_format="dict")
    assert docs["guidance_system"] is not None
    assert docs["guidance_system"]["type"] == GuidanceSystemType.BUTTERBOT


def test_analyze_guidance_integration(temp_project_dir, temp_ai_dir):
    """Integration test for guidance analysis."""
    # Analyze a specific AI folder
    result = analyze_guidance(temp_project_dir, path=temp_ai_dir)
    assert result["type"] == GuidanceSystemType.BUTTERBOT
    assert result["confidence"] > 0.5
    assert "STRUCTURE.md" in result["markers"]


def test_cache_workflow_integration(temp_project_dir, temp_ai_dir):
    """Integration test for the cache workflow."""
    # Generate cache
    cache_path = cache_docs(temp_project_dir, include_content=True)
    assert os.path.exists(cache_path)
    
    # Query the cache
    result = query_cache(temp_project_dir, format="dict")
    assert len(result["documents"]) > 0
    
    # Query by type
    result = query_cache(temp_project_dir, doc_type=DocumentType.README, format="dict")
    assert len(result["documents"]) == 1
    assert result["documents"][0]["type"] == DocumentType.README


# CLI command tests

def test_discover_docs_cmd(temp_project_dir):
    """Test discover-docs CLI command."""
    from focus_forge.cli_extensions import discover_docs_cmd
    
    runner = click.testing.CliRunner()
    with runner.isolated_filesystem():
        # Test without output file
        result = runner.invoke(discover_docs_cmd, ["--depth=2"])
        assert result.exit_code == 0
        
        # Test with output file
        result = runner.invoke(discover_docs_cmd, ["--depth=2", "--output=docs.json"])
        assert result.exit_code == 0
        assert os.path.exists("docs.json")


def test_analyze_guidance_cmd(temp_project_dir, temp_ai_dir):
    """Test analyze-guidance CLI command."""
    from focus_forge.cli_extensions import analyze_guidance_cmd
    
    runner = click.testing.CliRunner()
    with runner.isolated_filesystem():
        # Test without output file
        result = runner.invoke(analyze_guidance_cmd, ["--path", str(temp_ai_dir)])
        assert result.exit_code == 0
        
        # Test with output file
        result = runner.invoke(analyze_guidance_cmd, 
                              ["--path", str(temp_ai_dir), "--output=analysis.json"])
        assert result.exit_code == 0
        assert os.path.exists("analysis.json")


def test_cache_docs_cmd(temp_project_dir):
    """Test cache-docs CLI command."""
    from focus_forge.cli_extensions import cache_docs_cmd
    
    runner = click.testing.CliRunner()
    with runner.isolated_filesystem():
        result = runner.invoke(cache_docs_cmd, ["--include-content"])
        assert result.exit_code == 0


def test_extract_doc_cmd(temp_project_dir):
    """Test extract-doc CLI command."""
    from focus_forge.cli_extensions import extract_doc_cmd
    
    runner = click.testing.CliRunner()
    with runner.isolated_filesystem():
        # Test markdown format
        result = runner.invoke(extract_doc_cmd, 
                              ["--path", str(temp_project_dir / "README.md"), 
                               "--format=markdown"])
        assert result.exit_code == 0
        assert "Test Project" in result.output
        
        # Test JSON format
        result = runner.invoke(extract_doc_cmd, 
                              ["--path", str(temp_project_dir / "README.md"), 
                               "--format=json"])
        assert result.exit_code == 0
        assert "Test Project" in result.output 