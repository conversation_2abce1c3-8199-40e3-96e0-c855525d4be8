#!/usr/bin/env python3
"""
Test cases for the component detection and grouping module.

This test suite verifies that the component detection module can correctly:
1. Identify different types of components in PRD text
2. Group related components appropriately
3. Extract components from different sections of a document
4. Handle infrastructure components and their relationships
"""
import unittest
import os
import json
from typing import Dict, List, Any

# To be implemented - this would be the module we're testing
from focus_forge.component_detection import ComponentDetector

# Test constants
TEST_EXAMPLES = [
    # Format: (text, expected_components)
    (
        "The authentication service will handle user login and registration.",
        [("authentication service", ["service"])]
    ),
    (
        "We need to implement the user database and connect it to the auth module.",
        [("user database", ["database"]), ("auth module", ["module"])]
    ),
    (
        "The UI component should display data from the backend API.",
        [("UI component", ["ui", "module"]), ("backend API", ["api"])]
    ),
    (
        "Infrastructure requirements include setting up Docker containers and a CI/CD pipeline.",
        [("Docker containers", ["infrastructure"]), ("CI/CD pipeline", ["infrastructure"])]
    ),
    (
        "The payment processing library will be integrated with the checkout feature.",
        [("payment processing library", ["library"]), ("checkout feature", ["feature"])]
    ),
]

# More complex test cases with component relationships
COMPLEX_EXAMPLES = [
    """
    The user management module is part of the authentication service.
    It includes user profile management and role-based access control.
    The authentication service also contains the login component and password reset functionality.
    """,
    
    """
    The data processing pipeline consists of multiple components:
    - Data ingestion service
    - Transformation module
    - Storage system
    - Analysis library
    These components should be deployed to the processing server environment.
    """,
    
    """
    The frontend system includes several related features that should be developed together:
    - User dashboard UI
    - Notification component
    - Settings page
    All of these components depend on the UI framework library and API service.
    """
]


class TestComponentDetection(unittest.TestCase):
    """Test suite for the component detection module."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all test methods."""
        # Create detector instance
        cls.detector = ComponentDetector()
        
        # Create test dataset directory if needed
        os.makedirs("tests/data", exist_ok=True)
        
        # Generate training dataset file
        with open("tests/data/component_examples.json", "w") as f:
            json.dump({
                "components": [
                    {"name": "authentication service", "types": ["service"], "confidence": 1.0},
                    {"name": "user management module", "types": ["module"], "confidence": 1.0},
                    {"name": "payment gateway", "types": ["service", "api"], "confidence": 1.0},
                    {"name": "database server", "types": ["infrastructure", "database"], "confidence": 1.0},
                    {"name": "UI framework", "types": ["library", "ui"], "confidence": 1.0},
                ],
                "complex_examples": COMPLEX_EXAMPLES
            }, f, indent=2)
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        pass
        
    def test_component_detection(self):
        """Test detection of different component types."""
        for text, expected_components in TEST_EXAMPLES:
            detected_components = self.detector.detect_components(text)
            
            # Check if we found all expected components
            detected_names = [c["name"].lower() for c in detected_components]
            for expected_name, _ in expected_components:
                self.assertIn(expected_name.lower(), detected_names, 
                             f"Failed to detect '{expected_name}' in: {text}")
            
            # Check component types
            for expected_name, expected_types in expected_components:
                # Find the matching component
                component = next((c for c in detected_components 
                                if c["name"].lower() == expected_name.lower()), None)
                
                self.assertIsNotNone(component, f"Component '{expected_name}' not found in results")
                
                # Check that all expected types are present
                for exp_type in expected_types:
                    self.assertIn(exp_type, component["types"], 
                                 f"Component '{expected_name}' missing expected type '{exp_type}'")
    
    def test_infrastructure_detection(self):
        """Test detection of infrastructure components."""
        text = """
        We need to set up the following infrastructure:
        - Application servers for hosting the backend services
        - Database servers for data storage
        - CI/CD pipeline for automated deployments
        - Docker containers for consistent environments
        """
        
        detected_components = self.detector.detect_components(text)
        
        # Find infrastructure components
        infra_components = [c for c in detected_components if "infrastructure" in c["types"]]
        
        # Verify we found the expected infrastructure components
        self.assertGreaterEqual(len(infra_components), 3, 
                            "Should detect at least 3 infrastructure components")
        
        # Check for specific infrastructure types
        infra_names = [c["name"].lower() for c in infra_components]
        expected_infra = ["application servers", "database servers", "ci/cd pipeline", "docker containers"]
        
        for expected in expected_infra:
            found = any(expected in name for name in infra_names)
            self.assertTrue(found, f"Failed to identify '{expected}' as infrastructure")
    
    def test_component_grouping(self):
        """Test grouping of related components."""
        text = COMPLEX_EXAMPLES[0]  # Use the first complex example
        
        # First detect components
        components = self.detector.detect_components(text)
        
        # Then group them
        groups = self.detector.group_components(components, text)
        
        # Verify we have at least one group
        self.assertGreater(len(groups), 0, "Should create at least one component group")
        
        # Find the authentication service group
        auth_group = next((g for g in groups if "authentication" in g["name"].lower()), None)
        
        # Verify the group contains expected components
        self.assertIsNotNone(auth_group, "Should have a group related to authentication")
        
        group_component_names = [c["name"].lower() for c in auth_group["components"]]
        expected_components = ["authentication service", "user management", "login", "password reset"]
        
        for expected in expected_components:
            found = any(expected in name for name in group_component_names)
            self.assertTrue(found, f"Group should contain a component related to '{expected}'")
    
    def test_section_processing(self):
        """Test processing of sections in a document."""
        # Create a multi-section document
        doc = """# Authentication

The authentication module handles user login and session management.
It integrates with the user database for credential validation.

# User Management

User management includes profile creation, role assignment,
and permission management. The admin UI provides interfaces for these features.

# Infrastructure

The application requires the following infrastructure components:
- Web servers
- Database servers
- Cache servers
"""
        
        # Analyze the document
        results = self.detector.analyze_prd(doc)
        
        # Check that sections were properly parsed
        sections = results.get("section_components", {})
        self.assertGreaterEqual(len(sections), 3, "Should detect at least 3 sections")
        
        # Verify components were found in appropriate sections
        self.assertIn("Authentication", sections, "Should have 'Authentication' section")
        self.assertIn("User Management", sections, "Should have 'User Management' section")
        self.assertIn("Infrastructure", sections, "Should have 'Infrastructure' section")
        
        # Check component types in sections
        auth_components = sections.get("Authentication", [])
        user_components = sections.get("User Management", [])
        infra_components = sections.get("Infrastructure", [])
        
        # Authentication section should have module and database components
        auth_types = set()
        for component in auth_components:
            auth_types.update(component["types"])
        
        self.assertIn("module", auth_types, "Authentication section should have module components")
        self.assertIn("database", auth_types, "Authentication section should have database components")
        
        # User Management section should have UI components
        user_types = set()
        for component in user_components:
            user_types.update(component["types"])
        
        self.assertIn("ui", user_types, "User Management section should have UI components")
        
        # Infrastructure section should have infrastructure components
        infra_types = set()
        for component in infra_components:
            infra_types.update(component["types"])
        
        self.assertIn("infrastructure", infra_types, "Infrastructure section should have infrastructure components")
    
    def test_confidence_scoring(self):
        """Test confidence scoring of detected components."""
        # Create detector with training data
        detector_with_training = ComponentDetector("tests/data/component_examples.json")
        
        # Detect components in text with known and unknown components
        text = """
        The authentication service needs to be connected to the payment gateway.
        We also need to implement a new reporting system and notification service.
        """
        
        components = detector_with_training.detect_components(text)
        
        # Known components should have higher confidence
        auth_service = next((c for c in components if "authentication service" in c["name"].lower()), None)
        payment_gateway = next((c for c in components if "payment gateway" in c["name"].lower()), None)
        reporting_system = next((c for c in components if "reporting system" in c["name"].lower()), None)
        
        self.assertIsNotNone(auth_service, "Should detect authentication service")
        self.assertIsNotNone(payment_gateway, "Should detect payment gateway")
        self.assertIsNotNone(reporting_system, "Should detect reporting system")
        
        # Known components from training data should have higher confidence
        self.assertGreater(auth_service["confidence"], reporting_system["confidence"],
                         "Known components should have higher confidence than unknown ones")
        self.assertGreater(payment_gateway["confidence"], reporting_system["confidence"],
                         "Known components should have higher confidence than unknown ones")
    
    def test_multi_entity_relationships(self):
        """Test detection of relationships between multiple components."""
        text = COMPLEX_EXAMPLES[1]  # Use the example with pipeline components
        
        # First detect components
        components = self.detector.detect_components(text)
        
        # Then group them
        groups = self.detector.group_components(components, text)
        
        # Find the pipeline group
        pipeline_group = next((g for g in groups 
                              if "pipeline" in g["name"].lower() or 
                              "processing" in g["name"].lower()), None)
        
        # Verify the pipeline group exists and contains multiple components
        self.assertIsNotNone(pipeline_group, "Should have a pipeline-related group")
        self.assertGreaterEqual(len(pipeline_group["components"]), 3, 
                             "Pipeline group should contain multiple components")


if __name__ == "__main__":
    unittest.main() 