#!/usr/bin/env python3
"""
Extended test suite for sequence analysis functionality in focus_forge.

This test suite provides additional tests for T04 (Development Sequence Analysis Algorithm),
focusing on edge cases, special constraints, and performance optimization scenarios.
"""
import os
import sys
import json
import unittest
from pathlib import Path
from typing import List, Dict, Any, Optional, Set, Callable

# Ensure focus_forge module is in the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import sequence analysis functionality
from focus_forge.sequence_analysis import (
    SequenceAnalyzer,
    SequenceConstraint,
    SequenceConstraintType,
    get_optimized_sequence,
    get_parallel_task_groups,
    get_critical_path,
    create_sequence_analyzer_from_tasks
)
from focus_forge.dependency_graph import DependencyGraph, Dependency, DependencyType

# Replace pytest fixtures with functions
def diamond_dependency_tasks() -> List[Dict[str, Any]]:
    """Create tasks with diamond-shaped dependency pattern."""
    return [
        {
            "id": "A",
            "title": "Root Task",
            "description": "The starting point",
            "status": "done",
            "priority": "high",
            "dependencies": [],
            "details": "Root task details",
            "completed_at": "2023-01-01T10:00:00"
        },
        {
            "id": "B1",
            "title": "Left Branch",
            "description": "Left path from root",
            "status": "pending",
            "priority": "medium", 
            "dependencies": ["A"],
            "details": "Left branch details",
            "completed_at": None
        },
        {
            "id": "B2",
            "title": "Right Branch",
            "description": "Right path from root",
            "status": "pending",
            "priority": "high",  # Higher priority than B1
            "dependencies": ["A"],
            "details": "Right branch details",
            "completed_at": None
        },
        {
            "id": "C",
            "title": "Merge Point",
            "description": "Depends on both branches",
            "status": "pending",
            "priority": "medium",
            "dependencies": ["B1", "B2"],
            "details": "Merge task details",
            "completed_at": None
        }
    ]


def tasks_with_all_constraint_types() -> List[Dict[str, Any]]:
    """Create tasks for testing all constraint types."""
    return [
        {"id": "F1", "title": "Foundation 1", "priority": "high", "dependencies": []},
        {"id": "F2", "title": "Foundation 2", "priority": "high", "dependencies": []},
        {"id": "T1", "title": "Backend Task 1", "priority": "medium", "dependencies": ["F1"]},
        {"id": "T2", "title": "Backend Task 2", "priority": "medium", "dependencies": ["F1"]},
        {"id": "T3", "title": "Frontend Task 1", "priority": "medium", "dependencies": ["F2"]},
        {"id": "T4", "title": "Frontend Task 2", "priority": "medium", "dependencies": ["F2"]},
        {"id": "I1", "title": "Integration 1", "priority": "low", "dependencies": ["T1", "T3"]},
        {"id": "I2", "title": "Integration 2", "priority": "low", "dependencies": ["T2", "T4"]},
        {"id": "F", "title": "Final Task", "priority": "low", "dependencies": ["I1", "I2"]}
    ]


def tasks_with_metadata() -> List[Dict[str, Any]]:
    """Create tasks with additional metadata for testing complex scoring algorithms."""
    return [
        {
            "id": "A1",
            "title": "Task A1",
            "priority": "medium",
            "dependencies": [],
            "metadata": {
                "complexity": "high",
                "estimated_hours": 20,
                "team": "backend"
            }
        },
        {
            "id": "A2",
            "title": "Task A2",
            "priority": "high",
            "dependencies": [],
            "metadata": {
                "complexity": "low",
                "estimated_hours": 5,
                "team": "backend"
            }
        },
        {
            "id": "B1",
            "title": "Task B1",
            "priority": "medium",
            "dependencies": ["A1"],
            "metadata": {
                "complexity": "medium",
                "estimated_hours": 10,
                "team": "frontend"
            }
        },
        {
            "id": "B2",
            "title": "Task B2",
            "priority": "low",
            "dependencies": ["A2"],
            "metadata": {
                "complexity": "low",
                "estimated_hours": 8,
                "team": "frontend"
            }
        },
        {
            "id": "C",
            "title": "Task C",
            "priority": "high",
            "dependencies": ["B1", "B2"],
            "metadata": {
                "complexity": "high",
                "estimated_hours": 15,
                "team": "qa"
            }
        }
    ]


def empty_tasks() -> List[Dict[str, Any]]:
    """Create an empty task list for edge case testing."""
    return []


class TestSequenceAnalyzerAdvanced(unittest.TestCase):
    """Test advanced functionality of the SequenceAnalyzer class."""
    
    def test_diamond_dependency_sequence(self):
        """Test sequence generation with diamond-shaped dependency pattern."""
        analyzer = create_sequence_analyzer_from_tasks(diamond_dependency_tasks())
        sequence = analyzer.get_implementation_sequence()
        
        # Verify sequence respects dependencies
        task_ids = [task["id"] for task in sequence]
        
        # A must come before B1 and B2
        self.assertLess(task_ids.index("A"), task_ids.index("B1"))
        self.assertLess(task_ids.index("A"), task_ids.index("B2"))
        
        # B1 and B2 must come before C
        self.assertLess(task_ids.index("B1"), task_ids.index("C"))
        self.assertLess(task_ids.index("B2"), task_ids.index("C"))
        
        # Check that higher priority B2 comes before B1 (when both are ready)
        # This is only expected if they're at the same level in the dependency graph
        self.assertLess(task_ids.index("B2"), task_ids.index("B1"))
    
    def test_foundation_task_prioritization(self):
        """Test that foundation tasks are prioritized in the sequence."""
        analyzer = create_sequence_analyzer_from_tasks(tasks_with_all_constraint_types())
        
        # Add foundation constraint for F1 and F2
        foundation_constraint = SequenceConstraint(
            constraint_type=SequenceConstraintType.FOUNDATION_FIRST,
            task_ids=["F1", "F2"],
            description="Foundation tasks",
            weight=2.0
        )
        analyzer.add_constraint(foundation_constraint)
        
        sequence = analyzer.get_implementation_sequence()
        task_ids = [task["id"] for task in sequence]
        
        # Foundation tasks should be at the beginning of the sequence
        self.assertIn(task_ids[0], ["F1", "F2"])
        self.assertIn(task_ids[1], ["F1", "F2"])
        
        # F1 and F2 should be the first two tasks
        self.assertEqual(set(task_ids[:2]), {"F1", "F2"})
    
    def test_technical_prerequisite_constraints(self):
        """Test that technical prerequisite constraints are respected."""
        # Create a custom tasks list where we can control the dependencies exactly
        custom_tasks = [
            {"id": "A", "title": "Task A", "priority": "high", "dependencies": []},
            {"id": "B", "title": "Task B", "priority": "high", "dependencies": []},
            {"id": "C", "title": "Task C", "priority": "medium", "dependencies": ["A", "B"]}
        ]
        
        analyzer = create_sequence_analyzer_from_tasks(custom_tasks)
        
        # A and B have no dependencies between them, so their order is arbitrary
        # Add a technical constraint specifying that B should come before A
        tech_constraint = SequenceConstraint(
            constraint_type=SequenceConstraintType.TECHNICAL_PREREQUISITE,
            task_ids=["B", "A"],  # B should come before A
            description="Technical preference order",
            weight=2.0
        )
        analyzer.add_constraint(tech_constraint)
        
        sequence = analyzer.get_implementation_sequence()
        task_ids = [task["id"] for task in sequence]
        
        # Verify B comes before A
        self.assertLess(task_ids.index("B"), task_ids.index("A"))
        
        # Both A and B should come before C (which depends on both)
        self.assertLess(task_ids.index("A"), task_ids.index("C"))
        self.assertLess(task_ids.index("B"), task_ids.index("C"))
    
    def test_team_assignment_constraints(self):
        """Test that team assignment constraints group related tasks."""
        def get_task_id(task):
            return task["id"]
        
        def get_priority_value(task):
            priority_map = {"high": 1, "medium": 2, "low": 3}
            return priority_map.get(task["priority"], 999)
        
        analyzer = SequenceAnalyzer(
            tasks=tasks_with_metadata(),
            task_id_fn=get_task_id,
            priority_fn=get_priority_value
        )
        
        # Add dependencies
        for task in tasks_with_metadata():
            for dep_id in task.get("dependencies", []):
                analyzer.add_dependency(task["id"], dep_id)
        
        # Add team assignment constraint to group backend tasks
        team_constraint = SequenceConstraint(
            constraint_type=SequenceConstraintType.TEAM_ASSIGNMENT,
            task_ids=["A1", "A2"],  # Backend team tasks
            description="Backend team tasks",
            weight=1.0
        )
        analyzer.add_constraint(team_constraint)
        
        sequence = analyzer.get_implementation_sequence()
        task_ids = [analyzer.task_id_fn(task) for task in sequence]
        
        # A1 and A2 should be adjacent in the sequence when possible
        a1_pos = task_ids.index("A1")
        a2_pos = task_ids.index("A2")
        
        # Since both A1 and A2 have no dependencies and are in the same team,
        # they should be adjacent (or at most 1 position apart)
        self.assertLessEqual(abs(a1_pos - a2_pos), 1)
    
    def test_complexity_based_constraints(self):
        """Test that complexity-based constraints prioritize complex tasks."""
        analyzer = create_sequence_analyzer_from_tasks(tasks_with_metadata())
        
        # Add constraint to prioritize complex tasks
        complexity_constraint = SequenceConstraint(
            constraint_type=SequenceConstraintType.COMPLEXITY_BASED,
            task_ids=["A1", "C"],  # High complexity tasks
            description="High complexity tasks",
            weight=1.5
        )
        analyzer.add_constraint(complexity_constraint)
        
        sequence = analyzer.get_implementation_sequence()
        task_ids = [task["id"] for task in sequence]
        
        # A1 and A2 have no dependencies, so either could come first
        # However, A1 should be prioritized over A2 because it's marked as complex
        # and we added a constraint for it
        
        # Verify that A1 comes before tasks that depend on it
        self.assertLess(task_ids.index("A1"), task_ids.index("B1"))
        
        # Verify that C comes as early as possible given its dependencies
        # It should come right after its dependencies B1 and B2
        
        # Find positions
        b1_pos = task_ids.index("B1")
        b2_pos = task_ids.index("B2")
        c_pos = task_ids.index("C")
        
        # C should come after both its dependencies
        self.assertGreater(c_pos, b1_pos)
        self.assertGreater(c_pos, b2_pos)
        
        # C should come immediately after its last dependency
        # (or very soon after, allowing for other constraints)
        self.assertLessEqual(c_pos - max(b1_pos, b2_pos), 2)
    
    def test_empty_task_list(self):
        """Test behavior with empty task list."""
        analyzer = create_sequence_analyzer_from_tasks(empty_tasks())
        
        # Implementation sequence should be empty
        sequence = analyzer.get_implementation_sequence()
        self.assertEqual(len(sequence), 0)
        
        # Critical path should be empty
        critical_path = analyzer.get_critical_path()
        self.assertEqual(len(critical_path), 0)
        
        # Parallel tasks should be empty
        parallel_tasks = analyzer.identify_parallel_tasks()
        self.assertEqual(len(parallel_tasks), 0)
    
    def test_estimate_implementation_time(self):
        """Test estimation of implementation time based on critical path."""
        analyzer = create_sequence_analyzer_from_tasks(tasks_with_metadata())
        
        # Add custom implementation time estimation method
        def estimate_implementation_time(tasks: List[Dict[str, Any]]) -> float:
            """Estimate total implementation time based on critical path."""
            analyzer = create_sequence_analyzer_from_tasks(tasks)
            critical_path = analyzer.get_critical_path()
            
            # Sum durations of tasks in critical path
            total_time = 0.0
            for task_id in critical_path:
                # Find the task
                for task in tasks:
                    if task["id"] == task_id:
                        # Get estimated hours from metadata
                        total_time += task.get("metadata", {}).get("estimated_hours", 1.0)
                        break
            
            return total_time
        
        # Calculate estimated time
        estimated_time = estimate_implementation_time(tasks_with_metadata())
        
        # The critical path should be A1 -> B1 -> C with times 20 + 10 + 15 = 45 hours
        self.assertEqual(estimated_time, 45.0)


class TestSequenceVisualization(unittest.TestCase):
    """Test visualization capabilities for sequence analysis."""
    
    def test_sequence_to_dict(self):
        """Test converting sequence results to dictionary for visualization."""
        analyzer = create_sequence_analyzer_from_tasks(diamond_dependency_tasks())
        sequence = analyzer.get_implementation_sequence()
        
        # Create a sequence visualization dictionary
        sequence_data = {
            "sequence": [{"id": task["id"], "title": task["title"]} for task in sequence],
            "critical_path": analyzer.get_critical_path(),
            "parallel_groups": [[task_id for task_id in group] for group in analyzer.identify_parallel_tasks()]
        }
        
        # Verify structure is correct for visualization
        self.assertIn("sequence", sequence_data)
        self.assertIn("critical_path", sequence_data)
        self.assertIn("parallel_groups", sequence_data)
        
        # Ensure all tasks are in the sequence
        task_ids_in_sequence = [task["id"] for task in sequence_data["sequence"]]
        for task in diamond_dependency_tasks():
            self.assertIn(task["id"], task_ids_in_sequence)
        
        # Verify critical path follows a valid path through the graph
        for i in range(1, len(sequence_data["critical_path"])):
            current = sequence_data["critical_path"][i]
            previous = sequence_data["critical_path"][i-1]
            
            # Find the task object
            current_task = next((t for t in diamond_dependency_tasks() if t["id"] == current), None)
            
            # Verify dependency relationship
            self.assertIn(previous, current_task.get("dependencies", []))


class TestHelperFunctionsExtended(unittest.TestCase):
    """Test additional helper functions for sequence analysis."""
    
    def test_custom_priority_mapping(self):
        """Test using a custom priority mapping function."""
        tasks = tasks_with_all_constraint_types()
        
        # Create a custom priority function with more granular priority levels
        def custom_priority_function(task: Dict[str, Any]) -> float:
            """Custom priority calculation with decimal values for fine-grained control."""
            base_priority_map = {"high": 1.0, "medium": 2.0, "low": 3.0}
            base_value = base_priority_map.get(task.get("priority", "medium"), 2.0)
            
            # Adjust value based on task position in hierarchy (root tasks get bonus)
            if not task.get("dependencies"):
                base_value -= 0.2  # Bonus for root tasks
            
            # Adjust based on dependent count (more dependents = higher priority)
            dependent_count = len([t for t in tasks 
                                 if task["id"] in t.get("dependencies", [])])
            base_value -= dependent_count * 0.1  # Bonus for tasks with many dependents
            
            return base_value
        
        # Create analyzer with custom priority function
        analyzer = SequenceAnalyzer(
            tasks=tasks,
            task_id_fn=lambda t: t["id"],
            priority_fn=custom_priority_function
        )
        
        # Add dependencies
        for task in tasks:
            for dep_id in task.get("dependencies", []):
                analyzer.add_dependency(task["id"], dep_id)
        
        # Generate sequence with custom priorities
        sequence = analyzer.get_implementation_sequence()
        task_ids = [analyzer.task_id_fn(task) for task in sequence]
        
        # F1 and F2 (foundation tasks with no dependencies) should be first
        self.assertEqual(set(task_ids[:2]), {"F1", "F2"})
        
        # Verify sequence respects dependencies
        # T1 and T2 depend on F1
        self.assertLess(task_ids.index("F1"), task_ids.index("T1"))
        self.assertLess(task_ids.index("F1"), task_ids.index("T2"))
        
        # T3 and T4 depend on F2
        self.assertLess(task_ids.index("F2"), task_ids.index("T3"))
        self.assertLess(task_ids.index("F2"), task_ids.index("T4"))


def run_tests():
    """Run the test suite."""
    print("Running sequence analysis tests for T04")
    test_suite = unittest.TestSuite()
    test_suite.addTest(unittest.makeSuite(TestSequenceAnalyzerAdvanced))
    test_suite.addTest(unittest.makeSuite(TestSequenceVisualization))
    test_suite.addTest(unittest.makeSuite(TestHelperFunctionsExtended))
    
    runner = unittest.TextTestRunner(verbosity=2)
    runner.run(test_suite)


if __name__ == "__main__":
    run_tests() 