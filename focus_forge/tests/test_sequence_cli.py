#!/usr/bin/env python3
"""
Tests for the sequence analysis CLI commands in focus_forge.

This test file verifies that the CLI commands for sequence analysis
work as expected, including the sequence and parallel_tasks commands.
"""
import os
import sys
import json
import tempfile
import pytest
from unittest.mock import patch, MagicMock
from pathlib import Path
from typing import List, Dict, Any, Optional

# Ensure focus_forge module is in the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import focus_forge modules
from focus_forge.focus_forge import sequence, parallel_tasks
from focus_forge.sequence_analysis import get_optimized_sequence, get_critical_path, get_parallel_task_groups


@pytest.fixture
def mock_tasks() -> List[Dict[str, Any]]:
    """Create mock tasks for testing CLI commands."""
    return [
        {
            "id": "CLI1",
            "title": "First CLI Test Task",
            "description": "First task for CLI testing",
            "status": "done",
            "priority": "high",
            "dependencies": [],
            "details": "Task details",
            "test_strategy": "Test strategy",
            "subtasks": [],
            "completed_at": "2023-01-01T10:00:00"
        },
        {
            "id": "CLI2",
            "title": "Second CLI Test Task",
            "description": "Second task for CLI testing",
            "status": "pending",
            "priority": "medium",
            "dependencies": ["CLI1"],
            "details": "Task details",
            "test_strategy": "Test strategy",
            "subtasks": [],
            "completed_at": None
        },
        {
            "id": "CLI3",
            "title": "Third CLI Test Task",
            "description": "Third task for CLI testing",
            "status": "pending",
            "priority": "low",
            "dependencies": ["CLI2"],
            "details": "Task details",
            "test_strategy": "Test strategy",
            "subtasks": [],
            "completed_at": None
        }
    ]


@pytest.fixture
def mock_console():
    """Create a mock console for testing CLI output."""
    mock = MagicMock()
    mock.print = MagicMock()
    return mock


@pytest.fixture
def mock_load_tasks(mock_tasks):
    """Create a mock for the load_tasks function."""
    with patch('focus_forge.focus_forge.load_tasks', return_value=mock_tasks) as mock_func:
        yield mock_func


class TestSequenceCommand:
    """Test the sequence CLI command."""
    
    def test_sequence_command_with_no_tasks(self, mock_console):
        """Test sequence command when no tasks are found."""
        with patch('focus_forge.focus_forge.load_tasks', return_value=[]), \
             patch('focus_forge.focus_forge.console', mock_console):
            sequence(output=None)
            
            # Should show a warning message
            mock_console.print.assert_called_with(
                "[yellow]No tasks found. Run 'init' or 'parse-prd' to create tasks.[/]"
            )
    
    def test_sequence_command_with_tasks(self, mock_tasks, mock_console, mock_load_tasks):
        """Test sequence command with tasks."""
        # Mock the imported functions
        with patch('focus_forge.focus_forge.get_optimized_sequence', return_value=mock_tasks), \
             patch('focus_forge.focus_forge.get_critical_path', return_value=mock_tasks), \
             patch('focus_forge.focus_forge.console', mock_console), \
             patch('focus_forge.focus_forge.Table'), \
             patch('focus_forge.focus_forge.colorize_status'), \
             patch('focus_forge.focus_forge.colorize_priority'):
            
            sequence(output=None)
            
            # Check that console.print was called
            assert mock_console.print.call_count > 0
            # At minimum, should print the title and critical path header
            mock_console.print.assert_any_call("[bold]Generating optimized implementation sequence...[/]")
    
    def test_sequence_command_with_output_file(self, mock_tasks, mock_console, mock_load_tasks):
        """Test sequence command with output file."""
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            output_path = temp_file.name
        
        try:
            # Mock the imported functions
            with patch('focus_forge.focus_forge.get_optimized_sequence', return_value=mock_tasks), \
                 patch('focus_forge.focus_forge.get_critical_path', return_value=mock_tasks), \
                 patch('focus_forge.focus_forge.console', mock_console), \
                 patch('focus_forge.focus_forge.Table'), \
                 patch('focus_forge.focus_forge.colorize_status'), \
                 patch('focus_forge.focus_forge.colorize_priority'):
                
                sequence(output=output_path)
                
                # Should print message about export
                mock_console.print.assert_any_call(f"\n[green]Sequence data exported to {output_path}[/]")
                
                # Verify file was created and contains expected data
                with open(output_path, 'r') as f:
                    data = json.load(f)
                    assert "optimized_sequence" in data
                    assert "critical_path" in data
                    assert len(data["optimized_sequence"]) == 3
                    assert data["optimized_sequence"] == ["CLI1", "CLI2", "CLI3"]
        
        finally:
            # Clean up the temporary file
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    def test_sequence_command_import_error(self, mock_console, mock_load_tasks):
        """Test sequence command when import fails."""
        with patch('focus_forge.focus_forge.console', mock_console), \
             patch('focus_forge.focus_forge.__import__', side_effect=ImportError("Mocked import error")):
            
            sequence(output=None)
            
            # Should show an error message
            mock_console.print.assert_any_call("[red]Error:[/] Could not import sequence analysis module.")


class TestParallelTasksCommand:
    """Test the parallel_tasks CLI command."""
    
    def test_parallel_tasks_command_with_no_tasks(self, mock_console):
        """Test parallel_tasks command when no tasks are found."""
        with patch('focus_forge.focus_forge.load_tasks', return_value=[]), \
             patch('focus_forge.focus_forge.console', mock_console):
            
            parallel_tasks()
            
            # Should show a warning message
            mock_console.print.assert_called_with(
                "[yellow]No tasks found. Run 'init' or 'parse-prd' to create tasks.[/]"
            )
    
    def test_parallel_tasks_command_with_tasks(self, mock_tasks, mock_console, mock_load_tasks):
        """Test parallel_tasks command with tasks."""
        # Mock the get_parallel_task_groups function to return grouped tasks
        groups = [[mock_tasks[0]], [mock_tasks[1]], [mock_tasks[2]]]
        
        with patch('focus_forge.focus_forge.get_parallel_task_groups', return_value=groups), \
             patch('focus_forge.focus_forge.console', mock_console), \
             patch('focus_forge.focus_forge.Table'), \
             patch('focus_forge.focus_forge.colorize_status'), \
             patch('focus_forge.focus_forge.colorize_priority'):
            
            parallel_tasks()
            
            # Check that console.print was called
            assert mock_console.print.call_count > 0
            # Should print the title
            mock_console.print.assert_any_call("[bold]Identifying tasks that can be implemented in parallel...[/]")
            
            # Should print each group header
            for i in range(len(groups)):
                mock_console.print.assert_any_call(
                    f"\n[bold cyan]Group {i+1}[/] (tasks that can be worked on in parallel):"
                )
    
    def test_parallel_tasks_command_import_error(self, mock_console, mock_load_tasks):
        """Test parallel_tasks command when import fails."""
        with patch('focus_forge.focus_forge.console', mock_console), \
             patch('focus_forge.focus_forge.__import__', side_effect=ImportError("Mocked import error")):
            
            parallel_tasks()
            
            # Should show an error message
            mock_console.print.assert_any_call("[red]Error:[/] Could not import sequence analysis module.") 