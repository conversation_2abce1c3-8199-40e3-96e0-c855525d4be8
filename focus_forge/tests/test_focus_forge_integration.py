#!/usr/bin/env python3
"""
Tests for the integration commands in focus_forge.py.
"""
import os
import sys
import json
import unittest
import tempfile
from unittest.mock import patch, MagicMock
from pathlib import Path
from typing import Dict, List, Any, Optional
import click
from click.testing import C<PERSON><PERSON><PERSON>ner

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the focus_forge CLI
try:
    from focus_forge import cli, parse_prd_advanced, integrate_prd, validate_prd_tasks
    HAS_CLI = True
except ImportError:
    print("WARNING: Could not import focus_forge CLI. Tests will be skipped.")
    HAS_CLI = False


# Sample PRD text for testing
SAMPLE_PRD = """# Sample PRD for Testing

## Overview
This is a test PRD for the integration system.

## Features
1. Feature One: This is the first feature
2. Feature Two: This depends on Feature One
3. Feature Three: This is independent

## Technical Requirements
- Must be implemented in Python
- Should have error handling
- Database integration is required
"""

# Sample tasks for testing
SAMPLE_TASKS = [
    {
        "id": "T1",
        "title": "Implement Feature One",
        "description": "Create the first feature mentioned in the PRD",
        "status": "pending",
        "priority": "high",
        "dependencies": [],
        "details": "Feature One implementation details",
        "test_strategy": "Test Feature One thoroughly",
        "subtasks": []
    },
    {
        "id": "T2",
        "title": "Implement Feature Two",
        "description": "Create the second feature that depends on Feature One",
        "status": "pending",
        "priority": "medium",
        "dependencies": ["T1"],
        "details": "Feature Two implementation details",
        "test_strategy": "Test Feature Two with Feature One",
        "subtasks": []
    }
]


@unittest.skipIf(not HAS_CLI, "focus_forge CLI not available")
class TestFocusForgeIntegration(unittest.TestCase):
    """Test cases for focus_forge integration commands."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.runner = CliRunner()
        
        # Create temporary PRD file
        self.prd_file = tempfile.NamedTemporaryFile(delete=False, suffix=".md")
        with open(self.prd_file.name, 'w') as f:
            f.write(SAMPLE_PRD)
        
        # Create temporary tasks file
        self.tasks_file = tempfile.NamedTemporaryFile(delete=False, suffix=".json")
        with open(self.tasks_file.name, 'w') as f:
            json.dump({"tasks": SAMPLE_TASKS}, f)
        
        # Create temporary output file
        self.output_file = tempfile.NamedTemporaryFile(delete=False, suffix=".json")
        self.output_file.close()
        
        # Create temporary config file
        self.config_file = tempfile.NamedTemporaryFile(delete=False, suffix=".json")
        config = {
            "task_generation": {
                "default_priority": "high",
                "generate_subtasks": True
            },
            "task_validation": {
                "validate_dependencies": True
            }
        }
        with open(self.config_file.name, 'w') as f:
            json.dump(config, f)
    
    def tearDown(self):
        """Clean up temporary files."""
        try:
            os.unlink(self.prd_file.name)
            os.unlink(self.tasks_file.name)
            os.unlink(self.output_file.name)
            os.unlink(self.config_file.name)
        except Exception:
            pass
    
    @patch('focus_forge.parse_prd_to_tasks')
    @patch('focus_forge.click.confirm')
    def test_parse_prd_advanced(self, mock_confirm, mock_parse_prd):
        """Test the parse-prd-advanced command."""
        # Mock parse_prd_to_tasks to return sample tasks
        mock_parse_prd.return_value = SAMPLE_TASKS
        
        # Mock click.confirm to return False (don't import tasks)
        mock_confirm.return_value = False
        
        # Test with advanced mode
        with self.runner.isolated_filesystem():
            result = self.runner.invoke(
                parse_prd_advanced,
                ['--input', self.prd_file.name, '--output', 'output.json', '--advanced']
            )
            self.assertEqual(result.exit_code, 0)
            
            # Verify output contains success message
            self.assertIn("Successfully generated", result.output)
            
            # Verify output file was created
            self.assertTrue(os.path.exists('output.json'))
        
        # Test with standard mode (should call the original parse_prd)
        with patch('focus_forge.generate_tasks_from_prd') as mock_generate:
            mock_generate.return_value = SAMPLE_TASKS
            with self.runner.isolated_filesystem():
                result = self.runner.invoke(
                    parse_prd_advanced,
                    ['--input', self.prd_file.name]
                )
                self.assertEqual(result.exit_code, 0)
                mock_generate.assert_called_once()
        
        # Test error handling
        mock_parse_prd.side_effect = ValueError("Test error")
        result = self.runner.invoke(
            parse_prd_advanced,
            ['--input', self.prd_file.name, '--advanced']
        )
        self.assertIn("Error", result.output)
    
    @patch('focus_forge.integrate_prd_with_existing')
    @patch('focus_forge.click.confirm')
    def test_integrate_prd(self, mock_confirm, mock_integrate):
        """Test the integrate-prd command."""
        # Mock integrate_prd_with_existing to return a success report
        mock_integrate.return_value = {
            "status": "success",
            "new_tasks": 1,
            "existing_tasks": 2,
            "merged_tasks": 3,
            "output_file": self.output_file.name
        }
        
        # Mock click.confirm to return False (don't update system)
        mock_confirm.return_value = False
        
        # Test successful integration
        result = self.runner.invoke(
            integrate_prd,
            ['--input', self.prd_file.name, '--output', self.output_file.name]
        )
        self.assertEqual(result.exit_code, 0)
        self.assertIn("Successfully integrated", result.output)
        
        # Test verbose output
        result = self.runner.invoke(
            integrate_prd,
            ['--input', self.prd_file.name, '--output', self.output_file.name, '--verbose']
        )
        self.assertEqual(result.exit_code, 0)
        
        # Test error handling
        mock_integrate.return_value = {"status": "error", "error_message": "Test error"}
        result = self.runner.invoke(
            integrate_prd,
            ['--input', self.prd_file.name]
        )
        self.assertIn("Error", result.output)
    
    @patch('focus_forge.validate_tasks_with_prd')
    def test_validate_prd_tasks(self, mock_validate):
        """Test the validate-prd-tasks command."""
        # Mock validate_tasks_with_prd to return a validation report
        mock_validate.return_value = {
            "tasks_valid": True,
            "confidence_score": 0.85,
            "clarity_score": 0.9,
            "coverage_score": 0.8
        }
        
        # Test successful validation
        result = self.runner.invoke(
            validate_prd_tasks,
            ['--prd', self.prd_file.name]
        )
        self.assertEqual(result.exit_code, 0)
        self.assertIn("Tasks are valid", result.output)
        
        # Test with output file
        result = self.runner.invoke(
            validate_prd_tasks,
            ['--prd', self.prd_file.name, '--output', self.output_file.name]
        )
        self.assertEqual(result.exit_code, 0)
        self.assertTrue(os.path.exists(self.output_file.name))
        
        # Test verbose output
        result = self.runner.invoke(
            validate_prd_tasks,
            ['--prd', self.prd_file.name, '--verbose']
        )
        self.assertEqual(result.exit_code, 0)
        
        # Test failed validation
        mock_validate.return_value = {
            "tasks_valid": False,
            "status": "error",
            "error_message": "Test error"
        }
        
        # Without --fail flag
        result = self.runner.invoke(
            validate_prd_tasks,
            ['--prd', self.prd_file.name]
        )
        self.assertEqual(result.exit_code, 0)
        self.assertIn("failed", result.output)
        
        # With --fail flag (should exit with code 1)
        result = self.runner.invoke(
            validate_prd_tasks,
            ['--prd', self.prd_file.name, '--fail']
        )
        self.assertEqual(result.exit_code, 1)


if __name__ == "__main__":
    unittest.main() 