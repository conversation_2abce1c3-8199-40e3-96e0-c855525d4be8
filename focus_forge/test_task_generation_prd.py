#!/usr/bin/env python3
"""
Test script for task generation from a sample PRD.
"""
import sys
import json
import os
from pathlib import Path

# Add parent directory to path to import focus_forge
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from focus_forge.task_generation import TaskGenerator, TaskGenerationConfig


# Sample PRD text for testing
SAMPLE_PRD = """
# Task Management System

## System Overview

The Task Management System is a web-based application designed to help teams organize, track, and prioritize their work. It will provide an intuitive interface for creating, assigning, and monitoring tasks.

## Core Components

### Authentication Service

The system requires a secure authentication service that handles user registration, login, and access control. It must support role-based permissions and secure password storage.

### Task Storage

A robust task storage component is needed to persist task data. This should include support for task metadata, attachments, and historical tracking of changes.

### Notification System

The notification system will alert users about task assignments, updates, and approaching deadlines. It should support multiple notification channels (email, in-app, etc.).

## Features

### Task Creation and Editing

Users need to create and edit tasks with the following attributes:
- Title
- Description
- Priority (High, Medium, Low)
- Status (To Do, In Progress, Done)
- Assignee
- Due date
- Tags

The task creation UI must be simple and intuitive while allowing for detailed task information.

### Task Dashboard

The dashboard will show an overview of all tasks relevant to the user. It depends on the Authentication Service to determine which tasks to display based on user permissions.

### Reporting

A reporting feature will generate insights about task completion, team performance, and work distribution. This feature requires the Task Storage component for historical data access.

## Integrations

### Calendar Integration

The system should integrate with popular calendar applications (Google Calendar, Outlook) to sync task deadlines. This integration depends on the Notification System.

### Email Integration

Email integration will enable users to create tasks via email and receive task updates. The Authentication Service is required for this integration to ensure proper task attribution.

## Technical Requirements

### Performance

The system must handle at least 1000 concurrent users and 100,000 tasks without significant performance degradation.

### Security

All data must be encrypted in transit and at rest. The system must comply with GDPR and other relevant data protection regulations.

### Scalability

The architecture should support horizontal scaling to accommodate growing user bases.
"""


def main():
    """Run a demonstration of task generation from a sample PRD."""
    print("Task Generation Demo")
    print("=" * 50)
    
    # Create task generator with custom config
    config = TaskGenerationConfig(
        default_priority="medium",
        generate_subtasks=True,
        max_subtasks_per_task=3,
        id_prefix="T"
    )
    
    generator = TaskGenerator(config)
    
    # Step 1: Analyze the PRD
    print("Step 1: Analyzing PRD...")
    analysis = generator.analyze_prd_text(SAMPLE_PRD)
    
    # Print components found
    print(f"\nFound {len(analysis['components'])} components:")
    for component in analysis['components']:
        print(f"  - {component['name']} ({component['priority']} priority)")
    
    print(f"\nFound {len(analysis['features'])} features:")
    for feature in analysis['features']:
        print(f"  - {feature['name']} ({feature['priority']} priority)")
    
    # Step 2: Generate tasks
    print("\nStep 2: Generating tasks...")
    tasks = generator.generate_tasks_from_prd(SAMPLE_PRD)
    
    # Print generated tasks
    print(f"\nGenerated {len(tasks)} tasks:")
    for task in tasks:
        print(f"  - {task['id']}: {task['title']} ({task['priority']} priority)")
        if task['dependencies']:
            print(f"    Dependencies: {', '.join(task['dependencies'])}")
        if task['subtasks']:
            print(f"    Subtasks: {len(task['subtasks'])}")
    
    # Step 3: Save tasks to file for inspection
    output_file = "generated_tasks.json"
    with open(output_file, 'w') as f:
        json.dump(tasks, f, indent=2)
    
    print(f"\nTasks saved to {output_file} for detailed inspection")


if __name__ == "__main__":
    main() 