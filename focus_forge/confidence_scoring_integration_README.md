# Confidence Scoring Integration

This module provides integration utilities for using the confidence scoring system in various scenarios including:

- Single PRD/task validation
- Batch processing of multiple PRD/task pairs
- CI/CD pipeline integration

## Installation

Make sure you have the required dependencies:

```bash
# From the project root
pip install -r requirements.txt
```

## Usage

### 1. Single PRD/Task Validation

Check a single PRD and tasks file against quality thresholds:

```bash
python confidence_scoring_integration.py check path/to/prd.md path/to/tasks.json --output report.json
```

Options:
- `--output` / `-o`: Save detailed report to specified file
- `--fail`: Exit with error code if thresholds aren't met (useful for scripts)
- `--config`: Path to custom configuration file

### 2. Batch Processing

Process multiple PRD/task pairs in a directory structure:

```bash
python confidence_scoring_integration.py batch path/to/prds/ path/to/output/ --workers 4
```

Options:
- `--prd-pattern`: Glob pattern for PRD files (defaults to config value)
- `--tasks-pattern`: Glob pattern for task files (defaults to config value)
- `--workers`: Maximum number of parallel workers (defaults to config value)
- `--config`: Path to custom configuration file

The batch processor will:
1. Find PRD files matching the pattern
2. Look for corresponding tasks files in the same directory
3. Generate confidence reports for each pair
4. Save individual reports and a summary report to the output directory

### 3. CI/CD Integration

Generate CI-friendly reports for integration with CI/CD systems:

```bash
python confidence_scoring_integration.py ci path/to/prd.md path/to/tasks.json --output ci_report.json --fail
```

Options:
- `--output` / `-o`: Save CI report to specified file
- `--fail`: Exit with error code if thresholds aren't met (recommended for CI/CD)
- `--config`: Path to custom configuration file

The CI report contains streamlined information suitable for CI/CD systems:
- Overall status (success/failure/error)
- Score summaries
- Failed threshold information
- Critical issues list

## Workflow Diagrams

### Single PRD/Task Validation Workflow

```mermaid
flowchart TD
    A[PRD Document] --> C{Confidence Scoring}
    B[Tasks JSON] --> C
    D[Configuration] --> C
    C --> E{Quality Gate Check}
    E -->|Passed| F[Success Report]
    E -->|Failed| G[Improvement Plan]
    G --> H[PRD Improvements]
    G --> I[Task Improvements]
    G --> J[Critical Issues]
    H --> K[Update PRD]
    I --> L[Update Tasks]
    K --> C
    L --> C
```

### Batch Processing Workflow

```mermaid
flowchart TD
    A[Directory of PRDs] --> B[Find PRD-Task Pairs]
    B --> C[Process Pair 1]
    B --> D[Process Pair 2]
    B --> E[Process Pair N]
    C --> F[Generate Reports]
    D --> F
    E --> F
    F --> G[Summary Report]
    F --> H[Individual Reports]
    G --> I{All Critical Issues}
    H --> J{Failed Checks}
    I --> K[Update PRDs & Tasks]
    J --> K
    K --> A
```

### CI/CD Integration Workflow

```mermaid
flowchart LR
    A[Code Repository] --> B[CI/CD Pipeline]
    B --> C[Checkout Code]
    C --> D[Generate PRD]
    D --> E[Generate Tasks]
    E --> F[Confidence Scoring]
    F --> G{Quality Gate}
    G -->|Pass| H[Deploy]
    G -->|Fail| I[Block Merge]
    I --> J[Improvement Suggestions]
    J --> K[Update PRD/Tasks]
    K --> A
```

### Complete System Integration

```mermaid
flowchart TD
    subgraph Development
        A[Create PRD] --> B[Generate Tasks]
        B --> C[Local Validation]
        C -->|Improvements Needed| A
    end
    
    subgraph CI_Pipeline
        D[PR Created] --> E[PRD/Tasks Validation]
        E -->|Pass| F[PR Approved]
        E -->|Fail| G[PR Needs Work]
        G --> A
    end
    
    subgraph Analysis
        H[Batch Analysis] --> I[Quality Reports]
        I --> J[Identify Patterns]
        J --> K[Improve Process]
        K --> A
    end
    
    C -->|Validation Passed| D
    F --> L[Merge to Main]
    L --> H
```

## Configuration

The tool can be configured using a JSON configuration file. A default configuration is included as `confidence_scoring_config.json`, but you can provide a custom one with the `--config` option.

### Configuration Options

```json
{
  "thresholds": {
    "confidence": 0.7,
    "clarity": 0.6,
    "coverage": 0.8,
    "max_ambiguous_high": 0,
    "max_missing_high": 0
  },
  "batch_processing": {
    "default_workers": 4,
    "prd_pattern": "*.md",
    "tasks_pattern": "*.json",
    "auto_pair_files": true
  },
  "ci_integration": {
    "fail_on_threshold": true,
    "include_improvement_suggestions": true,
    "max_issues_to_report": 5
  },
  "logging": {
    "log_file": "confidence_scoring.log",
    "log_level": "INFO",
    "include_timestamp": true
  },
  "scoring_weights": {
    "clarity": 0.3,
    "coverage": 0.5,
    "complexity": 0.2
  },
  "review_system": {
    "max_clarifications": 10,
    "max_improvements": 10,
    "priority_order": ["high", "medium", "low"]
  }
}
```

### Configuration Hierarchy

```mermaid
classDiagram
    class Configuration {
        +thresholds
        +batch_processing
        +ci_integration
        +logging
        +scoring_weights
        +review_system
    }
    
    class Thresholds {
        +confidence: 0.7
        +clarity: 0.6
        +coverage: 0.8
        +max_ambiguous_high: 0
        +max_missing_high: 0
    }
    
    class BatchProcessing {
        +default_workers: 4
        +prd_pattern: "*.md"
        +tasks_pattern: "*.json"
        +auto_pair_files: true
    }
    
    class CIIntegration {
        +fail_on_threshold: true
        +include_improvement_suggestions: true
        +max_issues_to_report: 5
    }
    
    class ScoringWeights {
        +clarity: 0.3
        +coverage: 0.5
        +complexity: 0.2
    }
    
    class ReviewSystem {
        +max_clarifications: 10
        +max_improvements: 10
        +priority_order: ["high", "medium", "low"]
    }
    
    Configuration --> Thresholds
    Configuration --> BatchProcessing
    Configuration --> CIIntegration
    Configuration --> ScoringWeights
    Configuration --> ReviewSystem
```

### Configuring Thresholds

You can adjust the quality thresholds to match your project's requirements:

| Threshold | Description |
|-----------|-------------|
| confidence | Minimum overall confidence score (0.0-1.0) |
| clarity | Minimum clarity score (0.0-1.0) |
| coverage | Minimum coverage score (0.0-1.0) |
| max_ambiguous_high | Maximum allowed high severity ambiguous requirements |
| max_missing_high | Maximum allowed high severity missing requirements |

### Custom Configuration Example

Create a custom configuration for stricter thresholds:

```json
{
  "thresholds": {
    "confidence": 0.8,
    "clarity": 0.7,
    "coverage": 0.9
  }
}
```

And use it with any command:

```bash
python confidence_scoring_integration.py check prd.md tasks.json --config my_custom_config.json
```

## Example Workflow

A typical workflow might look like:

1. **Development**: Use single validation during PRD and task development
   ```bash
   python confidence_scoring_integration.py check prd.md tasks.json -o report.json
   ```

2. **Quality Gate**: Add to your CI/CD pipeline as a quality gate
   ```bash
   python confidence_scoring_integration.py ci prd.md tasks.json --fail
   ```

3. **Bulk Analysis**: Run batch processing for analysis across your project
   ```bash
   python confidence_scoring_integration.py batch ./prds/ ./reports/ --workers 4
   ```

```mermaid
sequenceDiagram
    participant Developer
    participant CI as CI/CD Pipeline
    participant Analysis as Analysis Team
    
    Developer->>Developer: Create PRD & Tasks
    Developer->>Developer: Run local validation
    Developer->>CI: Submit PR
    CI->>CI: Run confidence scoring check
    alt Check Passed
        CI->>Developer: PR Approved
    else Check Failed
        CI->>Developer: Fix Required
        Developer->>Developer: Update PRD & Tasks
        Developer->>CI: Resubmit PR
    end
    CI->>Analysis: Merged Code
    Analysis->>Analysis: Run batch analysis
    Analysis->>Developer: Process improvements
```

## Programmatic Usage

You can also use the module programmatically in your Python code:

```python
from confidence_scoring_integration import analyze_with_thresholds, generate_ci_report, load_config

# Load custom configuration
config = load_config("my_custom_config.json")

# Load your PRD and tasks
prd_text = "..."
tasks = [...]

# Analyze with quality thresholds
passed, report = analyze_with_thresholds(prd_text, tasks, config)

# Generate CI report
ci_report = generate_ci_report("path/to/prd.md", "path/to/tasks.json", config_file="my_custom_config.json")
``` 