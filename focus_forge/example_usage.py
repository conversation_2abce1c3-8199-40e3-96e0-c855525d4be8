#!/usr/bin/env python3
"""
Example usage of the confidence scoring integration API.

This script demonstrates how to use the confidence scoring integration
programmatically in your own Python applications.
"""
import json
import sys
from pathlib import Path

# Import the integration functions
from confidence_scoring_integration import (
    load_config,
    analyze_with_thresholds,
    generate_improvement_plan,
    generate_ci_report,
    batch_process
)

def example_single_analysis():
    """Example of analyzing a single PRD and tasks pair."""
    print("\n=== Example: Single PRD/Tasks Analysis ===")
    
    # Load a custom configuration
    config = load_config()
    
    # Override some configuration settings
    config["thresholds"]["confidence"] = 0.75  # Increase confidence threshold
    
    # Load PRD text
    prd_file = "example_prd.md"
    try:
        with open(prd_file, 'r') as f:
            prd_text = f.read()
    except FileNotFoundError:
        # Create a sample PRD if file doesn't exist
        prd_text = """# Example PRD
        
## Overview
This is an example PRD for testing the confidence scoring system.

## Requirements
1. The system must allow users to log in with email and password.
2. Users should be able to reset their password if they forget it.
3. The system will display a dashboard with key metrics upon login.
4. It would be nice if users could customize their dashboard layout.
"""
        # Save the sample PRD
        with open(prd_file, 'w') as f:
            f.write(prd_text)
            
    # Load tasks
    tasks_file = "example_tasks.json"
    try:
        with open(tasks_file, 'r') as f:
            tasks = json.load(f)
    except FileNotFoundError:
        # Create sample tasks if file doesn't exist
        tasks = [
            {
                "id": "TASK-001",
                "title": "Implement user login",
                "description": "Create authentication system with email/password login",
                "priority": "high",
                "tags": ["authentication", "frontend"]
            },
            {
                "id": "TASK-002",
                "title": "Create user dashboard",
                "description": "Implement the main dashboard to display user metrics",
                "priority": "medium",
                "tags": ["frontend", "dashboard"]
            },
            {
                "id": "TASK-003",
                "title": "Set up database schema",
                "description": "Design and implement the database schema for users and metrics",
                "priority": "high",
                "tags": ["backend", "database"]
            }
        ]
        # Save the sample tasks
        with open(tasks_file, 'w') as f:
            json.dump(tasks, f, indent=2)
    
    # Analyze with thresholds
    passed, report = analyze_with_thresholds(prd_text, tasks, config)
    
    # Print results
    print(f"Passed quality thresholds: {passed}")
    print(f"Confidence score: {report['confidence_score']:.2f}")
    print(f"Clarity score: {report['clarity_score']:.2f}")
    print(f"Coverage score: {report['coverage_score']:.2f}")
    
    # Generate improvement plan if thresholds weren't met
    if not passed:
        print("\nGenerating improvement plan...")
        plan = generate_improvement_plan(prd_text, tasks, config)
        
        # Print some improvement suggestions
        if plan["prd_improvements"]:
            print("\nPRD Improvement Suggestions:")
            for i, suggestion in enumerate(plan["prd_improvements"][:2], 1):
                print(f"{i}. {suggestion['action']}: {suggestion['original']}")
                print(f"   Suggested: {suggestion['replacement']}")
        
        if plan["task_improvements"]:
            print("\nTask Improvement Suggestions:")
            for i, suggestion in enumerate(plan["task_improvements"][:2], 1):
                print(f"{i}. {suggestion['action']}")
                if "task_id" in suggestion:
                    print(f"   Task: {suggestion['task_id']}")
                print(f"   Rationale: {suggestion['rationale']}")


def example_ci_integration():
    """Example of CI/CD integration."""
    print("\n=== Example: CI/CD Integration ===")
    
    # Use the example files created in the previous example
    prd_file = "example_prd.md"
    tasks_file = "example_tasks.json"
    
    if not Path(prd_file).exists() or not Path(tasks_file).exists():
        print(f"Error: Example files not found. Run example_single_analysis() first.")
        return
    
    # Generate CI report
    ci_report = generate_ci_report(prd_file, tasks_file)
    
    # Print CI report summary
    print(f"CI Report Status: {ci_report['status']}")
    print(f"Confidence Score: {ci_report['scores']['confidence']:.2f}")
    print(f"Clarity Score: {ci_report['scores']['clarity']:.2f}")
    print(f"Coverage Score: {ci_report['scores']['coverage']:.2f}")
    
    # Check if there are critical issues
    if ci_report["status"] != "success" and "critical_issues" in ci_report:
        print("\nCritical Issues:")
        for i, issue in enumerate(ci_report["critical_issues"], 1):
            print(f"{i}. {issue['description']}")


def example_batch_processing():
    """Example of batch processing multiple PRD/tasks pairs."""
    print("\n=== Example: Batch Processing ===")
    
    # Create example directory
    example_dir = Path("example_batch")
    example_dir.mkdir(exist_ok=True)
    
    # Create a few sample PRD and tasks files
    for i in range(1, 4):
        # Create PRD file
        prd_file = example_dir / f"prd_{i}.md"
        with open(prd_file, 'w') as f:
            f.write(f"""# Example PRD {i}
            
## Overview
This is example PRD {i} for testing batch processing.

## Requirements
1. Requirement A{i} is mandatory.
2. Requirement B{i} should be implemented.
3. Requirement C{i} is optional but nice to have.
""")
        
        # Create tasks file
        tasks_file = example_dir / f"prd_{i}_tasks.json"
        with open(tasks_file, 'w') as f:
            tasks = [
                {
                    "id": f"TASK-{i}01",
                    "title": f"Implement requirement A{i}",
                    "description": f"Create functionality for requirement A{i}",
                    "priority": "high"
                },
                {
                    "id": f"TASK-{i}02",
                    "title": f"Implement requirement B{i}",
                    "description": f"Create functionality for requirement B{i}",
                    "priority": "medium"
                }
            ]
            json.dump(tasks, f, indent=2)
    
    # Create output directory
    output_dir = Path("example_batch_reports")
    output_dir.mkdir(exist_ok=True)
    
    # Run batch processing
    print(f"Processing PRDs in {example_dir}...")
    results = batch_process(
        str(example_dir),
        str(output_dir),
        prd_pattern="*.md",
        tasks_pattern="*_tasks.json",
        max_workers=2
    )
    
    # Print summary
    print(f"\nBatch processing complete!")
    print(f"Total PRDs processed: {results['total']}")
    print(f"Passed: {results['passed']}")
    print(f"Failed: {results['failed']}")
    print(f"Errors: {results['errors']}")
    print(f"\nDetailed reports saved to: {output_dir}")


if __name__ == "__main__":
    print("Confidence Scoring Integration - Example Usage")
    
    example_single_analysis()
    example_ci_integration()
    example_batch_processing()
    
    print("\nAll examples completed!") 