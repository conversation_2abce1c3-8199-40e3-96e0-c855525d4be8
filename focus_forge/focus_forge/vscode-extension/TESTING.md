# Focus Forge VS Code Extension Testing

This document outlines the testing approach for the Focus Forge VS Code extension.

## Test Framework

The extension uses:
- **Mocha** - Test runner
- **VSCode Test API** - VS Code integration testing
- **Node.js Assert** - Assertions

## Test Structure

The tests are organized into several suites:

1. **Extension Tests** - Verify extension activation and command registration
2. **TaskProvider Tests** - Test the core task provider functionality
3. **Tree Provider Tests** - Test the sidebar view providers

## Running Tests

### Prerequisites

- Node.js 16+ and npm
- VS Code
- Xvfb (for headless testing on Linux)

### Option 1: Using npm

```bash
# Install dependencies
npm install

# Run tests
npm test
```

### Option 2: Using nix-shell

```bash
# Run the test script
./run-tests.sh
```

### Option 3: VS Code Launch Configuration

1. Open the extension in VS Code
2. Navigate to the Debug panel
3. Select "Extension Tests"
4. Press F5 to run

## Adding New Tests

To add a new test suite:

1. Create a new file in `src/test/suite/` named `*.test.ts`
2. Import the necessary modules
3. Use the `suite` and `test` functions to define test cases
4. Run the tests to verify

## Test Mocking

The tests use several mocking strategies:

- **Temporary Files** - Creating temporary task files
- **VS Code API Mocks** - Overriding VS Code API functions
- **Context Mocks** - Simplified extension context object

## Continuous Integration

For CI/CD pipelines, the tests can be run headlessly using:

```bash
xvfb-run -a npm test
```

This runs the tests in a virtual framebuffer, which is necessary since VS Code requires a graphical environment. 