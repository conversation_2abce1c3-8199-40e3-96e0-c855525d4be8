import * as vscode from 'vscode';
import { TaskProvider } from './taskProvider';
import { CurrentFileTasksProvider } from './currentFileTasksProvider';
import { ModuleTasksProvider } from './moduleTasksProvider';
import { RelatedTasksProvider } from './relatedTasksProvider';

export function activate(context: vscode.ExtensionContext) {
    console.log('Focus Forge extension is now active');

    // Initialize the task provider to manage all tasks
    const taskProvider = new TaskProvider(context);

    // Register tree view providers for the different task views
    const currentFileTasksProvider = new CurrentFileTasksProvider(taskProvider);
    const currentFileTreeView = vscode.window.createTreeView('focus-forge-current-file', {
        treeDataProvider: currentFileTasksProvider,
        showCollapseAll: true
    });
    
    const moduleTasksProvider = new ModuleTasksProvider(taskProvider);
    const moduleTreeView = vscode.window.createTreeView('focus-forge-module-tasks', {
        treeDataProvider: moduleTasksProvider,
        showCollapseAll: true
    });
    
    const relatedTasksProvider = new RelatedTasksProvider(taskProvider);
    const relatedTreeView = vscode.window.createTreeView('focus-forge-related-tasks', {
        treeDataProvider: relatedTasksProvider,
        showCollapseAll: true
    });

    // Register commands
    context.subscriptions.push(
        vscode.commands.registerCommand('focus-forge.refreshTasks', () => {
            taskProvider.refresh();
            currentFileTasksProvider.refresh();
            moduleTasksProvider.refresh();
            relatedTasksProvider.refresh();
        }),

        vscode.commands.registerCommand('focus-forge.createTask', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showInformationMessage('No active editor found');
                return;
            }

            const selection = editor.selection;
            const text = editor.document.getText(selection);
            
            if (!text) {
                vscode.window.showInformationMessage('No text selected');
                return;
            }

            const taskTitle = await vscode.window.showInputBox({
                prompt: 'Enter task title',
                value: text.length > 50 ? text.substring(0, 50) + '...' : text
            });

            if (taskTitle) {
                await taskProvider.createTask(taskTitle, editor.document.uri.fsPath, {
                    startLine: selection.start.line,
                    endLine: selection.end.line
                });
                vscode.commands.executeCommand('focus-forge.refreshTasks');
            }
        }),

        vscode.commands.registerCommand('focus-forge.linkTaskToCode', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showInformationMessage('No active editor found');
                return;
            }

            const tasks = await taskProvider.getAllTasks();
            const quickPickItems = tasks.map(task => ({
                label: `${task.id}: ${task.title}`,
                task
            }));

            const selected = await vscode.window.showQuickPick(quickPickItems, {
                placeHolder: 'Select a task to link to current position'
            });

            if (selected) {
                const position = editor.selection.active;
                await taskProvider.linkTaskToCode(selected.task.id, editor.document.uri.fsPath, {
                    line: position.line,
                    character: position.character
                });
                vscode.commands.executeCommand('focus-forge.refreshTasks');
            }
        }),

        vscode.commands.registerCommand('focus-forge.setTaskStatus', async (taskId?: string) => {
            if (!taskId) {
                const tasks = await taskProvider.getAllTasks();
                const quickPickItems = tasks.map(task => ({
                    label: `${task.id}: ${task.title}`,
                    task
                }));
    
                const selected = await vscode.window.showQuickPick(quickPickItems, {
                    placeHolder: 'Select a task to update'
                });
                
                if (selected) {
                    taskId = selected.task.id;
                } else {
                    return;
                }
            }

            const statusOptions = ['pending', 'in-progress', 'done', 'deferred'];
            const status = await vscode.window.showQuickPick(statusOptions, {
                placeHolder: 'Select new status'
            });

            if (status && taskId) {
                await taskProvider.updateTaskStatus(taskId, status);
                vscode.commands.executeCommand('focus-forge.refreshTasks');
            }
        })
    );

    // Update tasks when active editor changes
    vscode.window.onDidChangeActiveTextEditor(() => {
        currentFileTasksProvider.refresh();
        moduleTasksProvider.refresh();
        relatedTasksProvider.refresh();
    }, null, context.subscriptions);

    // Register tree views to enable dispose on deactivation
    context.subscriptions.push(currentFileTreeView, moduleTreeView, relatedTreeView);
}

export function deactivate() {
    console.log('Focus Forge extension is now deactivated');
} 