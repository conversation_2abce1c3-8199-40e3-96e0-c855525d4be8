import * as vscode from 'vscode';
import * as path from 'path';
import { Task, TaskProvider } from './taskProvider';
import { TaskTreeItem } from './taskTreeItem';

export class ModuleTasksProvider implements vscode.TreeDataProvider<TaskTreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<TaskTreeItem | undefined | null | void> = 
        new vscode.EventEmitter<TaskTreeItem | undefined | null | void>();
    
    readonly onDidChangeTreeData: vscode.Event<TaskTreeItem | undefined | null | void> = 
        this._onDidChangeTreeData.event;

    constructor(private taskProvider: TaskProvider) {
        this.taskProvider.onDidChangeTaskData(() => this.refresh());
    }

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    getTreeItem(element: TaskTreeItem): vscode.TreeItem {
        return element;
    }

    async getChildren(element?: TaskTreeItem): Promise<TaskTreeItem[]> {
        if (element) {
            return []; // No child items for task items
        }

        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return [];
        }

        const currentFilePath = editor.document.uri.fsPath;
        const currentDir = path.dirname(currentFilePath);
        const tasks = await this.taskProvider.getAllTasks();
        const moduleTasks: {task: Task, relevance: number}[] = [];

        // Find tasks related to files in the same directory (module)
        for (const task of tasks) {
            let isModuleTask = false;
            let highestRelevance = 0;

            // Check each code reference for files in the same directory
            for (const ref of task.codeReferences) {
                const refDir = path.dirname(ref.filePath);
                
                // If the reference is in the same directory or a subdirectory
                if (refDir === currentDir || refDir.startsWith(currentDir + path.sep)) {
                    isModuleTask = true;
                    
                    // Calculate distance between directories (0 for same dir, higher for nested)
                    const relPath = path.relative(currentDir, refDir);
                    const dirDepth = relPath.split(path.sep).length;
                    const relevance = dirDepth === 0 ? 0.8 : 0.8 / (dirDepth + 1);
                    
                    if (relevance > highestRelevance) {
                        highestRelevance = relevance;
                    }
                }
            }

            if (isModuleTask) {
                moduleTasks.push({ task, relevance: highestRelevance });
            }
        }

        // Filter out tasks already shown in the current file view (with high relevance)
        const relevanceThreshold = this.getRelevanceThreshold();
        const currentFileTasks = await Promise.all(tasks.map(async task => {
            const relevance = await this.taskProvider.calculateTaskRelevance(task, currentFilePath);
            return { task, relevance };
        }));
        
        const currentFileTaskIds = new Set(
            currentFileTasks
                .filter(item => item.relevance >= relevanceThreshold)
                .map(item => item.task.id)
        );
        
        const filteredModuleTasks = moduleTasks.filter(item => !currentFileTaskIds.has(item.task.id));

        // Sort tasks by status and then by relevance
        filteredModuleTasks.sort((a, b) => {
            const statusOrder = {
                'in-progress': 0,
                'pending': 1,
                'deferred': 2,
                'done': 3
            };
            
            if (statusOrder[a.task.status] !== statusOrder[b.task.status]) {
                return statusOrder[a.task.status] - statusOrder[b.task.status];
            }
            
            return b.relevance - a.relevance;
        });

        // Apply showCompleted filter
        const showCompleted = vscode.workspace.getConfiguration('focus-forge').get<boolean>('showCompletedTasks') || false;
        const displayTasks = showCompleted 
            ? filteredModuleTasks 
            : filteredModuleTasks.filter(item => item.task.status !== 'done');

        return displayTasks.map(item => {
            return new TaskTreeItem(
                item.task,
                item.relevance,
                vscode.TreeItemCollapsibleState.None
            );
        });
    }
    
    private getRelevanceThreshold(): number {
        const config = vscode.workspace.getConfiguration('focus-forge');
        return config.get<number>('relevanceThreshold') || 0.3;
    }
} 