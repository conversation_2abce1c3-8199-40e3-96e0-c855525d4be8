#!/usr/bin/env python3
"""
Test script for the component detection module using our PRD.

This script demonstrates the capabilities of the component detection module
by analyzing the PRD created for the PRD parser enhancement project.
"""
import os
import json
from focus_forge.component_detection import ComponentDetector, extract_components_from_file


def main():
    """Run the component detection on the PRD parser PRD."""
    prd_file = "prd_parser_prd.md"
    
    if not os.path.exists(prd_file):
        print(f"Error: PRD file '{prd_file}' not found.")
        return
    
    print(f"Analyzing PRD: {prd_file}")
    print("-" * 50)
    
    # Method 1: Use the standalone function
    print("Method 1: Using extract_components_from_file:")
    results = extract_components_from_file(prd_file)
    
    if not results or not results.get("components"):
        print("No components detected.")
    else:
        components = results.get("components", [])
        groups = results.get("groups", [])
        
        print(f"\nDetected {len(components)} components:")
        for component in components:
            types_str = ", ".join(component["types"])
            confidence = component.get("confidence", 0)
            infra_marker = " (Infrastructure)" if component.get("is_infrastructure", False) else ""
            print(f"  - {component['name']} [{types_str}]{infra_marker} (Confidence: {confidence:.2f})")
        
        print(f"\nIdentified {len(groups)} component groups:")
        for group in groups:
            components_str = ", ".join(c["name"] for c in group["components"])
            print(f"  - {group['name']} [{group['type']}]: {components_str}")
    
    print("\n" + "-" * 50)
    
    # Method 2: Use the ComponentDetector class directly
    print("Method 2: Using ComponentDetector class:")
    with open(prd_file, 'r') as f:
        prd_text = f.read()
    
    detector = ComponentDetector()
    
    # First, let's get the components from the entire PRD
    all_results = detector.analyze_prd(prd_text)
    
    # Get a breakdown by section
    section_components = all_results.get("section_components", {})
    
    print(f"\nComponents by section:")
    for section_title, components in section_components.items():
        print(f"\n  Section: {section_title} ({len(components)} components)")
        for component in components:
            types_str = ", ".join(component["types"])
            confidence = component.get("confidence", 0)
            print(f"    - {component['name']} [{types_str}] (Confidence: {confidence:.2f})")
    
    # Generate a JSON representation for potential visualization
    output_file = "component_analysis.json"
    with open(output_file, 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\nAnalysis results saved to {output_file}")
    print("You can use this data for visualization or further analysis.")


if __name__ == "__main__":
    main() 