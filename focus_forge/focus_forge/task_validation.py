#!/usr/bin/env python3
"""
Task Validation module for focus_forge.

This module implements the testing and validation logic for tasks
generated by the Task Generation System (Task T7.3).
"""
import json
import re
from typing import Dict, List, Set, Optional, Tuple, Any, Union, Literal
import logging
from pathlib import Path

from .dependency_graph import DependencyGraph, Dependency, DependencyType

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Type alias for task status
TaskStatus = Literal["pending", "in-progress", "done", "deferred"]


class TaskValidationError(Exception):
    """Exception raised for task validation errors."""
    pass


class TaskValidator:
    """
    Validates generated tasks and performs quality checks.
    
    This class provides methods to validate tasks for schema compliance,
    dependency correctness, and completeness against PRD requirements.
    """
    
    def __init__(self, schema_path: Optional[str] = None):
        """
        Initialize the task validator.
        
        Args:
            schema_path: Optional path to task schema JSON file
        """
        self.schema = self._load_schema(schema_path)
    
    def _load_schema(self, schema_path: Optional[str]) -> Dict[str, Any]:
        """
        Load task schema from file or use default.
        
        Args:
            schema_path: Path to schema file
            
        Returns:
            Dict: Schema definition
        """
        default_schema = {
            "task": {
                "required_fields": ["id", "title", "description", "status", "priority", "dependencies", "details"],
                "optional_fields": ["test_strategy", "subtasks", "component_id", "task_type", "completed_at"],
                "field_types": {
                    "id": str,
                    "title": str,
                    "description": str,
                    "status": str,
                    "priority": str,
                    "dependencies": list,
                    "details": str,
                    "test_strategy": [str, None],
                    "subtasks": list,
                    "component_id": str,
                    "task_type": str,
                    "completed_at": [str, None]
                },
                "allowed_values": {
                    "status": ["pending", "in-progress", "done", "deferred"],
                    "priority": ["high", "medium", "low"]
                }
            },
            "subtask": {
                "required_fields": ["id", "title", "description", "status"],
                "optional_fields": [],
                "field_types": {
                    "id": str,
                    "title": str,
                    "description": str,
                    "status": str
                },
                "allowed_values": {
                    "status": ["pending", "in-progress", "done", "deferred"]
                }
            }
        }
        
        if schema_path:
            try:
                schema_file = Path(schema_path)
                if schema_file.exists():
                    with open(schema_file, 'r') as f:
                        custom_schema = json.load(f)
                        # Merge custom schema with default
                        for key, value in custom_schema.items():
                            if key in default_schema:
                                default_schema[key].update(value)
                            else:
                                default_schema[key] = value
            except Exception as e:
                logger.warning(f"Error loading schema from {schema_path}: {e}")
                logger.warning("Using default schema instead")
        
        return default_schema
    
    def validate_task(self, task: Dict[str, Any], is_subtask: bool = False) -> List[str]:
        """
        Validate a single task against the schema.
        
        Args:
            task: Task dictionary to validate
            is_subtask: Whether this is a subtask
            
        Returns:
            List[str]: List of validation errors (empty if valid)
        """
        errors = []
        schema_key = "subtask" if is_subtask else "task"
        schema = self.schema.get(schema_key, {})
        
        # Check required fields
        for field in schema.get("required_fields", []):
            if field not in task:
                errors.append(f"Missing required field: {field}")
        
        # Check field types
        for field, expected_type in schema.get("field_types", {}).items():
            if field in task:
                # Handle union types (represented as lists in our schema)
                if isinstance(expected_type, list):
                    if not any(isinstance(task[field], t) for t in expected_type if t is not None) and task[field] is not None:
                        errors.append(f"Field {field} has incorrect type. Expected one of: {expected_type}, got: {type(task[field])}")
                elif not isinstance(task[field], expected_type):
                    errors.append(f"Field {field} has incorrect type. Expected: {expected_type}, got: {type(task[field])}")
        
        # Check allowed values
        for field, allowed_values in schema.get("allowed_values", {}).items():
            if field in task and task[field] not in allowed_values:
                errors.append(f"Field {field} has invalid value: {task[field]}. Allowed values: {allowed_values}")
        
        # Content validation
        if "title" in task and len(task["title"]) < 3:
            errors.append(f"Title is too short: {task['title']}")
        
        if "description" in task and len(task["description"]) < 5:
            errors.append(f"Description is too short: {task['description']}")
        
        # Validate subtasks if present
        if "subtasks" in task and isinstance(task["subtasks"], list):
            for i, subtask in enumerate(task["subtasks"]):
                subtask_errors = self.validate_task(subtask, is_subtask=True)
                for error in subtask_errors:
                    errors.append(f"Subtask {i+1}: {error}")
        
        return errors
    
    def validate_tasks(self, tasks: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """
        Validate a list of tasks.
        
        Args:
            tasks: List of tasks to validate
            
        Returns:
            Dict[str, List[str]]: Mapping of task IDs to validation errors
        """
        validation_results = {}
        
        for task in tasks:
            task_id = task.get("id", "unknown")
            errors = self.validate_task(task)
            if errors:
                validation_results[task_id] = errors
        
        return validation_results
    
    def check_schema_compliance(self, tasks: List[Dict[str, Any]]) -> Tuple[bool, Dict[str, List[str]]]:
        """
        Check if tasks comply with the defined schema.
        
        Args:
            tasks: List of tasks to check
            
        Returns:
            Tuple[bool, Dict]: (is_compliant, error_details)
        """
        validation_results = self.validate_tasks(tasks)
        is_compliant = len(validation_results) == 0
        return is_compliant, validation_results
    
    def check_dependency_consistency(self, tasks: List[Dict[str, Any]]) -> Tuple[bool, List[str]]:
        """
        Check if dependencies between tasks are consistent.
        
        Args:
            tasks: List of tasks to check
            
        Returns:
            Tuple[bool, List[str]]: (is_consistent, errors)
        """
        errors = []
        
        # Build a set of all task IDs
        task_ids = {task.get("id") for task in tasks if "id" in task}
        
        # Check dependencies
        for task in tasks:
            task_id = task.get("id", "unknown")
            for dep_id in task.get("dependencies", []):
                if dep_id not in task_ids:
                    errors.append(f"Task {task_id} depends on non-existent task {dep_id}")
                elif dep_id == task_id:
                    errors.append(f"Task {task_id} depends on itself")
        
        # Check for circular dependencies
        try:
            graph = self._build_dependency_graph(tasks)
            cycles = graph.find_cycles()
            if cycles:
                for cycle in cycles:
                    errors.append(f"Circular dependency detected: {' -> '.join(cycle)}")
        except Exception as e:
            errors.append(f"Error checking for circular dependencies: {str(e)}")
        
        is_consistent = len(errors) == 0
        return is_consistent, errors
    
    def check_dependency_structure(self, tasks: List[Dict[str, Any]]) -> Tuple[float, List[Dict[str, Any]]]:
        """
        Perform advanced checks on the dependency structure.
        
        This checks for:
        - Dependency chains (long sequences)
        - Dependency bottlenecks (many tasks depend on a single task)
        - Isolated task clusters
        - Balanced dependency graph structure
        
        Args:
            tasks: List of tasks to check
            
        Returns:
            Tuple[float, List[Dict]]: (structure_score, issues)
        """
        issues = []
        
        # Build dependency graph
        graph = self._build_dependency_graph(tasks)
        
        # Build a map of task IDs to tasks
        task_map = {task.get("id"): task for task in tasks if "id" in task}
        
        # Check for long dependency chains
        long_chains = self._find_long_dependency_chains(graph, min_length=5)
        if long_chains:
            for chain in long_chains:
                # Get task titles for more readable output
                task_titles = [task_map.get(task_id, {}).get("title", task_id) for task_id in chain]
                issues.append({
                    "type": "long_chain",
                    "severity": "medium",
                    "description": f"Long dependency chain of {len(chain)} tasks",
                    "tasks": chain,
                    "task_titles": task_titles
                })
        
        # Check for dependency bottlenecks - explicitly use a lower threshold for testing
        # or adjust based on the total number of tasks
        threshold = min(5, max(3, len(tasks) // 10))  # Adjusted threshold
        bottlenecks = self._find_dependency_bottlenecks(graph, threshold=threshold)
        for task_id, dependent_count in bottlenecks:
            issues.append({
                "type": "bottleneck",
                "severity": "high" if dependent_count > 7 else "medium",
                "description": f"Task {task_id} is a bottleneck with {dependent_count} dependent tasks",
                "task_id": task_id,
                "task_title": task_map.get(task_id, {}).get("title", ""),
                "dependent_count": dependent_count
            })
        
        # Check for isolated clusters
        clusters = self._find_isolated_clusters(graph)
        if len(clusters) > 1:
            # Multiple clusters indicate disconnected groups of tasks
            for i, cluster in enumerate(clusters):
                if len(cluster) < len(tasks) / 10:  # Small clusters (less than 10% of tasks)
                    issues.append({
                        "type": "isolated_cluster",
                        "severity": "medium",
                        "description": f"Isolated cluster of {len(cluster)} tasks",
                        "tasks": list(cluster),
                        "cluster_id": i
                    })
        
        # Calculate balanced score based on issues
        structure_score = 1.0
        
        # Reduce score for each issue
        for issue in issues:
            if issue["severity"] == "high":
                structure_score -= 0.15  # High severity issues reduce score by 15%
            else:
                structure_score -= 0.05  # Medium severity issues reduce score by 5%
        
        # Ensure score stays in 0-1 range
        structure_score = max(0.0, min(1.0, structure_score))
        
        return structure_score, issues
    
    def _find_long_dependency_chains(self, graph: DependencyGraph, min_length: int = 5) -> List[List[str]]:
        """
        Find long dependency chains in the graph.
        
        Args:
            graph: Dependency graph
            min_length: Minimum chain length to report
            
        Returns:
            List of chains, where each chain is a list of task IDs
        """
        long_chains = []
        
        # Get all nodes (task IDs) in the graph
        nodes = graph.get_nodes()
        
        # For each node, find the longest path starting from it
        for start_node in nodes:
            longest_path = graph.find_longest_path(start_node)
            if len(longest_path) >= min_length:
                long_chains.append(longest_path)
        
        # Sort by length (longest first)
        long_chains.sort(key=len, reverse=True)
        
        # Return only the top 5 longest chains to avoid overwhelming the report
        return long_chains[:5]
    
    def _find_dependency_bottlenecks(self, graph: DependencyGraph, threshold: int = 5) -> List[Tuple[str, int]]:
        """
        Find tasks that many other tasks depend on (bottlenecks).
        
        Args:
            graph: Dependency graph
            threshold: Minimum number of dependent tasks to consider a bottleneck
            
        Returns:
            List of (task_id, dependent_count) tuples
        """
        bottlenecks = []
        
        # Count dependencies for each task
        dependency_counts = {}
        
        # Iterate through all nodes and count their dependents
        for node in graph.get_nodes():
            dependents = graph.get_dependents(node)
            if len(dependents) >= threshold:
                bottlenecks.append((node, len(dependents)))
        
        # Sort by count (highest first)
        bottlenecks.sort(key=lambda x: x[1], reverse=True)
        
        return bottlenecks
    
    def _find_isolated_clusters(self, graph: DependencyGraph) -> List[Set[str]]:
        """
        Find isolated clusters of tasks in the graph.
        
        Args:
            graph: Dependency graph
            
        Returns:
            List of sets, where each set contains task IDs in a cluster
        """
        # Get all nodes
        nodes = graph.get_nodes()
        
        # Initialize clusters
        clusters = []
        unassigned = set(nodes)
        
        # For each unassigned node, find its cluster
        while unassigned:
            # Pick a node
            node = next(iter(unassigned))
            
            # Find all connected nodes (its cluster)
            cluster = graph.find_connected_nodes(node)
            
            # Add to clusters
            clusters.append(cluster)
            
            # Remove from unassigned
            unassigned -= cluster
        
        return clusters
    
    def check_task_structure(self, tasks: List[Dict[str, Any]]) -> Tuple[float, List[Dict[str, Any]]]:
        """
        Check the overall task structure for completeness and balance.
        
        This checks for:
        - Distribution of priorities
        - Distribution of task statuses
        - Distribution of task sizes (based on subtasks)
        - Balance of task types
        
        Args:
            tasks: List of tasks to check
            
        Returns:
            Tuple[float, List[Dict]]: (structure_score, issues)
        """
        issues = []
        
        # Count priorities
        priorities = {"high": 0, "medium": 0, "low": 0}
        for task in tasks:
            priority = task.get("priority")
            if priority in priorities:
                priorities[priority] += 1
        
        # Check priority balance
        total_tasks = len(tasks)
        if total_tasks > 0:
            high_ratio = priorities["high"] / total_tasks
            medium_ratio = priorities["medium"] / total_tasks
            low_ratio = priorities["low"] / total_tasks
            
            # Check for too many high priority tasks
            if high_ratio > 0.5:  # More than 50% high priority
                issues.append({
                    "type": "priority_imbalance",
                    "severity": "medium",
                    "description": f"Too many high priority tasks ({priorities['high']} out of {total_tasks})",
                    "recommendation": "Review task priorities and downgrade less critical tasks"
                })
            
            # Check for too few high priority tasks
            if high_ratio < 0.1 and total_tasks > 5:  # Less than 10% high priority
                issues.append({
                    "type": "priority_imbalance",
                    "severity": "low",
                    "description": f"Few high priority tasks ({priorities['high']} out of {total_tasks})",
                    "recommendation": "Consider identifying the most critical tasks as high priority"
                })
        
        # Count statuses
        statuses = {"pending": 0, "in-progress": 0, "done": 0, "deferred": 0}
        for task in tasks:
            status = task.get("status")
            if status in statuses:
                statuses[status] += 1
        
        # Check for too many in-progress tasks
        if total_tasks > 5 and statuses["in-progress"] > 0.3 * total_tasks:
            issues.append({
                "type": "status_imbalance",
                "severity": "medium",
                "description": f"Too many in-progress tasks ({statuses['in-progress']} out of {total_tasks})",
                "recommendation": "Consider focusing on fewer tasks at once"
            })
        
        # Check subtask distribution
        subtask_counts = [len(task.get("subtasks", [])) for task in tasks]
        if subtask_counts:
            avg_subtasks = sum(subtask_counts) / len(subtask_counts)
            
            # Identify tasks with many more subtasks than average
            for task in tasks:
                subtask_count = len(task.get("subtasks", []))
                if subtask_count > 3 * avg_subtasks and subtask_count > 5:
                    issues.append({
                        "type": "large_task",
                        "severity": "medium",
                        "description": f"Task {task.get('id')} has {subtask_count} subtasks (avg: {avg_subtasks:.1f})",
                        "task_id": task.get("id"),
                        "task_title": task.get("title"),
                        "recommendation": "Consider breaking this task into multiple smaller tasks"
                    })
        
        # Calculate structure score based on issues
        structure_score = 1.0
        
        # Reduce score for each issue
        for issue in issues:
            if issue["severity"] == "high":
                structure_score -= 0.15
            elif issue["severity"] == "medium":
                structure_score -= 0.1
            else:  # low
                structure_score -= 0.05
        
        # Ensure score stays in 0-1 range
        structure_score = max(0.0, min(1.0, structure_score))
        
        return structure_score, issues
    
    def _build_dependency_graph(self, tasks: List[Dict[str, Any]]) -> DependencyGraph:
        """
        Build a dependency graph from tasks.
        
        Args:
            tasks: List of tasks
            
        Returns:
            DependencyGraph: Graph representing task dependencies
        """
        graph = DependencyGraph()
        
        # Add all tasks as nodes
        for task in tasks:
            if "id" in task:
                graph.add_node(task["id"])
        
        # Add dependencies as edges, bypassing cycle detection to create the complete graph
        # The cycle detection will be done separately
        for task in tasks:
            if "id" in task:
                source_id = task["id"]
                for dep_id in task.get("dependencies", []):
                    # Force add the dependency to ensure all dependencies are in the graph
                    # even if they would create cycles
                    dependency = Dependency(
                        source_id=source_id,
                        target_id=dep_id,
                        dep_type=DependencyType.DEPENDS_ON
                    )
                    # Add nodes if they don't exist
                    graph.add_node(dependency.source_id)
                    graph.add_node(dependency.target_id)
                    
                    # Add the dependency regardless of cycles
                    graph.edges[dependency.source_id].append(dependency)
                    graph.reverse_edges[dependency.target_id].append(dependency)
        
        return graph
    
    def check_prd_coverage(self, tasks: List[Dict[str, Any]], prd_text: str) -> Tuple[float, List[Dict[str, Any]]]:
        """
        Check how well the tasks cover the requirements in the PRD.
        
        Args:
            tasks: List of tasks
            prd_text: PRD text
            
        Returns:
            Tuple[float, List[Dict[str, Any]]]: (coverage_score, uncovered_items)
        """
        # Extract key phrases that might represent requirements
        key_phrases = self._extract_key_phrases(prd_text)
        
        # Extract semantic areas from the PRD
        semantic_areas = self._extract_semantic_areas(prd_text)
        
        # Combine all task text to check for coverage
        all_task_text = ""
        task_details_by_id = {}
        
        for task in tasks:
            task_id = task.get("id", "unknown")
            task_text = f"{task.get('title', '')} {task.get('description', '')} {task.get('details', '')} {task.get('test_strategy', '')}"
            
            # Include subtasks
            for subtask in task.get("subtasks", []):
                task_text += f" {subtask.get('title', '')} {subtask.get('description', '')}"
            
            all_task_text += task_text + " "
            task_details_by_id[task_id] = task_text.lower()
        
        all_task_text = all_task_text.lower()
        
        # Check coverage of key phrases
        covered_phrases = 0
        uncovered_items = []
        
        for phrase in key_phrases:
            phrase_lower = phrase.lower()
            if phrase_lower in all_task_text:
                covered_phrases += 1
            else:
                # Try to find partial matches
                words = phrase_lower.split()
                if len(words) > 3:  # Only check partial matches for longer phrases
                    significant_words = [w for w in words if len(w) > 3 and w not in ["the", "and", "for", "with", "that", "this"]]
                    if all(word in all_task_text for word in significant_words):
                        covered_phrases += 0.5  # Partial credit
                        continue
                
                # No match found, add to uncovered with possible relevant tasks
                relevant_tasks = self._find_related_tasks(phrase_lower, task_details_by_id)
                uncovered_items.append({
                    "phrase": phrase,
                    "severity": "high" if self._is_critical_requirement(phrase) else "medium",
                    "possible_related_tasks": relevant_tasks,
                    "semantic_area": self._determine_semantic_area(phrase, semantic_areas)
                })
        
        # Check semantic area coverage
        area_coverage = {}
        for area, content in semantic_areas.items():
            area_content = " ".join(content).lower()
            content_words = set(w for w in area_content.split() if len(w) > 4)
            task_words = set(w for w in all_task_text.split() if len(w) > 4)
            if content_words:  # Avoid division by zero
                area_coverage[area] = len(content_words.intersection(task_words)) / len(content_words)
            else:
                area_coverage[area] = 1.0
        
        # Add poorly covered areas to uncovered items
        for area, score in area_coverage.items():
            if score < 0.6:  # Less than 60% coverage
                uncovered_items.append({
                    "phrase": f"Section: {area}",
                    "severity": "medium",
                    "coverage_score": score,
                    "semantic_area": area,
                    "type": "section_coverage"
                })
        
        # Calculate coverage score (weighted average of phrase coverage and area coverage)
        phrase_coverage = covered_phrases / len(key_phrases) if key_phrases else 1.0
        semantic_coverage = sum(area_coverage.values()) / len(area_coverage) if area_coverage else 1.0
        
        # Phrase coverage is 70% of score, semantic area coverage is 30%
        coverage_score = 0.7 * phrase_coverage + 0.3 * semantic_coverage
        
        return coverage_score, uncovered_items
    
    def _extract_semantic_areas(self, prd_text: str) -> Dict[str, List[str]]:
        """
        Extract semantic areas (sections and subsections) from the PRD.
        
        Args:
            prd_text: PRD text
            
        Returns:
            Dict mapping section titles to section content
        """
        sections = {}
        current_section = "introduction"
        current_content = []
        
        # Split text into lines for processing
        lines = prd_text.split('\n')
        
        for line in lines:
            # Check if line is a header
            header_match = re.match(r'^(#+)\s+(.+)$', line)
            if header_match:
                # Save previous section
                if current_content:
                    if current_section in sections:
                        sections[current_section].extend(current_content)
                    else:
                        sections[current_section] = current_content
                
                # Start new section
                level = len(header_match.group(1))
                section_title = header_match.group(2).strip()
                
                if level == 1:  # Main section
                    current_section = section_title.lower()
                else:  # Subsection
                    current_section = section_title.lower()
                
                current_content = []
            else:
                # Add non-empty lines to current section
                if line.strip():
                    current_content.append(line.strip())
        
        # Save the last section
        if current_content:
            if current_section in sections:
                sections[current_section].extend(current_content)
            else:
                sections[current_section] = current_content
        
        return sections
    
    def _extract_key_phrases(self, prd_text: str) -> List[str]:
        """
        Extract key phrases from PRD text that might represent requirements.
        
        Args:
            prd_text: PRD text
            
        Returns:
            List[str]: Extracted key phrases
        """
        phrases = []
        
        # Look for phrases matching these patterns
        patterns = [
            r'"([^"]+)"',  # Quoted phrases
            r'must\s+([^\.;]+)[\.;]',  # Must requirements
            r'should\s+([^\.;]+)[\.;]',  # Should requirements
            r'required\s+to\s+([^\.;]+)[\.;]',  # Required to X
            r'needs\s+to\s+([^\.;]+)[\.;]',  # Needs to X
            r'will\s+([^\.;]+)[\.;]',  # Will X
            r'shall\s+([^\.;]+)[\.;]',  # Shall X
            r'is\s+required\s+([^\.;]+)[\.;]',  # Is required X
            r'are\s+required\s+([^\.;]+)[\.;]',  # Are required X
            r'important\s+([^\.;]+)[\.;]',  # Important X
            r'critical\s+([^\.;]+)[\.;]',  # Critical X
            r'essential\s+([^\.;]+)[\.;]',  # Essential X
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, prd_text, re.IGNORECASE)
            for match in matches:
                phrase = match.group(1).strip()
                if len(phrase) > 5:  # Ignore very short phrases
                    phrases.append(phrase)
        
        # Extract list items, which often represent requirements
        list_pattern = r'[-*•]\s+([^\n]+)'
        for match in re.finditer(list_pattern, prd_text):
            phrase = match.group(1).strip()
            if len(phrase) > 5:  # Ignore very short phrases
                phrases.append(phrase)
        
        # Add section headings as key phrases, especially "Features", "Requirements" etc.
        heading_pattern = r'#+\s+(.+)$'
        important_section_keywords = ["feature", "requirement", "function", "capability"]
        for match in re.finditer(heading_pattern, prd_text, re.MULTILINE):
            heading = match.group(1).strip()
            if len(heading) > 5:  # Ignore very short headings
                # Prioritize headings that likely describe features or requirements
                if any(keyword in heading.lower() for keyword in important_section_keywords):
                    phrases.append(f"SECTION: {heading}")  # Mark as a section heading
                else:
                    phrases.append(heading)
        
        return phrases
    
    def _is_critical_requirement(self, phrase: str) -> bool:
        """
        Determine if a phrase represents a critical requirement.
        
        Args:
            phrase: The phrase to check
            
        Returns:
            bool: True if the phrase is likely a critical requirement
        """
        critical_indicators = [
            "must", "required", "shall", "critical", "essential", "important",
            "security", "performance", "compliance", "mandatory"
        ]
        phrase_lower = phrase.lower()
        return any(indicator in phrase_lower for indicator in critical_indicators)
    
    def _find_related_tasks(self, phrase: str, task_details: Dict[str, str]) -> List[str]:
        """
        Find tasks that might be related to an uncovered phrase.
        
        Args:
            phrase: The phrase to find related tasks for
            task_details: Map of task IDs to task content
            
        Returns:
            List[str]: IDs of tasks that might be related
        """
        related_tasks = []
        
        # Split the phrase into significant words (ignore common words)
        words = phrase.split()
        significant_words = [w for w in words if len(w) > 3 and w not in [
            "the", "and", "for", "with", "that", "this", "will", "should",
            "must", "shall", "can", "may", "would", "could", "have"
        ]]
        
        # Calculate relevance score for each task
        task_scores = {}
        for task_id, task_text in task_details.items():
            score = 0
            for word in significant_words:
                if word in task_text:
                    score += 1
            
            if score > 0:
                # Calculate percentage of words found
                relevance = score / len(significant_words)
                if relevance > 0.3:  # At least 30% of significant words found
                    task_scores[task_id] = relevance
        
        # Return top 3 most relevant tasks
        sorted_tasks = sorted(task_scores.items(), key=lambda x: x[1], reverse=True)
        related_tasks = [task_id for task_id, _ in sorted_tasks[:3]]
        
        return related_tasks
    
    def _determine_semantic_area(self, phrase: str, semantic_areas: Dict[str, List[str]]) -> str:
        """
        Determine which semantic area a phrase belongs to.
        
        Args:
            phrase: The phrase to check
            semantic_areas: Map of section titles to section content
            
        Returns:
            str: The semantic area that best matches the phrase
        """
        phrase_lower = phrase.lower()
        
        # First check if the phrase appears directly in a section
        for area, content in semantic_areas.items():
            content_text = " ".join(content).lower()
            if phrase_lower in content_text:
                return area
        
        # If no direct match, use word overlap
        best_area = "unknown"
        best_overlap = 0
        
        words = set(phrase_lower.split())
        significant_words = {w for w in words if len(w) > 3}
        
        for area, content in semantic_areas.items():
            area_words = set(" ".join(content).lower().split())
            area_significant_words = {w for w in area_words if len(w) > 3}
            
            if significant_words and area_significant_words:
                overlap = len(significant_words.intersection(area_significant_words)) / len(significant_words)
                if overlap > best_overlap:
                    best_overlap = overlap
                    best_area = area
        
        return best_area if best_overlap > 0.2 else "unknown"
    
    def validate_and_report(self, tasks: List[Dict[str, Any]], prd_text: Optional[str] = None) -> Dict[str, Any]:
        """
        Perform comprehensive validation and generate a report.
        
        Args:
            tasks: List of tasks to validate
            prd_text: Optional PRD text for coverage validation
            
        Returns:
            Dict: Validation report with all validation results
        """
        from datetime import datetime
        
        report = {
            "validation_time": datetime.now().isoformat(),
            "num_tasks": len(tasks),
            "num_subtasks": sum(len(task.get("subtasks", [])) for task in tasks),
            "schema_compliance": {},
            "dependency_validation": {},
            "dependency_structure": {},
            "task_structure": {},
            "prd_coverage": {},
            "orphaned_tasks": [],
            "task_quality": {},
            "overall_quality_score": 0.0,
            "recommendations": []
        }
        
        # Check schema compliance
        is_compliant, compliance_errors = self.check_schema_compliance(tasks)
        report["schema_compliance"] = {
            "is_compliant": is_compliant,
            "num_errors": sum(len(errors) for errors in compliance_errors.values()),
            "errors_by_task": compliance_errors
        }
        
        # Check dependency consistency
        is_consistent, consistency_errors = self.check_dependency_consistency(tasks)
        report["dependency_validation"] = {
            "is_consistent": is_consistent,
            "num_errors": len(consistency_errors),
            "errors": consistency_errors
        }
        
        # Check dependency structure
        structure_score, structure_issues = self.check_dependency_structure(tasks)
        report["dependency_structure"] = {
            "structure_score": structure_score,
            "issues": structure_issues,
            "has_issues": len(structure_issues) > 0
        }
        
        # Check task structure
        task_structure_score, task_structure_issues = self.check_task_structure(tasks)
        report["task_structure"] = {
            "structure_score": task_structure_score,
            "issues": task_structure_issues,
            "has_issues": len(task_structure_issues) > 0
        }
        
        # Check for orphaned tasks (tasks that no other task depends on)
        dependency_targets = set()
        for task in tasks:
            for dep_id in task.get("dependencies", []):
                dependency_targets.add(dep_id)
        
        for task in tasks:
            task_id = task.get("id", "unknown")
            if task_id not in dependency_targets and task.get("status") != "done":
                # This task is not a dependency for any other task
                report["orphaned_tasks"].append({
                    "id": task_id,
                    "title": task.get("title", ""),
                    "status": task.get("status", ""),
                    "priority": task.get("priority", "")
                })
        
        # Calculate task quality metrics
        task_quality = {}
        for task in tasks:
            task_id = task.get("id", "unknown")
            quality_score = self._calculate_task_quality(task)
            task_quality[task_id] = quality_score
        
        # Calculate average quality
        if task_quality:
            avg_quality = sum(task_quality.values()) / len(task_quality)
            report["task_quality"] = {
                "average_score": avg_quality,
                "scores_by_task": task_quality,
                "poor_quality_tasks": [
                    task_id for task_id, score in task_quality.items() 
                    if score < 0.7  # Tasks with less than 70% quality
                ]
            }
        
        # Check PRD coverage if PRD text is provided
        if prd_text:
            coverage_score, uncovered_items = self.check_prd_coverage(tasks, prd_text)
            
            # Group uncovered items by severity
            high_severity = [item for item in uncovered_items if item.get("severity") == "high"]
            medium_severity = [item for item in uncovered_items if item.get("severity") == "medium"]
            
            # Group uncovered items by semantic area
            by_semantic_area = {}
            for item in uncovered_items:
                area = item.get("semantic_area", "unknown")
                if area not in by_semantic_area:
                    by_semantic_area[area] = []
                by_semantic_area[area].append(item)
            
            report["prd_coverage"] = {
                "coverage_score": coverage_score,
                "num_uncovered_items": len(uncovered_items),
                "uncovered_items": uncovered_items,
                "by_severity": {
                    "high": high_severity,
                    "medium": medium_severity
                },
                "by_semantic_area": by_semantic_area
            }
        
        # Calculate overall quality score
        quality_scores = []
        
        # Schema compliance contributes 20% to quality score
        if report["schema_compliance"]["num_errors"] == 0:
            quality_scores.append(0.2)
        else:
            # Calculate based on percentage of tasks with errors
            error_ratio = len(report["schema_compliance"]["errors_by_task"]) / len(tasks)
            quality_scores.append(0.2 * (1 - error_ratio))
        
        # Dependency consistency contributes 15% to quality score
        if report["dependency_validation"]["is_consistent"]:
            quality_scores.append(0.15)
        else:
            # Calculate based on number of errors per task
            error_ratio = report["dependency_validation"]["num_errors"] / len(tasks)
            quality_scores.append(0.15 * (1 - min(error_ratio, 1.0)))
        
        # Dependency structure contributes 15% to quality score
        quality_scores.append(0.15 * report["dependency_structure"]["structure_score"])
        
        # Task structure contributes 15% to quality score
        quality_scores.append(0.15 * report["task_structure"]["structure_score"])
        
        # Task quality contributes 15% to quality score
        if "average_score" in report.get("task_quality", {}):
            quality_scores.append(0.15 * report["task_quality"]["average_score"])
        
        # PRD coverage contributes 20% to quality score
        if "coverage_score" in report.get("prd_coverage", {}):
            quality_scores.append(0.2 * report["prd_coverage"]["coverage_score"])
        
        # Calculate overall score
        report["overall_quality_score"] = sum(quality_scores)
        
        # Generate recommendations
        recommendations = []
        
        if report["schema_compliance"]["num_errors"] > 0:
            recommendations.append({
                "type": "schema_compliance",
                "description": "Fix schema compliance errors",
                "priority": "high" if report["schema_compliance"]["num_errors"] > 5 else "medium",
                "details": f"There are {report['schema_compliance']['num_errors']} schema errors in {len(report['schema_compliance']['errors_by_task'])} tasks"
            })
        
        if report["dependency_validation"]["num_errors"] > 0:
            recommendations.append({
                "type": "dependency",
                "description": "Resolve dependency inconsistencies",
                "priority": "high",
                "details": f"There are {report['dependency_validation']['num_errors']} dependency errors"
            })
        
        # Add dependency structure recommendations
        if report["dependency_structure"]["has_issues"]:
            # Add recommendations for different issue types
            bottlenecks = [issue for issue in report["dependency_structure"]["issues"] if issue["type"] == "bottleneck" and issue["severity"] == "high"]
            if bottlenecks:
                recommendations.append({
                    "type": "dependency_structure",
                    "description": "Address dependency bottlenecks",
                    "priority": "high",
                    "details": f"There are {len(bottlenecks)} high-severity bottleneck tasks that many other tasks depend on",
                    "issues": bottlenecks
                })
            
            long_chains = [issue for issue in report["dependency_structure"]["issues"] if issue["type"] == "long_chain"]
            if long_chains:
                recommendations.append({
                    "type": "dependency_structure",
                    "description": "Simplify long dependency chains",
                    "priority": "medium",
                    "details": f"There are {len(long_chains)} long dependency chains",
                    "issues": long_chains
                })
        
        # Add task structure recommendations
        if report["task_structure"]["has_issues"]:
            priority_issues = [issue for issue in report["task_structure"]["issues"] if issue["type"] == "priority_imbalance"]
            if priority_issues:
                recommendations.append({
                    "type": "task_structure",
                    "description": "Balance task priorities",
                    "priority": "medium",
                    "details": "The distribution of task priorities is imbalanced",
                    "issues": priority_issues
                })
            
            large_task_issues = [issue for issue in report["task_structure"]["issues"] if issue["type"] == "large_task"]
            if large_task_issues:
                recommendations.append({
                    "type": "task_structure",
                    "description": "Break down large tasks",
                    "priority": "medium",
                    "details": f"There are {len(large_task_issues)} tasks that are significantly larger than average",
                    "issues": large_task_issues
                })
        
        if report["orphaned_tasks"]:
            recommendations.append({
                "type": "orphaned_tasks",
                "description": "Review orphaned tasks",
                "priority": "medium",
                "details": f"There are {len(report['orphaned_tasks'])} tasks that no other task depends on"
            })
        
        if "poor_quality_tasks" in report.get("task_quality", {}) and report["task_quality"]["poor_quality_tasks"]:
            recommendations.append({
                "type": "task_quality",
                "description": "Improve quality of poorly defined tasks",
                "priority": "medium",
                "details": f"There are {len(report['task_quality']['poor_quality_tasks'])} tasks with poor quality descriptions or details"
            })
        
        if "by_severity" in report.get("prd_coverage", {}) and report["prd_coverage"]["by_severity"]["high"]:
            high_severity_items = report["prd_coverage"]["by_severity"]["high"]
            recommendations.append({
                "type": "prd_coverage",
                "description": "Address critical uncovered requirements",
                "priority": "high",
                "details": f"There are {len(high_severity_items)} critical requirements not covered by tasks",
                "items": high_severity_items[:5]  # Include up to 5 examples
            })
        
        if "coverage_score" in report.get("prd_coverage", {}) and report["prd_coverage"]["coverage_score"] < 0.8:
            recommendations.append({
                "type": "prd_coverage",
                "description": "Improve overall PRD coverage",
                "priority": "medium" if report["prd_coverage"]["coverage_score"] > 0.6 else "high",
                "details": f"PRD coverage is at {report['prd_coverage']['coverage_score']:.1%}, with {report['prd_coverage']['num_uncovered_items']} uncovered requirements"
            })
        
        # Sort recommendations by priority (high first)
        report["recommendations"] = sorted(recommendations, key=lambda x: 0 if x["priority"] == "high" else 1)
        
        return report
    
    def _calculate_task_quality(self, task: Dict[str, Any]) -> float:
        """
        Calculate a quality score for a task based on its content.
        
        Args:
            task: The task to evaluate
            
        Returns:
            float: Quality score between 0.0 and 1.0
        """
        score_components = []
        
        # Check title quality
        title = task.get("title", "")
        if len(title) > 50:
            score_components.append(1.0)  # Good length title
        elif len(title) > 20:
            score_components.append(0.8)  # Decent length title
        elif len(title) > 10:
            score_components.append(0.5)  # Short title
        else:
            score_components.append(0.2)  # Very short title
            
        # Check description quality
        description = task.get("description", "")
        if len(description) > 100:
            score_components.append(1.0)  # Good length description
        elif len(description) > 50:
            score_components.append(0.8)  # Decent length description
        elif len(description) > 20:
            score_components.append(0.5)  # Short description
        else:
            score_components.append(0.2)  # Very short description
            
        # Check details quality
        details = task.get("details", "")
        if len(details) > 200:
            score_components.append(1.0)  # Good length details
        elif len(details) > 100:
            score_components.append(0.8)  # Decent length details
        elif len(details) > 50:
            score_components.append(0.5)  # Short details
        else:
            score_components.append(0.2)  # Very short details
            
        # Check test strategy if present
        test_strategy = task.get("test_strategy", "")
        if test_strategy:
            if len(test_strategy) > 100:
                score_components.append(1.0)  # Good length test strategy
            elif len(test_strategy) > 50:
                score_components.append(0.8)  # Decent length test strategy
            else:
                score_components.append(0.5)  # Short test strategy
        else:
            score_components.append(0.0)  # No test strategy
            
        # Check subtasks if present
        subtasks = task.get("subtasks", [])
        if subtasks:
            # Calculate average quality of subtasks
            subtask_quality = 0.0
            for subtask in subtasks:
                subtask_title = subtask.get("title", "")
                subtask_description = subtask.get("description", "")
                
                if len(subtask_title) > 20 and len(subtask_description) > 50:
                    subtask_quality += 1.0  # Good subtask
                elif len(subtask_title) > 10 and len(subtask_description) > 20:
                    subtask_quality += 0.7  # Decent subtask
                else:
                    subtask_quality += 0.3  # Poor subtask
            
            average_subtask_quality = subtask_quality / len(subtasks)
            score_components.append(average_subtask_quality)
        
        # Calculate overall score (average of all components)
        return sum(score_components) / len(score_components)


def validate_tasks_file(tasks_file: str, prd_file: Optional[str] = None, schema_file: Optional[str] = None) -> Dict[str, Any]:
    """
    Validate tasks from a JSON file.
    
    Args:
        tasks_file: Path to tasks JSON file
        prd_file: Optional path to PRD text file
        schema_file: Optional path to schema JSON file
        
    Returns:
        Dict: Validation report
    """
    # Load tasks
    try:
        with open(tasks_file, 'r') as f:
            data = json.load(f)
            if isinstance(data, dict) and "tasks" in data:
                tasks = data["tasks"]
            elif isinstance(data, list):
                tasks = data
            else:
                raise TaskValidationError(f"Invalid tasks file format: {tasks_file}")
    except Exception as e:
        raise TaskValidationError(f"Error loading tasks file: {str(e)}")
    
    # Load PRD if provided
    prd_text = None
    if prd_file:
        try:
            with open(prd_file, 'r') as f:
                prd_text = f.read()
        except Exception as e:
            logger.warning(f"Error loading PRD file: {str(e)}")
    
    # Validate
    validator = TaskValidator(schema_file)
    return validator.validate_and_report(tasks, prd_text) 