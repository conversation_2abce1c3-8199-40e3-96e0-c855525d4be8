{"foundation": {"title": "Implement {name} Foundation", "description": "Create the core foundation for {name}.", "details": "Design and implement the foundation architecture for {name}. This includes core data structures, interfaces, and base functionality. Ensure the design is modular and extensible to support future requirements.", "test_strategy": "Implement comprehensive unit tests for all core functionality. Create integration tests to verify interactions between components. Document testing approach for future extensions.", "subtasks": [{"title": "Design {name} Architecture", "description": "Create the architectural design for {name} including component diagrams, data flow, and interface specifications."}, {"title": "Implement Core {name} Components", "description": "Develop the core components of {name} following the architectural design. Implement interfaces, base classes, and essential functionality."}, {"title": "Create {name} Tests and Documentation", "description": "Develop unit and integration tests for {name}. Create documentation including usage examples and API reference."}]}, "feature": {"title": "Implement {name} Feature", "description": "Develop the {name} feature as specified in the requirements.", "details": "Create the {name} feature with the specified functionality. Implement user interface components, business logic, and data persistence as needed. Ensure proper error handling, input validation, and performance considerations.", "test_strategy": "Create unit tests for components and integration tests for the feature as a whole. Implement UI tests if applicable. Verify performance meets requirements.", "subtasks": [{"title": "Design {name} Feature Components", "description": "Design the components and interactions for the {name} feature. Define data models, interfaces, and user experience."}, {"title": "Implement {name} Business Logic", "description": "Develop the core business logic for the {name} feature. Implement data processing, validation, and application rules."}, {"title": "Create {name} User Interface", "description": "Implement the user interface for the {name} feature. Ensure proper integration with business logic and a responsive user experience."}]}, "api": {"title": "Implement {name} API", "description": "Develop the API endpoints for {name} functionality.", "details": "Create RESTful API endpoints for {name}. Implement request validation, response formatting, error handling, and security measures. Document the API for consumers.", "test_strategy": "Implement automated API tests covering happy paths and edge cases. Test response formats, error handling, and authentication/authorization if applicable.", "subtasks": [{"title": "Design {name} API Endpoints", "description": "Define the API endpoints, request/response formats, and authentication requirements for {name}."}, {"title": "Implement {name} API Handlers", "description": "Develop the API handlers, including request validation, business logic integration, and response formatting."}, {"title": "Create {name} API Documentation and Tests", "description": "Generate API documentation and implement automated tests to verify functionality and security."}]}, "ui": {"title": "Implement {name} User Interface", "description": "Create the user interface for {name}.", "details": "Develop the user interface for {name} according to design specifications. Implement responsive layouts, user interactions, and accessibility features. Ensure cross-browser compatibility and performance optimization.", "test_strategy": "Implement UI component tests and integration tests. Verify responsive behavior, accessibility compliance, and browser compatibility.", "subtasks": [{"title": "Create {name} UI Components", "description": "Develop the UI components needed for {name} following design specifications and component architecture."}, {"title": "Implement {name} Interactions and State Management", "description": "Implement user interactions, state management, and data flow for the {name} interface."}, {"title": "Optimize and Test {name} UI", "description": "Optimize performance, ensure accessibility compliance, and implement automated tests for the {name} interface."}]}, "database": {"title": "Implement {name} Data Storage", "description": "Create the data storage solution for {name}.", "details": "Design and implement the database schema for {name}. Create data access layers, migration scripts, and data validation. Consider performance optimization for queries and data integrity.", "test_strategy": "Test data operations including CRUD functionality. Verify data integrity, migration functionality, and query performance.", "subtasks": [{"title": "Design {name} Database Schema", "description": "Create the database schema design for {name} including tables, relationships, indices, and constraints."}, {"title": "Implement {name} Data Access Layer", "description": "Develop the data access layer for {name} including models, repositories, and query optimization."}, {"title": "Create {name} Migrations and Tests", "description": "Implement database migrations and automated tests to verify data operations and integrity."}]}, "integration": {"title": "Integrate {name} with {target}", "description": "Create integration between {name} and {target}.", "details": "Implement the necessary integration between {name} and {target}. Create data transformation, API communication, and error handling. Ensure proper authentication and reliable data exchange.", "test_strategy": "Implement integration tests with mocked external systems. Test failure scenarios and recovery mechanisms. Verify data consistency across systems.", "subtasks": [{"title": "Design {name}-{target} Integration Architecture", "description": "Define the integration architecture between {name} and {target}, including data flow, transformation, and API contracts."}, {"title": "Implement {name}-{target} Communication", "description": "Develop the communication layer between {name} and {target}, including authentication, data exchange, and error handling."}, {"title": "Test and Monitor {name}-{target} Integration", "description": "Create automated tests for the integration and implement monitoring for operational use."}]}, "testing": {"title": "Implement {name} Testing Framework", "description": "Create the testing infrastructure for {name}.", "details": "Develop comprehensive testing infrastructure for {name}. Implement test frameworks, fixtures, mocks, and continuous integration setup. Create testing guidelines and documentation.", "test_strategy": "Verify test framework functionality with meta-tests. Ensure coverage reporting and integration with CI/CD pipelines.", "subtasks": [{"title": "Design {name} Test Architecture", "description": "Define the testing architecture for {name}, including test types, frameworks, and coverage requirements."}, {"title": "Implement {name} Test Infrastructure", "description": "Develop the test infrastructure including fixtures, mocks, and helpers for {name} testing."}, {"title": "Configure {name} CI/CD Integration", "description": "Set up continuous integration for {name} tests, including reporting and quality gates."}]}, "documentation": {"title": "Create {name} Documentation", "description": "Develop comprehensive documentation for {name}.", "details": "Create user and developer documentation for {name}. Include usage guides, API references, architecture documents, and deployment instructions. Ensure documentation is accessible and maintainable.", "test_strategy": "Verify documentation accuracy with peer reviews. Test code examples and ensure documentation builds correctly in the documentation system.", "subtasks": [{"title": "Create {name} User Documentation", "description": "Develop user-facing documentation for {name}, including usage guides and examples."}, {"title": "Create {name} Developer Documentation", "description": "Create technical documentation for developers, including API references and architecture documents."}, {"title": "Implement {name} Documentation Infrastructure", "description": "Set up documentation generation, hosting, and version control integration."}]}, "optimization": {"title": "Optimize {name} Performance", "description": "Improve the performance and efficiency of {name}.", "details": "Identify and resolve performance bottlenecks in {name}. Implement optimizations for speed, memory usage, and resource utilization. Conduct performance testing to verify improvements.", "test_strategy": "Establish performance baselines and targets. Implement automated performance tests to verify optimizations. Test at various load levels.", "subtasks": [{"title": "Profile and Analyze {name} Performance", "description": "Conduct profiling and analysis to identify performance bottlenecks in {name}."}, {"title": "Implement {name} Optimizations", "description": "Develop and implement performance optimizations for identified bottlenecks in {name}."}, {"title": "Validate {name} Performance Improvements", "description": "Conduct performance testing to validate optimizations and document performance characteristics."}]}, "deployment": {"title": "Implement {name} Deployment System", "description": "Create the deployment infrastructure for {name}.", "details": "Develop the deployment system for {name}. Create configuration management, environment setup, automation scripts, and monitoring. Ensure repeatability and reliability of deployments.", "test_strategy": "Test deployment processes in isolated environments. Verify rollback capabilities and monitoring integration. Test deployment pipeline end-to-end.", "subtasks": [{"title": "Design {name} Deployment Architecture", "description": "Define the deployment architecture for {name}, including environments, infrastructure requirements, and deployment processes."}, {"title": "Implement {name} Deployment Automation", "description": "Develop automation scripts and configuration for reliable {name} deployments across environments."}, {"title": "Create {name} Monitoring and Rollback Systems", "description": "Implement monitoring, alerting, and rollback mechanisms for {name} deployments."}]}}