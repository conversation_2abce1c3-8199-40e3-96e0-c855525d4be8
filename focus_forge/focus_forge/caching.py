#!/usr/bin/env python3
"""
Caching module for Focus Forge.

This module provides caching functionality to improve performance by avoiding
redundant computations and storing intermediate results for frequent operations.
"""
import os
import json
import hashlib
import time
import logging
from functools import wraps
from pathlib import Path
from typing import Dict, Any, Callable, Optional, Union, TypeVar, cast

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Type variables for generic function type hints
T = TypeVar('T')
R = TypeVar('R')

# Default cache directory
CACHE_DIR = Path(os.path.expanduser("~/.focus_forge/cache"))


def ensure_cache_dir(cache_dir: Path = CACHE_DIR) -> Path:
    """
    Ensure the cache directory exists.
    
    Args:
        cache_dir: Path to the cache directory
        
    Returns:
        Path to the cache directory
    """
    cache_dir.mkdir(parents=True, exist_ok=True)
    return cache_dir


class MemoryCache:
    """In-memory cache for function results."""
    
    def __init__(self, max_size: int = 100, ttl: int = 3600):
        """
        Initialize the memory cache.
        
        Args:
            max_size: Maximum number of items to store in cache
            ttl: Time to live for cache entries in seconds (default: 1 hour)
        """
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.max_size = max_size
        self.ttl = ttl
    
    def get(self, key: str) -> Optional[Any]:
        """
        Get a value from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found or expired
        """
        if key not in self.cache:
            return None
        
        entry = self.cache[key]
        
        # Check if entry has expired
        if time.time() - entry["timestamp"] > self.ttl:
            del self.cache[key]
            return None
        
        return entry["value"]
    
    def set(self, key: str, value: Any) -> None:
        """
        Set a value in the cache.
        
        Args:
            key: Cache key
            value: Value to cache
        """
        # If cache is full, remove the oldest entry
        if len(self.cache) >= self.max_size:
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k]["timestamp"])
            del self.cache[oldest_key]
        
        self.cache[key] = {
            "value": value,
            "timestamp": time.time()
        }
    
    def clear(self) -> None:
        """Clear all cache entries."""
        self.cache.clear()


class DiskCache:
    """Persistent disk-based cache for function results."""
    
    def __init__(self, cache_dir: Path = CACHE_DIR, ttl: int = 86400):
        """
        Initialize the disk cache.
        
        Args:
            cache_dir: Directory to store cache files
            ttl: Time to live for cache entries in seconds (default: 1 day)
        """
        self.cache_dir = ensure_cache_dir(cache_dir)
        self.ttl = ttl
    
    def _get_cache_path(self, key: str) -> Path:
        """Get the file path for a cache key."""
        hashed_key = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{hashed_key}.json"
    
    def get(self, key: str) -> Optional[Any]:
        """
        Get a value from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found or expired
        """
        cache_path = self._get_cache_path(key)
        
        if not cache_path.exists():
            return None
        
        try:
            with open(cache_path, 'r') as f:
                entry = json.load(f)
            
            # Check if entry has expired
            if time.time() - entry["timestamp"] > self.ttl:
                cache_path.unlink(missing_ok=True)
                return None
            
            return entry["value"]
        except (json.JSONDecodeError, KeyError, IOError) as e:
            logger.warning(f"Error reading cache file {cache_path}: {str(e)}")
            return None
    
    def set(self, key: str, value: Any) -> None:
        """
        Set a value in the cache.
        
        Args:
            key: Cache key
            value: Value to cache
        """
        cache_path = self._get_cache_path(key)
        
        try:
            # Ensure value is JSON serializable
            entry = {
                "value": value,
                "timestamp": time.time()
            }
            
            with open(cache_path, 'w') as f:
                json.dump(entry, f)
        except (TypeError, IOError) as e:
            logger.warning(f"Error writing to cache file {cache_path}: {str(e)}")
    
    def clear(self) -> None:
        """Clear all cache entries."""
        for cache_file in self.cache_dir.glob("*.json"):
            try:
                cache_file.unlink()
            except IOError as e:
                logger.warning(f"Error deleting cache file {cache_file}: {str(e)}")


class CompositeCache:
    """Combined memory and disk cache for optimal performance."""
    
    def __init__(self, memory_cache: MemoryCache, disk_cache: DiskCache):
        """
        Initialize the composite cache.
        
        Args:
            memory_cache: Memory cache instance
            disk_cache: Disk cache instance
        """
        self.memory_cache = memory_cache
        self.disk_cache = disk_cache
    
    def get(self, key: str) -> Optional[Any]:
        """
        Get a value from the cache, checking memory first, then disk.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found
        """
        # Check memory cache first
        value = self.memory_cache.get(key)
        if value is not None:
            return value
        
        # If not in memory, check disk cache
        value = self.disk_cache.get(key)
        if value is not None:
            # Update memory cache for faster access next time
            self.memory_cache.set(key, value)
            return value
        
        return None
    
    def set(self, key: str, value: Any) -> None:
        """
        Set a value in both memory and disk caches.
        
        Args:
            key: Cache key
            value: Value to cache
        """
        self.memory_cache.set(key, value)
        self.disk_cache.set(key, value)
    
    def clear(self) -> None:
        """Clear all cache entries."""
        self.memory_cache.clear()
        self.disk_cache.clear()


def create_default_cache() -> CompositeCache:
    """
    Create a default composite cache.
    
    Returns:
        Configured CompositeCache instance
    """
    memory_cache = MemoryCache(max_size=100, ttl=3600)  # 1 hour TTL
    disk_cache = DiskCache(ttl=86400 * 7)  # 1 week TTL
    return CompositeCache(memory_cache, disk_cache)


# Global cache instance
default_cache = create_default_cache()


def memoize(func: Callable[..., R]) -> Callable[..., R]:
    """
    Decorator to memoize function results in memory.
    
    Args:
        func: Function to memoize
        
    Returns:
        Wrapped function with memoization
    """
    cache: Dict[str, Any] = {}
    
    @wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> R:
        # Create a cache key from the function name and arguments
        key_parts = [func.__name__]
        key_parts.extend(str(arg) for arg in args)
        key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
        cache_key = ":".join(key_parts)
        
        # Check if result is in cache
        if cache_key in cache:
            return cast(R, cache[cache_key])
        
        # Calculate result and store in cache
        result = func(*args, **kwargs)
        cache[cache_key] = result
        return result
    
    return wrapper


def persistent_cache(key_prefix: str, cache_instance: Optional[CompositeCache] = None) -> Callable[[Callable[..., R]], Callable[..., R]]:
    """
    Decorator to cache function results persistently.
    
    Args:
        key_prefix: Prefix for cache keys
        cache_instance: Cache instance to use (default: global cache)
        
    Returns:
        Decorator function that wraps the target function
    """
    cache = cache_instance or default_cache
    
    def decorator(func: Callable[..., R]) -> Callable[..., R]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> R:
            # Create a cache key
            key_parts = [key_prefix, func.__name__]
            
            # Add args and kwargs to key
            # For complex objects, use their repr or hash
            for arg in args:
                if isinstance(arg, (str, int, float, bool, type(None))):
                    key_parts.append(str(arg))
                else:
                    # For complex objects, use a hash of their string representation
                    arg_str = str(arg)
                    arg_hash = hashlib.md5(arg_str.encode()).hexdigest()[:8]
                    key_parts.append(arg_hash)
            
            # Add sorted kwargs to ensure consistent keys
            for k, v in sorted(kwargs.items()):
                if isinstance(v, (str, int, float, bool, type(None))):
                    key_parts.append(f"{k}={v}")
                else:
                    v_str = str(v)
                    v_hash = hashlib.md5(v_str.encode()).hexdigest()[:8]
                    key_parts.append(f"{k}={v_hash}")
            
            cache_key = ":".join(key_parts)
            
            # Try to get cached result
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {func.__name__}")
                return cast(R, cached_result)
            
            # Calculate result and store in cache
            logger.debug(f"Cache miss for {func.__name__}")
            result = func(*args, **kwargs)
            
            try:
                cache.set(cache_key, result)
            except Exception as e:
                logger.warning(f"Failed to cache result for {func.__name__}: {str(e)}")
            
            return result
        
        return wrapper
    
    return decorator 