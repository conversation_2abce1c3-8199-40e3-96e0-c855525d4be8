# Development Container for Focus Forge

This directory contains configuration files for setting up a development container for the Focus Forge project. Development containers provide a consistent development environment across different machines, making it easier to collaborate and onboard new developers.

## Setup

### Prerequisites

- [Docker](https://www.docker.com/products/docker-desktop)
- [Visual Studio Code](https://code.visualstudio.com/)
- [VS Code Remote - Containers extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers)

### Using the Development Container

1. Open VS Code
2. Open the command palette (F1 or Ctrl+Shift+P)
3. Select "Remote-Containers: Open Folder in Container..."
4. Navigate to and select the `focus_forge` directory
5. VS Code will build the container and open the project in it

## Features

- Python 3.10 environment
- Pre-installed dependencies:
  - click, rich, requests (project dependencies)
  - pytest, pre-commit, ruff, black, mypy (development tools)
- VS Code extensions:
  - Python extension
  - Pylance
  - Ruff linter and formatter
  - Auto Docstring generator

## Alternative: Manual Development Setup

If you prefer not to use containers, you can set up a development environment manually:

```bash
# Create a virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -e ".[dev]"

# Set up pre-commit hooks
pre-commit install
```

## Running Tests

```bash
# Inside the container or virtual environment
pytest
``` 