{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    python310
    python310Packages.click
    python310Packages.rich
    python310Packages.requests
    python310Packages.pytest
    python310Packages.pytest-cov
    python310Packages.mypy
    pre-commit
  ];
  
  shellHook = ''
    alias focus-forge="python3 ${./focus_forge.py}"
    alias run-tests="python3 -m pytest -v tests/"
    
    echo ""
    echo "Focus Forge nix-shell environment activated!"
    echo "Commands:"
    echo "  focus-forge           Run the Focus Forge CLI tool"
    echo "  run-tests             Run test suite"
    echo "  pytest -v tests/      Run tests directly with pytest"
    echo "  pytest --cov=focus_forge tests/  Run tests with coverage"
    echo ""
  '';
} 