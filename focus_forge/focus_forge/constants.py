"""Constants for the Focus Forge package."""
import os
from pathlib import Path

# Application constants
APP_NAME = "focus_forge"
VERSION = "0.8.0"
CONFIG_FILE = ".focus_forge_config"
TASKS_FILE = "tasks.json"

# Determine tasks directory path
# If running as a submodule, tasks directory should be at the project root
# rather than inside the submodule
def get_tasks_dir():
    """
    Get the path to the tasks directory.
    
    If used as a submodule, this will point to tasks/ at the project root
    rather than inside the focus_forge module.
    """
    module_dir = Path(__file__).parent.absolute()
    
    # Check if this is a submodule by looking for parent directories
    # Typical structure when used as submodule:
    # /project_root/
    #   |-- focus_forge/  (submodule)
    #   |-- tasks/        (should be here)
    
    # First, check if we're in a focus_forge subdirectory
    if module_dir.name == "focus_forge":
        parent_dir = module_dir.parent
        
        # Check if a tasks directory exists at the parent level
        if (parent_dir / "tasks").exists():
            return "tasks"  # Use relative path from project root
    
    # Default case - direct tasks directory
    return "tasks"

TASKS_DIR = get_tasks_dir()
AI_FOLDER = ".ai"
CLAUDE_FOLDER = ".claude" 