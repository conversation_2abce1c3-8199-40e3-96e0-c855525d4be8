"""
CLI interface for the task format upgrader system.

This module provides command-line interfaces to upgrade task data between format versions.
"""

import click
from rich.console import Console
from rich.panel import Panel
from rich.markdown import Markdown
from rich.syntax import Syntax
from rich.table import Table
import json
from typing import Optional
from datetime import datetime
from pathlib import Path

from .task_upgrader import TaskUpgrader, SCHEMA_VERSIONS
from .constants import TASKS_FILE

console = Console()


def upgrade_cmd(ctx: click.Context, target_version: Optional[str] = None, skip_backup: bool = False) -> None:
    """
    Upgrade the tasks file to the specified version.
    
    Args:
        ctx: Click context
        target_version: Target version to upgrade to, or None for latest
        skip_backup: Whether to skip creating a backup
    """
    try:
        # Validate target version if specified
        if target_version is not None and target_version not in SCHEMA_VERSIONS:
            console.print(f"[red]Error:[/] Invalid target version: {target_version}")
            console.print(f"Available versions: {', '.join(SCHEMA_VERSIONS)}")
            return
        
        # Perform upgrade
        upgraded_data = TaskUpgrader.upgrade_tasks_file(target_version, backup=not skip_backup)
        
        # Print success message
        console.print(f"[green]Success:[/] Upgraded tasks file to version {upgraded_data['version']}")
        
    except FileNotFoundError:
        console.print(f"[red]Error:[/] Tasks file not found: {TASKS_FILE}")
    except ValueError as e:
        console.print(f"[red]Error:[/] {str(e)}")
    except Exception as e:
        console.print(f"[red]Error:[/] An unexpected error occurred: {str(e)}")


def silent_upgrade_cmd(ctx: click.Context, target_version: Optional[str] = None, skip_backup: bool = False) -> None:
    """
    Upgrade the tasks file to the specified version without prompting for confirmation.
    
    Args:
        ctx: Click context
        target_version: Target version to upgrade to, or None for latest
        skip_backup: Whether to skip creating a backup
    """
    try:
        # Validate target version if specified
        if target_version is not None and target_version not in SCHEMA_VERSIONS:
            console.print(f"[red]Error:[/] Invalid target version: {target_version}")
            console.print(f"Available versions: {', '.join(SCHEMA_VERSIONS)}")
            return
        
        # Perform silent upgrade
        success, data, message = TaskUpgrader.auto_upgrade_tasks_file_silent(target_version, backup=not skip_backup)
        
        # Print result message
        if success:
            console.print(f"[green]Success:[/] {message}")
        else:
            console.print(f"[red]Error:[/] {message}")
        
    except Exception as e:
        console.print(f"[red]Error:[/] An unexpected error occurred: {str(e)}")


def validate_cmd(ctx: click.Context, version: Optional[str] = None) -> None:
    """
    Validate the tasks file against a specific schema version.
    
    Args:
        ctx: Click context
        version: Version to validate against, or None to use the version in the file
    """
    try:
        # Load the tasks data
        with open(TASKS_FILE, 'r') as f:
            data = json.load(f)
        
        # Validate the data
        is_valid, errors = TaskUpgrader.validate(data, version)
        
        if is_valid:
            detected_version = version or TaskUpgrader.detect_version(data)
            console.print(f"[green]Valid:[/] Tasks file is valid for version {detected_version}")
        else:
            console.print(f"[red]Invalid:[/] Tasks file failed validation:")
            for error in errors:
                console.print(f"  - {error}")
        
    except FileNotFoundError:
        console.print(f"[red]Error:[/] Tasks file not found: {TASKS_FILE}")
    except ValueError as e:
        console.print(f"[red]Error:[/] {str(e)}")
    except Exception as e:
        console.print(f"[red]Error:[/] An unexpected error occurred: {str(e)}")


def list_versions_cmd(ctx: click.Context) -> None:
    """
    List all registered schema versions.
    
    Args:
        ctx: Click context
    """
    if not SCHEMA_VERSIONS:
        console.print("[yellow]Warning:[/] No schema versions are registered")
        return
    
    console.print("[bold]Registered schema versions:[/]")
    for version in SCHEMA_VERSIONS:
        console.print(f"  - {version}")


def check_upgrade_path_cmd(ctx: click.Context, from_version: str, to_version: Optional[str] = None) -> None:
    """
    Check the upgrade path between two versions.
    
    Args:
        ctx: Click context
        from_version: Source version
        to_version: Target version, or None for latest
    """
    try:
        # If no target version specified, use the latest
        if to_version is None:
            to_version = SCHEMA_VERSIONS[-1]
        
        # Get the upgrade path
        upgrade_path = TaskUpgrader.get_upgrade_path(from_version, to_version)
        
        if not upgrade_path:
            console.print(f"[green]No upgrade needed:[/] Already at version {to_version}")
            return
        
        console.print(f"[bold]Upgrade path from {from_version} to {to_version}:[/]")
        for i, (source, target) in enumerate(upgrade_path, 1):
            console.print(f"  {i}. {source} -> {target}")
        
    except ValueError as e:
        console.print(f"[red]Error:[/] {str(e)}")
    except Exception as e:
        console.print(f"[red]Error:[/] An unexpected error occurred: {str(e)}")


def show_schema_diff_cmd(ctx: click.Context, from_version: str, to_version: str) -> None:
    """
    Show the differences between two schema versions.
    
    Args:
        ctx: Click context
        from_version: Source version
        to_version: Target version
    """
    console.print(f"[bold]Schema differences between {from_version} and {to_version}:[/]")
    console.print("[yellow]This is a placeholder for actual schema diff functionality[/]")
    console.print("In a complete implementation, this would show:")
    console.print("- Added fields")
    console.print("- Removed fields")
    console.print("- Modified field types or constraints")
    console.print("- Changes to validation rules")


def backup_cmd(ctx: click.Context) -> None:
    """
    Create a backup of the tasks file.
    
    Args:
        ctx: Click context
    """
    try:
        backup_path = TaskUpgrader.backup_tasks_file()
        console.print(f"[green]Success:[/] Created backup at {backup_path}")
    except FileNotFoundError:
        console.print(f"[red]Error:[/] Tasks file not found: {TASKS_FILE}")
    except Exception as e:
        console.print(f"[red]Error:[/] An unexpected error occurred: {str(e)}")


def detect_version_cmd(ctx: click.Context) -> None:
    """
    Detect the version of the tasks file.
    
    Args:
        ctx: Click context
    """
    try:
        # Load the tasks data
        with open(TASKS_FILE, 'r') as f:
            data = json.load(f)
        
        # Detect the version
        version = TaskUpgrader.detect_version(data)
        console.print(f"[green]Detected version:[/] {version}")
        
    except FileNotFoundError:
        console.print(f"[red]Error:[/] Tasks file not found: {TASKS_FILE}")
    except ValueError as e:
        console.print(f"[red]Error:[/] {str(e)}")
    except Exception as e:
        console.print(f"[red]Error:[/] An unexpected error occurred: {str(e)}")


def upgrade_history_cmd(ctx: click.Context, json_format: bool = False) -> None:
    """
    Show the history of task file upgrades.
    
    Args:
        ctx: Click context
        json_format: Whether to output in JSON format
    """
    # Get upgrade history
    upgrades = TaskUpgrader.get_upgrade_history()
    
    if not upgrades:
        console.print("[yellow]No upgrade history found.[/]")
        return
    
    # Output in JSON format if requested
    if json_format:
        console.print(json.dumps(upgrades, indent=2))
        return
    
    # Output in table format
    table = Table(title="Upgrade History")
    table.add_column("ID", style="cyan")
    table.add_column("Timestamp", style="green")
    table.add_column("From", style="yellow")
    table.add_column("To", style="blue")
    table.add_column("Backup Path", style="magenta")
    
    # Sort upgrades by timestamp, newest first
    upgrades = sorted(upgrades, key=lambda u: u.get("timestamp", ""), reverse=True)
    
    for upgrade in upgrades:
        # Format timestamp
        try:
            timestamp = datetime.fromisoformat(upgrade.get("timestamp", "")).strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError):
            timestamp = upgrade.get("timestamp", "")
        
        table.add_row(
            upgrade.get("id", ""),
            timestamp,
            upgrade.get("from_version", ""),
            upgrade.get("to_version", ""),
            upgrade.get("backup_path", "")
        )
    
    console.print(table)


def list_backups_cmd(ctx: click.Context, json_format: bool = False) -> None:
    """
    List available backup files.
    
    Args:
        ctx: Click context
        json_format: Whether to output in JSON format
    """
    # Get available backups
    backups = TaskUpgrader.get_available_backups()
    
    if not backups:
        console.print("[yellow]No backup files found.[/]")
        return
    
    # Output in JSON format if requested
    if json_format:
        console.print(json.dumps(backups, indent=2))
        return
    
    # Output in table format
    table = Table(title="Available Backups")
    table.add_column("Filename", style="cyan")
    table.add_column("Timestamp", style="green")
    table.add_column("Version", style="yellow")
    table.add_column("Path", style="blue")
    
    for backup in backups:
        table.add_row(
            backup.get("filename", ""),
            backup.get("timestamp_str", ""),
            backup.get("version", ""),
            backup.get("path", "")
        )
    
    console.print(table)


def restore_backup_cmd(ctx: click.Context, backup_path: str, yes: bool = False) -> None:
    """
    Restore tasks file from a backup.
    
    Args:
        ctx: Click context
        backup_path: Path to the backup file
        yes: Skip confirmation prompt
    """
    try:
        # Check if backup exists
        if not Path(backup_path).exists():
            console.print(f"[red]Error:[/] Backup file not found: {backup_path}")
            return
        
        # Get confirmation unless --yes is specified
        if not yes:
            console.print(f"[yellow]Warning:[/] This will replace your current tasks file with the backup.")
            if not click.confirm("Are you sure you want to continue?"):
                console.print("[yellow]Aborted.[/]")
                return
        
        # Restore from backup
        data = TaskUpgrader.restore_from_backup(backup_path)
        
        console.print(f"[green]Success:[/] Restored tasks file from backup.")
        console.print(f"Version: {data.get('version', 'unknown')}")
        console.print(f"Tasks: {len(data.get('tasks', []))}")
        
    except FileNotFoundError as e:
        console.print(f"[red]Error:[/] {str(e)}")
    except ValueError as e:
        console.print(f"[red]Error:[/] {str(e)}")
    except Exception as e:
        console.print(f"[red]Error:[/] An unexpected error occurred: {str(e)}")


def restore_by_id_cmd(ctx: click.Context, backup_id: str, yes: bool = False) -> None:
    """
    Restore tasks file from a backup by its ID from the upgrade history.
    
    Args:
        ctx: Click context
        backup_id: ID of the backup from upgrade history
        yes: Skip confirmation prompt
    """
    try:
        # Find backup path by ID
        backup_path = TaskUpgrader.find_backup_by_id(backup_id)
        
        if not backup_path:
            console.print(f"[red]Error:[/] No backup found with ID: {backup_id}")
            console.print("Use [blue]focus-forge upgrade-history[/] to see available backups.")
            return
        
        # Use the regular restore command
        restore_backup_cmd(ctx, backup_path, yes)
        
    except Exception as e:
        console.print(f"[red]Error:[/] An unexpected error occurred: {str(e)}")


def register_upgrader_commands(cli: click.Group) -> None:
    """
    Register all upgrader commands with the CLI.
    
    Args:
        cli: Click CLI group to register commands with
    """
    @cli.command(name="upgrade-tasks")
    @click.option("--target-version", "-t", help="Target version to upgrade to")
    @click.option("--skip-backup", "-s", is_flag=True, help="Skip creating a backup before upgrading")
    @click.pass_context
    def upgrade_tasks(ctx: click.Context, target_version: Optional[str] = None, skip_backup: bool = False) -> None:
        """Upgrade the tasks file to the specified version."""
        upgrade_cmd(ctx, target_version, skip_backup)
    
    @cli.command(name="auto-upgrade-tasks")
    @click.option("--target-version", "-t", help="Target version to upgrade to")
    @click.option("--skip-backup", "-s", is_flag=True, help="Skip creating a backup before upgrading")
    @click.pass_context
    def auto_upgrade_tasks(ctx: click.Context, target_version: Optional[str] = None, skip_backup: bool = False) -> None:
        """Silently upgrade the tasks file without prompting for confirmation."""
        silent_upgrade_cmd(ctx, target_version, skip_backup)
    
    @cli.command(name="validate-tasks")
    @click.option("--version", "-v", help="Version to validate against")
    @click.pass_context
    def validate_tasks(ctx: click.Context, version: Optional[str] = None) -> None:
        """Validate the tasks file against a specific schema version."""
        validate_cmd(ctx, version)
    
    @cli.command(name="list-versions")
    @click.pass_context
    def list_versions(ctx: click.Context) -> None:
        """List all registered schema versions."""
        list_versions_cmd(ctx)
    
    @cli.command(name="check-upgrade-path")
    @click.argument("from-version")
    @click.option("--to-version", "-t", help="Target version")
    @click.pass_context
    def check_upgrade_path(ctx: click.Context, from_version: str, to_version: Optional[str] = None) -> None:
        """Check the upgrade path between two versions."""
        check_upgrade_path_cmd(ctx, from_version, to_version)
    
    @cli.command(name="show-schema-diff")
    @click.argument("from-version")
    @click.argument("to-version")
    @click.pass_context
    def show_schema_diff(ctx: click.Context, from_version: str, to_version: str) -> None:
        """Show the differences between two schema versions."""
        show_schema_diff_cmd(ctx, from_version, to_version)
    
    @cli.command(name="backup-tasks")
    @click.pass_context
    def backup_tasks(ctx: click.Context) -> None:
        """Create a backup of the tasks file."""
        backup_cmd(ctx)
    
    @cli.command(name="detect-version")
    @click.pass_context
    def detect_version(ctx: click.Context) -> None:
        """Detect the version of the tasks file."""
        detect_version_cmd(ctx)
    
    @cli.command(name="upgrade-history")
    @click.option("--json", "json_format", is_flag=True, help="Output in JSON format")
    @click.pass_context
    def upgrade_history(ctx: click.Context, json_format: bool = False) -> None:
        """Show the history of task file upgrades."""
        upgrade_history_cmd(ctx, json_format)
    
    @cli.command(name="list-backups")
    @click.option("--json", "json_format", is_flag=True, help="Output in JSON format")
    @click.pass_context
    def list_backups(ctx: click.Context, json_format: bool = False) -> None:
        """List available backup files."""
        list_backups_cmd(ctx, json_format)
    
    @cli.command(name="restore-backup")
    @click.argument("backup_path")
    @click.option("--yes", "-y", is_flag=True, help="Skip confirmation prompt")
    @click.pass_context
    def restore_backup(ctx: click.Context, backup_path: str, yes: bool = False) -> None:
        """Restore tasks file from a backup file."""
        restore_backup_cmd(ctx, backup_path, yes)
    
    @cli.command(name="restore-by-id")
    @click.argument("backup_id")
    @click.option("--yes", "-y", is_flag=True, help="Skip confirmation prompt")
    @click.pass_context
    def restore_by_id(ctx: click.Context, backup_id: str, yes: bool = False) -> None:
        """Restore tasks file from a backup by its ID from the upgrade history."""
        restore_by_id_cmd(ctx, backup_id, yes) 