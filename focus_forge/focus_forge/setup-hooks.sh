#!/usr/bin/env bash

# Exit on error
set -e

# Check if nix-shell is available
if ! command -v nix-shell &> /dev/null; then
    echo "Error: nix-shell not found. Please install Nix: https://nixos.org/download.html"
    exit 1
fi

echo "Setting up pre-commit hooks..."

# Run in nix-shell to ensure pre-commit is available
nix-shell --run "pre-commit install"

echo "Pre-commit hooks installed successfully!"
echo "Hooks will run automatically on git commit."
echo "To run manually: pre-commit run --all-files" 