#!/usr/bin/env python3
"""
Simple integration test for the priority inference module.

This script tests importing and using the Priority Inference Engine programmatically.
"""
import os
import sys
import json
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import the module to test
from focus_forge import PriorityInferenceEngine, PriorityLevel, ComplexityLevel

def main():
    """Main function to run the integration test."""
    print("Running priority inference integration test...")
    
    # Create an instance of the engine
    engine = PriorityInferenceEngine()
    
    # Test with different priority and complexity levels
    test_cases = [
        {
            "name": "Critical Security Issue",
            "text": "Critical security vulnerability that must be fixed immediately. It affects all users and may expose sensitive data."
        },
        {
            "name": "Nice-to-have Feature",
            "text": "Add a dark mode option to the UI. This is a nice-to-have feature that could be implemented when time permits."
        },
        {
            "name": "Complex Integration",
            "text": "Integrate with multiple external payment providers. This is a complex task requiring significant coordination and testing."
        }
    ]
    
    for test in test_cases:
        # Analyze the text
        print(f"\nAnalyzing: {test['name']}")
        result = engine.analyze_task(test["text"])
        
        # Print the results
        print(f"Priority: {result['priority']}")
        print(f"Complexity: {result['complexity']}")
        print(f"Combined Score: {result['combined_score']:.2f}")
        
        # Print priority scores
        print("\nPriority Scores:")
        for category, score in result["priority_scores"].items():
            if category != "overall":
                print(f"  {category}: {score:.2f}")
        
        # Print complexity scores
        print("\nComplexity Scores:")
        for category, score in result["complexity_scores"].items():
            if category != "overall":
                print(f"  {category}: {score:.2f}")
    
    print("\nIntegration test completed successfully.")
    return 0

if __name__ == "__main__":
    sys.exit(main()) 