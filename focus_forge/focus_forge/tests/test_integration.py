"""Integration tests for Focus Forge workflow."""

import json
import os
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock
import sys

import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

# Add parent directory to path so we can import focus_forge module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the CLI commands to test
from focus_forge import cli, load_tasks, save_tasks, CONFIG_FILE, TASKS_FILE, TASKS_DIR


@pytest.mark.integration
class TestFocusForgeWorkflow:
    """Test the complete Focus Forge workflow."""

    def test_complete_workflow(self, runner: <PERSON><PERSON><PERSON><PERSON><PERSON>, temp_dir: Path, mock_claude_response: str):
        """Test a complete workflow of creating and managing tasks."""
        # Create a sample PRD file
        prd_file = temp_dir / "sample_prd.txt"
        with open(prd_file, 'w') as f:
            f.write("Sample PRD content for a todo app.\n"
                    "The app should allow users to create, update, and delete tasks.\n"
                    "Tasks should have priorities and due dates.\n")

        # Create mock tasks to return from Claude API calls
        mock_tasks = [
            {
                "id": "1",
                "title": "Set up project structure",
                "description": "Initialize the project with necessary directories and files.",
                "status": "pending",
                "priority": "high",
                "dependencies": [],
                "details": "Create main directories: src, tests, docs.",
                "test_strategy": "Verify directory structure exists.",
                "subtasks": []
            },
            {
                "id": "2",
                "title": "Implement task creation",
                "description": "Allow users to create new tasks.",
                "status": "pending",
                "priority": "high",
                "dependencies": ["1"],
                "details": "Create form for task input, validate fields, save to database.",
                "test_strategy": "Unit test form validation and integration test task creation.",
                "subtasks": []
            }
        ]

        # =====================
        # Step 1: Initialize project
        # =====================
        with patch('click.prompt', side_effect=["Test Todo App", "0.1.0", "test-api-key"]):
            result_init = runner.invoke(cli, ["init"])
        
        assert result_init.exit_code == 0
        assert "Project initialized successfully" in result_init.output
        assert Path(CONFIG_FILE).exists()
        assert Path(TASKS_FILE).exists()
        assert Path(TASKS_DIR).exists()

        # =====================
        # Step 2: Parse PRD and generate tasks
        # =====================
        with patch('focus_forge.call_claude_api', return_value=json.dumps(mock_tasks)):
            result_parse = runner.invoke(cli, ["parse-prd", "--input", str(prd_file)])
        
        assert result_parse.exit_code == 0
        assert "Successfully generated" in result_parse.output

        # Verify tasks were created
        tasks = load_tasks()
        assert len(tasks) == 2
        assert tasks[0]["id"] == "1"
        assert tasks[1]["id"] == "2"

        # =====================
        # Step 3: List tasks
        # =====================
        result_list = runner.invoke(cli, ["list"])
        
        assert result_list.exit_code == 0
        assert "Set up project structure" in result_list.output
        assert "Implement task creation" in result_list.output

        # =====================
        # Step 4: Show next task
        # =====================
        result_next = runner.invoke(cli, ["next"])
        
        assert result_next.exit_code == 0
        assert "Task 1: Set up project structure" in result_next.output

        # =====================
        # Step 5: Set status of task 1 to done
        # =====================
        result_status = runner.invoke(cli, ["set-status", "--id=1", "--status=done"])
        
        assert result_status.exit_code == 0
        assert "Updated task 1 status" in result_status.output

        # =====================
        # Step 6: Get next task again, should be task 2 now
        # =====================
        result_next_2 = runner.invoke(cli, ["next"])
        
        assert result_next_2.exit_code == 0
        assert "Task 2: Implement task creation" in result_next_2.output

        # =====================
        # Step 7: Expand task 2 into subtasks
        # =====================
        mock_subtasks = [
            {
                "id": "2.1",
                "title": "Create task input form",
                "description": "Design UI for task input form.",
                "status": "pending"
            },
            {
                "id": "2.2",
                "title": "Implement form validation",
                "description": "Add validation for form inputs.",
                "status": "pending"
            },
            {
                "id": "2.3",
                "title": "Save task to database",
                "description": "Implement database operations for task creation.",
                "status": "pending"
            }
        ]

        with patch('focus_forge.call_claude_api', return_value=json.dumps(mock_subtasks)):
            result_expand = runner.invoke(cli, ["expand", "--id=2", "--num=3"])
        
        assert result_expand.exit_code == 0
        assert "Successfully expanded" in result_expand.output

        # =====================
        # Step 8: Show task 2 details with subtasks
        # =====================
        result_show = runner.invoke(cli, ["show", "2"])
        
        assert result_show.exit_code == 0
        assert "Task 2: Implement task creation" in result_show.output
        assert "Subtasks" in result_show.output
        assert "2.1: Create task input form" in result_show.output

        # =====================
        # Step 9: Update tasks based on new requirements
        # =====================
        mock_updated_tasks = [
            {
                "id": "2",
                "title": "Implement task creation with categories",
                "description": "Allow users to create new tasks with category assignment.",
                "dependencies": ["1"],
                "details": "Create form for task input with category selection, validate fields, save to database.",
                "test_strategy": "Unit test form validation and integration test task creation with categories."
            }
        ]

        with patch('focus_forge.call_claude_api', return_value=json.dumps(mock_updated_tasks)):
            result_update = runner.invoke(cli, ["update", "--from=2", "--prompt=Add category support to tasks"])
        
        assert result_update.exit_code == 0
        assert "Successfully updated" in result_update.output

        # Verify task was updated
        tasks = load_tasks()
        assert "categories" in tasks[1]["title"].lower()

        # =====================
        # Step 10: Validate dependencies
        # =====================
        result_validate = runner.invoke(cli, ["validate-dependencies"])
        
        assert result_validate.exit_code == 0
        assert "All dependencies are valid" in result_validate.output

    @pytest.mark.parametrize("command", [
        ["list"],
        ["next"],
        ["show", "1"],
        ["validate-dependencies"],
        ["fix-dependencies"],
        ["generate"]
    ])
    def test_empty_tasks_handling(self, runner: CliRunner, temp_dir: Path, command):
        """Test commands behave correctly when no tasks exist."""
        # Ensure tasks.json doesn't exist
        if Path(TASKS_FILE).exists():
            os.remove(TASKS_FILE)

        result = runner.invoke(cli, command)
        
        assert result.exit_code == 0
        assert "No tasks found" in result.output

    def test_error_recovery(self, runner: CliRunner, temp_dir: Path):
        """Test recovery from corrupted tasks file."""
        # Create a corrupted tasks.json
        with open(TASKS_FILE, 'w') as f:
            f.write("This is not valid JSON")

        # Test list command should handle the error gracefully
        result = runner.invoke(cli, ["list"])
        
        assert result.exit_code == 0
        assert "No tasks found" in result.output or "Error" in result.output

        # Test init should work even with corrupted file
        with patch('click.prompt', side_effect=["Test Project", "0.1.0", "test-api-key"]):
            with patch('click.confirm', return_value=True):
                result_init = runner.invoke(cli, ["init"])
        
        assert result_init.exit_code == 0
        assert "Project initialized successfully" in result_init.output

        # Verify file is now valid
        tasks = load_tasks()
        assert isinstance(tasks, list) 