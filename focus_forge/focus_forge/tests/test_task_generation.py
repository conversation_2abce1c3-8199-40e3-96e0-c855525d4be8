#!/usr/bin/env python3
"""
Tests for the Task Generation System.
"""
import unittest
from unittest.mock import patch, MagicMock
import json
from typing import Dict, List, Any, Optional

from focus_forge.task_generation import (
    TaskGenerator, TaskGenerationConfig, TaskType, 
    TaskTemplate, convert_prd_to_tasks
)
from focus_forge.dependency_graph import DependencyGraph, Dependency, DependencyType


class TestTaskGenerationConfig(unittest.TestCase):
    """Test the TaskGenerationConfig class."""
    
    def test_default_config(self):
        """Test that default config values are set correctly."""
        config = TaskGenerationConfig()
        self.assertEqual(config.default_status, "pending")
        self.assertEqual(config.default_priority, "medium")
        self.assertTrue(config.use_sequence_constraints)
        self.assertTrue(config.generate_subtasks)
        self.assertEqual(config.max_subtasks_per_task, 3)
        self.assertEqual(config.id_prefix, "T")
        self.assertTrue(config.detailed_descriptions)
        self.assertTrue(config.include_test_strategy)
        self.assertEqual(config.format_version, "1.0")
    
    def test_custom_config(self):
        """Test that custom config values can be set."""
        config = TaskGenerationConfig(
            default_status="in-progress",
            default_priority="high",
            use_sequence_constraints=False,
            generate_subtasks=False,
            max_subtasks_per_task=5,
            id_prefix="TASK-",
            detailed_descriptions=False,
            include_test_strategy=False,
            format_version="2.0"
        )
        self.assertEqual(config.default_status, "in-progress")
        self.assertEqual(config.default_priority, "high")
        self.assertFalse(config.use_sequence_constraints)
        self.assertFalse(config.generate_subtasks)
        self.assertEqual(config.max_subtasks_per_task, 5)
        self.assertEqual(config.id_prefix, "TASK-")
        self.assertFalse(config.detailed_descriptions)
        self.assertFalse(config.include_test_strategy)
        self.assertEqual(config.format_version, "2.0")


class TestTaskGenerator(unittest.TestCase):
    """Test the TaskGenerator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = TaskGenerationConfig(
            generate_subtasks=True,
            max_subtasks_per_task=2
        )
        self.generator = TaskGenerator(self.config)
        
        # Sample component data for testing
        self.components = [
            {
                "id": "C1",
                "name": "Authentication Service",
                "type": "foundation",
                "description": "User authentication and authorization system",
                "dependencies": []
            },
            {
                "id": "C2",
                "name": "User Profile API",
                "type": "api",
                "description": "API for user profile management",
                "dependencies": ["C1"]
            },
            {
                "id": "C3",
                "name": "User Interface",
                "type": "ui",
                "description": "User interface for the application",
                "dependencies": ["C2"]
            }
        ]
    
    def test_generator_initialization(self):
        """Test that the generator initializes correctly."""
        self.assertIsNotNone(self.generator)
        self.assertEqual(self.generator.task_id_counter, 1)
        self.assertIsNotNone(self.generator.templates)
        self.assertGreaterEqual(len(self.generator.templates), 3)  # At least the 3 default templates
    
    def test_task_id_generation(self):
        """Test that task IDs are generated correctly."""
        task_id1 = self.generator._generate_task_id()
        self.assertEqual(task_id1, "T1")
        
        task_id2 = self.generator._generate_task_id()
        self.assertEqual(task_id2, "T2")
        
        # Test with custom prefix
        self.generator.config.id_prefix = "TASK-"
        task_id3 = self.generator._generate_task_id()
        self.assertEqual(task_id3, "TASK-3")
    
    def test_task_type_determination(self):
        """Test that task types are determined correctly."""
        foundation_type = self.generator._determine_task_type({"type": "foundation"})
        self.assertEqual(foundation_type, TaskType.FOUNDATION)
        
        ui_type = self.generator._determine_task_type({"type": "ui"})
        self.assertEqual(ui_type, TaskType.UI)
        
        api_type = self.generator._determine_task_type({"type": "api"})
        self.assertEqual(api_type, TaskType.API)
        
        # Test fallback to FEATURE
        unknown_type = self.generator._determine_task_type({"type": "unknown"})
        self.assertEqual(unknown_type, TaskType.FEATURE)
    
    @patch('focus_forge.task_generation.PriorityInferenceEngine.infer_priority')
    def test_priority_determination(self, mock_infer_priority):
        """Test that priorities are determined correctly."""
        # Mock the priority inference
        mock_infer_priority.return_value = ("high", {})
        
        # Test with description
        component = {"description": "This is a high priority task"}
        priority = self.generator._determine_priority(component)
        self.assertEqual(priority, "high")
        mock_infer_priority.assert_called_once_with("This is a high priority task")
        
        # Test fallback to default
        component = {}  # No description
        priority = self.generator._determine_priority(component)
        self.assertEqual(priority, "medium")  # Default priority
    
    def test_template_filling(self):
        """Test that templates are filled correctly."""
        template = "Create {name} for {purpose}"
        data = {
            "name": "Authentication Service",
            "purpose": "user authentication"
        }
        
        filled = self.generator._fill_template(template, data)
        self.assertEqual(filled, "Create Authentication Service for user authentication")
        
        # Test with missing values
        template = "Create {name} for {missing}"
        filled = self.generator._fill_template(template, data)
        self.assertEqual(filled, "Create Authentication Service for {missing}")
    
    def test_subtask_generation(self):
        """Test that subtasks are generated correctly."""
        task = {
            "id": "T1",
            "title": "Implement Authentication Service",
            "description": "Create user authentication system",
            "task_type": "foundation"
        }
        
        subtasks = self.generator._generate_subtasks(task, [])
        
        # Check that we have the right number of subtasks
        self.assertEqual(len(subtasks), 2)  # As configured in setUp
        
        # Check subtask structure
        self.assertEqual(subtasks[0]["id"], "T1.1")
        self.assertEqual(subtasks[1]["id"], "T1.2")
        
        # Check that subtasks have titles and descriptions
        self.assertTrue("title" in subtasks[0])
        self.assertTrue("description" in subtasks[0])
        self.assertTrue("status" in subtasks[0])
        
        # Check that status is set to default
        self.assertEqual(subtasks[0]["status"], "pending")
    
    def test_generate_tasks_from_components(self):
        """Test generating tasks from components."""
        tasks = self.generator.generate_tasks_from_components(self.components)
        
        # Check that we have the right number of tasks
        self.assertEqual(len(tasks), 3)
        
        # Check task structure
        self.assertTrue(all("id" in task for task in tasks))
        self.assertTrue(all("title" in task for task in tasks))
        self.assertTrue(all("description" in task for task in tasks))
        self.assertTrue(all("status" in task for task in tasks))
        self.assertTrue(all("priority" in task for task in tasks))
        self.assertTrue(all("dependencies" in task for task in tasks))
        self.assertTrue(all("details" in task for task in tasks))
        self.assertTrue(all("test_strategy" in task for task in tasks))
        self.assertTrue(all("subtasks" in task for task in tasks))
        
        # Check that dependencies are correctly established
        # T1 should have no dependencies
        self.assertEqual(len(tasks[0]["dependencies"]), 0)
        
        # T2 should depend on T1
        self.assertEqual(len(tasks[1]["dependencies"]), 1)
        self.assertIn(tasks[0]["id"], tasks[1]["dependencies"])
        
        # T3 should depend on T2
        self.assertEqual(len(tasks[2]["dependencies"]), 1)
        self.assertIn(tasks[1]["id"], tasks[2]["dependencies"])
        
        # Check that subtasks are generated
        for task in tasks:
            self.assertEqual(len(task["subtasks"]), 2)  # As configured in setUp
    
    @patch('focus_forge.task_generation.PriorityInferenceEngine.analyze_prd_text')
    def test_generate_tasks_from_prd(self, mock_analyze_prd):
        """Test generating tasks from PRD text."""
        # Mock the PRD analysis result
        mock_analyze_prd.return_value = {
            "features": [
                {
                    "title": "Authentication",
                    "text": "User authentication system",
                    "relationships": []
                },
                {
                    "title": "User Profiles",
                    "text": "User profile management",
                    "relationships": [
                        {"type": "depends_on", "target": "Authentication"}
                    ]
                }
            ]
        }
        
        tasks = self.generator.generate_tasks_from_prd("Sample PRD text")
        
        # Check that tasks were generated
        self.assertGreaterEqual(len(tasks), 2)
        
        # Verify the mock was called
        mock_analyze_prd.assert_called_once_with("Sample PRD text")


class TestConvenienceFunctions(unittest.TestCase):
    """Test the convenience functions."""
    
    @patch('focus_forge.task_generation.TaskGenerator.generate_tasks_from_prd')
    def test_convert_prd_to_tasks(self, mock_generate):
        """Test the convert_prd_to_tasks function."""
        # Mock the generator output
        mock_generate.return_value = [{"id": "T1", "title": "Test Task"}]
        
        # Call the convenience function
        tasks = convert_prd_to_tasks("Sample PRD")
        
        # Verify results
        self.assertEqual(len(tasks), 1)
        self.assertEqual(tasks[0]["id"], "T1")
        
        # Verify the mock was called
        mock_generate.assert_called_once_with("Sample PRD")


if __name__ == '__main__':
    unittest.main() 