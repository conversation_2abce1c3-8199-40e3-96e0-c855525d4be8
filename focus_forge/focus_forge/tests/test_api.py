"""Tests for API integration in Focus Forge."""

import json
import os
from pathlib import Path
from unittest.mock import patch, MagicMock
import sys

import pytest
import requests

# Add parent directory to path so we can import focus_forge module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import API functions to test
from focus_forge import (
    call_claude_api, generate_tasks_from_prd, expand_task
)


@pytest.fixture
def mock_response():
    """Create a mock response for requests."""
    mock = MagicMock()
    mock.status_code = 200
    mock.json.return_value = {
        "content": [
            {"text": json.dumps([{"id": "1", "title": "Test task"}])}
        ]
    }
    return mock


def test_call_claude_api_success(mock_response):
    """Test successful API call to Claude."""
    with patch('requests.post', return_value=mock_response) as mock_post:
        response = call_claude_api(
            prompt="Test prompt",
            api_key="test-api-key",
            model="test-model",
            max_tokens=1000,
            temperature=0.5
        )
    
    # Verify the request was made with correct parameters
    mock_post.assert_called_once()
    args, kwargs = mock_post.call_args
    
    assert args[0] == "https://api.anthropic.com/v1/messages"
    assert kwargs["headers"]["x-api-key"] == "test-api-key"
    assert kwargs["json"]["model"] == "test-model"
    assert kwargs["json"]["max_tokens"] == 1000
    assert kwargs["json"]["temperature"] == 0.5
    assert kwargs["json"]["messages"][0]["content"] == "Test prompt"
    
    # Verify the response is processed correctly
    assert response == json.dumps([{"id": "1", "title": "Test task"}])


def test_call_claude_api_error():
    """Test error handling in Claude API call."""
    error_response = MagicMock()
    error_response.status_code = 400
    error_response.text = "Bad request"
    
    with patch('requests.post', return_value=error_response) as mock_post:
        with patch('rich.console.Console.print') as mock_print:
            response = call_claude_api(
                prompt="Test prompt",
                api_key="test-api-key",
                model="test-model",
                max_tokens=1000,
                temperature=0.5
            )
    
    assert response == ""
    mock_print.assert_called_once()
    assert "API Error" in mock_print.call_args[0][0]


def test_call_claude_api_exception():
    """Test exception handling in Claude API call."""
    with patch('requests.post', side_effect=Exception("Test exception")) as mock_post:
        with patch('rich.console.Console.print') as mock_print:
            response = call_claude_api(
                prompt="Test prompt",
                api_key="test-api-key",
                model="test-model",
                max_tokens=1000,
                temperature=0.5
            )
    
    assert response == ""
    mock_print.assert_called_once()
    assert "Error calling Claude API" in mock_print.call_args[0][0]


def test_generate_tasks_from_prd(sample_config):
    """Test generating tasks from PRD."""
    mock_tasks = [
        {
            "id": "1",
            "title": "Test task 1",
            "description": "Description 1",
            "status": "pending",
            "priority": "high",
            "dependencies": [],
            "details": "Details 1",
            "test_strategy": "Test strategy 1",
            "subtasks": []
        },
        {
            "id": "2",
            "title": "Test task 2",
            "description": "Description 2",
            "dependencies": ["1"],
            "details": "Details 2"
        }
    ]
    
    with patch('focus_forge.call_claude_api', return_value=json.dumps(mock_tasks)) as mock_api:
        tasks = generate_tasks_from_prd("Test PRD", sample_config, max_tasks=2)
    
    assert len(tasks) == 2
    assert tasks[0]["id"] == "1"
    assert tasks[0]["title"] == "Test task 1"
    assert tasks[0]["status"] == "pending"
    
    # Check that missing fields are filled with defaults
    assert tasks[1]["status"] == "pending"
    assert tasks[1]["priority"] == "medium"
    assert tasks[1]["test_strategy"] is None
    assert tasks[1]["subtasks"] == []


def test_generate_tasks_from_prd_json_error(sample_config):
    """Test error handling in generate_tasks_from_prd with invalid JSON."""
    with patch('focus_forge.call_claude_api', return_value="Invalid JSON") as mock_api:
        with patch('rich.console.Console.print') as mock_print:
            tasks = generate_tasks_from_prd("Test PRD", sample_config, max_tasks=2)
    
    assert tasks == []
    mock_print.assert_called()
    assert any("Error decoding JSON" in str(call[0][0]) for call in mock_print.call_args_list)


def test_expand_task(sample_tasks, sample_config):
    """Test expanding a task into subtasks."""
    task = sample_tasks[0]  # Use the first task from sample tasks
    mock_subtasks = [
        {
            "id": "1.1",
            "title": "Subtask 1",
            "description": "Subtask description 1",
            "status": "pending"
        },
        {
            "id": "1.2",
            "title": "Subtask 2",
            "description": "Subtask description 2",
            "status": "pending"
        }
    ]
    
    with patch('focus_forge.call_claude_api', return_value=json.dumps(mock_subtasks)) as mock_api:
        subtasks = expand_task(task, sample_config, num_subtasks=2)
    
    assert len(subtasks) == 2
    assert subtasks[0]["id"] == "1.1"
    assert subtasks[0]["title"] == "Subtask 1"
    assert subtasks[0]["status"] == "pending"


def test_expand_task_malformed_ids(sample_tasks, sample_config):
    """Test expanding a task with malformed subtask IDs."""
    task = sample_tasks[0]  # Use the first task from sample tasks
    mock_subtasks = [
        {
            # Missing ID
            "title": "Subtask 1",
            "description": "Subtask description 1"
        },
        {
            "id": "wrong_format",  # Wrong ID format
            "title": "Subtask 2",
            "description": "Subtask description 2"
        }
    ]
    
    with patch('focus_forge.call_claude_api', return_value=json.dumps(mock_subtasks)) as mock_api:
        subtasks = expand_task(task, sample_config, num_subtasks=2)
    
    assert len(subtasks) == 2
    assert subtasks[0]["id"] == "1.1"  # ID was fixed
    assert subtasks[1]["id"] == "1.2"  # ID was fixed
    
    # Both subtasks should have pending status
    assert subtasks[0]["status"] == "pending"
    assert subtasks[1]["status"] == "pending" 