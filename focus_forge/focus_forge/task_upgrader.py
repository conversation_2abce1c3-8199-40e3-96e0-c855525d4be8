"""
Task Format Upgrader System for Focus Forge.

This module provides functionality to upgrade task data between different format versions,
ensuring backward compatibility as Focus Forge evolves.
"""

import json
import os
import logging
import copy
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable, Tuple, Union, cast
import shutil
import uuid

from .constants import VERSION, TASKS_FILE

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Type definitions for version handling
UpgraderFunc = Callable[[Dict[str, Any]], Dict[str, Any]]
ValidationFunc = Callable[[Dict[str, Any]], Tuple[bool, List[str]]]

# Schema versioning registry
SCHEMA_VERSIONS = []
UPGRADERS: Dict[str, UpgraderFunc] = {}
VALIDATORS: Dict[str, ValidationFunc] = {}

# Constants
HISTORY_FILE = ".upgrade_history.json"


def register_schema_version(version: str) -> None:
    """
    Register a new schema version in ascending order.
    
    Args:
        version: The schema version to register
    """
    if version not in SCHEMA_VERSIONS:
        SCHEMA_VERSIONS.append(version)
        SCHEMA_VERSIONS.sort(key=lambda v: [int(x) for x in v.split('.')])
        logger.debug(f"Registered schema version: {version}")


def register_upgrader(from_version: str, to_version: str, upgrader_func: UpgraderFunc) -> None:
    """
    Register an upgrader function that converts from one version to another.
    
    Args:
        from_version: The source schema version
        to_version: The target schema version
        upgrader_func: The function that performs the upgrade
    """
    key = f"{from_version}->{to_version}"
    UPGRADERS[key] = upgrader_func
    logger.debug(f"Registered upgrader: {key}")
    
    # Ensure both versions are in the registry
    register_schema_version(from_version)
    register_schema_version(to_version)


def register_validator(version: str, validator_func: ValidationFunc) -> None:
    """
    Register a validator function for a specific schema version.
    
    Args:
        version: The schema version to validate
        validator_func: The function that performs validation
    """
    VALIDATORS[version] = validator_func
    logger.debug(f"Registered validator for version {version}")
    
    # Ensure version is in the registry
    register_schema_version(version)


class TaskUpgrader:
    """
    Task upgrader system that handles migration between task format versions.
    """
    
    @classmethod
    def detect_version(cls, data: Dict[str, Any]) -> str:
        """
        Detect the version of a task data structure.
        
        Args:
            data: The task data to check
            
        Returns:
            str: The detected version string
            
        Raises:
            ValueError: If version cannot be detected
        """
        # First check if version is explicitly specified
        if "version" in data and isinstance(data["version"], str):
            return data["version"]
        
        # TODO: Add heuristic detection for older versions that might not have version field
        # This will be implemented as new versions are developed
        
        raise ValueError("Unable to detect task data version")
    
    @classmethod
    def validate(cls, data: Dict[str, Any], version: Optional[str] = None) -> Tuple[bool, List[str]]:
        """
        Validate the structure of task data for a specific version.
        
        Args:
            data: The task data to validate
            version: The version to validate against, or None to detect
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, list_of_error_messages)
        """
        if version is None:
            try:
                version = cls.detect_version(data)
            except ValueError as e:
                return False, [str(e)]
        
        if version not in VALIDATORS:
            return False, [f"No validator registered for version {version}"]
        
        return VALIDATORS[version](data)
    
    @classmethod
    def get_upgrade_path(cls, from_version: str, to_version: str) -> List[Tuple[str, str]]:
        """
        Determine the sequence of upgrades needed to go from one version to another.
        
        Args:
            from_version: The starting version
            to_version: The target version
            
        Returns:
            List[Tuple[str, str]]: List of (from_ver, to_ver) pairs representing the upgrade path
            
        Raises:
            ValueError: If no valid upgrade path can be found
        """
        if from_version == to_version:
            return []
        
        # Ensure both versions are registered
        if from_version not in SCHEMA_VERSIONS:
            raise ValueError(f"Unknown source version: {from_version}")
        if to_version not in SCHEMA_VERSIONS:
            raise ValueError(f"Unknown target version: {to_version}")
        
        # Get indices in the version list
        from_idx = SCHEMA_VERSIONS.index(from_version)
        to_idx = SCHEMA_VERSIONS.index(to_version)
        
        # Ensure we're upgrading to a newer version
        if from_idx >= to_idx:
            raise ValueError(f"Cannot downgrade from {from_version} to {to_version}")
        
        # Build the upgrade path
        upgrade_path = []
        for i in range(from_idx, to_idx):
            source = SCHEMA_VERSIONS[i]
            target = SCHEMA_VERSIONS[i + 1]
            upgrade_path.append((source, target))
        
        return upgrade_path
    
    @classmethod
    def upgrade(cls, data: Dict[str, Any], target_version: Optional[str] = None) -> Dict[str, Any]:
        """
        Upgrade task data to the specified version, or to the latest version if none specified.
        
        Args:
            data: The task data to upgrade
            target_version: The target version, or None for the latest
            
        Returns:
            Dict[str, Any]: The upgraded task data
            
        Raises:
            ValueError: If upgrade cannot be performed
        """
        # Make a deep copy to prevent modifying the original
        working_data = copy.deepcopy(data)
        
        try:
            current_version = cls.detect_version(working_data)
        except ValueError as e:
            raise ValueError(f"Cannot upgrade data with unknown version: {e}")
        
        # If no target version specified, use the latest
        if target_version is None:
            target_version = SCHEMA_VERSIONS[-1]
        
        # Check if upgrade is needed
        if current_version == target_version:
            logger.info(f"No upgrade needed, already at version {target_version}")
            return working_data
        
        # Get the upgrade path
        upgrade_path = cls.get_upgrade_path(current_version, target_version)
        
        # Apply each upgrader in sequence
        for from_ver, to_ver in upgrade_path:
            upgrader_key = f"{from_ver}->{to_ver}"
            
            if upgrader_key not in UPGRADERS:
                raise ValueError(f"No upgrader registered for {upgrader_key}")
            
            # Apply the upgrader
            logger.info(f"Upgrading from {from_ver} to {to_ver}")
            working_data = UPGRADERS[upgrader_key](working_data)
            
            # Validate after upgrade
            is_valid, errors = cls.validate(working_data, to_ver)
            if not is_valid:
                raise ValueError(f"Validation failed after upgrading to {to_ver}: {errors}")
            
            # Ensure version is updated in the data
            working_data["version"] = to_ver
        
        return working_data
    
    @classmethod
    def backup_tasks_file(cls) -> Path:
        """
        Create a backup of the tasks file before upgrading.
        
        Returns:
            Path: Path to the backup file
        """
        tasks_path = Path(TASKS_FILE)
        if not tasks_path.exists():
            raise FileNotFoundError(f"Tasks file not found: {TASKS_FILE}")
        
        # Create backup with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = tasks_path.with_suffix(f".{timestamp}.backup")
        
        # Copy the file
        shutil.copy2(tasks_path, backup_path)
        logger.info(f"Created backup at {backup_path}")
        
        return backup_path
    
    @classmethod
    def record_upgrade(cls, from_version: str, to_version: str, backup_path: Optional[Path] = None) -> str:
        """
        Record an upgrade in the history file for tracking purposes.
        
        Args:
            from_version: The source version
            to_version: The target version
            backup_path: Path to the backup file, if any
            
        Returns:
            str: The ID of the recorded upgrade
        """
        history_path = Path(HISTORY_FILE)
        
        # Create history data structure
        history = {}
        if history_path.exists():
            try:
                with open(history_path, 'r') as f:
                    history = json.load(f)
            except json.JSONDecodeError:
                # If file exists but is invalid, start fresh
                history = {"upgrades": []}
        
        if "upgrades" not in history:
            history["upgrades"] = []
        
        # Generate a unique ID for this upgrade
        upgrade_id = str(uuid.uuid4())[:8]
        
        # Create upgrade record
        upgrade_record = {
            "id": upgrade_id,
            "timestamp": datetime.now().isoformat(),
            "from_version": from_version,
            "to_version": to_version,
            "backup_path": str(backup_path) if backup_path else None
        }
        
        # Add to history
        history["upgrades"].append(upgrade_record)
        
        # Save history
        with open(history_path, 'w') as f:
            json.dump(history, f, indent=2)
        
        logger.info(f"Recorded upgrade {upgrade_id}: {from_version} -> {to_version}")
        return upgrade_id
    
    @classmethod
    def get_upgrade_history(cls) -> List[Dict[str, Any]]:
        """
        Get the upgrade history.
        
        Returns:
            List[Dict[str, Any]]: List of upgrade records
        """
        history_path = Path(HISTORY_FILE)
        if not history_path.exists():
            return []
        
        try:
            with open(history_path, 'r') as f:
                history = json.load(f)
                return history.get("upgrades", [])
        except json.JSONDecodeError:
            return []
    
    @classmethod
    def upgrade_tasks_file(cls, target_version: Optional[str] = None, backup: bool = True) -> Dict[str, Any]:
        """
        Upgrade the tasks file to the specified version.
        
        Args:
            target_version: The target version, or None for the latest
            backup: Whether to create a backup before upgrading
            
        Returns:
            Dict[str, Any]: The upgraded task data
            
        Raises:
            ValueError: If upgrade cannot be performed
        """
        tasks_path = Path(TASKS_FILE)
        if not tasks_path.exists():
            raise FileNotFoundError(f"Tasks file not found: {TASKS_FILE}")
        
        # Load the current data
        with open(tasks_path, 'r') as f:
            data = json.load(f)
        
        # Detect the current version
        current_version = cls.detect_version(data)
        
        # Create backup if requested
        backup_path = None
        if backup:
            backup_path = cls.backup_tasks_file()
        
        # Perform the upgrade
        upgraded_data = cls.upgrade(data, target_version)
        
        # Save the upgraded data
        with open(tasks_path, 'w') as f:
            json.dump(upgraded_data, f, indent=2)
        
        logger.info(f"Successfully upgraded tasks file to version {upgraded_data['version']}")
        
        # Record the upgrade
        cls.record_upgrade(current_version, upgraded_data['version'], backup_path)
        
        return upgraded_data
    
    @classmethod
    def auto_upgrade_tasks_file_silent(cls, target_version: Optional[str] = None, backup: bool = True) -> Tuple[bool, Dict[str, Any], str]:
        """
        Automatically upgrade the tasks file without prompting the user.
        Useful for non-interactive environments like CI/CD pipelines.
        
        Args:
            target_version: The target version, or None for the latest
            backup: Whether to create a backup before upgrading
            
        Returns:
            Tuple[bool, Dict[str, Any], str]: (success, data, message)
            
        The returned tuple contains:
            - success: Whether the upgrade was successful or not needed
            - data: The upgraded task data or original data if no upgrade needed
            - message: A descriptive message about what happened
        """
        tasks_path = Path(TASKS_FILE)
        if not tasks_path.exists():
            return False, {}, f"Tasks file not found: {TASKS_FILE}"
        
        try:
            # Load the current data
            with open(tasks_path, 'r') as f:
                data = json.load(f)
            
            # Detect the version
            current_version = cls.detect_version(data)
            
            # If no target version specified, use the latest
            if target_version is None:
                target_version = SCHEMA_VERSIONS[-1]
            
            # Check if upgrade is needed
            if not cls.needs_upgrade(current_version, target_version):
                return True, data, f"No upgrade needed, already at or newer than version {target_version}"
            
            # Create backup if requested
            backup_path = None
            if backup:
                backup_path = cls.backup_tasks_file()
            
            # Perform the upgrade
            upgraded_data = cls.upgrade(data, target_version)
            
            # Save the upgraded data
            with open(tasks_path, 'w') as f:
                json.dump(upgraded_data, f, indent=2)
            
            # Record the upgrade
            cls.record_upgrade(current_version, upgraded_data['version'], backup_path)
            
            return True, upgraded_data, f"Successfully upgraded from version {current_version} to {upgraded_data['version']}"
            
        except Exception as e:
            return False, data if 'data' in locals() else {}, f"Error during auto-upgrade: {str(e)}"

    @classmethod
    def check_version_and_prompt_upgrade(cls, data: Dict[str, Any], current_version: str) -> Dict[str, Any]:
        """
        Check if the task data version matches the current application version,
        and return upgraded data if needed.
        
        Args:
            data: The task data to check
            current_version: The current application version
            
        Returns:
            Dict[str, Any]: The original or upgraded task data
        """
        try:
            detected_version = cls.detect_version(data)
            
            # No upgrade needed if versions match
            if detected_version == current_version:
                return data
            
            # If versions don't match, check if upgrade is needed
            if not cls.needs_upgrade(detected_version, current_version):
                # No upgrade needed but versions differ (could be a downgrade situation)
                return data
            
            # Upgrade needed
            try:
                # Perform the upgrade
                return cls.upgrade(data, current_version)
            except ValueError as e:
                # Log error but return original data
                logger.error(f"Failed to auto-upgrade from {detected_version} to {current_version}: {e}")
                return data
                
        except ValueError as e:
            # If version detection fails, just return the original data
            logger.warning(f"Failed to detect version for auto-upgrade check: {e}")
            return data
    
    @classmethod
    def needs_upgrade(cls, from_version: str, to_version: str) -> bool:
        """
        Check if an upgrade is needed between two versions.
        
        Args:
            from_version: The source version
            to_version: The target version
            
        Returns:
            bool: True if an upgrade is needed
        """
        try:
            # If versions are the same, no upgrade needed
            if from_version == to_version:
                return False
            
            # Check if both versions are registered
            if from_version not in SCHEMA_VERSIONS or to_version not in SCHEMA_VERSIONS:
                return False
            
            # Get indices in the version list
            from_idx = SCHEMA_VERSIONS.index(from_version)
            to_idx = SCHEMA_VERSIONS.index(to_version)
            
            # Only upgrade to newer versions
            return from_idx < to_idx
        except Exception:
            # If any error occurs, assume no upgrade is needed
            return False

    @classmethod
    def get_available_backups(cls) -> List[Dict[str, Any]]:
        """
        Get a list of available backup files.
        
        Returns:
            List[Dict[str, Any]]: List of backup information
        """
        tasks_path = Path(TASKS_FILE)
        backups = []
        
        # Build pattern for backup files with extension like .20250325_115846.backup
        pattern = f"{tasks_path.stem}.*{tasks_path.suffix}.*.backup"
        
        for backup_file in tasks_path.parent.glob(pattern):
            try:
                # Parse timestamp from filename
                timestamp_str = backup_file.suffix.split('.')[1]
                timestamp = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                
                # Try to detect version in the backup file
                version = "unknown"
                try:
                    with open(backup_file, 'r') as f:
                        data = json.load(f)
                        version = cls.detect_version(data)
                except (json.JSONDecodeError, ValueError, IOError):
                    pass
                
                backups.append({
                    "path": str(backup_file),
                    "filename": backup_file.name,
                    "timestamp": timestamp.isoformat(),
                    "timestamp_str": timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                    "version": version
                })
            except (ValueError, IndexError):
                # Skip files that don't match the expected format
                continue
        
        # Sort by timestamp, newest first
        return sorted(backups, key=lambda b: b.get("timestamp", ""), reverse=True)
    
    @classmethod
    def restore_from_backup(cls, backup_path: str) -> Dict[str, Any]:
        """
        Restore tasks file from a backup.
        
        Args:
            backup_path: Path to the backup file
            
        Returns:
            Dict[str, Any]: The restored task data
            
        Raises:
            FileNotFoundError: If backup file doesn't exist
            ValueError: If backup file is invalid
        """
        backup_file = Path(backup_path)
        if not backup_file.exists():
            raise FileNotFoundError(f"Backup file not found: {backup_path}")
        
        tasks_path = Path(TASKS_FILE)
        
        # Create a backup of the current file before restoring
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pre_restore_backup = tasks_path.with_suffix(f".{timestamp}.pre_restore.backup")
        
        if tasks_path.exists():
            shutil.copy2(tasks_path, pre_restore_backup)
            logger.info(f"Created pre-restore backup at {pre_restore_backup}")
        
        # Load backup data to validate it
        with open(backup_file, 'r') as f:
            try:
                data = json.load(f)
                
                # Validate backup data
                if "tasks" not in data:
                    raise ValueError("Backup file does not contain tasks data")
                
                # Copy backup file to tasks file
                shutil.copy2(backup_file, tasks_path)
                logger.info(f"Restored from backup {backup_file}")
                
                # Record the restore operation
                current_version = cls.detect_version(data)
                cls.record_upgrade("restore", current_version, pre_restore_backup)
                
                return data
                
            except json.JSONDecodeError:
                raise ValueError("Backup file is not valid JSON")
            except Exception as e:
                raise ValueError(f"Error restoring from backup: {str(e)}")
    
    @classmethod
    def find_backup_by_id(cls, backup_id: str) -> Optional[str]:
        """
        Find a backup file by its ID in the upgrade history.
        
        Args:
            backup_id: The ID to search for
            
        Returns:
            Optional[str]: Path to the backup file, or None if not found
        """
        history = cls.get_upgrade_history()
        
        for upgrade in history:
            if upgrade.get("id") == backup_id and upgrade.get("backup_path"):
                backup_path = upgrade.get("backup_path")
                if Path(backup_path).exists():
                    return backup_path
        
        return None


# Define validator for version 0.7.0 (current format)
def validate_v0_7_0(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate the structure of task data for version 0.7.0.
    
    Args:
        data: The task data to validate
        
    Returns:
        Tuple[bool, List[str]]: (is_valid, list_of_error_messages)
    """
    errors = []
    
    # Check required top-level fields
    required_fields = ["version", "updated_at", "tasks"]
    for field in required_fields:
        if field not in data:
            errors.append(f"Missing required field: {field}")
    
    # Early return if basic structure is invalid
    if errors:
        return False, errors
    
    # Validate tasks array
    tasks = data.get("tasks", [])
    if not isinstance(tasks, list):
        errors.append("'tasks' must be an array")
        return False, errors
    
    # Validate each task
    for i, task in enumerate(tasks):
        task_errors = validate_task_v0_7_0(task, i)
        errors.extend(task_errors)
    
    return len(errors) == 0, errors


def validate_task_v0_7_0(task: Dict[str, Any], index: int) -> List[str]:
    """
    Validate a single task for version 0.7.0.
    
    Args:
        task: The task to validate
        index: The index of the task in the tasks array
        
    Returns:
        List[str]: List of error messages
    """
    errors = []
    task_prefix = f"Task at index {index}"
    
    # Check required task fields
    required_fields = ["id", "title", "description", "status", "priority", "dependencies", "details"]
    for field in required_fields:
        if field not in task:
            errors.append(f"{task_prefix} missing required field: {field}")
    
    # Early return if basic structure is invalid
    if errors:
        return errors
    
    # Validate id format
    if not isinstance(task["id"], str) or not task["id"].startswith("T"):
        errors.append(f"{task_prefix} has invalid 'id' format, should start with 'T'")
    
    # Validate status
    valid_statuses = ["pending", "in-progress", "done", "deferred"]
    if task["status"] not in valid_statuses:
        errors.append(f"{task_prefix} has invalid 'status': {task['status']}, should be one of {valid_statuses}")
    
    # Validate priority
    valid_priorities = ["high", "medium", "low"]
    if task["priority"] not in valid_priorities:
        errors.append(f"{task_prefix} has invalid 'priority': {task['priority']}, should be one of {valid_priorities}")
    
    # Validate dependencies
    if not isinstance(task["dependencies"], list):
        errors.append(f"{task_prefix} has invalid 'dependencies', should be a list")
    
    # Validate subtasks if present
    if "subtasks" in task:
        if not isinstance(task["subtasks"], list):
            errors.append(f"{task_prefix} has invalid 'subtasks', should be a list")
        else:
            for j, subtask in enumerate(task["subtasks"]):
                subtask_errors = validate_subtask_v0_7_0(subtask, index, j)
                errors.extend(subtask_errors)
    
    return errors


def validate_subtask_v0_7_0(subtask: Dict[str, Any], task_index: int, subtask_index: int) -> List[str]:
    """
    Validate a single subtask for version 0.7.0.
    
    Args:
        subtask: The subtask to validate
        task_index: The index of the parent task
        subtask_index: The index of the subtask
        
    Returns:
        List[str]: List of error messages
    """
    errors = []
    subtask_prefix = f"Subtask at index {subtask_index} of task {task_index}"
    
    # Check required subtask fields
    required_fields = ["id", "title", "description", "status"]
    for field in required_fields:
        if field not in subtask:
            errors.append(f"{subtask_prefix} missing required field: {field}")
    
    # Early return if basic structure is invalid
    if errors:
        return errors
    
    # Validate id format
    if not isinstance(subtask["id"], str) or "." not in subtask["id"]:
        errors.append(f"{subtask_prefix} has invalid 'id' format, should be in format 'T1.1'")
    
    # Validate status
    valid_statuses = ["pending", "in-progress", "done", "deferred"]
    if subtask["status"] not in valid_statuses:
        errors.append(f"{subtask_prefix} has invalid 'status': {subtask['status']}, should be one of {valid_statuses}")
    
    return errors


# Register version 0.7.0 validator
register_validator("0.7.0", validate_v0_7_0)

# Define validator for version 0.8.0 (future version)
def validate_v0_8_0(data: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate the structure of task data for version 0.8.0.
    
    Args:
        data: The task data to validate
        
    Returns:
        Tuple[bool, List[str]]: (is_valid, list_of_error_messages)
    """
    # For now, 0.8.0 has the same structure as 0.7.0
    # Any future specific validation rules for 0.8.0 would be added here
    errors = []
    
    # Check required top-level fields
    required_fields = ["version", "updated_at", "tasks"]
    for field in required_fields:
        if field not in data:
            errors.append(f"Missing required field: {field}")
    
    # Early return if basic structure is invalid
    if errors:
        return False, errors
    
    # Validate tasks array
    tasks = data.get("tasks", [])
    if not isinstance(tasks, list):
        errors.append("'tasks' must be an array")
        return False, errors
    
    # Validate each task
    for i, task in enumerate(tasks):
        task_errors = validate_task_v0_7_0(task, i)  # Reuse the 0.7.0 validation for now
        errors.extend(task_errors)
    
    return len(errors) == 0, errors

# Register version 0.8.0 validator
register_validator("0.8.0", validate_v0_8_0)

# Define upgrader from 0.7.0 to 0.8.0 (future version with nested subtasks)
def upgrade_v0_7_0_to_v0_8_0(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Upgrade task data from version 0.7.0 to 0.8.0.
    
    For demonstration purposes, this upgrader doesn't change the data structure
    but illustrates where future schema changes would be implemented.
    
    Args:
        data: The task data to upgrade
        
    Returns:
        Dict[str, Any]: The upgraded task data
    """
    # Create a deep copy to prevent modifying the original
    upgraded_data = copy.deepcopy(data)
    
    # Update version
    upgraded_data["version"] = "0.8.0"
    upgraded_data["updated_at"] = datetime.now().isoformat()
    
    # If we were making actual schema changes, they would go here
    # Example: adding a new field to each task
    # for task in upgraded_data["tasks"]:
    #     task["new_field"] = default_value
    
    return upgraded_data


# Register upgrader
register_upgrader("0.7.0", "0.8.0", upgrade_v0_7_0_to_v0_8_0)

# Function that can be used as a decorator for functions that load tasks
def auto_upgrade_tasks(load_func):
    """
    Decorator that automatically upgrades tasks when they're loaded.
    
    Args:
        load_func: The function that loads tasks
        
    Returns:
        Function that loads and auto-upgrades tasks
    """
    def wrapper(*args, **kwargs):
        # Call the original load function
        data = load_func(*args, **kwargs)
        
        # Skip auto-upgrade if data is empty
        if not data:
            return data
        
        # Auto-upgrade if needed
        try:
            from .constants import VERSION
            return TaskUpgrader.check_version_and_prompt_upgrade(data, VERSION)
        except Exception as e:
            # If any error occurs during auto-upgrade, log and return original data
            logger.error(f"Error during auto-upgrade: {e}")
            return data
    
    return wrapper 