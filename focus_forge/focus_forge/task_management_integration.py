#!/usr/bin/env python3
"""
Integration module for connecting PRD parsing with the existing task management system.

This module provides integration points and adapters to connect the PRD parser,
task generation, and validation systems with the existing task management infrastructure.
"""
import os
import sys
import json
import logging
import tempfile
from typing import Dict, List, Any, Optional, Tuple, Union
from pathlib import Path
from datetime import datetime

# Import task generation and validation components
try:
    from focus_forge.task_generation import TaskGenerator, convert_prd_to_tasks
    from focus_forge.task_validation import TaskValidator
    from focus_forge.confidence_scoring import ConfidenceScorer
    from focus_forge.dependency_graph import DependencyGraph
except ImportError:
    # When running directly from source directory
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from focus_forge.task_generation import TaskGenerator, convert_prd_to_tasks
    from focus_forge.task_validation import TaskValidator
    from focus_forge.confidence_scoring import ConfidenceScorer
    from focus_forge.dependency_graph import DependencyGraph

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TaskManagementAdapter:
    """
    Adapter class for integrating PRD parsing with the existing task management system.
    
    This class provides methods to:
    1. Generate tasks from PRDs using the enhanced parser
    2. Convert tasks to the format expected by the existing system
    3. Validate tasks before integration
    4. Handle errors during the integration process
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the adapter with configuration.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.task_generator = TaskGenerator(config=self.config.get("task_generation", {}))
        self.task_validator = TaskValidator()
        self.confidence_scorer = ConfidenceScorer(config=self.config.get("confidence_scoring", {}))
    
    def parse_prd_file(self, prd_file: str) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """
        Parse a PRD file and generate tasks with validation information.
        
        Args:
            prd_file: Path to the PRD file
            
        Returns:
            Tuple[List[Dict[str, Any]], Dict[str, Any]]: (tasks, validation_report)
            
        Raises:
            FileNotFoundError: If the PRD file doesn't exist
            ValueError: If the PRD file can't be parsed
        """
        try:
            # Check if file exists
            if not os.path.exists(prd_file):
                raise FileNotFoundError(f"PRD file not found: {prd_file}")
            
            # Read PRD file
            with open(prd_file, 'r') as f:
                prd_text = f.read()
            
            # Generate tasks using enhanced parser
            tasks = self.task_generator.generate_tasks_from_prd(prd_text)
            
            # Validate tasks with PRD-specific checks
            validation_report = self.validate_tasks_with_prd(tasks, prd_text)
            
            return tasks, validation_report
            
        except Exception as e:
            logger.error(f"Error parsing PRD file {prd_file}: {str(e)}")
            raise ValueError(f"Failed to parse PRD file: {str(e)}")
    
    def convert_to_system_format(self, 
                                 tasks: List[Dict[str, Any]],
                                 include_validation: bool = True) -> Dict[str, Any]:
        """
        Convert tasks to the format expected by the existing task management system.
        
        Args:
            tasks: List of generated tasks
            include_validation: Whether to include validation information
            
        Returns:
            Dict[str, Any]: Tasks in the format expected by existing system
        """
        try:
            # Basic system format with metadata
            system_format = {
                "version": "1.0",
                "generated_at": self._get_iso_timestamp(),
                "tasks": []
            }
            
            # Add tasks in system format
            for task in tasks:
                system_task = {
                    # Required fields for the existing system
                    "id": task["id"],
                    "title": task["title"],
                    "description": task["description"],
                    "status": task["status"],
                    "priority": task["priority"],
                    "dependencies": task["dependencies"],
                    
                    # Optional fields that might be supported
                    "details": task.get("details", ""),
                    "test_strategy": task.get("test_strategy", ""),
                    
                    # Add subtasks if present
                    "subtasks": task.get("subtasks", [])
                }
                
                # Add any task-specific validation info if requested
                if include_validation:
                    system_task["validation"] = {
                        "has_circular_dependencies": self._check_circular_dependencies(task, tasks),
                        "has_invalid_dependencies": self._check_invalid_dependencies(task, tasks),
                        "confidence_score": self._calculate_task_confidence(task)
                    }
                
                system_format["tasks"].append(system_task)
            
            return system_format
            
        except Exception as e:
            logger.error(f"Error converting tasks to system format: {str(e)}")
            raise ValueError(f"Failed to convert tasks to system format: {str(e)}")
    
    def merge_with_existing(self, 
                           new_tasks: List[Dict[str, Any]], 
                           existing_tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Merge new tasks with existing tasks, preserving completed tasks and relationships.
        
        Args:
            new_tasks: List of newly generated tasks
            existing_tasks: List of existing tasks in the system
            
        Returns:
            List[Dict[str, Any]]: Merged task list
        """
        try:
            # Create a map of existing task IDs for quick lookup
            existing_task_ids = {task["id"]: task for task in existing_tasks}
            
            # Find the highest task ID to ensure new tasks get unique IDs
            highest_id = 0
            for task_id in existing_task_ids.keys():
                try:
                    # Handle both numeric and T{num} format IDs
                    if task_id.startswith('T'):
                        id_num = int(task_id[1:])
                    else:
                        id_num = int(task_id)
                    highest_id = max(highest_id, id_num)
                except ValueError:
                    # Skip non-numeric IDs
                    pass
            
            merged_tasks = existing_tasks.copy()
            
            # Process new tasks
            for new_task in new_tasks:
                # Check if this task already exists
                if new_task["id"] in existing_task_ids:
                    # If it exists but is not done, update some fields but preserve status
                    existing_task = existing_task_ids[new_task["id"]]
                    if existing_task["status"] != "done":
                        # Update fields except status
                        for field in ["title", "description", "priority", "dependencies", "details", "test_strategy"]:
                            if field in new_task:
                                existing_task[field] = new_task[field]
                else:
                    # If the task is new, ensure it has a unique ID if needed
                    if any(t["id"] == new_task["id"] for t in merged_tasks):
                        # Generate a new unique ID
                        highest_id += 1
                        if new_task["id"].startswith('T'):
                            new_task["id"] = f"T{highest_id}"
                        else:
                            new_task["id"] = str(highest_id)
                    
                    # Add the new task
                    merged_tasks.append(new_task)
            
            return merged_tasks
            
        except Exception as e:
            logger.error(f"Error merging tasks: {str(e)}")
            raise ValueError(f"Failed to merge tasks: {str(e)}")
    
    def validate_integration(self, 
                            tasks: List[Dict[str, Any]], 
                            prd_text: str) -> Dict[str, Any]:
        """
        Perform comprehensive validation before integration.
        
        Args:
            tasks: List of tasks to validate
            prd_text: The PRD text used to generate tasks
            
        Returns:
            Dict[str, Any]: Validation report with confidence scoring
        """
        try:
            # Use PRD-specific validation
            validation_report = self.validate_tasks_with_prd(tasks, prd_text)
            
            # Add confidence scoring if not already included
            if "confidence_score" not in validation_report:
                confidence_report = self.confidence_scorer.generate_review_report(prd_text, tasks)
                validation_report.update({
                    "confidence_score": confidence_report["confidence_score"],
                    "clarity_score": confidence_report["clarity_score"],
                    "coverage_score": confidence_report["coverage_score"],
                    "ambiguous_requirements": confidence_report["ambiguous_requirements"],
                    "missing_requirements": confidence_report["missing_requirements"]
                })
            
            return validation_report
            
        except Exception as e:
            logger.error(f"Error during validation: {str(e)}")
            return {
                "status": "error",
                "error_message": str(e),
                "tasks_valid": False
            }
    
    def _check_circular_dependencies(self, task: Dict[str, Any], all_tasks: List[Dict[str, Any]]) -> bool:
        """Check if a task has circular dependencies."""
        task_id = task["id"]
        dependencies = task.get("dependencies", [])
        
        # Quick check - if no dependencies, can't have circular dependencies
        if not dependencies:
            return False
        
        # Create a dependency graph
        dependency_graph = DependencyGraph()
        
        # Add all tasks and dependencies to the graph
        for t in all_tasks:
            dependency_graph.add_node(t["id"])
        
        # Add dependencies (fixed to avoid Dependency attribute error)
        for t in all_tasks:
            for dep_id in t.get("dependencies", []):
                try:
                    dependency_graph.add_edge(t["id"], dep_id, "depends_on")
                except (AttributeError, TypeError):
                    # Fallback if add_edge doesn't exist
                    try:
                        dependency_graph.add_dependency(t["id"], dep_id, "depends_on")
                    except (AttributeError, TypeError):
                        # Last resort - just log it
                        logger.warning(f"Could not add dependency {t['id']} -> {dep_id}")
        
        # Check for cycles
        try:
            return dependency_graph.has_cycles()
        except (AttributeError, TypeError):
            # Fallback if has_cycles doesn't exist
            try:
                cycles = dependency_graph.find_cycles()
                return len(cycles) > 0
            except (AttributeError, TypeError):
                # Last resort - just return False
                logger.warning("Could not check for cycles in dependency graph")
                return False
    
    def _check_invalid_dependencies(self, task: Dict[str, Any], all_tasks: List[Dict[str, Any]]) -> bool:
        """Check if a task has invalid dependencies."""
        dependencies = task.get("dependencies", [])
        
        # Quick check - if no dependencies, can't have invalid dependencies
        if not dependencies:
            return False
        
        # Get all valid task IDs
        valid_task_ids = {t["id"] for t in all_tasks}
        
        # Check if any dependency is invalid
        for dep_id in dependencies:
            if dep_id not in valid_task_ids or dep_id == task["id"]:
                return True
        
        return False
    
    def validate_tasks_with_prd(self, tasks: List[Dict[str, Any]], prd_text: str) -> Dict[str, Any]:
        """
        Validate tasks with PRD-specific checks beyond basic schema validation.
        
        Args:
            tasks: List of tasks to validate
            prd_text: PRD text to compare tasks against
            
        Returns:
            Dict[str, Any]: Enhanced validation report
        """
        # Start with basic task validation
        validation_results = self.task_validator.validate_tasks(tasks)
        
        # Check PRD coverage
        try:
            # Use the confidence scorer to check coverage
            coverage_score = self.confidence_scorer.calculate_coverage_score(prd_text, tasks)
            clarity_score = self.confidence_scorer.calculate_clarity_score(prd_text)
            
            # Identify ambiguous and missing requirements
            ambiguous_reqs = self.confidence_scorer.identify_ambiguous_requirements(prd_text)
            missing_reqs = self.confidence_scorer.identify_missing_requirements(prd_text, tasks)
            
            # Add PRD-specific validation data
            validation_results.update({
                "prd_validation": {
                    "coverage_score": coverage_score,
                    "clarity_score": clarity_score,
                    "ambiguous_requirements": {
                        "count": len(ambiguous_reqs),
                        "items": ambiguous_reqs[:5]  # Limit to top 5 for clarity
                    },
                    "missing_requirements": {
                        "count": len(missing_reqs),
                        "items": missing_reqs[:5]  # Limit to top 5 for clarity
                    }
                }
            })
            
            # Overall validity based on coverage and basic validation
            validation_results["tasks_valid"] = (len(validation_results.get("errors", {})) == 0 and 
                                                coverage_score >= 0.7)  # 70% coverage threshold
            
        except Exception as e:
            logger.error(f"Error in PRD validation: {str(e)}")
            validation_results.update({
                "prd_validation_error": str(e)
            })
            
        return validation_results
    
    def _calculate_task_confidence(self, task: Dict[str, Any]) -> float:
        """Calculate a confidence score for a single task."""
        # Simple heuristic - more details and test strategy means higher confidence
        details_score = min(1.0, len(task.get("details", "")) / 100)
        test_score = min(1.0, len(task.get("test_strategy", "")) / 100)
        
        # Check for subtasks - having subtasks increases confidence
        subtasks_score = min(1.0, len(task.get("subtasks", [])) / 3) * 0.5
        
        # Weighted average
        return (details_score * 0.4) + (test_score * 0.3) + (subtasks_score * 0.3)
    
    def _get_iso_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        return datetime.now().isoformat()


# Simplified interface functions for common operations

def parse_prd_to_tasks(prd_file: str, config: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Parse a PRD file and generate tasks.
    
    Args:
        prd_file: Path to the PRD file
        config: Optional configuration
        
    Returns:
        List[Dict[str, Any]]: Generated tasks
    """
    adapter = TaskManagementAdapter(config)
    tasks, _ = adapter.parse_prd_file(prd_file)
    return tasks


def integrate_prd_with_existing(prd_file: str, 
                              existing_tasks_file: str,
                              output_file: Optional[str] = None,
                              config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Integrate a PRD with existing tasks.
    
    Args:
        prd_file: Path to the PRD file
        existing_tasks_file: Path to existing tasks file
        output_file: Optional path to save merged tasks
        config: Optional configuration
        
    Returns:
        Dict[str, Any]: Integration report
    """
    try:
        # Create adapter
        adapter = TaskManagementAdapter(config)
        
        # Check files
        if not os.path.exists(prd_file):
            raise FileNotFoundError(f"PRD file not found: {prd_file}")
        
        if not os.path.exists(existing_tasks_file):
            raise FileNotFoundError(f"Tasks file not found: {existing_tasks_file}")
        
        # Parse PRD
        with open(prd_file, 'r') as f:
            prd_text = f.read()
        
        # Load existing tasks
        with open(existing_tasks_file, 'r') as f:
            data = json.load(f)
            existing_tasks = data.get("tasks", [])
            if not existing_tasks and isinstance(data, list):
                existing_tasks = data
        
        # Generate new tasks from PRD
        new_tasks = adapter.task_generator.generate_tasks_from_prd(prd_text)
        
        # Merge tasks
        merged_tasks = adapter.merge_with_existing(new_tasks, existing_tasks)
        
        # Validate merged tasks
        validation_report = adapter.validate_tasks_with_prd(merged_tasks, prd_text)
        
        # Save to output file if specified
        temp_output = None
        if output_file:
            output_path = output_file
        else:
            # Create a temporary file if no output file specified
            temp_fd, temp_output = tempfile.mkstemp(suffix=".json")
            os.close(temp_fd)
            output_path = temp_output
        
        # Save merged tasks
        output_data = {
            "version": "1.0",
            "prd_file": prd_file,
            "existing_tasks_file": existing_tasks_file,
            "generated_at": datetime.now().isoformat(),
            "tasks": merged_tasks
        }
        
        with open(output_path, 'w') as f:
            json.dump(output_data, f, indent=2)
        
        # Create report
        report = {
            "status": "success",
            "new_tasks": len(new_tasks),
            "existing_tasks": len(existing_tasks),
            "merged_tasks": len(merged_tasks),
            "validation": validation_report,
            "output_file": output_path
        }
        
        return report
        
    except Exception as e:
        logger.error(f"Integration error: {str(e)}")
        return {
            "status": "error",
            "error_message": str(e)
        }


def validate_tasks_with_prd(tasks_file: str, 
                          prd_file: str,
                          config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Validate tasks against a PRD.
    
    Args:
        tasks_file: Path to tasks file
        prd_file: Path to PRD file
        config: Optional configuration
        
    Returns:
        Dict[str, Any]: Validation report
    """
    try:
        # Create adapter
        adapter = TaskManagementAdapter(config)
        
        # Check files
        if not os.path.exists(tasks_file):
            raise FileNotFoundError(f"Tasks file not found: {tasks_file}")
        
        if not os.path.exists(prd_file):
            raise FileNotFoundError(f"PRD file not found: {prd_file}")
        
        # Load tasks
        with open(tasks_file, 'r') as f:
            data = json.load(f)
            tasks = data.get("tasks", [])
            if not tasks and isinstance(data, list):
                tasks = data
        
        # Load PRD
        with open(prd_file, 'r') as f:
            prd_text = f.read()
        
        # Validate tasks against PRD
        validation_report = adapter.validate_tasks_with_prd(tasks, prd_text)
        
        # Add validation status
        validation_report["tasks_file"] = tasks_file
        validation_report["prd_file"] = prd_file
        validation_report["status"] = "success"
        
        return validation_report
        
    except Exception as e:
        logger.error(f"Validation error: {str(e)}")
        return {
            "status": "error",
            "error_message": str(e),
            "tasks_valid": False
        } 