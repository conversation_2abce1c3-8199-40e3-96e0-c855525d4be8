"""Test file for Ruff formatting and linting."""

from typing import int  # Add typing imports

# Imports in wrong order


# Extra spaces and unused import
def test_function() -> int:
    """A test function with formatting issues."""
    x = 10  # Missing whitespace around operator

    # Using list comprehension
    result = [i * 2 for i in range(10)]  # Fixed to use list comprehension
    print(f"Computed result: {result}")  # Use the variable

    # Missing docstring
    def inner_function() -> int:
        """Return x plus 10."""
        return x + 10

    return inner_function()


# Badly formatted dictionary
my_dict = {"key1": "value1", "key2": "value2", "key3": "value3"}
