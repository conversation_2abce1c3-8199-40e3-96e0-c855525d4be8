#!/usr/bin/env python3
"""
Confidence Scoring and Review System for focus_forge.

This module provides functionality for assessing the confidence in task generation
results and providing review suggestions for ambiguous or missing requirements.
"""
import re
import json
import logging
from typing import Dict, List, Any, Set, Tuple, Optional, Union
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ConfidenceScorer:
    """
    Calculates confidence scores for generated tasks based on various factors.
    
    This class analyzes PRD text and generated tasks to determine confidence levels,
    identify ambiguities, and suggest improvements.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the confidence scorer.
        
        Args:
            config: Optional configuration parameters
        """
        self.config = config or {}
        self.ambiguity_phrases = [
            "may", "might", "could", "should", "possibly", "perhaps", "maybe",
            "consider", "probably", "would be nice", "if possible", "potentially",
            "would like", "would be good", "ideally", "if feasible", "if time permits",
            "when convenient", "at some point", "eventually", "in the future"
        ]
        self.clarity_phrases = [
            "must", "required", "shall", "will", "always", "never", "every", "all",
            "none", "absolutely", "definitely", "critical", "essential", "necessary"
        ]
    
    def calculate_clarity_score(self, prd_text: str) -> float:
        """
        Calculate clarity score based on language used in the PRD.
        
        Args:
            prd_text: The PRD text to analyze
            
        Returns:
            float: Clarity score between 0.0 and 1.0
        """
        if not prd_text:
            return 0.0
        
        # Split the text into lines and then words for analysis
        lines = [line for line in prd_text.splitlines() if line.strip()]
        if not lines:
            return 0.0
        
        requirements = []
        in_requirements = False
        for line in lines:
            # Skip headings
            if line.startswith('#'):
                if 'requirement' in line.lower():
                    in_requirements = True
                continue
            
            # Look for common requirement patterns (numbered lists, bullet points)
            if re.match(r'^\d+\.', line) or line.strip().startswith('*') or line.strip().startswith('-'):
                requirements.append(line)
            elif in_requirements and line.strip():
                requirements.append(line)
        
        # If no specific requirements found, analyze all non-heading lines
        if not requirements:
            requirements = [line for line in lines if not line.startswith('#')]
        
        # Count ambiguous phrases
        ambiguous_count = 0
        for phrase in self.ambiguity_phrases:
            pattern = r'\b' + re.escape(phrase) + r'\b'
            for req in requirements:
                ambiguous_count += len(re.findall(pattern, req.lower()))
        
        # Count clarity phrases
        clarity_count = 0
        for phrase in self.clarity_phrases:
            pattern = r'\b' + re.escape(phrase) + r'\b'
            for req in requirements:
                clarity_count += len(re.findall(pattern, req.lower()))
        
        # Calculate base score
        total_phrases = max(1, ambiguous_count + clarity_count)  # Avoid division by zero
        clarity_ratio = clarity_count / total_phrases
        
        # Apply additional factors
        num_requirements = len(requirements)
        avg_requirement_length = sum(len(req.split()) for req in requirements) / max(1, num_requirements)
        
        # Longer requirements tend to be more detailed and clearer
        length_factor = min(1.0, avg_requirement_length / 15.0)  # Scale based on average length
        
        # Final score: weighted combination of clarity ratio and length factor
        weight_clarity = 0.7
        weight_length = 0.3
        score = (clarity_ratio * weight_clarity) + (length_factor * weight_length)
        
        # Ensure score is within bounds
        return max(0.0, min(1.0, score))
    
    def calculate_coverage_score(self, prd_text: str, tasks: List[Dict[str, Any]]) -> float:
        """
        Calculate coverage score based on how well tasks cover PRD requirements.
        
        Args:
            prd_text: The PRD text to analyze
            tasks: List of generated tasks
            
        Returns:
            float: Coverage score between 0.0 and 1.0
        """
        if not prd_text or not tasks:
            return 0.0
        
        # Extract key requirements from PRD
        requirements = self._extract_requirements(prd_text)
        if not requirements:
            return 0.0
        
        # Combine all task text for analysis
        task_text = ""
        for task in tasks:
            # Add task title and description
            task_text += task.get("title", "") + " "
            task_text += task.get("description", "") + " "
            task_text += task.get("details", "") + " "
            
            # Add subtask text if present
            subtasks = task.get("subtasks", [])
            for subtask in subtasks:
                task_text += subtask.get("title", "") + " "
                task_text += subtask.get("description", "") + " "
        
        task_text = task_text.lower()
        
        # Calculate coverage by checking for each requirement's key terms in task text
        covered_count = 0
        for req in requirements:
            # Extract key phrases from requirement
            key_terms = self._extract_key_terms(req)
            
            # Check if key terms appear in task text
            if key_terms and any(term in task_text for term in key_terms):
                covered_count += 1
        
        # Calculate coverage ratio
        coverage_ratio = covered_count / len(requirements)
        
        # Adjust for task depth (subtasks indicate more detailed coverage)
        subtask_count = sum(len(task.get("subtasks", [])) for task in tasks)
        depth_factor = min(1.0, subtask_count / max(5, len(requirements)))
        
        # Final score: weighted combination of coverage ratio and depth factor
        weight_coverage = 0.8
        weight_depth = 0.2
        score = (coverage_ratio * weight_coverage) + (depth_factor * weight_depth)
        
        # Ensure score is within bounds
        return max(0.0, min(1.0, score))
    
    def _extract_requirements(self, prd_text: str) -> List[str]:
        """
        Extract requirements from PRD text.
        
        Args:
            prd_text: The PRD text to analyze
            
        Returns:
            List[str]: List of requirement statements
        """
        lines = [line for line in prd_text.splitlines() if line.strip()]
        
        requirements = []
        in_requirements = False
        
        for line in lines:
            # Check for requirement section headers
            if line.startswith('#') and ('requirement' in line.lower() or 'feature' in line.lower()):
                in_requirements = True
                continue
            
            # Look for common requirement patterns (numbered lists, bullet points)
            if re.match(r'^\d+\.', line) or line.strip().startswith('*') or line.strip().startswith('-'):
                requirements.append(line.strip())
            elif in_requirements and line.strip() and not line.startswith('#'):
                requirements.append(line.strip())
        
        # If no specific requirements found, analyze all non-heading lines
        if not requirements:
            requirements = [line.strip() for line in lines if not line.startswith('#') and line.strip()]
        
        return requirements
    
    def _extract_key_terms(self, text: str) -> List[str]:
        """
        Extract key terms from text.
        
        Args:
            text: The text to analyze
            
        Returns:
            List[str]: List of key terms
        """
        # Remove common words and punctuation
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        words = text.split()
        
        # Filter out common stop words
        stop_words = {
            "a", "an", "the", "and", "or", "but", "if", "then", "else", "when",
            "at", "by", "for", "with", "about", "against", "between", "into",
            "through", "during", "before", "after", "above", "below", "to",
            "from", "up", "down", "in", "out", "on", "off", "over", "under",
            "again", "further", "then", "once", "here", "there", "when",
            "where", "why", "how", "all", "any", "both", "each", "few", "more",
            "most", "other", "some", "such", "no", "nor", "not", "only", "own",
            "same", "so", "than", "too", "very", "s", "t", "can", "will", "just",
            "don", "don't", "should", "now", "of", "is", "are", "be"
        }
        
        key_terms = [word for word in words if word not in stop_words and len(word) > 2]
        
        # Add any multi-word terms that might be important
        phrases = []
        for i in range(len(words) - 1):
            if words[i] not in stop_words or words[i+1] not in stop_words:
                phrases.append(words[i] + " " + words[i+1])
        
        # Combine individual terms and phrases
        return key_terms + phrases
    
    def calculate_complexity_score(self, prd_text: str) -> float:
        """
        Calculate complexity score for the PRD requirements.
        
        Args:
            prd_text: The PRD text to analyze
            
        Returns:
            float: Complexity score between 0.0 and 1.0
        """
        if not prd_text:
            return 0.0
        
        # Extract requirements from PRD
        requirements = self._extract_requirements(prd_text)
        if not requirements:
            return 0.0
        
        # Complexity factors
        avg_length = sum(len(req.split()) for req in requirements) / len(requirements)
        
        # Number of technical terms (simplified approach)
        technical_term_patterns = [
            r'\b[a-z]+\.[a-z]+\b',  # Dot notation (e.g., user.profile)
            r'\b[A-Z][a-zA-Z]+\b',  # CamelCase words likely to be technical terms
            r'\b[A-Z][A-Z0-9_]+\b',  # UPPERCASE_CONSTANTS
            r'\b\d+[a-zA-Z]+\b',     # Numbers with letters (e.g., 3D, 2FA)
            r'<[^>]+>',              # Angle brackets (e.g., <token>)
            r'\[\w+\]',              # Square brackets (e.g., [parameter])
            r'`[^`]+`'               # Backticks (e.g., `code`)
        ]
        
        technical_term_count = 0
        for pattern in technical_term_patterns:
            for req in requirements:
                technical_term_count += len(re.findall(pattern, req))
        
        # Normalize technical term count by number of requirements
        normalized_tech_terms = technical_term_count / max(1, len(requirements))
        
        # Conditional statements indicate complexity
        conditional_patterns = [
            r'\bif\b', r'\belse\b', r'\bunless\b', r'\bin case\b', r'\bwhen\b',
            r'\bshould\b', r'\bmust\b'
        ]
        
        conditional_count = 0
        for pattern in conditional_patterns:
            for req in requirements:
                conditional_count += len(re.findall(pattern, req.lower()))
        
        # Normalized by number of requirements
        normalized_conditionals = conditional_count / max(1, len(requirements))
        
        # Interconnected requirements score (mentions of other components)
        interconnection_patterns = [
            r'\bintegrate\b', r'\bconnect\b', r'\binterface\b', r'\bapi\b',
            r'\bdatabase\b', r'\bcommunicate\b', r'\bsync\b', r'\binteract\b'
        ]
        
        interconnection_count = 0
        for pattern in interconnection_patterns:
            for req in requirements:
                interconnection_count += len(re.findall(pattern, req.lower()))
        
        # Normalized by number of requirements
        normalized_interconnections = interconnection_count / max(1, len(requirements))
        
        # Combine complexity factors
        length_score = min(1.0, avg_length / 25.0)  # Scale based on average length
        tech_score = min(1.0, normalized_tech_terms / 2.0)
        conditional_score = min(1.0, normalized_conditionals / 1.5)
        interconnection_score = min(1.0, normalized_interconnections / 1.5)
        
        # Final complexity score: weighted average of all factors
        weights = {
            "length": 0.2,
            "technical": 0.3,
            "conditional": 0.25,
            "interconnection": 0.25
        }
        
        complexity_score = (
            length_score * weights["length"] +
            tech_score * weights["technical"] +
            conditional_score * weights["conditional"] +
            interconnection_score * weights["interconnection"]
        )
        
        # Ensure score is within bounds
        return max(0.0, min(1.0, complexity_score))
    
    def calculate_overall_confidence(self, prd_text: str, tasks: List[Dict[str, Any]]) -> float:
        """
        Calculate overall confidence score.
        
        Args:
            prd_text: The PRD text to analyze
            tasks: List of generated tasks
            
        Returns:
            float: Overall confidence score between 0.0 and 1.0
        """
        clarity = self.calculate_clarity_score(prd_text)
        coverage = self.calculate_coverage_score(prd_text, tasks)
        complexity = self.calculate_complexity_score(prd_text)
        
        # TODO: Implement weighted scoring algorithm
        # For now, use a simple average
        return (clarity + coverage + (1.0 - complexity)) / 3.0
    
    def identify_ambiguous_requirements(self, prd_text: str) -> List[Dict[str, Any]]:
        """
        Identify ambiguous requirements in the PRD.
        
        Args:
            prd_text: The PRD text to analyze
            
        Returns:
            List[Dict[str, Any]]: List of ambiguous requirements with details
        """
        if not prd_text:
            return []
        
        # Extract requirements
        requirements = self._extract_requirements(prd_text)
        if not requirements:
            return []
        
        ambiguous_requirements = []
        
        for i, req in enumerate(requirements):
            ambiguous_phrases_found = []
            
            # Check for ambiguous phrases
            for phrase in self.ambiguity_phrases:
                pattern = r'\b' + re.escape(phrase) + r'\b'
                matches = re.findall(pattern, req.lower())
                if matches:
                    ambiguous_phrases_found.extend(matches)
            
            # Check for vague quantifiers
            vague_quantifiers = [r'\bmany\b', r'\bfew\b', r'\bsome\b', r'\blots\b', r'\bseveral\b']
            for pattern in vague_quantifiers:
                matches = re.findall(pattern, req.lower())
                if matches:
                    ambiguous_phrases_found.extend(matches)
            
            # Check for missing specifics
            missing_specifics = False
            if len(req.split()) < 6:  # Too short to be specific
                missing_specifics = True
            
            # Only add if ambiguous phrases or missing specifics found
            if ambiguous_phrases_found or missing_specifics:
                ambiguity_score = len(ambiguous_phrases_found) / 3.0  # Scale by expected max phrases
                if missing_specifics:
                    ambiguity_score += 0.3
                
                # Cap at 1.0
                ambiguity_score = min(1.0, ambiguity_score)
                
                severity = "high" if ambiguity_score > 0.7 else "medium" if ambiguity_score > 0.3 else "low"
                
                ambiguous_requirements.append({
                    "requirement": req,
                    "requirement_index": i,
                    "ambiguous_phrases": ambiguous_phrases_found,
                    "missing_specifics": missing_specifics,
                    "ambiguity_score": ambiguity_score,
                    "severity": severity
                })
        
        # Sort by ambiguity score (highest first)
        ambiguous_requirements.sort(key=lambda x: x["ambiguity_score"], reverse=True)
        
        return ambiguous_requirements
    
    def identify_missing_requirements(self, prd_text: str, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Identify potentially missing requirements.
        
        Args:
            prd_text: The PRD text to analyze
            tasks: List of generated tasks
            
        Returns:
            List[Dict[str, Any]]: List of potentially missing requirements
        """
        if not prd_text or not tasks:
            return []
        
        # Extract requirements from PRD
        requirements = self._extract_requirements(prd_text)
        if not requirements:
            return []
        
        # Combine all task text for analysis
        task_text = ""
        for task in tasks:
            # Add task title and description
            task_text += task.get("title", "") + " "
            task_text += task.get("description", "") + " "
            task_text += task.get("details", "") + " "
            
            # Add subtask text if present
            subtasks = task.get("subtasks", [])
            for subtask in subtasks:
                task_text += subtask.get("title", "") + " "
                task_text += subtask.get("description", "") + " "
        
        task_text = task_text.lower()
        
        # Look for potentially missing requirements
        missing_requirements = []
        
        for i, req in enumerate(requirements):
            # Extract key phrases from requirement
            key_terms = self._extract_key_terms(req)
            
            # Check if key terms appear in task text
            covered = False
            matching_terms = []
            
            for term in key_terms:
                if term in task_text:
                    covered = True
                    matching_terms.append(term)
            
            # If not covered, mark as potentially missing
            if not covered:
                # Try to find related tasks by partial matches
                related_tasks = []
                for task in tasks:
                    task_combined_text = (
                        task.get("title", "") + " " +
                        task.get("description", "") + " " +
                        task.get("details", "")
                    ).lower()
                    
                    # Check if any term has partial match
                    match_score = 0
                    for term in key_terms:
                        # Calculate longest common substring length
                        for word in task_combined_text.split():
                            common_length = self._longest_common_substring_length(term, word)
                            if common_length >= 4:  # Require at least 4 chars to match
                                match_score += 1
                    
                    if match_score > 0:
                        related_tasks.append({
                            "id": task.get("id", "unknown"),
                            "title": task.get("title", "unknown"),
                            "match_score": match_score
                        })
                
                # Sort related tasks by match score
                related_tasks.sort(key=lambda x: x["match_score"], reverse=True)
                
                # Limit to top 3 matches
                related_tasks = related_tasks[:3]
                
                # Determine severity based on the importance indicators in the requirement
                importance_indicators = [
                    "must", "required", "shall", "will", "critical", "essential",
                    "necessary", "important", "crucial", "vital"
                ]
                
                has_importance_indicator = any(
                    indicator in req.lower() for indicator in importance_indicators
                )
                
                severity = "high" if has_importance_indicator else "medium"
                
                missing_requirements.append({
                    "requirement": req,
                    "requirement_index": i,
                    "key_terms": key_terms,
                    "related_tasks": related_tasks,
                    "severity": severity
                })
        
        return missing_requirements
    
    def generate_review_report(self, prd_text: str, tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate a comprehensive review report.
        
        Args:
            prd_text: The PRD text to analyze
            tasks: List of generated tasks
            
        Returns:
            Dict[str, Any]: Detailed review report
        """
        # Calculate scores
        clarity_score = self.calculate_clarity_score(prd_text)
        coverage_score = self.calculate_coverage_score(prd_text, tasks)
        complexity_score = self.calculate_complexity_score(prd_text)
        confidence_score = self.calculate_overall_confidence(prd_text, tasks)
        
        # Identify issues
        ambiguous_requirements = self.identify_ambiguous_requirements(prd_text)
        missing_requirements = self.identify_missing_requirements(prd_text, tasks)
        
        # Generate recommendations based on findings
        recommendations = []
        
        # Recommendation for ambiguous requirements
        if ambiguous_requirements:
            high_severity = [req for req in ambiguous_requirements if req["severity"] == "high"]
            if high_severity:
                recommendations.append({
                    "type": "clarify_ambiguous_requirements",
                    "priority": "high",
                    "description": "Clarify ambiguous requirements with high severity",
                    "details": f"There are {len(high_severity)} high severity ambiguous requirements that need clarification.",
                    "items": high_severity[:3]  # Include top 3 as examples
                })
            
            medium_severity = [req for req in ambiguous_requirements if req["severity"] == "medium"]
            if medium_severity:
                recommendations.append({
                    "type": "clarify_ambiguous_requirements",
                    "priority": "medium",
                    "description": "Consider clarifying moderately ambiguous requirements",
                    "details": f"There are {len(medium_severity)} moderately ambiguous requirements that may benefit from clarification.",
                    "items": medium_severity[:3]  # Include top 3 as examples
                })
        
        # Recommendation for missing requirements
        if missing_requirements:
            high_severity = [req for req in missing_requirements if req["severity"] == "high"]
            if high_severity:
                recommendations.append({
                    "type": "address_missing_requirements",
                    "priority": "high",
                    "description": "Address potentially missing important requirements",
                    "details": f"There are {len(high_severity)} important requirements that may not be adequately covered by tasks.",
                    "items": high_severity[:3]  # Include top 3 as examples
                })
            
            medium_severity = [req for req in missing_requirements if req["severity"] == "medium"]
            if medium_severity:
                recommendations.append({
                    "type": "address_missing_requirements",
                    "priority": "medium",
                    "description": "Consider addressing potentially missing requirements",
                    "details": f"There are {len(medium_severity)} requirements that may not be adequately covered by tasks.",
                    "items": medium_severity[:3]  # Include top 3 as examples
                })
        
        # Recommendation for complexity
        if complexity_score > 0.7:
            recommendations.append({
                "type": "complexity_management",
                "priority": "high",
                "description": "Consider breaking down complex requirements",
                "details": "The requirements have high complexity which may lead to implementation challenges. Consider breaking them down into smaller, more manageable pieces."
            })
        elif complexity_score > 0.5:
            recommendations.append({
                "type": "complexity_management",
                "priority": "medium",
                "description": "Monitor complexity during implementation",
                "details": "The requirements have moderate complexity. Consider additional planning and review during implementation."
            })
        
        # Recommendation for coverage
        if coverage_score < 0.5:
            recommendations.append({
                "type": "improve_coverage",
                "priority": "high",
                "description": "Improve task coverage of requirements",
                "details": "Task coverage of requirements is low. Consider adding more tasks to ensure all requirements are addressed."
            })
        elif coverage_score < 0.7:
            recommendations.append({
                "type": "improve_coverage",
                "priority": "medium",
                "description": "Review task coverage of requirements",
                "details": "Task coverage of requirements is moderate. Consider reviewing tasks to ensure comprehensive coverage."
            })
        
        # Structure the final report
        report = {
            "confidence_score": confidence_score,
            "clarity_score": clarity_score,
            "coverage_score": coverage_score,
            "complexity_score": complexity_score,
            "ambiguous_requirements": {
                "count": len(ambiguous_requirements),
                "high_severity_count": len([req for req in ambiguous_requirements if req["severity"] == "high"]),
                "medium_severity_count": len([req for req in ambiguous_requirements if req["severity"] == "medium"]),
                "items": ambiguous_requirements
            },
            "missing_requirements": {
                "count": len(missing_requirements),
                "high_severity_count": len([req for req in missing_requirements if req["severity"] == "high"]),
                "medium_severity_count": len([req for req in missing_requirements if req["severity"] == "medium"]),
                "items": missing_requirements
            },
            "recommendations": recommendations,
            "summary": self._generate_summary(confidence_score, ambiguous_requirements, missing_requirements)
        }
        
        return report
    
    def _generate_summary(self, confidence_score: float, ambiguous_requirements: List[Dict[str, Any]], 
                         missing_requirements: List[Dict[str, Any]]) -> str:
        """
        Generate a summary of the analysis.
        
        Args:
            confidence_score: Overall confidence score
            ambiguous_requirements: List of ambiguous requirements
            missing_requirements: List of missing requirements
            
        Returns:
            str: Summary text
        """
        if confidence_score > 0.8:
            confidence_level = "high"
        elif confidence_score > 0.5:
            confidence_level = "moderate"
        else:
            confidence_level = "low"
        
        high_severity_ambiguous = len([req for req in ambiguous_requirements if req["severity"] == "high"])
        high_severity_missing = len([req for req in missing_requirements if req["severity"] == "high"])
        
        if high_severity_ambiguous == 0 and high_severity_missing == 0:
            issue_status = "no critical issues"
        elif high_severity_ambiguous > 0 and high_severity_missing > 0:
            issue_status = f"{high_severity_ambiguous} critical ambiguities and {high_severity_missing} potentially missing requirements"
        elif high_severity_ambiguous > 0:
            issue_status = f"{high_severity_ambiguous} critical ambiguities"
        else:
            issue_status = f"{high_severity_missing} potentially missing requirements"
        
        return f"Analysis completed with {confidence_level} confidence. Found {issue_status}."
    
    def _longest_common_substring_length(self, s1: str, s2: str) -> int:
        """
        Find the length of the longest common substring between two strings.
        
        Args:
            s1: First string
            s2: Second string
            
        Returns:
            int: Length of longest common substring
        """
        if not s1 or not s2:
            return 0
        
        s1, s2 = s1.lower(), s2.lower()
        m, n = len(s1), len(s2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        max_length = 0
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if s1[i-1] == s2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                    max_length = max(max_length, dp[i][j])
        
        return max_length


class ReviewSystem:
    """
    System for reviewing and suggesting improvements to PRD analysis and task generation.
    
    This class provides tools for flagging issues, suggesting clarifications,
    and improving generated tasks.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the review system.
        
        Args:
            config: Optional configuration parameters
        """
        self.config = config or {}
        self.confidence_scorer = ConfidenceScorer(config)
    
    def analyze_and_flag_issues(self, prd_text: str, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Analyze PRD and tasks to flag issues.
        
        Args:
            prd_text: The PRD text to analyze
            tasks: List of generated tasks
            
        Returns:
            List[Dict[str, Any]]: List of flagged issues
        """
        issues = []
        
        # Get analysis results from confidence scorer
        ambiguous_requirements = self.confidence_scorer.identify_ambiguous_requirements(prd_text)
        missing_requirements = self.confidence_scorer.identify_missing_requirements(prd_text, tasks)
        
        # Flag high severity ambiguous requirements
        for req in ambiguous_requirements:
            if req["severity"] == "high":
                issues.append({
                    "type": "ambiguous_requirement",
                    "priority": "high",
                    "requirement": req["requirement"],
                    "ambiguous_phrases": req["ambiguous_phrases"],
                    "missing_specifics": req.get("missing_specifics", False),
                    "description": f"Ambiguous requirement with severity: {req['severity']}"
                })
        
        # Flag high severity missing requirements
        for req in missing_requirements:
            if req["severity"] == "high":
                issues.append({
                    "type": "missing_requirement",
                    "priority": "high",
                    "requirement": req["requirement"],
                    "possible_related_tasks": [task["id"] for task in req.get("related_tasks", [])],
                    "description": f"Potentially missing requirement with severity: {req['severity']}"
                })
        
        # Flag dependency issues
        dependency_issues = self._check_task_dependencies(tasks)
        issues.extend(dependency_issues)
        
        # Flag quality issues
        quality_issues = self._check_task_quality(tasks)
        issues.extend(quality_issues)
        
        # Sort issues by priority
        priority_order = {"high": 0, "medium": 1, "low": 2}
        issues.sort(key=lambda x: priority_order.get(x.get("priority", "low"), 3))
        
        return issues
    
    def suggest_clarifications(self, prd_text: str) -> List[Dict[str, Any]]:
        """
        Suggest clarifications for ambiguous requirements.
        
        Args:
            prd_text: The PRD text to analyze
            
        Returns:
            List[Dict[str, Any]]: List of suggested clarifications
        """
        clarifications = []
        
        # Get ambiguous requirements
        ambiguous_requirements = self.confidence_scorer.identify_ambiguous_requirements(prd_text)
        
        for req in ambiguous_requirements:
            suggestion = {"original": req["requirement"]}
            
            # Generate clarification suggestion based on ambiguity type
            clarified_text = req["requirement"]
            
            # Replace ambiguous phrases with more specific alternatives
            for phrase in req.get("ambiguous_phrases", []):
                if phrase in ["may", "might", "could"]:
                    clarified_text = clarified_text.replace(phrase, "must")
                elif phrase in ["should", "would be nice", "ideally"]:
                    clarified_text = clarified_text.replace(phrase, "shall")
                elif phrase in ["possibly", "perhaps", "maybe", "consider"]:
                    clarified_text = clarified_text.replace(phrase, "will")
            
            # Suggestion for requirements that are too short
            if req.get("missing_specifics", False):
                suggestion["suggestion"] = clarified_text + " [NEEDS MORE DETAIL: Add specific criteria, constraints, or acceptance criteria]"
                suggestion["rationale"] = "Requirement is too vague and needs more specific details."
            else:
                suggestion["suggestion"] = clarified_text
                suggestion["rationale"] = "Replaced ambiguous language with clear, definitive terms."
            
            # Add ambiguous phrases that were identified
            suggestion["ambiguous_phrases"] = req.get("ambiguous_phrases", [])
            
            # Add severity
            suggestion["severity"] = req["severity"]
            
            clarifications.append(suggestion)
        
        # Sort by severity
        severity_order = {"high": 0, "medium": 1, "low": 2}
        clarifications.sort(key=lambda x: severity_order.get(x.get("severity", "low"), 3))
        
        return clarifications
    
    def suggest_task_improvements(self, prd_text: str, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Suggest improvements for generated tasks.
        
        Args:
            prd_text: The PRD text to analyze
            tasks: List of generated tasks
            
        Returns:
            List[Dict[str, Any]]: List of suggested task improvements
        """
        improvements = []
        
        # Get potentially missing requirements
        missing_requirements = self.confidence_scorer.identify_missing_requirements(prd_text, tasks)
        
        # Suggest new tasks for missing requirements
        for req in missing_requirements:
            # Create suggested task
            suggested_task = {
                "title": f"Implement {req['requirement'][:50]}{'...' if len(req['requirement']) > 50 else ''}",
                "description": f"Address requirement: {req['requirement']}",
                "status": "pending",
                "priority": "high" if req["severity"] == "high" else "medium",
                "dependencies": []
            }
            
            # Add dependencies to related tasks if any
            for related in req.get("related_tasks", []):
                suggested_task["dependencies"].append(related["id"])
            
            improvements.append({
                "type": "new_task",
                "suggested_task": suggested_task,
                "related_requirement": req["requirement"],
                "severity": req["severity"],
                "rationale": "This requirement may not be adequately covered by existing tasks."
            })
        
        # Suggest task quality improvements
        quality_improvements = self._suggest_task_quality_improvements(tasks)
        improvements.extend(quality_improvements)
        
        # Sort by severity
        severity_order = {"high": 0, "medium": 1, "low": 2}
        improvements.sort(key=lambda x: severity_order.get(x.get("severity", "low"), 3))
        
        return improvements
    
    def generate_review_interface(self, prd_text: str, tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate a review interface for human review.
        
        Args:
            prd_text: The PRD text to analyze
            tasks: List of generated tasks
            
        Returns:
            Dict[str, Any]: Review interface data
        """
        # Get confidence report
        report = self.confidence_scorer.generate_review_report(prd_text, tasks)
        
        # Get issues
        issues = self.analyze_and_flag_issues(prd_text, tasks)
        
        # Get clarifications
        clarifications = self.suggest_clarifications(prd_text)
        
        # Get task improvements
        improvements = self.suggest_task_improvements(prd_text, tasks)
        
        # Create interactive elements for the interface
        interactive_elements = self._create_interactive_elements(
            report, issues, clarifications, improvements
        )
        
        # Compile interface data
        interface = {
            "report": report,
            "issues": issues,
            "clarifications": clarifications,
            "improvements": improvements,
            "interactive_elements": interactive_elements
        }
        
        return interface
    
    def _check_task_dependencies(self, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Check task dependencies for issues.
        
        Args:
            tasks: List of tasks to check
            
        Returns:
            List[Dict[str, Any]]: List of dependency issues
        """
        issues = []
        
        # Build a set of all task IDs
        task_ids = {task.get("id") for task in tasks if "id" in task}
        
        # Check for missing dependencies
        for task in tasks:
            task_id = task.get("id", "unknown")
            for dep_id in task.get("dependencies", []):
                if dep_id not in task_ids:
                    issues.append({
                        "type": "missing_dependency",
                        "priority": "high",
                        "task_id": task_id,
                        "task_title": task.get("title", "unknown"),
                        "dependency_id": dep_id,
                        "description": f"Task {task_id} depends on non-existent task {dep_id}"
                    })
        
        # Check for circular dependencies
        # This would require implementing cycle detection in a dependency graph
        
        return issues
    
    def _check_task_quality(self, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Check task quality for issues.
        
        Args:
            tasks: List of tasks to check
            
        Returns:
            List[Dict[str, Any]]: List of quality issues
        """
        issues = []
        
        for task in tasks:
            task_id = task.get("id", "unknown")
            
            # Check title length
            title = task.get("title", "")
            if len(title) < 10:
                issues.append({
                    "type": "title_too_short",
                    "priority": "medium",
                    "task_id": task_id,
                    "current_value": title,
                    "description": f"Task {task_id} has a very short title"
                })
            
            # Check description length
            description = task.get("description", "")
            if len(description) < 15:
                issues.append({
                    "type": "description_too_short",
                    "priority": "medium",
                    "task_id": task_id,
                    "current_value": description,
                    "description": f"Task {task_id} has a very short description"
                })
            
            # Check details length
            details = task.get("details", "")
            if len(details) < 25:
                issues.append({
                    "type": "details_too_short",
                    "priority": "low",
                    "task_id": task_id,
                    "current_value": details,
                    "description": f"Task {task_id} has limited details"
                })
        
        return issues
    
    def _suggest_task_quality_improvements(self, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Suggest improvements for task quality.
        
        Args:
            tasks: List of tasks to check
            
        Returns:
            List[Dict[str, Any]]: List of task quality improvements
        """
        improvements = []
        
        # Look for tasks with minimal descriptions or details
        for task in tasks:
            task_id = task.get("id", "unknown")
            title = task.get("title", "")
            description = task.get("description", "")
            details = task.get("details", "")
            
            if len(description) < 30 or len(details) < 50:
                improvements.append({
                    "type": "enhance_task_description",
                    "task_id": task_id,
                    "task_title": title,
                    "severity": "medium",
                    "rationale": "Task has minimal description or details which may lead to implementation confusion.",
                    "suggestion": "Expand the description and details to provide more context, acceptance criteria, and implementation guidance."
                })
            
            # Check if task has subtasks
            subtasks = task.get("subtasks", [])
            if not subtasks and ("implement" in title.lower() or "create" in title.lower() or "build" in title.lower()):
                improvements.append({
                    "type": "add_subtasks",
                    "task_id": task_id,
                    "task_title": title,
                    "severity": "medium",
                    "rationale": "Implementation task has no subtasks which may indicate insufficient breakdown.",
                    "suggestion": "Consider breaking this task into subtasks to clearly define the implementation steps."
                })
        
        return improvements
    
    def _create_interactive_elements(self, report: Dict[str, Any], issues: List[Dict[str, Any]],
                                    clarifications: List[Dict[str, Any]], improvements: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create interactive elements for the review interface.
        
        Args:
            report: Confidence report
            issues: Flagged issues
            clarifications: Suggested clarifications
            improvements: Suggested improvements
            
        Returns:
            Dict[str, Any]: Interactive elements
        """
        # This is a placeholder for actual UI elements that would be implemented
        # in a real user interface. For now, we just provide data structures that
        # could be used by a UI.
        
        # Create filterable issue list
        issue_filters = {
            "by_priority": {
                "high": [i for i in issues if i.get("priority") == "high"],
                "medium": [i for i in issues if i.get("priority") == "medium"],
                "low": [i for i in issues if i.get("priority") == "low"]
            },
            "by_type": {}
        }
        
        # Group issues by type
        for issue in issues:
            issue_type = issue.get("type", "unknown")
            if issue_type not in issue_filters["by_type"]:
                issue_filters["by_type"][issue_type] = []
            issue_filters["by_type"][issue_type].append(issue)
        
        # Create clarification interface
        clarification_interface = {
            "editable_suggestions": True,
            "items": clarifications
        }
        
        # Create improvement interface
        improvement_interface = {
            "can_add_to_tasks": True,
            "items": improvements
        }
        
        # Create score visualizations
        score_visualizations = {
            "confidence_gauge": {
                "value": report["confidence_score"],
                "thresholds": {"low": 0.4, "medium": 0.7, "high": 1.0}
            },
            "scores_radar": {
                "values": {
                    "Clarity": report["clarity_score"],
                    "Coverage": report["coverage_score"],
                    "Simplicity": 1.0 - report["complexity_score"]
                }
            }
        }
        
        return {
            "issue_filters": issue_filters,
            "clarification_interface": clarification_interface,
            "improvement_interface": improvement_interface,
            "score_visualizations": score_visualizations,
            "can_export_report": True,
            "can_apply_suggestions": True
        }
