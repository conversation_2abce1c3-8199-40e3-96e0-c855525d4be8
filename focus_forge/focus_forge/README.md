# 🔥 Focus Forge 🛠️

> **Turn chaos into clarity** - Your productivity companion in the terminal

<p align="center">
  <img src="https://img.shields.io/badge/Python-3.10+-blue.svg" alt="Python 3.10+"/>
  <img src="https://img.shields.io/badge/License-MIT-green.svg" alt="MIT License"/>
  <img src="https://img.shields.io/badge/Status-Active-brightgreen.svg" alt="Status Active"/>
</p>

## 🌟 What is Focus Forge?

Focus Forge is a powerful, single-file Python CLI task management system designed for developers who want to **stay in the flow** without leaving the terminal. It helps you organize tasks, track dependencies, and leverage AI to break down complex work.

## ⚡ Key Features

- 📋 **Task Management** - Create, organize, and track your tasks 
- 🔄 **Dependencies** - Define task relationships and validate them
- 🧠 **AI-Powered** - Auto-generate tasks from PRDs and expand complex tasks
- 🖥️ **Terminal UI** - Rich colors and formatting in your terminal
- 🧩 **Task Generation** - Generate individual task files from your task list
- 🏗️ **Nix Integration** - Seamless setup with nix-shell configuration

## 🔄 Workflow

```mermaid
graph TD
    A[Initialize Project] -->|Creates config & task files| B[Parse PRD]
    B -->|Generates initial tasks| C[List Tasks]
    C --> D{Choose Next Action}
    D -->|View details| E[Show Task]
    D -->|Get recommendation| F[Next Task]
    D -->|Change status| G[Update Status]
    D -->|Break down task| H[Expand Task]
    D -->|Manage connections| I[Manage Dependencies]
    D -->|Update based on changes| J[Update Tasks]
    D -->|Generate files| K[Generate Task Files]
    G --> C
    H --> C
    I --> C
    J --> C
    E --> D
    F --> D
    K --> D
```

## 🚀 Installation

### 🐳 Using DevContainer (Recommended for Development)

```bash
# 1. Install Docker and VS Code with Remote-Containers extension
# 2. Open the project in VS Code
# 3. When prompted, click "Reopen in Container"
# 4. 🎉 You're ready to go!
```

### 🧙‍♂️ Using nix-shell

```bash
# Generate nix-shell configuration
./focus_forge.py nix-config

# Enter nix-shell environment
nix-shell

# Use the tool with magic ✨
focus-forge --help
```

### 🛠️ Manual Installation

```bash
# Install dependencies
pip install click rich requests

# Make the script executable
chmod +x focus_forge.py

# Run the tool
./focus_forge.py --help
```

## 📋 Usage Guide

### 🌱 Initialize a Project

```bash
./focus_forge.py init
```

### 📝 Parse a PRD and Generate Tasks

```bash
./focus_forge.py parse-prd --input=prd.txt --tasks=10
```

### 📊 View Your Tasks

```bash
# List all tasks
./focus_forge.py list

# Show specific task details
./focus_forge.py show <task_id>

# Show next recommended task
./focus_forge.py next
```

### 🔄 Update Task Status

```bash
./focus_forge.py set-status --id=<task_id> --status=<status>
# Status options: 🟡 pending, 🟢 in-progress, ✅ done, ⏸️ deferred
```

### 🧩 Break Down a Task with AI

```bash
./focus_forge.py expand --id=<task_id> --num=<num_subtasks>
```

### 🔗 Dependency Management

```bash
# Add dependency
./focus_forge.py add-dependency --id=<task_id> --depends-on=<dependency_id>

# Remove dependency
./focus_forge.py remove-dependency --id=<task_id> --depends-on=<dependency_id>

# Validate dependencies
./focus_forge.py validate-dependencies

# Fix dependency issues
./focus_forge.py fix-dependencies
```

### 🔄 Update Tasks with New Context

```bash
./focus_forge.py update --from=<task_id> --prompt="New context or changes"
```

### 📄 Generate Task Files

```bash
./focus_forge.py generate
```

## ⚙️ Configuration

Focus Forge can be configured through environment variables or a `.focus_forge_config` file:

| Variable | Description | Default |
|----------|-------------|---------|
| `ANTHROPIC_API_KEY` | Your Anthropic API key for Claude | - |
| `MODEL` | AI model to use | "claude-3-7-sonnet-20250219" |
| `MAX_TOKENS` | Maximum tokens for responses | 4000 |
| `TEMPERATURE` | Temperature for responses | 0.7 |
| `PROJECT_NAME` | Default project name | - |
| `PROJECT_VERSION` | Default project version | - |
| `DEFAULT_SUBTASKS` | Default number of subtasks | 3 |
| `DEFAULT_PRIORITY` | Default priority | medium |

## 🧪 Development

### 🧬 Testing

```bash
# Install dev dependencies
pip install -e ".[dev]"

# Run tests
pytest
```

### 🧹 Code Quality

```bash
# The repository includes a Ruff configuration for code linting and formatting
# To set up pre-commit hooks, simply run the setup script:
./setup-hooks.sh

# Alternatively, you can manually install and set up pre-commit:
pip install pre-commit
pre-commit install

# To run the hooks manually on all files:
pre-commit run --all-files
```

The pre-commit hooks will automatically run Ruff for:
- Code linting (with auto-fixes for common issues)
- Code formatting to ensure consistent style

## 📜 License

MIT

## 💡 Tips & Tricks

- 💻 Use `focus-forge next` to always know what to work on next
- 🔄 Run `validate-dependencies` periodically to catch circular dependencies
- 🌙 Dark mode terminal works best with the rich text formatting
- ⚡ Define keyboard shortcuts for common commands to boost productivity

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the project
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 🙏 Acknowledgements

- The [Rich](https://github.com/Textualize/rich) library for beautiful terminal output
- [Click](https://click.palletsprojects.com/) for making CLI development a breeze
- [Anthropic Claude](https://www.anthropic.com/claude) for powering the AI features

## New Features

### PRD Integration System

Focus Forge now includes a comprehensive PRD integration system that allows you to:

1. Parse PRDs with advanced features
2. Integrate with existing task management
3. Validate tasks against PRD requirements

For detailed documentation, see:
- [PRD Integration Guide](../docs/prd_integration.md)
- Main README section on [PRD Integration](../README.md#prd-integration-system)

### Usage Examples

```bash
# Parse PRD with advanced features
./focus_forge.py parse-prd-advanced --input=prd.md --advanced

# Integrate with existing tasks
./focus_forge.py integrate-prd --input=prd.md

# Validate tasks against PRD
./focus_forge.py validate-prd-tasks --prd=prd.md
```
