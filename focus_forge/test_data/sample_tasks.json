{"tasks": [{"id": "T1", "title": "Implement User Authentication", "description": "Create authentication system with login and registration", "status": "pending", "priority": "high", "dependencies": [], "details": "Implement a secure authentication system that allows users to register and log in with email and password.", "subtasks": [{"id": "T1.1", "title": "Create User Registration", "description": "Implement user registration with email and password validation", "status": "pending"}, {"id": "T1.2", "title": "Implement Login Functionality", "description": "Create login form and authentication process", "status": "pending"}]}, {"id": "T2", "title": "Develop Task Creation", "description": "Implement functionality for creating and editing tasks", "status": "pending", "priority": "high", "dependencies": ["T1"], "details": "Create a system for users to add new tasks with title, description, and due date.", "subtasks": []}, {"id": "T3", "title": "Create Task Status Management", "description": "Allow users to mark tasks as complete", "status": "pending", "priority": "medium", "dependencies": ["T2"], "details": "Implement functionality to update task status and mark tasks as complete.", "subtasks": []}, {"id": "T4", "title": "Implement Data Storage", "description": "Set up database for storing user and task data", "status": "pending", "priority": "high", "dependencies": [], "details": "Design and implement database schema for storing user accounts, tasks, and related data.", "subtasks": []}]}