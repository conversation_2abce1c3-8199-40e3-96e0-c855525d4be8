# Focus Forge User Guide

## Introduction

Focus Forge is a task management system designed specifically for development projects. It analyzes Product Requirements Documents (PRDs) to generate well-structured tasks with dependency relationships, allowing teams to efficiently plan and track development work.

This guide provides comprehensive documentation on using Focus Forge effectively, from basic commands to advanced features.

## Installation

### Requirements

- Python 3.10 or later
- Nix package manager (optional, for seamless environment setup)
- Git (for version control integration)

### Basic Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/focus_forge.git
cd focus_forge

# Setup with <PERSON> (recommended)
nix-shell

# Alternative: Manual setup
pip install -r requirements.txt
```

### Environment Setup

Create a `.env` file in the project root:

```
ANTHROPIC_API_KEY=your_api_key_here
MODEL=claude-3-7-sonnet-20250219
MAX_TOKENS=4000
TEMPERATURE=0.7
PROJECT_NAME=My Project
PROJECT_VERSION=0.1.0
```

## Architecture Overview

Focus Forge consists of several core components:

1. **Natural Language Relationship Detection Core**: Analyzes text to identify relationships between components.
2. **Component Detection and Grouping System**: Identifies logical components from PRD text.
3. **Dependency Graph Builder**: Creates a graph of task dependencies based on detected relationships.
4. **Development Sequence Analysis Algorithm**: Determines the optimal sequence for task execution.
5. **Priority and Complexity Inference Engine**: Infers task priorities and complexity levels from PRD text.
6. **Task Generation System**: Generates structured tasks from the analysis results.
7. **Validation and Completeness Checking**: Ensures tasks cover all requirements in the PRD.
8. **Task Management Integration**: Integrates with existing task management systems.

## Basic Usage

### Initializing a Project

```bash
./focus_forge.py init
```

This creates a basic configuration and prepares the environment for task management.

### Parsing a PRD and Generating Tasks

```bash
./focus_forge.py parse-prd --input=my_project_prd.md
```

This analyzes the PRD and generates a structured task list saved in `tasks.json`.

### Listing Tasks

```bash
./focus_forge.py list
```

This displays a table of all tasks with their IDs, titles, statuses, priorities, and dependencies.

### Showing Task Details

```bash
./focus_forge.py show T1
```

This displays detailed information about a specific task, including its description, implementation details, test strategy, and dependencies.

### Updating Task Status

```bash
./focus_forge.py set-status --id=T1 --status=in-progress
```

Valid status values are: `pending`, `in-progress`, `done`, and `deferred`.

### Finding the Next Task to Work On

```bash
./focus_forge.py next
```

This suggests the next task to work on based on dependencies, priority, and current status.

### Closing a Task and Moving to the Next

```bash
./focus_forge.py close-and-next --id=T1
```

This marks the specified task as done and suggests the next task to work on.

## Advanced Features

### Expanding Tasks into Subtasks

```bash
./focus_forge.py expand --id=T1 --num=5
```

This generates detailed subtasks for a high-level task.

### Dependency Management

```bash
# Add a dependency
./focus_forge.py add-dependency --id=T2 --depends-on=T1

# Remove a dependency
./focus_forge.py remove-dependency --id=T2 --depends-on=T1

# Validate dependencies
./focus_forge.py validate-dependencies

# Fix invalid dependencies
./focus_forge.py fix-dependencies
```

### Managing Subtasks

```bash
# Clear subtasks for a specific task
./focus_forge.py clear-subtasks --id=T1

# Clear subtasks for all tasks
./focus_forge.py clear-subtasks --all
```

### Advanced PRD Parsing

```bash
# Advanced parsing with configuration
./focus_forge.py parse-prd-advanced --input=my_prd.md --config-file=parse_config.json

# Integrating PRD with existing tasks
./focus_forge.py integrate-prd --input=additional_requirements.md

# Validating tasks against a PRD
./focus_forge.py validate-prd-tasks --prd=my_prd.md
```

### Task Format Upgrading

```bash
# Check upgrade path
./focus_forge.py check-upgrade-path --from=0.7.0 --to=0.8.0

# Upgrade tasks file
./focus_forge.py upgrade-tasks --version=0.8.0

# Auto-upgrade tasks
./focus_forge.py auto-upgrade-tasks
```

### Version Control Integration

Focus Forge is designed to work well with version control:

1. The `tasks.json` file can be committed to track task changes
2. Task IDs are stable across versions
3. Task history can be tracked through git history

## Customization

### Configuration Options

Focus Forge can be customized through the `.focus_forge_config` file:

```json
{
  "api_key": "your_api_key_here",
  "model": "claude-3-7-sonnet-20250219",
  "max_tokens": 4000,
  "temperature": 0.7,
  "project_name": "My Project",
  "project_version": "0.1.0",
  "default_subtasks": 3,
  "default_priority": "medium",
  "upgrader_behavior": "prompt"
}
```

### Custom Templates

Task templates can be customized by creating a `templates/task_templates.json` file:

```json
{
  "FEATURE": {
    "title": "Implement {feature_name} Feature",
    "description": "Develop the {feature_name} feature as specified in the PRD.",
    "details": "Create the {feature_name} feature with the following capabilities: {details}.",
    "test_strategy": "Create unit tests for all functionality.",
    "subtasks": [
      {
        "title": "Design {feature_name} architecture",
        "description": "Create the architectural design for {feature_name}."
      },
      {
        "title": "Implement {feature_name} core",
        "description": "Implement the core functionality of {feature_name}."
      },
      {
        "title": "Test {feature_name}",
        "description": "Create and run tests for {feature_name}."
      }
    ]
  }
}
```

## Performance Optimization

See [Performance Optimization Guide](performance_optimization.md) for detailed information on:

- Caching strategies
- Performance profiling
- Handling large PRDs
- Memory optimization
- Configuration tuning

## Troubleshooting

### Common Issues

1. **Missing Dependencies**: Ensure all required Python packages are installed.
2. **API Key Issues**: Verify your API key is correctly set in `.env` or `.focus_forge_config`.
3. **PRD Parsing Errors**: Check your PRD for very complex or ambiguous language.
4. **Duplicate Task IDs**: Run `validate-tasks` to identify and fix inconsistencies.

### Debugging

Enable debug logging for more detailed output:

```bash
export FOCUS_FORGE_DEBUG=true
./focus_forge.py parse-prd --input=my_prd.md
```

### Clearing Cache

If you encounter unexpected behavior, try clearing the cache:

```bash
rm -rf ~/.focus_forge/cache/*
```

## Use Cases and Examples

### Example 1: Small Project Setup

```bash
# Initialize project
./focus_forge.py init

# Generate tasks from PRD
./focus_forge.py parse-prd --input=small_project_prd.md

# Show the project plan
./focus_forge.py list

# Get the first task to work on
./focus_forge.py next
```

### Example 2: Integrating with Existing Tasks

```bash
# Initialize with existing tasks
./focus_forge.py init

# Create tasks.json manually or from another system

# Validate the existing tasks
./focus_forge.py validate-tasks

# Add new requirements
./focus_forge.py integrate-prd --input=new_features.md

# Check the updated task list
./focus_forge.py list
```

### Example 3: Team Workflow

```bash
# Initialize project (team lead)
./focus_forge.py init

# Generate initial tasks (team lead)
./focus_forge.py parse-prd --input=project_prd.md

# Expand high-level tasks (team lead)
./focus_forge.py expand --id=T1

# Assign and track tasks (team members)
./focus_forge.py set-status --id=T1.1 --status=in-progress
./focus_forge.py set-status --id=T1.1 --status=done

# Generate report (team lead)
./focus_forge.py list > task_status_report.txt
```

## Best Practices

1. **Keep PRDs Clear and Structured**: Well-organized PRDs produce better tasks.
2. **Use Consistent Terminology**: Consistent language improves relationship detection.
3. **Regularly Update Task Status**: Keep the task list current for accurate dependency tracking.
4. **Version Control Integration**: Commit `tasks.json` to track changes over time.
5. **Use Subtasks for Complex Work**: Break down complex tasks into manageable subtasks.
6. **Add Test Strategies**: Include test strategies for better quality assurance.
7. **Validate Before Integrating**: Run validation when integrating new requirements.
8. **Regular Backups**: Use the backup features to safeguard against data loss.

## Advanced Topics

### Custom Hooks

Focus Forge supports custom hooks for integrating with external systems:

```python
# custom_hooks.py
def on_task_status_change(task_id, old_status, new_status):
    # Integrate with external systems
    print(f"Task {task_id} changed from {old_status} to {new_status}")
```

To enable hooks, add the hook file path to your configuration:

```json
{
  "hooks": {
    "status_change": "path/to/custom_hooks.py:on_task_status_change"
  }
}
```

### Using Focus Forge Programmatically

You can import Focus Forge as a library in your Python code:

```python
from focus_forge import TaskGenerator, DependencyGraph

# Create a task generator
generator = TaskGenerator()

# Generate tasks from a PRD
with open("my_prd.md", "r") as f:
    prd_text = f.read()

tasks = generator.generate_tasks_from_prd(prd_text)

# Build a dependency graph
graph = DependencyGraph()
for task in tasks:
    graph.add_node(task["id"])

# Add dependencies
for task in tasks:
    for dep_id in task["dependencies"]:
        graph.add_dependency(task["id"], dep_id)
```

## Contributing to Focus Forge

We welcome contributions to Focus Forge! See [CONTRIBUTING.md](../CONTRIBUTING.md) for details on:

- Setting up a development environment
- Code style guidelines
- Testing requirements
- Pull request process
- Feature request guidelines

## Appendix

### Command Reference

A complete list of commands and their options:

| Command | Description | Options |
|---------|-------------|---------|
| `init` | Initialize a new project | `--config`: Path to config file |
| `list` | List all tasks | |
| `show` | Show task details | `<task_id>`: ID of the task to show |
| `next` | Show next task | |
| `set-status` | Set task status | `--id`: Task ID, `--status`: New status |
| `parse-prd` | Parse PRD and generate tasks | `--input`: PRD file path |
| `expand` | Expand task into subtasks | `--id`: Task ID, `--num`: Number of subtasks |
| `clear-subtasks` | Clear subtasks | `--id`: Task ID or `--all` |
| `add-dependency` | Add dependency | `--id`: Task ID, `--depends-on`: Dependency ID |
| `remove-dependency` | Remove dependency | `--id`: Task ID, `--depends-on`: Dependency ID |
| `close-and-next` | Close task and show next | `--id`: Task ID to close |

### Glossary

| Term | Definition |
|------|------------|
| PRD | Product Requirements Document, a detailed specification of product features |
| Task | A unit of work to be completed, with a specific goal and acceptance criteria |
| Dependency | A relationship where one task depends on the completion of another |
| Subtask | A smaller unit of work that is part of a larger task |
| Priority | The relative importance of a task in the project |
| Status | The current state of a task: pending, in-progress, done, or deferred |

### File Format Reference

The structure of the `tasks.json` file:

```json
{
  "version": "0.8.0",
  "updated_at": "2023-07-10T15:30:45.123456",
  "tasks": [
    {
      "id": "T1",
      "title": "Task Title",
      "description": "Task description",
      "status": "pending",
      "priority": "high",
      "dependencies": ["T2", "T3"],
      "details": "Detailed implementation notes",
      "test_strategy": "Testing approach",
      "subtasks": [
        {
          "id": "T1.1",
          "title": "Subtask Title",
          "description": "Subtask description",
          "status": "pending"
        }
      ]
    }
  ]
} 