# First Time User Guide: Integrating Focus Forge as a Submodule

This guide will walk you through the process of integrating Focus Forge into your existing project as a Git submodule, allowing you to keep Focus Forge updated independently while using it to manage your project's tasks.

## 1. Adding Focus Forge as a Git Submodule

```mermaid
flowchart LR
    A[Your Project Repository] -->|add submodule| B[focus_forge Submodule]
    B -->|fetch updates| C[focus_forge Remote Repository]
    A -->|commit changes| D[Your Project Remote Repository]
```

### Step-by-Step Instructions

1. Navigate to your project's root directory:
   ```bash
   cd /path/to/your/project
   ```

2. Add Focus Forge as a submodule:
   ```bash
   git submodule add https://github.com/forkrul/focus_forge.git
   git submodule init
   git submodule update
   ```

3. Commit the submodule addition:
   ```bash
   git add .gitmodules focus_forge
   git commit -m "Add Focus Forge as a submodule for task management"
   ```

## 2. Setting Up Task Files Structure

Focus Forge manages task files outside of its own directory, in your main project directory:

```mermaid
flowchart TD
    A[Your Project Root] --> B[focus_forge/]
    A --> C[tasks.json]
    A --> D[tasks/]
    A --> E[.focus_forge_config]
    A --> F[.upgrade_history.json]
    
    D --> G[task_001.txt]
    D --> H[task_002.txt]
    D --> I[...]
    
    B --> J[Python modules]
```

### File Structure Explanation

- **tasks.json**: The main task data file (stored in your project root)
- **tasks/**: Directory containing individual task files (one per task)
- **.focus_forge_config**: Optional configuration file
- **.upgrade_history.json**: Task format upgrade history (created when upgrading)

## 3. Initializing Focus Forge in Your Project

```mermaid
flowchart TD
    A[Initialize] --> B[Create config and tasks.json] 
    B --> C[Set environment variables]
    C --> D[Create initial tasks]
    D --> E{Add to version control?}
    E -->|Yes| F[Add to git]
    E -->|No| G[Add to .gitignore]
```

### Initialization Steps

1. Initialize Focus Forge in your project root:
   ```bash
   # Navigate to your project
   cd /path/to/your/project
   
   # Run Focus Forge init from the submodule
   ./focus_forge/focus_forge.py init
   ```

2. Configure Focus Forge for your project (optional):
   ```bash
   # Edit the config file
   nano .focus_forge_config
   
   # Or set environment variables
   export PROJECT_NAME="Your Project Name"
   export UPGRADER_BEHAVIOR="auto"  # Options: prompt, auto, never
   ```

3. Version control decisions:
   ```bash
   # Option 1: Add task files to version control
   git add tasks.json tasks/ .focus_forge_config
   git commit -m "Add Focus Forge task management files"
   
   # Option 2: Exclude task files from version control
   echo "tasks.json" >> .gitignore
   echo "tasks/" >> .gitignore
   echo ".focus_forge_config" >> .gitignore
   echo ".upgrade_history.json" >> .gitignore
   git add .gitignore
   git commit -m "Exclude Focus Forge task files from version control"
   ```

## 4. Using Focus Forge Commands

All Focus Forge commands can be run from your project root directory by referencing the executable in the submodule:

```mermaid
flowchart LR
    A[Project Root] -->|use commands| B["./focus_forge/focus_forge.py [command]"]
    B -->|Common Commands| C["init
    list
    next
    parse-prd
    set-status"]
```

### Basic Usage Examples

```bash
# List all tasks
./focus_forge/focus_forge.py list

# Get the next task to work on
./focus_forge/focus_forge.py next

# Update task status
./focus_forge/focus_forge.py set-status --id=T1 --status=in-progress

# Parse a PRD document and generate tasks
./focus_forge/focus_forge.py parse-prd --input=docs/requirements.md
```

### Creating a Convenience Alias

To avoid typing the long path each time, set up an alias:

```bash
# In .bashrc or .zshrc
echo 'alias ff="./focus_forge/focus_forge.py"' >> ~/.bashrc
source ~/.bashrc

# Now use the shorter command
ff list
ff next
```

## 5. Updating Focus Forge

```mermaid
flowchart TD
    A[Update Focus Forge] --> B[Pull latest changes]
    B --> C{Version changed?}
    C -->|Yes| D[Check task format]
    C -->|No| E[Continue using]
    D -->|Needs upgrade| F[Upgrade tasks]
    D -->|Compatible| E
```

### Update Process

1. Update the Focus Forge submodule:
   ```bash
   # Navigate to your project
   cd /path/to/your/project
   
   # Update the submodule
   git submodule update --remote focus_forge
   
   # Commit the update
   git add focus_forge
   git commit -m "Update Focus Forge submodule"
   ```

2. Check if tasks need upgrading:
   ```bash
   # Detect current version
   ./focus_forge/focus_forge.py detect-version
   
   # If you need to upgrade tasks
   ./focus_forge/focus_forge.py upgrade-tasks
   ```

## 6. Key Task Management Workflows

```mermaid
flowchart TD
    A[Focus Forge Workflow] --> B[Importing Tasks]
    A --> C[Daily Task Management]
    A --> D[Task Reporting]
    
    B --> B1[parse-prd]
    B --> B2[integrate-prd]
    
    C --> C1[next]
    C --> C2[set-status]
    C --> C3[list]
    
    D --> D1[generate]
    D --> D2[sequence]
```

### Common Workflows

1. **Importing from Requirements:**
   ```bash
   # Generate initial tasks from PRD
   ./focus_forge/focus_forge.py parse-prd --input=docs/requirements.md
   
   # Later, integrate changes from updated PRD
   ./focus_forge/focus_forge.py integrate-prd --input=docs/requirements.md
   ```

2. **Daily Task Management:**
   ```bash
   # Start your day by finding what to work on
   ./focus_forge/focus_forge.py next
   
   # Mark a task as in-progress
   ./focus_forge/focus_forge.py set-status --id=T3 --status=in-progress
   
   # Complete a task
   ./focus_forge/focus_forge.py set-status --id=T3 --status=done
   ```

3. **Task Reporting and Visualization:**
   ```bash
   # Generate task files for documentation
   ./focus_forge/focus_forge.py generate
   
   # View dependency graph
   ./focus_forge/focus_forge.py dependency-graph --output=graph.dot
   
   # Find optimal implementation sequence
   ./focus_forge/focus_forge.py sequence
   ```

## 7. Task Format Version Management

As your project evolves alongside Focus Forge, you may need to deal with task format version changes:

```mermaid
flowchart LR
    A[Task Format] --> B{Auto-upgrade enabled?}
    B -->|Yes| C[Automatic upgrades]
    B -->|No| D[Manual management]
    
    C --> E[Backups created]
    C --> F[Version history recorded]
    
    D --> G[Upgrade when needed]
    D --> H[Manage backups]
    
    G --> I[upgrade-tasks]
    H --> J[list-backups]
    H --> K[restore-backup]
```

### Version Management Options

1. **Configure automatic upgrades:**
   In your `.focus_forge_config` file, set:
   ```json
   {
     "upgrader_behavior": "auto"
   }
   ```

2. **Manually manage versions:**
   ```bash
   # Check current version
   ./focus_forge/focus_forge.py detect-version
   
   # Upgrade tasks when needed
   ./focus_forge/focus_forge.py upgrade-tasks
   
   # View upgrade history
   ./focus_forge/focus_forge.py upgrade-history
   
   # List available backups
   ./focus_forge/focus_forge.py list-backups
   
   # Restore from a backup if needed
   ./focus_forge/focus_forge.py restore-backup path/to/backup
   ```

## Conclusion

By integrating Focus Forge as a Git submodule, you gain powerful task management capabilities that evolve independently from your main project. The task files are stored in your project's root directory while the core functionality remains separate, making it easy to update Focus Forge without impacting your tasks or project-specific configurations.

Remember that all task data is stored outside the Focus Forge directory in your project's main folder, ensuring your task information stays with your project even as the Focus Forge submodule is updated. With the upgrader system, you can confidently keep Focus Forge updated knowing your task data will remain compatible. 