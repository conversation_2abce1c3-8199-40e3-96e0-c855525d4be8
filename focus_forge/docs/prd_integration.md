# PRD Integration System

This document details the integration system that connects the PRD parser with the existing task management functionality in Focus Forge.

## Overview

The PRD Integration System provides a seamless bridge between product requirement documents (PRDs) and the task management infrastructure in Focus Forge. It allows you to:

1. Parse PRDs and generate high-quality tasks with appropriate dependencies
2. Merge these tasks with your existing task management system
3. Validate the tasks against PRD requirements to ensure complete coverage and alignment

## Architecture

The integration system consists of three main components:

1. **TaskManagementAdapter**: Core adapter class that provides the integration between PRD parsing and task management
2. **Integration Functions**: High-level functions for parsing, integration, and validation
3. **CLI Integration**: Command-line interface for easy access to the integration capabilities

```
+----------------+      +---------------------+      +---------------+
| PRD Parser     |----->| TaskManagementAdapter|----->| Task System   |
| (Input)        |      | (Integration Layer)  |      | (Management)  |
+----------------+      +---------------------+      +---------------+
        |                          |                         |
        v                          v                         v
+----------------+      +---------------------+      +---------------+
| Task Generation |----->| Merge & Validation  |----->| Task Storage  |
| (Processing)    |      | (Quality Assurance) |      | (Persistence) |
+----------------+      +---------------------+      +---------------+
```

## Command-Line Interface

### Parse PRD with Advanced Features

Parse a PRD file and generate tasks with enhanced capabilities:

```bash
./focus_forge.py parse-prd-advanced --input=prd.md --advanced --output=tasks.json
```

Options:
- `--input`: Path to the PRD file (required)
- `--advanced`: Enable advanced parsing features (recommended)
- `--output`: Path to save generated tasks (default: generated_tasks.json)
- `--config-file`: Path to custom configuration file

After parsing, you'll be prompted whether to import the tasks into the system.

### Integrate PRD with Existing Tasks

Merge tasks generated from a PRD with your existing task management system:

```bash
./focus_forge.py integrate-prd --input=prd.md --verbose
```

Options:
- `--input`: Path to the PRD file (required)
- `--output`: Path to save merged tasks (optional, if not specified, you'll be prompted to update the system)
- `--config-file`: Path to custom configuration file
- `--verbose`: Print detailed integration report

### Validate Tasks against PRD

Validate your tasks to ensure they properly cover and align with PRD requirements:

```bash
./focus_forge.py validate-prd-tasks --prd=prd.md --output=report.json
```

Options:
- `--prd`: Path to the PRD file (required)
- `--output`: Path to save validation report (optional)
- `--config-file`: Path to custom configuration file
- `--verbose`: Print detailed validation report
- `--fail`: Exit with error code if validation fails (useful for CI/CD pipelines)

## Configuration

You can customize the integration behavior using a configuration file. Create a JSON file with the following structure:

```json
{
  "task_generation": {
    "default_priority": "medium",
    "generate_subtasks": true,
    "max_subtasks_per_task": 3
  },
  "task_validation": {
    "validate_dependencies": true,
    "validate_coverage": true,
    "require_test_strategy": true
  },
  "confidence_scoring": {
    "scoring_weights": {
      "clarity": 0.3,
      "coverage": 0.5,
      "complexity": 0.2
    }
  }
}
```

## Advanced Usage

### Programmatic Integration

You can use the integration functions directly in your Python code:

```python
from focus_forge.task_management_integration import (
    parse_prd_to_tasks,
    integrate_prd_with_existing,
    validate_tasks_with_prd
)

# Parse PRD and generate tasks
tasks = parse_prd_to_tasks("prd.md")

# Integrate with existing tasks
report = integrate_prd_with_existing("prd.md", "tasks.json", "output.json")

# Validate tasks against PRD
validation = validate_tasks_with_prd("tasks.json", "prd.md")
```

### Custom Adapters

You can extend the `TaskManagementAdapter` class for custom integration needs:

```python
from focus_forge.task_management_integration import TaskManagementAdapter

class CustomAdapter(TaskManagementAdapter):
    def __init__(self, config=None):
        super().__init__(config)
        # Custom initialization
    
    def parse_prd_file(self, prd_file):
        # Custom parsing logic
        return super().parse_prd_file(prd_file)
```

## Validation Reports

The validation system provides detailed reports on task quality and PRD alignment:

- **Task Validity**: Checks for schema conformance and dependency consistency
- **Confidence Score**: Overall quality score based on multiple factors
- **Clarity Score**: How clear and unambiguous the tasks are
- **Coverage Score**: How well the tasks cover the PRD requirements
- **Ambiguous Requirements**: Identifies unclear or ambiguous requirements in PRD
- **Missing Requirements**: Detects potentially missing requirements in tasks

Example validation report:

```json
{
  "tasks_valid": true,
  "confidence_score": 0.85,
  "clarity_score": 0.9,
  "coverage_score": 0.8,
  "ambiguous_requirements": {
    "count": 1,
    "items": [
      {
        "requirement": "Should have error handling",
        "severity": "medium",
        "ambiguous_phrases": ["should"]
      }
    ]
  },
  "missing_requirements": {
    "count": 0,
    "items": []
  }
}
```

## Best Practices

1. **Use Advanced Mode**: Always use the `--advanced` flag with `parse-prd-advanced` for best results
2. **Review Integration Reports**: Check detailed reports with `--verbose` to identify potential issues
3. **Version Control PRDs**: Keep your PRDs under version control alongside tasks
4. **Regular Validation**: Run `validate-prd-tasks` regularly to ensure tasks stay aligned with PRD
5. **Use in CI/CD**: Add validation with the `--fail` flag to your CI/CD pipeline
6. **Custom Configuration**: Create specific configurations for different project types

## Troubleshooting

- **Missing Module Error**: If you get "Module not available" errors, ensure the focus_forge package is installed
- **Import Errors**: Check your Python path includes the focus_forge directory
- **Validation Failures**: Use `--verbose` to get detailed information about validation issues
- **Merge Conflicts**: If you encounter ID conflicts during merging, the system will automatically assign new IDs 