# Focus Forge Performance Optimization

This document provides guidance on optimizing the performance of Focus Forge, particularly when dealing with large PRDs and complex dependency graphs.

## Performance Requirements

Focus Forge is designed to meet the following performance goals:

- Process a 50-page PRD in under 60 seconds
- Support concurrent task generation without significant slowdown
- Minimize memory usage for large dependency graphs
- Provide responsive CLI experience even with large task collections

## Identifying Performance Bottlenecks

The primary performance bottlenecks in Focus Forge are:

1. **NLP Processing**: The relationship detection and component identification processes involve computationally intensive natural language processing.
2. **Dependency Graph Operations**: Operations like cycle detection, topological sorting, and path finding can become expensive with large graphs.
3. **Task Generation**: Generating tasks from PRDs involves multiple stages that can be performance-intensive.
4. **File Operations**: Reading and writing large JSON files can impact performance.

You can profile your specific usage patterns using the `performance_profiler.py` script:

```bash
# Generate and profile a 500KB PRD
python performance_profiler.py --generate-size=500 --output=profile_results.json

# Profile an existing PRD
python performance_profiler.py --prd=my_project_prd.md --output=profile_results.json
```

## Caching Framework

Focus Forge now includes a comprehensive caching framework in `focus_forge/caching.py` that provides several levels of caching:

1. **In-Memory Caching**: Fast, but volatile; great for repeated operations within the same session
2. **Disk Caching**: Persistent across sessions; good for PRD parsing and task generation results
3. **Composite Caching**: Combines both approaches for optimal performance

### Using the Caching Framework

To use the caching framework in your functions:

```python
from focus_forge.caching import memoize, persistent_cache

# Simple in-memory caching
@memoize
def expensive_calculation(input_data):
    # Complex calculation here
    return result

# Persistent caching with custom prefix
@persistent_cache("component_detection")
def detect_components(text):
    # Complex component detection logic
    return components
```

## Performance Optimizations Implemented

### 1. Task Generation Optimizations

- **Section Caching**: PRD sections are cached to avoid redundant parsing
- **Component Detection Caching**: Results of component detection are cached
- **Relationship Detection Memoization**: Common patterns in relationship detection are memoized
- **Partial Results Storage**: Intermediate results are stored to enable incremental processing

### 2. Dependency Graph Optimizations

- **Lazy Evaluation**: Some graph operations are evaluated lazily
- **Optimized Algorithms**: Graph traversal algorithms have been optimized for performance
- **Edge Index**: An edge index is maintained for faster lookups
- **Memory-Efficient Storage**: Graph representation is optimized for memory usage

### 3. I/O Optimizations

- **Buffered Reading**: Large files are read in chunks to minimize memory usage
- **Incremental Updates**: Tasks file is updated incrementally when possible
- **Compressed Storage**: Large intermediate results can be compressed

## Best Practices for Performance

Follow these best practices to ensure optimal performance:

1. **Break Down Large PRDs**: Split very large PRDs into logical sections and process them separately
2. **Use Incremental Processing**: Add to existing task files rather than regenerating them
3. **Leverage Caching**: Enable caching for repeat operations
4. **Profile Your Workflows**: Use the profiler to identify bottlenecks in your specific use cases
5. **Optimize Memory Usage**: Close large files after reading, use generators where appropriate
6. **Pre-process PRDs**: Remove unnecessary formatting and large images before processing

## Advanced Configuration for Performance

The following configuration options can be used to tune performance:

```bash
# Set environment variables for performance tuning
export FOCUS_FORGE_CACHE_SIZE=200  # Increase memory cache size
export FOCUS_FORGE_CACHE_TTL=7200  # Increase cache time-to-live (seconds)
export FOCUS_FORGE_PARALLEL=true   # Enable parallel processing where supported
export FOCUS_FORGE_COMPRESSION=true # Enable compression for large data
```

Or in `.focus_forge_config`:

```json
{
  "performance": {
    "cache_size": 200,
    "cache_ttl": 7200,
    "enable_parallel": true,
    "compression": true,
    "max_memory_mb": 1024
  }
}
```

## Performance Monitoring

Focus Forge can log performance metrics to help you identify issues:

```bash
# Enable performance logging
export FOCUS_FORGE_PERF_LOG=true
export FOCUS_FORGE_PERF_LOG_PATH="~/.focus_forge/logs/performance.log"

# Run with performance metrics
./focus_forge.py parse-prd --input=large_prd.md --perf-log
```

## Troubleshooting Performance Issues

### High Memory Usage

If Focus Forge is using excessive memory:

1. Check the size of your PRD files
2. Reduce the `max_memory_mb` setting
3. Process PRDs in smaller chunks
4. Enable compression for large data

### Slow PRD Processing

If PRD processing is slow:

1. Check for complex or repetitive language in your PRD
2. Ensure caching is enabled
3. Pre-process the PRD to simplify structure
4. Use the profiler to identify specific bottlenecks

### Slow Dependency Graph Operations

If dependency operations are slow:

1. Check for large dependency cycles
2. Reduce the complexity of dependencies
3. Use topological sorting to identify dependency chains
4. Break down monolithic tasks into smaller sub-tasks

## Future Performance Improvements

The following performance improvements are planned for future versions:

1. **Parallel Processing**: Multi-threaded processing for independent sections
2. **GPU Acceleration**: Optional GPU acceleration for NLP operations
3. **Incremental Parsing**: Updating only changed sections of PRDs
4. **Distributed Processing**: Distributed processing for very large PRDs
5. **Adaptive Caching**: Dynamic cache sizing based on workload

## Reporting Performance Issues

If you encounter performance issues not addressed by this guide, please report them with:

1. The output from `performance_profiler.py`
2. The size and complexity of your PRD
3. Your hardware specifications
4. Steps to reproduce the performance issue 