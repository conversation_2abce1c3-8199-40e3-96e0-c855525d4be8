#!/usr/bin/env python3
"""
Database Migration Script: SQLite to PostgreSQL
Migrates CertRats data from SQLite to PostgreSQL with validation
"""

import os
import sys
import sqlite3
import psycopg2
import logging
from datetime import datetime
from typing import Dict, List, Any
import json

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from models.certification_explorer import CertificationExplorer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DatabaseMigrator:
    """Handles migration from SQLite to PostgreSQL"""
    
    def __init__(self, sqlite_path: str, postgres_url: str):
        self.sqlite_path = sqlite_path
        self.postgres_url = postgres_url
        self.migration_stats = {
            'tables_migrated': 0,
            'records_migrated': 0,
            'errors': 0,
            'start_time': None,
            'end_time': None
        }
    
    def connect_sqlite(self):
        """Connect to SQLite database"""
        try:
            conn = sqlite3.connect(self.sqlite_path)
            conn.row_factory = sqlite3.Row  # Enable column access by name
            logger.info(f"Connected to SQLite database: {self.sqlite_path}")
            return conn
        except Exception as e:
            logger.error(f"Failed to connect to SQLite: {e}")
            raise
    
    def connect_postgresql(self):
        """Connect to PostgreSQL database"""
        try:
            engine = create_engine(self.postgres_url)
            Session = sessionmaker(bind=engine)
            session = Session()
            logger.info(f"Connected to PostgreSQL database")
            return engine, session
        except Exception as e:
            logger.error(f"Failed to connect to PostgreSQL: {e}")
            raise
    
    def validate_sqlite_data(self, sqlite_conn):
        """Validate SQLite data before migration"""
        logger.info("Validating SQLite data...")
        
        cursor = sqlite_conn.cursor()
        
        # Check if certification_explorer table exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='certification_explorer'
        """)
        
        if not cursor.fetchone():
            logger.error("certification_explorer table not found in SQLite database")
            return False
        
        # Count records
        cursor.execute("SELECT COUNT(*) FROM certification_explorer")
        record_count = cursor.fetchone()[0]
        logger.info(f"Found {record_count} records in certification_explorer table")
        
        if record_count == 0:
            logger.warning("No records found in certification_explorer table")
            return False
        
        # Check for required columns
        cursor.execute("PRAGMA table_info(certification_explorer)")
        columns = [row[1] for row in cursor.fetchall()]
        required_columns = ['id', 'name', 'provider', 'category', 'difficulty']
        
        missing_columns = [col for col in required_columns if col not in columns]
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            return False
        
        logger.info("SQLite data validation completed successfully")
        return True
    
    def create_postgresql_tables(self, engine):
        """Create PostgreSQL tables using SQLAlchemy models"""
        logger.info("Creating PostgreSQL tables...")
        
        try:
            # Import all models to ensure they're registered
            from database import Base
            from models.certification_explorer import CertificationExplorer
            from models.user import User
            from models.certification import Certification, Organization
            from models.reports import Report
            
            # Create all tables
            Base.metadata.create_all(bind=engine)
            logger.info("PostgreSQL tables created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create PostgreSQL tables: {e}")
            return False
    
    def migrate_certification_explorer(self, sqlite_conn, pg_session):
        """Migrate certification_explorer table"""
        logger.info("Migrating certification_explorer table...")
        
        cursor = sqlite_conn.cursor()
        cursor.execute("SELECT * FROM certification_explorer")
        
        migrated_count = 0
        error_count = 0
        
        for row in cursor.fetchall():
            try:
                # Create CertificationExplorer object
                cert = CertificationExplorer(
                    name=row['name'],
                    provider=row['provider'] if row['provider'] else 'Unknown',
                    category=row['category'] if row['category'] else 'General',
                    difficulty=row['difficulty'] if row['difficulty'] else 'Unknown',
                    cost=row['cost'] if row['cost'] else 0,
                    duration=row['duration'],
                    description=row['description'],
                    prerequisites=row['prerequisites'],
                    career_paths=row['career_paths'],
                    exam_details=row['exam_details'],
                    renewal_requirements=row['renewal_requirements']
                )
                
                pg_session.add(cert)
                migrated_count += 1
                
                # Commit in batches of 100
                if migrated_count % 100 == 0:
                    pg_session.commit()
                    logger.info(f"Migrated {migrated_count} certification records...")
                
            except Exception as e:
                error_count += 1
                logger.error(f"Failed to migrate record {row['id']}: {e}")
                pg_session.rollback()
        
        # Final commit
        try:
            pg_session.commit()
            logger.info(f"Successfully migrated {migrated_count} certification records")
            
            if error_count > 0:
                logger.warning(f"Encountered {error_count} errors during migration")
            
            return migrated_count, error_count
            
        except Exception as e:
            logger.error(f"Failed to commit final batch: {e}")
            pg_session.rollback()
            return migrated_count, error_count + 1
    
    def validate_migration(self, sqlite_conn, pg_session):
        """Validate that migration was successful"""
        logger.info("Validating migration...")
        
        # Count records in SQLite
        cursor = sqlite_conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM certification_explorer")
        sqlite_count = cursor.fetchone()[0]
        
        # Count records in PostgreSQL
        pg_count = pg_session.query(CertificationExplorer).count()
        
        logger.info(f"SQLite records: {sqlite_count}")
        logger.info(f"PostgreSQL records: {pg_count}")
        
        if sqlite_count == pg_count:
            logger.info("✅ Migration validation successful - record counts match")
            return True
        else:
            logger.error(f"❌ Migration validation failed - record count mismatch")
            return False
    
    def generate_migration_report(self):
        """Generate migration report"""
        duration = None
        if self.migration_stats['start_time'] and self.migration_stats['end_time']:
            duration = self.migration_stats['end_time'] - self.migration_stats['start_time']
        
        report = {
            'migration_timestamp': datetime.now().isoformat(),
            'source_database': self.sqlite_path,
            'target_database': self.postgres_url.split('@')[-1],  # Hide credentials
            'duration_seconds': duration.total_seconds() if duration else None,
            'statistics': self.migration_stats
        }
        
        # Save report to file
        with open('migration_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info("Migration report saved to migration_report.json")
        return report
    
    def run_migration(self):
        """Run the complete migration process"""
        logger.info("🚀 Starting database migration from SQLite to PostgreSQL")
        self.migration_stats['start_time'] = datetime.now()
        
        try:
            # Connect to databases
            sqlite_conn = self.connect_sqlite()
            pg_engine, pg_session = self.connect_postgresql()
            
            # Validate source data
            if not self.validate_sqlite_data(sqlite_conn):
                logger.error("SQLite data validation failed. Aborting migration.")
                return False
            
            # Create PostgreSQL tables
            if not self.create_postgresql_tables(pg_engine):
                logger.error("Failed to create PostgreSQL tables. Aborting migration.")
                return False
            
            # Migrate data
            migrated, errors = self.migrate_certification_explorer(sqlite_conn, pg_session)
            self.migration_stats['records_migrated'] = migrated
            self.migration_stats['errors'] = errors
            self.migration_stats['tables_migrated'] = 1
            
            # Validate migration
            if not self.validate_migration(sqlite_conn, pg_session):
                logger.error("Migration validation failed.")
                return False
            
            self.migration_stats['end_time'] = datetime.now()
            
            # Generate report
            report = self.generate_migration_report()
            
            logger.info("🎉 Database migration completed successfully!")
            logger.info(f"📊 Migrated {migrated} records in {report['duration_seconds']:.2f} seconds")
            
            return True
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            self.migration_stats['end_time'] = datetime.now()
            return False
        
        finally:
            # Close connections
            if 'sqlite_conn' in locals():
                sqlite_conn.close()
            if 'pg_session' in locals():
                pg_session.close()


def main():
    """Main migration function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Migrate CertRats database from SQLite to PostgreSQL')
    parser.add_argument('--sqlite', default='certrats.db', help='SQLite database path')
    parser.add_argument('--postgres', required=True, help='PostgreSQL connection URL')
    parser.add_argument('--dry-run', action='store_true', help='Validate only, do not migrate')
    
    args = parser.parse_args()
    
    # Validate arguments
    if not os.path.exists(args.sqlite):
        logger.error(f"SQLite database not found: {args.sqlite}")
        return 1
    
    if args.dry_run:
        logger.info("🔍 Running in dry-run mode (validation only)")
        
        try:
            migrator = DatabaseMigrator(args.sqlite, args.postgres)
            sqlite_conn = migrator.connect_sqlite()
            
            if migrator.validate_sqlite_data(sqlite_conn):
                logger.info("✅ Dry-run completed successfully - data is ready for migration")
                return 0
            else:
                logger.error("❌ Dry-run failed - data validation errors found")
                return 1
                
        except Exception as e:
            logger.error(f"Dry-run failed: {e}")
            return 1
    else:
        # Run actual migration
        migrator = DatabaseMigrator(args.sqlite, args.postgres)
        
        if migrator.run_migration():
            logger.info("✅ Migration completed successfully")
            return 0
        else:
            logger.error("❌ Migration failed")
            return 1


if __name__ == "__main__":
    sys.exit(main())
