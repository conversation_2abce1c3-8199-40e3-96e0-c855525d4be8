// CertRats Load Testing Script using k6
// Comprehensive load testing for production readiness validation

import http from 'k6/http';
import { check, sleep, group } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('error_rate');
const responseTime = new Trend('response_time');
const apiCalls = new Counter('api_calls');

// Test configuration
export const options = {
  stages: [
    // Ramp-up
    { duration: '2m', target: 10 },   // Ramp up to 10 users over 2 minutes
    { duration: '5m', target: 50 },   // Ramp up to 50 users over 5 minutes
    { duration: '10m', target: 100 }, // Ramp up to 100 users over 10 minutes
    
    // Steady state
    { duration: '15m', target: 100 }, // Stay at 100 users for 15 minutes
    { duration: '10m', target: 200 }, // Spike to 200 users for 10 minutes
    { duration: '5m', target: 100 },  // Drop back to 100 users
    
    // Ramp-down
    { duration: '5m', target: 50 },   // Ramp down to 50 users
    { duration: '2m', target: 0 },    // Ramp down to 0 users
  ],
  
  thresholds: {
    // Performance thresholds
    http_req_duration: ['p(95)<2000'], // 95% of requests must complete below 2s
    http_req_failed: ['rate<0.05'],    // Error rate must be below 5%
    error_rate: ['rate<0.05'],         // Custom error rate below 5%
    
    // API-specific thresholds
    'http_req_duration{endpoint:health}': ['p(95)<500'],
    'http_req_duration{endpoint:certifications}': ['p(95)<1000'],
    'http_req_duration{endpoint:career_path}': ['p(95)<5000'], // AI endpoint can be slower
    
    // Concurrent user thresholds
    http_reqs: ['rate>50'],            // Minimum 50 requests per second
  },
  
  // Test environment
  ext: {
    loadimpact: {
      distribution: {
        'amazon:us:ashburn': { loadZone: 'amazon:us:ashburn', percent: 50 },
        'amazon:ie:dublin': { loadZone: 'amazon:ie:dublin', percent: 25 },
        'amazon:sg:singapore': { loadZone: 'amazon:sg:singapore', percent: 25 },
      },
    },
  },
};

// Test configuration
const BASE_URL = __ENV.BASE_URL || 'http://localhost:8000';
const API_BASE = `${BASE_URL}/api/v1`;

// Test data
const testUsers = [
  {
    current_role: 'IT Support Specialist',
    target_role: 'Security Analyst',
    years_experience: 2,
    current_skills: ['Windows Administration', 'Network Basics'],
    interests: ['Cybersecurity', 'Incident Response'],
    budget: 2000,
    timeline: '12 months'
  },
  {
    current_role: 'Network Administrator',
    target_role: 'Security Engineer',
    years_experience: 5,
    current_skills: ['Networking', 'Firewalls', 'VPN'],
    interests: ['Network Security', 'Cloud Security'],
    budget: 3000,
    timeline: '18 months'
  },
  {
    current_role: 'System Administrator',
    target_role: 'Penetration Tester',
    years_experience: 3,
    current_skills: ['Linux', 'Scripting', 'System Security'],
    interests: ['Ethical Hacking', 'Vulnerability Assessment'],
    budget: 2500,
    timeline: '15 months'
  }
];

// Helper functions
function randomChoice(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function logResponse(response, endpoint) {
  const success = check(response, {
    [`${endpoint}: status is 200`]: (r) => r.status === 200,
    [`${endpoint}: response time < 5s`]: (r) => r.timings.duration < 5000,
  });
  
  errorRate.add(!success);
  responseTime.add(response.timings.duration);
  apiCalls.add(1);
  
  return success;
}

// Test scenarios
export default function () {
  // Simulate user session
  group('User Session', function () {
    
    // 1. Health Check
    group('Health Check', function () {
      const response = http.get(`${API_BASE}/health`, {
        tags: { endpoint: 'health' },
      });
      
      logResponse(response, 'Health Check');
    });
    
    sleep(1);
    
    // 2. Browse Certifications
    group('Browse Certifications', function () {
      const response = http.get(`${API_BASE}/certifications`, {
        tags: { endpoint: 'certifications' },
      });
      
      const success = logResponse(response, 'Browse Certifications');
      
      if (success) {
        const certifications = JSON.parse(response.body);
        check(certifications, {
          'certifications: has data': (certs) => Array.isArray(certs) && certs.length > 0,
          'certifications: has required fields': (certs) => 
            certs.every(cert => cert.name && cert.provider && cert.category),
        });
      }
    });
    
    sleep(2);
    
    // 3. Search Certifications
    group('Search Certifications', function () {
      const searchTerms = ['Security', 'Network', 'Cloud', 'CompTIA', 'CISSP'];
      const searchTerm = randomChoice(searchTerms);
      
      const response = http.get(`${API_BASE}/certifications/search?q=${searchTerm}`, {
        tags: { endpoint: 'search' },
      });
      
      const success = logResponse(response, 'Search Certifications');
      
      if (success) {
        const results = JSON.parse(response.body);
        check(results, {
          'search: returns results': (res) => Array.isArray(res),
          'search: relevant results': (res) => 
            res.length === 0 || res.some(cert => 
              cert.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
              cert.category.toLowerCase().includes(searchTerm.toLowerCase())
            ),
        });
      }
    });
    
    sleep(3);
    
    // 4. Generate Career Path (Heavy Operation)
    group('Generate Career Path', function () {
      const userData = randomChoice(testUsers);
      
      const response = http.post(`${API_BASE}/career-path`, JSON.stringify(userData), {
        headers: { 'Content-Type': 'application/json' },
        tags: { endpoint: 'career_path' },
        timeout: '30s', // Longer timeout for AI processing
      });
      
      const success = logResponse(response, 'Generate Career Path');
      
      if (success) {
        const careerPath = JSON.parse(response.body);
        check(careerPath, {
          'career_path: has stages': (cp) => Array.isArray(cp.stages) && cp.stages.length > 0,
          'career_path: has total_cost': (cp) => typeof cp.total_cost === 'number' && cp.total_cost > 0,
          'career_path: has alignment_score': (cp) => 
            typeof cp.alignment_score === 'number' && cp.alignment_score >= 0 && cp.alignment_score <= 100,
          'career_path: stages have required fields': (cp) => 
            cp.stages.every(stage => stage.stage && stage.certifications && stage.cost),
        });
      }
    });
    
    sleep(5);
    
    // 5. View Certification Details
    group('View Certification Details', function () {
      // Simulate viewing a specific certification
      const certId = Math.floor(Math.random() * 50) + 1; // Assume we have 50 certifications
      
      const response = http.get(`${API_BASE}/certifications/${certId}`, {
        tags: { endpoint: 'certification_detail' },
      });
      
      if (response.status === 200) {
        const cert = JSON.parse(response.body);
        check(cert, {
          'cert_detail: has name': (c) => c.name && c.name.length > 0,
          'cert_detail: has provider': (c) => c.provider && c.provider.length > 0,
          'cert_detail: has cost': (c) => typeof c.cost === 'number',
        });
      }
      
      logResponse(response, 'View Certification Details');
    });
    
    sleep(2);
  });
}

// Setup function (runs once per VU)
export function setup() {
  console.log('Starting load test...');
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`API Base: ${API_BASE}`);
  
  // Verify the API is accessible
  const healthCheck = http.get(`${API_BASE}/health`);
  if (healthCheck.status !== 200) {
    throw new Error(`API health check failed: ${healthCheck.status}`);
  }
  
  console.log('API health check passed');
  return { baseUrl: BASE_URL };
}

// Teardown function (runs once after all VUs finish)
export function teardown(data) {
  console.log('Load test completed');
  console.log(`Base URL tested: ${data.baseUrl}`);
}

// Handle summary (custom summary output)
export function handleSummary(data) {
  const summary = {
    timestamp: new Date().toISOString(),
    test_duration: data.state.testRunDurationMs / 1000,
    total_requests: data.metrics.http_reqs.values.count,
    failed_requests: data.metrics.http_req_failed.values.count,
    error_rate: (data.metrics.http_req_failed.values.rate * 100).toFixed(2),
    avg_response_time: data.metrics.http_req_duration.values.avg.toFixed(2),
    p95_response_time: data.metrics.http_req_duration.values['p(95)'].toFixed(2),
    p99_response_time: data.metrics.http_req_duration.values['p(99)'].toFixed(2),
    requests_per_second: data.metrics.http_reqs.values.rate.toFixed(2),
    virtual_users: data.metrics.vus.values.value,
    max_virtual_users: data.metrics.vus_max.values.value,
    
    // Endpoint-specific metrics
    endpoints: {},
    
    // Thresholds
    thresholds_passed: Object.keys(data.thresholds).filter(
      key => data.thresholds[key].ok
    ).length,
    thresholds_failed: Object.keys(data.thresholds).filter(
      key => !data.thresholds[key].ok
    ).length,
    
    // Performance assessment
    performance_grade: getPerformanceGrade(data),
    recommendations: getRecommendations(data)
  };
  
  // Extract endpoint-specific metrics
  Object.keys(data.metrics).forEach(key => {
    if (key.includes('endpoint:')) {
      const endpoint = key.match(/endpoint:(\w+)/)[1];
      if (!summary.endpoints[endpoint]) {
        summary.endpoints[endpoint] = {};
      }
      
      if (key.includes('http_req_duration')) {
        summary.endpoints[endpoint].avg_response_time = 
          data.metrics[key].values.avg.toFixed(2);
        summary.endpoints[endpoint].p95_response_time = 
          data.metrics[key].values['p(95)'].toFixed(2);
      }
    }
  });
  
  return {
    'load_test_summary.json': JSON.stringify(summary, null, 2),
    'stdout': generateTextSummary(summary),
  };
}

function getPerformanceGrade(data) {
  const errorRate = data.metrics.http_req_failed.values.rate * 100;
  const p95ResponseTime = data.metrics.http_req_duration.values['p(95)'];
  
  if (errorRate < 1 && p95ResponseTime < 1000) return 'A';
  if (errorRate < 2 && p95ResponseTime < 2000) return 'B';
  if (errorRate < 5 && p95ResponseTime < 3000) return 'C';
  if (errorRate < 10 && p95ResponseTime < 5000) return 'D';
  return 'F';
}

function getRecommendations(data) {
  const recommendations = [];
  const errorRate = data.metrics.http_req_failed.values.rate * 100;
  const p95ResponseTime = data.metrics.http_req_duration.values['p(95)'];
  
  if (errorRate > 5) {
    recommendations.push('High error rate detected. Check application logs and error handling.');
  }
  
  if (p95ResponseTime > 2000) {
    recommendations.push('High response times detected. Consider scaling up or optimizing queries.');
  }
  
  if (data.metrics.http_reqs.values.rate < 50) {
    recommendations.push('Low throughput detected. Check for bottlenecks in the application.');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('Performance looks good! Consider testing with higher load.');
  }
  
  return recommendations;
}

function generateTextSummary(summary) {
  return `
🚀 CertRats Load Test Summary
============================

📊 Test Results:
- Duration: ${summary.test_duration}s
- Total Requests: ${summary.total_requests}
- Failed Requests: ${summary.failed_requests}
- Error Rate: ${summary.error_rate}%
- Requests/sec: ${summary.requests_per_second}

⏱️ Response Times:
- Average: ${summary.avg_response_time}ms
- 95th Percentile: ${summary.p95_response_time}ms
- 99th Percentile: ${summary.p99_response_time}ms

👥 Virtual Users:
- Current: ${summary.virtual_users}
- Maximum: ${summary.max_virtual_users}

🎯 Performance Grade: ${summary.performance_grade}

✅ Thresholds:
- Passed: ${summary.thresholds_passed}
- Failed: ${summary.thresholds_failed}

💡 Recommendations:
${summary.recommendations.map(rec => `- ${rec}`).join('\n')}

📈 Endpoint Performance:
${Object.entries(summary.endpoints).map(([endpoint, metrics]) => 
  `- ${endpoint}: ${metrics.avg_response_time}ms avg, ${metrics.p95_response_time}ms p95`
).join('\n')}
`;
}
