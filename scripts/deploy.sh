#!/bin/bash

# CertRats Deployment Script
# Automated deployment with blue-green strategy and rollback capabilities

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_status() {
    echo -e "${PURPLE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
ENVIRONMENT="staging"
NAMESPACE="certrats"
IMAGE_TAG="latest"
DEPLOYMENT_STRATEGY="rolling"
DRY_RUN=false
FORCE_DEPLOY=false
ROLLBACK=false
ROLLBACK_REVISION=""
HELM_CHART_PATH="./helm/certrats"
VALUES_FILE=""
TIMEOUT=600

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -s|--strategy)
            DEPLOYMENT_STRATEGY="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE_DEPLOY=true
            shift
            ;;
        --rollback)
            ROLLBACK=true
            ROLLBACK_REVISION="$2"
            shift 2
            ;;
        -f|--values)
            VALUES_FILE="$2"
            shift 2
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -h|--help)
            echo "CertRats Deployment Script"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -e, --environment ENV    Deployment environment (staging, production)"
            echo "  -n, --namespace NS       Kubernetes namespace"
            echo "  -t, --tag TAG           Docker image tag"
            echo "  -s, --strategy STRATEGY  Deployment strategy (rolling, blue-green)"
            echo "  --dry-run               Validate deployment without applying"
            echo "  --force                 Force deployment without checks"
            echo "  --rollback REVISION     Rollback to specific revision"
            echo "  -f, --values FILE       Custom values file"
            echo "  --timeout SECONDS       Deployment timeout"
            echo "  -h, --help              Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 -e staging -t v1.2.3"
            echo "  $0 -e production -s blue-green --dry-run"
            echo "  $0 --rollback 5"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Set namespace based on environment
if [[ "$ENVIRONMENT" == "staging" ]]; then
    NAMESPACE="certrats-staging"
elif [[ "$ENVIRONMENT" == "production" ]]; then
    NAMESPACE="certrats"
fi

# Set values file if not specified
if [[ -z "$VALUES_FILE" ]]; then
    VALUES_FILE="$HELM_CHART_PATH/values-$ENVIRONMENT.yaml"
    if [[ ! -f "$VALUES_FILE" ]]; then
        VALUES_FILE="$HELM_CHART_PATH/values.yaml"
    fi
fi

print_header "CertRats Deployment"
echo "Environment: $ENVIRONMENT"
echo "Namespace: $NAMESPACE"
echo "Image Tag: $IMAGE_TAG"
echo "Strategy: $DEPLOYMENT_STRATEGY"
echo "Values File: $VALUES_FILE"
echo "Dry Run: $DRY_RUN"
echo "Force Deploy: $FORCE_DEPLOY"
echo "Rollback: $ROLLBACK"
echo ""

# Check prerequisites
print_status "Checking prerequisites..."

# Check kubectl
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is required but not installed"
    exit 1
fi

# Check helm
if ! command -v helm &> /dev/null; then
    print_error "helm is required but not installed"
    exit 1
fi

# Check cluster connectivity
if ! kubectl cluster-info &> /dev/null; then
    print_error "Cannot connect to Kubernetes cluster"
    exit 1
fi

# Check if namespace exists
if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
    print_warning "Namespace $NAMESPACE does not exist. Creating..."
    kubectl create namespace "$NAMESPACE"
fi

print_success "Prerequisites check completed"

# Rollback function
perform_rollback() {
    print_header "Performing Rollback"
    
    if [[ -z "$ROLLBACK_REVISION" ]]; then
        print_status "Getting latest revision..."
        ROLLBACK_REVISION=$(helm history certrats -n "$NAMESPACE" --max 1 -o json | jq -r '.[0].revision - 1')
    fi
    
    print_status "Rolling back to revision $ROLLBACK_REVISION..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status "DRY RUN: Would rollback to revision $ROLLBACK_REVISION"
        return 0
    fi
    
    helm rollback certrats "$ROLLBACK_REVISION" -n "$NAMESPACE" --timeout "${TIMEOUT}s"
    
    if [[ $? -eq 0 ]]; then
        print_success "Rollback completed successfully"
        
        # Wait for rollout to complete
        print_status "Waiting for rollout to complete..."
        kubectl rollout status deployment/backend -n "$NAMESPACE" --timeout="${TIMEOUT}s"
        kubectl rollout status deployment/frontend -n "$NAMESPACE" --timeout="${TIMEOUT}s"
        
        print_success "Rollback verification completed"
    else
        print_error "Rollback failed"
        exit 1
    fi
}

# Pre-deployment checks
pre_deployment_checks() {
    print_header "Pre-deployment Checks"
    
    # Check if Helm chart exists
    if [[ ! -f "$HELM_CHART_PATH/Chart.yaml" ]]; then
        print_error "Helm chart not found at $HELM_CHART_PATH"
        exit 1
    fi
    
    # Check if values file exists
    if [[ ! -f "$VALUES_FILE" ]]; then
        print_error "Values file not found: $VALUES_FILE"
        exit 1
    fi
    
    # Validate Helm chart
    print_status "Validating Helm chart..."
    helm lint "$HELM_CHART_PATH" -f "$VALUES_FILE"
    
    if [[ $? -ne 0 ]]; then
        print_error "Helm chart validation failed"
        exit 1
    fi
    
    # Check if images exist (if not force deploy)
    if [[ "$FORCE_DEPLOY" != "true" ]]; then
        print_status "Checking if Docker images exist..."
        # Add image existence checks here
        print_status "Image checks completed"
    fi
    
    # Check cluster resources
    print_status "Checking cluster resources..."
    kubectl top nodes &> /dev/null || print_warning "Cannot get node metrics"
    
    print_success "Pre-deployment checks completed"
}

# Blue-green deployment
blue_green_deploy() {
    print_header "Blue-Green Deployment"
    
    CURRENT_COLOR=$(kubectl get deployment backend -n "$NAMESPACE" -o jsonpath='{.metadata.labels.color}' 2>/dev/null || echo "blue")
    NEW_COLOR=$([ "$CURRENT_COLOR" = "blue" ] && echo "green" || echo "blue")
    
    print_status "Current deployment: $CURRENT_COLOR"
    print_status "New deployment: $NEW_COLOR"
    
    # Deploy to new color
    print_status "Deploying to $NEW_COLOR environment..."
    
    helm upgrade --install "certrats-$NEW_COLOR" "$HELM_CHART_PATH" \
        -n "$NAMESPACE" \
        -f "$VALUES_FILE" \
        --set global.color="$NEW_COLOR" \
        --set backend.image.tag="$IMAGE_TAG" \
        --set frontend.image.tag="$IMAGE_TAG" \
        --timeout "${TIMEOUT}s" \
        $([ "$DRY_RUN" == "true" ] && echo "--dry-run")
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_success "DRY RUN: Blue-green deployment validated"
        return 0
    fi
    
    # Wait for new deployment to be ready
    print_status "Waiting for $NEW_COLOR deployment to be ready..."
    kubectl rollout status deployment/backend-$NEW_COLOR -n "$NAMESPACE" --timeout="${TIMEOUT}s"
    kubectl rollout status deployment/frontend-$NEW_COLOR -n "$NAMESPACE" --timeout="${TIMEOUT}s"
    
    # Run health checks
    print_status "Running health checks on $NEW_COLOR environment..."
    sleep 30  # Allow time for services to stabilize
    
    # Switch traffic
    print_status "Switching traffic to $NEW_COLOR environment..."
    kubectl patch service backend-service -n "$NAMESPACE" -p '{"spec":{"selector":{"color":"'$NEW_COLOR'"}}}'
    kubectl patch service frontend-service -n "$NAMESPACE" -p '{"spec":{"selector":{"color":"'$NEW_COLOR'"}}}'
    
    # Monitor new deployment
    print_status "Monitoring new deployment for 60 seconds..."
    sleep 60
    
    # Clean up old deployment
    print_status "Cleaning up $CURRENT_COLOR environment..."
    helm uninstall "certrats-$CURRENT_COLOR" -n "$NAMESPACE" || true
    
    print_success "Blue-green deployment completed"
}

# Rolling deployment
rolling_deploy() {
    print_header "Rolling Deployment"
    
    print_status "Performing rolling update..."
    
    helm upgrade --install certrats "$HELM_CHART_PATH" \
        -n "$NAMESPACE" \
        -f "$VALUES_FILE" \
        --set backend.image.tag="$IMAGE_TAG" \
        --set frontend.image.tag="$IMAGE_TAG" \
        --timeout "${TIMEOUT}s" \
        $([ "$DRY_RUN" == "true" ] && echo "--dry-run")
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_success "DRY RUN: Rolling deployment validated"
        return 0
    fi
    
    # Wait for rollout to complete
    print_status "Waiting for rollout to complete..."
    kubectl rollout status deployment/backend -n "$NAMESPACE" --timeout="${TIMEOUT}s"
    kubectl rollout status deployment/frontend -n "$NAMESPACE" --timeout="${TIMEOUT}s"
    
    print_success "Rolling deployment completed"
}

# Post-deployment verification
post_deployment_verification() {
    print_header "Post-deployment Verification"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status "DRY RUN: Skipping post-deployment verification"
        return 0
    fi
    
    # Check pod status
    print_status "Checking pod status..."
    kubectl get pods -n "$NAMESPACE" -l app.kubernetes.io/name=certrats
    
    # Check service endpoints
    print_status "Checking service endpoints..."
    kubectl get endpoints -n "$NAMESPACE"
    
    # Run smoke tests
    print_status "Running smoke tests..."
    
    # Get service URL
    if kubectl get ingress certrats-ingress -n "$NAMESPACE" &> /dev/null; then
        SERVICE_URL=$(kubectl get ingress certrats-ingress -n "$NAMESPACE" -o jsonpath='{.spec.rules[0].host}')
        SERVICE_URL="https://$SERVICE_URL"
    else
        SERVICE_URL="http://$(kubectl get service nginx-service -n "$NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].ip}')"
    fi
    
    print_status "Testing service at: $SERVICE_URL"
    
    # Health check
    if curl -f -s "$SERVICE_URL/health" > /dev/null; then
        print_success "Health check passed"
    else
        print_warning "Health check failed"
    fi
    
    # API check
    if curl -f -s "$SERVICE_URL/api/v1/health" > /dev/null; then
        print_success "API health check passed"
    else
        print_warning "API health check failed"
    fi
    
    print_success "Post-deployment verification completed"
}

# Main deployment logic
main() {
    if [[ "$ROLLBACK" == "true" ]]; then
        perform_rollback
        return 0
    fi
    
    pre_deployment_checks
    
    case "$DEPLOYMENT_STRATEGY" in
        "rolling")
            rolling_deploy
            ;;
        "blue-green")
            blue_green_deploy
            ;;
        *)
            print_error "Unknown deployment strategy: $DEPLOYMENT_STRATEGY"
            exit 1
            ;;
    esac
    
    post_deployment_verification
    
    print_header "🎉 Deployment Completed Successfully!"
    echo ""
    echo "Environment: $ENVIRONMENT"
    echo "Namespace: $NAMESPACE"
    echo "Image Tag: $IMAGE_TAG"
    echo "Strategy: $DEPLOYMENT_STRATEGY"
    echo ""
    echo "Next steps:"
    echo "1. Monitor application performance"
    echo "2. Check logs for any errors"
    echo "3. Validate user experience"
    echo "4. Update monitoring dashboards"
}

# Error handling
trap 'print_error "Deployment failed at line $LINENO"' ERR

# Run main function
main
