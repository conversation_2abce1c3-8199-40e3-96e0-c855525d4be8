"""
<PERSON>ript to populate security jobs from the provided data file
"""
import os
import logging
from models.job import SecurityJob
from database import init_db, get_db

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define security domains
SECURITY_DOMAINS = [
    "Defensive Security",
    "Asset Security",
    "Identity & Access Management",
    "Network Security", 
    "Offensive Security",
    "Security Engineering",
    "Security Operations",
    "Security Management",
    "Security Testing"
]

def is_valid_job_title(title: str) -> bool:
    """Validate job title format and content"""
    # Skip entries that look like URLs, Reddit posts, or other invalid formats
    invalid_markers = [
        "http", "www.", "reddit", "accessed on",
        ".com", ".org", ".net",
        "?", ":", "-", " - ", "..."
    ]

    # Explicitly handle empty or None input
    if not title or not isinstance(title, str):
        return False

    return (
        len(title) >= 3 and
        len(title) <= 100 and
        not any(marker in title.lower() for marker in invalid_markers) and
        not title.startswith(("The", "A ", "An ")) and
        not title.isupper() and  # Skip all uppercase titles
        not title.islower()  # Skip all lowercase titles
    )

def parse_job_titles(content):
    """Parse job titles and domains from the content"""
    jobs = []
    current_domain = None
    lines = content.split('\n')

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # Check if line matches a known security domain
        if line in SECURITY_DOMAINS:
            current_domain = line
            logger.info(f"Found domain: {current_domain}")
            continue

        # Skip non-job lines and header sections
        if (not current_domain or
            any(skip in line for skip in ['Cybersecurity Job Titles', 'Based on', 'Conclusion', 
                                      'Works cited', 'focuses on', 'protects', 'This report',
                                      'Inferred style'])):
            continue

        # Process job title line
        parts = line.split()
        title_parts = []
        for word in parts:
            # Skip reference numbers at the end of lines
            if word.isdigit() and len(word) <= 2:
                continue
            # Keep only words that are part of the title
            if not any(char.isdigit() for char in word):
                title_parts.append(word)

        title = ' '.join(title_parts).strip()

        # Validate the job title before adding
        if title and is_valid_job_title(title):
            logger.info(f"Adding job: {title} in domain {current_domain}")
            jobs.append({
                'title': title,
                'domain': current_domain,
                'description': f"Position in {current_domain} domain focusing on specialized security tasks and responsibilities."
            })
        else:
            logger.warning(f"Skipping invalid job title: {title}")

    logger.info(f"Total valid jobs parsed: {len(jobs)}")
    return jobs

def populate_jobs():
    """Populate the database with security jobs"""
    init_db()
    db = next(get_db())

    try:
        # Read the job titles file
        file_path = 'attached_assets/Pasted-Cybersecurity-Job-Titles-Based-on-an-exhaustive-search-of-job-boards-and-websites-like-Indeed-Linke-1740515734230.txt'
        with open(file_path, 'r') as f:
            content = f.read()

        jobs = parse_job_titles(content)
        success_count = 0
        error_count = 0

        # Insert jobs into database
        for job_data in jobs:
            try:
                # Check if job already exists
                existing = db.query(SecurityJob).filter_by(
                    title=job_data['title'],
                    domain=job_data['domain']
                ).first()

                if not existing:
                    job = SecurityJob(
                        title=job_data['title'],
                        domain=job_data['domain'],
                        description=job_data['description']
                    )
                    db.add(job)
                    success_count += 1
                else:
                    logger.info(f"Skipping existing job: {job_data['title']}")
            except Exception as e:
                logger.error(f"Error seeding job {job_data['title']}: {str(e)}")
                error_count += 1

        db.commit()
        logger.info(f"Successfully seeded {success_count} jobs with {error_count} errors")
        return success_count, error_count
    except Exception as e:
        logger.error(f"Error during job seeding: {str(e)}")
        db.rollback()
        return 0, 0

if __name__ == "__main__":
    populate_jobs()