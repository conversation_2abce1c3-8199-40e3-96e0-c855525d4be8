#!/bin/bash

# CertRats Cost Optimization Script
# Automated cost optimization for AWS infrastructure and Kubernetes resources

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_status() {
    echo -e "${PURPLE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_savings() {
    echo -e "${GREEN}[SAVINGS]${NC} $1"
}

# Default values
DRY_RUN=false
ENVIRONMENT="production"
NAMESPACE="certrats"
REPORT_ONLY=false
AGGRESSIVE_MODE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --report-only)
            REPORT_ONLY=true
            shift
            ;;
        --aggressive)
            AGGRESSIVE_MODE=true
            shift
            ;;
        -h|--help)
            echo "CertRats Cost Optimization Script"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --dry-run           Show what would be done without making changes"
            echo "  -e, --environment   Environment (staging, production)"
            echo "  -n, --namespace     Kubernetes namespace"
            echo "  --report-only       Generate cost report only"
            echo "  --aggressive        Enable aggressive cost optimization"
            echo "  -h, --help          Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 --dry-run                    # Preview optimizations"
            echo "  $0 --report-only                # Generate cost report"
            echo "  $0 -e staging --aggressive      # Aggressive optimization for staging"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_header "CertRats Cost Optimization"
echo "Environment: $ENVIRONMENT"
echo "Namespace: $NAMESPACE"
echo "Dry Run: $DRY_RUN"
echo "Report Only: $REPORT_ONLY"
echo "Aggressive Mode: $AGGRESSIVE_MODE"
echo ""

# Check prerequisites
print_status "Checking prerequisites..."

# Check kubectl
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is required but not installed"
    exit 1
fi

# Check AWS CLI
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is required but not installed"
    exit 1
fi

# Check cluster connectivity
if ! kubectl cluster-info &> /dev/null; then
    print_error "Cannot connect to Kubernetes cluster"
    exit 1
fi

print_success "Prerequisites check completed"

# Initialize cost tracking
TOTAL_SAVINGS=0
OPTIMIZATION_ACTIONS=()

# Function to add savings
add_savings() {
    local amount=$1
    local description="$2"
    TOTAL_SAVINGS=$((TOTAL_SAVINGS + amount))
    OPTIMIZATION_ACTIONS+=("$description: \$${amount}/month")
    print_savings "$description: \$${amount}/month"
}

# Function to execute or simulate command
execute_command() {
    local cmd="$1"
    local description="$2"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        print_status "DRY RUN: Would execute: $cmd"
        print_status "DRY RUN: $description"
    else
        print_status "Executing: $description"
        eval "$cmd"
        if [[ $? -eq 0 ]]; then
            print_success "$description completed"
        else
            print_error "$description failed"
        fi
    fi
}

# Kubernetes Resource Optimization
optimize_kubernetes_resources() {
    print_header "Kubernetes Resource Optimization"
    
    # Get current resource usage
    print_status "Analyzing current resource usage..."
    
    # Check for over-provisioned pods
    print_status "Checking for over-provisioned resources..."
    
    # Get pod resource requests vs actual usage
    kubectl top pods -n "$NAMESPACE" --no-headers 2>/dev/null | while read line; do
        if [[ -n "$line" ]]; then
            pod_name=$(echo "$line" | awk '{print $1}')
            cpu_usage=$(echo "$line" | awk '{print $2}' | sed 's/m//')
            memory_usage=$(echo "$line" | awk '{print $3}' | sed 's/Mi//')
            
            # Get resource requests
            cpu_request=$(kubectl get pod "$pod_name" -n "$NAMESPACE" -o jsonpath='{.spec.containers[0].resources.requests.cpu}' | sed 's/m//')
            memory_request=$(kubectl get pod "$pod_name" -n "$NAMESPACE" -o jsonpath='{.spec.containers[0].resources.requests.memory}' | sed 's/Mi//')
            
            if [[ -n "$cpu_request" && -n "$cpu_usage" ]]; then
                cpu_utilization=$((cpu_usage * 100 / cpu_request))
                if [[ $cpu_utilization -lt 20 ]]; then
                    print_warning "Pod $pod_name: Low CPU utilization ($cpu_utilization%)"
                    if [[ "$AGGRESSIVE_MODE" == "true" ]]; then
                        new_cpu_request=$((cpu_request / 2))
                        execute_command "kubectl patch deployment ${pod_name%-*} -n $NAMESPACE -p '{\"spec\":{\"template\":{\"spec\":{\"containers\":[{\"name\":\"${pod_name%-*}\",\"resources\":{\"requests\":{\"cpu\":\"${new_cpu_request}m\"}}}]}}}}'" \
                                      "Reduce CPU request for $pod_name"
                        add_savings 15 "Reduced CPU request for $pod_name"
                    fi
                fi
            fi
        fi
    done
    
    # Optimize replica counts based on usage
    print_status "Optimizing replica counts..."
    
    deployments=$(kubectl get deployments -n "$NAMESPACE" -o jsonpath='{.items[*].metadata.name}')
    for deployment in $deployments; do
        current_replicas=$(kubectl get deployment "$deployment" -n "$NAMESPACE" -o jsonpath='{.spec.replicas}')
        
        # Check if we can reduce replicas during off-peak hours
        current_hour=$(date +%H)
        if [[ $current_hour -ge 2 && $current_hour -le 6 ]] && [[ "$ENVIRONMENT" == "staging" ]]; then
            # Off-peak hours for staging
            if [[ $current_replicas -gt 1 ]]; then
                new_replicas=1
                execute_command "kubectl scale deployment $deployment --replicas=$new_replicas -n $NAMESPACE" \
                              "Scale down $deployment during off-peak hours"
                add_savings 50 "Off-peak scaling for $deployment"
            fi
        fi
    done
}

# AWS Cost Optimization
optimize_aws_costs() {
    print_header "AWS Cost Optimization"
    
    # Check for unused EBS volumes
    print_status "Checking for unused EBS volumes..."
    
    unused_volumes=$(aws ec2 describe-volumes --filters Name=status,Values=available --query 'Volumes[*].{ID:VolumeId,Size:Size,Type:VolumeType}' --output table)
    if [[ -n "$unused_volumes" ]]; then
        print_warning "Found unused EBS volumes:"
        echo "$unused_volumes"
        
        # Calculate potential savings
        volume_count=$(aws ec2 describe-volumes --filters Name=status,Values=available --query 'length(Volumes)')
        if [[ $volume_count -gt 0 ]]; then
            estimated_savings=$((volume_count * 10))  # Rough estimate
            add_savings $estimated_savings "Delete unused EBS volumes"
            
            if [[ "$AGGRESSIVE_MODE" == "true" ]]; then
                aws ec2 describe-volumes --filters Name=status,Values=available --query 'Volumes[*].VolumeId' --output text | while read volume_id; do
                    execute_command "aws ec2 delete-volume --volume-id $volume_id" \
                                  "Delete unused volume $volume_id"
                done
            fi
        fi
    fi
    
    # Check for unattached Elastic IPs
    print_status "Checking for unattached Elastic IPs..."
    
    unattached_eips=$(aws ec2 describe-addresses --query 'Addresses[?!AssociationId].AllocationId' --output text)
    if [[ -n "$unattached_eips" ]]; then
        eip_count=$(echo "$unattached_eips" | wc -w)
        estimated_savings=$((eip_count * 4))  # $4/month per unattached EIP
        add_savings $estimated_savings "Release unattached Elastic IPs"
        
        if [[ "$AGGRESSIVE_MODE" == "true" ]]; then
            for eip in $unattached_eips; do
                execute_command "aws ec2 release-address --allocation-id $eip" \
                              "Release unattached Elastic IP $eip"
            done
        fi
    fi
    
    # Check for old snapshots
    print_status "Checking for old snapshots..."
    
    # Find snapshots older than 30 days
    cutoff_date=$(date -d '30 days ago' +%Y-%m-%d)
    old_snapshots=$(aws ec2 describe-snapshots --owner-ids self --query "Snapshots[?StartTime<='$cutoff_date'].SnapshotId" --output text)
    
    if [[ -n "$old_snapshots" ]]; then
        snapshot_count=$(echo "$old_snapshots" | wc -w)
        estimated_savings=$((snapshot_count * 2))  # Rough estimate
        add_savings $estimated_savings "Delete old snapshots (>30 days)"
        
        if [[ "$AGGRESSIVE_MODE" == "true" ]]; then
            for snapshot in $old_snapshots; do
                execute_command "aws ec2 delete-snapshot --snapshot-id $snapshot" \
                              "Delete old snapshot $snapshot"
            done
        fi
    fi
    
    # Suggest Reserved Instances
    print_status "Analyzing EC2 instances for Reserved Instance opportunities..."
    
    running_instances=$(aws ec2 describe-instances --filters Name=instance-state-name,Values=running --query 'Reservations[*].Instances[*].{Type:InstanceType,AZ:Placement.AvailabilityZone}' --output table)
    if [[ -n "$running_instances" ]]; then
        print_status "Running instances that could benefit from Reserved Instances:"
        echo "$running_instances"
        
        # Rough estimate: 30% savings with Reserved Instances
        instance_count=$(aws ec2 describe-instances --filters Name=instance-state-name,Values=running --query 'length(Reservations[*].Instances[*])')
        estimated_savings=$((instance_count * 50))  # Rough estimate
        print_warning "Consider purchasing Reserved Instances for potential savings: \$${estimated_savings}/month"
    fi
}

# Database Optimization
optimize_database_costs() {
    print_header "Database Cost Optimization"
    
    # Check RDS instances
    print_status "Analyzing RDS instances..."
    
    # Get RDS instances
    rds_instances=$(aws rds describe-db-instances --query 'DBInstances[*].{ID:DBInstanceIdentifier,Class:DBInstanceClass,Engine:Engine,Status:DBInstanceStatus}' --output table)
    if [[ -n "$rds_instances" ]]; then
        print_status "Current RDS instances:"
        echo "$rds_instances"
        
        # Check for development/staging instances that could be stopped
        if [[ "$ENVIRONMENT" == "staging" ]]; then
            aws rds describe-db-instances --query 'DBInstances[*].DBInstanceIdentifier' --output text | while read db_id; do
                if [[ "$db_id" == *"staging"* ]] || [[ "$db_id" == *"dev"* ]]; then
                    execute_command "aws rds stop-db-instance --db-instance-identifier $db_id" \
                                  "Stop staging/dev RDS instance $db_id during off-hours"
                    add_savings 100 "Stop staging RDS instance during off-hours"
                fi
            done
        fi
    fi
    
    # Check for unused RDS snapshots
    print_status "Checking for old RDS snapshots..."
    
    cutoff_date=$(date -d '30 days ago' +%Y-%m-%d)
    old_snapshots=$(aws rds describe-db-snapshots --snapshot-type manual --query "DBSnapshots[?SnapshotCreateTime<='$cutoff_date'].DBSnapshotIdentifier" --output text)
    
    if [[ -n "$old_snapshots" ]]; then
        snapshot_count=$(echo "$old_snapshots" | wc -w)
        estimated_savings=$((snapshot_count * 5))  # Rough estimate
        add_savings $estimated_savings "Delete old RDS snapshots (>30 days)"
        
        if [[ "$AGGRESSIVE_MODE" == "true" ]]; then
            for snapshot in $old_snapshots; do
                execute_command "aws rds delete-db-snapshot --db-snapshot-identifier $snapshot" \
                              "Delete old RDS snapshot $snapshot"
            done
        fi
    fi
}

# Storage Optimization
optimize_storage_costs() {
    print_header "Storage Cost Optimization"
    
    # Check S3 buckets for lifecycle policies
    print_status "Analyzing S3 storage..."
    
    buckets=$(aws s3api list-buckets --query 'Buckets[*].Name' --output text)
    for bucket in $buckets; do
        if [[ "$bucket" == *"certrats"* ]]; then
            # Check if lifecycle policy exists
            lifecycle_policy=$(aws s3api get-bucket-lifecycle-configuration --bucket "$bucket" 2>/dev/null || echo "none")
            
            if [[ "$lifecycle_policy" == "none" ]]; then
                print_warning "Bucket $bucket has no lifecycle policy"
                
                # Create lifecycle policy
                lifecycle_config='{
                    "Rules": [
                        {
                            "ID": "CostOptimization",
                            "Status": "Enabled",
                            "Filter": {},
                            "Transitions": [
                                {
                                    "Days": 30,
                                    "StorageClass": "STANDARD_IA"
                                },
                                {
                                    "Days": 90,
                                    "StorageClass": "GLACIER"
                                },
                                {
                                    "Days": 365,
                                    "StorageClass": "DEEP_ARCHIVE"
                                }
                            ]
                        }
                    ]
                }'
                
                execute_command "aws s3api put-bucket-lifecycle-configuration --bucket $bucket --lifecycle-configuration '$lifecycle_config'" \
                              "Add lifecycle policy to bucket $bucket"
                add_savings 20 "S3 lifecycle policy for $bucket"
            fi
            
            # Check for incomplete multipart uploads
            incomplete_uploads=$(aws s3api list-multipart-uploads --bucket "$bucket" --query 'length(Uploads)')
            if [[ $incomplete_uploads -gt 0 ]]; then
                execute_command "aws s3api abort-multipart-upload --bucket $bucket" \
                              "Clean up incomplete multipart uploads in $bucket"
                add_savings 5 "Clean up incomplete uploads in $bucket"
            fi
        fi
    done
}

# Generate Cost Report
generate_cost_report() {
    print_header "Cost Optimization Report"
    
    # Get current AWS costs (last 30 days)
    start_date=$(date -d '30 days ago' +%Y-%m-%d)
    end_date=$(date +%Y-%m-%d)
    
    print_status "Generating cost report for period: $start_date to $end_date"
    
    # Get cost by service
    cost_by_service=$(aws ce get-cost-and-usage \
        --time-period Start="$start_date",End="$end_date" \
        --granularity MONTHLY \
        --metrics BlendedCost \
        --group-by Type=DIMENSION,Key=SERVICE \
        --query 'ResultsByTime[0].Groups[*].{Service:Keys[0],Cost:Metrics.BlendedCost.Amount}' \
        --output table)
    
    if [[ -n "$cost_by_service" ]]; then
        print_status "Current AWS costs by service:"
        echo "$cost_by_service"
    fi
    
    # Get Kubernetes resource costs
    print_status "Kubernetes resource utilization:"
    kubectl top nodes 2>/dev/null || print_warning "Node metrics not available"
    kubectl top pods -n "$NAMESPACE" 2>/dev/null || print_warning "Pod metrics not available"
    
    # Summary
    echo ""
    print_header "Cost Optimization Summary"
    echo "Total Potential Monthly Savings: \$${TOTAL_SAVINGS}"
    echo ""
    echo "Optimization Actions:"
    for action in "${OPTIMIZATION_ACTIONS[@]}"; do
        echo "  - $action"
    done
    
    # Save report to file
    report_file="cost_optimization_report_$(date +%Y%m%d_%H%M%S).txt"
    {
        echo "CertRats Cost Optimization Report"
        echo "Generated: $(date)"
        echo "Environment: $ENVIRONMENT"
        echo "Namespace: $NAMESPACE"
        echo ""
        echo "Total Potential Monthly Savings: \$${TOTAL_SAVINGS}"
        echo ""
        echo "Optimization Actions:"
        for action in "${OPTIMIZATION_ACTIONS[@]}"; do
            echo "  - $action"
        done
        echo ""
        echo "AWS Costs by Service:"
        echo "$cost_by_service"
    } > "$report_file"
    
    print_success "Cost report saved to $report_file"
}

# Main execution
main() {
    if [[ "$REPORT_ONLY" == "true" ]]; then
        generate_cost_report
        return 0
    fi
    
    # Run optimization modules
    optimize_kubernetes_resources
    optimize_aws_costs
    optimize_database_costs
    optimize_storage_costs
    
    # Generate final report
    generate_cost_report
    
    print_header "🎉 Cost Optimization Completed!"
    echo ""
    echo "Total Potential Monthly Savings: \$${TOTAL_SAVINGS}"
    echo ""
    echo "Next steps:"
    echo "1. Review the optimization actions taken"
    echo "2. Monitor cost changes over the next billing cycle"
    echo "3. Set up cost alerts and budgets"
    echo "4. Schedule regular cost optimization reviews"
    echo ""
    print_success "Cost optimization completed successfully!"
}

# Error handling
trap 'print_error "Cost optimization failed at line $LINENO"' ERR

# Run main function
main
