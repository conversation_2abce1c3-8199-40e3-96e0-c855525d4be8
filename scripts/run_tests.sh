#!/bin/bash

# Script to run tests with PostgreSQL test database
set -e

echo "🧪 CertRats PostgreSQL Test Runner"
echo "=================================="

# Check if we're in Docker environment
if [ -f /.dockerenv ]; then
    echo "📦 Running in Docker environment"
    export TEST_DATABASE_URL="***********************************************"
else
    echo "💻 Running in local environment"
    export TEST_DATABASE_URL="postgresql://postgres:postgres@localhost:5432"
fi

echo "🔗 Using database URL: $TEST_DATABASE_URL"

# Set test environment
export ENVIRONMENT=test

# Function to check if PostgreSQL is available
check_postgres() {
    echo "⏳ Checking PostgreSQL availability..."
    python3 -c "
import sys
sys.path.append('.')
from tests.test_db_utils import wait_for_postgres
if not wait_for_postgres():
    print('❌ PostgreSQL is not available')
    sys.exit(1)
print('✅ PostgreSQL is available')
"
}

# Function to setup test database
setup_test_db() {
    echo "🔧 Setting up test database..."
    python3 scripts/test_db_setup.py
}

# Function to run tests
run_tests() {
    echo "🧪 Running tests..."
    
    # Parse command line arguments
    TEST_ARGS="$@"
    
    if [ -z "$TEST_ARGS" ]; then
        echo "Running all tests..."
        pytest tests/ -v
    else
        echo "Running tests with args: $TEST_ARGS"
        pytest $TEST_ARGS
    fi
}

# Function to cleanup
cleanup() {
    echo "🧹 Cleaning up test databases..."
    python3 -c "
import sys
sys.path.append('.')
from tests.test_db_utils import cleanup_test_databases
cleanup_test_databases()
"
}

# Main execution
main() {
    # Check PostgreSQL
    check_postgres
    
    # Setup test database
    setup_test_db
    
    # Run tests
    run_tests "$@"
    
    # Cleanup
    cleanup
    
    echo "✅ Tests completed successfully!"
}

# Handle script interruption
trap cleanup EXIT

# Run main function with all arguments
main "$@"
