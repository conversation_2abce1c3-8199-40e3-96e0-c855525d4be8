#!/bin/bash

# CertRats Test Runner Script
# Comprehensive testing with different test categories and reporting

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_status() {
    echo -e "${PURPLE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
TEST_TYPE="all"
COVERAGE_THRESHOLD=80
VERBOSE=false
PARALLEL=false
GENERATE_REPORT=true

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            TEST_TYPE="$2"
            shift 2
            ;;
        -c|--coverage)
            COVERAGE_THRESHOLD="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -p|--parallel)
            PARALLEL=true
            shift
            ;;
        --no-report)
            GENERATE_REPORT=false
            shift
            ;;
        -h|--help)
            echo "CertRats Test Runner"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -t, --type TYPE        Test type: all, unit, integration, e2e, performance, security"
            echo "  -c, --coverage NUM     Coverage threshold (default: 80)"
            echo "  -v, --verbose          Verbose output"
            echo "  -p, --parallel         Run tests in parallel"
            echo "  --no-report           Skip HTML report generation"
            echo "  -h, --help            Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                     # Run all tests"
            echo "  $0 -t unit             # Run only unit tests"
            echo "  $0 -t integration -v   # Run integration tests with verbose output"
            echo "  $0 -c 90 -p            # Run all tests with 90% coverage threshold in parallel"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Check if we're in the correct directory
if [ ! -f "pytest.ini" ] || [ ! -d "tests" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_header "CertRats Test Suite Runner"
echo "Test Type: $TEST_TYPE"
echo "Coverage Threshold: $COVERAGE_THRESHOLD%"
echo "Verbose: $VERBOSE"
echo "Parallel: $PARALLEL"
echo "Generate Report: $GENERATE_REPORT"
echo ""

# Check prerequisites
print_status "Checking prerequisites..."

# Check Python and pytest
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is required but not installed"
    exit 1
fi

if ! python3 -c "import pytest" &> /dev/null; then
    print_error "pytest is not installed. Run: pip install pytest"
    exit 1
fi

# Check if virtual environment is activated
if [[ "$VIRTUAL_ENV" == "" ]]; then
    print_warning "Virtual environment not detected. Consider activating venv."
fi

print_success "Prerequisites check completed"

# Setup test environment
print_status "Setting up test environment..."

# Set environment variables
export ENVIRONMENT=test
export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/certrats_test"
export ANTHROPIC_API_KEY="test_key"

# Create necessary directories
mkdir -p logs
mkdir -p htmlcov
mkdir -p test-reports

print_success "Test environment setup completed"

# Build pytest command
PYTEST_CMD="python -m pytest"

# Add coverage options
if [ "$GENERATE_REPORT" = true ]; then
    PYTEST_CMD="$PYTEST_CMD --cov=. --cov-report=html --cov-report=term-missing --cov-report=xml"
    PYTEST_CMD="$PYTEST_CMD --cov-fail-under=$COVERAGE_THRESHOLD"
fi

# Add verbosity
if [ "$VERBOSE" = true ]; then
    PYTEST_CMD="$PYTEST_CMD -v"
else
    PYTEST_CMD="$PYTEST_CMD -q"
fi

# Add parallel execution
if [ "$PARALLEL" = true ]; then
    PYTEST_CMD="$PYTEST_CMD -n auto"
fi

# Add test markers based on type
case $TEST_TYPE in
    "unit")
        PYTEST_CMD="$PYTEST_CMD -m unit"
        print_header "Running Unit Tests"
        ;;
    "integration")
        PYTEST_CMD="$PYTEST_CMD -m integration"
        print_header "Running Integration Tests"
        ;;
    "e2e")
        PYTEST_CMD="$PYTEST_CMD -m e2e"
        print_header "Running End-to-End Tests"
        ;;
    "performance")
        PYTEST_CMD="$PYTEST_CMD -m performance"
        print_header "Running Performance Tests"
        ;;
    "security")
        PYTEST_CMD="$PYTEST_CMD -m security"
        print_header "Running Security Tests"
        ;;
    "fast")
        PYTEST_CMD="$PYTEST_CMD -m 'not slow'"
        print_header "Running Fast Tests (excluding slow tests)"
        ;;
    "all")
        print_header "Running All Tests"
        ;;
    *)
        print_error "Invalid test type: $TEST_TYPE"
        print_error "Valid types: all, unit, integration, e2e, performance, security, fast"
        exit 1
        ;;
esac

# Add JUnit XML output for CI/CD
PYTEST_CMD="$PYTEST_CMD --junitxml=test-reports/junit.xml"

# Add timing information
PYTEST_CMD="$PYTEST_CMD --durations=10"

print_status "Running command: $PYTEST_CMD"
echo ""

# Run the tests
START_TIME=$(date +%s)

if eval $PYTEST_CMD; then
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    print_success "All tests passed! ✅"
    echo ""
    print_status "Test execution completed in ${DURATION} seconds"
    
    # Generate additional reports if requested
    if [ "$GENERATE_REPORT" = true ]; then
        print_status "Generating test reports..."
        
        # Coverage report location
        if [ -f "htmlcov/index.html" ]; then
            print_success "Coverage report generated: htmlcov/index.html"
        fi
        
        # JUnit XML report
        if [ -f "test-reports/junit.xml" ]; then
            print_success "JUnit XML report generated: test-reports/junit.xml"
        fi
        
        # Coverage XML for CI/CD
        if [ -f "coverage.xml" ]; then
            print_success "Coverage XML generated: coverage.xml"
        fi
    fi
    
    # Test summary
    echo ""
    print_header "Test Summary"
    
    if [ -f "coverage.xml" ]; then
        # Extract coverage percentage from XML (if available)
        COVERAGE=$(python3 -c "
import xml.etree.ElementTree as ET
try:
    tree = ET.parse('coverage.xml')
    root = tree.getroot()
    coverage = root.attrib.get('line-rate', '0')
    print(f'{float(coverage)*100:.1f}%')
except:
    print('N/A')
" 2>/dev/null)
        echo "Coverage: $COVERAGE"
    fi
    
    echo "Duration: ${DURATION}s"
    echo "Test Type: $TEST_TYPE"
    echo "Status: ✅ PASSED"
    
else
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    print_error "Tests failed! ❌"
    echo ""
    print_status "Test execution failed after ${DURATION} seconds"
    
    # Show failure summary
    echo ""
    print_header "Failure Summary"
    echo "Duration: ${DURATION}s"
    echo "Test Type: $TEST_TYPE"
    echo "Status: ❌ FAILED"
    
    # Suggest next steps
    echo ""
    print_status "Next steps:"
    echo "1. Check the test output above for specific failures"
    echo "2. Run with -v flag for more detailed output"
    echo "3. Run specific test types to isolate issues"
    echo "4. Check htmlcov/index.html for coverage details"
    
    exit 1
fi

# Final success message
echo ""
print_header "🎉 Test Suite Completed Successfully!"
echo ""
echo "📊 Reports generated:"
if [ "$GENERATE_REPORT" = true ]; then
    echo "   • HTML Coverage: htmlcov/index.html"
    echo "   • XML Coverage: coverage.xml"
    echo "   • JUnit XML: test-reports/junit.xml"
fi
echo ""
echo "🚀 Ready for deployment!"
