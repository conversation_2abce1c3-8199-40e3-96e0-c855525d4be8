"""
Script to normalize certification data.
"""
import os
import sys
import logging

# Add project root to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.certification_normalizer import update_certification_data

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Execute certification data normalization."""
    try:
        input_file = os.path.join('attached_assets', 'certifications.json')
        output_file = os.path.join('data', 'normalized_certifications.json')

        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # Normalize and update data
        update_certification_data(input_file, output_file)
        logger.info("Certification data normalized successfully")

    except Exception as e:
        logger.error(f"Error in normalization process: {str(e)}")
        raise

if __name__ == "__main__":
    main()