#!/bin/bash

# CertRats Go-Live Checklist Script
# Comprehensive pre-launch validation and go-live procedures

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_status() {
    echo -e "${PURPLE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✅ PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠️ WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[❌ FAIL]${NC} $1"
}

print_check() {
    echo -e "${BLUE}[🔍 CHECK]${NC} $1"
}

# Initialize counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# Function to run a check
run_check() {
    local check_name="$1"
    local check_command="$2"
    local is_critical="${3:-true}"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    print_check "$check_name"
    
    if eval "$check_command" >/dev/null 2>&1; then
        print_success "$check_name"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        if [[ "$is_critical" == "true" ]]; then
            print_error "$check_name"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            return 1
        else
            print_warning "$check_name"
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
            return 0
        fi
    fi
}

# Function to check URL response
check_url() {
    local url="$1"
    local expected_status="${2:-200}"
    curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "$expected_status"
}

# Function to check API endpoint
check_api() {
    local endpoint="$1"
    local method="${2:-GET}"
    local data="${3:-}"
    
    if [[ -n "$data" ]]; then
        curl -s -X "$method" -H "Content-Type: application/json" -d "$data" "$endpoint" | jq -e . >/dev/null
    else
        curl -s -X "$method" "$endpoint" | jq -e . >/dev/null
    fi
}

print_header "🚀 CertRats Go-Live Checklist"
echo "Starting comprehensive pre-launch validation..."
echo "Timestamp: $(date)"
echo ""

# =============================================================================
# INFRASTRUCTURE CHECKS
# =============================================================================

print_header "🏗️ Infrastructure Validation"

# Kubernetes cluster health
run_check "Kubernetes cluster connectivity" "kubectl cluster-info"
run_check "All nodes ready" "kubectl get nodes | grep -v NotReady"
run_check "System pods running" "kubectl get pods -n kube-system | grep -v Running | wc -l | grep -q '^0$'"

# Namespace and resource checks
run_check "CertRats namespace exists" "kubectl get namespace certrats"
run_check "Monitoring namespace exists" "kubectl get namespace certrats-monitoring"
run_check "All pods running in certrats namespace" "kubectl get pods -n certrats | grep -v Running | grep -v Completed | wc -l | grep -q '^0$'"

# Storage and persistence
run_check "Persistent volumes available" "kubectl get pv | grep Available"
run_check "Storage classes configured" "kubectl get storageclass"

# =============================================================================
# APPLICATION CHECKS
# =============================================================================

print_header "📱 Application Validation"

# Core services
run_check "Backend deployment ready" "kubectl get deployment backend -n certrats -o jsonpath='{.status.readyReplicas}' | grep -q '[1-9]'"
run_check "Frontend deployment ready" "kubectl get deployment frontend -n certrats -o jsonpath='{.status.readyReplicas}' | grep -q '[1-9]'"
run_check "Database pod running" "kubectl get pods -l app=postgres -n certrats | grep Running"
run_check "Redis cluster running" "kubectl get pods -l app=redis-cluster -n certrats | grep Running"

# Service endpoints
run_check "Backend service accessible" "kubectl get service backend-service -n certrats"
run_check "Frontend service accessible" "kubectl get service frontend-service -n certrats"
run_check "Load balancer configured" "kubectl get service nginx-service -n certrats | grep LoadBalancer"

# =============================================================================
# NETWORK AND DNS CHECKS
# =============================================================================

print_header "🌐 Network and DNS Validation"

# DNS resolution
run_check "Main domain resolves" "nslookup certrats.com"
run_check "WWW domain resolves" "nslookup www.certrats.com"
run_check "API domain resolves" "nslookup api.certrats.com"

# SSL certificates
run_check "SSL certificate valid for main domain" "echo | openssl s_client -servername certrats.com -connect certrats.com:443 2>/dev/null | openssl x509 -noout -dates"
run_check "SSL certificate valid for API domain" "echo | openssl s_client -servername api.certrats.com -connect api.certrats.com:443 2>/dev/null | openssl x509 -noout -dates"

# Load balancer health
run_check "Load balancer health check" "kubectl get service nginx-service -n certrats -o jsonpath='{.status.loadBalancer.ingress[0].ip}' | grep -E '^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$'"

# =============================================================================
# APPLICATION FUNCTIONALITY CHECKS
# =============================================================================

print_header "🔧 Application Functionality Validation"

# Health endpoints
run_check "Main site accessible" "check_url 'https://certrats.com' 200"
run_check "API health endpoint" "check_url 'https://api.certrats.com/api/v1/health' 200"
run_check "Frontend health endpoint" "check_url 'https://certrats.com/api/health' 200"

# API functionality
run_check "Certifications API endpoint" "check_api 'https://api.certrats.com/api/v1/certifications'"
run_check "Career path API endpoint" "check_api 'https://api.certrats.com/api/v1/career-path' 'POST' '{\"current_role\":\"IT Support\",\"target_role\":\"Security Analyst\",\"years_experience\":2}'"

# Database connectivity
run_check "Database connection from backend" "kubectl exec -it \$(kubectl get pods -l app=backend -n certrats -o jsonpath='{.items[0].metadata.name}') -n certrats -- python -c 'import psycopg2; conn = psycopg2.connect(\"*********************************************************/certrats\"); print(\"Connected\")'"

# Cache connectivity
run_check "Redis connection from backend" "kubectl exec -it \$(kubectl get pods -l app=redis-cluster -n certrats -o jsonpath='{.items[0].metadata.name}') -n certrats -- redis-cli ping"

# =============================================================================
# SECURITY CHECKS
# =============================================================================

print_header "🔒 Security Validation"

# SSL/TLS configuration
run_check "HTTPS redirect working" "curl -s -I http://certrats.com | grep -q 'Location: https://'"
run_check "Security headers present" "curl -s -I https://certrats.com | grep -q 'X-Content-Type-Options'"
run_check "HSTS header present" "curl -s -I https://certrats.com | grep -q 'Strict-Transport-Security'"

# Network policies
run_check "Network policies configured" "kubectl get networkpolicy -n certrats"
run_check "Pod security policies active" "kubectl get podsecuritypolicy" false

# Secrets management
run_check "Application secrets exist" "kubectl get secret certrats-secrets -n certrats"
run_check "TLS secrets exist" "kubectl get secret certrats-tls -n certrats"

# =============================================================================
# MONITORING AND OBSERVABILITY CHECKS
# =============================================================================

print_header "📊 Monitoring and Observability Validation"

# Monitoring stack
run_check "Prometheus running" "kubectl get pods -l app=prometheus -n certrats-monitoring | grep Running"
run_check "Grafana running" "kubectl get pods -l app=grafana -n certrats-monitoring | grep Running"
run_check "AlertManager running" "kubectl get pods -l app=alertmanager -n certrats-monitoring | grep Running" false

# Monitoring endpoints
run_check "Prometheus metrics accessible" "check_url 'http://prometheus-service.certrats-monitoring.svc.cluster.local:9090/metrics' 200" false
run_check "Application metrics endpoint" "check_url 'https://api.certrats.com/metrics' 200" false

# Logging
run_check "Application logs available" "kubectl logs -l app=backend -n certrats --tail=1"
run_check "Error tracking functional" "kubectl logs -l app=backend -n certrats | grep -q 'INFO\\|ERROR\\|WARN'"

# =============================================================================
# BACKUP AND DISASTER RECOVERY CHECKS
# =============================================================================

print_header "💾 Backup and Disaster Recovery Validation"

# Backup infrastructure
run_check "Velero installed" "kubectl get pods -n velero | grep Running" false
run_check "Backup storage configured" "velero backup-location get" false
run_check "Recent backups available" "velero backup get | grep Completed" false

# Database backups
run_check "Database backup job configured" "kubectl get cronjob postgres-backup -n certrats" false
run_check "Redis backup job configured" "kubectl get cronjob redis-backup -n certrats" false

# =============================================================================
# PERFORMANCE CHECKS
# =============================================================================

print_header "⚡ Performance Validation"

# Response time checks
print_check "API response time validation"
API_RESPONSE_TIME=$(curl -w '%{time_total}' -s -o /dev/null https://api.certrats.com/api/v1/health)
if (( $(echo "$API_RESPONSE_TIME < 2.0" | bc -l) )); then
    print_success "API response time: ${API_RESPONSE_TIME}s (< 2s target)"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    print_warning "API response time: ${API_RESPONSE_TIME}s (> 2s target)"
    WARNING_CHECKS=$((WARNING_CHECKS + 1))
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

# Resource utilization
run_check "CPU utilization reasonable" "kubectl top nodes | awk 'NR>1 {print \$3}' | sed 's/%//' | awk '{if(\$1>80) exit 1}'" false
run_check "Memory utilization reasonable" "kubectl top nodes | awk 'NR>1 {print \$5}' | sed 's/%//' | awk '{if(\$1>80) exit 1}'" false

# Auto-scaling configuration
run_check "HPA configured for backend" "kubectl get hpa backend-hpa -n certrats"
run_check "HPA configured for frontend" "kubectl get hpa frontend-hpa -n certrats"

# =============================================================================
# BUSINESS FUNCTIONALITY CHECKS
# =============================================================================

print_header "💼 Business Functionality Validation"

# Core business features
run_check "User can browse certifications" "check_api 'https://api.certrats.com/api/v1/certifications'"
run_check "Career path generation works" "check_api 'https://api.certrats.com/api/v1/career-path' 'POST' '{\"current_role\":\"Developer\",\"target_role\":\"Security Engineer\",\"years_experience\":3}'"

# Data integrity
run_check "Certification data loaded" "curl -s 'https://api.certrats.com/api/v1/certifications' | jq 'length' | awk '{if(\$1>0) exit 0; else exit 1}'"

# =============================================================================
# COMPLIANCE AND DOCUMENTATION CHECKS
# =============================================================================

print_header "📋 Compliance and Documentation Validation"

# Documentation
run_check "Deployment guide exists" "test -f docs/DEPLOYMENT_GUIDE.md"
run_check "Operations runbook exists" "test -f docs/OPERATIONS_RUNBOOK.md"
run_check "Security hardening guide exists" "test -f docs/SECURITY_HARDENING.md"

# Configuration management
run_check "Environment variables configured" "kubectl get configmap certrats-config -n certrats"
run_check "Resource limits defined" "kubectl get deployment backend -n certrats -o yaml | grep -q 'limits:'"

# =============================================================================
# FINAL VALIDATION AND LOAD TESTING
# =============================================================================

print_header "🧪 Final Validation and Load Testing"

# Load testing (if k6 is available)
if command -v k6 &> /dev/null; then
    print_check "Running basic load test"
    if k6 run --duration 30s --vus 10 scripts/load-testing.js >/dev/null 2>&1; then
        print_success "Basic load test passed"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        print_warning "Basic load test failed or had issues"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
    fi
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
else
    print_warning "k6 not available, skipping load test"
fi

# End-to-end user journey
print_check "End-to-end user journey test"
if curl -s https://certrats.com >/dev/null && \
   curl -s https://api.certrats.com/api/v1/certifications >/dev/null && \
   curl -s -X POST -H "Content-Type: application/json" \
   -d '{"current_role":"IT Support","target_role":"Security Analyst","years_experience":2}' \
   https://api.certrats.com/api/v1/career-path >/dev/null; then
    print_success "End-to-end user journey test"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    print_error "End-to-end user journey test"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

# =============================================================================
# SUMMARY AND RECOMMENDATIONS
# =============================================================================

print_header "📊 Go-Live Checklist Summary"

echo "Total Checks: $TOTAL_CHECKS"
echo "✅ Passed: $PASSED_CHECKS"
echo "⚠️ Warnings: $WARNING_CHECKS"
echo "❌ Failed: $FAILED_CHECKS"
echo ""

# Calculate success rate
SUCCESS_RATE=$(( (PASSED_CHECKS + WARNING_CHECKS) * 100 / TOTAL_CHECKS ))
echo "Success Rate: $SUCCESS_RATE%"
echo ""

# Determine go-live readiness
if [[ $FAILED_CHECKS -eq 0 && $SUCCESS_RATE -ge 90 ]]; then
    print_success "🎉 READY FOR GO-LIVE!"
    echo ""
    echo "✅ All critical checks passed"
    echo "✅ Success rate above 90%"
    echo "✅ No critical failures detected"
    echo ""
    echo "🚀 Recommended Actions:"
    echo "1. Notify stakeholders of go-live readiness"
    echo "2. Schedule go-live window"
    echo "3. Prepare monitoring dashboards"
    echo "4. Brief support team on procedures"
    echo "5. Execute go-live plan"
    
elif [[ $FAILED_CHECKS -eq 0 && $SUCCESS_RATE -ge 80 ]]; then
    print_warning "⚠️ READY FOR GO-LIVE WITH CAUTION"
    echo ""
    echo "✅ No critical failures"
    echo "⚠️ Some warnings present"
    echo "⚠️ Success rate between 80-90%"
    echo ""
    echo "🔧 Recommended Actions:"
    echo "1. Review and address warnings"
    echo "2. Consider limited rollout"
    echo "3. Increase monitoring during launch"
    echo "4. Have rollback plan ready"
    
else
    print_error "❌ NOT READY FOR GO-LIVE"
    echo ""
    echo "❌ Critical failures detected"
    echo "❌ Success rate below 80%"
    echo ""
    echo "🛠️ Required Actions:"
    echo "1. Address all critical failures"
    echo "2. Re-run checklist after fixes"
    echo "3. Consider additional testing"
    echo "4. Review deployment procedures"
fi

echo ""
print_header "🎯 Next Steps"

if [[ $FAILED_CHECKS -eq 0 && $SUCCESS_RATE -ge 90 ]]; then
    echo "1. 📋 Final stakeholder approval"
    echo "2. 📅 Schedule go-live window"
    echo "3. 👥 Brief operations team"
    echo "4. 📊 Prepare monitoring dashboards"
    echo "5. 🚀 Execute go-live plan"
    echo "6. 📈 Monitor post-launch metrics"
    echo "7. 🎉 Celebrate successful launch!"
else
    echo "1. 🔧 Address failed checks and warnings"
    echo "2. 🧪 Re-run validation tests"
    echo "3. 📝 Update documentation as needed"
    echo "4. 🔄 Repeat go-live checklist"
    echo "5. 👥 Review with team before proceeding"
fi

echo ""
echo "📝 Detailed logs and reports available in:"
echo "- Kubernetes events: kubectl get events --all-namespaces"
echo "- Application logs: kubectl logs -l app=backend -n certrats"
echo "- Monitoring dashboards: https://monitoring.certrats.com"
echo ""

# Generate report file
REPORT_FILE="go-live-checklist-$(date +%Y%m%d_%H%M%S).txt"
{
    echo "CertRats Go-Live Checklist Report"
    echo "Generated: $(date)"
    echo "================================"
    echo ""
    echo "Summary:"
    echo "Total Checks: $TOTAL_CHECKS"
    echo "Passed: $PASSED_CHECKS"
    echo "Warnings: $WARNING_CHECKS"
    echo "Failed: $FAILED_CHECKS"
    echo "Success Rate: $SUCCESS_RATE%"
    echo ""
    if [[ $FAILED_CHECKS -eq 0 && $SUCCESS_RATE -ge 90 ]]; then
        echo "Status: READY FOR GO-LIVE ✅"
    elif [[ $FAILED_CHECKS -eq 0 && $SUCCESS_RATE -ge 80 ]]; then
        echo "Status: READY WITH CAUTION ⚠️"
    else
        echo "Status: NOT READY ❌"
    fi
} > "$REPORT_FILE"

print_success "Report saved to: $REPORT_FILE"

# Exit with appropriate code
if [[ $FAILED_CHECKS -eq 0 ]]; then
    exit 0
else
    exit 1
fi
