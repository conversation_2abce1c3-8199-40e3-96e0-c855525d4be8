#!/usr/bin/env python3
"""
CertRats Performance Optimization Script
Automated performance tuning and optimization
"""

import os
import sys
import time
import json
import logging
import asyncio
import aiohttp
import psutil
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('performance_optimization.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure"""
    timestamp: datetime
    response_time: float
    throughput: float
    error_rate: float
    cpu_usage: float
    memory_usage: float
    database_connections: int
    cache_hit_ratio: float


class PerformanceOptimizer:
    """Main performance optimization class"""
    
    def __init__(self, config_file: str = "performance_config.json"):
        self.config = self.load_config(config_file)
        self.metrics_history: List[PerformanceMetrics] = []
        self.optimization_actions: List[str] = []
    
    def load_config(self, config_file: str) -> Dict[str, Any]:
        """Load performance optimization configuration"""
        default_config = {
            "api_endpoint": "http://localhost:8000",
            "prometheus_endpoint": "http://localhost:9090",
            "optimization_thresholds": {
                "response_time_ms": 2000,
                "error_rate_percent": 5.0,
                "cpu_usage_percent": 80.0,
                "memory_usage_percent": 85.0,
                "cache_hit_ratio_percent": 80.0
            },
            "optimization_actions": {
                "scale_up_threshold": 0.8,
                "scale_down_threshold": 0.3,
                "cache_optimization": True,
                "database_optimization": True,
                "auto_scaling": True
            },
            "monitoring_interval": 60,
            "optimization_interval": 300
        }
        
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    # Merge with defaults
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                return config
            else:
                logger.warning(f"Config file {config_file} not found, using defaults")
                return default_config
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return default_config
    
    async def collect_metrics(self) -> PerformanceMetrics:
        """Collect current performance metrics"""
        try:
            # Collect API response time
            response_time = await self.measure_api_response_time()
            
            # Collect system metrics
            cpu_usage = psutil.cpu_percent(interval=1)
            memory_usage = psutil.virtual_memory().percent
            
            # Collect application metrics from Prometheus
            app_metrics = await self.get_prometheus_metrics()
            
            metrics = PerformanceMetrics(
                timestamp=datetime.now(),
                response_time=response_time,
                throughput=app_metrics.get('throughput', 0),
                error_rate=app_metrics.get('error_rate', 0),
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                database_connections=app_metrics.get('db_connections', 0),
                cache_hit_ratio=app_metrics.get('cache_hit_ratio', 0)
            )
            
            self.metrics_history.append(metrics)
            
            # Keep only last 24 hours of metrics
            cutoff_time = datetime.now() - timedelta(hours=24)
            self.metrics_history = [
                m for m in self.metrics_history 
                if m.timestamp > cutoff_time
            ]
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
            return None
    
    async def measure_api_response_time(self) -> float:
        """Measure API response time"""
        try:
            start_time = time.time()
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.config['api_endpoint']}/api/v1/health",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    await response.text()
                    
            end_time = time.time()
            return (end_time - start_time) * 1000  # Convert to milliseconds
            
        except Exception as e:
            logger.error(f"Error measuring API response time: {e}")
            return 9999  # Return high value on error
    
    async def get_prometheus_metrics(self) -> Dict[str, float]:
        """Get metrics from Prometheus"""
        try:
            metrics = {}
            
            async with aiohttp.ClientSession() as session:
                # Query throughput (requests per second)
                throughput_query = 'rate(http_requests_total[5m])'
                throughput = await self.query_prometheus(session, throughput_query)
                metrics['throughput'] = throughput
                
                # Query error rate
                error_query = 'rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) * 100'
                error_rate = await self.query_prometheus(session, error_query)
                metrics['error_rate'] = error_rate
                
                # Query database connections
                db_query = 'pg_stat_database_numbackends'
                db_connections = await self.query_prometheus(session, db_query)
                metrics['db_connections'] = db_connections
                
                # Query cache hit ratio
                cache_query = 'redis_keyspace_hits_total / (redis_keyspace_hits_total + redis_keyspace_misses_total) * 100'
                cache_hit_ratio = await self.query_prometheus(session, cache_query)
                metrics['cache_hit_ratio'] = cache_hit_ratio
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting Prometheus metrics: {e}")
            return {}
    
    async def query_prometheus(self, session: aiohttp.ClientSession, query: str) -> float:
        """Query Prometheus for a specific metric"""
        try:
            url = f"{self.config['prometheus_endpoint']}/api/v1/query"
            params = {'query': query}
            
            async with session.get(url, params=params) as response:
                data = await response.json()
                
                if data['status'] == 'success' and data['data']['result']:
                    return float(data['data']['result'][0]['value'][1])
                else:
                    return 0.0
                    
        except Exception as e:
            logger.error(f"Error querying Prometheus: {e}")
            return 0.0
    
    def analyze_performance(self, metrics: PerformanceMetrics) -> List[str]:
        """Analyze performance metrics and suggest optimizations"""
        suggestions = []
        thresholds = self.config['optimization_thresholds']
        
        # Check response time
        if metrics.response_time > thresholds['response_time_ms']:
            suggestions.append(f"High response time: {metrics.response_time:.2f}ms")
            suggestions.append("Consider: scaling up, optimizing queries, adding caching")
        
        # Check error rate
        if metrics.error_rate > thresholds['error_rate_percent']:
            suggestions.append(f"High error rate: {metrics.error_rate:.2f}%")
            suggestions.append("Consider: checking logs, fixing bugs, improving error handling")
        
        # Check CPU usage
        if metrics.cpu_usage > thresholds['cpu_usage_percent']:
            suggestions.append(f"High CPU usage: {metrics.cpu_usage:.2f}%")
            suggestions.append("Consider: horizontal scaling, code optimization")
        
        # Check memory usage
        if metrics.memory_usage > thresholds['memory_usage_percent']:
            suggestions.append(f"High memory usage: {metrics.memory_usage:.2f}%")
            suggestions.append("Consider: memory optimization, garbage collection tuning")
        
        # Check cache hit ratio
        if metrics.cache_hit_ratio < thresholds['cache_hit_ratio_percent']:
            suggestions.append(f"Low cache hit ratio: {metrics.cache_hit_ratio:.2f}%")
            suggestions.append("Consider: cache warming, TTL optimization, cache key optimization")
        
        return suggestions
    
    async def apply_optimizations(self, metrics: PerformanceMetrics) -> List[str]:
        """Apply automatic optimizations based on metrics"""
        actions = []
        
        if not self.config['optimization_actions']['auto_scaling']:
            return actions
        
        try:
            # Auto-scaling based on CPU and memory
            if (metrics.cpu_usage > 80 or metrics.memory_usage > 85):
                action = await self.scale_up_application()
                if action:
                    actions.append(action)
            
            elif (metrics.cpu_usage < 30 and metrics.memory_usage < 40):
                action = await self.scale_down_application()
                if action:
                    actions.append(action)
            
            # Cache optimization
            if (self.config['optimization_actions']['cache_optimization'] and 
                metrics.cache_hit_ratio < 80):
                action = await self.optimize_cache()
                if action:
                    actions.append(action)
            
            # Database optimization
            if (self.config['optimization_actions']['database_optimization'] and 
                metrics.database_connections > 80):
                action = await self.optimize_database()
                if action:
                    actions.append(action)
            
        except Exception as e:
            logger.error(f"Error applying optimizations: {e}")
        
        return actions
    
    async def scale_up_application(self) -> Optional[str]:
        """Scale up the application"""
        try:
            # Scale backend
            result = subprocess.run([
                'kubectl', 'scale', 'deployment', 'backend', 
                '--replicas=5', '-n', 'certrats'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("Scaled up backend deployment")
                return "Scaled up backend to 5 replicas"
            else:
                logger.error(f"Failed to scale up backend: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Error scaling up application: {e}")
            return None
    
    async def scale_down_application(self) -> Optional[str]:
        """Scale down the application"""
        try:
            # Scale backend (minimum 2 replicas)
            result = subprocess.run([
                'kubectl', 'scale', 'deployment', 'backend', 
                '--replicas=2', '-n', 'certrats'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("Scaled down backend deployment")
                return "Scaled down backend to 2 replicas"
            else:
                logger.error(f"Failed to scale down backend: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Error scaling down application: {e}")
            return None
    
    async def optimize_cache(self) -> Optional[str]:
        """Optimize cache configuration"""
        try:
            # Restart Redis to clear cache and reload configuration
            result = subprocess.run([
                'kubectl', 'rollout', 'restart', 'deployment', 'redis', 
                '-n', 'certrats'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("Restarted Redis for cache optimization")
                return "Optimized cache configuration"
            else:
                logger.error(f"Failed to optimize cache: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Error optimizing cache: {e}")
            return None
    
    async def optimize_database(self) -> Optional[str]:
        """Optimize database configuration"""
        try:
            # This would typically involve running database optimization queries
            # For now, we'll just log the action
            logger.info("Database optimization triggered")
            return "Applied database optimization settings"
            
        except Exception as e:
            logger.error(f"Error optimizing database: {e}")
            return None
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate performance report"""
        if not self.metrics_history:
            return {"error": "No metrics available"}
        
        recent_metrics = self.metrics_history[-10:]  # Last 10 measurements
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "metrics_count": len(self.metrics_history),
            "current_metrics": {
                "response_time_ms": recent_metrics[-1].response_time,
                "throughput_rps": recent_metrics[-1].throughput,
                "error_rate_percent": recent_metrics[-1].error_rate,
                "cpu_usage_percent": recent_metrics[-1].cpu_usage,
                "memory_usage_percent": recent_metrics[-1].memory_usage,
                "cache_hit_ratio_percent": recent_metrics[-1].cache_hit_ratio
            },
            "averages_last_10": {
                "response_time_ms": sum(m.response_time for m in recent_metrics) / len(recent_metrics),
                "throughput_rps": sum(m.throughput for m in recent_metrics) / len(recent_metrics),
                "error_rate_percent": sum(m.error_rate for m in recent_metrics) / len(recent_metrics),
                "cpu_usage_percent": sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics),
                "memory_usage_percent": sum(m.memory_usage for m in recent_metrics) / len(recent_metrics),
                "cache_hit_ratio_percent": sum(m.cache_hit_ratio for m in recent_metrics) / len(recent_metrics)
            },
            "optimization_actions": self.optimization_actions[-10:],  # Last 10 actions
            "recommendations": self.analyze_performance(recent_metrics[-1])
        }
        
        return report
    
    async def run_optimization_cycle(self):
        """Run a single optimization cycle"""
        logger.info("Starting optimization cycle")
        
        # Collect metrics
        metrics = await self.collect_metrics()
        if not metrics:
            logger.error("Failed to collect metrics")
            return
        
        logger.info(f"Collected metrics: RT={metrics.response_time:.2f}ms, "
                   f"CPU={metrics.cpu_usage:.1f}%, MEM={metrics.memory_usage:.1f}%, "
                   f"Cache={metrics.cache_hit_ratio:.1f}%")
        
        # Analyze performance
        suggestions = self.analyze_performance(metrics)
        if suggestions:
            logger.info(f"Performance suggestions: {suggestions}")
        
        # Apply optimizations
        actions = await self.apply_optimizations(metrics)
        if actions:
            logger.info(f"Applied optimizations: {actions}")
            self.optimization_actions.extend(actions)
        
        # Generate report
        report = self.generate_performance_report()
        
        # Save report
        report_file = f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Performance report saved to {report_file}")
    
    async def run_continuous_optimization(self):
        """Run continuous performance optimization"""
        logger.info("Starting continuous performance optimization")
        
        try:
            while True:
                await self.run_optimization_cycle()
                
                # Wait for next cycle
                await asyncio.sleep(self.config['optimization_interval'])
                
        except KeyboardInterrupt:
            logger.info("Optimization stopped by user")
        except Exception as e:
            logger.error(f"Error in continuous optimization: {e}")


async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='CertRats Performance Optimization')
    parser.add_argument('--config', default='performance_config.json', 
                       help='Configuration file path')
    parser.add_argument('--once', action='store_true', 
                       help='Run optimization once instead of continuously')
    parser.add_argument('--report-only', action='store_true', 
                       help='Generate report only, no optimizations')
    
    args = parser.parse_args()
    
    optimizer = PerformanceOptimizer(args.config)
    
    if args.report_only:
        # Just collect metrics and generate report
        metrics = await optimizer.collect_metrics()
        if metrics:
            report = optimizer.generate_performance_report()
            print(json.dumps(report, indent=2))
    elif args.once:
        # Run optimization once
        await optimizer.run_optimization_cycle()
    else:
        # Run continuous optimization
        await optimizer.run_continuous_optimization()


if __name__ == "__main__":
    asyncio.run(main())
