#!/usr/bin/env python3
"""
Script to set up and verify PostgreSQL test database configuration
"""
import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

from tests.test_db_utils import verify_test_setup, cleanup_test_databases, wait_for_postgres

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Main function to set up and verify test database"""
    print("🔧 Setting up PostgreSQL test database...")
    
    # Check if we're in Docker environment
    in_docker = os.path.exists('/.dockerenv')
    if in_docker:
        print("📦 Running in Docker environment")
        # In Docker, PostgreSQL should be available at certrats_db
        os.environ.setdefault('TEST_DATABASE_URL', '***********************************************')
    else:
        print("💻 Running in local environment")
        # Locally, PostgreSQL should be available at localhost
        os.environ.setdefault('TEST_DATABASE_URL', 'postgresql://postgres:postgres@localhost:5432')
    
    print(f"🔗 Using database URL: {os.environ.get('TEST_DATABASE_URL')}")
    
    # Wait for PostgreSQL to be available
    print("⏳ Waiting for PostgreSQL to be available...")
    if not wait_for_postgres():
        print("❌ PostgreSQL is not available. Please ensure PostgreSQL is running.")
        return False
    
    print("✅ PostgreSQL is available")
    
    # Clean up any existing test databases
    print("🧹 Cleaning up existing test databases...")
    cleanup_test_databases()
    
    # Verify test setup
    print("🔍 Verifying test database setup...")
    if verify_test_setup():
        print("✅ Test database setup is working correctly!")
        print("\n📋 Next steps:")
        print("   1. Run tests with: pytest tests/")
        print("   2. Or run specific test: pytest tests/test_certification_explorer.py")
        print("   3. Tests will automatically create and clean up test databases")
        return True
    else:
        print("❌ Test database setup has issues")
        print("\n🔧 Troubleshooting:")
        print("   1. Ensure PostgreSQL is running")
        print("   2. Check database credentials")
        print("   3. Verify network connectivity")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
