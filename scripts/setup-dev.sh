#!/bin/bash

# Development Environment Setup Script
# This script sets up the development environment for CertRats

set -e  # Exit on any error

echo "🚀 Setting up CertRats Development Environment"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running in correct directory
if [ ! -f "package.json" ] || [ ! -f "requirements.txt" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Check prerequisites
print_status "Checking prerequisites..."

# Check Python
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is required but not installed"
    exit 1
fi

# Check Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js is required but not installed"
    exit 1
fi

# Check Docker
if ! command -v docker &> /dev/null; then
    print_warning "Docker is not installed. Some features may not work."
fi

# Check PostgreSQL
if ! command -v psql &> /dev/null; then
    print_warning "PostgreSQL client is not installed. Using Docker for database."
fi

print_success "Prerequisites check completed"

# Create virtual environment
print_status "Setting up Python virtual environment..."
if [ ! -d "venv" ]; then
    python3 -m venv venv
    print_success "Virtual environment created"
else
    print_warning "Virtual environment already exists"
fi

# Activate virtual environment
source venv/bin/activate
print_success "Virtual environment activated"

# Install Python dependencies
print_status "Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements.txt
print_success "Python dependencies installed"

# Install Node.js dependencies
print_status "Installing Node.js dependencies..."
npm install
print_success "Node.js dependencies installed"

# Install frontend dependencies (React app)
if [ -d "frontend" ]; then
    print_status "Installing React frontend dependencies..."
    cd frontend
    npm install
    cd ..
    print_success "React frontend dependencies installed"
fi

# Create environment file
print_status "Setting up environment configuration..."
if [ ! -f ".env.local" ]; then
    cp .env.example .env.local
    print_success "Environment file created (.env.local)"
    print_warning "Please update .env.local with your actual configuration values"
else
    print_warning "Environment file already exists (.env.local)"
fi

# Setup database
print_status "Setting up database..."
if command -v docker &> /dev/null; then
    print_status "Starting PostgreSQL with Docker..."
    docker-compose -f docker/docker-compose.dev.yml up -d postgres redis
    
    # Wait for database to be ready
    print_status "Waiting for database to be ready..."
    sleep 10
    
    # Run database migrations
    print_status "Running database migrations..."
    export DATABASE_URL="postgresql://certrats_user:certrats_password@localhost:5432/certrats_dev"
    python -m alembic upgrade head
    
    print_success "Database setup completed"
else
    print_warning "Docker not available. Please setup PostgreSQL manually."
    print_warning "Update DATABASE_URL in .env.local with your PostgreSQL connection string"
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p logs
mkdir -p data
mkdir -p uploads
print_success "Directories created"

# Setup pre-commit hooks (if available)
if command -v pre-commit &> /dev/null; then
    print_status "Setting up pre-commit hooks..."
    pre-commit install
    print_success "Pre-commit hooks installed"
fi

# Run initial tests
print_status "Running initial tests..."
if python -m pytest tests/ --tb=short -q; then
    print_success "Initial tests passed"
else
    print_warning "Some tests failed. This is normal for initial setup."
fi

# Build frontend
print_status "Building Next.js frontend..."
if npm run build; then
    print_success "Frontend build completed"
else
    print_warning "Frontend build failed. Check for errors above."
fi

echo ""
echo "🎉 Development Environment Setup Complete!"
echo "=========================================="
echo ""
echo "Next steps:"
echo "1. Update .env.local with your configuration"
echo "2. Start the development servers:"
echo "   - Backend: python -m uvicorn api.app:app --reload"
echo "   - Frontend: npm run dev"
echo "   - Or use Docker: docker-compose -f docker/docker-compose.dev.yml up"
echo ""
echo "3. Access the application:"
echo "   - Frontend: http://localhost:3000"
echo "   - Backend API: http://localhost:8000"
echo "   - API Docs: http://localhost:8000/docs"
echo ""
echo "4. Run tests: make test"
echo ""
print_success "Happy coding! 🚀"
