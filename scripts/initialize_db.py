"""
Database initialization and seeding script
"""
import os
import sys
from pathlib import Path
import logging

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from database import init_db, get_db
from data.seed import seed_database
from models.certification import Base
from models.job import SecurityJob
from sqlalchemy import text

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def initialize_database():
    """
    Initialize the database with the current schema and seed data
    """
    try:
        # Ensure DATABASE_URL is set
        if not os.environ.get('DATABASE_URL'):
            raise ValueError("DATABASE_URL environment variable is not set")

        logger.info("Starting database initialization")

        # Initialize database schema
        init_db()
        logger.info("Database schema initialized successfully")

        # Seed initial data
        seed_database()
        logger.info("Database seeded successfully")

        # Verify the setup using parameterized queries
        db = next(get_db())
        try:
            # Check if tables exist and have data using parameterized queries
            # Note: Using text() with bind parameters for safety
            result = db.execute(
                text("SELECT COUNT(*) FROM organizations")
            ).scalar()
            logger.info(f"Found {result} organizations")

            # Query certifications with parameterization
            cert_query = text("SELECT COUNT(*) FROM certifications WHERE is_deleted = :is_deleted")
            result = db.execute(cert_query, {"is_deleted": False}).scalar()
            logger.info(f"Found {result} active certifications")

            # Query security jobs with parameterization
            jobs_query = text("""
                SELECT COUNT(*) 
                FROM security_jobs 
                WHERE deleted_at IS NULL 
                AND domain = :domain
            """)
            for domain in ['Security Operations', 'Security Engineering']:
                result = db.execute(jobs_query, {"domain": domain}).scalar()
                logger.info(f"Found {result} jobs in {domain}")

            logger.info("Database initialization completed successfully")
        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error initializing database: {e}", exc_info=True)
        raise

if __name__ == "__main__":
    initialize_database()