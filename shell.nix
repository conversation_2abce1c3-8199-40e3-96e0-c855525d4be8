# shell.nix
{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Python environment
    python311
    python311Packages.pip
    python311Packages.virtualenv
    
    # Database
    postgresql_14
    
    # Docker tools
    docker
    docker-compose
    
    # Node.js environment
    nodejs_20
    nodePackages.pnpm
  ];

  shellHook = ''
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
      virtualenv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Install Python dependencies if needed
    if [ ! -f "venv/.requirements_installed" ]; then
      pip install -r .dockerwrapper/requirements.txt
      touch venv/.requirements_installed
    fi
    
    # Set environment variables
    export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/certrats"
    export NEXT_PUBLIC_API_URL="http://localhost:3001/api/v1"
    
    echo "Development environment ready!"
  '';
} 