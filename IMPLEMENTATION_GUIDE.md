# CertRats Implementation Guide - Detailed Development Plan

## 🎯 **PHASE 1: USER AUTHENTICATION & PERSONALIZATION (Weeks 1-4)**

### **Backend Authentication Implementation**

#### **1. JWT Authentication System**
```python
# api/auth/jwt_handler.py
from datetime import datetime, timedelta
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status

class JWTHandler:
    SECRET_KEY = "your-secret-key"  # Use environment variable
    ALGORITHM = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES = 30
    REFRESH_TOKEN_EXPIRE_DAYS = 7
    
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    @classmethod
    def create_access_token(cls, data: dict):
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=cls.ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode.update({"exp": expire, "type": "access"})
        return jwt.encode(to_encode, cls.SECRET_KEY, algorithm=cls.ALGORITHM)
    
    @classmethod
    def create_refresh_token(cls, data: dict):
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=cls.REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire, "type": "refresh"})
        return jwt.encode(to_encode, cls.SECRET_KEY, algorithm=cls.ALGORITHM)
```

#### **2. User Model Enhancement**
```python
# models/user.py
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text
from sqlalchemy.dialects.postgresql import ARRAY
from models.base import Base
import bcrypt

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    name = Column(String(255), nullable=False)
    
    # Profile Information
    current_role = Column(String(255))
    target_role = Column(String(255))
    years_experience = Column(Integer, default=0)
    learning_style = Column(String(50), default="Mixed")
    study_hours_per_week = Column(Integer, default=10)
    
    # Preferences
    interests = Column(ARRAY(String), default=[])
    completed_certifications = Column(ARRAY(String), default=[])
    preferred_providers = Column(ARRAY(String), default=[])
    budget_range = Column(String(50))
    
    # Account Status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    email_verified_at = Column(DateTime)
    last_login = Column(DateTime)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def verify_password(self, password: str) -> bool:
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))
    
    @staticmethod
    def hash_password(password: str) -> str:
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
```

#### **3. Authentication Endpoints**
```python
# api/endpoints/auth.py
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from api.deps import get_db
from models.user import User
from schemas.auth import UserCreate, UserLogin, Token
from api.auth.jwt_handler import JWTHandler

router = APIRouter(prefix="/auth", tags=["authentication"])
security = HTTPBearer()

@router.post("/register", response_model=Token)
async def register(user_data: UserCreate, db: Session = Depends(get_db)):
    # Check if user exists
    existing_user = db.query(User).filter(User.email == user_data.email).first()
    if existing_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Create new user
    hashed_password = User.hash_password(user_data.password)
    user = User(
        email=user_data.email,
        password_hash=hashed_password,
        name=user_data.name
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    
    # Generate tokens
    access_token = JWTHandler.create_access_token({"sub": str(user.id)})
    refresh_token = JWTHandler.create_refresh_token({"sub": str(user.id)})
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }

@router.post("/login", response_model=Token)
async def login(user_data: UserLogin, db: Session = Depends(get_db)):
    user = db.query(User).filter(User.email == user_data.email).first()
    if not user or not user.verify_password(user_data.password):
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    # Update last login
    user.last_login = datetime.utcnow()
    db.commit()
    
    # Generate tokens
    access_token = JWTHandler.create_access_token({"sub": str(user.id)})
    refresh_token = JWTHandler.create_refresh_token({"sub": str(user.id)})
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }
```

### **Frontend Authentication Implementation**

#### **1. Authentication Context**
```typescript
// src/contexts/AuthContext.tsx
'use client';
import React, { createContext, useContext, useState, useEffect } from 'react';

interface User {
  id: string;
  email: string;
  name: string;
  currentRole?: string;
  targetRole?: string;
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, name: string) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for existing token and validate
    const token = localStorage.getItem('access_token');
    if (token) {
      validateToken(token);
    } else {
      setIsLoading(false);
    }
  }, []);

  const validateToken = async (token: string) => {
    try {
      const response = await fetch('/api/v1/auth/me', {
        headers: { Authorization: `Bearer ${token}` }
      });
      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
      } else {
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
      }
    } catch (error) {
      console.error('Token validation failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    const response = await fetch('/api/v1/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });

    if (!response.ok) {
      throw new Error('Login failed');
    }

    const { access_token, refresh_token } = await response.json();
    localStorage.setItem('access_token', access_token);
    localStorage.setItem('refresh_token', refresh_token);
    
    await validateToken(access_token);
  };

  const register = async (email: string, password: string, name: string) => {
    const response = await fetch('/api/v1/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password, name })
    });

    if (!response.ok) {
      throw new Error('Registration failed');
    }

    const { access_token, refresh_token } = await response.json();
    localStorage.setItem('access_token', access_token);
    localStorage.setItem('refresh_token', refresh_token);
    
    await validateToken(access_token);
  };

  const logout = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, login, register, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
```

#### **2. Protected Route Component**
```typescript
// src/components/ProtectedRoute.tsx
'use client';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

export default function ProtectedRoute({ 
  children, 
  redirectTo = '/login' 
}: ProtectedRouteProps) {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !user) {
      router.push(redirectTo);
    }
  }, [user, isLoading, router, redirectTo]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return <>{children}</>;
}
```

## 🎯 **PHASE 2: ENHANCED CERTIFICATION DATABASE (Weeks 5-8)**

### **Database Schema Expansion**

#### **1. Enhanced Certification Model**
```python
# models/certification.py
from sqlalchemy import Column, Integer, String, Text, DECIMAL, DateTime, Boolean
from sqlalchemy.dialects.postgresql import ARRAY, JSONB
from models.base import Base

class Certification(Base):
    __tablename__ = "certifications"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Basic Information
    name = Column(String(255), nullable=False, index=True)
    provider = Column(String(255), nullable=False, index=True)
    provider_logo_url = Column(String(500))
    description = Column(Text)
    detailed_description = Column(Text)
    
    # Classification
    level = Column(String(50), index=True)  # beginner, intermediate, advanced
    domain = Column(String(100), index=True)  # network, cloud, application, etc.
    subdomain = Column(String(100))
    certification_type = Column(String(50))  # technical, management, compliance
    
    # Cost Information
    exam_cost = Column(DECIMAL(10,2))
    training_cost_min = Column(DECIMAL(10,2))
    training_cost_max = Column(DECIMAL(10,2))
    materials_cost = Column(DECIMAL(10,2))
    renewal_cost = Column(DECIMAL(10,2))
    
    # Requirements
    prerequisites = Column(ARRAY(String))
    experience_required = Column(String(100))
    education_required = Column(String(100))
    
    # Exam Details
    exam_duration = Column(Integer)  # minutes
    exam_questions = Column(Integer)
    passing_score = Column(Integer)
    exam_format = Column(String(100))  # multiple-choice, hands-on, etc.
    
    # Skills and Knowledge
    skills_covered = Column(ARRAY(String))
    knowledge_areas = Column(JSONB)
    job_roles = Column(ARRAY(String))
    
    # Validity and Maintenance
    validity_period = Column(Integer)  # months
    cpe_required = Column(Integer)  # continuing education credits
    renewal_requirements = Column(Text)
    
    # URLs and Resources
    official_url = Column(String(500))
    exam_guide_url = Column(String(500))
    training_urls = Column(ARRAY(String))
    
    # Metadata
    popularity_score = Column(DECIMAL(5,2))
    difficulty_rating = Column(DECIMAL(3,1))
    market_demand = Column(String(50))
    salary_impact = Column(DECIMAL(10,2))
    
    # Status
    is_active = Column(Boolean, default=True)
    is_retired = Column(Boolean, default=False)
    retirement_date = Column(DateTime)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

#### **2. Certification Relationships**
```python
# models/certification_relationships.py
from sqlalchemy import Column, Integer, String, ForeignKey, Table
from sqlalchemy.orm import relationship
from models.base import Base

# Many-to-many relationship for certification paths
certification_paths = Table(
    'certification_paths',
    Base.metadata,
    Column('prerequisite_id', Integer, ForeignKey('certifications.id')),
    Column('target_id', Integer, ForeignKey('certifications.id'))
)

# Many-to-many relationship for alternative certifications
certification_alternatives = Table(
    'certification_alternatives',
    Base.metadata,
    Column('certification_id', Integer, ForeignKey('certifications.id')),
    Column('alternative_id', Integer, ForeignKey('certifications.id'))
)

class CertificationProvider(Base):
    __tablename__ = "certification_providers"
    
    id = Column(Integer, primary_key=True)
    name = Column(String(255), unique=True, nullable=False)
    website = Column(String(500))
    logo_url = Column(String(500))
    description = Column(Text)
    headquarters = Column(String(255))
    founded_year = Column(Integer)
    
    # Relationships
    certifications = relationship("Certification", back_populates="provider_info")
```

### **Advanced Search and Filtering**

#### **1. Search Service**
```python
# utils/search_service.py
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from models.certification import Certification
from typing import List, Optional, Dict, Any

class CertificationSearchService:
    def __init__(self, db: Session):
        self.db = db
    
    def search_certifications(
        self,
        query: Optional[str] = None,
        providers: Optional[List[str]] = None,
        domains: Optional[List[str]] = None,
        levels: Optional[List[str]] = None,
        cost_min: Optional[float] = None,
        cost_max: Optional[float] = None,
        skills: Optional[List[str]] = None,
        job_roles: Optional[List[str]] = None,
        sort_by: str = "popularity",
        sort_order: str = "desc",
        page: int = 0,
        per_page: int = 20
    ) -> Dict[str, Any]:
        
        # Base query
        query_obj = self.db.query(Certification).filter(Certification.is_active == True)
        
        # Text search
        if query:
            search_filter = or_(
                Certification.name.ilike(f"%{query}%"),
                Certification.description.ilike(f"%{query}%"),
                func.array_to_string(Certification.skills_covered, ' ').ilike(f"%{query}%")
            )
            query_obj = query_obj.filter(search_filter)
        
        # Provider filter
        if providers:
            query_obj = query_obj.filter(Certification.provider.in_(providers))
        
        # Domain filter
        if domains:
            query_obj = query_obj.filter(Certification.domain.in_(domains))
        
        # Level filter
        if levels:
            query_obj = query_obj.filter(Certification.level.in_(levels))
        
        # Cost range filter
        if cost_min is not None:
            query_obj = query_obj.filter(Certification.exam_cost >= cost_min)
        if cost_max is not None:
            query_obj = query_obj.filter(Certification.exam_cost <= cost_max)
        
        # Skills filter
        if skills:
            for skill in skills:
                query_obj = query_obj.filter(
                    func.array_to_string(Certification.skills_covered, ' ').ilike(f"%{skill}%")
                )
        
        # Job roles filter
        if job_roles:
            for role in job_roles:
                query_obj = query_obj.filter(
                    func.array_to_string(Certification.job_roles, ' ').ilike(f"%{role}%")
                )
        
        # Sorting
        if sort_by == "popularity":
            order_col = Certification.popularity_score
        elif sort_by == "cost":
            order_col = Certification.exam_cost
        elif sort_by == "name":
            order_col = Certification.name
        elif sort_by == "difficulty":
            order_col = Certification.difficulty_rating
        else:
            order_col = Certification.created_at
        
        if sort_order == "desc":
            query_obj = query_obj.order_by(order_col.desc())
        else:
            query_obj = query_obj.order_by(order_col.asc())
        
        # Get total count
        total = query_obj.count()
        
        # Pagination
        certifications = query_obj.offset(page * per_page).limit(per_page).all()
        
        return {
            "certifications": certifications,
            "total": total,
            "page": page,
            "per_page": per_page,
            "total_pages": (total + per_page - 1) // per_page
        }
    
    def get_filter_options(self) -> Dict[str, List[str]]:
        """Get available filter options for the UI"""
        providers = self.db.query(Certification.provider).distinct().all()
        domains = self.db.query(Certification.domain).distinct().all()
        levels = self.db.query(Certification.level).distinct().all()
        
        return {
            "providers": [p[0] for p in providers if p[0]],
            "domains": [d[0] for d in domains if d[0]],
            "levels": [l[0] for l in levels if l[0]]
        }
```

## 🎯 **PHASE 3: ADVANCED AI FEATURES (Weeks 9-12)**

### **Enhanced AI Integration**

#### **1. Advanced Career Path Generator**
```python
# utils/advanced_career_ai.py
from typing import Dict, List, Any, Optional
import json
from utils.claude_assistant import ClaudeAssistant
from models.certification import Certification
from models.user import User
from sqlalchemy.orm import Session

class AdvancedCareerAI:
    def __init__(self, db: Session):
        self.db = db
        self.claude = ClaudeAssistant()
    
    def generate_personalized_path(
        self,
        user: User,
        target_role: str,
        timeline_months: int = 12,
        budget: Optional[float] = None,
        learning_preferences: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        
        # Get user's current certifications and experience
        user_context = self._build_user_context(user)
        
        # Get relevant certifications from database
        relevant_certs = self._get_relevant_certifications(
            current_role=user.current_role,
            target_role=target_role,
            user_interests=user.interests
        )
        
        # Build AI prompt with enhanced context
        prompt = self._build_enhanced_prompt(
            user_context=user_context,
            target_role=target_role,
            timeline_months=timeline_months,
            budget=budget,
            learning_preferences=learning_preferences,
            available_certifications=relevant_certs
        )
        
        # Generate AI response
        ai_response = self.claude.generate_career_path(prompt)
        
        # Post-process and enhance the response
        enhanced_path = self._enhance_career_path(ai_response, relevant_certs)
        
        # Add personalized study schedule
        study_schedule = self._generate_study_schedule(
            career_path=enhanced_path,
            user_availability=user.study_hours_per_week,
            learning_style=user.learning_style
        )
        
        return {
            "career_path": enhanced_path,
            "study_schedule": study_schedule,
            "personalization_score": self._calculate_personalization_score(user, enhanced_path),
            "success_probability": self._estimate_success_probability(user, enhanced_path),
            "roi_analysis": self._calculate_roi_analysis(enhanced_path, user.current_role, target_role)
        }
    
    def _build_enhanced_prompt(self, **kwargs) -> str:
        return f"""
        You are an expert cybersecurity career advisor with deep knowledge of certification paths,
        industry trends, and career progression strategies.
        
        User Context:
        - Current Role: {kwargs['user_context']['current_role']}
        - Experience: {kwargs['user_context']['years_experience']} years
        - Current Certifications: {kwargs['user_context']['completed_certifications']}
        - Interests: {kwargs['user_context']['interests']}
        - Learning Style: {kwargs['user_context']['learning_style']}
        - Study Hours/Week: {kwargs['user_context']['study_hours']}
        
        Target: {kwargs['target_role']}
        Timeline: {kwargs['timeline_months']} months
        Budget: ${kwargs['budget'] or 'No specific limit'}
        
        Available Certifications:
        {self._format_certifications_for_ai(kwargs['available_certifications'])}
        
        Please create a detailed, personalized certification roadmap that:
        1. Considers the user's current experience and certifications
        2. Provides a logical progression toward the target role
        3. Includes timeline estimates for each certification
        4. Considers budget constraints if specified
        5. Matches the user's learning style and availability
        6. Includes alternative paths and contingencies
        7. Provides specific study recommendations
        
        Format the response as a structured JSON with stages, timelines, costs, and detailed explanations.
        """
```

This implementation guide provides concrete, actionable steps for the next development phase. Each section includes working code examples that can be directly implemented to extend the CertRats platform.

The guide focuses on the three highest-priority areas:
1. **User Authentication** - Enabling personalization and user accounts
2. **Enhanced Database** - Expanding the certification catalog with rich metadata
3. **Advanced AI** - Improving the intelligence and personalization of recommendations

Would you like me to continue with more specific implementation details for any of these areas, or move on to other aspects of the development roadmap?
