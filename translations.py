"""
Internationalization support for the Security Certification Explorer
"""
import os
import babel.support
from flask_babel import Babel, gettext
import streamlit as st
from functools import wraps
from typing import Callable, Any

# Initialize Flask-Babel with a distinct name
flask_babel_instance = Babel()

# Language display names with flags
LANGUAGE_DISPLAY = {
    'en': '🇬🇧 English',
    'de': '🇩🇪 Deutsch',
    'es': '🇪🇸 Español',
    'fr': '🇫🇷 Français',
    'af': '🇿🇦 Afrikaans',
    'zu': '🇿🇦 isiZulu',
    'ro': '🇷🇴 Română'
}

# Available languages list for direct import
available_languages = list(LANGUAGE_DISPLAY.keys())

def get_available_languages() -> list:
    """Get list of languages that have compiled translations"""
    locale_dir = os.path.join(os.path.dirname(__file__), 'translations')
    available = []
    for lang in available_languages:
        mo_path = os.path.join(locale_dir, lang, 'LC_MESSAGES', 'messages.mo')
        if os.path.exists(mo_path) or lang == 'en':  # Always include English
            available.append(lang)
    return available

def _(text: str) -> str:
    """
    Translate text using gettext
    """
    if not text:
        return text

    if not hasattr(st.session_state, 'translations'):
        init_localization()

    translations = st.session_state.translations
    if translations and hasattr(translations, 'ugettext'):
        try:
            translated = translations.ugettext(text)
            return translated if translated else text
        except Exception:
            return text
    return text

def init_localization():
    """
    Initialize localization settings and load translations
    """
    # Check URL parameters first, then session state
    lang = st.query_params.get('lang', None)
    available_langs = get_available_languages()

    if lang and lang in available_langs:
        st.session_state.language = lang
    elif 'language' not in st.session_state:
        st.session_state.language = 'en'

    # Always load translations, even if we already have them
    lang = st.session_state.language
    locale_dir = os.path.join(os.path.dirname(__file__), 'translations')

    try:
        translations = babel.support.Translations.load(
            locale_dir,
            [lang]
        )
        if translations:
            st.session_state.translations = translations
            st.session_state.last_loaded_lang = lang
            # Add debug information
            print(f"Loaded translations for language: {lang}")
        else:
            st.session_state.translations = None
            print(f"No translations found for language: {lang}")
    except Exception as e:
        print(f"Error loading translations: {str(e)}")
        st.session_state.translations = None

def switch_language(lang: str):
    """
    Switch the current language and reload translations
    """
    available_langs = get_available_languages()
    if lang not in available_langs:
        return

    if 'last_loaded_lang' not in st.session_state or st.session_state.last_loaded_lang != lang:
        st.session_state.language = lang
        locale_dir = os.path.join(os.path.dirname(__file__), 'translations')
        try:
            translations = babel.support.Translations.load(
                locale_dir,
                [lang]
            )
            if translations:
                st.session_state.translations = translations
                st.session_state.last_loaded_lang = lang
                # Update URL parameter and force page rerun
                st.query_params['lang'] = lang
                st.rerun()
            else:
                print(f"No translations found for language: {lang}")
        except Exception as e:
            print(f"Error switching language: {str(e)}")

def translate_page(func: Callable) -> Callable:
    """
    Decorator to handle page translation
    """
    @wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        if 'language' not in st.session_state:
            init_localization()
        elif ('last_loaded_lang' not in st.session_state or 
              st.session_state.last_loaded_lang != st.session_state.language):
            init_localization()
        return func(*args, **kwargs)
    return wrapper