#!/usr/bin/env python3
"""
Script to seed the certification_explorer table with data
"""
import sys
import os
import json
from typing import List, Dict, Any, Optional

# Add proper paths for imports
if __name__ == "__main__":
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import get_db, init_db
from models.certification_explorer import (
    CertificationExplorer,
    CertificationTypeEnum,
    CertificationLevelEnum,
    SecurityDomainEnum
)

def map_certification_type(category: str) -> CertificationTypeEnum:
    """Map category to certification type enum"""
    category = category.lower() if category else ""
    if "security" in category or "cyber" in category:
        return CertificationTypeEnum.SECURITY
    elif "cloud" in category or "aws" in category or "azure" in category:
        return CertificationTypeEnum.CLOUD
    elif "network" in category:
        return CertificationTypeEnum.NETWORKING
    elif "development" in category or "developer" in category:
        return CertificationTypeEnum.DEVELOPMENT
    elif "devops" in category:
        return CertificationTypeEnum.DEVOPS
    else:
        return CertificationTypeEnum.OTHER

def map_certification_level(level: Optional[str], difficulty: Optional[int]) -> CertificationLevelEnum:
    """Map level string and difficulty to certification level enum"""
    # If we have a level string, use it
    if level:
        level = level.lower()
        if "beginner" in level or "entry" in level:
            return CertificationLevelEnum.BEGINNER
        elif "intermediate" in level:
            return CertificationLevelEnum.INTERMEDIATE
        elif "advanced" in level:
            return CertificationLevelEnum.ADVANCED
        elif "expert" in level:
            return CertificationLevelEnum.EXPERT
    
    # If no level string, use difficulty
    if difficulty is not None:
        if difficulty == 1:
            return CertificationLevelEnum.BEGINNER
        elif difficulty == 2:
            return CertificationLevelEnum.INTERMEDIATE
        elif difficulty == 3:
            return CertificationLevelEnum.ADVANCED
        elif difficulty >= 4:
            return CertificationLevelEnum.EXPERT
    
    # Default to beginner
    return CertificationLevelEnum.BEGINNER

def map_security_domain(domain: Optional[str]) -> SecurityDomainEnum:
    """Map domain to security domain enum"""
    if not domain:
        return SecurityDomainEnum.SECURITY_MANAGEMENT
        
    domain = domain.lower()
    if "engineering" in domain:
        return SecurityDomainEnum.SECURITY_ENGINEERING
    elif "defensive" in domain:
        return SecurityDomainEnum.DEFENSIVE_SECURITY
    elif "management" in domain:
        return SecurityDomainEnum.SECURITY_MANAGEMENT
    elif "network" in domain:
        return SecurityDomainEnum.NETWORK_SECURITY
    elif "identity" in domain or "access" in domain:
        return SecurityDomainEnum.IDENTITY_ACCESS_MANAGEMENT
    elif "asset" in domain:
        return SecurityDomainEnum.ASSET_SECURITY
    elif "testing" in domain:
        return SecurityDomainEnum.SECURITY_TESTING
    elif "offensive" in domain or "penetration" in domain:
        return SecurityDomainEnum.OFFENSIVE_SECURITY
    else:
        return SecurityDomainEnum.SECURITY_MANAGEMENT

def load_certification_data() -> Dict[str, Dict[str, Any]]:
    """Load certification data from JSON file"""
    print("Loading certification data from JSON file...")
    json_file = os.path.join('data', 'normalized_certifications.json')
    
    if not os.path.exists(json_file):
        print(f"Error: JSON file not found at {json_file}")
        return {}

    with open(json_file, 'r', encoding='utf-8') as f:
        certifications_list = json.load(f)

    certifications = {}
    print(f"Found {len(certifications_list)} total certifications to load")

    for cert in certifications_list:
        name = cert['name']
        if name in certifications:
            print(f"Warning: Duplicate certification found: {name}")
            continue

        difficulty = cert.get('difficulty', 1)
        level = cert.get('level', '')

        domain = cert.get('domain', 'General Security')

        # Extract organization from name or description
        org_name = 'Unknown'
        if 'AWS' in name:
            org_name = 'Amazon Web Services'
        elif 'CompTIA' in name:
            org_name = 'CompTIA'
        elif 'Microsoft' in name or 'Azure' in name:
            org_name = 'Microsoft'
        elif 'Cisco' in name:
            org_name = 'Cisco Systems'
        elif 'EC-Council' in name:
            org_name = 'EC-Council'
        elif 'GIAC' in name:
            org_name = 'Global Information Assurance Certification'
        elif 'Oracle' in name:
            org_name = 'Oracle Corporation'
        elif 'ISC2' in name:
            org_name = 'International Information System Security Certification Consortium'
        elif 'ISACA' in name:
            org_name = 'Information Systems Audit and Control Association'

        certifications[name] = {
            'name': name,
            'provider': org_name,
            'description': cert.get('description', ''),
            'level': level,
            'difficulty': difficulty,
            'domain': domain,
            'category': cert.get('category', 'General Security'),
            'prerequisites': cert.get('prerequisites', []),
            'price': cert.get('cost'),
            'url': cert.get('url')
        }

    print(f"Total unique certifications loaded: {len(certifications)}")
    return certifications

def seed_certifications():
    """Seed certifications into the certification_explorer table"""
    db = next(get_db())
    try:
        certifications_data = load_certification_data()
        success_count = 0
        error_count = 0
        
        # Check if we already have certifications in the table
        existing_count = db.query(CertificationExplorer).count()
        if existing_count > 0:
            print(f"Found {existing_count} existing certifications. Skipping...")
            return

        for name, cert_data in certifications_data.items():
            try:
                # Create certification explorer record
                cert = CertificationExplorer(
                    name=name,
                    provider=cert_data['provider'],
                    description=cert_data['description'],
                    type=map_certification_type(cert_data['category']),
                    level=map_certification_level(cert_data['level'], cert_data['difficulty']),
                    domain=map_security_domain(cert_data['domain']),
                    price=cert_data['price'],
                    prerequisites_list=cert_data['prerequisites'],
                    skills_covered=[],
                    url=cert_data['url']
                )
                db.add(cert)
                success_count += 1
                
                # Commit every 100 records
                if success_count % 100 == 0:
                    db.commit()
                    print(f"Added {success_count} certifications so far...")
                
            except Exception as e:
                print(f"Error adding certification {name}: {e}")
                error_count += 1
        
        db.commit()
        print(f"Successfully added {success_count} certifications with {error_count} errors")
        
        # Verify
        final_count = db.query(CertificationExplorer).count()
        print(f"Total certifications in explorer table: {final_count}")
        
    except Exception as e:
        print(f"Error seeding certifications: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    seed_certifications() 