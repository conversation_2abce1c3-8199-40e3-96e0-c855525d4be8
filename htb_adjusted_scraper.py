#!/usr/bin/env python3
"""
HackTheBox Machine Scraper - Adjusted Version
Scrapes machine data from the actual HackTheBox website with improved error handling
No login required - only scrapes publicly available data
"""
import requests
from bs4 import BeautifulSoup
import re
import json
import time
import random
import psycopg2
from psycopg2.extras import <PERSON><PERSON>
from datetime import datetime, timedelta
import os
import sys
import logging
import traceback
from rich.console import Console
from rich.progress import Progress, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn, SpinnerColumn
from rich.panel import Panel
from rich.table import Table
from rich import print as rprint
from urllib.parse import urljoin

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='htb_scraper.log',  # Log to file
    filemode='a'
)
logger = logging.getLogger("htb_scraper")

# Create Rich console
console = Console()

# Database connection string
DB_URL = "***************************************************"

# Maximum retries for HTTP requests
MAX_RETRIES = 3

# Batch size for database updates
DB_BATCH_SIZE = 10

class HackTheBoxScraper:
    def __init__(self, base_url="https://www.hackthebox.com"):
        self.base_url = base_url
        self.session = requests.Session()
        
        # Set up a proper user agent string
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Referer': 'https://www.hackthebox.com/',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
        })
        
        # Check if robots.txt allows crawling
        self._check_robots_txt()
        
        # Initialize database connection
        self.db_conn = None
        self.db_cursor = None
        self._init_database()
    
    def _init_database(self):
        """Initialize database connection and tables"""
        try:
            self.db_conn = psycopg2.connect(DB_URL)
            self.db_cursor = self.db_conn.cursor()
            
            # Create table if not exists
            self.db_cursor.execute("""
                CREATE TABLE IF NOT EXISTS hackthebox_machines (
                    id SERIAL PRIMARY KEY,
                    machine_id VARCHAR(50) UNIQUE,
                    name VARCHAR(100) NOT NULL,
                    logo_url TEXT,
                    difficulty VARCHAR(20),
                    operating_system VARCHAR(50),
                    user_owns INTEGER,
                    system_owns INTEGER,
                    release_date DATE,
                    rating FLOAT,
                    matrix JSONB,
                    provider VARCHAR(50) DEFAULT 'HackTheBox',
                    created_at TIMESTAMP DEFAULT NOW(),
                    updated_at TIMESTAMP DEFAULT NOW()
                )
            """)
            
            # Create indexes if not exists
            self.db_cursor.execute("""
                DO $$
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM pg_indexes 
                        WHERE indexname = 'idx_htb_machine_name'
                    ) THEN
                        CREATE INDEX idx_htb_machine_name ON hackthebox_machines(name);
                    END IF;
                    
                    IF NOT EXISTS (
                        SELECT 1 FROM pg_indexes 
                        WHERE indexname = 'idx_htb_difficulty'
                    ) THEN
                        CREATE INDEX idx_htb_difficulty ON hackthebox_machines(difficulty);
                    END IF;
                    
                    IF NOT EXISTS (
                        SELECT 1 FROM pg_indexes 
                        WHERE indexname = 'idx_htb_os'
                    ) THEN
                        CREATE INDEX idx_htb_os ON hackthebox_machines(operating_system);
                    END IF;
                END
                $$;
            """)
            
            # Clear existing data
            self.db_cursor.execute("TRUNCATE TABLE hackthebox_machines")
            console.log("[yellow]Cleared existing data from hackthebox_machines table[/yellow]")
            
            self.db_conn.commit()
        except Exception as e:
            console.log(f"[red]Database initialization error: {str(e)}[/red]")
            logger.error(f"Database initialization error: {str(e)}")
            if self.db_conn:
                self.db_conn.close()
            self.db_conn = None
            self.db_cursor = None
    
    def _check_robots_txt(self):
        """Check if robots.txt allows crawling the machines page"""
        try:
            robots_url = f"{self.base_url}/robots.txt"
            response = self.session.get(robots_url)
            if response.status_code == 200:
                # Very basic check - look for "Disallow: /machines" or similar
                if "Disallow: /machines" in response.text or "Disallow: /" in response.text:
                    console.log("[yellow]Warning:[/yellow] robots.txt disallows crawling the machines page. Proceed with caution.")
        except Exception as e:
            console.log(f"[yellow]Warning:[/yellow] Could not check robots.txt: {e}")
    
    def sleep_with_delay(self, progress_task=None):
        """Sleep with short delay (1-5 seconds) between requests"""
        delay = random.uniform(1, 5)
        
        if progress_task:
            with console.status(f"[cyan]Waiting {delay:.2f} seconds before next request...[/cyan]"):
                time.sleep(delay)
        else:
            console.log(f"Waiting for {delay:.2f} seconds...")
            time.sleep(delay)
    
    def safe_request(self, url, retry_count=0):
        """Make a safe HTTP request with retry logic"""
        try:
            response = self.session.get(url, timeout=30)
            return response
        except (requests.RequestException, ConnectionError, TimeoutError) as e:
            if retry_count < MAX_RETRIES:
                retry_delay = (2 ** retry_count) + random.uniform(0, 1)  # Exponential backoff with jitter
                console.log(f"[yellow]Request failed: {str(e)}. Retrying in {retry_delay:.2f} seconds...[/yellow]")
                time.sleep(retry_delay)
                return self.safe_request(url, retry_count + 1)
            else:
                console.log(f"[red]Maximum retries reached. Request failed: {str(e)}[/red]")
                return None
    
    def get_machines_list(self):
        """Get list of all publicly visible machines"""
        machines_url = f"{self.base_url}/machines"
        console.log(f"Fetching machines list from [link={machines_url}]{machines_url}[/link]")
        
        response = self.safe_request(machines_url)
        if not response or response.status_code != 200:
            console.log(f"[red]Failed to fetch machines, status code: {response.status_code if response else 'N/A'}[/red]")
            return []
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Save the page content for debugging
        with open('debug_machines_page.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        console.log("Saved page content to debug_machines_page.html for inspection")
        
        machines = []
        
        # Try table structure first (current HTB website as of 2023-2024)
        machine_rows = soup.find_all('tr', class_=lambda c: c and ('machine-item' in c or 'htb-machine' in c))
        if machine_rows:
            console.log(f"Found {len(machine_rows)} machine rows in table")
            
            for row in machine_rows:
                machine_info = {}
                
                # Look for name in link or cell
                name_link = row.find('a')
                if name_link:
                    machine_info['name'] = name_link.text.strip()
                    machine_info['id'] = name_link.get('href', '').split('/')[-1]
                
                # If no name found, try cell content
                if 'name' not in machine_info:
                    for cell in row.find_all('td'):
                        # Only use first non-empty cell for name
                        if cell.text.strip() and 'name' not in machine_info:
                            machine_info['name'] = cell.text.strip()
                
                # Try to find difficulty and OS
                for cell in row.find_all('td'):
                    # Check for difficulty keywords
                    text = cell.text.strip().lower()
                    if text in ['easy', 'medium', 'hard', 'insane']:
                        machine_info['difficulty'] = text.capitalize()
                    
                    # Check for OS images
                    img = cell.find('img')
                    if img and img.get('alt'):
                        if any(os in img.get('alt').lower() for os in ['windows', 'linux', 'freebsd', 'openbsd']):
                            machine_info['os'] = img.get('alt')
                
                # If we have at least a name, add to list
                if 'name' in machine_info:
                    machines.append(machine_info)
                    
            if machines:
                return machines
        
        # Try multiple approaches to find machine cards (older HTB website)
        all_approaches = [
            # Approach 1: Standard machine cards
            soup.find_all('div', class_=lambda c: c and ('machine-card' in c or 'machineCard' in c)),
            # Approach 2: Generic card approach
            soup.find_all('div', class_=lambda c: c and ('card' in c))
        ]
        
        machine_cards = []
        for approach_cards in all_approaches:
            if approach_cards:
                machine_cards = approach_cards
                break
        
        if not machine_cards:
            # Last resort - try to find list items with machine names
            machine_cards = soup.find_all('li', class_=lambda c: c and ('machine' in c or 'htb-machine' in c))
            
        if not machine_cards:
            console.log("[yellow]No machine cards found using standard selectors. Trying links...[/yellow]")
            # Try looking for links to machine pages
            machine_links = soup.find_all('a', href=lambda h: h and '/machines/' in h)
            
            # Remove duplicates by href
            seen_hrefs = set()
            unique_links = []
            for link in machine_links:
                href = link.get('href')
                if href and href not in seen_hrefs:
                    seen_hrefs.add(href)
                    unique_links.append(link)
            
            if unique_links:
                console.log(f"Found {len(unique_links)} unique machine links")
                for link in unique_links:
                    href = link.get('href')
                    machine_id = href.split('/')[-1] if href else None
                    name = link.text.strip() or (f"Machine {machine_id}" if machine_id else "Unknown")
                    
                    if machine_id:
                        machines.append({
                            'id': machine_id,
                            'name': name
                        })
        else:
            console.log(f"Found {len(machine_cards)} machine cards")
            
            for card in machine_cards:
                machine_info = {}
                
                # Machine name - look for common name elements
                name_elem = (
                    card.find('h4', class_=lambda c: c and 'machine-name' in c) or
                    card.find('div', class_=lambda c: c and 'name' in c) or
                    card.find('h3') or
                    card.find('h4') or
                    card.find('h5') or
                    card.find('span', class_=lambda c: c and 'name' in c)
                )
                
                if name_elem:
                    machine_info['name'] = name_elem.text.strip()
                else:
                    # Try to extract name from alt text of an image
                    img = card.find('img', alt=True)
                    if img:
                        machine_info['name'] = img['alt'].strip()
                
                # If no name found, try to get any text
                if 'name' not in machine_info:
                    text = card.text.strip()
                    if text:
                        # Try to extract a meaningful name
                        lines = [line.strip() for line in text.split('\n') if line.strip()]
                        if lines:
                            machine_info['name'] = lines[0]
                
                # If still no name found, skip this card
                if 'name' not in machine_info:
                    console.log("[yellow]Skipping card - could not find machine name[/yellow]")
                    continue
                    
                console.log(f"Processing machine: [bold]{machine_info['name']}[/bold]")
                
                # Machine logo
                logo_elem = card.find('img', class_=lambda c: c and ('machine-logo' in c or 'logo' in c))
                if logo_elem and 'src' in logo_elem.attrs:
                    machine_info['logo_url'] = logo_elem['src']
                    if machine_info['logo_url'].startswith('/'):
                        machine_info['logo_url'] = self.base_url + machine_info['logo_url']
                
                # Difficulty
                difficulty_elem = card.find('div', class_=lambda c: c and ('difficulty' in c))
                if difficulty_elem:
                    machine_info['difficulty'] = difficulty_elem.text.strip()
                else:
                    # Try different approaches to find difficulty
                    for elem in card.find_all(['div', 'span']):
                        text = elem.text.strip().lower()
                        if text in ['easy', 'medium', 'hard', 'insane']:
                            machine_info['difficulty'] = text.capitalize()
                            break
                
                # Operating System
                os_elem = card.find('img', class_=lambda c: c and ('os' in c or 'operating-system' in c))
                if os_elem and 'alt' in os_elem.attrs:
                    machine_info['os'] = os_elem['alt']
                elif os_elem and 'title' in os_elem.attrs:
                    machine_info['os'] = os_elem['title']
                
                # Machine ID for further details
                # Try different ways to get machine ID
                for attr in ['data-id', 'id', 'data-machine-id']:
                    machine_id = card.get(attr)
                    if machine_id:
                        machine_info['id'] = machine_id
                        break
                        
                # If no ID found, try to extract from URLs
                if 'id' not in machine_info:
                    link = card.find('a', href=True)
                    if link:
                        href = link['href']
                        # Extract ID from URL pattern like /machines/123 or similar
                        match = re.search(r'/machines/([^/]+)', href)
                        if match:
                            machine_info['id'] = match.group(1)
                
                machines.append(machine_info)
        
        # Final fallback: if we still don't have machine_ids for some entries, try to get them
        for machine in machines:
            if 'id' not in machine and 'name' in machine:
                # Try to find ID based on name (approximation only)
                machine_id = machine['name'].lower().replace(' ', '-')
                machine['id'] = machine_id
                console.log(f"[yellow]Added approximate ID '{machine_id}' for machine '{machine['name']}'[/yellow]")
        
        # Remove duplicates by name or id
        unique_machines = []
        seen_names = set()
        seen_ids = set()
        
        for machine in machines:
            if 'id' in machine and machine['id'] not in seen_ids:
                seen_ids.add(machine['id'])
                unique_machines.append(machine)
            elif 'name' in machine and machine['name'] not in seen_names:
                seen_names.add(machine['name'])
                unique_machines.append(machine)
        
        console.log(f"Found {len(unique_machines)} unique machines after deduplication")
        return unique_machines
    
    def get_machine_details(self, machine_id, machine_name="Unknown"):
        """Get detailed information about a specific machine"""
        machine_url = f"{self.base_url}/machines/{machine_id}"
        
        response = self.safe_request(machine_url)
        if not response or response.status_code != 200:
            console.log(f"[red]Failed to fetch machine details for [bold]{machine_name}[/bold], status code: {response.status_code if response else 'N/A'}[/red]")
            return {}
            
        # Save page content for debugging
        with open(f'debug_machine_{machine_id}.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        details = {}
        
        # User owns
        user_owns_elem = soup.find(['div', 'span'], class_=lambda c: c and ('user-owns' in c or 'userOwns' in c))
        if user_owns_elem:
            owns_text = user_owns_elem.text.strip()
            owns_count = re.search(r'\d+', owns_text)
            if owns_count:
                details['user_owns'] = int(owns_count.group())
        
        # System owns
        system_owns_elem = soup.find(['div', 'span'], class_=lambda c: c and ('system-owns' in c or 'systemOwns' in c))
        if system_owns_elem:
            owns_text = system_owns_elem.text.strip()
            owns_count = re.search(r'\d+', owns_text)
            if owns_count:
                details['system_owns'] = int(owns_count.group())
        
        # Release date
        release_elem = soup.find(['div', 'span'], class_=lambda c: c and ('release' in c or 'date' in c))
        if release_elem:
            date_text = release_elem.text.strip()
            # Try different date formats
            date_patterns = [
                r'\d{2}/\d{2}/\d{4}',  # DD/MM/YYYY
                r'\d{2}-\d{2}-\d{4}',  # DD-MM-YYYY
                r'\d{4}-\d{2}-\d{2}',  # YYYY-MM-DD
                r'\d{2}\.\d{2}\.\d{4}'  # DD.MM.YYYY
            ]
            
            for pattern in date_patterns:
                date_match = re.search(pattern, date_text)
                if date_match:
                    details['release_date'] = date_match.group()
                    break
        
        # Machine matrix (5 axis ratings)
        matrix_elem = soup.find('div', class_=lambda c: c and ('matrix' in c or 'ratings' in c))
        if matrix_elem:
            axis_ratings = {}
            axes = matrix_elem.find_all('div', class_=lambda c: c and ('axis' in c or 'rating' in c))
            
            for axis in axes:
                # Try different ways to get axis name and value
                axis_name = None
                axis_value = None
                
                # Method 1: Dedicated spans for name and value
                name_span = axis.find('span', class_=lambda c: c and ('name' in c or 'label' in c))
                value_span = axis.find('span', class_=lambda c: c and ('value' in c or 'score' in c))
                
                if name_span and value_span:
                    axis_name = name_span.text.strip()
                    value_text = value_span.text.strip()
                    value_match = re.search(r'[\d.]+', value_text)
                    if value_match:
                        axis_value = float(value_match.group())
                
                # Method 2: Text contains both name and value
                if not (axis_name and axis_value):
                    text = axis.text.strip()
                    parts = text.split(':')
                    if len(parts) == 2:
                        axis_name = parts[0].strip()
                        value_match = re.search(r'[\d.]+', parts[1])
                        if value_match:
                            axis_value = float(value_match.group())
                
                if axis_name and axis_value:
                    axis_ratings[axis_name] = axis_value
            
            if axis_ratings:
                details['matrix'] = axis_ratings
        
        # Machine rating
        rating_elem = soup.find(['div', 'span'], class_=lambda c: c and ('rating' in c or 'score' in c))
        if rating_elem:
            rating_text = rating_elem.text.strip()
            rating_match = re.search(r'[\d.]+', rating_text)
            if rating_match:
                details['rating'] = float(rating_match.group())
        
        # If we couldn't find any details, try to derive some from the page title
        if not details and 'os' not in details:
            title = soup.find('title')
            if title:
                title_text = title.text.strip()
                # Try to extract OS from title
                for os_name in ['Windows', 'Linux', 'FreeBSD', 'OpenBSD']:
                    if os_name in title_text:
                        details['os'] = os_name
                        break
        
        return details
    
    def store_machine_batch(self, machines):
        """Store a batch of machines in the database"""
        if not self.db_conn or not self.db_cursor:
            console.log("[yellow]Database connection not available. Skipping batch update.[/yellow]")
            return 0
            
        success_count = 0
        
        try:
            for machine in machines:
                try:
                    # Parse date with various formats
                    release_date = None
                    if 'release_date' in machine and machine['release_date']:
                        date_str = machine['release_date']
                        date_formats = [
                            '%d/%m/%Y',  # DD/MM/YYYY
                            '%d-%m-%Y',  # DD-MM-YYYY
                            '%Y-%m-%d',  # YYYY-MM-DD
                            '%d.%m.%Y'   # DD.MM.YYYY
                        ]
                        
                        for fmt in date_formats:
                            try:
                                release_date = datetime.strptime(date_str, fmt).date()
                                break
                            except ValueError:
                                continue
                    
                    self.db_cursor.execute("""
                        INSERT INTO hackthebox_machines 
                        (machine_id, name, logo_url, difficulty, operating_system, 
                        user_owns, system_owns, release_date, rating, matrix)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (machine_id) DO UPDATE SET
                        name = EXCLUDED.name,
                        logo_url = EXCLUDED.logo_url,
                        difficulty = EXCLUDED.difficulty,
                        operating_system = EXCLUDED.operating_system,
                        user_owns = EXCLUDED.user_owns,
                        system_owns = EXCLUDED.system_owns,
                        release_date = EXCLUDED.release_date,
                        rating = EXCLUDED.rating,
                        matrix = EXCLUDED.matrix,
                        updated_at = NOW()
                    """, (
                        machine.get('id'),
                        machine.get('name'),
                        machine.get('logo_url'),
                        machine.get('difficulty'),
                        machine.get('os'),
                        machine.get('user_owns'),
                        machine.get('system_owns'),
                        release_date,
                        machine.get('rating'),
                        Json(machine.get('matrix', {}))
                    ))
                    success_count += 1
                except Exception as e:
                    console.log(f"[red]Error storing machine {machine.get('name')}: {str(e)}[/red]")
                    logger.error(f"Error storing machine {machine.get('name')}: {str(e)}")
            
            self.db_conn.commit()
        except Exception as e:
            console.log(f"[red]Database batch update error: {str(e)}[/red]")
            logger.error(f"Database batch update error: {str(e)}")
            if self.db_conn:
                self.db_conn.rollback()
            
        return success_count
    
    def scrape_all_machines(self, limit=None):
        """Scrape all machines with their details
        Args:
            limit: Optional limit on number of machines to scrape
        """
        # First try to load progress file if it exists
        machine_list = []
        detailed_machines = []
        progress_files = sorted([f for f in os.listdir('.') if f.startswith('htb_machines_progress_')])
        
        if progress_files:
            latest_progress = progress_files[-1]
            console.log(f"[yellow]Found progress file: {latest_progress}. Attempting to use it.[/yellow]")
            try:
                with open(latest_progress, 'r', encoding='utf-8') as f:
                    detailed_machines = json.load(f)
                console.log(f"[green]Loaded {len(detailed_machines)} machines from progress file.[/green]")
                
                # Get IDs of already processed machines
                processed_ids = {m.get('id') for m in detailed_machines if 'id' in m}
                
                # Try to get a full list of machines to continue with remainder
                machine_list = self.get_machines_list()
                if machine_list:
                    # Filter out already processed machines
                    machine_list = [m for m in machine_list if m.get('id') not in processed_ids]
                    console.log(f"[green]Found {len(machine_list)} remaining machines to process.[/green]")
            except Exception as e:
                console.log(f"[red]Failed to load progress file: {str(e)}[/red]")
                logger.error(f"Failed to load progress file: {str(e)}")
                # Fall back to fresh start
                machine_list = []
                detailed_machines = []
        
        # If we don't have a list of machines yet, get a fresh one
        if not machine_list:
            machine_list = self.get_machines_list()
            
        if not machine_list:
            console.log("[red]No machines found to scrape[/red]")
            return detailed_machines  # Return whatever we loaded from progress file
            
        console.log(f"Retrieved list of [bold green]{len(machine_list)}[/bold green] machines")
        
        if limit and len(machine_list) > limit:
            console.log(f"Limiting to [bold yellow]{limit}[/bold yellow] machines as requested")
            machine_list = machine_list[:limit]
            
        total = len(machine_list)
        
        # Calculate estimated time (using 3 seconds as average time per machine)
        est_time_per_machine = 3  # seconds (1-5 seconds random delay)
        total_est_seconds = total * est_time_per_machine
        est_completion_time = datetime.now() + timedelta(seconds=total_est_seconds)
        
        console.print(Panel(
            f"[bold]Starting adjusted scraper for {total} machines[/bold]\n"
            f"Already have {len(detailed_machines)} machines from previous runs\n"
            f"Estimated time for remaining: {timedelta(seconds=total_est_seconds)}\n"
            f"Expected completion: {est_completion_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"Average time per machine: {est_time_per_machine} seconds (1-5s random delay)",
            title="[bold cyan]HackTheBox Adjusted Scraper[/bold cyan]",
            border_style="green"
        ))
        
        # Save initial machines list to JSON in case of crash
        try:
            with open('htb_machines_initial.json', 'w', encoding='utf-8') as f:
                json.dump(machine_list, f, indent=2, ensure_ascii=False)
            console.log("[green]Saved initial machines list to htb_machines_initial.json[/green]")
        except Exception as e:
            console.log(f"[red]Error saving initial machines list: {str(e)}[/red]")
            logger.error(f"Error saving initial machines list: {str(e)}")
        
        # Create progress bar
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TextColumn("•"),
            TimeElapsedColumn(),
            TextColumn("•"),
            TimeRemainingColumn(),
        ) as progress:
            task = progress.add_task("[cyan]Scraping machines...", total=total)
            
            # Save progress every N machines
            save_interval = 20
            db_batch = []
            batch_count = 0
            
            for i, machine in enumerate(machine_list):
                try:
                    if 'id' not in machine:
                        progress.log(f"[yellow]Skipping machine {machine.get('name', 'Unknown')} - no ID found[/yellow]")
                        progress.update(task, advance=1)
                        continue
                    
                    name = machine.get('name', "Unknown")
                    progress.update(task, description=f"[cyan]Scraping machine {i+1}/{total}: [bold]{name}[/bold]")
                    
                    # Use the faster delay between requests
                    self.sleep_with_delay()
                    
                    details = self.get_machine_details(machine['id'], name)
                    if details:
                        machine.update(details)
                        detailed_machines.append(machine)
                        db_batch.append(machine)
                        
                        # Show a summary of the scraped machine
                        difficulty = machine.get('difficulty', 'Unknown')
                        difficulty_color = {
                            'Easy': 'green',
                            'Medium': 'yellow',
                            'Hard': 'red',
                            'Insane': 'magenta'
                        }.get(difficulty, 'blue')
                        
                        os_type = machine.get('os', 'Unknown')
                        progress.log(f"✓ [{difficulty_color}]{name}[/{difficulty_color}] ({os_type}) - " + 
                                   f"Rating: {machine.get('rating', 'N/A')}, " + 
                                   f"User/System Owns: {machine.get('user_owns', 'N/A')}/{machine.get('system_owns', 'N/A')}")
                        
                    else:
                        progress.log(f"[red]Failed to retrieve details for {name}[/red]")
                    
                    # Increment batch count
                    batch_count += 1
                    
                    # Handle database batch updates
                    if len(db_batch) >= DB_BATCH_SIZE:
                        count = self.store_machine_batch(db_batch)
                        progress.log(f"[green]Updated {count} machines in database[/green]")
                        db_batch = []
                    
                    # Handle progress file updates
                    if batch_count >= save_interval:
                        batch_count = 0
                        try:
                            # Save intermediate results
                            with open(f'htb_machines_progress_{i+1}.json', 'w', encoding='utf-8') as f:
                                json.dump(detailed_machines, f, indent=2, ensure_ascii=False)
                            progress.log(f"[green]Saved progress to htb_machines_progress_{i+1}.json ({len(detailed_machines)} machines)[/green]")
                        except Exception as e:
                            progress.log(f"[red]Error saving progress: {str(e)}[/red]")
                            logger.error(f"Error saving progress: {str(e)}")
                    
                    progress.update(task, advance=1)
                    
                except Exception as e:
                    progress.log(f"[red]Error processing machine {machine.get('name', 'Unknown')}: {str(e)}[/red]")
                    logger.error(f"Error processing machine {machine.get('name', 'Unknown')}: {str(e)}")
                    logger.error(traceback.format_exc())
                    progress.update(task, advance=1)
            
            # Process any remaining batch items
            if db_batch:
                count = self.store_machine_batch(db_batch)
                progress.log(f"[green]Updated final batch of {count} machines in database[/green]")
        
        # Try to save final results
        try:
            # Save the final JSON output
            output_file = 'htb_machines_complete.json'
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(detailed_machines, f, indent=2, ensure_ascii=False)
            console.log(f"[green]Successfully saved all scraped data to {output_file}[/green]")
        except Exception as e:
            console.log(f"[red]Error saving final JSON output: {str(e)}[/red]")
            logger.error(f"Error saving final JSON output: {str(e)}")
            # Try to save to an alternative location
            try:
                alt_output = '/tmp/htb_machines_complete.json'
                with open(alt_output, 'w', encoding='utf-8') as f:
                    json.dump(detailed_machines, f, indent=2, ensure_ascii=False)
                console.log(f"[yellow]Saved data to alternative location: {alt_output}[/yellow]")
            except Exception as e2:
                console.log(f"[red]Failed to save data to alternative location: {str(e2)}[/red]")
                logger.error(f"Failed to save data to alternative location: {str(e2)}")
        
        return detailed_machines
    
    def cleanup(self):
        """Clean up database connections"""
        if self.db_conn:
            if self.db_cursor:
                self.db_cursor.close()
            self.db_conn.close()

def display_database_summary():
    """Display summary of data in the database"""
    conn = None
    try:
        conn = psycopg2.connect(DB_URL)
        cursor = conn.cursor()
        
        # Get summary data
        cursor.execute("SELECT COUNT(*) FROM hackthebox_machines")
        total_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT difficulty, COUNT(*) FROM hackthebox_machines GROUP BY difficulty")
        difficulty_counts = cursor.fetchall()
        
        cursor.execute("SELECT operating_system, COUNT(*) FROM hackthebox_machines GROUP BY operating_system")
        os_counts = cursor.fetchall()
        
        cursor.execute("SELECT AVG(rating) FROM hackthebox_machines")
        avg_rating = cursor.fetchone()[0]
        
        # Create a summary table
        table = Table(title="Database Summary", show_header=True, header_style="bold cyan")
        table.add_column("Category", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Total machines", str(total_count))
        table.add_row("Average rating", f"{avg_rating:.2f}" if avg_rating else "N/A")
        
        console.print(table)
        
        # Difficulty distribution table
        diff_table = Table(title="Difficulty Distribution", show_header=True, header_style="bold cyan")
        diff_table.add_column("Difficulty", style="cyan")
        diff_table.add_column("Count", style="green")
        diff_table.add_column("Percentage", style="yellow")
        
        for diff, count in difficulty_counts:
            percent = (count / total_count) * 100 if total_count else 0
            difficulty_color = {
                'Easy': 'green',
                'Medium': 'yellow',
                'Hard': 'red',
                'Insane': 'magenta'
            }.get(diff, 'cyan') if diff else 'cyan'
            
            diff_table.add_row(
                f"[{difficulty_color}]{diff or 'Unknown'}[/{difficulty_color}]", 
                str(count), 
                f"{percent:.1f}%"
            )
        
        console.print(diff_table)
        
        # OS distribution table
        os_table = Table(title="Operating System Distribution", show_header=True, header_style="bold cyan")
        os_table.add_column("Operating System", style="cyan")
        os_table.add_column("Count", style="green")
        os_table.add_column("Percentage", style="yellow")
        
        for os_name, count in os_counts:
            percent = (count / total_count) * 100 if total_count else 0
            os_table.add_row(
                os_name or "Unknown", 
                str(count), 
                f"{percent:.1f}%"
            )
        
        console.print(os_table)
        
        # Show top rated machines
        cursor.execute("""
            SELECT name, difficulty, operating_system, rating
            FROM hackthebox_machines
            WHERE rating IS NOT NULL
            ORDER BY rating DESC
            LIMIT 5
        """)
        top_rated = cursor.fetchall()
        
        if top_rated:
            rated_table = Table(title="Top Rated Machines", show_header=True, header_style="bold cyan")
            rated_table.add_column("Name", style="cyan")
            rated_table.add_column("Difficulty", style="green")
            rated_table.add_column("OS", style="yellow")
            rated_table.add_column("Rating", style="magenta")
            
            for name, diff, os, rating in top_rated:
                rated_table.add_row(
                    name or "Unknown",
                    diff or "Unknown",
                    os or "Unknown",
                    f"{rating:.1f}" if rating else "N/A"
                )
            
            console.print(rated_table)
    
    except Exception as e:
        console.print(f"[bold red]Error generating database summary: {str(e)}[/bold red]")
        logger.error(f"Error generating database summary: {str(e)}")
    finally:
        if conn:
            conn.close()

def main():
    console.print(Panel(
        "[bold green]HackTheBox Machine Scraper - Adjusted Version[/bold green]\n"
        "[bold cyan]No login required - scrapes publicly available data[/bold cyan]\n"
        "[bold yellow]Using improved error handling and real-time database updates[/bold yellow]",
        border_style="green"
    ))
    
    # No machine limit by default - scrape all machines
    machine_limit = int(os.environ.get('HTB_MACHINE_LIMIT', 0))
    if machine_limit > 0:
        console.print(f"[yellow]Note: Limiting to {machine_limit} machines as requested via HTB_MACHINE_LIMIT env var[/yellow]")
    
    # Initialize scraper
    scraper = HackTheBoxScraper()
    
    console.print("[cyan]Starting scraping as a non-logged-in user...[/cyan]")
    
    start_time = datetime.now()
    
    # Kill any existing scraper process to avoid conflicts
    try:
        os.system("pkill -f 'python.*htb_.*scraper.py'")
        console.print("[yellow]Terminated previous scraper processes[/yellow]")
    except:
        pass
    
    # Scrape machines
    try:
        machines = scraper.scrape_all_machines(limit=machine_limit)
    except KeyboardInterrupt:
        console.print("[yellow]Scraping interrupted by user. Saving progress...[/yellow]")
        logger.warning("Scraping interrupted by user")
    except Exception as e:
        console.print(f"[bold red]Error during scraping: {str(e)}[/bold red]")
        logger.error(f"Error during scraping: {str(e)}")
        logger.error(traceback.format_exc())
    finally:
        # Clean up
        scraper.cleanup()
    
    # Print final summary
    end_time = datetime.now()
    elapsed_time = end_time - start_time
    console.print(Panel(
        f"[bold green]Process complete![/bold green]\n"
        f"Total elapsed time: [bold]{elapsed_time}[/bold]",
        title="[bold cyan]Summary[/bold cyan]",
        border_style="green"
    ))
    
    # Display database summary
    display_database_summary()

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        console.print(f"[bold red]Fatal error: {str(e)}[/bold red]")
        logger.critical(f"Fatal error: {str(e)}")
        logger.critical(traceback.format_exc())
        sys.exit(1) 