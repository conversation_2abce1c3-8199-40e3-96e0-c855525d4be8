# Makefile for CertRats Testing and Development

.PHONY: help test test-unit test-integration test-api test-performance test-coverage test-all clean setup-db

# Default target
help:
	@echo "CertRats Test Suite Commands:"
	@echo ""
	@echo "Setup Commands:"
	@echo "  setup-db          Setup test database"
	@echo "  install-deps      Install Python dependencies"
	@echo ""
	@echo "Test Commands:"
	@echo "  test              Run basic test suite"
	@echo "  test-unit         Run unit tests only"
	@echo "  test-integration  Run integration tests only"
	@echo "  test-api          Run API tests only"
	@echo "  test-performance  Run performance tests only"
	@echo "  test-coverage     Run tests with coverage report"
	@echo "  test-all          Run comprehensive test suite"
	@echo ""
	@echo "Utility Commands:"
	@echo "  clean             Clean test artifacts"
	@echo "  lint              Run code linting"
	@echo "  format            Format code"
	@echo ""

# Setup commands
setup-db:
	@echo "Setting up test database..."
	@python tests/test_database_setup.py

install-deps:
	@echo "Installing Python dependencies..."
	@pip install -r requirements.txt

# Basic test commands
test:
	@echo "Running basic test suite..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/ -v --tb=short

test-unit:
	@echo "Running unit tests..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/ -m "unit" -v

test-integration:
	@echo "Running integration tests..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/ -m "integration" -v

test-api:
	@echo "Running API tests..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/test_api/ tests/test_api_integration/ -v

test-performance:
	@echo "Running performance tests..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/test_performance/ -m "performance" -v

test-coverage:
	@echo "Running tests with coverage..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/ --cov=. --cov-report=html --cov-report=term-missing -v

test-all:
	@echo "Running comprehensive test suite..."
	@python scripts/run_comprehensive_tests.py

# Quick test commands for development
test-quick:
	@echo "Running quick tests (no performance)..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/ -m "not performance" -x --tb=short

test-failed:
	@echo "Re-running failed tests..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/ --lf -v

# Specific test categories
test-auth:
	@echo "Running authentication tests..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/test_api_integration/test_auth_integration.py -v

test-certification:
	@echo "Running certification tests..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/test_api_integration/test_certification_integration.py -v

test-business-logic:
	@echo "Running business logic tests..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/test_business_logic/ -v

test-error-handling:
	@echo "Running error handling tests..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/test_error_handling/ -v

# Database commands
db-setup:
	@echo "Setting up database..."
	@python tests/test_database_setup.py

db-clean:
	@echo "Cleaning test database..."
	@python -c "from tests.test_database_setup import DatabaseManager; DatabaseManager().clean_test_data()"

db-reset:
	@echo "Resetting test database..."
	@python -c "from tests.test_database_setup import DatabaseManager; m = DatabaseManager(); m.drop_test_database(); m.create_test_database(); m.setup_test_schema(); m.seed_test_data()"

# Code quality commands
lint:
	@echo "Running code linting..."
	@python -m flake8 api/ models/ utils/ tests/ --max-line-length=100 --ignore=E501,W503

format:
	@echo "Formatting code..."
	@python -m black api/ models/ utils/ tests/ --line-length=100

type-check:
	@echo "Running type checking..."
	@python -m mypy api/ models/ utils/ --ignore-missing-imports

# Cleanup commands
clean:
	@echo "Cleaning test artifacts..."
	@rm -rf .pytest_cache/
	@rm -rf htmlcov/
	@rm -rf .coverage
	@rm -rf test_results.json
	@find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	@find . -type f -name "*.pyc" -delete

clean-all: clean
	@echo "Deep cleaning..."
	@rm -rf .mypy_cache/
	@rm -rf node_modules/
	@rm -rf venv/

# Development commands
dev-setup: install-deps setup-db
	@echo "Development environment setup complete!"

dev-test: test-quick
	@echo "Development test run complete!"

# CI/CD commands
ci-test:
	@echo "Running CI test suite..."
	@python scripts/run_comprehensive_tests.py --no-coverage

ci-test-with-coverage:
	@echo "Running CI test suite with coverage..."
	@python scripts/run_comprehensive_tests.py

# Documentation commands
docs-coverage:
	@echo "Generating test coverage documentation..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/ --cov=. --cov-report=html
	@echo "Coverage report generated in htmlcov/index.html"

# Monitoring commands
test-monitor:
	@echo "Running tests in monitor mode..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/ -f

# Parallel testing (if pytest-xdist is installed)
test-parallel:
	@echo "Running tests in parallel..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/ -n auto -v

# Benchmark testing
test-benchmark:
	@echo "Running benchmark tests..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/test_performance/ --benchmark-only

# Security testing
test-security:
	@echo "Running security tests..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/test_security/ -v

# Load testing
test-load:
	@echo "Running load tests..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/test_performance/test_load_performance.py -v

# Test reporting
test-report:
	@echo "Generating test report..."
	@DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test \
	ENVIRONMENT=test \
	python -m pytest tests/ --html=test_report.html --self-contained-html

# Environment validation
validate-env:
	@echo "Validating test environment..."
	@python -c "import psycopg2; print('✅ PostgreSQL driver available')"
	@python -c "import pytest; print('✅ Pytest available')"
	@python -c "import fastapi; print('✅ FastAPI available')"
	@python -c "import sqlalchemy; print('✅ SQLAlchemy available')"
	@echo "Environment validation complete!"

# Help for specific categories
help-test:
	@echo "Test Command Details:"
	@echo ""
	@echo "Basic Commands:"
	@echo "  test              - Run all tests with basic output"
	@echo "  test-quick        - Run tests excluding performance tests"
	@echo "  test-failed       - Re-run only failed tests from last run"
	@echo ""
	@echo "Category Commands:"
	@echo "  test-unit         - Unit tests only"
	@echo "  test-integration  - Integration tests only"
	@echo "  test-api          - API endpoint tests"
	@echo "  test-performance  - Performance and load tests"
	@echo ""
	@echo "Specific Area Commands:"
	@echo "  test-auth         - Authentication tests"
	@echo "  test-certification - Certification management tests"
	@echo "  test-business-logic - Business logic tests"
	@echo "  test-error-handling - Error handling tests"

help-db:
	@echo "Database Command Details:"
	@echo ""
	@echo "  db-setup          - Create and setup test database"
	@echo "  db-clean          - Clean test data from database"
	@echo "  db-reset          - Drop and recreate test database"
	@echo ""
	@echo "Note: These commands work with the test database only"
