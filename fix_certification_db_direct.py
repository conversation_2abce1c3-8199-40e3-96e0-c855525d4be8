#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to reset and properly seed the certification_explorer table using direct SQL
"""
import psycopg2
from psycopg2.extras import Json
import os
import json

def main():
    """Main function to seed the database"""
    print("=== Certification Database Fix Script (Direct SQL) ===")
    print("This script will reset and reseed the certification database")
    
    # Database connection string
    DB_URL = os.environ.get("DATABASE_URL", "***************************************************")
    
    try:
        # Connect to the database directly
        print(f"Connecting to database with URL: {DB_URL}...")
        conn = psycopg2.connect(DB_URL)
        cursor = conn.cursor()
        
        # Check current certification count
        cursor.execute("SELECT COUNT(*) FROM certification_explorer")
        current_count = cursor.fetchone()[0]
        print(f"Current certification count: {current_count}")
        
        # Truncate the table if there are certifications
        if current_count > 0:
            print("Clearing existing certifications table...")
            cursor.execute("TRUNCATE TABLE certification_explorer CASCADE")
            conn.commit()
            print("Table cleared successfully")
        
        # Load certification data
        print("Loading certification data...")
        certifications_data = load_certification_data()
        print(f"Loaded {len(certifications_data)} certifications from data file")
        
        # Import certifications
        success_count = 0
        error_count = 0
        
        print("Starting import process...")
        
        # Check data format (list or dict)
        if isinstance(certifications_data, list):
            print("Data format is a list of certifications")
            # Process each certification in the list
            for cert_data in certifications_data:
                try:
                    name = cert_data.get('name') or 'Unknown'
                    
                    # Extract certification data
                    provider = get_provider(cert_data)
                    description = cert_data.get('description', '')
                    cert_type = map_certification_type(cert_data.get('category', ''))
                    level = map_certification_level(cert_data.get('level', ''), cert_data.get('difficulty'))
                    domain = map_security_domain(cert_data.get('domain', ''))
                    price = cert_data.get('cost') if cert_data.get('cost') else None
                    prerequisites = cert_data.get('prerequisites', [])
                    url = cert_data.get('url', '')
                    
                    # Insert certification using direct SQL
                    cursor.execute("""
                        INSERT INTO certification_explorer
                        (name, provider, description, type, level, domain, price, prerequisites_list, skills_covered, url)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        name,
                        provider,
                        description,
                        cert_type,
                        level,
                        domain,
                        price,
                        Json(prerequisites) if prerequisites else Json([]),
                        Json([]),  # Empty skills_covered for now
                        url
                    ))
                    
                    success_count += 1
                    
                    # Commit every 100 records
                    if success_count % 100 == 0:
                        conn.commit()
                        print(f"Added {success_count} certifications so far...")
                    
                except Exception as e:
                    print(f"Error adding certification {cert_data.get('name', 'Unknown')}: {e}")
                    error_count += 1
        else:
            print("Data format is a dictionary of certifications")
            # Process each certification in the dictionary
            for name, cert_data in certifications_data.items():
                try:
                    # Extract certification data
                    provider = get_provider(cert_data)
                    description = cert_data.get('description', '')
                    cert_type = map_certification_type(cert_data.get('category', ''))
                    level = map_certification_level(cert_data.get('level', ''), cert_data.get('difficulty'))
                    domain = map_security_domain(cert_data.get('domain', ''))
                    price = cert_data.get('cost') if cert_data.get('cost') else None
                    prerequisites = cert_data.get('prerequisites', [])
                    url = cert_data.get('url', '')
                    
                    # Insert certification using direct SQL
                    cursor.execute("""
                        INSERT INTO certification_explorer
                        (name, provider, description, type, level, domain, price, prerequisites_list, skills_covered, url)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        name,
                        provider,
                        description,
                        cert_type,
                        level,
                        domain,
                        price,
                        Json(prerequisites) if prerequisites else Json([]),
                        Json([]),  # Empty skills_covered for now
                        url
                    ))
                    
                    success_count += 1
                    
                    # Commit every 100 records
                    if success_count % 100 == 0:
                        conn.commit()
                        print(f"Added {success_count} certifications so far...")
                    
                except Exception as e:
                    print(f"Error adding certification {name}: {e}")
                    error_count += 1
        
        # Final commit
        conn.commit()
        print(f"Successfully added {success_count} certifications with {error_count} errors")
        
        # Verify
        cursor.execute("SELECT COUNT(*) FROM certification_explorer")
        final_count = cursor.fetchone()[0]
        print(f"Total certifications in explorer table: {final_count}")
        
        # Close connection
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"An error occurred: {e}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()

def get_provider(cert_data):
    """Extract provider from certification data safely"""
    if 'organization' in cert_data and isinstance(cert_data['organization'], dict) and 'name' in cert_data['organization']:
        return cert_data['organization']['name']
    elif 'provider' in cert_data:
        return cert_data['provider']
    else:
        return 'Unknown'

def load_certification_data():
    """Load certification data from JSON file"""
    import json
    import os
    
    # Get the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Path to normalized data file
    json_path = os.path.join(current_dir, "data", "normalized_certifications.json")
    
    print(f"Loading certification data from JSON file: {json_path}")
    
    # Try loading from the current directory
    try:
        with open(json_path, "r") as f:
            data = json.load(f)
            print(f"Found {len(data)} total certifications to load")
            return data
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Error loading from local path: {e}")
    
    # If that fails, try looking in standard locations
    try:
        with open("/app/data/normalized_certifications.json", "r") as f:
            data = json.load(f)
            print(f"Found {len(data)} total certifications to load from /app/data")
            return data
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Error loading from /app/data: {e}")
    
    # If all else fails, return an empty list
    print("Could not load certification data, returning empty list")
    return []

def map_certification_type(category):
    """Map category to certification type enum"""
    category_lower = category.lower() if category else ""
    if "security" in category_lower or "pentest" in category_lower or "forensic" in category_lower:
        return "SECURITY"
    if "cloud" in category_lower:
        return "CLOUD"
    if "network" in category_lower:
        return "NETWORKING"
    if "dev" in category_lower or "program" in category_lower:
        return "DEVELOPMENT"
    if "devops" in category_lower or "ops" in category_lower:
        return "DEVOPS"
    return "SECURITY"  # Default

def map_certification_level(level, difficulty=None):
    """Map level string to certification level enum"""
    if not level:
        level = ""
    
    if not isinstance(level, str):
        level = str(level)
        
    level_lower = level.lower()
    
    if difficulty and isinstance(difficulty, (int, float)):
        if difficulty <= 2:
            return "BEGINNER"
        if difficulty <= 3:
            return "INTERMEDIATE"
        return "ADVANCED"
        
    if "beginner" in level_lower or "foundation" in level_lower or "fundamental" in level_lower:
        return "BEGINNER"
    if "intermediate" in level_lower or "associate" in level_lower:
        return "INTERMEDIATE"
    if "advanced" in level_lower or "expert" in level_lower or "professional" in level_lower:
        return "ADVANCED"
    return "INTERMEDIATE"  # Default

def map_security_domain(domain):
    """Map domain string to security domain enum"""
    if not domain:
        return "GENERAL_SECURITY"
    domain_lower = domain.lower()
    
    domain_map = {
        "application": "APPLICATION_SECURITY",
        "app": "APPLICATION_SECURITY",
        "network": "NETWORK_SECURITY",
        "cloud": "CLOUD_SECURITY",
        "offensive": "OFFENSIVE_SECURITY",
        "pentest": "OFFENSIVE_SECURITY",
        "defensive": "DEFENSIVE_SECURITY",
        "forensic": "FORENSICS",
        "governance": "GOVERNANCE",
        "risk": "RISK_MANAGEMENT",
        "compliance": "COMPLIANCE",
        "audit": "AUDIT",
        "identity": "IDENTITY_MANAGEMENT",
        "access": "IDENTITY_MANAGEMENT",
        "management": "SECURITY_MANAGEMENT"
    }
    
    for key, value in domain_map.items():
        if key in domain_lower:
            return value
    
    return "GENERAL_SECURITY"  # Default

if __name__ == "__main__":
    main() 