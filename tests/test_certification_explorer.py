"""
Tests for certification explorer feature
"""
import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from api.app import app
from models.certification_explorer import (
    CertificationExplorer, CertificationTypeEnum, CertificationLevelEnum,
    CertificationModel, CertificationCreate, CertificationUpdate
)
import json
from datetime import datetime

client = TestClient(app)

# Mock certification data
MOCK_CERTIFICATION_DATA = {
    "name": "Certified Ethical Hacker (CEH)",
    "provider": "EC-Council",
    "description": "The Certified Ethical Hacker certification focuses on ethical hacking methodologies.",
    "type": "security",
    "level": "intermediate",
    "exam_code": "312-50",
    "price": 1199.0,
    "duration_minutes": 240,
    "passing_score": 70,
    "skills_covered": ["penetration testing", "vulnerability assessment", "network security"],
    "url": "https://www.eccouncil.org/programs/certified-ethical-hacker-ceh/"
}


def create_mock_certification(cert_id=1, **kwargs):
    """Create a mock certification object for testing"""
    data = {**MOCK_CERTIFICATION_DATA}
    data.update(kwargs)

    cert = CertificationExplorer(
        id=cert_id,
        name=data["name"],
        provider=data["provider"],
        description=data["description"],
        type=CertificationTypeEnum(data["type"]),
        level=CertificationLevelEnum(data["level"]),
        exam_code=data["exam_code"],
        price=data["price"],
        duration_minutes=data["duration_minutes"],
        passing_score=data["passing_score"],
        skills_covered=data["skills_covered"],
        url=data["url"],
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    return cert


@patch('api.endpoints.certification_explorer.get_db')
def test_get_certifications(mock_get_db):
    """Test getting a list of certifications"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session

    # Setup mock query results
    mock_certifications = [
        create_mock_certification(cert_id=1),
        create_mock_certification(
            cert_id=2, name="CompTIA Security+", provider="CompTIA", price=349.0)
    ]

    # Configure mock query
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.offset.return_value = mock_query
    mock_query.limit.return_value = mock_query
    mock_query.all.return_value = mock_certifications
    mock_query.count.return_value = len(mock_certifications)

    # Make request
    response = client.get("/api/v1/certifications/")

    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 2
    assert data["page"] == 1
    assert data["page_size"] == 10
    assert len(data["certifications"]) == 2
    assert data["certifications"][0]["name"] == "Certified Ethical Hacker (CEH)"
    assert data["certifications"][1]["name"] == "CompTIA Security+"


@patch('api.endpoints.certification_explorer.get_db')
def test_get_certifications_with_filters(mock_get_db):
    """Test getting certifications with filters"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session

    # Setup mock query results
    mock_certifications = [
        create_mock_certification(cert_id=1)
    ]

    # Configure mock query
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.offset.return_value = mock_query
    mock_query.limit.return_value = mock_query
    mock_query.all.return_value = mock_certifications
    mock_query.count.return_value = len(mock_certifications)

    # Make request with filters
    response = client.get(
        "/api/v1/certifications/?provider=EC-Council&type=security&level=intermediate&min_price=1000")

    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 1
    assert len(data["certifications"]) == 1
    assert data["certifications"][0]["provider"] == "EC-Council"


@patch('api.endpoints.certification_explorer.get_db')
def test_get_certification_by_id(mock_get_db):
    """Test getting a certification by ID"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session

    # Setup mock query results
    mock_certification = create_mock_certification(cert_id=1)

    # Configure mock query
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = mock_certification

    # Make request
    response = client.get("/api/v1/certifications/1")

    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == 1
    assert data["name"] == "Certified Ethical Hacker (CEH)"
    assert data["provider"] == "EC-Council"


@patch('api.endpoints.certification_explorer.get_db')
def test_get_certification_not_found(mock_get_db):
    """Test getting a non-existent certification"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session

    # Configure mock query to return None
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = None

    # Make request
    response = client.get("/api/v1/certifications/999")

    # Assertions
    assert response.status_code == 404
    assert response.json()["detail"] == "Certification not found"


@patch('api.endpoints.certification_explorer.get_db')
def test_create_certification(mock_get_db):
    """Test creating a new certification"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session

    # Configure mock query to check for existing certification
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = None

    # Create certification data
    certification_data = {**MOCK_CERTIFICATION_DATA}

    # Make request
    response = client.post("/api/v1/certifications/", json=certification_data)

    # Assertions
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == certification_data["name"]
    assert data["provider"] == certification_data["provider"]

    # Verify that add, commit, and refresh were called
    mock_session.add.assert_called_once()
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()


@patch('api.endpoints.certification_explorer.get_db')
def test_create_certification_duplicate(mock_get_db):
    """Test creating a certification that already exists"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session

    # Configure mock query to return an existing certification
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = create_mock_certification()

    # Create certification data
    certification_data = {**MOCK_CERTIFICATION_DATA}

    # Make request
    response = client.post("/api/v1/certifications/", json=certification_data)

    # Assertions
    assert response.status_code == 400
    assert "already exists" in response.json()["detail"]


@patch('api.endpoints.certification_explorer.get_db')
def test_update_certification(mock_get_db):
    """Test updating a certification"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session

    # Setup mock certification to update
    mock_certification = create_mock_certification(cert_id=1)

    # Configure mock query
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = mock_certification

    # Update data
    update_data = {
        "name": "Updated CEH",
        "price": 1299.0
    }

    # Make request
    response = client.put("/api/v1/certifications/1", json=update_data)

    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == update_data["name"]
    assert data["price"] == update_data["price"]

    # Verify that commit and refresh were called
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()


@patch('api.endpoints.certification_explorer.get_db')
def test_update_certification_not_found(mock_get_db):
    """Test updating a non-existent certification"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session

    # Configure mock query to return None
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = None

    # Update data
    update_data = {
        "name": "Updated CEH",
        "price": 1299.0
    }

    # Make request
    response = client.put("/api/v1/certifications/999", json=update_data)

    # Assertions
    assert response.status_code == 404
    assert response.json()["detail"] == "Certification not found"


@patch('api.endpoints.certification_explorer.get_db')
def test_delete_certification(mock_get_db):
    """Test deleting a certification"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session

    # Setup mock certification to delete
    mock_certification = create_mock_certification(cert_id=1)

    # Configure mock query
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = mock_certification

    # Make request
    response = client.delete("/api/v1/certifications/1")

    # Assertions
    assert response.status_code == 204

    # Verify that delete and commit were called
    mock_session.delete.assert_called_once_with(mock_certification)
    mock_session.commit.assert_called_once()


@patch('api.endpoints.certification_explorer.get_db')
def test_delete_certification_not_found(mock_get_db):
    """Test deleting a non-existent certification"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session

    # Configure mock query to return None
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = None

    # Make request
    response = client.delete("/api/v1/certifications/999")

    # Assertions
    assert response.status_code == 404
    assert response.json()["detail"] == "Certification not found"


@patch('api.endpoints.certification_explorer.get_db')
def test_get_certification_providers(mock_get_db):
    """Test getting a list of certification providers"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session

    # Setup mock query results
    mock_providers = [("EC-Council",), ("CompTIA",), ("ISC2",)]

    # Configure mock query
    mock_query = mock_session.query.return_value
    mock_query.distinct.return_value = mock_query
    mock_query.all.return_value = mock_providers

    # Make request
    response = client.get("/api/v1/certifications/providers")

    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 3
    assert "EC-Council" in data
    assert "CompTIA" in data
    assert "ISC2" in data


@patch('api.endpoints.certification_explorer.get_db')
def test_get_related_certifications(mock_get_db):
    """Test getting related certifications"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session

    # Setup mock certification
    mock_certification = create_mock_certification(cert_id=1)

    # Setup mock related certifications
    mock_related = [
        create_mock_certification(
            cert_id=2, name="CompTIA Security+", provider="CompTIA"),
        create_mock_certification(cert_id=3, name="CISSP", provider="ISC2")
    ]

    # Configure mock queries
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query

    # First query to get the target certification
    mock_query.first.side_effect = [mock_certification, None]

    # Second query to get related certifications
    mock_query.limit.return_value = mock_query
    mock_query.all.return_value = mock_related

    # Make request
    response = client.get("/api/v1/certifications/related/1")

    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 2
    assert data[0]["name"] == "CompTIA Security+"
    assert data[1]["name"] == "CISSP"


if __name__ == "__main__":
    unittest.main()
