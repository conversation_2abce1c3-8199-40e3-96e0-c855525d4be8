# PostgreSQL Test Database Setup

This document explains the new PostgreSQL-based testing setup for CertRats, which replaces the previous SQLite in-memory database approach.

## Overview

We now use PostgreSQL for all environments:
- **Development**: PostgreSQL (`certrats` database)
- **Production**: PostgreSQL with SSL
- **Testing**: PostgreSQL (unique test databases per test session)

## Benefits

✅ **Consistency**: Same database engine across all environments  
✅ **Reliability**: Tests catch PostgreSQL-specific issues  
✅ **Performance**: Better test performance with proper indexing  
✅ **Features**: Access to PostgreSQL-specific features in tests  
✅ **Simplicity**: No need to maintain SQLite-specific code  

## How It Works

### Test Database Creation
- Each test session creates a unique PostgreSQL database (e.g., `certrats_test_a1b2c3d4`)
- Database is automatically created before tests run
- Database is automatically dropped after tests complete
- Each test runs in a transaction that gets rolled back

### Test Isolation
- **Session-level**: Each test session gets its own database
- **Test-level**: Each test runs in a transaction that's rolled back
- **Integration tests**: Use `clean_db_session` fixture for committed changes

## Configuration

### Environment Variables
```bash
# Test database configuration
TEST_DATABASE_URL=postgresql://postgres:postgres@localhost:5432
ENVIRONMENT=test
```

### Docker Environment
In Docker, tests automatically use:
```bash
TEST_DATABASE_URL=***********************************************
```

## Running Tests

### Quick Start
```bash
# Run all tests
./scripts/run_tests.sh

# Run specific test file
./scripts/run_tests.sh tests/test_postgresql_setup.py

# Run with pytest directly
pytest tests/test_postgresql_setup.py -v
```

### Setup and Verification
```bash
# Verify test database setup
python scripts/test_db_setup.py

# Clean up any leftover test databases
python -c "from tests.test_db_utils import cleanup_test_databases; cleanup_test_databases()"
```

## Test Fixtures

### `db_session` (Function-scoped)
- Creates a new database session for each test
- Runs in a transaction that gets rolled back
- Use for most unit tests

```python
def test_something(db_session):
    user = User(username="test")
    db_session.add(user)
    db_session.flush()  # Changes visible in this test only
    # Transaction automatically rolled back after test
```

### `clean_db_session` (Function-scoped)
- Creates a session that commits changes
- Cleans up all data after the test
- Use for integration tests that need committed data

```python
@pytest.mark.integration
def test_integration(clean_db_session):
    user = User(username="test")
    clean_db_session.add(user)
    clean_db_session.commit()  # Changes are committed
    # Data automatically cleaned up after test
```

### `db_engine` (Session-scoped)
- Creates the test database engine
- Shared across all tests in a session
- Automatically creates and drops the test database

## Test Examples

### Basic Database Test
```python
def test_database_connection(db_session):
    result = db_session.execute(text("SELECT 1"))
    assert result.scalar() == 1
```

### Model Testing
```python
def test_user_creation(db_session):
    user = User(username="testuser", email="<EMAIL>")
    db_session.add(user)
    db_session.flush()
    
    assert user.id is not None
    assert user.username == "testuser"
```

### Foreign Key Testing
```python
def test_certification_organization_relationship(db_session):
    org = Organization(name="Test Org")
    db_session.add(org)
    db_session.flush()
    
    cert = Certification(name="Test Cert", organization_id=org.id)
    db_session.add(cert)
    db_session.flush()
    
    assert cert.organization_id == org.id
```

## Troubleshooting

### PostgreSQL Not Available
```bash
# Check if PostgreSQL is running
python -c "from tests.test_db_utils import wait_for_postgres; wait_for_postgres()"

# In Docker environment
docker-compose -f .dockerwrapper/docker-compose.yml up certrats_db
```

### Permission Issues
```bash
# Ensure PostgreSQL user has database creation privileges
psql -U postgres -c "ALTER USER postgres CREATEDB;"
```

### Leftover Test Databases
```bash
# Clean up all test databases
python -c "from tests.test_db_utils import cleanup_test_databases; cleanup_test_databases()"
```

### Connection Issues
Check your database URL format:
```bash
# Correct format
postgresql://username:password@host:port/database

# Examples
postgresql://postgres:postgres@localhost:5432
***********************************************  # Docker
```

## Migration from SQLite

The migration is complete! Key changes:
- ✅ `tests/conftest.py` updated to use PostgreSQL
- ✅ `.env.test` updated with PostgreSQL URLs
- ✅ `pytest.ini` configured for PostgreSQL
- ✅ Test utilities added for database management
- ✅ Comprehensive test suite for verification

## Performance

PostgreSQL test databases typically perform better than SQLite for:
- Complex queries with joins
- Foreign key constraint checking
- Concurrent access patterns
- Large dataset testing

Typical test performance:
- Database creation: ~100ms
- Test execution: Similar to SQLite
- Database cleanup: ~50ms

## Best Practices

1. **Use `db_session` for unit tests** - automatic rollback
2. **Use `clean_db_session` for integration tests** - committed changes
3. **Mark slow tests** with `@pytest.mark.slow`
4. **Mark integration tests** with `@pytest.mark.integration`
5. **Keep test data minimal** - faster execution
6. **Use transactions** - better isolation and performance
