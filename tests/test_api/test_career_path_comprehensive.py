"""
Comprehensive tests for career path API endpoints
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
import json


@pytest.mark.unit
def test_career_path_generation_success(test_client, career_path_request_data, multiple_certifications):
    """Test successful career path generation"""
    
    # Mock the Anthropic API response
    mock_response = {
        "stages": [
            {
                "stage": "Foundation",
                "duration": "3 months",
                "certifications": ["CompTIA Security+"],
                "cost": 370,
                "description": "Build fundamental security knowledge",
                "skills_gained": ["Security fundamentals", "Risk assessment"],
                "timeline": "3 months"
            },
            {
                "stage": "Specialization",
                "duration": "6 months", 
                "certifications": ["CompTIA CySA+"],
                "cost": 392,
                "description": "Develop analyst skills",
                "skills_gained": ["Threat detection", "Incident response"],
                "timeline": "6 months"
            }
        ],
        "total_cost": 762,
        "total_duration": "9 months",
        "alignment_score": 85,
        "gap_analysis": {
            "current_strengths": ["IT Support", "Windows Administration"],
            "skill_gaps": ["Security Analysis", "Threat Detection"],
            "recommendations": ["Focus on security fundamentals first"]
        }
    }
    
    with patch('utils.claude_assistant.generate_career_path') as mock_generate:
        mock_generate.return_value = mock_response
        
        response = test_client.post("/api/v1/career-path", json=career_path_request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "stages" in data
        assert "total_cost" in data
        assert "total_duration" in data
        assert "alignment_score" in data
        assert "gap_analysis" in data
        
        # Verify data values
        assert data["alignment_score"] == 85
        assert data["total_cost"] == 762
        assert len(data["stages"]) == 2
        
        # Verify stage structure
        stage = data["stages"][0]
        assert "stage" in stage
        assert "certifications" in stage
        assert "cost" in stage
        assert "description" in stage


@pytest.mark.unit
def test_career_path_invalid_request_empty_fields(test_client):
    """Test career path generation with empty required fields"""
    invalid_data = {
        "current_role": "",  # Empty role
        "target_role": "Security Analyst",
        "years_experience": 2
    }
    
    response = test_client.post("/api/v1/career-path", json=invalid_data)
    
    assert response.status_code == 422  # Validation error
    error_detail = response.json()
    assert "detail" in error_detail


@pytest.mark.unit
def test_career_path_invalid_request_missing_fields(test_client):
    """Test career path generation with missing required fields"""
    invalid_data = {
        "current_role": "IT Support"
        # Missing target_role and years_experience
    }
    
    response = test_client.post("/api/v1/career-path", json=invalid_data)
    
    assert response.status_code == 422  # Validation error


@pytest.mark.unit
def test_career_path_invalid_experience_range(test_client):
    """Test career path generation with invalid experience range"""
    invalid_data = {
        "current_role": "IT Support",
        "target_role": "Security Analyst", 
        "years_experience": -1  # Invalid negative experience
    }
    
    response = test_client.post("/api/v1/career-path", json=invalid_data)
    
    assert response.status_code == 422


@pytest.mark.unit
def test_career_path_anthropic_api_error(test_client, career_path_request_data):
    """Test career path generation when Anthropic API fails"""
    
    with patch('utils.claude_assistant.generate_career_path') as mock_generate:
        mock_generate.side_effect = Exception("Anthropic API Error")
        
        response = test_client.post("/api/v1/career-path", json=career_path_request_data)
        
        assert response.status_code == 500
        error_data = response.json()
        assert "detail" in error_data


@pytest.mark.unit
def test_career_path_anthropic_api_timeout(test_client, career_path_request_data):
    """Test career path generation when Anthropic API times out"""
    
    with patch('utils.claude_assistant.generate_career_path') as mock_generate:
        mock_generate.side_effect = TimeoutError("Request timeout")
        
        response = test_client.post("/api/v1/career-path", json=career_path_request_data)
        
        assert response.status_code == 500


@pytest.mark.unit
def test_career_path_malformed_anthropic_response(test_client, career_path_request_data):
    """Test career path generation with malformed Anthropic response"""
    
    with patch('utils.claude_assistant.generate_career_path') as mock_generate:
        # Return malformed response
        mock_generate.return_value = {"invalid": "response"}
        
        response = test_client.post("/api/v1/career-path", json=career_path_request_data)
        
        assert response.status_code == 500


@pytest.mark.integration
def test_career_path_with_database_certifications(test_client, career_path_request_data, multiple_certifications):
    """Test career path generation using actual database certifications"""
    
    # This test uses real certifications from the database
    with patch('utils.claude_assistant.generate_career_path') as mock_generate:
        mock_generate.return_value = {
            "stages": [
                {
                    "stage": "Foundation",
                    "certifications": ["CompTIA Security+"],
                    "cost": 370,
                    "duration": "3 months",
                    "description": "Foundation stage"
                }
            ],
            "total_cost": 370,
            "total_duration": "3 months",
            "alignment_score": 85
        }
        
        response = test_client.post("/api/v1/career-path", json=career_path_request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["stages"]) > 0
        
        # Verify that the certification mentioned exists in our test database
        cert_names = [cert.name for cert in multiple_certifications]
        assert "CompTIA Security+" in cert_names


@pytest.mark.unit
def test_career_path_different_roles(test_client, multiple_certifications):
    """Test career path generation for different role transitions"""
    
    test_cases = [
        {
            "current_role": "Network Administrator",
            "target_role": "Security Engineer",
            "years_experience": 5
        },
        {
            "current_role": "Help Desk Technician", 
            "target_role": "Penetration Tester",
            "years_experience": 1
        },
        {
            "current_role": "System Administrator",
            "target_role": "CISO",
            "years_experience": 10
        }
    ]
    
    for test_case in test_cases:
        with patch('utils.claude_assistant.generate_career_path') as mock_generate:
            mock_generate.return_value = {
                "stages": [{"stage": "Test", "cost": 100}],
                "total_cost": 100,
                "alignment_score": 80
            }
            
            response = test_client.post("/api/v1/career-path", json=test_case)
            assert response.status_code == 200


@pytest.mark.unit
def test_career_path_budget_constraints(test_client, career_path_request_data):
    """Test career path generation with budget constraints"""
    
    # Test with low budget
    low_budget_data = career_path_request_data.copy()
    low_budget_data["budget"] = 500
    
    with patch('utils.claude_assistant.generate_career_path') as mock_generate:
        mock_generate.return_value = {
            "stages": [
                {
                    "stage": "Budget-Conscious Foundation",
                    "certifications": ["CompTIA Security+"],
                    "cost": 370,
                    "description": "Cost-effective entry point"
                }
            ],
            "total_cost": 370,
            "budget_analysis": {
                "within_budget": True,
                "remaining_budget": 130
            }
        }
        
        response = test_client.post("/api/v1/career-path", json=low_budget_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["total_cost"] <= low_budget_data["budget"]


@pytest.mark.unit
def test_career_path_timeline_constraints(test_client, career_path_request_data):
    """Test career path generation with timeline constraints"""
    
    # Test with short timeline
    short_timeline_data = career_path_request_data.copy()
    short_timeline_data["timeline"] = "6 months"
    
    with patch('utils.claude_assistant.generate_career_path') as mock_generate:
        mock_generate.return_value = {
            "stages": [
                {
                    "stage": "Accelerated Foundation",
                    "certifications": ["CompTIA Security+"],
                    "duration": "3 months",
                    "cost": 370
                }
            ],
            "total_duration": "3 months",
            "total_cost": 370
        }
        
        response = test_client.post("/api/v1/career-path", json=short_timeline_data)
        
        assert response.status_code == 200


@pytest.mark.performance
def test_career_path_response_time(test_client, career_path_request_data):
    """Test career path generation response time"""
    import time
    
    with patch('utils.claude_assistant.generate_career_path') as mock_generate:
        mock_generate.return_value = {
            "stages": [{"stage": "Test", "cost": 100}],
            "total_cost": 100
        }
        
        start_time = time.time()
        response = test_client.post("/api/v1/career-path", json=career_path_request_data)
        end_time = time.time()
        
        assert response.status_code == 200
        # API should respond within 5 seconds
        assert (end_time - start_time) < 5.0


@pytest.mark.security
def test_career_path_sql_injection_protection(test_client):
    """Test career path endpoint against SQL injection attempts"""
    
    malicious_data = {
        "current_role": "'; DROP TABLE users; --",
        "target_role": "Security Analyst",
        "years_experience": 2
    }
    
    with patch('utils.claude_assistant.generate_career_path') as mock_generate:
        mock_generate.return_value = {"stages": [], "total_cost": 0}
        
        response = test_client.post("/api/v1/career-path", json=malicious_data)
        
        # Should either validate input or handle gracefully
        assert response.status_code in [200, 422]
