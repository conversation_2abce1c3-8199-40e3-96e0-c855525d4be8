"""Tests for authentication API endpoints"""
import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from unittest.mock import patch, MagicMock
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import jwt

from api.app import app
from models.user import User, UserRole, UserStatus
from api.config import settings


class TestAuthEndpoints:
    """Test authentication endpoints"""
    
    def test_register_user_success(self, test_client: TestClient, db_session: Session):
        """Test successful user registration"""
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "securepassword123",
            "full_name": "New User"
        }
        
        response = test_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["username"] == "newuser"
        assert data["email"] == "<EMAIL>"
        assert "password" not in data  # Password should not be returned
        assert "access_token" in data
        assert "token_type" in data
    
    def test_register_user_duplicate_username(self, test_client: TestClient, db_session: Session):
        """Test registration with duplicate username"""
        # Create existing user
        existing_user = User(
            username="existinguser",
            email="<EMAIL>",
            password="hashed_password",
            full_name="Existing User",
            role=UserRole.USER,
            status=UserStatus.ACTIVE
        )
        db_session.add(existing_user)
        db_session.commit()
        
        user_data = {
            "username": "existinguser",  # Duplicate username
            "email": "<EMAIL>",
            "password": "securepassword123",
            "full_name": "New User"
        }
        
        response = test_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"].lower()
    
    def test_register_user_duplicate_email(self, test_client: TestClient, db_session: Session):
        """Test registration with duplicate email"""
        # Create existing user
        existing_user = User(
            username="existinguser",
            email="<EMAIL>",
            password="hashed_password",
            full_name="Existing User",
            role=UserRole.USER,
            status=UserStatus.ACTIVE
        )
        db_session.add(existing_user)
        db_session.commit()
        
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",  # Duplicate email
            "password": "securepassword123",
            "full_name": "New User"
        }
        
        response = test_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"].lower()
    
    def test_register_user_invalid_data(self, test_client: TestClient):
        """Test registration with invalid data"""
        invalid_data = {
            "username": "",  # Empty username
            "email": "invalid-email",  # Invalid email format
            "password": "123",  # Too short password
            "full_name": ""  # Empty full name
        }
        
        response = test_client.post("/api/v1/auth/register", json=invalid_data)
        
        assert response.status_code == 422  # Validation error
    
    def test_login_success(self, test_client: TestClient, db_session: Session):
        """Test successful login"""
        # Create user with known password
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        user = User(
            username="testuser",
            email="<EMAIL>",
            password=pwd_context.hash("testpassword"),
            full_name="Test User",
            role=UserRole.USER,
            status=UserStatus.ACTIVE
        )
        db_session.add(user)
        db_session.commit()
        
        login_data = {
            "username": "testuser",
            "password": "testpassword"
        }
        
        response = test_client.post("/api/v1/auth/login", data=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "token_type" in data
        assert data["token_type"] == "bearer"
    
    def test_login_invalid_credentials(self, test_client: TestClient, db_session: Session):
        """Test login with invalid credentials"""
        # Create user
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        user = User(
            username="testuser",
            email="<EMAIL>",
            password=pwd_context.hash("correctpassword"),
            full_name="Test User",
            role=UserRole.USER,
            status=UserStatus.ACTIVE
        )
        db_session.add(user)
        db_session.commit()
        
        login_data = {
            "username": "testuser",
            "password": "wrongpassword"
        }
        
        response = test_client.post("/api/v1/auth/login", data=login_data)
        
        assert response.status_code == 401
        assert "incorrect" in response.json()["detail"].lower()
    
    def test_login_nonexistent_user(self, test_client: TestClient):
        """Test login with nonexistent user"""
        login_data = {
            "username": "nonexistent",
            "password": "password"
        }
        
        response = test_client.post("/api/v1/auth/login", data=login_data)
        
        assert response.status_code == 401
        assert "incorrect" in response.json()["detail"].lower()
    
    def test_login_inactive_user(self, test_client: TestClient, db_session: Session):
        """Test login with inactive user"""
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        user = User(
            username="inactiveuser",
            email="<EMAIL>",
            password=pwd_context.hash("password"),
            full_name="Inactive User",
            role=UserRole.USER,
            status=UserStatus.INACTIVE  # Inactive user
        )
        db_session.add(user)
        db_session.commit()
        
        login_data = {
            "username": "inactiveuser",
            "password": "password"
        }
        
        response = test_client.post("/api/v1/auth/login", data=login_data)
        
        assert response.status_code == 401
        assert "inactive" in response.json()["detail"].lower()
    
    def test_get_current_user(self, test_client: TestClient, db_session: Session):
        """Test getting current user information"""
        # Create user
        user = User(
            username="currentuser",
            email="<EMAIL>",
            password="hashed_password",
            full_name="Current User",
            role=UserRole.USER,
            status=UserStatus.ACTIVE
        )
        db_session.add(user)
        db_session.commit()
        
        # Create JWT token
        token_data = {"sub": user.username, "exp": datetime.utcnow() + timedelta(hours=1)}
        token = jwt.encode(token_data, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        
        response = test_client.get(
            "/api/v1/auth/me",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["username"] == "currentuser"
        assert data["email"] == "<EMAIL>"
        assert "password" not in data
    
    def test_get_current_user_invalid_token(self, test_client: TestClient):
        """Test getting current user with invalid token"""
        response = test_client.get(
            "/api/v1/auth/me",
            headers={"Authorization": "Bearer invalid_token"}
        )
        
        assert response.status_code == 401
        assert "invalid" in response.json()["detail"].lower()
    
    def test_get_current_user_no_token(self, test_client: TestClient):
        """Test getting current user without token"""
        response = test_client.get("/api/v1/auth/me")
        
        assert response.status_code == 401
    
    def test_refresh_token(self, test_client: TestClient, db_session: Session):
        """Test token refresh"""
        # Create user
        user = User(
            username="refreshuser",
            email="<EMAIL>",
            password="hashed_password",
            full_name="Refresh User",
            role=UserRole.USER,
            status=UserStatus.ACTIVE
        )
        db_session.add(user)
        db_session.commit()
        
        # Create valid token
        token_data = {"sub": user.username, "exp": datetime.utcnow() + timedelta(hours=1)}
        token = jwt.encode(token_data, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        
        response = test_client.post(
            "/api/v1/auth/refresh",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "token_type" in data
        assert data["token_type"] == "bearer"
    
    def test_logout(self, test_client: TestClient, db_session: Session):
        """Test user logout"""
        # Create user
        user = User(
            username="logoutuser",
            email="<EMAIL>",
            password="hashed_password",
            full_name="Logout User",
            role=UserRole.USER,
            status=UserStatus.ACTIVE
        )
        db_session.add(user)
        db_session.commit()
        
        # Create valid token
        token_data = {"sub": user.username, "exp": datetime.utcnow() + timedelta(hours=1)}
        token = jwt.encode(token_data, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        
        response = test_client.post(
            "/api/v1/auth/logout",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        assert "successfully" in response.json()["message"].lower()
    
    def test_change_password(self, test_client: TestClient, db_session: Session):
        """Test password change"""
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # Create user
        user = User(
            username="changeuser",
            email="<EMAIL>",
            password=pwd_context.hash("oldpassword"),
            full_name="Change User",
            role=UserRole.USER,
            status=UserStatus.ACTIVE
        )
        db_session.add(user)
        db_session.commit()
        
        # Create valid token
        token_data = {"sub": user.username, "exp": datetime.utcnow() + timedelta(hours=1)}
        token = jwt.encode(token_data, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        
        password_data = {
            "current_password": "oldpassword",
            "new_password": "newpassword123"
        }
        
        response = test_client.post(
            "/api/v1/auth/change-password",
            json=password_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 200
        assert "successfully" in response.json()["message"].lower()
    
    def test_change_password_wrong_current(self, test_client: TestClient, db_session: Session):
        """Test password change with wrong current password"""
        from passlib.context import CryptContext
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # Create user
        user = User(
            username="wronguser",
            email="<EMAIL>",
            password=pwd_context.hash("correctpassword"),
            full_name="Wrong User",
            role=UserRole.USER,
            status=UserStatus.ACTIVE
        )
        db_session.add(user)
        db_session.commit()
        
        # Create valid token
        token_data = {"sub": user.username, "exp": datetime.utcnow() + timedelta(hours=1)}
        token = jwt.encode(token_data, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        
        password_data = {
            "current_password": "wrongpassword",
            "new_password": "newpassword123"
        }
        
        response = test_client.post(
            "/api/v1/auth/change-password",
            json=password_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 400
        assert "incorrect" in response.json()["detail"].lower()


class TestTokenValidation:
    """Test JWT token validation"""
    
    def test_expired_token(self, test_client: TestClient, db_session: Session):
        """Test access with expired token"""
        # Create user
        user = User(
            username="expireduser",
            email="<EMAIL>",
            password="hashed_password",
            full_name="Expired User",
            role=UserRole.USER,
            status=UserStatus.ACTIVE
        )
        db_session.add(user)
        db_session.commit()
        
        # Create expired token
        token_data = {"sub": user.username, "exp": datetime.utcnow() - timedelta(hours=1)}
        expired_token = jwt.encode(token_data, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        
        response = test_client.get(
            "/api/v1/auth/me",
            headers={"Authorization": f"Bearer {expired_token}"}
        )
        
        assert response.status_code == 401
        assert "expired" in response.json()["detail"].lower()
    
    def test_malformed_token(self, test_client: TestClient):
        """Test access with malformed token"""
        response = test_client.get(
            "/api/v1/auth/me",
            headers={"Authorization": "Bearer malformed.token.here"}
        )
        
        assert response.status_code == 401
    
    def test_token_with_invalid_user(self, test_client: TestClient):
        """Test token with user that doesn't exist"""
        # Create token for non-existent user
        token_data = {"sub": "nonexistentuser", "exp": datetime.utcnow() + timedelta(hours=1)}
        token = jwt.encode(token_data, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        
        response = test_client.get(
            "/api/v1/auth/me",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert response.status_code == 401
