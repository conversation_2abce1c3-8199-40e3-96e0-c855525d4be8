import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime
from api.app import app
from models.certification import Certification, Organization

def test_career_path_input_validation():
    """Test input validation for career path endpoint"""
    client = TestClient(app)
    # Test missing required fields
    response = client.post("/api/v1/career-path", json={
        "interests": ["Network Security"]
        # Missing other required fields
    })
    assert response.status_code == 422  # Validation error

def test_career_path_different_experience_levels(db_session: Session):
    """Test career path generation with different experience levels"""
    current_time = datetime.utcnow()

    # Create organization first since it's required for certification
    org = Organization(
        name="Test Org",
        description="Test organization",
        created_at=current_time,
        updated_at=current_time
    )
    db_session.add(org)
    db_session.commit()

    # Create test certifications with all required fields
    cert = Certification(
        name="Test Cert",
        domain="Network Security",
        level="Entry Level",
        difficulty=1,
        cost=500,
        created_at=current_time,
        organization_id=org.id,  # Required foreign key
        description="Test certification",
        validity_period=12,  # In months
        exam_code="TEST-001",
        custom_hours=40
    )
    db_session.add(cert)
    db_session.commit()

    client = TestClient(app)
    # Test with beginner
    response = client.post("/api/v1/career-path", json={
        "interests": ["Network Security"],
        "years_experience": 0,
        "current_role": "Student",
        "target_role": "Security Analyst",
        "learning_style": "Mixed",
        "study_hours": 10
    })
    assert response.status_code == 200
    assert "career_path" in response.json()

    # Test with expert
    response = client.post("/api/v1/career-path", json={
        "interests": ["Network Security"],
        "years_experience": 15,
        "current_role": "Security Manager",
        "target_role": "CISO",
        "learning_style": "Mixed",
        "study_hours": 5
    })
    assert response.status_code == 200
    assert "career_path" in response.json()