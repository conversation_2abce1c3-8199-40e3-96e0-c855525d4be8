"""Integration tests for API endpoints with focus on parameterized queries"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime
from api.app import app
from models.job import SecurityJob

def test_job_search_parameterized(db_session: Session):
    """Test job search endpoint with various parameters"""
    # Create test jobs
    test_jobs = [
        SecurityJob(
            title="Security Analyst",
            domain="Security Operations",
            experience_level="Entry Level",
            created_at=datetime.utcnow()
        ),
        SecurityJob(
            title="SQL' OR '1'='1",  # Test SQL injection attempt
            domain="Security Testing",
            experience_level="Mid Level",
            created_at=datetime.utcnow()
        ),
        SecurityJob(
            title="Security Engineer",
            domain="Security Engineering",
            experience_level="Senior",
            created_at=datetime.utcnow()
        )
    ]

    for job in test_jobs:
        db_session.add(job)
    db_session.commit()

    client = TestClient(app)
    # Test search with normal parameters
    response = client.get("/api/v1/jobs/search?term=Security&domain=Security+Operations")
    assert response.status_code == 200
    data = response.json()
    assert len(data["jobs"]) == 1
    assert data["jobs"][0]["title"] == "Security Analyst"

    # Test search with SQL injection attempt
    response = client.get("/api/v1/jobs/search?term=SQL'+OR+'1'='1")
    assert response.status_code == 200
    data = response.json()
    assert len(data["jobs"]) == 1  # Should only find the exact match
    assert data["jobs"][0]["title"] == "SQL' OR '1'='1"

    # Test pagination parameters
    response = client.get("/api/v1/jobs/search?page=0&per_page=2")
    assert response.status_code == 200
    data = response.json()
    assert len(data["jobs"]) == 2
    assert data["total"] == 3

def test_career_path_db_interactions(db_session: Session):
    """Test career path endpoint's database interactions"""
    # Create test certification
    from models.certification import Certification
    cert = Certification(
        name="Test Cert",
        domain="Security Operations",
        level="Beginner",
        cost=500,
        custom_hours=40
    )
    db_session.add(cert)
    db_session.commit()

    client = TestClient(app)
    # Test career path generation with parameterized certification query
    response = client.post("/api/v1/career-path", json={
        "completed_certifications": [],
        "interests": ["Security Operations"],
        "years_experience": 2,
        "current_role": "Junior Analyst",
        "target_role": "Security Engineer",
        "learning_style": "Mixed",
        "study_hours": 10
    })
    
    assert response.status_code == 200
    data = response.json()
    assert "career_path" in data
    assert isinstance(data["career_path"], list)

def test_health_check_parameterized():
    """Test health check endpoint"""
    client = TestClient(app)
    response = client.get("/api/v1/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["database"] == "connected"