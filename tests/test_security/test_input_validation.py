"""Test input validation and security measures"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from datetime import datetime
from api.app import app
from models.job import SecurityJob

def test_sql_injection_prevention():
    """Test that API endpoints are protected against SQL injection"""
    client = TestClient(app)

    # Test with SQL injection attempts in search parameters
    response = client.get("/api/v1/jobs/search?term='; DROP TABLE security_jobs; --")
    assert response.status_code == 200

    # Check that the response is normal despite the injection attempt
    assert "jobs" in response.json()
    assert isinstance(response.json()["jobs"], list)

def test_xss_prevention(db_session):
    """Test that user input is properly sanitized to prevent XSS"""
    client = TestClient(app)

    # Create a job with malicious script content
    current_time = datetime.utcnow()
    job = SecurityJob(
        title="<script>alert('XSS')</script>Test Job",
        domain="Security Testing",
        description="<img src=x onerror=alert('XSS')>",
        created_at=current_time,
        updated_at=current_time,
        experience_level="Entry Level"
    )
    db_session.add(job)
    db_session.commit()

    # Search for the job
    response = client.get("/api/v1/jobs/search?term=Test")
    assert response.status_code == 200

    # Verify that the response contains escaped content
    assert "jobs" in response.json()

    # The content should be returned sanitized or properly JSON encoded
    job_data = next((j for j in response.json()["jobs"] if "Test Job" in j["title"]), None)
    assert job_data is not None
    assert "<script>" not in job_data["title"] or job_data["title"].startswith("&lt;script&gt;")