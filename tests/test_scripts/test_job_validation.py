"""Tests for job title validation logic"""
import pytest
from scripts.populate_security_jobs import is_valid_job_title, SECURITY_DOMAINS

@pytest.mark.parametrize("title,expected", [
    ("Security Analyst", True),
    ("Senior Security Engineer", True),
    ("Cloud Security Architect", True),
    ("", False),
    ("a" * 101, False),  # Too long
    ("security analyst", <PERSON>als<PERSON>),  # All lowercase
    ("SECURITY ANALYST", False),  # All uppercase
    ("http://example.com", False),  # URL
    ("Reddit - Security Jobs", False),  # Invalid format
    ("Security Analyst?", False),  # Invalid character
    ("The Security Analyst", False),  # Starts with article
    ("Security Analyst...", False),  # Invalid format
    ("accessed on February", False),  # Invalid format
])
def test_job_title_validation(title, expected):
    """Test various job titles against validation rules"""
    assert is_valid_job_title(title) == expected

def test_security_domains_exist():
    """Test that security domains are properly defined"""
    assert len(SECURITY_DOMAINS) > 0
    assert "Security Operations" in SECURITY_DOMAINS
    assert "Security Engineering" in SECURITY_DOMAINS
    assert "Security Management" in SECURITY_DOMAINS

@pytest.mark.parametrize("domain", SECURITY_DOMAINS)
def test_valid_job_title_for_each_domain(domain):
    """Test that valid job titles can be created for each domain"""
    title = f"Senior {domain} Specialist"
    assert is_valid_job_title(title)
