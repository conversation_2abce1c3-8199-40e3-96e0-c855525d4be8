import pytest
from unittest.mock import patch
import os
from translations import get_available_languages, _, init_localization, switch_language
import streamlit as st

def test_available_languages():
    """Test that supported languages are correctly detected"""
    languages = get_available_languages()
    assert "en" in languages
    assert isinstance(languages, list)
    assert len(languages) > 0

@pytest.mark.parametrize("language", ["en", "de", "es", "fr"])
def test_translation_loading(language):
    """Test that translations can be loaded for different languages"""
    with patch("streamlit.session_state") as mock_state:
        mock_state.language = language
        init_localization()
        
        # Test a simple string translation
        translated = _("Home")
        assert isinstance(translated, str)
        assert len(translated) > 0
