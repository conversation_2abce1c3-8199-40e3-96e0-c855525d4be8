"""Test suite for certification path visualization component"""
import pytest
from unittest.mock import patch
import streamlit as st
from components.certification_path import create_cert_path_html, display_certification_path

def test_create_cert_path_html_empty():
    """Test behavior with empty certifications dictionary"""
    html = create_cert_path_html({})
    assert "cert-path-viz" in html
    assert "Loaded data: {" in html

def test_create_cert_path_html_with_certs():
    """Test with sample certification data"""
    sample_certs = {
        "Cert A": {"level": "Entry Level", "domain": "Network Security", "difficulty": "Low"},
        "Cert B": {"level": "Advanced", "domain": "Network Security", "difficulty": "High", 
                  "related_certs": {"prerequisites": ["Cert A"]}}
    }
    
    html = create_cert_path_html(sample_certs)
    assert "cert-path-viz" in html
    assert "nodes" in html
    assert "links" in html
