"""
Tests for reports feature
"""
import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.orm import Session
from main import app
from models.reports import (
    Report, ReportTypeEnum, ReportFormatEnum, ReportFrequencyEnum,
    ReportModel, ReportCreate, ReportUpdate
)
from models.user import User, UserR<PERSON>
from datetime import datetime, timedelta
import json
import os

client = TestClient(app)

# Mock JWT token for authentication
def create_test_token(username="testuser", role=UserRole.ADMIN):
    """Create a test JWT token"""
    return f"mock_token_{username}_{role.value}"

# Mock report data
MOCK_REPORT_DATA = {
    "title": "Test Report",
    "description": "Test report description",
    "type": "certification_progress",
    "parameters": {"param1": "value1", "param2": "value2"},
    "format": "pdf",
    "is_scheduled": False,
    "frequency": None,
    "next_run_date": None,
    "user_id": 1
}

def create_mock_report(report_id=1, **kwargs):
    """Create a mock report object for testing"""
    data = {**MOCK_REPORT_DATA}
    data.update(kwargs)
    
    report = Report(
        id=report_id,
        title=data["title"],
        description=data["description"],
        type=ReportTypeEnum(data["type"]),
        parameters=data["parameters"],
        format=ReportFormatEnum(data["format"]),
        is_scheduled=data["is_scheduled"],
        frequency=ReportFrequencyEnum(data["frequency"]) if data["frequency"] else None,
        next_run_date=data["next_run_date"],
        user_id=data["user_id"],
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        last_run_at=None
    )
    return report

def create_mock_user(user_id=1, role=UserRole.ADMIN):
    """Create a mock user object for testing"""
    user = User(
        id=user_id,
        username="testuser",
        email="<EMAIL>",
        password="hashed_password",
        full_name="Test User",
        role=role,
        status="active",
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        last_login=datetime.utcnow()
    )
    return user

@patch('api.endpoints.reports.get_current_active_user')
@patch('api.endpoints.reports.get_db')
def test_get_reports(mock_get_db, mock_get_current_user):
    """Test getting a list of reports"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session
    
    # Setup mock user
    mock_user = create_mock_user()
    mock_get_current_user.return_value = mock_user
    
    # Setup mock query results
    mock_reports = [
        create_mock_report(report_id=1),
        create_mock_report(report_id=2, title="Another Report", type="user_activity")
    ]
    
    # Configure mock query
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.offset.return_value = mock_query
    mock_query.limit.return_value = mock_query
    mock_query.all.return_value = mock_reports
    mock_query.count.return_value = len(mock_reports)
    
    # Make request with auth header
    response = client.get(
        "/reports/",
        headers={"Authorization": f"Bearer {create_test_token()}"}
    )
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 2
    assert data["page"] == 1
    assert data["page_size"] == 10
    assert len(data["reports"]) == 2
    assert data["reports"][0]["title"] == "Test Report"
    assert data["reports"][1]["title"] == "Another Report"

@patch('api.endpoints.reports.get_current_active_user')
@patch('api.endpoints.reports.get_db')
def test_get_reports_with_filters(mock_get_db, mock_get_current_user):
    """Test getting reports with filters"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session
    
    # Setup mock user
    mock_user = create_mock_user()
    mock_get_current_user.return_value = mock_user
    
    # Setup mock query results
    mock_reports = [
        create_mock_report(report_id=1)
    ]
    
    # Configure mock query
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.offset.return_value = mock_query
    mock_query.limit.return_value = mock_query
    mock_query.all.return_value = mock_reports
    mock_query.count.return_value = len(mock_reports)
    
    # Make request with filters
    response = client.get(
        "/reports/?type=certification_progress&is_scheduled=false",
        headers={"Authorization": f"Bearer {create_test_token()}"}
    )
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 1
    assert len(data["reports"]) == 1
    assert data["reports"][0]["type"] == "certification_progress"

@patch('api.endpoints.reports.get_current_active_user')
@patch('api.endpoints.reports.get_db')
def test_get_report_by_id(mock_get_db, mock_get_current_user):
    """Test getting a report by ID"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session
    
    # Setup mock user
    mock_user = create_mock_user()
    mock_get_current_user.return_value = mock_user
    
    # Setup mock query results
    mock_report = create_mock_report(report_id=1)
    
    # Configure mock query
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = mock_report
    
    # Make request
    response = client.get(
        "/reports/1",
        headers={"Authorization": f"Bearer {create_test_token()}"}
    )
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == 1
    assert data["title"] == "Test Report"
    assert data["type"] == "certification_progress"

@patch('api.endpoints.reports.get_current_active_user')
@patch('api.endpoints.reports.get_db')
def test_get_report_not_found(mock_get_db, mock_get_current_user):
    """Test getting a non-existent report"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session
    
    # Setup mock user
    mock_user = create_mock_user()
    mock_get_current_user.return_value = mock_user
    
    # Configure mock query to return None
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = None
    
    # Make request
    response = client.get(
        "/reports/999",
        headers={"Authorization": f"Bearer {create_test_token()}"}
    )
    
    # Assertions
    assert response.status_code == 404
    assert response.json()["detail"] == "Report not found"

@patch('api.endpoints.reports.get_current_active_user')
@patch('api.endpoints.reports.get_db')
def test_create_report(mock_get_db, mock_get_current_user):
    """Test creating a new report"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session
    
    # Setup mock user
    mock_user = create_mock_user()
    mock_get_current_user.return_value = mock_user
    
    # Create report data
    report_data = {
        "title": "New Report",
        "description": "New report description",
        "type": "certification_progress",
        "parameters": {"param1": "value1"},
        "format": "pdf",
        "is_scheduled": False
    }
    
    # Make request
    response = client.post(
        "/reports/",
        json=report_data,
        headers={"Authorization": f"Bearer {create_test_token()}"}
    )
    
    # Assertions
    assert response.status_code == 201
    data = response.json()
    assert data["title"] == report_data["title"]
    assert data["description"] == report_data["description"]
    
    # Verify that add, commit, and refresh were called
    mock_session.add.assert_called_once()
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()

@patch('api.endpoints.reports.get_current_active_user')
@patch('api.endpoints.reports.get_db')
def test_update_report(mock_get_db, mock_get_current_user):
    """Test updating a report"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session
    
    # Setup mock user
    mock_user = create_mock_user()
    mock_get_current_user.return_value = mock_user
    
    # Setup mock report to update
    mock_report = create_mock_report(report_id=1)
    
    # Configure mock query
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = mock_report
    
    # Update data
    update_data = {
        "title": "Updated Report",
        "is_scheduled": True,
        "frequency": "weekly"
    }
    
    # Make request
    response = client.put(
        "/reports/1",
        json=update_data,
        headers={"Authorization": f"Bearer {create_test_token()}"}
    )
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == update_data["title"]
    assert data["is_scheduled"] == update_data["is_scheduled"]
    assert data["frequency"] == update_data["frequency"]
    
    # Verify that commit and refresh were called
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()

@patch('api.endpoints.reports.get_current_active_user')
@patch('api.endpoints.reports.get_db')
def test_update_report_not_found(mock_get_db, mock_get_current_user):
    """Test updating a non-existent report"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session
    
    # Setup mock user
    mock_user = create_mock_user()
    mock_get_current_user.return_value = mock_user
    
    # Configure mock query to return None
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = None
    
    # Update data
    update_data = {
        "title": "Updated Report"
    }
    
    # Make request
    response = client.put(
        "/reports/999",
        json=update_data,
        headers={"Authorization": f"Bearer {create_test_token()}"}
    )
    
    # Assertions
    assert response.status_code == 404
    assert response.json()["detail"] == "Report not found"

@patch('api.endpoints.reports.get_current_active_user')
@patch('api.endpoints.reports.get_db')
def test_delete_report(mock_get_db, mock_get_current_user):
    """Test deleting a report"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session
    
    # Setup mock user
    mock_user = create_mock_user()
    mock_get_current_user.return_value = mock_user
    
    # Setup mock report to delete
    mock_report = create_mock_report(report_id=1)
    
    # Configure mock query
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = mock_report
    
    # Make request
    response = client.delete(
        "/reports/1",
        headers={"Authorization": f"Bearer {create_test_token()}"}
    )
    
    # Assertions
    assert response.status_code == 204
    
    # Verify that delete and commit were called
    mock_session.delete.assert_called_once_with(mock_report)
    mock_session.commit.assert_called_once()

@patch('api.endpoints.reports.get_current_active_user')
@patch('api.endpoints.reports.get_db')
def test_delete_report_not_found(mock_get_db, mock_get_current_user):
    """Test deleting a non-existent report"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session
    
    # Setup mock user
    mock_user = create_mock_user()
    mock_get_current_user.return_value = mock_user
    
    # Configure mock query to return None
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = None
    
    # Make request
    response = client.delete(
        "/reports/999",
        headers={"Authorization": f"Bearer {create_test_token()}"}
    )
    
    # Assertions
    assert response.status_code == 404
    assert response.json()["detail"] == "Report not found"

@patch('api.endpoints.reports.get_current_active_user')
def test_get_report_types(mock_get_current_user):
    """Test getting a list of report types"""
    # Setup mock user
    mock_user = create_mock_user()
    mock_get_current_user.return_value = mock_user
    
    # Make request
    response = client.get(
        "/reports/types",
        headers={"Authorization": f"Bearer {create_test_token()}"}
    )
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert "certification_progress" in data
    assert "user_activity" in data
    assert "certification_popularity" in data
    assert "skill_gaps" in data
    assert "custom" in data

@patch('api.endpoints.reports.get_current_active_user')
def test_get_report_formats(mock_get_current_user):
    """Test getting a list of report formats"""
    # Setup mock user
    mock_user = create_mock_user()
    mock_get_current_user.return_value = mock_user
    
    # Make request
    response = client.get(
        "/reports/formats",
        headers={"Authorization": f"Bearer {create_test_token()}"}
    )
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert "pdf" in data
    assert "csv" in data
    assert "json" in data
    assert "html" in data

@patch('api.endpoints.reports.get_current_active_user')
def test_get_report_frequencies(mock_get_current_user):
    """Test getting a list of report frequencies"""
    # Setup mock user
    mock_user = create_mock_user()
    mock_get_current_user.return_value = mock_user
    
    # Make request
    response = client.get(
        "/reports/frequencies",
        headers={"Authorization": f"Bearer {create_test_token()}"}
    )
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert "daily" in data
    assert "weekly" in data
    assert "monthly" in data
    assert "quarterly" in data
    assert "yearly" in data
    assert "once" in data

@patch('api.endpoints.reports.generate_report_background')
@patch('api.endpoints.reports.get_current_active_user')
@patch('api.endpoints.reports.get_db')
def test_generate_report(mock_get_db, mock_get_current_user, mock_generate_report):
    """Test generating a report"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session
    
    # Setup mock user
    mock_user = create_mock_user()
    mock_get_current_user.return_value = mock_user
    
    # Setup mock report
    mock_report = create_mock_report(report_id=1)
    
    # Configure mock query
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = mock_report
    
    # Configure mock generate function
    mock_generate_report.return_value = None
    
    # Make request
    response = client.post(
        "/reports/1/generate",
        headers={"Authorization": f"Bearer {create_test_token()}"}
    )
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "Report generated"

@patch('api.endpoints.reports.get_admin_user')
@patch('api.endpoints.reports.get_db')
def test_run_scheduled_reports(mock_get_db, mock_get_admin_user):
    """Test running scheduled reports"""
    # Setup mock database session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value = mock_session
    
    # Setup mock admin user
    mock_user = create_mock_user(role=UserRole.ADMIN)
    mock_get_admin_user.return_value = mock_user
    
    # Setup mock scheduled reports
    mock_reports = [
        create_mock_report(report_id=1, is_scheduled=True, frequency="daily", next_run_date=datetime.utcnow()),
        create_mock_report(report_id=2, is_scheduled=True, frequency="weekly", next_run_date=datetime.utcnow())
    ]
    
    # Configure mock query
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.all.return_value = mock_reports
    
    # Make request
    response = client.get(
        "/reports/scheduled/run",
        headers={"Authorization": f"Bearer {create_test_token(role=UserRole.ADMIN)}"}
    )
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "Scheduled reports generation started"
    assert data["count"] == 2
    assert len(data["reports"]) == 2

if __name__ == "__main__":
    unittest.main()
