"""
Performance tests for CertRats API endpoints
"""
import pytest
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import patch
import statistics


@pytest.mark.performance
def test_career_path_endpoint_response_time(test_client, career_path_request_data):
    """Test career path endpoint response time under normal load"""
    
    with patch('utils.claude_assistant.generate_career_path') as mock_generate:
        mock_generate.return_value = {
            "stages": [{"stage": "Test", "cost": 100}],
            "total_cost": 100,
            "alignment_score": 80
        }
        
        # Measure response time
        start_time = time.time()
        response = test_client.post("/api/v1/career-path", json=career_path_request_data)
        end_time = time.time()
        
        response_time = end_time - start_time
        
        assert response.status_code == 200
        # API should respond within 2 seconds
        assert response_time < 2.0
        
        print(f"Career path response time: {response_time:.3f} seconds")


@pytest.mark.performance
def test_career_path_concurrent_requests(test_client, career_path_request_data):
    """Test career path endpoint under concurrent load"""
    
    def make_request():
        with patch('utils.claude_assistant.generate_career_path') as mock_generate:
            mock_generate.return_value = {
                "stages": [{"stage": "Test", "cost": 100}],
                "total_cost": 100
            }
            
            start_time = time.time()
            response = test_client.post("/api/v1/career-path", json=career_path_request_data)
            end_time = time.time()
            
            return {
                "status_code": response.status_code,
                "response_time": end_time - start_time
            }
    
    # Test with 10 concurrent requests
    num_requests = 10
    
    with ThreadPoolExecutor(max_workers=num_requests) as executor:
        start_time = time.time()
        futures = [executor.submit(make_request) for _ in range(num_requests)]
        results = [future.result() for future in futures]
        total_time = time.time() - start_time
    
    # Verify all requests succeeded
    for result in results:
        assert result["status_code"] == 200
    
    # Calculate performance metrics
    response_times = [result["response_time"] for result in results]
    avg_response_time = statistics.mean(response_times)
    max_response_time = max(response_times)
    
    print(f"Concurrent requests: {num_requests}")
    print(f"Total time: {total_time:.3f} seconds")
    print(f"Average response time: {avg_response_time:.3f} seconds")
    print(f"Max response time: {max_response_time:.3f} seconds")
    
    # Performance assertions
    assert avg_response_time < 3.0  # Average should be under 3 seconds
    assert max_response_time < 5.0  # No request should take more than 5 seconds
    assert total_time < 10.0  # All requests should complete within 10 seconds


@pytest.mark.performance
def test_certification_search_performance(test_client, multiple_certifications):
    """Test certification search endpoint performance"""
    
    search_terms = ["Security", "Network", "CompTIA", "Advanced", "Entry"]
    response_times = []
    
    for term in search_terms:
        start_time = time.time()
        response = test_client.get(f"/api/v1/certifications/search?q={term}")
        end_time = time.time()
        
        response_time = end_time - start_time
        response_times.append(response_time)
        
        assert response.status_code == 200
        print(f"Search '{term}' response time: {response_time:.3f} seconds")
    
    avg_search_time = statistics.mean(response_times)
    max_search_time = max(response_times)
    
    print(f"Average search time: {avg_search_time:.3f} seconds")
    print(f"Max search time: {max_search_time:.3f} seconds")
    
    # Search should be fast
    assert avg_search_time < 0.5  # Average under 500ms
    assert max_search_time < 1.0  # No search over 1 second


@pytest.mark.performance
def test_database_query_performance(db_session, multiple_certifications):
    """Test database query performance"""
    
    # Test simple query performance
    start_time = time.time()
    all_certs = db_session.query(CertificationExplorer).all()
    simple_query_time = time.time() - start_time
    
    assert len(all_certs) >= 3
    print(f"Simple query time: {simple_query_time:.3f} seconds")
    
    # Test filtered query performance
    start_time = time.time()
    filtered_certs = db_session.query(CertificationExplorer).filter(
        CertificationExplorer.cost < 500
    ).all()
    filtered_query_time = time.time() - start_time
    
    print(f"Filtered query time: {filtered_query_time:.3f} seconds")
    
    # Test complex query performance
    start_time = time.time()
    complex_certs = db_session.query(CertificationExplorer).filter(
        (CertificationExplorer.cost < 1000) &
        (CertificationExplorer.difficulty.in_(["Intermediate", "Advanced"]))
    ).order_by(CertificationExplorer.cost.asc()).all()
    complex_query_time = time.time() - start_time
    
    print(f"Complex query time: {complex_query_time:.3f} seconds")
    
    # Database queries should be fast
    assert simple_query_time < 0.1  # Under 100ms
    assert filtered_query_time < 0.1  # Under 100ms
    assert complex_query_time < 0.2  # Under 200ms


@pytest.mark.performance
def test_memory_usage_during_large_operations(db_session):
    """Test memory usage during large database operations"""
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    # Create a large number of certifications
    large_batch = []
    for i in range(1000):
        cert = CertificationExplorer(
            name=f"Performance Test Cert {i}",
            provider="Performance Test Provider",
            category="Performance Test",
            difficulty="Easy",
            cost=100 + i
        )
        large_batch.append(cert)
    
    # Measure memory during bulk insert
    start_time = time.time()
    db_session.add_all(large_batch)
    db_session.commit()
    bulk_insert_time = time.time() - start_time
    
    peak_memory = process.memory_info().rss / 1024 / 1024  # MB
    memory_increase = peak_memory - initial_memory
    
    print(f"Bulk insert time (1000 records): {bulk_insert_time:.3f} seconds")
    print(f"Memory increase: {memory_increase:.2f} MB")
    
    # Clean up
    db_session.query(CertificationExplorer).filter(
        CertificationExplorer.provider == "Performance Test Provider"
    ).delete()
    db_session.commit()
    
    # Performance assertions
    assert bulk_insert_time < 5.0  # Should complete within 5 seconds
    assert memory_increase < 100  # Should not use more than 100MB additional memory


@pytest.mark.performance
def test_api_endpoint_stress_test(test_client):
    """Stress test API endpoints with rapid requests"""
    
    endpoints = [
        ("/api/v1/health", "GET", None),
        ("/api/v1/certifications", "GET", None),
    ]
    
    for endpoint, method, data in endpoints:
        response_times = []
        error_count = 0
        
        # Make 50 rapid requests
        for i in range(50):
            start_time = time.time()
            
            if method == "GET":
                response = test_client.get(endpoint)
            elif method == "POST":
                response = test_client.post(endpoint, json=data)
            
            end_time = time.time()
            response_time = end_time - start_time
            response_times.append(response_time)
            
            if response.status_code >= 400:
                error_count += 1
        
        # Calculate metrics
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        error_rate = error_count / 50
        
        print(f"Endpoint: {endpoint}")
        print(f"Average response time: {avg_response_time:.3f} seconds")
        print(f"Max response time: {max_response_time:.3f} seconds")
        print(f"Error rate: {error_rate:.2%}")
        
        # Stress test assertions
        assert error_rate < 0.05  # Less than 5% error rate
        assert avg_response_time < 1.0  # Average under 1 second
        assert max_response_time < 3.0  # No request over 3 seconds


@pytest.mark.performance
def test_anthropic_api_mock_performance():
    """Test performance of mocked Anthropic API calls"""
    
    def mock_anthropic_call():
        # Simulate API processing time
        time.sleep(0.1)  # 100ms simulated API call
        return {
            "stages": [{"stage": "Test", "cost": 100}],
            "total_cost": 100
        }
    
    # Test sequential calls
    start_time = time.time()
    for _ in range(10):
        result = mock_anthropic_call()
        assert result is not None
    sequential_time = time.time() - start_time
    
    print(f"Sequential API calls (10): {sequential_time:.3f} seconds")
    
    # Sequential calls should complete in reasonable time
    assert sequential_time < 2.0  # Should complete within 2 seconds


@pytest.mark.performance
@pytest.mark.slow
def test_long_running_career_path_generation(test_client, career_path_request_data):
    """Test career path generation with simulated long-running AI processing"""
    
    def slow_mock_generate(*args, **kwargs):
        # Simulate longer AI processing time
        time.sleep(2.0)  # 2 second delay
        return {
            "stages": [{"stage": "Complex Analysis", "cost": 500}],
            "total_cost": 500,
            "alignment_score": 90
        }
    
    with patch('utils.claude_assistant.generate_career_path', side_effect=slow_mock_generate):
        start_time = time.time()
        response = test_client.post("/api/v1/career-path", json=career_path_request_data)
        end_time = time.time()
        
        total_time = end_time - start_time
        
        assert response.status_code == 200
        print(f"Long-running career path generation: {total_time:.3f} seconds")
        
        # Should handle long-running requests gracefully
        assert total_time >= 2.0  # Should include the delay
        assert total_time < 5.0  # But not take too much longer
