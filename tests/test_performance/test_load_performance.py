"""
Performance and Load Tests.

This module tests system performance under various load conditions including:
- High concurrent user loads
- Large dataset processing
- Memory usage optimization
- Database query performance
- API response times
"""
import pytest
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import patch, Mock
import statistics
import psutil
import gc

from fastapi.testclient import TestClient
from sqlalchemy import text

from api.app import app
from models.certification import Certification, Organization
from models.user import User, UserRole, UserStatus
from shared.api_endpoints import API_PREFIX
from tests.conftest import create_test_token


@pytest.mark.performance
class TestAPIPerformance:
    """Test API endpoint performance."""
    
    def test_certification_list_performance(self, client, auth_headers, db_session):
        """Test performance of certification listing endpoint."""
        # Create test data
        org = Organization(
            name="Performance Test Org",
            country="US",
            website="https://perftest.com",
            description="Performance testing organization"
        )
        db_session.add(org)
        db_session.flush()
        
        # Create multiple certifications for performance testing
        certifications = []
        for i in range(100):
            cert = Certification(
                name=f"Performance Test Cert {i}",
                category="Security",
                domain="Security Management",
                level="Intermediate",
                difficulty=5,
                cost=499.0,
                exam_code=f"PERF-{i:03d}",
                organization_id=org.id,
                description=f"Performance test certification number {i}"
            )
            certifications.append(cert)
            db_session.add(cert)
        
        db_session.commit()
        
        # Measure response time
        start_time = time.time()
        response = client.get(
            f"{API_PREFIX}/certifications",
            headers=auth_headers
        )
        end_time = time.time()
        
        response_time = end_time - start_time
        
        if response.status_code == 200:
            # Response should be under 2 seconds for 100 items
            assert response_time < 2.0, f"Response time {response_time:.2f}s exceeds 2.0s threshold"
            
            # Verify data integrity
            data = response.json()
            assert len(data) >= 100  # Should include our test certifications
        else:
            # If endpoint doesn't exist, just verify it responds quickly
            assert response_time < 1.0, f"Even 404 responses should be fast: {response_time:.2f}s"
    
    def test_concurrent_user_performance(self, client, db_session):
        """Test performance under concurrent user load."""
        # Create test users
        users = []
        for i in range(20):
            user = User(
                username=f"perfuser{i}",
                email=f"perfuser{i}@example.com",
                hashed_password="hashed_password",
                role=UserRole.USER,
                status=UserStatus.ACTIVE,
                first_name=f"Perf{i}",
                last_name="User"
            )
            users.append(user)
            db_session.add(user)
        
        db_session.commit()
        
        def make_concurrent_request(user_index):
            """Make a request as a specific user."""
            user_data = {
                "username": f"perfuser{user_index}",
                "role": UserRole.USER
            }
            token = create_test_token(user_data)
            headers = {"Authorization": f"Bearer {token}"}
            
            start_time = time.time()
            response = client.get(
                f"{API_PREFIX}/certifications",
                headers=headers
            )
            end_time = time.time()
            
            return {
                "status_code": response.status_code,
                "response_time": end_time - start_time,
                "user_index": user_index
            }
        
        # Execute concurrent requests
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [
                executor.submit(make_concurrent_request, i)
                for i in range(20)
            ]
            
            results = [future.result() for future in as_completed(futures)]
        
        total_time = time.time() - start_time
        
        # Analyze results
        response_times = [r["response_time"] for r in results]
        status_codes = [r["status_code"] for r in results]
        
        # Performance assertions
        avg_response_time = statistics.mean(response_times)
        max_response_time = max(response_times)
        
        # All requests should complete within reasonable time
        assert total_time < 10.0, f"Total time {total_time:.2f}s exceeds 10s threshold"
        assert avg_response_time < 2.0, f"Average response time {avg_response_time:.2f}s exceeds 2s threshold"
        assert max_response_time < 5.0, f"Max response time {max_response_time:.2f}s exceeds 5s threshold"
        
        # All requests should return consistent status codes
        unique_codes = set(status_codes)
        assert len(unique_codes) <= 2, f"Too many different status codes: {unique_codes}"
    
    def test_large_dataset_query_performance(self, client, auth_headers, db_session):
        """Test performance with large datasets."""
        # Create organization
        org = Organization(
            name="Large Dataset Org",
            country="US",
            website="https://largedata.com",
            description="Large dataset testing"
        )
        db_session.add(org)
        db_session.flush()
        
        # Create large number of certifications
        batch_size = 100
        total_certs = 500
        
        for batch_start in range(0, total_certs, batch_size):
            batch_certs = []
            for i in range(batch_start, min(batch_start + batch_size, total_certs)):
                cert = Certification(
                    name=f"Large Dataset Cert {i}",
                    category="Security",
                    domain="Security Management",
                    level="Intermediate",
                    difficulty=5,
                    cost=499.0,
                    exam_code=f"LARGE-{i:04d}",
                    organization_id=org.id
                )
                batch_certs.append(cert)
                db_session.add(cert)
            
            db_session.commit()
        
        # Test pagination performance
        page_sizes = [10, 50, 100]
        for page_size in page_sizes:
            start_time = time.time()
            response = client.get(
                f"{API_PREFIX}/certifications?limit={page_size}&offset=0",
                headers=auth_headers
            )
            end_time = time.time()
            
            response_time = end_time - start_time
            
            if response.status_code == 200:
                # Larger page sizes should still be reasonably fast
                max_time = 3.0 if page_size <= 50 else 5.0
                assert response_time < max_time, f"Page size {page_size} took {response_time:.2f}s (max: {max_time}s)"
    
    def test_search_performance(self, client, auth_headers, db_session):
        """Test search functionality performance."""
        # Create test data with searchable content
        org = Organization(
            name="Search Test Org",
            country="US",
            website="https://search.com",
            description="Search testing"
        )
        db_session.add(org)
        db_session.flush()
        
        # Create certifications with various searchable terms
        search_terms = ["Security", "Network", "Cloud", "Ethical", "Management"]
        for i, term in enumerate(search_terms * 20):  # 100 total
            cert = Certification(
                name=f"{term} Certification {i}",
                category="Security",
                domain=f"{term} Security",
                level="Intermediate",
                difficulty=5,
                cost=499.0,
                exam_code=f"SEARCH-{i:03d}",
                organization_id=org.id,
                description=f"This is a {term.lower()} related certification for testing search performance"
            )
            db_session.add(cert)
        
        db_session.commit()
        
        # Test search performance
        search_queries = ["Security", "Network", "Cloud"]
        for query in search_queries:
            start_time = time.time()
            response = client.get(
                f"{API_PREFIX}/certifications?search={query}",
                headers=auth_headers
            )
            end_time = time.time()
            
            response_time = end_time - start_time
            
            if response.status_code == 200:
                # Search should be fast even with filtering
                assert response_time < 2.0, f"Search for '{query}' took {response_time:.2f}s (max: 2.0s)"


@pytest.mark.performance
class TestDatabasePerformance:
    """Test database performance."""
    
    def test_query_optimization(self, db_session):
        """Test database query optimization."""
        # Create test data
        org = Organization(
            name="Query Test Org",
            country="US",
            website="https://query.com",
            description="Query testing"
        )
        db_session.add(org)
        db_session.flush()
        
        # Create certifications for query testing
        for i in range(200):
            cert = Certification(
                name=f"Query Test Cert {i}",
                category="Security",
                domain="Security Management",
                level="Intermediate",
                difficulty=5,
                cost=499.0,
                exam_code=f"QUERY-{i:03d}",
                organization_id=org.id
            )
            db_session.add(cert)
        
        db_session.commit()
        
        # Test various query patterns
        queries = [
            # Simple select
            "SELECT COUNT(*) FROM certifications",
            
            # Join query
            """
            SELECT c.name, o.name 
            FROM certifications c 
            JOIN organizations o ON c.organization_id = o.id 
            LIMIT 10
            """,
            
            # Filtered query
            """
            SELECT * FROM certifications 
            WHERE domain = 'Security Management' 
            AND difficulty >= 5 
            LIMIT 20
            """,
            
            # Aggregation query
            """
            SELECT domain, COUNT(*), AVG(cost) 
            FROM certifications 
            GROUP BY domain
            """
        ]
        
        for query in queries:
            start_time = time.time()
            result = db_session.execute(text(query))
            result.fetchall()  # Ensure query is fully executed
            end_time = time.time()
            
            query_time = end_time - start_time
            
            # Database queries should be fast
            assert query_time < 1.0, f"Query took {query_time:.2f}s (max: 1.0s): {query[:50]}..."
    
    def test_connection_pool_performance(self, db_session):
        """Test database connection pool performance."""
        def execute_query(query_id):
            """Execute a query in a separate thread."""
            start_time = time.time()
            result = db_session.execute(text("SELECT COUNT(*) FROM certifications"))
            result.fetchone()
            end_time = time.time()
            return end_time - start_time
        
        # Test concurrent database access
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [
                executor.submit(execute_query, i)
                for i in range(20)
            ]
            
            query_times = [future.result() for future in as_completed(futures)]
        
        # All queries should complete quickly
        avg_time = statistics.mean(query_times)
        max_time = max(query_times)
        
        assert avg_time < 0.5, f"Average query time {avg_time:.2f}s exceeds 0.5s"
        assert max_time < 2.0, f"Max query time {max_time:.2f}s exceeds 2.0s"


@pytest.mark.performance
class TestMemoryPerformance:
    """Test memory usage and optimization."""
    
    def test_memory_usage_under_load(self, client, auth_headers):
        """Test memory usage under load."""
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Make multiple requests to test memory usage
        for i in range(50):
            response = client.get(
                f"{API_PREFIX}/certifications",
                headers=auth_headers
            )
            
            # Force garbage collection periodically
            if i % 10 == 0:
                gc.collect()
        
        # Get final memory usage
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB for 50 requests)
        assert memory_increase < 100, f"Memory increased by {memory_increase:.2f}MB (max: 100MB)"
    
    def test_large_response_memory_efficiency(self, client, auth_headers, db_session):
        """Test memory efficiency with large responses."""
        # Create organization
        org = Organization(
            name="Memory Test Org",
            country="US",
            website="https://memory.com",
            description="Memory testing"
        )
        db_session.add(org)
        db_session.flush()
        
        # Create many certifications
        for i in range(300):
            cert = Certification(
                name=f"Memory Test Cert {i}",
                category="Security",
                domain="Security Management",
                level="Intermediate",
                difficulty=5,
                cost=499.0,
                exam_code=f"MEM-{i:03d}",
                organization_id=org.id,
                description="A" * 500  # Large description to increase memory usage
            )
            db_session.add(cert)
        
        db_session.commit()
        
        # Monitor memory during large request
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        response = client.get(
            f"{API_PREFIX}/certifications?limit=300",
            headers=auth_headers
        )
        
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_spike = peak_memory - initial_memory
        
        # Memory spike should be reasonable for large response
        if response.status_code == 200:
            assert memory_spike < 200, f"Memory spike {memory_spike:.2f}MB exceeds 200MB threshold"
        
        # Force cleanup
        gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_retained = final_memory - initial_memory
        
        # Most memory should be released after request
        assert memory_retained < 50, f"Memory retained {memory_retained:.2f}MB exceeds 50MB threshold"


@pytest.mark.performance
class TestScalabilityTests:
    """Test system scalability."""
    
    def test_user_scalability(self, client, db_session):
        """Test system behavior with many users."""
        # Create many test users
        users = []
        batch_size = 50
        total_users = 200
        
        for batch_start in range(0, total_users, batch_size):
            batch_users = []
            for i in range(batch_start, min(batch_start + batch_size, total_users)):
                user = User(
                    username=f"scaleuser{i}",
                    email=f"scaleuser{i}@example.com",
                    hashed_password="hashed_password",
                    role=UserRole.USER,
                    status=UserStatus.ACTIVE,
                    first_name=f"Scale{i}",
                    last_name="User"
                )
                batch_users.append(user)
                db_session.add(user)
            
            db_session.commit()
        
        # Test user listing performance
        start_time = time.time()
        user_count = db_session.query(User).count()
        end_time = time.time()
        
        query_time = end_time - start_time
        
        assert user_count >= total_users
        assert query_time < 1.0, f"User count query took {query_time:.2f}s (max: 1.0s)"
    
    def test_data_volume_scalability(self, db_session):
        """Test system behavior with large data volumes."""
        # Create organization
        org = Organization(
            name="Volume Test Org",
            country="US",
            website="https://volume.com",
            description="Volume testing"
        )
        db_session.add(org)
        db_session.flush()
        
        # Create large volume of certifications
        batch_size = 100
        total_certs = 1000
        
        start_time = time.time()
        
        for batch_start in range(0, total_certs, batch_size):
            batch_certs = []
            for i in range(batch_start, min(batch_start + batch_size, total_certs)):
                cert = Certification(
                    name=f"Volume Test Cert {i}",
                    category="Security",
                    domain="Security Management",
                    level="Intermediate",
                    difficulty=5,
                    cost=499.0,
                    exam_code=f"VOL-{i:04d}",
                    organization_id=org.id
                )
                batch_certs.append(cert)
                db_session.add(cert)
            
            db_session.commit()
        
        creation_time = time.time() - start_time
        
        # Large data creation should complete in reasonable time
        assert creation_time < 30.0, f"Creating {total_certs} records took {creation_time:.2f}s (max: 30s)"
        
        # Test querying large dataset
        start_time = time.time()
        cert_count = db_session.query(Certification).filter(
            Certification.exam_code.like("VOL-%")
        ).count()
        query_time = time.time() - start_time
        
        assert cert_count == total_certs
        assert query_time < 2.0, f"Counting {total_certs} records took {query_time:.2f}s (max: 2s)"
