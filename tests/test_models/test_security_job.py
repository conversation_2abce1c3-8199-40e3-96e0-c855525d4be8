"""Unit tests for SecurityJob model"""
import pytest
from datetime import datetime
from models.job import SecurityJob
from sqlalchemy.exc import IntegrityError

def test_security_job_creation():
    """Test creating a security job with valid data"""
    job = SecurityJob(
        title="Security Analyst",
        domain="Security Operations",
        description="Entry level security analyst position",
        experience_level="Entry Level",
        salary_range_min=50000,
        salary_range_max=75000,
        skills_required="SIEM, Log Analysis, Incident Response"
    )

    assert job.title == "Security Analyst"
    assert job.domain == "Security Operations"
    assert job.experience_level == "Entry Level"
    assert job.salary_range_min == 50000
    assert not job.is_deleted

def test_security_job_required_fields(db_session):
    """Test that title and domain are required"""
    job = SecurityJob()
    db_session.add(job)

    with pytest.raises(IntegrityError):
        db_session.commit()

def test_security_job_timestamps():
    """Test that timestamps are set correctly"""
    job = SecurityJob(
        title="Security Engineer",
        domain="Security Engineering"
    )

    assert isinstance(job.created_at, datetime)
    assert isinstance(job.updated_at, datetime)  # Should be set on creation
    assert job.deleted_at is None

def test_security_job_soft_delete():
    """Test soft delete functionality"""
    job = SecurityJob(
        title="Security Admin",
        domain="Security Operations"
    )

    job.soft_delete()
    assert job.is_deleted
    assert isinstance(job.deleted_at, datetime)

def test_security_job_to_dict():
    """Test dictionary representation"""
    job = SecurityJob(
        title="Security Consultant",
        domain="Security Management",
        description="Senior security consultant role",
        experience_level="Senior",
        salary_range_min=100000,
        salary_range_max=150000,
        skills_required="Risk Assessment, Security Architecture"
    )

    job_dict = job.to_dict()
    assert job_dict["title"] == "Security Consultant"
    assert job_dict["domain"] == "Security Management"
    assert "created_at" in job_dict
    assert "is_deleted" in job_dict

def test_invalid_experience_level(db_session):
    """Test that invalid experience levels are rejected"""
    job = SecurityJob(
        title="Security Engineer",
        domain="Security Engineering",
        experience_level="Invalid Level"  # Invalid experience level
    )
    db_session.add(job)

    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()

def test_invalid_salary_range(db_session):
    """Test that invalid salary ranges are rejected"""
    job = SecurityJob(
        title="Security Engineer",
        domain="Security Engineering",
        salary_range_min=100000,
        salary_range_max=50000  # Invalid: min > max
    )
    db_session.add(job)

    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()

def test_duplicate_job_creation(db_session):
    """Test that duplicate jobs are not allowed"""
    job1 = SecurityJob(
        title="Security Engineer",
        domain="Security Engineering"
    )
    job2 = SecurityJob(
        title="Security Engineer",
        domain="Security Engineering"
    )

    db_session.add(job1)
    db_session.commit()

    db_session.add(job2)
    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()