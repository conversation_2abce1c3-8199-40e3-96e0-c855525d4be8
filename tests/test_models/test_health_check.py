"""Tests for health check models"""
import pytest
from datetime import datetime
from models.health_check import HealthResponse
from pydantic import ValidationError


class TestHealthResponse:
    """Test HealthResponse model"""
    
    def test_create_health_response(self):
        """Test creating a health response"""
        health_response = HealthResponse(
            status="healthy",
            database="connected",
            anthropic_client="connected"
        )
        
        assert health_response.status == "healthy"
        assert health_response.database == "connected"
        assert health_response.anthropic_client == "connected"
        assert isinstance(health_response.timestamp, datetime)
    
    def test_health_response_with_custom_timestamp(self):
        """Test health response with custom timestamp"""
        custom_time = datetime(2023, 1, 1, 12, 0, 0)
        
        health_response = HealthResponse(
            status="healthy",
            database="connected",
            anthropic_client="connected",
            timestamp=custom_time
        )
        
        assert health_response.timestamp == custom_time
    
    def test_health_response_auto_timestamp(self):
        """Test that timestamp is automatically set"""
        before_creation = datetime.utcnow()
        
        health_response = HealthResponse(
            status="healthy",
            database="connected",
            anthropic_client="connected"
        )
        
        after_creation = datetime.utcnow()
        
        # Timestamp should be between before and after creation
        assert before_creation <= health_response.timestamp <= after_creation
    
    def test_health_response_different_statuses(self):
        """Test health response with different status values"""
        statuses = [
            ("healthy", "connected", "connected"),
            ("degraded", "slow", "connected"),
            ("unhealthy", "disconnected", "error"),
            ("unknown", "timeout", "unavailable")
        ]
        
        for status, db_status, client_status in statuses:
            health_response = HealthResponse(
                status=status,
                database=db_status,
                anthropic_client=client_status
            )
            
            assert health_response.status == status
            assert health_response.database == db_status
            assert health_response.anthropic_client == client_status
    
    def test_health_response_validation(self):
        """Test health response validation"""
        # Test missing required fields
        with pytest.raises(ValidationError):
            HealthResponse(
                # Missing required status
                database="connected",
                anthropic_client="connected"
            )
        
        with pytest.raises(ValidationError):
            HealthResponse(
                status="healthy",
                # Missing required database
                anthropic_client="connected"
            )
        
        with pytest.raises(ValidationError):
            HealthResponse(
                status="healthy",
                database="connected"
                # Missing required anthropic_client
            )
    
    def test_health_response_serialization(self):
        """Test health response serialization"""
        health_response = HealthResponse(
            status="healthy",
            database="connected",
            anthropic_client="connected"
        )
        
        # Test dict conversion
        response_dict = health_response.dict()
        
        assert isinstance(response_dict, dict)
        assert response_dict["status"] == "healthy"
        assert response_dict["database"] == "connected"
        assert response_dict["anthropic_client"] == "connected"
        assert "timestamp" in response_dict
        assert isinstance(response_dict["timestamp"], datetime)
    
    def test_health_response_json_serialization(self):
        """Test health response JSON serialization"""
        health_response = HealthResponse(
            status="healthy",
            database="connected",
            anthropic_client="connected"
        )
        
        # Test JSON conversion
        json_str = health_response.json()
        
        assert isinstance(json_str, str)
        assert '"status":"healthy"' in json_str or '"status": "healthy"' in json_str
        assert '"database":"connected"' in json_str or '"database": "connected"' in json_str
        assert '"anthropic_client":"connected"' in json_str or '"anthropic_client": "connected"' in json_str
        assert '"timestamp"' in json_str
    
    def test_health_response_from_dict(self):
        """Test creating health response from dictionary"""
        data = {
            "status": "degraded",
            "database": "slow",
            "anthropic_client": "connected",
            "timestamp": datetime(2023, 1, 1, 12, 0, 0)
        }
        
        health_response = HealthResponse(**data)
        
        assert health_response.status == "degraded"
        assert health_response.database == "slow"
        assert health_response.anthropic_client == "connected"
        assert health_response.timestamp == datetime(2023, 1, 1, 12, 0, 0)
    
    def test_health_response_field_types(self):
        """Test health response field type validation"""
        # Test with valid string types
        health_response = HealthResponse(
            status="healthy",
            database="connected",
            anthropic_client="connected"
        )
        
        assert isinstance(health_response.status, str)
        assert isinstance(health_response.database, str)
        assert isinstance(health_response.anthropic_client, str)
        assert isinstance(health_response.timestamp, datetime)
    
    def test_health_response_empty_strings(self):
        """Test health response with empty strings"""
        health_response = HealthResponse(
            status="",
            database="",
            anthropic_client=""
        )
        
        assert health_response.status == ""
        assert health_response.database == ""
        assert health_response.anthropic_client == ""
    
    def test_health_response_long_strings(self):
        """Test health response with long strings"""
        long_string = "x" * 1000
        
        health_response = HealthResponse(
            status=long_string,
            database=long_string,
            anthropic_client=long_string
        )
        
        assert health_response.status == long_string
        assert health_response.database == long_string
        assert health_response.anthropic_client == long_string
    
    def test_health_response_special_characters(self):
        """Test health response with special characters"""
        special_chars = "!@#$%^&*()_+-=[]{}|;':\",./<>?"
        
        health_response = HealthResponse(
            status=special_chars,
            database=special_chars,
            anthropic_client=special_chars
        )
        
        assert health_response.status == special_chars
        assert health_response.database == special_chars
        assert health_response.anthropic_client == special_chars
    
    def test_health_response_unicode_support(self):
        """Test health response with unicode characters"""
        unicode_text = "健康状态 🟢 système sain"
        
        health_response = HealthResponse(
            status=unicode_text,
            database=unicode_text,
            anthropic_client=unicode_text
        )
        
        assert health_response.status == unicode_text
        assert health_response.database == unicode_text
        assert health_response.anthropic_client == unicode_text
    
    def test_health_response_immutability(self):
        """Test that health response fields can be modified"""
        health_response = HealthResponse(
            status="healthy",
            database="connected",
            anthropic_client="connected"
        )
        
        # Pydantic models are mutable by default
        health_response.status = "unhealthy"
        assert health_response.status == "unhealthy"
    
    def test_health_response_copy(self):
        """Test copying health response"""
        original = HealthResponse(
            status="healthy",
            database="connected",
            anthropic_client="connected"
        )
        
        # Test copy
        copied = original.copy()
        
        assert copied.status == original.status
        assert copied.database == original.database
        assert copied.anthropic_client == original.anthropic_client
        assert copied.timestamp == original.timestamp
        
        # Modify copy
        copied.status = "unhealthy"
        
        # Original should be unchanged
        assert original.status == "healthy"
        assert copied.status == "unhealthy"
    
    def test_health_response_update(self):
        """Test updating health response"""
        health_response = HealthResponse(
            status="healthy",
            database="connected",
            anthropic_client="connected"
        )
        
        # Test copy with updates
        updated = health_response.copy(update={
            "status": "degraded",
            "database": "slow"
        })
        
        assert updated.status == "degraded"
        assert updated.database == "slow"
        assert updated.anthropic_client == "connected"  # Unchanged
        
        # Original should be unchanged
        assert health_response.status == "healthy"
        assert health_response.database == "connected"


class TestHealthResponseIntegration:
    """Integration tests for HealthResponse"""
    
    def test_health_response_api_format(self):
        """Test health response in API response format"""
        health_response = HealthResponse(
            status="healthy",
            database="connected",
            anthropic_client="connected"
        )
        
        # Simulate API response
        api_response = {
            "health": health_response.dict(),
            "message": "System is healthy"
        }
        
        assert api_response["health"]["status"] == "healthy"
        assert api_response["health"]["database"] == "connected"
        assert api_response["health"]["anthropic_client"] == "connected"
        assert "timestamp" in api_response["health"]
    
    def test_health_response_monitoring_scenario(self):
        """Test health response in monitoring scenario"""
        # Simulate different health states
        scenarios = [
            {
                "status": "healthy",
                "database": "connected",
                "anthropic_client": "connected",
                "expected_alert": False
            },
            {
                "status": "degraded",
                "database": "slow",
                "anthropic_client": "connected",
                "expected_alert": True
            },
            {
                "status": "unhealthy",
                "database": "disconnected",
                "anthropic_client": "error",
                "expected_alert": True
            }
        ]
        
        for scenario in scenarios:
            health_response = HealthResponse(
                status=scenario["status"],
                database=scenario["database"],
                anthropic_client=scenario["anthropic_client"]
            )
            
            # Simulate alert logic
            should_alert = health_response.status != "healthy"
            
            assert should_alert == scenario["expected_alert"]
    
    def test_health_response_batch_creation(self):
        """Test creating multiple health responses"""
        responses = []
        
        for i in range(10):
            response = HealthResponse(
                status=f"status_{i}",
                database=f"db_{i}",
                anthropic_client=f"client_{i}"
            )
            responses.append(response)
        
        assert len(responses) == 10
        
        for i, response in enumerate(responses):
            assert response.status == f"status_{i}"
            assert response.database == f"db_{i}"
            assert response.anthropic_client == f"client_{i}"
