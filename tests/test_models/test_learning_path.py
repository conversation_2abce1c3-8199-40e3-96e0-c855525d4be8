import pytest
from datetime import datetime
from models.learning_path import UserExperience, LearningPath, LearningPathItem, StudyResource

def test_user_experience_relationships(db_session):
    """Test UserExperience relationships and cascading behavior"""
    # Create test data with required fields
    current_time = datetime.utcnow()
    user_exp = UserExperience(
        user_id="test_user",
        years_experience=5,
        user_role="Security Analyst",
        desired_role="Security Engineer",
        expertise_areas=["Network Security", "Cloud Security"],
        preferred_learning_style="Mixed",
        study_time_available=10,
        created_at=current_time,
        updated_at=current_time
    )
    db_session.add(user_exp)
    db_session.commit()

    # Create learning path linked to user experience
    learning_path = LearningPath(
        user_experience_id=user_exp.id,
        name="Test Path",
        description="Test description",
        estimated_duration_weeks=12,
        difficulty_level="Intermediate",
        status="active",
        created_at=current_time,
        updated_at=current_time
    )
    db_session.add(learning_path)
    db_session.commit()

    # Test relationship from user to path
    assert len(user_exp.learning_paths) == 1
    assert user_exp.learning_paths[0].name == "Test Path"

    # Test cascading delete
    db_session.delete(user_exp)
    db_session.commit()

    # Verify learning path is also deleted
    assert db_session.query(LearningPath).count() == 0
