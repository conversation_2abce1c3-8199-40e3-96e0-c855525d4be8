"""
Tests for CertificationExplorer model
"""
import pytest
from sqlalchemy.exc import IntegrityError
from models.certification_explorer import CertificationExplorer


@pytest.mark.unit
def test_certification_explorer_creation(db_session):
    """Test creating a CertificationExplorer instance"""
    cert = CertificationExplorer(
        name="CompTIA Security+",
        provider="CompTIA",
        category="Entry-Level Security",
        difficulty="Intermediate",
        cost=370,
        duration="3-6 months",
        description="Entry-level cybersecurity certification",
        prerequisites="Basic IT knowledge",
        career_paths="Security Analyst, SOC Analyst",
        exam_details="90 questions, 90 minutes",
        renewal_requirements="3 years, 50 CEUs"
    )
    
    db_session.add(cert)
    db_session.commit()
    db_session.refresh(cert)
    
    assert cert.id is not None
    assert cert.name == "CompTIA Security+"
    assert cert.provider == "CompTIA"
    assert cert.cost == 370
    assert cert.difficulty == "Intermediate"


@pytest.mark.unit
def test_certification_explorer_required_fields(db_session):
    """Test that required fields are enforced"""
    # Test missing name
    cert = CertificationExplorer(
        provider="CompTIA",
        category="Security",
        difficulty="Intermediate"
    )
    
    db_session.add(cert)
    
    with pytest.raises(IntegrityError):
        db_session.commit()


@pytest.mark.unit
def test_certification_explorer_unique_name(db_session):
    """Test that certification names must be unique"""
    cert1 = CertificationExplorer(
        name="CompTIA Security+",
        provider="CompTIA",
        category="Security",
        difficulty="Intermediate"
    )
    
    cert2 = CertificationExplorer(
        name="CompTIA Security+",  # Same name
        provider="CompTIA",
        category="Security",
        difficulty="Intermediate"
    )
    
    db_session.add(cert1)
    db_session.commit()
    
    db_session.add(cert2)
    
    with pytest.raises(IntegrityError):
        db_session.commit()


@pytest.mark.unit
def test_certification_explorer_cost_validation(db_session):
    """Test cost field validation"""
    # Test negative cost
    cert = CertificationExplorer(
        name="Test Cert",
        provider="Test Provider",
        category="Test",
        difficulty="Easy",
        cost=-100  # Negative cost should be invalid
    )
    
    db_session.add(cert)
    
    # This should either raise an error or be handled by application logic
    try:
        db_session.commit()
        # If commit succeeds, verify the cost is handled appropriately
        assert cert.cost >= 0 or cert.cost is None
    except IntegrityError:
        # Expected if database has constraints
        pass


@pytest.mark.unit
def test_certification_explorer_string_representation(db_session):
    """Test string representation of CertificationExplorer"""
    cert = CertificationExplorer(
        name="CompTIA Security+",
        provider="CompTIA",
        category="Security",
        difficulty="Intermediate"
    )
    
    db_session.add(cert)
    db_session.commit()
    
    # Test __str__ or __repr__ method if implemented
    str_repr = str(cert)
    assert "CompTIA Security+" in str_repr


@pytest.mark.unit
def test_certification_explorer_query_by_category(db_session, multiple_certifications):
    """Test querying certifications by category"""
    # Query for security certifications
    security_certs = db_session.query(CertificationExplorer).filter(
        CertificationExplorer.category.like("%Security%")
    ).all()
    
    assert len(security_certs) >= 2  # Should find Security+ and CISSP
    
    cert_names = [cert.name for cert in security_certs]
    assert "CompTIA Security+" in cert_names
    assert "CISSP" in cert_names


@pytest.mark.unit
def test_certification_explorer_query_by_difficulty(db_session, multiple_certifications):
    """Test querying certifications by difficulty"""
    # Query for intermediate difficulty
    intermediate_certs = db_session.query(CertificationExplorer).filter(
        CertificationExplorer.difficulty == "Intermediate"
    ).all()
    
    assert len(intermediate_certs) >= 1
    assert "CompTIA Security+" in [cert.name for cert in intermediate_certs]


@pytest.mark.unit
def test_certification_explorer_query_by_cost_range(db_session, multiple_certifications):
    """Test querying certifications by cost range"""
    # Query for certifications under $500
    affordable_certs = db_session.query(CertificationExplorer).filter(
        CertificationExplorer.cost < 500
    ).all()
    
    assert len(affordable_certs) >= 1
    
    # Query for expensive certifications (over $1000)
    expensive_certs = db_session.query(CertificationExplorer).filter(
        CertificationExplorer.cost > 1000
    ).all()
    
    assert len(expensive_certs) >= 1
    assert "CEH" in [cert.name for cert in expensive_certs]


@pytest.mark.unit
def test_certification_explorer_query_by_provider(db_session, multiple_certifications):
    """Test querying certifications by provider"""
    # Query for CompTIA certifications
    comptia_certs = db_session.query(CertificationExplorer).filter(
        CertificationExplorer.provider == "CompTIA"
    ).all()
    
    assert len(comptia_certs) >= 1
    assert "CompTIA Security+" in [cert.name for cert in comptia_certs]


@pytest.mark.unit
def test_certification_explorer_update(db_session, sample_certification):
    """Test updating a certification"""
    original_cost = sample_certification.cost
    
    # Update the cost
    sample_certification.cost = 400
    db_session.commit()
    db_session.refresh(sample_certification)
    
    assert sample_certification.cost == 400
    assert sample_certification.cost != original_cost


@pytest.mark.unit
def test_certification_explorer_delete(db_session, sample_certification):
    """Test deleting a certification"""
    cert_id = sample_certification.id
    
    db_session.delete(sample_certification)
    db_session.commit()
    
    # Verify the certification is deleted
    deleted_cert = db_session.query(CertificationExplorer).filter(
        CertificationExplorer.id == cert_id
    ).first()
    
    assert deleted_cert is None


@pytest.mark.integration
def test_certification_explorer_bulk_operations(db_session):
    """Test bulk operations on certifications"""
    # Create multiple certifications
    certifications = [
        CertificationExplorer(
            name=f"Test Cert {i}",
            provider="Test Provider",
            category="Test Category",
            difficulty="Easy",
            cost=100 * i
        )
        for i in range(1, 6)
    ]
    
    # Bulk insert
    db_session.add_all(certifications)
    db_session.commit()
    
    # Verify all were inserted
    count = db_session.query(CertificationExplorer).filter(
        CertificationExplorer.provider == "Test Provider"
    ).count()
    
    assert count == 5
    
    # Bulk update
    db_session.query(CertificationExplorer).filter(
        CertificationExplorer.provider == "Test Provider"
    ).update({"difficulty": "Medium"})
    db_session.commit()
    
    # Verify update
    updated_certs = db_session.query(CertificationExplorer).filter(
        CertificationExplorer.provider == "Test Provider"
    ).all()
    
    for cert in updated_certs:
        assert cert.difficulty == "Medium"
    
    # Bulk delete
    db_session.query(CertificationExplorer).filter(
        CertificationExplorer.provider == "Test Provider"
    ).delete()
    db_session.commit()
    
    # Verify deletion
    remaining_count = db_session.query(CertificationExplorer).filter(
        CertificationExplorer.provider == "Test Provider"
    ).count()
    
    assert remaining_count == 0


@pytest.mark.unit
def test_certification_explorer_search_functionality(db_session, multiple_certifications):
    """Test search functionality across multiple fields"""
    # Search for "Security" in name or category
    search_term = "Security"
    results = db_session.query(CertificationExplorer).filter(
        (CertificationExplorer.name.like(f"%{search_term}%")) |
        (CertificationExplorer.category.like(f"%{search_term}%"))
    ).all()
    
    assert len(results) >= 2
    
    # Verify results contain the search term
    for cert in results:
        assert (search_term.lower() in cert.name.lower() or 
                search_term.lower() in cert.category.lower())


@pytest.mark.unit
def test_certification_explorer_ordering(db_session, multiple_certifications):
    """Test ordering certifications by different fields"""
    # Order by cost (ascending)
    certs_by_cost = db_session.query(CertificationExplorer).order_by(
        CertificationExplorer.cost.asc()
    ).all()
    
    # Verify ordering
    for i in range(len(certs_by_cost) - 1):
        assert certs_by_cost[i].cost <= certs_by_cost[i + 1].cost
    
    # Order by name (alphabetical)
    certs_by_name = db_session.query(CertificationExplorer).order_by(
        CertificationExplorer.name.asc()
    ).all()
    
    # Verify alphabetical ordering
    for i in range(len(certs_by_name) - 1):
        assert certs_by_name[i].name <= certs_by_name[i + 1].name
