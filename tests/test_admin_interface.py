"""
Tests for admin interface feature
"""
import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from api.app import app
from datetime import datetime
from models.admin_interface import AdminSetting, SystemStatusBase, AuditLog

client = TestClient(app)

# Mock user for authentication
mock_admin_user = {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "role": "admin",
    "is_active": True
}

mock_regular_user = {
    "id": 2,
    "username": "user",
    "email": "<EMAIL>",
    "role": "user",
    "is_active": True
}

# Settings tests
@patch('api.endpoints.admin_interface.get_admin_user')
@patch('api.endpoints.admin_interface.get_db')
def test_get_all_settings(mock_get_db, mock_get_admin_user):
    """Test getting all admin settings"""
    # Setup mock
    mock_get_admin_user.return_value = mock_admin_user
    mock_db = MagicMock()
    mock_get_db.return_value = mock_db
    
    # Create mock settings
    mock_settings = [
        AdminSetting(
            id=1,
            key="site_name",
            value="CertRats",
            description="Site name",
            category="general",
            created_at=datetime.now(),
            updated_at=None
        ),
        AdminSetting(
            id=2,
            key="maintenance_mode",
            value=False,
            description="Maintenance mode",
            category="system",
            created_at=datetime.now(),
            updated_at=None
        )
    ]
    
    mock_query = MagicMock()
    mock_db.query.return_value = mock_query
    mock_query.filter.return_value = mock_query
    mock_query.offset.return_value = mock_query
    mock_query.limit.return_value = mock_query
    mock_query.all.return_value = mock_settings
    
    # Make request
    response = client.get("/admin/settings")
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 2
    assert data[0]["key"] == "site_name"
    assert data[1]["key"] == "maintenance_mode"

@patch('api.endpoints.admin_interface.get_admin_user')
@patch('api.endpoints.admin_interface.get_db')
def test_get_setting(mock_get_db, mock_get_admin_user):
    """Test getting a specific admin setting"""
    # Setup mock
    mock_get_admin_user.return_value = mock_admin_user
    mock_db = MagicMock()
    mock_get_db.return_value = mock_db
    
    # Create mock setting
    mock_setting = AdminSetting(
        id=1,
        key="site_name",
        value="CertRats",
        description="Site name",
        category="general",
        created_at=datetime.now(),
        updated_at=None
    )
    
    mock_query = MagicMock()
    mock_db.query.return_value = mock_query
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = mock_setting
    
    # Make request
    response = client.get("/admin/settings/site_name")
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert data["key"] == "site_name"
    assert data["value"] == "CertRats"
    assert data["category"] == "general"

@patch('api.endpoints.admin_interface.get_admin_user')
@patch('api.endpoints.admin_interface.get_db')
def test_create_setting(mock_get_db, mock_get_admin_user):
    """Test creating an admin setting"""
    # Setup mock
    mock_get_admin_user.return_value = mock_admin_user
    mock_db = MagicMock()
    mock_get_db.return_value = mock_db
    
    # Mock query for existing setting check
    mock_query = MagicMock()
    mock_db.query.return_value = mock_query
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = None  # No existing setting
    
    # Setting data
    setting_data = {
        "key": "new_setting",
        "value": "new value",
        "description": "New setting description",
        "category": "custom"
    }
    
    # Make request
    response = client.post("/admin/settings", json=setting_data)
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert data["key"] == setting_data["key"]
    assert data["value"] == setting_data["value"]
    assert data["category"] == setting_data["category"]
    
    # Verify db operations
    mock_db.add.assert_called_once()
    mock_db.commit.assert_called_once()
    mock_db.refresh.assert_called_once()

@patch('api.endpoints.admin_interface.get_admin_user')
@patch('api.endpoints.admin_interface.get_db')
def test_update_setting(mock_get_db, mock_get_admin_user):
    """Test updating an admin setting"""
    # Setup mock
    mock_get_admin_user.return_value = mock_admin_user
    mock_db = MagicMock()
    mock_get_db.return_value = mock_db
    
    # Create mock setting
    mock_setting = AdminSetting(
        id=1,
        key="site_name",
        value="CertRats",
        description="Site name",
        category="general",
        created_at=datetime.now(),
        updated_at=None
    )
    
    mock_query = MagicMock()
    mock_db.query.return_value = mock_query
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = mock_setting
    
    # Update data
    update_data = {
        "value": "Updated CertRats",
        "description": "Updated site name"
    }
    
    # Make request
    response = client.put("/admin/settings/site_name", json=update_data)
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert data["key"] == "site_name"
    assert data["value"] == update_data["value"]
    assert data["description"] == update_data["description"]
    
    # Verify db operations
    mock_db.commit.assert_called_once()
    mock_db.refresh.assert_called_once()

@patch('api.endpoints.admin_interface.get_admin_user')
@patch('api.endpoints.admin_interface.get_db')
def test_delete_setting(mock_get_db, mock_get_admin_user):
    """Test deleting an admin setting"""
    # Setup mock
    mock_get_admin_user.return_value = mock_admin_user
    mock_db = MagicMock()
    mock_get_db.return_value = mock_db
    
    # Create mock setting
    mock_setting = AdminSetting(
        id=1,
        key="site_name",
        value="CertRats",
        description="Site name",
        category="general",
        created_at=datetime.now(),
        updated_at=None
    )
    
    mock_query = MagicMock()
    mock_db.query.return_value = mock_query
    mock_query.filter.return_value = mock_query
    mock_query.first.return_value = mock_setting
    
    # Make request
    response = client.delete("/admin/settings/site_name")
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Setting deleted"
    
    # Verify db operations
    mock_db.delete.assert_called_once_with(mock_setting)
    mock_db.commit.assert_called_once()

# System status tests
@patch('api.endpoints.admin_interface.get_admin_user')
@patch('api.endpoints.admin_interface.psutil')
def test_get_system_status(mock_psutil, mock_get_admin_user):
    """Test getting system status"""
    # Setup mock
    mock_get_admin_user.return_value = mock_admin_user
    
    # Mock psutil functions
    mock_psutil.cpu_percent.return_value = 25.5
    mock_psutil.virtual_memory.return_value = MagicMock(
        total=16000000000,
        available=8000000000,
        percent=50.0
    )
    mock_psutil.disk_usage.return_value = MagicMock(
        total=500000000000,
        free=250000000000,
        percent=50.0
    )
    mock_psutil.boot_time.return_value = datetime.now().timestamp() - 3600  # 1 hour ago
    mock_psutil.cpu_count.return_value = 8
    
    # Make request
    response = client.get("/admin/system/status")
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "operational"
    assert len(data["components"]) == 3
    assert "cpu" in data["resources"]
    assert "memory" in data["resources"]
    assert "disk" in data["resources"]
    assert data["uptime"] > 0

# Audit log tests
@patch('api.endpoints.admin_interface.get_admin_user')
@patch('api.endpoints.admin_interface.get_db')
def test_get_audit_logs(mock_get_db, mock_get_admin_user):
    """Test getting audit logs"""
    # Setup mock
    mock_get_admin_user.return_value = mock_admin_user
    mock_db = MagicMock()
    mock_get_db.return_value = mock_db
    
    # Create mock audit logs
    mock_logs = [
        AuditLog(
            id=1,
            user_id=1,
            action="create",
            resource_type="user",
            resource_id="2",
            details={"username": "newuser"},
            ip_address="***********",
            timestamp=datetime.now()
        ),
        AuditLog(
            id=2,
            user_id=1,
            action="update",
            resource_type="setting",
            resource_id="site_name",
            details={"old_value": "CertRats", "new_value": "Updated CertRats"},
            ip_address="***********",
            timestamp=datetime.now()
        )
    ]
    
    mock_query = MagicMock()
    mock_db.query.return_value = mock_query
    mock_query.filter.return_value = mock_query
    mock_query.order_by.return_value = mock_query
    mock_query.offset.return_value = mock_query
    mock_query.limit.return_value = mock_query
    mock_query.all.return_value = mock_logs
    
    # Make request
    response = client.get("/admin/audit-logs")
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 2
    assert data[0]["action"] == "create"
    assert data[1]["action"] == "update"

@patch('api.endpoints.admin_interface.get_current_active_user')
@patch('api.endpoints.admin_interface.get_db')
def test_create_audit_log(mock_get_db, mock_get_current_user):
    """Test creating an audit log"""
    # Setup mock
    mock_get_current_user.return_value = mock_regular_user
    mock_db = MagicMock()
    mock_get_db.return_value = mock_db
    
    # Audit log data
    log_data = {
        "user_id": 2,
        "action": "view",
        "resource_type": "certification",
        "resource_id": "123",
        "details": {"certification_name": "AWS Certified Solutions Architect"},
        "ip_address": "***********"
    }
    
    # Make request
    response = client.post("/admin/audit-logs", json=log_data)
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert data["action"] == log_data["action"]
    assert data["resource_type"] == log_data["resource_type"]
    assert data["resource_id"] == log_data["resource_id"]
    
    # Verify db operations
    mock_db.add.assert_called_once()
    mock_db.commit.assert_called_once()
    mock_db.refresh.assert_called_once()

# Access control tests
@patch('api.endpoints.admin_interface.get_current_active_user')
def test_regular_user_cannot_access_admin_settings(mock_get_current_user):
    """Test that regular users cannot access admin settings"""
    # Setup mock to return a regular user
    mock_get_current_user.return_value = mock_regular_user
    
    # Make request
    response = client.get("/admin/settings")
    
    # Assertions
    assert response.status_code == 403  # Forbidden
