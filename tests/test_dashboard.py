"""
Tests for dashboard feature
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from api.app import app
from unittest.mock import patch, MagicMock
from models.dashboard import DashboardD<PERSON>, DashboardStats

client = TestClient(app)

def test_get_dashboard_data():
    """Test getting dashboard data"""
    response = client.get("/dashboard/")
    assert response.status_code == 200
    data = response.json()
    
    # Check that the response has the expected structure
    assert "stats" in data
    assert "recent_certifications" in data
    assert "popular_certifications" in data
    assert "organizations" in data
    assert "domains" in data
    
    # Check stats structure
    stats = data["stats"]
    assert "total_certifications" in stats
    assert "total_organizations" in stats
    assert "total_domains" in stats
    assert "certifications_by_level" in stats
    assert isinstance(stats["certifications_by_level"], dict)

def test_get_dashboard_data_with_filters():
    """Test getting dashboard data with filters"""
    # Test with organization filter
    response = client.get("/dashboard/?organization_id=1")
    assert response.status_code == 200
    
    # Test with domain filter
    response = client.get("/dashboard/?domain=Network%20Security")
    assert response.status_code == 200
    
    # Test with level filter
    response = client.get("/dashboard/?level=Intermediate")
    assert response.status_code == 200
    
    # Test with cost filters
    response = client.get("/dashboard/?cost_min=100&cost_max=500")
    assert response.status_code == 200
    
    # Test with multiple filters
    response = client.get("/dashboard/?organization_id=1&level=Advanced&cost_min=200")
    assert response.status_code == 200

def test_get_dashboard_stats():
    """Test getting dashboard statistics only"""
    response = client.get("/dashboard/stats")
    assert response.status_code == 200
    stats = response.json()
    
    # Check stats structure
    assert "total_certifications" in stats
    assert "total_organizations" in stats
    assert "total_domains" in stats
    assert "certifications_by_level" in stats
    assert isinstance(stats["certifications_by_level"], dict)
    
    # Check data types
    assert isinstance(stats["total_certifications"], int)
    assert isinstance(stats["total_organizations"], int)
    assert isinstance(stats["total_domains"], int)
    
    # If average_cost is present, it should be a number
    if "average_cost" in stats and stats["average_cost"] is not None:
        assert isinstance(stats["average_cost"], (int, float))

@patch('api.endpoints.dashboard.get_db')
def test_dashboard_data_error_handling(mock_get_db):
    """Test error handling in dashboard data endpoint"""
    # Mock the database session to raise an exception
    mock_session = MagicMock()
    mock_session.query.side_effect = Exception("Database error")
    mock_get_db.return_value = mock_session
    
    response = client.get("/dashboard/")
    assert response.status_code == 500
    assert "Error retrieving dashboard data" in response.json()["detail"]

@patch('api.endpoints.dashboard.get_db')
def test_dashboard_stats_error_handling(mock_get_db):
    """Test error handling in dashboard stats endpoint"""
    # Mock the database session to raise an exception
    mock_session = MagicMock()
    mock_session.query.side_effect = Exception("Database error")
    mock_get_db.return_value = mock_session
    
    response = client.get("/dashboard/stats")
    assert response.status_code == 500
    assert "Error retrieving dashboard statistics" in response.json()["detail"]
