"""
Comprehensive Error Handling and Edge Case Tests.

This module tests error scenarios and edge cases including:
- Database connection failures
- Invalid input data
- Resource limitations
- Concurrent access scenarios
- External service failures
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.exc import Integrity<PERSON><PERSON>r, OperationalError, DatabaseError
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from fastapi.testclient import TestClient
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

from api.app import app
from models.certification import Certification, Organization
from models.user import User, User<PERSON><PERSON>, UserStatus
from shared.api_endpoints import API_PREFIX


class TestDatabaseErrorHandling:
    """Test database error scenarios."""
    
    def test_database_connection_failure(self, client, auth_headers):
        """Test handling of database connection failures."""
        with patch('database.get_db') as mock_get_db:
            # Simulate database connection failure
            mock_get_db.side_effect = OperationalError(
                "connection failed", None, None
            )
            
            response = client.get(
                f"{API_PREFIX}/certifications",
                headers=auth_headers
            )
            
            # Should handle the error gracefully
            assert response.status_code in [500, 503, 404]  # Server error or not found
    
    def test_database_integrity_constraint_violation(self, client, auth_headers, db_session):
        """Test handling of database integrity constraint violations."""
        # Create organization first
        org = Organization(
            name="Test Org",
            country="US",
            website="https://test.com",
            description="Test"
        )
        db_session.add(org)
        db_session.commit()
        
        # Create first certification
        cert_data = {
            "name": "Unique Certification",
            "category": "Security",
            "domain": "Security Management",
            "level": "Beginner",
            "difficulty": 3,
            "cost": 299.0,
            "exam_code": "UNIQUE-001",
            "organization_id": org.id
        }
        
        response1 = client.post(
            f"{API_PREFIX}/certifications",
            json=cert_data,
            headers=auth_headers
        )
        
        # Try to create duplicate (if endpoint exists and enforces uniqueness)
        response2 = client.post(
            f"{API_PREFIX}/certifications",
            json=cert_data,
            headers=auth_headers
        )
        
        # Should handle duplicate appropriately
        if response1.status_code == 201:
            assert response2.status_code in [400, 409, 422]  # Bad request or conflict
        else:
            # If endpoint doesn't exist, both should return 404
            assert response1.status_code == 404
            assert response2.status_code == 404
    
    def test_database_transaction_rollback(self, db_session):
        """Test database transaction rollback scenarios."""
        try:
            # Start a transaction that will fail
            org = Organization(
                name="Test Org",
                country="US",
                website="https://test.com",
                description="Test"
            )
            db_session.add(org)
            db_session.flush()  # Get ID but don't commit
            
            # Create certification with invalid data that should cause rollback
            cert = Certification(
                name="Test Cert",
                category="Security",
                domain="Security Management",
                level="Beginner",
                difficulty=3,
                cost=299.0,
                exam_code="TEST-001",
                organization_id=org.id
            )
            db_session.add(cert)
            
            # Force an error by trying to add duplicate with same exam_code
            cert_duplicate = Certification(
                name="Duplicate Cert",
                category="Security",
                domain="Security Management",
                level="Beginner",
                difficulty=3,
                cost=299.0,
                exam_code="TEST-001",  # Same exam code
                organization_id=org.id
            )
            db_session.add(cert_duplicate)
            
            # This should fail due to unique constraint
            with pytest.raises(IntegrityError):
                db_session.commit()
                
        except IntegrityError:
            # Rollback should work properly
            db_session.rollback()
            
            # Verify nothing was committed
            orgs = db_session.query(Organization).filter(
                Organization.name == "Test Org"
            ).all()
            assert len(orgs) == 0


class TestInputValidationErrorHandling:
    """Test input validation error scenarios."""
    
    def test_malformed_json_requests(self, client, auth_headers):
        """Test handling of malformed JSON requests."""
        # Test invalid JSON
        response = client.post(
            f"{API_PREFIX}/certifications",
            data="invalid json data",
            headers={**auth_headers, "Content-Type": "application/json"}
        )
        
        # Should return validation error or not found
        assert response.status_code in [400, 422, 404]
    
    def test_missing_required_fields(self, client, auth_headers):
        """Test handling of missing required fields."""
        incomplete_data = {
            "name": "Incomplete Certification"
            # Missing required fields like category, domain, etc.
        }
        
        response = client.post(
            f"{API_PREFIX}/certifications",
            json=incomplete_data,
            headers=auth_headers
        )
        
        # Should return validation error or not found
        assert response.status_code in [422, 404]
    
    def test_invalid_data_types(self, client, auth_headers):
        """Test handling of invalid data types."""
        invalid_data = {
            "name": "Test Certification",
            "category": "Security",
            "domain": "Security Management",
            "level": "Beginner",
            "difficulty": "invalid_number",  # Should be integer
            "cost": "invalid_float",         # Should be float
            "exam_code": 12345               # Should be string
        }
        
        response = client.post(
            f"{API_PREFIX}/certifications",
            json=invalid_data,
            headers=auth_headers
        )
        
        # Should return validation error or not found
        assert response.status_code in [422, 404]
    
    def test_out_of_range_values(self, client, auth_headers):
        """Test handling of out-of-range values."""
        out_of_range_data = {
            "name": "Test Certification",
            "category": "Security",
            "domain": "Security Management",
            "level": "Beginner",
            "difficulty": 15,    # Should be 1-10
            "cost": -100.0,      # Should be positive
            "exam_code": "TEST-001"
        }
        
        response = client.post(
            f"{API_PREFIX}/certifications",
            json=out_of_range_data,
            headers=auth_headers
        )
        
        # Should return validation error or not found
        assert response.status_code in [422, 404]
    
    def test_extremely_large_input_data(self, client, auth_headers):
        """Test handling of extremely large input data."""
        large_data = {
            "name": "A" * 10000,  # Very long name
            "category": "Security",
            "domain": "Security Management",
            "level": "Beginner",
            "difficulty": 3,
            "cost": 299.0,
            "description": "B" * 50000,  # Very long description
            "exam_code": "TEST-001"
        }
        
        response = client.post(
            f"{API_PREFIX}/certifications",
            json=large_data,
            headers=auth_headers
        )
        
        # Should handle large data appropriately
        assert response.status_code in [400, 413, 422, 404]  # Bad request, payload too large, validation error, or not found


class TestConcurrencyErrorHandling:
    """Test concurrent access error scenarios."""
    
    def test_concurrent_certification_creation(self, client, auth_headers, db_session):
        """Test concurrent creation of certifications."""
        # Create organization first
        org = Organization(
            name="Test Org",
            country="US",
            website="https://test.com",
            description="Test"
        )
        db_session.add(org)
        db_session.commit()
        
        def create_certification(index):
            """Helper function to create certification."""
            cert_data = {
                "name": f"Concurrent Certification {index}",
                "category": "Security",
                "domain": "Security Management",
                "level": "Beginner",
                "difficulty": 3,
                "cost": 299.0,
                "exam_code": f"CONC-{index:03d}",
                "organization_id": org.id
            }
            
            response = client.post(
                f"{API_PREFIX}/certifications",
                json=cert_data,
                headers=auth_headers
            )
            return response.status_code
        
        # Create multiple certifications concurrently
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [
                executor.submit(create_certification, i)
                for i in range(10)
            ]
            
            results = [future.result() for future in as_completed(futures)]
        
        # All should either succeed or return consistent error codes
        unique_codes = set(results)
        assert len(unique_codes) <= 2  # Should be consistent (e.g., all 201 or all 404)
    
    def test_concurrent_user_access(self, client, db_session):
        """Test concurrent user access scenarios."""
        # Create test users
        users = []
        for i in range(5):
            user = User(
                username=f"concuser{i}",
                email=f"concuser{i}@example.com",
                hashed_password="hashed_password",
                role=UserRole.USER,
                status=UserStatus.ACTIVE,
                first_name=f"Concurrent{i}",
                last_name="User"
            )
            users.append(user)
            db_session.add(user)
        
        db_session.commit()
        
        def make_authenticated_request(user_index):
            """Helper function to make authenticated request."""
            user_data = {
                "username": f"concuser{user_index}",
                "role": UserRole.USER
            }
            
            from tests.conftest import create_test_token
            token = create_test_token(user_data)
            headers = {"Authorization": f"Bearer {token}"}
            
            response = client.get(
                f"{API_PREFIX}/certifications",
                headers=headers
            )
            return response.status_code
        
        # Make concurrent requests
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [
                executor.submit(make_authenticated_request, i)
                for i in range(5)
            ]
            
            results = [future.result() for future in as_completed(futures)]
        
        # All should return consistent results
        unique_codes = set(results)
        assert len(unique_codes) <= 2  # Should be consistent


class TestExternalServiceErrorHandling:
    """Test external service failure scenarios."""
    
    def test_external_api_timeout(self, client, auth_headers):
        """Test handling of external API timeouts."""
        with patch('requests.get') as mock_get:
            # Simulate timeout
            mock_get.side_effect = TimeoutError("Request timed out")
            
            # Test endpoint that might call external services
            response = client.get(
                f"{API_PREFIX}/certifications/providers",
                headers=auth_headers
            )
            
            # Should handle timeout gracefully
            assert response.status_code in [500, 503, 504, 404]  # Server error, service unavailable, gateway timeout, or not found
    
    def test_external_api_rate_limiting(self, client, auth_headers):
        """Test handling of external API rate limiting."""
        with patch('requests.get') as mock_get:
            # Simulate rate limiting
            mock_response = Mock()
            mock_response.status_code = 429
            mock_response.text = "Rate limit exceeded"
            mock_get.return_value = mock_response
            
            response = client.get(
                f"{API_PREFIX}/certifications/providers",
                headers=auth_headers
            )
            
            # Should handle rate limiting gracefully
            assert response.status_code in [429, 500, 503, 404]  # Rate limited, server error, service unavailable, or not found
    
    def test_external_service_unavailable(self, client, auth_headers):
        """Test handling of external service unavailability."""
        with patch('requests.get') as mock_get:
            # Simulate service unavailable
            mock_get.side_effect = ConnectionError("Service unavailable")
            
            response = client.get(
                f"{API_PREFIX}/certifications/providers",
                headers=auth_headers
            )
            
            # Should handle service unavailability gracefully
            assert response.status_code in [500, 503, 404]  # Server error, service unavailable, or not found


class TestResourceLimitationHandling:
    """Test resource limitation scenarios."""
    
    def test_memory_limitation_simulation(self, client, auth_headers):
        """Test handling of memory limitations."""
        # Simulate memory pressure by requesting large datasets
        response = client.get(
            f"{API_PREFIX}/certifications?limit=10000",
            headers=auth_headers
        )
        
        # Should handle large requests appropriately
        assert response.status_code in [200, 400, 413, 404]  # OK, bad request, payload too large, or not found
    
    def test_request_timeout_handling(self, client, auth_headers):
        """Test handling of request timeouts."""
        with patch('time.sleep') as mock_sleep:
            # Simulate slow processing
            mock_sleep.side_effect = lambda x: time.sleep(0.1)  # Actual small delay
            
            response = client.get(
                f"{API_PREFIX}/certifications",
                headers=auth_headers
            )
            
            # Should complete within reasonable time
            assert response.status_code in [200, 404, 408]  # OK, not found, or request timeout
    
    def test_disk_space_limitation_simulation(self, client, auth_headers):
        """Test handling of disk space limitations."""
        with patch('builtins.open') as mock_open:
            # Simulate disk full error
            mock_open.side_effect = OSError("No space left on device")
            
            # Test endpoint that might write to disk
            large_data = {
                "name": "Large Certification",
                "category": "Security",
                "domain": "Security Management",
                "level": "Beginner",
                "difficulty": 3,
                "cost": 299.0,
                "description": "A" * 1000,  # Large description
                "exam_code": "LARGE-001"
            }
            
            response = client.post(
                f"{API_PREFIX}/certifications",
                json=large_data,
                headers=auth_headers
            )
            
            # Should handle disk space issues gracefully
            assert response.status_code in [500, 507, 404]  # Server error, insufficient storage, or not found
