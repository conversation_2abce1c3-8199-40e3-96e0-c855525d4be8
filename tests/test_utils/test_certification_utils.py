"""Tests for certification utility functions"""
import pytest
from unittest.mock import patch, MagicMock
from sqlalchemy.orm import Session
from utils.certification_utils import get_certifications
from models.certification import Certification, Organization


class TestGetCertifications:
    """Test get_certifications function"""
    
    @patch('utils.certification_utils.get_db')
    def test_get_certifications_success(self, mock_get_db):
        """Test successful retrieval of certifications"""
        # Mock database session
        mock_session = MagicMock()
        mock_get_db.return_value = mock_session
        
        # Mock certification objects
        mock_cert1 = MagicMock()
        mock_cert1.to_dict.return_value = {
            'id': 1,
            'name': 'CISSP',
            'category': 'Security Management',
            'domain': 'Security Management',
            'level': 'Advanced',
            'cost': 749,
            'is_deleted': False
        }
        
        mock_cert2 = MagicMock()
        mock_cert2.to_dict.return_value = {
            'id': 2,
            'name': 'Security+',
            'category': 'Security Fundamentals',
            'domain': 'Security Operations',
            'level': 'Beginner',
            'cost': 370,
            'is_deleted': False
        }
        
        # Mock query chain
        mock_query = mock_session.query.return_value
        mock_filter = mock_query.filter.return_value
        mock_filter.all.return_value = [mock_cert1, mock_cert2]
        
        # Call the function
        result = get_certifications()
        
        # Verify results
        assert isinstance(result, list)
        assert len(result) == 2
        assert result[0]['name'] == 'CISSP'
        assert result[1]['name'] == 'Security+'
        
        # Verify database interactions
        mock_session.query.assert_called_once()
        mock_query.filter.assert_called_once()
        mock_session.close.assert_called_once()
    
    @patch('utils.certification_utils.get_db')
    def test_get_certifications_empty_result(self, mock_get_db):
        """Test get_certifications with no results"""
        # Mock database session
        mock_session = MagicMock()
        mock_get_db.return_value = mock_session
        
        # Mock empty query result
        mock_query = mock_session.query.return_value
        mock_filter = mock_query.filter.return_value
        mock_filter.all.return_value = []
        
        # Call the function
        result = get_certifications()
        
        # Verify results
        assert isinstance(result, list)
        assert len(result) == 0
        
        # Verify database cleanup
        mock_session.close.assert_called_once()
    
    @patch('utils.certification_utils.get_db')
    def test_get_certifications_filters_deleted(self, mock_get_db):
        """Test that get_certifications filters out deleted certifications"""
        # Mock database session
        mock_session = MagicMock()
        mock_get_db.return_value = mock_session
        
        # Mock certification objects (including deleted ones)
        mock_cert_active = MagicMock()
        mock_cert_active.to_dict.return_value = {
            'id': 1,
            'name': 'CISSP',
            'is_deleted': False
        }
        
        # Mock query chain - should only return non-deleted certs
        mock_query = mock_session.query.return_value
        mock_filter = mock_query.filter.return_value
        mock_filter.all.return_value = [mock_cert_active]
        
        # Call the function
        result = get_certifications()
        
        # Verify that filter was called to exclude deleted certifications
        from models.certification import Certification
        mock_session.query.assert_called_once_with(Certification)
        mock_query.filter.assert_called_once()
        
        # Check that the filter condition was for is_deleted == False
        filter_call_args = mock_query.filter.call_args[0]
        # The filter should be checking for is_deleted == False
        assert len(filter_call_args) == 1
        
        # Verify results
        assert len(result) == 1
        assert result[0]['name'] == 'CISSP'
    
    @patch('utils.certification_utils.get_db')
    def test_get_certifications_database_error(self, mock_get_db):
        """Test get_certifications handling database errors"""
        # Mock database session that raises an exception
        mock_session = MagicMock()
        mock_get_db.return_value = mock_session
        
        # Mock query to raise an exception
        mock_session.query.side_effect = Exception("Database connection error")
        
        # Call the function and expect it to raise an exception
        with pytest.raises(Exception) as exc_info:
            get_certifications()
        
        assert "Database connection error" in str(exc_info.value)
        
        # Verify database cleanup still happens
        mock_session.close.assert_called_once()
    
    @patch('utils.certification_utils.get_db')
    def test_get_certifications_to_dict_error(self, mock_get_db):
        """Test get_certifications handling to_dict errors"""
        # Mock database session
        mock_session = MagicMock()
        mock_get_db.return_value = mock_session
        
        # Mock certification that raises error in to_dict
        mock_cert = MagicMock()
        mock_cert.to_dict.side_effect = Exception("Serialization error")
        
        # Mock query chain
        mock_query = mock_session.query.return_value
        mock_filter = mock_query.filter.return_value
        mock_filter.all.return_value = [mock_cert]
        
        # Call the function and expect it to raise an exception
        with pytest.raises(Exception) as exc_info:
            get_certifications()
        
        assert "Serialization error" in str(exc_info.value)
        
        # Verify database cleanup
        mock_session.close.assert_called_once()
    
    def test_get_certifications_with_args_kwargs(self):
        """Test that get_certifications accepts arbitrary args and kwargs"""
        # The function signature shows it accepts *args, **kwargs
        # but doesn't use them, so we test that it doesn't break
        with patch('utils.certification_utils.get_db') as mock_get_db:
            mock_session = MagicMock()
            mock_get_db.return_value = mock_session
            
            # Mock empty result
            mock_query = mock_session.query.return_value
            mock_filter = mock_query.filter.return_value
            mock_filter.all.return_value = []
            
            # Call with various args and kwargs
            result1 = get_certifications("arg1", "arg2")
            result2 = get_certifications(param1="value1", param2="value2")
            result3 = get_certifications("arg", param="value")
            
            # All should return empty list and not crash
            assert result1 == []
            assert result2 == []
            assert result3 == []


class TestCertificationUtilsIntegration:
    """Integration tests for certification utils"""
    
    def test_get_certifications_with_real_data(self, db_session: Session):
        """Test get_certifications with real database data"""
        # Create test organization
        org = Organization(
            name="Test Certification Body",
            country="US",
            description="Test organization for certification utils"
        )
        db_session.add(org)
        db_session.commit()
        
        # Create test certifications
        cert1 = Certification(
            name="Test Security+",
            category="Security Fundamentals",
            domain="Security Operations",
            level="Beginner",
            cost=370,
            organization_id=org.id,
            is_deleted=False
        )
        
        cert2 = Certification(
            name="Test CISSP",
            category="Security Management",
            domain="Security Management",
            level="Advanced",
            cost=749,
            organization_id=org.id,
            is_deleted=False
        )
        
        # Create a deleted certification (should be filtered out)
        cert3 = Certification(
            name="Deleted Cert",
            category="Obsolete",
            domain="Legacy",
            level="Intermediate",
            cost=500,
            organization_id=org.id,
            is_deleted=True
        )
        
        db_session.add_all([cert1, cert2, cert3])
        db_session.commit()
        
        # Test the function
        with patch('utils.certification_utils.get_db') as mock_get_db:
            mock_get_db.return_value = db_session
            
            result = get_certifications()
            
            # Should return only non-deleted certifications
            assert isinstance(result, list)
            assert len(result) == 2
            
            # Check that results contain expected data
            cert_names = [cert['name'] for cert in result]
            assert "Test Security+" in cert_names
            assert "Test CISSP" in cert_names
            assert "Deleted Cert" not in cert_names
            
            # Verify structure of returned data
            for cert_dict in result:
                assert 'id' in cert_dict
                assert 'name' in cert_dict
                assert 'category' in cert_dict
                assert 'domain' in cert_dict
                assert 'level' in cert_dict
                assert 'cost' in cert_dict
                assert cert_dict['is_deleted'] is False
    
    def test_get_certifications_performance(self):
        """Test get_certifications performance characteristics"""
        with patch('utils.certification_utils.get_db') as mock_get_db:
            mock_session = MagicMock()
            mock_get_db.return_value = mock_session
            
            # Create many mock certifications
            mock_certs = []
            for i in range(1000):
                mock_cert = MagicMock()
                mock_cert.to_dict.return_value = {
                    'id': i,
                    'name': f'Cert {i}',
                    'category': 'Test',
                    'domain': 'Test Domain',
                    'level': 'Intermediate',
                    'cost': 500,
                    'is_deleted': False
                }
                mock_certs.append(mock_cert)
            
            # Mock query chain
            mock_query = mock_session.query.return_value
            mock_filter = mock_query.filter.return_value
            mock_filter.all.return_value = mock_certs
            
            # Measure execution time
            import time
            start_time = time.time()
            result = get_certifications()
            end_time = time.time()
            
            # Verify results
            assert len(result) == 1000
            
            # Performance should be reasonable (less than 1 second for 1000 items)
            execution_time = end_time - start_time
            assert execution_time < 1.0
            
            # Verify database was properly closed
            mock_session.close.assert_called_once()


class TestCertificationUtilsEdgeCases:
    """Test edge cases for certification utils"""
    
    @patch('utils.certification_utils.get_db')
    def test_get_certifications_none_result(self, mock_get_db):
        """Test get_certifications when query returns None"""
        mock_session = MagicMock()
        mock_get_db.return_value = mock_session
        
        # Mock query to return None
        mock_query = mock_session.query.return_value
        mock_filter = mock_query.filter.return_value
        mock_filter.all.return_value = None
        
        # This should handle None gracefully
        with pytest.raises(TypeError):
            # The function tries to iterate over None, which should raise TypeError
            get_certifications()
        
        mock_session.close.assert_called_once()
    
    @patch('utils.certification_utils.get_db')
    def test_get_certifications_mixed_valid_invalid(self, mock_get_db):
        """Test get_certifications with mix of valid and invalid cert objects"""
        mock_session = MagicMock()
        mock_get_db.return_value = mock_session
        
        # Mock valid certification
        mock_cert_valid = MagicMock()
        mock_cert_valid.to_dict.return_value = {
            'id': 1,
            'name': 'Valid Cert',
            'is_deleted': False
        }
        
        # Mock invalid certification (to_dict raises exception)
        mock_cert_invalid = MagicMock()
        mock_cert_invalid.to_dict.side_effect = Exception("Invalid cert")
        
        # Mock query chain
        mock_query = mock_session.query.return_value
        mock_filter = mock_query.filter.return_value
        mock_filter.all.return_value = [mock_cert_valid, mock_cert_invalid]
        
        # Should raise exception on first invalid cert
        with pytest.raises(Exception) as exc_info:
            get_certifications()
        
        assert "Invalid cert" in str(exc_info.value)
        mock_session.close.assert_called_once()
    
    @patch('utils.certification_utils.get_db')
    def test_get_certifications_database_session_cleanup(self, mock_get_db):
        """Test that database session is always cleaned up"""
        mock_session = MagicMock()
        mock_get_db.return_value = mock_session
        
        # Test successful case
        mock_query = mock_session.query.return_value
        mock_filter = mock_query.filter.return_value
        mock_filter.all.return_value = []
        
        get_certifications()
        mock_session.close.assert_called_once()
        
        # Reset mock
        mock_session.reset_mock()
        
        # Test exception case
        mock_session.query.side_effect = Exception("DB Error")
        
        with pytest.raises(Exception):
            get_certifications()
        
        # Session should still be closed
        mock_session.close.assert_called_once()
