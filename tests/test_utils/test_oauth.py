import pytest
from unittest.mock import patch, MagicMock
from utils.oauth import OAuthHand<PERSON>, init_auth_state
import streamlit as st

@pytest.fixture
def mock_session():
    """Mock Streamlit session state"""
    with patch("streamlit.session_state") as mock_state:
        mock_state.oauth_states = {}
        mock_state.user = None
        yield mock_state

def test_get_authorization_url():
    """Test authorization URL generation"""
    with patch("utils.oauth.OAuthHandler.initialize_oauth_session") as mock_init:
        mock_session = MagicMock()
        mock_session.create_authorization_url.return_value = ("https://example.com/auth", "state123")
        mock_init.return_value = mock_session
        
        with patch("utils.oauth.OAuthSessionManager.store_state") as mock_store:
            url, state = OAuthHandler.get_authorization_url("github")
            assert url == "https://example.com/auth"
            assert state == "state123"
            assert mock_store.called
