import pytest
from unittest.mock import patch, MagicMock
import os

# Mock the missing components.icons module
with patch.dict('sys.modules', {'components.icons': MagicMock()}):
    from utils.oauth import OAuthHandler


@pytest.fixture
def mock_env_vars():
    """Mock environment variables for OAuth"""
    env_vars = {
        'GITHUB_CLIENT_ID': 'test_client_id',
        'GITHUB_CLIENT_SECRET': 'test_client_secret',
        'OAUTH_REDIRECT_URI': 'http://localhost:8501/oauth/callback'
    }
    with patch.dict(os.environ, env_vars):
        yield env_vars


def test_oauth_config():
    """Test OAuth configuration"""
    config = OAuthHandler.CONFIGS.get('github')
    assert config is not None
    assert 'authorize_url' in config
    assert 'token_url' in config
    assert 'userinfo_url' in config


def test_get_callback_uri():
    """Test callback URI generation"""
    with patch.dict(os.environ, {'OAUTH_REDIRECT_URI': 'http://localhost:8501/oauth/callback'}):
        uri = OAuthHandler.get_callback_uri('github')
        assert uri == 'http://localhost:8501/oauth/callback?provider=github'
