"""
Test to verify PostgreSQL test database setup is working correctly
"""
import pytest
from sqlalchemy import text
from models.user import User
from models.certification import Organization, Certification


def test_database_connection(db_session):
    """Test that we can connect to the PostgreSQL test database"""
    # Simple query to verify connection
    result = db_session.execute(text("SELECT 1 as test_value"))
    assert result.scalar() == 1


def test_database_is_postgresql(db_session):
    """Verify we're actually using PostgreSQL, not SQLite"""
    result = db_session.execute(text("SELECT version()"))
    version = result.scalar()
    assert "PostgreSQL" in version
    print(f"Using database: {version}")


def test_table_creation(db_session):
    """Test that all tables are created correctly"""
    # Check that main tables exist
    tables_to_check = [
        'users', 'organizations', 'certifications',
        'user_preferences', 'user_activities', 'reports'
    ]

    for table_name in tables_to_check:
        result = db_session.execute(text(f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = '{table_name}'
            )
        """))
        assert result.scalar() is True, f"Table {table_name} should exist"


def test_transaction_rollback(db_session):
    """Test that transactions are properly rolled back between tests"""
    # Create a user
    user = User(
        username="test_user",
        email="<EMAIL>",
        password="hashed_password"
    )
    db_session.add(user)
    db_session.flush()  # Flush to get ID but don't commit

    user_id = user.id
    assert user_id is not None

    # Verify user exists in this session
    found_user = db_session.query(User).filter(User.id == user_id).first()
    assert found_user is not None
    assert found_user.username == "test_user"


def test_transaction_rollback_verification(db_session):
    """Verify that the user from the previous test was rolled back"""
    # This test should not see any users from the previous test
    user_count = db_session.query(User).count()
    assert user_count == 0, "Previous test data should be rolled back"


def test_foreign_key_constraints(db_session):
    """Test that foreign key constraints work correctly"""
    # Create an organization first
    org = Organization(
        name="Test Organization",
        country="US",
        description="A test organization"
    )
    db_session.add(org)
    db_session.flush()

    # Create a certification linked to the organization
    cert = Certification(
        name="Test Certification",
        category="Security",
        domain="Network Security",
        level="Beginner",
        difficulty=1,
        description="A test certification",
        organization_id=org.id
    )
    db_session.add(cert)
    db_session.flush()

    # Verify the relationship works
    assert cert.organization_id == org.id

    # Test that we can query through the relationship
    found_cert = db_session.query(Certification).filter(
        Certification.organization_id == org.id
    ).first()
    assert found_cert is not None
    assert found_cert.name == "Test Certification"


def test_concurrent_access_simulation(db_session):
    """Test that the database handles concurrent-like access patterns"""
    # Create multiple records in a single transaction
    orgs = []
    for i in range(5):
        org = Organization(
            name=f"Organization {i}",
            country="US",
            description=f"Description for organization {i}"
        )
        orgs.append(org)
        db_session.add(org)

    db_session.flush()

    # Verify all were created
    org_count = db_session.query(Organization).count()
    assert org_count == 5

    # Verify we can query them
    for i, org in enumerate(orgs):
        found_org = db_session.query(Organization).filter(
            Organization.name == f"Organization {i}"
        ).first()
        assert found_org is not None
        assert found_org.description == f"Description for organization {i}"


@pytest.mark.integration
def test_clean_db_session_fixture(clean_db_session):
    """Test the clean_db_session fixture that commits changes"""
    # Create a user that should be committed
    user = User(
        username="persistent_user",
        email="<EMAIL>",
        password="hashed_password"
    )
    clean_db_session.add(user)
    clean_db_session.commit()

    # Verify user was committed
    user_count = clean_db_session.query(User).count()
    assert user_count == 1

    # Note: The clean_db_session fixture will clean up this data after the test


def test_database_performance_basic(db_session):
    """Basic performance test to ensure database operations are reasonably fast"""
    import time

    start_time = time.time()

    # Create and query some data
    for i in range(10):
        org = Organization(
            name=f"Perf Test Org {i}",
            country="US",
            description=f"Performance test organization {i}"
        )
        db_session.add(org)

    db_session.flush()

    # Query the data
    orgs = db_session.query(Organization).filter(
        Organization.name.like("Perf Test Org%")
    ).all()

    end_time = time.time()
    duration = end_time - start_time

    assert len(orgs) == 10
    assert duration < 1.0, f"Database operations took too long: {duration:.2f}s"
    print(f"Database operations completed in {duration:.3f}s")
