import pytest
import os
import sys

def test_environment_variables():
    """Test that required environment variables are set"""
    required_vars = ["DATABASE_URL", "ANTHROPIC_API_KEY"]
    for var in required_vars:
        assert var in os.environ or var in [s.split("=")[0] for s in open(".env").readlines()]

def test_python_version():
    """Test that Python version meets requirements"""
    version_info = sys.version_info
    assert version_info.major == 3
    assert version_info.minor >= 11
