"""
End-to-end tests for career path functionality
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch
import json


@pytest.mark.e2e
def test_complete_career_path_user_journey(test_client, multiple_certifications):
    """Test complete user journey from search to career path generation"""
    
    # Step 1: User searches for certifications
    search_response = test_client.get("/api/v1/certifications/search?q=Security")
    assert search_response.status_code == 200
    search_data = search_response.json()
    assert len(search_data) > 0
    
    # Step 2: User views certification details
    cert_id = search_data[0]["id"]
    detail_response = test_client.get(f"/api/v1/certifications/{cert_id}")
    assert detail_response.status_code == 200
    cert_details = detail_response.json()
    assert "name" in cert_details
    
    # Step 3: User generates career path
    career_path_data = {
        "current_role": "IT Support Specialist",
        "target_role": "Security Analyst",
        "years_experience": 2,
        "current_skills": ["Windows Administration", "Network Basics"],
        "interests": ["Cybersecurity", "Incident Response"],
        "budget": 2000,
        "timeline": "12 months"
    }
    
    with patch('utils.claude_assistant.generate_career_path') as mock_generate:
        mock_generate.return_value = {
            "stages": [
                {
                    "stage": "Foundation",
                    "duration": "3 months",
                    "certifications": ["CompTIA Security+"],
                    "cost": 370,
                    "description": "Build fundamental security knowledge"
                },
                {
                    "stage": "Specialization",
                    "duration": "6 months",
                    "certifications": ["CompTIA CySA+"],
                    "cost": 392,
                    "description": "Develop analyst skills"
                }
            ],
            "total_cost": 762,
            "total_duration": "9 months",
            "alignment_score": 85
        }
        
        career_response = test_client.post("/api/v1/career-path", json=career_path_data)
        assert career_response.status_code == 200
        career_data = career_response.json()
        
        # Verify career path structure
        assert "stages" in career_data
        assert "total_cost" in career_data
        assert len(career_data["stages"]) == 2
        assert career_data["total_cost"] == 762


@pytest.mark.e2e
def test_career_path_with_different_experience_levels(test_client, multiple_certifications):
    """Test career path generation for users with different experience levels"""
    
    experience_scenarios = [
        {
            "name": "Entry Level",
            "data": {
                "current_role": "Student",
                "target_role": "Security Analyst",
                "years_experience": 0,
                "current_skills": [],
                "interests": ["Cybersecurity"],
                "budget": 1500,
                "timeline": "18 months"
            },
            "expected_stages": 3
        },
        {
            "name": "Mid Level",
            "data": {
                "current_role": "Network Administrator",
                "target_role": "Security Engineer",
                "years_experience": 5,
                "current_skills": ["Networking", "System Administration"],
                "interests": ["Security Architecture"],
                "budget": 3000,
                "timeline": "12 months"
            },
            "expected_stages": 2
        },
        {
            "name": "Senior Level",
            "data": {
                "current_role": "Security Engineer",
                "target_role": "CISO",
                "years_experience": 10,
                "current_skills": ["Security Engineering", "Risk Management"],
                "interests": ["Leadership", "Strategy"],
                "budget": 5000,
                "timeline": "24 months"
            },
            "expected_stages": 2
        }
    ]
    
    for scenario in experience_scenarios:
        with patch('utils.claude_assistant.generate_career_path') as mock_generate:
            # Mock different responses based on experience level
            if scenario["data"]["years_experience"] == 0:
                mock_response = {
                    "stages": [
                        {"stage": "Foundation", "cost": 370},
                        {"stage": "Core Skills", "cost": 400},
                        {"stage": "Specialization", "cost": 500}
                    ],
                    "total_cost": 1270,
                    "alignment_score": 75
                }
            elif scenario["data"]["years_experience"] == 5:
                mock_response = {
                    "stages": [
                        {"stage": "Security Transition", "cost": 600},
                        {"stage": "Advanced Skills", "cost": 800}
                    ],
                    "total_cost": 1400,
                    "alignment_score": 85
                }
            else:  # Senior level
                mock_response = {
                    "stages": [
                        {"stage": "Leadership", "cost": 1000},
                        {"stage": "Executive", "cost": 1500}
                    ],
                    "total_cost": 2500,
                    "alignment_score": 90
                }
            
            mock_generate.return_value = mock_response
            
            response = test_client.post("/api/v1/career-path", json=scenario["data"])
            assert response.status_code == 200
            
            data = response.json()
            assert len(data["stages"]) == scenario["expected_stages"]
            assert data["total_cost"] <= scenario["data"]["budget"]
            
            print(f"✅ {scenario['name']} scenario completed successfully")


@pytest.mark.e2e
def test_career_path_error_handling_flow(test_client):
    """Test error handling throughout the career path flow"""
    
    # Test 1: Invalid input validation
    invalid_data = {
        "current_role": "",  # Empty role
        "target_role": "Security Analyst",
        "years_experience": -1  # Invalid experience
    }
    
    response = test_client.post("/api/v1/career-path", json=invalid_data)
    assert response.status_code == 422
    error_data = response.json()
    assert "detail" in error_data
    
    # Test 2: API service failure
    valid_data = {
        "current_role": "IT Support",
        "target_role": "Security Analyst",
        "years_experience": 2
    }
    
    with patch('utils.claude_assistant.generate_career_path') as mock_generate:
        mock_generate.side_effect = Exception("Service unavailable")
        
        response = test_client.post("/api/v1/career-path", json=valid_data)
        assert response.status_code == 500
        error_data = response.json()
        assert "detail" in error_data
    
    # Test 3: Recovery after error
    with patch('utils.claude_assistant.generate_career_path') as mock_generate:
        mock_generate.return_value = {
            "stages": [{"stage": "Recovery", "cost": 100}],
            "total_cost": 100
        }
        
        response = test_client.post("/api/v1/career-path", json=valid_data)
        assert response.status_code == 200
        
        print("✅ Error handling and recovery flow completed")


@pytest.mark.e2e
def test_career_path_budget_optimization_flow(test_client, multiple_certifications):
    """Test career path optimization based on budget constraints"""
    
    budget_scenarios = [
        {
            "budget": 500,
            "expected_max_cost": 500,
            "description": "Low budget scenario"
        },
        {
            "budget": 2000,
            "expected_max_cost": 2000,
            "description": "Medium budget scenario"
        },
        {
            "budget": 5000,
            "expected_max_cost": 5000,
            "description": "High budget scenario"
        }
    ]
    
    base_data = {
        "current_role": "IT Support",
        "target_role": "Security Analyst",
        "years_experience": 2,
        "timeline": "12 months"
    }
    
    for scenario in budget_scenarios:
        request_data = base_data.copy()
        request_data["budget"] = scenario["budget"]
        
        with patch('utils.claude_assistant.generate_career_path') as mock_generate:
            # Mock budget-appropriate response
            if scenario["budget"] <= 500:
                mock_response = {
                    "stages": [{"stage": "Budget Foundation", "cost": 370}],
                    "total_cost": 370
                }
            elif scenario["budget"] <= 2000:
                mock_response = {
                    "stages": [
                        {"stage": "Foundation", "cost": 370},
                        {"stage": "Intermediate", "cost": 400}
                    ],
                    "total_cost": 770
                }
            else:
                mock_response = {
                    "stages": [
                        {"stage": "Foundation", "cost": 370},
                        {"stage": "Intermediate", "cost": 400},
                        {"stage": "Advanced", "cost": 800}
                    ],
                    "total_cost": 1570
                }
            
            mock_generate.return_value = mock_response
            
            response = test_client.post("/api/v1/career-path", json=request_data)
            assert response.status_code == 200
            
            data = response.json()
            assert data["total_cost"] <= scenario["expected_max_cost"]
            
            print(f"✅ {scenario['description']} completed - Cost: ${data['total_cost']}")


@pytest.mark.e2e
def test_career_path_timeline_optimization_flow(test_client):
    """Test career path optimization based on timeline constraints"""
    
    timeline_scenarios = [
        {
            "timeline": "6 months",
            "expected_max_duration": "6 months",
            "description": "Accelerated timeline"
        },
        {
            "timeline": "12 months",
            "expected_max_duration": "12 months",
            "description": "Standard timeline"
        },
        {
            "timeline": "24 months",
            "expected_max_duration": "24 months",
            "description": "Extended timeline"
        }
    ]
    
    base_data = {
        "current_role": "IT Support",
        "target_role": "Security Analyst",
        "years_experience": 2,
        "budget": 2000
    }
    
    for scenario in timeline_scenarios:
        request_data = base_data.copy()
        request_data["timeline"] = scenario["timeline"]
        
        with patch('utils.claude_assistant.generate_career_path') as mock_generate:
            # Mock timeline-appropriate response
            if "6 months" in scenario["timeline"]:
                mock_response = {
                    "stages": [{"stage": "Intensive Foundation", "duration": "3 months"}],
                    "total_duration": "3 months"
                }
            elif "12 months" in scenario["timeline"]:
                mock_response = {
                    "stages": [
                        {"stage": "Foundation", "duration": "4 months"},
                        {"stage": "Specialization", "duration": "6 months"}
                    ],
                    "total_duration": "10 months"
                }
            else:
                mock_response = {
                    "stages": [
                        {"stage": "Foundation", "duration": "6 months"},
                        {"stage": "Intermediate", "duration": "8 months"},
                        {"stage": "Advanced", "duration": "6 months"}
                    ],
                    "total_duration": "20 months"
                }
            
            mock_generate.return_value = mock_response
            
            response = test_client.post("/api/v1/career-path", json=request_data)
            assert response.status_code == 200
            
            data = response.json()
            # Verify timeline constraint is respected
            assert "total_duration" in data
            
            print(f"✅ {scenario['description']} completed - Duration: {data.get('total_duration', 'N/A')}")


@pytest.mark.e2e
def test_career_path_integration_with_certification_data(test_client, multiple_certifications):
    """Test integration between career path generation and certification database"""
    
    # Step 1: Verify certifications exist in database
    certs_response = test_client.get("/api/v1/certifications")
    assert certs_response.status_code == 200
    available_certs = certs_response.json()
    assert len(available_certs) >= 3
    
    cert_names = [cert["name"] for cert in available_certs]
    
    # Step 2: Generate career path that references existing certifications
    career_data = {
        "current_role": "IT Support",
        "target_role": "Security Analyst",
        "years_experience": 2
    }
    
    with patch('utils.claude_assistant.generate_career_path') as mock_generate:
        # Mock response that references actual certifications in database
        mock_response = {
            "stages": [
                {
                    "stage": "Foundation",
                    "certifications": ["CompTIA Security+"],  # Should exist in test data
                    "cost": 370
                }
            ],
            "total_cost": 370
        }
        mock_generate.return_value = mock_response
        
        response = test_client.post("/api/v1/career-path", json=career_data)
        assert response.status_code == 200
        
        data = response.json()
        recommended_cert = data["stages"][0]["certifications"][0]
        
        # Verify the recommended certification exists in our database
        assert recommended_cert in cert_names
        
        print(f"✅ Integration test completed - Recommended: {recommended_cert}")


@pytest.mark.e2e
@pytest.mark.slow
def test_complete_user_workflow_with_multiple_paths(test_client, multiple_certifications):
    """Test complete workflow with multiple career path generations"""
    
    user_scenarios = [
        {
            "current_role": "Help Desk",
            "target_role": "Security Analyst",
            "years_experience": 1
        },
        {
            "current_role": "Network Admin",
            "target_role": "Security Engineer", 
            "years_experience": 5
        },
        {
            "current_role": "System Admin",
            "target_role": "Penetration Tester",
            "years_experience": 3
        }
    ]
    
    for i, scenario in enumerate(user_scenarios):
        print(f"Testing scenario {i+1}: {scenario['current_role']} → {scenario['target_role']}")
        
        with patch('utils.claude_assistant.generate_career_path') as mock_generate:
            mock_generate.return_value = {
                "stages": [
                    {"stage": f"Path {i+1} Foundation", "cost": 300 + i*100},
                    {"stage": f"Path {i+1} Advanced", "cost": 500 + i*100}
                ],
                "total_cost": 800 + i*200,
                "alignment_score": 80 + i*5
            }
            
            response = test_client.post("/api/v1/career-path", json=scenario)
            assert response.status_code == 200
            
            data = response.json()
            assert len(data["stages"]) == 2
            assert data["total_cost"] == 800 + i*200
            assert data["alignment_score"] == 80 + i*5
            
            print(f"  ✅ Scenario {i+1} completed successfully")
    
    print("✅ All user workflow scenarios completed")
