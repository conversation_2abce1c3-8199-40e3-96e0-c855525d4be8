"""Integration tests for job management functionality"""
import pytest
from models.job import SecurityJob
from datetime import datetime
from sqlalchemy.exc import IntegrityError
from utils.job_management import get_filtered_jobs
from scripts.populate_security_jobs import SECURITY_DOMAINS

def test_job_create(db_session):
    """Test job creation with valid data"""
    job = SecurityJob(
        title="DevSecOps Engineer",
        domain="Security Engineering",
        description="DevSecOps position",
        experience_level="Mid Level",
        salary_range_min=80000,
        salary_range_max=120000,
        skills_required="CI/CD, Security Automation"
    )

    db_session.add(job)
    db_session.commit()

    saved_job = db_session.query(SecurityJob).filter_by(title="DevSecOps Engineer").first()
    assert saved_job is not None
    assert saved_job.domain == "Security Engineering"
    assert saved_job.experience_level == "Mid Level"

def test_get_filtered_jobs_search(db_session, sample_jobs):
    """Test job search with parameterized queries"""
    # Test title search
    jobs, total = get_filtered_jobs(db_session, search_term="analyst", domain="All")
    assert len(jobs) == 1
    assert total == 1
    assert jobs[0].title == "Security Analyst"

    # Test domain search
    jobs, total = get_filtered_jobs(db_session, search_term="operations", domain="All")
    assert len(jobs) == 1
    assert total == 1
    assert jobs[0].domain == "Security Operations"

def test_get_filtered_jobs_domain(db_session, sample_jobs):
    """Test domain filtering with parameterized queries"""
    for domain in SECURITY_DOMAINS:
        jobs, total = get_filtered_jobs(db_session, domain=domain)
        domain_jobs = [j for j in sample_jobs if j.domain == domain]
        assert len(jobs) == len(domain_jobs)
        assert total == len(domain_jobs)

def test_get_filtered_jobs_pagination(db_session, sample_jobs):
    """Test pagination with parameterized queries"""
    # Test first page
    jobs, total = get_filtered_jobs(db_session, per_page=2, page=0)
    assert len(jobs) == 2
    assert total == 3

    # Test second page
    jobs, total = get_filtered_jobs(db_session, per_page=2, page=1)
    assert len(jobs) == 1
    assert total == 3

def test_invalid_job_creation(db_session):
    """Test validation for job creation"""
    # Test missing required fields
    job = SecurityJob()
    db_session.add(job)

    with pytest.raises(IntegrityError):
        db_session.commit()
    db_session.rollback()

    # Test duplicate job with parameterized query
    job1 = SecurityJob(title="Test Job", domain="Security Testing")
    job2 = SecurityJob(title="Test Job", domain="Security Testing")

    db_session.add(job1)
    db_session.commit()

    db_session.add(job2)
    with pytest.raises(IntegrityError):
        db_session.commit()

def test_job_update_parameterized(db_session):
    """Test job update with parameterized queries"""
    # Create test job
    job = SecurityJob(
        title="Security Consultant",
        domain="Security Management",
        description="Senior consultant role"
    )
    db_session.add(job)
    db_session.commit()

    # Update using parameterized query
    job_id = job.id
    new_title = "Senior Security Consultant"
    new_level = "Senior"

    db_session.query(SecurityJob).filter(
        SecurityJob.id == job_id
    ).update({
        "title": new_title,
        "experience_level": new_level,
        "updated_at": datetime.utcnow()
    })
    db_session.commit()

    # Verify update with parameterized query
    updated_job = db_session.query(SecurityJob).filter(
        SecurityJob.id == job_id
    ).first()

    assert updated_job.title == new_title
    assert updated_job.experience_level == new_level
    assert updated_job.updated_at is not None

def test_job_soft_delete_and_uniqueness(db_session):
    """Test soft delete functionality and uniqueness constraints"""
    # Create initial job
    job = SecurityJob(
        title="Test Job",
        domain="Security Testing"
    )
    db_session.add(job)
    db_session.commit()

    # Soft delete the job
    job.soft_delete()
    db_session.commit()

    # Verify soft delete
    assert job.is_deleted
    assert job.deleted_at is not None

    # Create new job with same title/domain
    new_job = SecurityJob(
        title="Test Job",
        domain="Security Testing"
    )
    db_session.add(new_job)
    # Should work since previous job is soft deleted
    db_session.commit()

    assert not new_job.is_deleted
    assert new_job.deleted_at is None

@pytest.fixture
def sample_jobs(db_session):
    """Create sample jobs for testing"""
    jobs = [
        SecurityJob(
            title="Security Analyst",
            domain="Security Operations",
            description="Entry level position",
            experience_level="Entry Level"
        ),
        SecurityJob(
            title="Senior Security Engineer",
            domain="Security Engineering",
            description="Senior position",
            experience_level="Senior"
        ),
        SecurityJob(
            title="Security Architect",
            domain="Security Architecture",
            description="Lead position",
            experience_level="Lead"
        )
    ]

    for job in jobs:
        db_session.add(job)
    db_session.commit()
    return jobs

def test_combined_search_and_filter(db_session, sample_jobs):
    """Test combining search and domain filter with parameterized queries"""
    jobs, total = get_filtered_jobs(
        db_session,
        search_term="senior",
        domain="Security Engineering"
    )
    assert len(jobs) == 1
    assert total == 1
    assert jobs[0].title == "Senior Security Engineer"
    assert jobs[0].domain == "Security Engineering"