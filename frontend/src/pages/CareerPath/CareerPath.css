.career-path-container {
  padding: 20px;
}

.career-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 18px;
  color: #6c757d;
}

.career-path-content {
  display: flex;
  gap: 30px;
  margin-top: 20px;
}

.career-roles-list {
  flex: 1;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.career-roles-list ul {
  list-style-type: none;
  padding: 0;
}

.career-roles-list li {
  padding: 12px 15px;
  margin-bottom: 10px;
  background-color: white;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.career-roles-list li:hover {
  background-color: #f0f0f0;
}

.career-roles-list li.selected {
  background-color: #007bff;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.career-role-details {
  flex: 2;
  background: #f8f9fa;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.select-role-prompt {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #6c757d;
  font-style: italic;
}

.role-description {
  margin-bottom: 20px;
  line-height: 1.6;
}

.role-info {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.info-section {
  margin-bottom: 20px;
}

.info-section h4 {
  margin-bottom: 10px;
  color: #495057;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  list-style-type: none;
  padding: 0;
}

.tag {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
}

.cert-tag {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.skill-tag {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.career-progression {
  margin-top: 30px;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progression-path {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
  padding: 0 40px;
}

.path-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.node {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  color: white;
  font-weight: bold;
}

.entry {
  background-color: #4caf50;
}

.mid {
  background-color: #2196f3;
}

.senior {
  background-color: #ff9800;
}

.executive {
  background-color: #f44336;
}

.path-arrow {
  font-size: 24px;
  color: #6c757d;
}

/* Dark theme overrides for Career Path */
[data-theme='dark'] .career-roles-list {
  background: var(--color-card-background);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--color-border);
}

[data-theme='dark'] .career-roles-list li {
  background-color: #1a1a1a;
  color: var(--color-text);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--color-border);
}

[data-theme='dark'] .career-roles-list li:hover {
  background-color: #2a2a2a;
}

[data-theme='dark'] .career-roles-list li.selected {
  background-color: var(--color-primary);
  color: #000;
  box-shadow: 0 4px 8px rgba(78, 204, 163, 0.3);
}

[data-theme='dark'] .career-role-details {
  background: var(--color-card-background);
  color: var(--color-text);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--color-border);
}

[data-theme='dark'] .select-role-prompt {
  color: var(--color-text-muted);
}

[data-theme='dark'] .info-section h4 {
  color: var(--color-primary);
}

[data-theme='dark'] .cert-tag {
  background-color: #162742;
  color: #7bdba8;
  border: 1px solid #1d3253;
}

[data-theme='dark'] .skill-tag {
  background-color: #192f27;
  color: #6bddbe;
  border: 1px solid #1e3a30;
}

[data-theme='dark'] .career-progression {
  background: var(--color-card-background);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--color-border);
}

[data-theme='dark'] .path-arrow {
  color: var(--color-text-muted);
}

[data-theme='dark'] .career-loading {
  color: var(--color-text-muted);
} 