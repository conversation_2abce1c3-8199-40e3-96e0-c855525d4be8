import React, { useEffect, useState } from 'react';
import './CareerPath.css';

interface CareerRole {
    id: number;
    title: string;
    description: string;
    averageSalary: string;
    requiredCertifications: string[];
    skillsRequired: string[];
    experienceLevel: string;
}

const mockCareerRoles: CareerRole[] = [
    {
        id: 1,
        title: "Security Analyst",
        description: "Monitors and analyzes security threats and implements protective measures.",
        averageSalary: "$85,000 - $110,000",
        requiredCertifications: ["Security+", "CySA+"],
        skillsRequired: ["Threat Detection", "Vulnerability Assessment", "Incident Response"],
        experienceLevel: "Entry to Mid-Level"
    },
    {
        id: 2,
        title: "Penetration Tester",
        description: "Simulates cyberattacks to identify security vulnerabilities in systems and networks.",
        averageSalary: "$95,000 - $130,000",
        requiredCertifications: ["CEH", "OSCP", "PenTest+"],
        skillsRequired: ["Ethical Hacking", "Exploit Development", "Social Engineering"],
        experienceLevel: "Mid-Level"
    },
    {
        id: 3,
        title: "Security Architect",
        description: "Designs security systems and infrastructure to protect against cyber threats.",
        averageSalary: "$120,000 - $160,000",
        requiredCertifications: ["CISSP", "CASP+", "CCSP"],
        skillsRequired: ["Security Design", "Risk Management", "Compliance"],
        experienceLevel: "Senior"
    },
    {
        id: 4,
        title: "Chief Information Security Officer (CISO)",
        description: "Executive responsible for an organization's information and data security.",
        averageSalary: "$150,000 - $250,000",
        requiredCertifications: ["CISSP", "CISM", "CGEIT"],
        skillsRequired: ["Leadership", "Strategy", "Governance", "Risk Management"],
        experienceLevel: "Executive"
    }
];

const CareerPath: React.FC = () => {
    const [careerRoles, setCareerRoles] = useState<CareerRole[]>([]);
    const [selectedRole, setSelectedRole] = useState<CareerRole | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true);

    useEffect(() => {
        // Simulate API call
        const fetchCareerRoles = async () => {
            try {
                // In a real app, this would be an API call
                // const response = await fetch('/api/v1/career/roles');
                // const data = await response.json();

                // Using mock data for now
                setTimeout(() => {
                    setCareerRoles(mockCareerRoles);
                    setIsLoading(false);
                }, 800);
            } catch (err) {
                console.error("Failed to fetch career roles:", err);
                setIsLoading(false);
            }
        };

        fetchCareerRoles();
    }, []);

    const handleRoleClick = (role: CareerRole) => {
        setSelectedRole(role);
    };

    if (isLoading) {
        return <div className="career-loading">Loading career paths...</div>;
    }

    return (
        <div className="career-path-container">
            <h2>Cybersecurity Career Paths</h2>

            <div className="career-path-content">
                <div className="career-roles-list">
                    <h3>Career Roles</h3>
                    <ul>
                        {careerRoles.map(role => (
                            <li
                                key={role.id}
                                className={selectedRole?.id === role.id ? 'selected' : ''}
                                onClick={() => handleRoleClick(role)}
                            >
                                {role.title}
                            </li>
                        ))}
                    </ul>
                </div>

                <div className="career-role-details">
                    {selectedRole ? (
                        <>
                            <h3>{selectedRole.title}</h3>
                            <p className="role-description">{selectedRole.description}</p>

                            <div className="role-info">
                                <div className="info-section">
                                    <h4>Average Salary</h4>
                                    <p>{selectedRole.averageSalary}</p>
                                </div>

                                <div className="info-section">
                                    <h4>Experience Level</h4>
                                    <p>{selectedRole.experienceLevel}</p>
                                </div>
                            </div>

                            <div className="info-section">
                                <h4>Required Certifications</h4>
                                <ul className="tags">
                                    {selectedRole.requiredCertifications.map((cert, index) => (
                                        <li key={index} className="tag cert-tag">{cert}</li>
                                    ))}
                                </ul>
                            </div>

                            <div className="info-section">
                                <h4>Skills Required</h4>
                                <ul className="tags">
                                    {selectedRole.skillsRequired.map((skill, index) => (
                                        <li key={index} className="tag skill-tag">{skill}</li>
                                    ))}
                                </ul>
                            </div>
                        </>
                    ) : (
                        <div className="select-role-prompt">
                            <p>Select a role from the list to view details</p>
                        </div>
                    )}
                </div>
            </div>

            <div className="career-progression">
                <h3>Career Progression Path</h3>
                <div className="progression-path">
                    <div className="path-node">
                        <div className="node entry">Entry</div>
                        <p>Security Analyst</p>
                    </div>
                    <div className="path-arrow">→</div>
                    <div className="path-node">
                        <div className="node mid">Mid-Level</div>
                        <p>Penetration Tester</p>
                    </div>
                    <div className="path-arrow">→</div>
                    <div className="path-node">
                        <div className="node senior">Senior</div>
                        <p>Security Architect</p>
                    </div>
                    <div className="path-arrow">→</div>
                    <div className="path-node">
                        <div className="node executive">Executive</div>
                        <p>CISO</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CareerPath; 