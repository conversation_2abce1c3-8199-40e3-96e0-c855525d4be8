import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import axios from 'axios';
import CareerPathVisualization from './index';

// Mock axios
jest.mock('axios');
const mockAxios = axios as jest.Mocked<typeof axios>;

// Mock the d3 force simulation to prevent errors in testing environment
jest.mock('d3', () => {
    const originalD3 = jest.requireActual('d3');
    return {
        ...originalD3,
        forceSimulation: () => {
            return {
                force: () => {
                    return {
                        id: () => {
                            return {
                                distance: () => {
                                    return {
                                        strength: () => { }
                                    }
                                }
                            }
                        },
                        strength: () => { },
                        radius: () => { }
                    }
                },
                nodes: () => {
                    return {
                        on: () => { }
                    }
                },
                velocityDecay: () => { },
                alphaTarget: () => {
                    return {
                        restart: () => { }
                    }
                },
                stop: () => { }
            }
        },
        drag: () => {
            return {
                on: () => {
                    return {
                        on: () => {
                            return {
                                on: () => { }
                            }
                        }
                    }
                }
            }
        }
    };
});

describe('CareerPathVisualization Component', () => {
    // Mock certification data
    const mockCertifications = [
        {
            id: 1,
            name: 'CompTIA A+',
            vendor: 'CompTIA',
            level: 'beginner',
            description: 'Entry-level certification for IT technicians',
            prerequisites: []
        },
        {
            id: 2,
            name: 'CompTIA Network+',
            vendor: 'CompTIA',
            level: 'beginner',
            description: 'Certification for networking professionals',
            prerequisites: [1]
        }
    ];

    // Mock career role data
    const mockRoles = [
        {
            id: 101,
            title: 'Help Desk Technician',
            description: 'First-line support for IT issues',
            salary_range: {
                min: 35000,
                max: 55000,
                currency: 'USD'
            },
            required_certifications: [1],
            recommended_certifications: [2],
            next_roles: [102]
        },
        {
            id: 102,
            title: 'Network Administrator',
            description: 'Responsible for network infrastructure',
            salary_range: {
                min: 55000,
                max: 75000,
                currency: 'USD'
            },
            required_certifications: [2],
            recommended_certifications: [],
            next_roles: []
        }
    ];

    beforeEach(() => {
        // Mock implementation to reject the requests (so we use our mock data)
        mockAxios.get.mockImplementation((url) => {
            return Promise.reject(new Error('API not available'));
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('renders CareerPathVisualization with loading state', () => {
        render(<CareerPathVisualization />);
        expect(screen.getByText(/Loading career path data/i)).toBeInTheDocument();
    });

    test('renders visualization after loading mock data', async () => {
        render(<CareerPathVisualization />);

        // Wait for the loading state to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading career path data/i)).not.toBeInTheDocument();
        });

        // Check if the filters are rendered
        expect(screen.getByLabelText(/Certification Level/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/Vendor/i)).toBeInTheDocument();

        // Check if the legend is rendered
        expect(screen.getByText(/Job Role/i)).toBeInTheDocument();
        expect(screen.getByText(/Beginner Certification/i)).toBeInTheDocument();
        expect(screen.getByText(/Intermediate Certification/i)).toBeInTheDocument();
        expect(screen.getByText(/Advanced Certification/i)).toBeInTheDocument();
        expect(screen.getByText(/Expert Certification/i)).toBeInTheDocument();

        // Check if the link legend is rendered
        expect(screen.getByText(/Prerequisite/i)).toBeInTheDocument();
        expect(screen.getByText(/Required for Role/i)).toBeInTheDocument();
        expect(screen.getByText(/Recommended for Role/i)).toBeInTheDocument();
        expect(screen.getByText(/Career Progression/i)).toBeInTheDocument();

        // Check if the instructions are rendered
        expect(screen.getByText(/How to Use This Visualization/i)).toBeInTheDocument();
    });

    test('changes certification level filter', async () => {
        render(<CareerPathVisualization />);

        // Wait for the loading state to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading career path data/i)).not.toBeInTheDocument();
        });

        // Get the level filter dropdown
        const levelFilter = screen.getByLabelText(/Certification Level/i);

        // Change the filter to 'beginner'
        fireEvent.change(levelFilter, { target: { value: 'beginner' } });

        // Verify the value changed
        expect((levelFilter as HTMLSelectElement).value).toBe('beginner');

        // Change the filter to 'intermediate'
        fireEvent.change(levelFilter, { target: { value: 'intermediate' } });

        // Verify the value changed
        expect((levelFilter as HTMLSelectElement).value).toBe('intermediate');
    });

    test('changes vendor filter', async () => {
        render(<CareerPathVisualization />);

        // Wait for the loading state to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading career path data/i)).not.toBeInTheDocument();
        });

        // Get the vendor filter dropdown
        const vendorFilter = screen.getByLabelText(/Vendor/i);

        // Change the filter to 'CompTIA'
        fireEvent.change(vendorFilter, { target: { value: 'CompTIA' } });

        // Verify the value changed
        expect((vendorFilter as HTMLSelectElement).value).toBe('CompTIA');

        // Change the filter back to 'all'
        fireEvent.change(vendorFilter, { target: { value: 'all' } });

        // Verify the value changed
        expect((vendorFilter as HTMLSelectElement).value).toBe('all');
    });

    test('handles API error gracefully', async () => {
        // API error is already mocked in beforeEach
        render(<CareerPathVisualization />);

        // Wait for the component to handle the error and display mock data
        await waitFor(() => {
            expect(screen.queryByText(/Loading career path data/i)).not.toBeInTheDocument();
        });

        // Make sure error isn't displayed (since we're using mock data)
        expect(screen.queryByText(/Failed to load career path data/i)).not.toBeInTheDocument();

        // Verify that the visualization is shown with mock data
        expect(screen.getByText(/How to Use This Visualization/i)).toBeInTheDocument();
    });
}); 