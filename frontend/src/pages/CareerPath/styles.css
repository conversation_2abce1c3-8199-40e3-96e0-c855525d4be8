.career-path-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: '<PERSON><PERSON>', sans-serif;
}

.career-path-container h1 {
  text-align: center;
  margin-bottom: 2rem;
  color: #333;
}

/* Visualization container */
.visualization-container {
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #eaeaea;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: relative;
}

/* Filters */
.filters {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 1.5rem;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 500;
  color: #555;
}

.filter-group select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 0.9rem;
}

/* Legend */
.legend {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  border: 1px solid #eee;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.legend-color {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.legend-color.role {
  background-color: #4a6cf7;
  border: 2px solid #3a5ce5;
}

.legend-color.certification.beginner {
  background-color: #4caf50;
  border: 2px solid #388e3c;
}

.legend-color.certification.intermediate {
  background-color: #ff9800;
  border: 2px solid #f57c00;
}

.legend-color.certification.advanced {
  background-color: #f44336;
  border: 2px solid #d32f2f;
}

.legend-color.certification.expert {
  background-color: #9c27b0;
  border: 2px solid #7b1fa2;
}

.link-legend {
  margin-top: 0.5rem;
}

.legend-line {
  width: 30px;
  height: 3px;
}

.legend-line.prerequisite {
  background-color: #888;
}

.legend-line.required {
  background-color: #f44336;
}

.legend-line.recommended {
  background-color: #2196f3;
}

.legend-line.next_role {
  background-color: #4a6cf7;
  height: 4px;
}

/* SVG and D3 Elements */
.career-path-svg {
  background-color: white;
  border-radius: 4px;
  border: 1px solid #eee;
}

/* Link styling */
.link {
  stroke-width: 2px;
}

.link.prerequisite {
  stroke: #888;
  stroke-dasharray: 5, 5;
}

.link.required {
  stroke: #f44336;
}

.link.recommended {
  stroke: #2196f3;
  stroke-dasharray: 5, 3;
}

.link.next_role {
  stroke: #4a6cf7;
  stroke-width: 3px;
}

/* Arrow styling */
.arrow {
  fill: currentColor;
}

.arrow.prerequisite {
  color: #888;
}

.arrow.required {
  color: #f44336;
}

.arrow.recommended {
  color: #2196f3;
}

.arrow.next_role {
  color: #4a6cf7;
}

/* Node styling */
.node circle {
  stroke-width: 2px;
}

.node.role circle {
  fill: #4a6cf7;
  stroke: #3a5ce5;
}

.node.certification.beginner circle {
  fill: #4caf50;
  stroke: #388e3c;
}

.node.certification.intermediate circle {
  fill: #ff9800;
  stroke: #f57c00;
}

.node.certification.advanced circle {
  fill: #f44336;
  stroke: #d32f2f;
}

.node.certification.expert circle {
  fill: #9c27b0;
  stroke: #7b1fa2;
}

.node.hover circle {
  filter: brightness(1.1);
  stroke-width: 3px;
}

.node circle.highlighted {
  stroke: #ffc107;
  stroke-width: 4px;
  filter: drop-shadow(0 0 5px rgba(255, 193, 7, 0.5));
}

.node .icon {
  font-size: 1.2rem;
  fill: white;
  user-select: none;
}

.node .label {
  font-size: 0.9rem;
  fill: #333;
  font-weight: 500;
  user-select: none;
  pointer-events: none;
}

/* Tooltip */
.tooltip {
  position: absolute;
  display: none;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 300px;
  z-index: 10;
  pointer-events: none;
}

.tooltip h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #333;
  font-size: 1.1rem;
}

.tooltip p {
  margin: 0.5rem 0;
  color: #555;
  font-size: 0.9rem;
}

.tooltip-section {
  margin-top: 0.5rem;
}

.tooltip-section h4 {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: #666;
}

.tooltip-section ul {
  margin: 0.5rem 0;
  padding-left: 1.2rem;
  font-size: 0.85rem;
  color: #555;
}

/* Loading and error states */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #4a6cf7;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  padding: 2rem;
  text-align: center;
}

.error-message {
  color: #f44336;
  margin-bottom: 1rem;
}

.error-container button {
  padding: 0.5rem 1rem;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
}

/* Instructions */
.visualization-instructions {
  background-color: #f5f7ff;
  border: 1px solid #d8e1ff;
  border-radius: 8px;
  padding: 1.5rem;
}

.visualization-instructions h3 {
  margin-top: 0;
  color: #4a6cf7;
  font-size: 1.2rem;
}

.visualization-instructions ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #555;
}

.visualization-instructions li {
  margin-bottom: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .career-path-container {
    padding: 1rem;
  }
  
  .visualization-container {
    padding: 1rem;
  }
  
  .filters {
    flex-direction: column;
    gap: 1rem;
  }
  
  .filter-group {
    width: 100%;
  }
  
  .filter-group select {
    flex-grow: 1;
  }
  
  .legend {
    justify-content: center;
  }
} 