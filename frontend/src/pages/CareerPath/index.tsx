import axios from 'axios';
import * as d3 from 'd3';
import React, { useEffect, useRef, useState } from 'react';
import './styles.css';

interface Certification {
    id: number;
    name: string;
    vendor: string;
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    description: string;
    prerequisites: number[];
}

interface CareerRole {
    id: number;
    title: string;
    description: string;
    salary_range: {
        min: number;
        max: number;
        currency: string;
    };
    required_certifications: number[];
    recommended_certifications: number[];
    next_roles: number[];
}

interface NodeData extends d3.SimulationNodeDatum {
    id: number;
    name: string;
    type: 'role' | 'certification';
    level?: string;
    x: number;
    y: number;
    fx: number | null;
    fy: number | null;
    radius?: number;
    children?: number[];
}

interface LinkData {
    source: number;
    target: number;
    type: 'prerequisite' | 'required' | 'recommended' | 'next_role';
}

const CareerPathVisualization: React.FC = () => {
    const svgRef = useRef<SVGSVGElement>(null);
    const tooltipRef = useRef<HTMLDivElement>(null);

    const [roles, setRoles] = useState<CareerRole[]>([]);
    const [certifications, setCertifications] = useState<Certification[]>([]);
    const [selectedRole, setSelectedRole] = useState<number | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [nodes, setNodes] = useState<NodeData[]>([]);
    const [links, setLinks] = useState<LinkData[]>([]);
    const [filterLevel, setFilterLevel] = useState<string>('all');
    const [filterVendor, setFilterVendor] = useState<string>('all');
    const [vendors, setVendors] = useState<string[]>([]);

    // Fetch data
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);

                // Fetch career roles
                const rolesResponse = await axios.get('/api/career/roles');
                setRoles(rolesResponse.data);

                // Fetch certifications
                const certificationsResponse = await axios.get('/api/certifications');
                setCertifications(certificationsResponse.data);

                // Extract unique vendors
                const uniqueVendors = Array.from(
                    new Set(certificationsResponse.data.map((cert: Certification) => cert.vendor))
                ) as string[];
                setVendors(uniqueVendors);

                setError(null);
            } catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load career path data. Please try again later.');

                // Mock data for development
                const mockCertifications = [
                    {
                        id: 1,
                        name: 'CompTIA A+',
                        vendor: 'CompTIA',
                        level: 'beginner',
                        description: 'Entry-level certification for IT technicians',
                        prerequisites: []
                    },
                    {
                        id: 2,
                        name: 'CompTIA Network+',
                        vendor: 'CompTIA',
                        level: 'beginner',
                        description: 'Certification for networking professionals',
                        prerequisites: [1]
                    },
                    {
                        id: 3,
                        name: 'CompTIA Security+',
                        vendor: 'CompTIA',
                        level: 'intermediate',
                        description: 'Certification for security professionals',
                        prerequisites: [2]
                    },
                    {
                        id: 4,
                        name: 'CCNA',
                        vendor: 'Cisco',
                        level: 'intermediate',
                        description: 'Cisco Certified Network Associate',
                        prerequisites: [2]
                    },
                    {
                        id: 5,
                        name: 'CCNP',
                        vendor: 'Cisco',
                        level: 'advanced',
                        description: 'Cisco Certified Network Professional',
                        prerequisites: [4]
                    },
                    {
                        id: 6,
                        name: 'AWS Solutions Architect Associate',
                        vendor: 'AWS',
                        level: 'intermediate',
                        description: 'Cloud architecture certification for AWS',
                        prerequisites: []
                    },
                    {
                        id: 7,
                        name: 'AWS Solutions Architect Professional',
                        vendor: 'AWS',
                        level: 'advanced',
                        description: 'Advanced cloud architecture certification for AWS',
                        prerequisites: [6]
                    },
                    {
                        id: 8,
                        name: 'Azure Administrator',
                        vendor: 'Microsoft',
                        level: 'intermediate',
                        description: 'Cloud administration certification for Azure',
                        prerequisites: []
                    }
                ] as Certification[];

                const mockRoles = [
                    {
                        id: 101,
                        title: 'Help Desk Technician',
                        description: 'First-line support for IT issues',
                        salary_range: {
                            min: 35000,
                            max: 55000,
                            currency: 'USD'
                        },
                        required_certifications: [1],
                        recommended_certifications: [2],
                        next_roles: [102, 103]
                    },
                    {
                        id: 102,
                        title: 'Network Administrator',
                        description: 'Responsible for network infrastructure',
                        salary_range: {
                            min: 55000,
                            max: 75000,
                            currency: 'USD'
                        },
                        required_certifications: [2, 4],
                        recommended_certifications: [3],
                        next_roles: [104]
                    },
                    {
                        id: 103,
                        title: 'Security Analyst',
                        description: 'Monitors and analyzes security threats',
                        salary_range: {
                            min: 60000,
                            max: 85000,
                            currency: 'USD'
                        },
                        required_certifications: [3],
                        recommended_certifications: [5],
                        next_roles: [105]
                    },
                    {
                        id: 104,
                        title: 'Network Engineer',
                        description: 'Designs and implements network solutions',
                        salary_range: {
                            min: 70000,
                            max: 95000,
                            currency: 'USD'
                        },
                        required_certifications: [4],
                        recommended_certifications: [5],
                        next_roles: [106]
                    },
                    {
                        id: 105,
                        title: 'Cloud Administrator',
                        description: 'Manages cloud infrastructure',
                        salary_range: {
                            min: 75000,
                            max: 100000,
                            currency: 'USD'
                        },
                        required_certifications: [6, 8],
                        recommended_certifications: [7],
                        next_roles: [106]
                    },
                    {
                        id: 106,
                        title: 'DevOps Engineer',
                        description: 'Bridges development and operations',
                        salary_range: {
                            min: 90000,
                            max: 120000,
                            currency: 'USD'
                        },
                        required_certifications: [5, 7],
                        recommended_certifications: [],
                        next_roles: []
                    }
                ] as CareerRole[];

                setCertifications(mockCertifications);
                setRoles(mockRoles);

                // Extract unique vendors from mock data
                const uniqueVendors = Array.from(
                    new Set(mockCertifications.map(cert => cert.vendor))
                ) as string[];
                setVendors(uniqueVendors);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    // Prepare nodes and links for visualization
    useEffect(() => {
        if (roles.length === 0 || certifications.length === 0) return;

        // Create nodes for all certifications
        const certNodes: NodeData[] = certifications
            .filter(cert => {
                // Apply filters
                if (filterLevel !== 'all' && cert.level !== filterLevel) return false;
                if (filterVendor !== 'all' && cert.vendor !== filterVendor) return false;
                return true;
            })
            .map(cert => ({
                id: cert.id,
                name: cert.name,
                type: 'certification',
                level: cert.level,
                x: Math.random() * 900,  // Initialize with random positions
                y: Math.random() * 600,
                fx: null,
                fy: null
            }));

        // Create nodes for roles (always show all roles)
        const roleNodes: NodeData[] = roles.map(role => ({
            id: role.id,
            name: role.title,
            type: 'role',
            children: role.next_roles,
            x: Math.random() * 900,  // Initialize with random positions
            y: Math.random() * 600,
            fx: null,
            fy: null
        }));

        // Combine all nodes
        const allNodes = [...certNodes, ...roleNodes];

        // Create links
        let allLinks: LinkData[] = [];

        // Add prerequisite links between certifications
        certifications.forEach(cert => {
            // Only add links for nodes that passed the filter
            if (!certNodes.some(node => node.id === cert.id)) return;

            cert.prerequisites.forEach(prereqId => {
                // Only add links for nodes that passed the filter
                if (!certNodes.some(node => node.id === prereqId)) return;

                allLinks.push({
                    source: prereqId,
                    target: cert.id,
                    type: 'prerequisite'
                });
            });
        });

        // Add links between roles and certifications
        roles.forEach(role => {
            // Required certifications
            role.required_certifications.forEach(certId => {
                // Only add links for nodes that passed the filter
                if (!certNodes.some(node => node.id === certId)) return;

                allLinks.push({
                    source: certId,
                    target: role.id,
                    type: 'required'
                });
            });

            // Recommended certifications
            role.recommended_certifications.forEach(certId => {
                // Only add links for nodes that passed the filter
                if (!certNodes.some(node => node.id === certId)) return;

                allLinks.push({
                    source: certId,
                    target: role.id,
                    type: 'recommended'
                });
            });

            // Next roles
            role.next_roles.forEach(nextRoleId => {
                allLinks.push({
                    source: role.id,
                    target: nextRoleId,
                    type: 'next_role'
                });
            });
        });

        setNodes(allNodes);
        setLinks(allLinks);
    }, [roles, certifications, filterLevel, filterVendor]);

    // Create or update the visualization
    useEffect(() => {
        if (!svgRef.current || nodes.length === 0 || loading) return;

        const svg = d3.select(svgRef.current);
        const tooltip = d3.select(tooltipRef.current);

        // Clear previous visualization
        svg.selectAll('*').remove();

        const width = svg.node()?.getBoundingClientRect().width || 900;
        const height = svg.node()?.getBoundingClientRect().height || 600;

        // Create force simulation
        const simulation = d3.forceSimulation<NodeData & d3.SimulationNodeDatum>()
            .force('link', d3.forceLink<NodeData, LinkData>()
                .id(d => d.id.toString())
                .distance(d => d.type === 'next_role' ? 150 : 100)
                .strength(d => d.type === 'next_role' ? 0.5 : 0.2))
            .force('charge', d3.forceManyBody().strength(-200))
            .force('center', d3.forceCenter(width / 2, height / 2))
            .force('collide', d3.forceCollide().radius((d: d3.SimulationNodeDatum) => {
                // Type assertion to access our custom properties
                const node = d as NodeData;
                return node.type === 'role' ? 50 : 40;
            }))
            .force('x', d3.forceX(width / 2).strength(0.05))
            .force('y', d3.forceY(height / 2).strength(0.05))
            .velocityDecay(0.5);

        // Create links
        const link = svg.append('g')
            .attr('class', 'links')
            .selectAll('line')
            .data(links)
            .enter()
            .append('line')
            .attr('class', d => `link ${d.type}`)
            .attr('marker-end', d => `url(#arrow-${d.type})`);

        // Create arrow markers for different link types
        const defs = svg.append('defs');

        ['prerequisite', 'required', 'recommended', 'next_role'].forEach(type => {
            defs.append('marker')
                .attr('id', `arrow-${type}`)
                .attr('viewBox', '0 -5 10 10')
                .attr('refX', 25)
                .attr('refY', 0)
                .attr('markerWidth', 6)
                .attr('markerHeight', 6)
                .attr('orient', 'auto')
                .append('path')
                .attr('class', `arrow ${type}`)
                .attr('d', 'M0,-5L10,0L0,5');
        });

        // Create nodes
        const node = svg.append('g')
            .attr('class', 'nodes')
            .selectAll('g')
            .data(nodes)
            .enter()
            .append('g')
            .attr('class', d => {
                const classes = [d.type];
                if (d.level) classes.push(d.level as any);
                if (selectedRole !== null && (
                    (d.type === 'role' && d.id === selectedRole) ||
                    (roles.find(r => r.id === selectedRole)?.required_certifications.includes(d.id)) ||
                    (roles.find(r => r.id === selectedRole)?.recommended_certifications.includes(d.id))
                )) {
                    classes.push('highlighted' as any);
                }
                return classes.join(' ');
            });

        // Add circles for nodes
        node.append('circle')
            .attr('r', d => d.type === 'role' ? 35 : 25)
            .attr('class', d => {
                const classes = [d.type];
                if (d.level) classes.push(d.level as any);
                if (selectedRole !== null && (
                    (d.type === 'role' && d.id === selectedRole) ||
                    (roles.find(r => r.id === selectedRole)?.required_certifications.includes(d.id)) ||
                    (roles.find(r => r.id === selectedRole)?.recommended_certifications.includes(d.id))
                )) {
                    classes.push('highlighted' as any);
                }
                return classes.join(' ');
            });

        // Add icons to nodes (could be replaced with actual icons in a real app)
        node.append('text')
            .attr('text-anchor', 'middle')
            .attr('dy', 5)
            .attr('class', 'icon')
            .text(d => d.type === 'role' ? '👔' : '🏆');

        // Add titles to nodes
        node.append('text')
            .attr('dy', d => d.type === 'role' ? 55 : 45)
            .attr('text-anchor', 'middle')
            .attr('class', 'label')
            .text(d => {
                // Truncate long names
                return d.name.length > 20 ? d.name.substring(0, 17) + '...' : d.name;
            });

        // Add tooltips on hover
        node.on('mouseover', function (event, d) {
            const nodeElement = d3.select(this);
            nodeElement.classed('hover', true);

            tooltip.style('display', 'block')
                .style('left', (event.pageX + 15) + 'px')
                .style('top', (event.pageY - 30) + 'px');

            let content = '';

            if (d.type === 'role') {
                const role = roles.find(r => r.id === d.id);
                if (role) {
                    content = `
            <h3>${role.title}</h3>
            <p>${role.description}</p>
            <p><strong>Salary Range:</strong> ${role.salary_range.currency} ${role.salary_range.min.toLocaleString()} - ${role.salary_range.max.toLocaleString()}</p>
            <div class="tooltip-section">
              <h4>Required Certifications:</h4>
              <ul>
                ${role.required_certifications.map(certId => {
                        const cert = certifications.find(c => c.id === certId);
                        return cert ? `<li>${cert.name}</li>` : '';
                    }).join('')}
              </ul>
            </div>
            <div class="tooltip-section">
              <h4>Recommended Certifications:</h4>
              <ul>
                ${role.recommended_certifications.map(certId => {
                        const cert = certifications.find(c => c.id === certId);
                        return cert ? `<li>${cert.name}</li>` : '';
                    }).join('')}
              </ul>
            </div>
          `;
                }
            } else {
                const cert = certifications.find(c => c.id === d.id);
                if (cert) {
                    content = `
            <h3>${cert.name}</h3>
            <p>${cert.description}</p>
            <p><strong>Vendor:</strong> ${cert.vendor}</p>
            <p><strong>Level:</strong> ${cert.level.charAt(0).toUpperCase() + cert.level.slice(1)}</p>
            <div class="tooltip-section">
              <h4>Prerequisites:</h4>
              <ul>
                ${cert.prerequisites.map(prereqId => {
                        const prereq = certifications.find(c => c.id === prereqId);
                        return prereq ? `<li>${prereq.name}</li>` : '';
                    }).join('')}
              </ul>
            </div>
          `;
                }
            }

            tooltip.html(content);
        })
            .on('mouseout', function () {
                d3.select(this).classed('hover', false);
                tooltip.style('display', 'none');
            })
            .on('click', function (event, d) {
                if (d.type === 'role') {
                    setSelectedRole(selectedRole === d.id ? null : d.id);
                }
            });

        // Set up simulation
        simulation
            .nodes(nodes)
            .on('tick', ticked);

        (simulation.force('link') as d3.ForceLink<NodeData, LinkData>)
            .links(links);

        // Tick function for simulation
        function ticked() {
            link
                .attr('x1', d => (nodes.find(n => n.id === d.source) || { x: 0 }).x!)
                .attr('y1', d => (nodes.find(n => n.id === d.source) || { y: 0 }).y!)
                .attr('x2', d => (nodes.find(n => n.id === d.target) || { x: 0 }).x!)
                .attr('y2', d => (nodes.find(n => n.id === d.target) || { y: 0 }).y!);

            node
                .attr('transform', d => `translate(${d.x}, ${d.y})`);
        }

        // Drag functions
        function dragstarted(event: any, d: NodeData) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event: any, d: NodeData) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event: any, d: NodeData) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }

        // Reset the simulation when component is unmounted
        return () => {
            simulation.stop();
        };
    }, [nodes, links, selectedRole, certifications, roles]);

    return (
        <div className="career-path-container">
            <h1>Career Path Visualization</h1>

            <div className="visualization-container">
                <div className="filters">
                    <div className="filter-group">
                        <label htmlFor="level-filter">Certification Level:</label>
                        <select
                            id="level-filter"
                            value={filterLevel}
                            onChange={(e) => setFilterLevel(e.target.value)}
                        >
                            <option value="all">All Levels</option>
                            <option value="beginner">Beginner</option>
                            <option value="intermediate">Intermediate</option>
                            <option value="advanced">Advanced</option>
                            <option value="expert">Expert</option>
                        </select>
                    </div>

                    <div className="filter-group">
                        <label htmlFor="vendor-filter">Vendor:</label>
                        <select
                            id="vendor-filter"
                            value={filterVendor}
                            onChange={(e) => setFilterVendor(e.target.value)}
                        >
                            <option value="all">All Vendors</option>
                            {vendors.map(vendor => (
                                <option key={vendor} value={vendor}>{vendor}</option>
                            ))}
                        </select>
                    </div>
                </div>

                {loading ? (
                    <div className="loading-container">
                        <div className="loading-spinner"></div>
                        <p>Loading career path data...</p>
                    </div>
                ) : error ? (
                    <div className="error-container">
                        <p className="error-message">{error}</p>
                        <button onClick={() => window.location.reload()}>Try Again</button>
                    </div>
                ) : (
                    <>
                        <div className="legend">
                            <div className="legend-item">
                                <div className="legend-color role"></div>
                                <span>Job Role</span>
                            </div>
                            <div className="legend-item">
                                <div className="legend-color certification beginner"></div>
                                <span>Beginner Certification</span>
                            </div>
                            <div className="legend-item">
                                <div className="legend-color certification intermediate"></div>
                                <span>Intermediate Certification</span>
                            </div>
                            <div className="legend-item">
                                <div className="legend-color certification advanced"></div>
                                <span>Advanced Certification</span>
                            </div>
                            <div className="legend-item">
                                <div className="legend-color certification expert"></div>
                                <span>Expert Certification</span>
                            </div>
                        </div>

                        <div className="legend link-legend">
                            <div className="legend-item">
                                <div className="legend-line prerequisite"></div>
                                <span>Prerequisite</span>
                            </div>
                            <div className="legend-item">
                                <div className="legend-line required"></div>
                                <span>Required for Role</span>
                            </div>
                            <div className="legend-item">
                                <div className="legend-line recommended"></div>
                                <span>Recommended for Role</span>
                            </div>
                            <div className="legend-item">
                                <div className="legend-line next_role"></div>
                                <span>Career Progression</span>
                            </div>
                        </div>

                        <div className="tooltip" ref={tooltipRef}></div>

                        <svg ref={svgRef} className="career-path-svg" width="100%" height="600"></svg>
                    </>
                )}
            </div>

            <div className="visualization-instructions">
                <h3>How to Use This Visualization</h3>
                <ul>
                    <li>Drag nodes to reposition them for better viewing</li>
                    <li>Hover over a node to see detailed information</li>
                    <li>Click on a job role to highlight its required and recommended certifications</li>
                    <li>Use the filters above to show certifications by level or vendor</li>
                </ul>
            </div>
        </div>
    );
};

export default CareerPathVisualization; 