/**
 * Type definitions for certifications
 */

/**
 * Certification types enum
 */
export enum CertificationType {
  SECURITY = "security",
  CLOUD = "cloud",
  NETWORKING = "networking",
  DEVELOPMENT = "development",
  DEVOPS = "devops",
  OTHER = "other"
}

/**
 * Certification level enum
 */
export enum CertificationLevel {
  BEGINNER = "beginner",
  INTERMEDIATE = "intermediate",
  ADVANCED = "advanced",
  EXPERT = "expert"
}

/**
 * Security domains
 */
export enum SecurityDomain {
  SECURITY_ENGINEERING = "Security Engineering",
  DEFENSIVE_SECURITY = "Defensive Security",
  SECURITY_MANAGEMENT = "Security Management",
  NETWORK_SECURITY = "Network Security",
  IDENTITY_ACCESS_MANAGEMENT = "Identity & Access Management",
  ASSET_SECURITY = "Asset Security",
  SECURITY_TESTING = "Security Testing",
  OFFENSIVE_SECURITY = "Offensive Security"
}

/**
 * Domain interface
 */
export interface IDomain {
  id: number;
  name: string;
  description?: string;
}

/**
 * Base certification interface
 */
export interface ICertification {
  id: number;
  name: string;
  provider: string;
  description?: string;
  type: CertificationType;
  level: CertificationLevel;
  // Primary domain is stored directly on the certification
  domain: SecurityDomain;
  // Associated domains (many-to-many)
  domains?: IDomain[];
  exam_code?: string;
  price?: number;
  duration_minutes?: number;
  passing_score?: number;
  prerequisites?: string[];
  skills_covered?: string[];
  url?: string;
  created_at: string;
  updated_at?: string;
}

/**
 * Certification filter parameters
 */
export interface ICertificationFilter {
  provider?: string;
  type?: CertificationType;
  level?: CertificationLevel;
  domain?: SecurityDomain;  // Added domain filter
  search?: string;
  min_price?: number;
  max_price?: number;
  page?: number;
  page_size?: number;
}

/**
 * Certification list response
 */
export interface ICertificationListResponse {
  certifications: ICertification[];
  total: number;
  page: number;
  page_size: number;
}

/**
 * User certification interface
 */
export interface IUserCertification {
  id: number;
  certification_id: number;
  name: string;
  code: string;
  organization: string;
  date_earned: string;
  expiration_date?: string;
  verification_url?: string;
  certificate_image_url?: string;
  status: 'active' | 'expired' | 'revoked';
}

/**
 * Certification comparison interface
 */
export interface ICertificationComparison {
  certifications: ICertification[];
  commonSkills: string[];
  uniqueSkills: Record<number, string[]>;
  commonDomains: SecurityDomain[];
  uniqueDomains: Record<number, SecurityDomain[]>;
  prerequisites: Record<number, string[]>;
}

/**
 * Certification ROI information
 */
export interface ICertificationROI {
  certification_id: number;
  cost: number;
  potential_salary_increase: number;
  average_time_to_recoup: number;
  job_opportunities: number;
  market_demand_score: number;
}

/**
 * Study time estimate
 */
export interface IStudyTimeEstimate {
  certification_id: number;
  total_hours: number;
  weekly_breakdown: Array<{
    week: number;
    hours: number;
    topics: string[];
  }>;
  completion_date: string;
  difficulty_score: number;
} 