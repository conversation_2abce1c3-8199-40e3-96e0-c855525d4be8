.cert-explorer-container {
  padding: 20px;
}

.cert-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 18px;
  color: #6c757d;
}

.cert-filters {
  margin-bottom: 25px;
}

.search-bar {
  margin-bottom: 15px;
}

.search-bar input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 16px;
}

.filter-controls {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 200px;
}

.filter-group label {
  margin-bottom: 5px;
  font-weight: bold;
  color: #495057;
}

.filter-group select {
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.cert-explorer-content {
  display: flex;
  gap: 30px;
}

.cert-list {
  flex: 1;
}

.cert-list ul {
  list-style-type: none;
  padding: 0;
}

.cert-item {
  padding: 15px;
  margin-bottom: 15px;
  background-color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 4px solid transparent;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cert-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.cert-item.selected {
  border-left-color: #007bff;
  background-color: #f8f9fa;
}

.cert-item.beginner {
  border-left-color: #28a745;
}

.cert-item.intermediate {
  border-left-color: #fd7e14;
}

.cert-item.advanced {
  border-left-color: #dc3545;
}

.cert-item.expert {
  border-left-color: #6f42c1;
}

.cert-item h4 {
  margin: 0 0 8px 0;
  color: #343a40;
}

.cert-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.cert-provider {
  color: #6c757d;
  font-size: 14px;
}

.cert-level {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.cert-level.beginner {
  background-color: #d4edda;
  color: #28a745;
}

.cert-level.intermediate {
  background-color: #fff3cd;
  color: #fd7e14;
}

.cert-level.advanced {
  background-color: #f8d7da;
  color: #dc3545;
}

.cert-level.expert {
  background-color: #e2d9f3;
  color: #6f42c1;
}

.cert-brief {
  color: #495057;
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 1.5;
}

.cert-popularity {
  display: flex;
  align-items: center;
  gap: 10px;
}

.popularity-label {
  font-size: 12px;
  color: #6c757d;
}

.popularity-bar {
  flex-grow: 1;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.popularity-fill {
  height: 100%;
  background-color: #007bff;
}

.cert-details {
  flex: 2;
  background: #f8f9fa;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.select-cert-prompt {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #6c757d;
  font-style: italic;
}

.cert-header {
  margin-bottom: 20px;
}

.cert-header h3 {
  margin: 0 0 10px 0;
  color: #343a40;
}

.cert-badges {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.level-badge, .provider-badge, .category-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.level-badge.beginner {
  background-color: #d4edda;
  color: #28a745;
}

.level-badge.intermediate {
  background-color: #fff3cd;
  color: #fd7e14;
}

.level-badge.advanced {
  background-color: #f8d7da;
  color: #dc3545;
}

.level-badge.expert {
  background-color: #e2d9f3;
  color: #6f42c1;
}

.provider-badge {
  background-color: #e7f5ff;
  color: #007bff;
}

.category-badge {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

.cert-description {
  margin-bottom: 25px;
  line-height: 1.6;
}

.cert-requirements {
  margin-bottom: 25px;
}

.cert-requirements h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #495057;
}

.cert-requirements ul {
  margin: 0;
  padding-left: 20px;
}

.cert-requirements li {
  margin-bottom: 8px;
}

.cert-exam-details {
  margin-bottom: 25px;
}

.cert-exam-details h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #495057;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.detail-item {
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.detail-label {
  display: block;
  font-size: 12px;
  font-weight: bold;
  color: #6c757d;
  margin-bottom: 5px;
}

.detail-value {
  font-size: 16px;
  color: #343a40;
}

.cert-additional-info {
  margin-bottom: 30px;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.info-item {
  flex: 1;
  min-width: 200px;
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-label {
  display: block;
  font-size: 12px;
  font-weight: bold;
  color: #6c757d;
  margin-bottom: 5px;
}

.info-value {
  font-size: 16px;
  color: #343a40;
}

.cert-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.cert-action-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cert-action-btn.prepare {
  background-color: #007bff;
  color: white;
}

.cert-action-btn.prepare:hover {
  background-color: #0069d9;
}

.cert-action-btn.calculator {
  background-color: #6c757d;
  color: white;
}

.cert-action-btn.calculator:hover {
  background-color: #5a6268;
}

.no-certs-found {
  padding: 30px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
} 