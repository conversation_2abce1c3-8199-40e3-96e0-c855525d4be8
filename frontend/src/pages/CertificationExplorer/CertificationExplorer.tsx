import React, { useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import './styles.css';

// Import our components
import CertificationComparison from './components/CertificationComparison';
import CertificationDetail from './components/CertificationDetail';
import CertificationFilter from './components/CertificationFilter';
import CertificationList from './components/CertificationList';

// Import our hooks
import { useCertificationComparison } from './hooks/useCertificationComparison';
import { useCertifications } from './hooks/useCertifications';

// Enable detailed error logging
const enableDetailedErrorLogging = () => {
    // Save the original console.error function
    const originalConsoleError = console.error;

    // Override console.error to provide more details
    console.error = (...args) => {
        // Call the original function first
        originalConsoleError(...args);

        // Add additional debugging info
        if (args[0] && typeof args[0] === 'string') {
            if (args[0].includes('TypeScript error')) {
                console.log('TypeScript error detected. Details:', JSON.stringify(args.slice(1)));
            }
        }

        // Add stack trace for non-Error objects
        if (args[0] && !(args[0] instanceof Error)) {
            console.log('Error context:', new Error().stack);
        }
    };

    // Add window error handler
    window.addEventListener('error', (event) => {
        console.log('Global error caught:', {
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            error: event.error
        });
    });

    // Add unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
        console.log('Unhandled promise rejection:', event.reason);
    });
};

const CertificationExplorer: React.FC = () => {
    const { translate } = useLanguage();

    // Enable detailed error logging when component mounts
    useEffect(() => {
        enableDetailedErrorLogging();
    }, []);

    // Main certifications data and logic
    const {
        certifications,
        loading,
        error,
        page,
        pageSize,
        totalCertifications,
        filters,
        providers,
        selectedCertification,
        relatedCertifications,
        handleFilterChange,
        handleCertificationSelect,
        handleBackToList,
        handlePageChange,
        handlePageSizeChange,
    } = useCertifications();

    // Comparison functionality
    const {
        certificationsToCompare,
        comparisonData,
        loading: comparisonLoading,
        error: comparisonError,
        isComparing,
        addCertificationToCompare,
        removeCertificationFromCompare,
        clearComparison,
        startComparison,
        cancelComparison
    } = useCertificationComparison();

    return (
        <div className="certification-explorer-container" data-testid="certification-explorer">
            <h1 data-testid="explorer-title">{translate('certifications.title', 'Certification Explorer')}</h1>
            <div className="certification-description-container">
                <p className="certification-description">
                    {translate('certifications.description', 'Explore and compare security certifications to find the right path for your career.')}
                </p>
            </div>

            {certifications.length === 0 && !loading && !error && (
                <div className="no-data-message">
                    <p>{translate('certifications.no_data', 'No certifications found. Try adjusting your filters or search criteria.')}</p>
                </div>
            )}

            {/* Display any errors */}
            {error && (
                <div className="error-message" role="alert" data-testid="error-message">
                    {error}
                </div>
            )}

            {comparisonError && (
                <div className="error-message" role="alert">
                    {comparisonError}
                </div>
            )}

            {/* Loading state */}
            {loading && (
                <div className="loading-state" data-testid="loading-state">
                    <div className="spinner"></div>
                    <p>Loading certifications...</p>
                </div>
            )}

            {/* Comparison mode UI */}
            {isComparing && comparisonData && (
                <CertificationComparison
                    comparisonData={comparisonData}
                    onClose={cancelComparison}
                    onCertificationSelect={handleCertificationSelect}
                />
            )}

            {/* Comparison selection bar - visible when items are selected for comparison but not in comparison mode */}
            {certificationsToCompare.length > 0 && !isComparing && (
                <div className="comparison-selection-bar">
                    <div className="comparison-items">
                        <span className="comparison-label">
                            {translate('certifications.comparison.selected', 'Selected for comparison {{count}}/4:', {
                                count: certificationsToCompare.length
                            })}
                        </span>
                        {certificationsToCompare.map(cert => (
                            <div key={cert.id} className="comparison-item">
                                <span>{cert.name}</span>
                                <button
                                    onClick={() => removeCertificationFromCompare(cert.id)}
                                    className="remove-comparison-item"
                                    aria-label={translate('certifications.comparison.remove', 'Remove {{name}} from comparison', {
                                        name: cert.name
                                    })}
                                >
                                    &times;
                                </button>
                            </div>
                        ))}
                    </div>
                    <div className="comparison-actions">
                        <button
                            className="action-button compare-button"
                            onClick={startComparison}
                            disabled={certificationsToCompare.length < 2 || comparisonLoading}
                        >
                            {comparisonLoading
                                ? translate('certifications.comparison.loading', 'Loading...')
                                : translate('certifications.comparison.compare', 'Compare')}
                        </button>
                        <button
                            className="action-button cancel-button"
                            onClick={clearComparison}
                        >
                            {translate('certifications.comparison.clear', 'Clear')}
                        </button>
                    </div>
                </div>
            )}

            {/* Main content area */}
            <div className="certification-explorer-content">
                {/* Filters Panel */}
                <CertificationFilter
                    filters={filters}
                    onFilterChange={handleFilterChange}
                    providers={providers}
                    isLoading={loading}
                />

                {/* Main Content Area */}
                <div className="main-content">
                    {selectedCertification ? (
                        <CertificationDetail
                            certification={selectedCertification}
                            relatedCertifications={relatedCertifications}
                            onBack={handleBackToList}
                            onRelatedCertificationSelect={handleCertificationSelect}
                            onAddToComparison={addCertificationToCompare}
                            isInComparison={certificationsToCompare.some(cert => cert.id === selectedCertification.id)}
                        />
                    ) : (
                        <CertificationList
                            certifications={certifications}
                            onSelectCertification={handleCertificationSelect}
                            totalCertifications={totalCertifications}
                            page={page}
                            pageSize={pageSize}
                            onPageChange={handlePageChange}
                            onPageSizeChange={handlePageSizeChange}
                            isLoading={loading}
                            onAddToComparison={addCertificationToCompare}
                            certificationsToCompare={certificationsToCompare}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default CertificationExplorer; 