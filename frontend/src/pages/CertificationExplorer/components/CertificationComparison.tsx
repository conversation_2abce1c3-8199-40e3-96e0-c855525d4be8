import React from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';
import { DOMAIN_COLORS, LEVEL_COLORS, TYPE_COLORS } from '../constants';
import { ICertification, ICertificationComparison } from '../types/certification.types';
import './CertificationComparison.css';

interface CertificationComparisonProps {
    comparisonData: ICertificationComparison;
    onClose: () => void;
    onCertificationSelect: (certification: ICertification) => void;
}

const CertificationComparison: React.FC<CertificationComparisonProps> = ({
    comparisonData,
    onClose,
    onCertificationSelect
}) => {
    const { translate } = useLanguage();
    const { certifications, commonSkills, uniqueSkills, commonDomains, uniqueDomains, prerequisites } = comparisonData;

    // Helper to format price
    const formatPrice = (price: number | null | undefined): string => {
        if (price === null || price === undefined) {
            return translate('certifications.price.not_available', 'Not available');
        }
        return `$${price.toFixed(2)}`;
    };

    // Helper to format duration
    const formatDuration = (minutes: number | null | undefined): string => {
        if (minutes === null || minutes === undefined) {
            return translate('certifications.duration.not_available', 'Not available');
        }
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return hours > 0
            ? translate('certifications.duration.hours_minutes', '{{hours}}h {{minutes}}m', { hours, minutes: mins })
            : translate('certifications.duration.minutes', '{{minutes}}m', { minutes: mins });
    };

    return (
        <div className="certification-comparison">
            <div className="comparison-header">
                <h2>{translate('certifications.comparison.title', 'Certification Comparison')}</h2>
                <button
                    className="close-comparison-btn"
                    onClick={onClose}
                    aria-label={translate('certifications.comparison.close_aria', 'Close comparison')}
                >
                    &times;
                </button>
            </div>

            <div className="comparison-content">
                <div className="comparison-table-container">
                    <table className="comparison-table">
                        <thead>
                            <tr>
                                <th>{translate('certifications.comparison.feature', 'Feature')}</th>
                                {certifications.map(cert => (
                                    <th key={cert.id} onClick={() => onCertificationSelect(cert)} className="cert-header">
                                        {cert.name}
                                    </th>
                                ))}
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{translate('certifications.comparison.provider', 'Provider')}</td>
                                {certifications.map(cert => (
                                    <td key={cert.id}>{cert.provider}</td>
                                ))}
                            </tr>
                            <tr>
                                <td>{translate('certifications.comparison.type', 'Type')}</td>
                                {certifications.map(cert => (
                                    <td key={cert.id}>
                                        <span
                                            className={`type-badge type-${cert.type}`}
                                            style={{ backgroundColor: TYPE_COLORS[cert.type] || '#95a5a6' }}
                                        >
                                            {translate(`certifications.type.${cert.type}`, cert.type)}
                                        </span>
                                    </td>
                                ))}
                            </tr>
                            <tr>
                                <td>{translate('certifications.comparison.level', 'Level')}</td>
                                {certifications.map(cert => (
                                    <td key={cert.id}>
                                        <span
                                            className={`level-badge level-${cert.level}`}
                                            style={{ backgroundColor: LEVEL_COLORS[cert.level] || '#95a5a6' }}
                                        >
                                            {translate(`certifications.level.${cert.level}`, cert.level)}
                                        </span>
                                    </td>
                                ))}
                            </tr>
                            <tr>
                                <td>{translate('certifications.comparison.domain', 'Domain')}</td>
                                {certifications.map(cert => (
                                    <td key={cert.id}>
                                        <span
                                            className="domain-badge"
                                            style={{ backgroundColor: DOMAIN_COLORS[cert.domain] || '#95a5a6' }}
                                        >
                                            {translate(`certifications.domain.${cert.domain}`, cert.domain)}
                                        </span>
                                    </td>
                                ))}
                            </tr>
                            <tr>
                                <td>{translate('certifications.comparison.price', 'Price')}</td>
                                {certifications.map(cert => (
                                    <td key={cert.id}>{formatPrice(cert.price)}</td>
                                ))}
                            </tr>
                            <tr>
                                <td>{translate('certifications.comparison.duration', 'Duration')}</td>
                                {certifications.map(cert => (
                                    <td key={cert.id}>{formatDuration(cert.duration_minutes)}</td>
                                ))}
                            </tr>
                            <tr>
                                <td>{translate('certifications.comparison.passing_score', 'Passing Score')}</td>
                                {certifications.map(cert => (
                                    <td key={cert.id}>
                                        {cert.passing_score ? `${cert.passing_score}%` : translate('certifications.not_available', 'N/A')}
                                    </td>
                                ))}
                            </tr>
                            <tr>
                                <td>{translate('certifications.comparison.prerequisites', 'Prerequisites')}</td>
                                {certifications.map(cert => (
                                    <td key={cert.id}>
                                        {prerequisites[cert.id] && prerequisites[cert.id].length > 0 ? (
                                            <ul className="prerequisite-list">
                                                {prerequisites[cert.id].map((prereq, idx) => (
                                                    <li key={idx}>{prereq}</li>
                                                ))}
                                            </ul>
                                        ) : (
                                            translate('certifications.comparison.no_prerequisites', 'None')
                                        )}
                                    </td>
                                ))}
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div className="comparison-skills">
                    <h3>{translate('certifications.comparison.skills_title', 'Skills Coverage')}</h3>

                    {commonSkills.length > 0 && (
                        <div className="common-skills">
                            <h4>{translate('certifications.comparison.common_skills', 'Common Skills')}</h4>
                            <ul className="skills-list">
                                {commonSkills.map((skill, idx) => (
                                    <li key={idx} className="common-skill">{skill}</li>
                                ))}
                            </ul>
                        </div>
                    )}

                    <div className="unique-skills">
                        <h4>{translate('certifications.comparison.unique_skills', 'Unique Skills')}</h4>
                        {Object.keys(uniqueSkills).length > 0 ? (
                            <div className="unique-skills-grid">
                                {certifications.map(cert => (
                                    <div key={cert.id} className="unique-skills-column">
                                        <h5>{cert.name}</h5>
                                        {uniqueSkills[cert.id] && uniqueSkills[cert.id].length > 0 ? (
                                            <ul className="skills-list">
                                                {uniqueSkills[cert.id].map((skill, idx) => (
                                                    <li key={idx} className="unique-skill">{skill}</li>
                                                ))}
                                            </ul>
                                        ) : (
                                            <p className="no-unique-skills">
                                                {translate('certifications.comparison.no_unique_skills', 'No unique skills')}
                                            </p>
                                        )}
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <p className="no-unique-skills-found">
                                {translate('certifications.comparison.no_unique_skills_found', 'No unique skills found')}
                            </p>
                        )}
                    </div>
                </div>

                <div className="comparison-actions">
                    <button className="action-button close-button" onClick={onClose}>
                        {translate('certifications.comparison.close', 'Close Comparison')}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default CertificationComparison; 