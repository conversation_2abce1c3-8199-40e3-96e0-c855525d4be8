import React from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';
import { PAGINATION } from '../constants';
import { ICertification } from '../types/certification.types';
import CertificationCard from './CertificationCard';

interface CertificationListProps {
    certifications: ICertification[];
    onSelectCertification: (certification: ICertification) => void;
    totalCertifications: number;
    page: number;
    pageSize: number;
    onPageChange: (page: number) => void;
    onPageSizeChange: (pageSize: number) => void;
    isLoading?: boolean;
    onAddToComparison: (certification: ICertification) => void;
    certificationsToCompare: ICertification[];
}

const CertificationList: React.FC<CertificationListProps> = ({
    certifications,
    onSelectCertification,
    totalCertifications,
    page,
    pageSize,
    onPageChange,
    onPageSizeChange,
    isLoading = false,
    onAddToComparison,
    certificationsToCompare
}) => {
    const { translate } = useLanguage();

    // Calculate total pages based on total items and page size
    const totalPages = Math.ceil(totalCertifications / pageSize);

    // Check if a certification is already in comparison
    const isInComparison = (certId: number): boolean => {
        return certificationsToCompare.some(cert => cert.id === certId);
    };

    // Generate pagination controls
    const renderPagination = () => {
        return (
            <div className="pagination" data-testid="pagination-controls">
                <button
                    onClick={() => onPageChange(page - 1)}
                    disabled={page === 1 || isLoading}
                    className="page-nav prev"
                    aria-label={translate('certifications.pagination.previous', 'Previous page')}
                >
                    &lt;
                </button>

                <span className="page-info" data-testid="current-page">
                    {translate('certifications.pagination.page_of', 'Page {{page}} of {{total}}', { page, total: totalPages })}
                </span>

                <button
                    onClick={() => onPageChange(page + 1)}
                    disabled={page === totalPages || isLoading}
                    className="page-nav next"
                    aria-label={translate('certifications.pagination.next', 'Next page')}
                >
                    &gt;
                </button>

                <select
                    value={pageSize}
                    onChange={(e) => onPageSizeChange(Number(e.target.value))}
                    disabled={isLoading}
                    aria-label={translate('certifications.pagination.items_per_page', 'Items per page')}
                >
                    {PAGINATION.PAGE_SIZE_OPTIONS.map(size => (
                        <option key={size} value={size}>
                            {size} {translate('certifications.pagination.per_page', 'per page')}
                        </option>
                    ))}
                </select>
            </div>
        );
    };

    return (
        <div className="certification-list-container">
            <h2>
                {translate('certifications.list.title', 'Available Certifications')}
                {totalCertifications > 0 && (
                    <span className="certification-count">
                        {translate('certifications.list.count', '({{count}})', { count: totalCertifications })}
                    </span>
                )}
            </h2>

            {isLoading ? (
                <div className="loading-placeholder">
                    <div className="spinner"></div>
                    <p>{translate('certifications.list.loading', 'Loading certifications...')}</p>
                </div>
            ) : certifications.length === 0 ? (
                <div className="no-results">
                    <p>{translate('certifications.list.no_results', 'No certifications found matching your criteria.')}</p>
                </div>
            ) : (
                <>
                    <div className="certification-count">
                        {translate('certifications.list.showing', 'Showing {{showing}} of {{total}} certifications', {
                            showing: certifications.length,
                            total: totalCertifications
                        })}
                    </div>

                    <div className="certification-grid" data-testid="certifications-grid">
                        {certifications.map(cert => (
                            <CertificationCard
                                key={cert.id}
                                certification={cert}
                                onClick={() => onSelectCertification(cert)}
                                onAddToComparison={() => onAddToComparison(cert)}
                                isInComparison={isInComparison(cert.id)}
                            />
                        ))}
                    </div>

                    {renderPagination()}
                </>
            )}
        </div>
    );
};

export default CertificationList; 