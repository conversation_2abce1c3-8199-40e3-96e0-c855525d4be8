import React from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';
import { DOMAIN_COLORS, LEVEL_COLORS, TYPE_COLORS } from '../constants';
import { ICertification } from '../types/certification.types';

interface CertificationCardProps {
    certification: ICertification;
    onClick: () => void;
    onAddToComparison: () => void;
    isInComparison: boolean;
}

const CertificationCard: React.FC<CertificationCardProps> = ({
    certification,
    onClick,
    onAddToComparison,
    isInComparison
}) => {
    const { translate } = useLanguage();

    const formatPrice = (price: number | null | undefined): string => {
        if (price === null || price === undefined) {
            return translate('certifications.price.not_available', 'Not available');
        }
        return `$${price.toFixed(2)}`;
    };

    // Prevent the click from bubbling to the card when clicking the compare button
    const handleCompareClick = (e: React.MouseEvent) => {
        e.stopPropagation();
        onAddToComparison();
    };

    return (
        <div
            className="certification-card"
            onClick={onClick}
            data-testid={`certification-card-${certification.id}`}
        >
            <div className="certification-card-header">
                <h3 className="certification-title" title={certification.name} data-testid={`certification-title-${certification.id}`}>
                    {certification.name}
                </h3>
                <span className="certification-provider" data-testid={`certification-provider-${certification.id}`}>{certification.provider}</span>
            </div>

            <div className="certification-meta">
                <span
                    className={`certification-type type-${certification.type}`}
                    style={{ backgroundColor: TYPE_COLORS[certification.type] || '#95a5a6' }}
                    data-testid={`certification-type-${certification.id}`}
                >
                    {translate(`certifications.type.${certification.type}`, certification.type)}
                </span>

                <span
                    className={`certification-level level-${certification.level}`}
                    style={{ backgroundColor: LEVEL_COLORS[certification.level] || '#95a5a6' }}
                    data-testid={`certification-level-${certification.id}`}
                >
                    {translate(`certifications.level.${certification.level}`, certification.level)}
                </span>

                <span
                    className="certification-domain"
                    style={{ backgroundColor: DOMAIN_COLORS[certification.domain] || '#95a5a6' }}
                    data-testid={`certification-domain-${certification.id}`}
                >
                    {translate(`certifications.domain.${certification.domain}`, certification.domain)}
                </span>
            </div>

            <div className="certification-description">
                <p data-testid={`certification-description-${certification.id}`}>
                    {certification.description
                        ? certification.description.length > 100
                            ? `${certification.description.substring(0, 100)}...`
                            : certification.description
                        : translate('certifications.card.no_description', 'No description available')}
                </p>
            </div>

            <div className="certification-footer">
                <div className="certification-price" data-testid={`certification-price-${certification.id}`}>
                    {formatPrice(certification.price)}
                </div>

                <button
                    className={`compare-button ${isInComparison ? 'in-comparison' : ''}`}
                    onClick={handleCompareClick}
                    disabled={isInComparison}
                    data-testid={`compare-button-${certification.id}`}
                    aria-label={isInComparison
                        ? translate('certifications.card.added_to_comparison_aria', 'Already added to comparison')
                        : translate('certifications.card.add_to_comparison_aria', 'Add to comparison')}
                >
                    {isInComparison
                        ? translate('certifications.card.added', 'Added')
                        : translate('certifications.card.compare', 'Compare')}
                </button>
            </div>
        </div>
    );
};

export default CertificationCard; 