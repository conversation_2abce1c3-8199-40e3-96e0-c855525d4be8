import React from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';
import { DOMAIN_COLORS, LEVEL_COLORS, TYPE_COLORS } from '../constants';
import { ICertification } from '../types/certification.types';

interface CertificationDetailProps {
    certification: ICertification;
    relatedCertifications: ICertification[];
    onBack: () => void;
    onRelatedCertificationSelect: (certification: ICertification) => void;
    onAddToComparison: (certification: ICertification) => void;
    isInComparison: boolean;
}

const CertificationDetail: React.FC<CertificationDetailProps> = ({
    certification,
    relatedCertifications,
    onBack,
    onRelatedCertificationSelect,
    onAddToComparison,
    isInComparison
}) => {
    const { translate } = useLanguage();

    const formatPrice = (price: number | null | undefined): string => {
        if (price === null || price === undefined) {
            return translate('certifications.price.not_available', 'Not available');
        }
        return `$${price.toFixed(2)}`;
    };

    const formatDuration = (minutes: number | null | undefined): string => {
        if (minutes === null || minutes === undefined) {
            return translate('certifications.duration.not_available', 'Not available');
        }
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return hours > 0
            ? translate('certifications.duration.hours_minutes', '{{hours}}h {{minutes}}m', { hours, minutes: mins })
            : translate('certifications.duration.minutes', '{{minutes}}m', { minutes: mins });
    };

    return (
        <div className="certification-detail">
            <button
                className="back-button"
                onClick={onBack}
                aria-label={translate('certifications.detail.back_aria', 'Back to certification list')}
            >
                ← {translate('certifications.detail.back', 'Back to List')}
            </button>

            <div className="certification-detail-header">
                <h2>{certification.name}</h2>
                <div className="certification-detail-provider">{certification.provider}</div>
            </div>

            <div className="certification-meta">
                <span
                    className={`certification-type type-${certification.type}`}
                    style={{ backgroundColor: TYPE_COLORS[certification.type] || '#95a5a6' }}
                >
                    {translate(`certifications.type.${certification.type}`, certification.type)}
                </span>
                <span
                    className={`certification-level level-${certification.level}`}
                    style={{ backgroundColor: LEVEL_COLORS[certification.level] || '#95a5a6' }}
                >
                    {translate(`certifications.level.${certification.level}`, certification.level)}
                </span>
                <span
                    className="certification-domain"
                    style={{ backgroundColor: DOMAIN_COLORS[certification.domain] || '#95a5a6' }}
                >
                    {translate(`certifications.domain.${certification.domain}`, certification.domain)}
                </span>
            </div>

            <div className="certification-description">
                <p>{certification.description || translate('certifications.description.not_available', 'No description available')}</p>
            </div>

            <div className="certification-detail-grid">
                <div className="info-panel">
                    <h3>{translate('certifications.detail.exam_details', 'Exam Details')}</h3>
                    <div className="info-item">
                        <div className="info-label">{translate('certifications.detail.exam_code', 'Exam Code')}:</div>
                        <div className="info-value">{certification.exam_code || translate('certifications.detail.not_available', 'Not available')}</div>
                    </div>
                    <div className="info-item">
                        <div className="info-label">{translate('certifications.detail.price', 'Price')}:</div>
                        <div className="info-value">{formatPrice(certification.price)}</div>
                    </div>
                    <div className="info-item">
                        <div className="info-label">{translate('certifications.detail.duration', 'Duration')}:</div>
                        <div className="info-value">{formatDuration(certification.duration_minutes)}</div>
                    </div>
                    <div className="info-item">
                        <div className="info-label">{translate('certifications.detail.passing_score', 'Passing Score')}:</div>
                        <div className="info-value">{certification.passing_score ? `${certification.passing_score}%` : translate('certifications.detail.not_available', 'Not available')}</div>
                    </div>
                </div>

                <div className="skills-panel">
                    <h3>{translate('certifications.detail.skills_covered', 'Skills Covered')}</h3>
                    {certification.skills_covered && certification.skills_covered.length > 0 ? (
                        <ul className="skills-list">
                            {certification.skills_covered.map((skill, index) => (
                                <li key={index}>{skill}</li>
                            ))}
                        </ul>
                    ) : (
                        <p className="no-data">{translate('certifications.detail.no_skills', 'No skills information available')}</p>
                    )}
                </div>

                {certification.prerequisites && certification.prerequisites.length > 0 && (
                    <div className="prerequisites-panel">
                        <h3>{translate('certifications.detail.prerequisites', 'Prerequisites')}</h3>
                        <ul className="prerequisites-list">
                            {certification.prerequisites.map((prerequisite, index) => (
                                <li key={index}>{prerequisite}</li>
                            ))}
                        </ul>
                    </div>
                )}

                {certification.url && (
                    <div className="links-panel">
                        <h3>{translate('certifications.detail.links', 'Links')}</h3>
                        <a
                            href={certification.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="official-link"
                        >
                            {translate('certifications.detail.visit_official', 'Visit Official Page')}
                        </a>
                    </div>
                )}
            </div>

            <div className="certification-actions">
                <button
                    className="cert-action-btn prepare"
                    onClick={() => window.location.href = `/study-time?certification=${certification.id}`}
                >
                    {translate('certifications.detail.prepare', 'Prepare for this Certification')}
                </button>
                <button
                    className="cert-action-btn calculator"
                    onClick={() => window.location.href = `/cost-calculator?certification=${certification.id}`}
                >
                    {translate('certifications.detail.calculate_cost', 'Calculate Cost')}
                </button>
                <button
                    className={`cert-action-btn ${isInComparison ? 'in-comparison' : 'compare'}`}
                    onClick={() => onAddToComparison(certification)}
                    disabled={isInComparison}
                >
                    {isInComparison
                        ? translate('certifications.detail.added_to_comparison', 'Added to Comparison')
                        : translate('certifications.detail.add_to_comparison', 'Add to Comparison')}
                </button>
            </div>

            {/* Related Certifications */}
            {relatedCertifications.length > 0 && (
                <div className="related-certifications">
                    <h3>{translate('certifications.detail.related', 'Related Certifications')}</h3>
                    <div className="related-list">
                        {relatedCertifications.map(cert => (
                            <div
                                key={cert.id}
                                className="related-item"
                                onClick={() => onRelatedCertificationSelect(cert)}
                            >
                                <h4>{cert.name}</h4>
                                <div className="related-meta">
                                    <span className="related-provider">{cert.provider}</span>
                                    <span
                                        className={`related-level level-${cert.level}`}
                                        style={{ backgroundColor: LEVEL_COLORS[cert.level] || '#95a5a6' }}
                                    >
                                        {translate(`certifications.level.${cert.level}`, cert.level)}
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default CertificationDetail; 