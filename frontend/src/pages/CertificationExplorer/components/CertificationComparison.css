.certification-comparison {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin: 20px 0;
    overflow: hidden;
    width: 100%;
}

.comparison-header {
    align-items: center;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    padding: 16px 20px;
}

.comparison-header h2 {
    color: #343a40;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.close-comparison-btn {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    font-size: 1.5rem;
    line-height: 1;
    padding: 4px 8px;
    transition: color 0.2s;
}

.close-comparison-btn:hover {
    color: #dc3545;
}

.comparison-content {
    padding: 20px;
}

.comparison-table-container {
    margin-bottom: 30px;
    overflow-x: auto;
}

.comparison-table {
    border-collapse: collapse;
    width: 100%;
}

.comparison-table th,
.comparison-table td {
    border: 1px solid #dee2e6;
    padding: 12px;
    text-align: center;
    vertical-align: middle;
}

.comparison-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    position: sticky;
    top: 0;
}

.comparison-table th:first-child,
.comparison-table td:first-child {
    background-color: #f8f9fa;
    font-weight: 600;
    left: 0;
    position: sticky;
    text-align: left;
    z-index: 1;
}

.cert-header {
    cursor: pointer;
    transition: background-color 0.2s;
}

.cert-header:hover {
    background-color: #e9ecef;
}

.type-badge,
.level-badge,
.domain-badge {
    border-radius: 4px;
    color: white;
    display: inline-block;
    font-size: 0.85rem;
    font-weight: 500;
    padding: 4px 8px;
}

.prerequisite-list {
    list-style-type: none;
    margin: 0;
    padding: 0;
    text-align: left;
}

.prerequisite-list li {
    margin-bottom: 4px;
}

.comparison-skills {
    margin-bottom: 30px;
}

.comparison-skills h3 {
    border-bottom: 1px solid #dee2e6;
    color: #343a40;
    font-size: 1.3rem;
    margin-bottom: 20px;
    padding-bottom: 10px;
}

.common-skills,
.unique-skills {
    margin-bottom: 20px;
}

.comparison-skills h4 {
    color: #495057;
    font-size: 1.1rem;
    margin-bottom: 12px;
}

.skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    list-style-type: none;
    margin: 0;
    padding: 0;
}

.skills-list li {
    background-color: #e9ecef;
    border-radius: 4px;
    color: #495057;
    padding: 6px 10px;
}

.common-skill {
    background-color: #d4edda !important;
    color: #155724 !important;
}

.unique-skill {
    background-color: #f8d7da !important;
    color: #721c24 !important;
}

.unique-skills-grid {
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.unique-skills-column h5 {
    border-bottom: 1px dashed #dee2e6;
    color: #495057;
    font-size: 1rem;
    margin-bottom: 10px;
    padding-bottom: 6px;
}

.no-unique-skills,
.no-unique-skills-found {
    color: #6c757d;
    font-style: italic;
}

.comparison-actions {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.action-button {
    background-color: #6c757d;
    border: none;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    padding: 10px 20px;
    transition: background-color 0.2s;
}

.action-button:hover {
    background-color: #5a6268;
}

.close-button {
    background-color: #dc3545;
}

.close-button:hover {
    background-color: #c82333;
}

@media (max-width: 768px) {
    .comparison-table {
        font-size: 0.9rem;
    }

    .comparison-table th,
    .comparison-table td {
        padding: 8px;
    }

    .type-badge,
    .level-badge,
    .domain-badge {
        font-size: 0.75rem;
        padding: 3px 6px;
    }

    .unique-skills-grid {
        grid-template-columns: 1fr;
    }
} 