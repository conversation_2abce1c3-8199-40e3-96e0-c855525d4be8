import React from 'react';
import { useLanguage } from '../../../contexts/LanguageContext';
import { CertificationLevel, CertificationType, ICertificationFilter, SecurityDomain } from '../types/certification.types';

interface CertificationFilterProps {
    filters: ICertificationFilter;
    onFilterChange: (name: string, value: string | number) => void;
    providers: string[];
    isLoading?: boolean;
}

const CertificationFilter: React.FC<CertificationFilterProps> = ({
    filters,
    onFilterChange,
    providers,
    isLoading = false
}) => {
    const { translate } = useLanguage();

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        onFilterChange(name, value);
    };

    return (
        <div className="filters-panel" aria-label={translate('certifications.filters.aria_label', 'Certification filters')} data-testid="filters-panel">
            <h2>{translate('certifications.filters.title', 'Filters')}</h2>

            <div className="filter-group">
                <label htmlFor="search">{translate('certifications.filters.search', 'Search')}:</label>
                <input
                    type="text"
                    id="search"
                    name="search"
                    value={filters.search || ''}
                    onChange={handleChange}
                    placeholder={translate('certifications.filters.search_placeholder', 'Search certifications...')}
                    disabled={isLoading}
                    aria-label={translate('certifications.filters.search_aria', 'Search certifications')}
                    data-testid="search-input"
                />
            </div>

            <div className="filter-group">
                <label htmlFor="provider">{translate('certifications.filters.provider', 'Provider')}:</label>
                <select
                    id="provider"
                    name="provider"
                    value={filters.provider || ''}
                    onChange={handleChange}
                    disabled={isLoading}
                    aria-label={translate('certifications.filters.provider_aria', 'Filter by provider')}
                    data-testid="provider-filter"
                >
                    <option value="">{translate('certifications.filters.all_providers', 'All Providers')}</option>
                    {providers.map(provider => (
                        <option key={provider} value={provider}>{provider}</option>
                    ))}
                </select>
            </div>

            <div className="filter-group">
                <label htmlFor="type">{translate('certifications.filters.type', 'Type')}:</label>
                <select
                    id="type"
                    name="type"
                    value={filters.type || ''}
                    onChange={handleChange}
                    disabled={isLoading}
                    aria-label={translate('certifications.filters.type_aria', 'Filter by certification type')}
                    data-testid="type-filter"
                >
                    <option value="">{translate('certifications.filters.all_types', 'All Types')}</option>
                    <option value={CertificationType.SECURITY}>{translate('certifications.types.security', 'Security')}</option>
                    <option value={CertificationType.CLOUD}>{translate('certifications.types.cloud', 'Cloud')}</option>
                    <option value={CertificationType.NETWORKING}>{translate('certifications.types.networking', 'Networking')}</option>
                    <option value={CertificationType.DEVELOPMENT}>{translate('certifications.types.development', 'Development')}</option>
                    <option value={CertificationType.DEVOPS}>{translate('certifications.types.devops', 'DevOps')}</option>
                    <option value={CertificationType.OTHER}>{translate('certifications.types.other', 'Other')}</option>
                </select>
            </div>

            <div className="filter-group">
                <label htmlFor="domain">{translate('certifications.filters.domain', 'Domain')}:</label>
                <select
                    id="domain"
                    name="domain"
                    value={filters.domain || ''}
                    onChange={handleChange}
                    disabled={isLoading}
                    aria-label={translate('certifications.filters.domain_aria', 'Filter by security domain')}
                    data-testid="domain-filter"
                >
                    <option value="">{translate('certifications.filters.all_domains', 'All Domains')}</option>
                    <option value={SecurityDomain.SECURITY_ENGINEERING}>{translate('certifications.domains.security_engineering', 'Security Engineering')}</option>
                    <option value={SecurityDomain.DEFENSIVE_SECURITY}>{translate('certifications.domains.defensive_security', 'Defensive Security')}</option>
                    <option value={SecurityDomain.SECURITY_MANAGEMENT}>{translate('certifications.domains.security_management', 'Security Management')}</option>
                    <option value={SecurityDomain.NETWORK_SECURITY}>{translate('certifications.domains.network_security', 'Network Security')}</option>
                    <option value={SecurityDomain.IDENTITY_ACCESS_MANAGEMENT}>{translate('certifications.domains.identity_access_management', 'Identity & Access Management')}</option>
                    <option value={SecurityDomain.ASSET_SECURITY}>{translate('certifications.domains.asset_security', 'Asset Security')}</option>
                    <option value={SecurityDomain.SECURITY_TESTING}>{translate('certifications.domains.security_testing', 'Security Testing')}</option>
                    <option value={SecurityDomain.OFFENSIVE_SECURITY}>{translate('certifications.domains.offensive_security', 'Offensive Security')}</option>
                </select>
            </div>

            <div className="filter-group">
                <label htmlFor="level">{translate('certifications.filters.level', 'Level')}:</label>
                <select
                    id="level"
                    name="level"
                    value={filters.level || ''}
                    onChange={handleChange}
                    disabled={isLoading}
                    aria-label={translate('certifications.filters.level_aria', 'Filter by certification level')}
                    data-testid="level-filter"
                >
                    <option value="">{translate('certifications.filters.all_levels', 'All Levels')}</option>
                    <option value={CertificationLevel.BEGINNER}>{translate('certifications.levels.beginner', 'Beginner')}</option>
                    <option value={CertificationLevel.INTERMEDIATE}>{translate('certifications.levels.intermediate', 'Intermediate')}</option>
                    <option value={CertificationLevel.ADVANCED}>{translate('certifications.levels.advanced', 'Advanced')}</option>
                    <option value={CertificationLevel.EXPERT}>{translate('certifications.levels.expert', 'Expert')}</option>
                </select>
            </div>

            <div className="filter-group">
                <label htmlFor="min_price">{translate('certifications.filters.min_price', 'Min Price')}:</label>
                <input
                    type="number"
                    id="min_price"
                    name="min_price"
                    value={filters.min_price || ''}
                    onChange={handleChange}
                    placeholder={translate('certifications.filters.min_price_placeholder', 'Min price')}
                    min="0"
                    disabled={isLoading}
                    aria-label={translate('certifications.filters.min_price_aria', 'Minimum price filter')}
                />
            </div>

            <div className="filter-group">
                <label htmlFor="max_price">{translate('certifications.filters.max_price', 'Max Price')}:</label>
                <input
                    type="number"
                    id="max_price"
                    name="max_price"
                    value={filters.max_price || ''}
                    onChange={handleChange}
                    placeholder={translate('certifications.filters.max_price_placeholder', 'Max price')}
                    min="0"
                    disabled={isLoading}
                    aria-label={translate('certifications.filters.max_price_aria', 'Maximum price filter')}
                />
            </div>

            <button
                className="filter-reset-btn"
                onClick={() => {
                    onFilterChange('provider', '');
                    onFilterChange('type', '');
                    onFilterChange('level', '');
                    onFilterChange('domain', '');
                    onFilterChange('search', '');
                    onFilterChange('min_price', '');
                    onFilterChange('max_price', '');
                }}
                disabled={isLoading}
                aria-label={translate('certifications.filters.reset_aria', 'Reset all filters')}
            >
                {translate('certifications.filters.reset', 'Reset Filters')}
            </button>
        </div>
    );
};

export default CertificationFilter; 