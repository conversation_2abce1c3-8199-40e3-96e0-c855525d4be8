/**
 * Constants for the Certification Explorer
 */
import { SecurityDomain } from './types/certification.types';

// API endpoints
export const API_ENDPOINTS = {
  PREFIX: '/api/v1/certifications',
  LIST: '/api/v1/certifications',
  DETAIL: '/api/v1/certifications',
  RELATIONSHIPS: '/api/v1/certifications/relationships',
  COMPARISON: '/api/v1/certifications/compare',
  PROVIDERS: '/api/v1/certifications/providers',
  USER_CERTIFICATIONS: '/api/v1/users/certifications',
  STUDY_ESTIMATE: '/api/v1/study/estimate',
  STUDY_ROI: '/api/v1/study/roi',
};

// Pagination defaults
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [5, 10, 25, 50],
};

// Filter defaults
export const FILTER_OPTIONS = {
  TYPES: [
    { value: '', label: 'All Types' },
    { value: 'security', label: 'Security' },
    { value: 'cloud', label: 'Cloud' },
    { value: 'networking', label: 'Networking' },
    { value: 'development', label: 'Development' },
    { value: 'devops', label: 'DevOps' },
    { value: 'other', label: 'Other' }
  ],
  LEVELS: [
    { value: '', label: 'All Levels' },
    { value: 'beginner', label: 'Beginner' },
    { value: 'intermediate', label: 'Intermediate' },
    { value: 'advanced', label: 'Advanced' },
    { value: 'expert', label: 'Expert' }
  ],
  DOMAINS: [
    { value: '', label: 'All Domains' },
    { value: 'Security Engineering', label: 'Security Engineering' },
    { value: 'Defensive Security', label: 'Defensive Security' },
    { value: 'Security Management', label: 'Security Management' },
    { value: 'Network Security', label: 'Network Security' },
    { value: 'Identity & Access Management', label: 'Identity & Access Management' },
    { value: 'Asset Security', label: 'Asset Security' },
    { value: 'Security Testing', label: 'Security Testing' },
    { value: 'Offensive Security', label: 'Offensive Security' }
  ],
  PRICE_RANGES: [
    { min: 0, max: 100, label: 'Under $100' },
    { min: 100, max: 300, label: '$100 - $300' },
    { min: 300, max: 500, label: '$300 - $500' },
    { min: 500, max: 1000, label: '$500 - $1,000' },
    { min: 1000, max: null, label: 'Over $1,000' }
  ]
};

// UI constants
export const UI = {
  ANIMATION_DURATION: 300, // ms
  DEBOUNCE_DELAY: 300, // ms for search input
  MOBILE_BREAKPOINT: 768, // px
  GRID_BREAKPOINTS: {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
  },
};

// Level colors
export const LEVEL_COLORS = {
  beginner: '#4caf50', // green
  intermediate: '#2196f3', // blue
  advanced: '#ff9800', // orange
  expert: '#f44336', // red
};

// Type colors
export const TYPE_COLORS = {
  security: '#9c27b0', // purple
  cloud: '#03a9f4', // light blue
  networking: '#3f51b5', // indigo
  development: '#ff5722', // deep orange
  devops: '#009688', // teal
  other: '#607d8b', // blue grey
};

// Domain colors
export const DOMAIN_COLORS: Record<SecurityDomain, string> = {
  [SecurityDomain.SECURITY_ENGINEERING]: '#673ab7', // deep purple
  [SecurityDomain.DEFENSIVE_SECURITY]: '#2196f3', // blue
  [SecurityDomain.SECURITY_MANAGEMENT]: '#3f51b5', // indigo
  [SecurityDomain.NETWORK_SECURITY]: '#00bcd4', // cyan
  [SecurityDomain.IDENTITY_ACCESS_MANAGEMENT]: '#009688', // teal
  [SecurityDomain.ASSET_SECURITY]: '#4caf50', // green
  [SecurityDomain.SECURITY_TESTING]: '#ffc107', // amber
  [SecurityDomain.OFFENSIVE_SECURITY]: '#ff5722', // deep orange
};

// Provider logos
export const PROVIDER_LOGOS: Record<string, string> = {
  'CompTIA': '/assets/logos/comptia.png',
  'Cisco': '/assets/logos/cisco.png',
  'Microsoft': '/assets/logos/microsoft.png',
  'Amazon': '/assets/logos/aws.png',
  'Google': '/assets/logos/google.png',
  'ISC²': '/assets/logos/isc2.png',
  'EC-Council': '/assets/logos/eccouncil.png',
  'ISACA': '/assets/logos/isaca.png',
};

// Text constants
export const TEXTS = {
  NO_RESULTS: 'No certifications found matching your criteria.',
  LOADING: 'Loading certifications...',
  ERROR: 'Error loading certifications. Please try again later.'
}; 