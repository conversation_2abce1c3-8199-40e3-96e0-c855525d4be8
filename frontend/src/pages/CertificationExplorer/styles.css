/* Certification Explorer Styles */
.certification-explorer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  color: var(--text-color, #333);
}

.certification-explorer-loading {
  text-align: center;
  padding: 40px;
  font-size: 18px;
}

.explorer-description {
  font-size: 1.1rem;
  margin-bottom: 30px;
  color: var(--text-secondary, #666);
}

/* Content layout */
.certification-explorer-content {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 30px;
}

/* Filters panel */
.filters-panel {
  background-color: var(--bg-secondary, #f5f5f5);
  padding: 20px;
  border-radius: 8px;
  height: fit-content;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filters-panel h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.3rem;
  border-bottom: 1px solid var(--border-color, #ddd);
  padding-bottom: 10px;
}

.filter-group {
  margin-bottom: 15px;
}

.filter-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.filter-group input,
.filter-group select {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 4px;
  background-color: var(--bg-primary, #fff);
  color: var(--text-color, #333);
}

/* Main content */
.main-content {
  min-height: 500px;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

/* Certification list */
.certification-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.certification-card {
  background-color: var(--bg-secondary, #f9f9f9);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.certification-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.certification-card h3 {
  margin: 0;
  font-size: 1.1rem;
}

.certification-card span {
  display: block;
  font-size: 0.9rem;
  color: var(--text-secondary, #666);
}

/* Certification details */
.certification-details {
  background-color: var(--bg-secondary, #f9f9f9);
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.back-button {
  background-color: var(--primary-color, #007bff);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 20px;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: var(--primary-color-dark, #0056b3);
}

.certification-meta {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin: 20px 0;
  padding: 15px;
  background-color: var(--bg-primary, #fff);
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.certification-meta span {
  font-size: 0.95rem;
}

.certification-description,
.certification-skills {
  margin-bottom: 25px;
}

.certification-description h3,
.certification-skills h3 {
  font-size: 1.2rem;
  margin-bottom: 10px;
}

.certification-skills ul {
  list-style-type: disc;
  padding-left: 25px;
}

.certification-skills li {
  margin-bottom: 5px;
}

.certification-url a {
  display: inline-block;
  background-color: var(--primary-color, #007bff);
  color: white;
  text-decoration: none;
  padding: 10px 20px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.certification-url a:hover {
  background-color: var(--primary-color-dark, #0056b3);
}

/* Related certifications */
.related-certifications {
  margin-top: 30px;
}

.related-certifications h3 {
  font-size: 1.2rem;
  margin-bottom: 15px;
}

.related-certifications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.related-certification-card {
  background-color: var(--bg-primary, #fff);
  border-radius: 6px;
  padding: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.related-certification-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.related-certification-card h4 {
  margin: 0 0 8px 0;
  font-size: 1rem;
}

.related-certification-card span {
  font-size: 0.9rem;
  color: var(--text-secondary, #666);
}

/* Pagination */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
  gap: 15px;
}

.pagination button {
  background-color: var(--primary-color, #007bff);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.pagination button:disabled {
  background-color: var(--disabled-color, #ccc);
  cursor: not-allowed;
}

.pagination button:not(:disabled):hover {
  background-color: var(--primary-color-dark, #0056b3);
}

.pagination select {
  padding: 8px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 4px;
  background-color: var(--bg-primary, #fff);
}

.page-info {
  font-size: 0.9rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .certification-explorer-content {
    grid-template-columns: 1fr;
  }
  
  .filters-panel {
    margin-bottom: 20px;
  }
  
  .certification-list {
    grid-template-columns: 1fr;
  }
  
  .certification-meta {
    grid-template-columns: 1fr;
  }
}
