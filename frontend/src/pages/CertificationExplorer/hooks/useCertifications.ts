import axios from 'axios';
import { useCallback, useEffect, useState } from 'react';
import { API_ENDPOINTS, PAGINATION } from '../constants';
import {
    ICertification,
    ICertificationFilter,
    ICertificationListResponse
} from '../types/certification.types';

export const useCertifications = () => {
  // State for certifications data
  const [certifications, setCertifications] = useState<ICertification[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // State for pagination
  const [page, setPage] = useState<number>(PAGINATION.DEFAULT_PAGE);
  const [pageSize, setPageSize] = useState<number>(PAGINATION.DEFAULT_PAGE_SIZE);
  const [totalCertifications, setTotalCertifications] = useState<number>(0);
  
  // State for filters
  const [filters, setFilters] = useState<ICertificationFilter>({
    provider: '',
    type: undefined,
    level: undefined,
    domain: undefined,
    search: '',
    min_price: undefined,
    max_price: undefined,
    page: PAGINATION.DEFAULT_PAGE,
    page_size: PAGINATION.DEFAULT_PAGE_SIZE
  });
  
  // State for providers list
  const [providers, setProviders] = useState<string[]>([]);
  
  // State for selected certification
  const [selectedCertification, setSelectedCertification] = useState<ICertification | null>(null);
  
  // State for related certifications
  const [relatedCertifications, setRelatedCertifications] = useState<ICertification[]>([]);

  // Keep a reference to the full sample dataset
  let fullSampleDataset: ICertification[] = [];

  // Fetch certifications data
  const fetchCertifications = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Calculate skip for pagination
      const skip = (page - 1) * pageSize;

      // Build query parameters
      const params = new URLSearchParams();
      params.append('skip', skip.toString());
      params.append('limit', pageSize.toString());
      if (filters.provider) params.append('provider', filters.provider);
      if (filters.type) params.append('type', filters.type as string);
      if (filters.level) params.append('level', filters.level as string);
      if (filters.domain) params.append('domain', filters.domain as string);
      if (filters.search) params.append('search', filters.search);
      if (filters.min_price !== undefined) params.append('min_price', filters.min_price.toString());
      if (filters.max_price !== undefined) params.append('max_price', filters.max_price.toString());

      // Make API request with full API path
      console.log(`Fetching certifications from ${API_ENDPOINTS.LIST}?${params.toString()}`);
      const response = await axios.get<ICertificationListResponse>(`${API_ENDPOINTS.LIST}?${params.toString()}`);
      
      if (response.data && Array.isArray(response.data.certifications)) {
        console.log(`Loaded ${response.data.certifications.length} of ${response.data.total} certifications`);
        setCertifications(response.data.certifications);
        setTotalCertifications(response.data.total);
        setError(null);
      } else {
        console.error('Invalid API response format:', response.data);
        setError('API returned data in unexpected format. Please try again later.');
        setCertifications([]);
        setTotalCertifications(0);
      }
    } catch (err) {
      console.error('Error fetching certifications:', err);
      setError('Failed to load certifications. Please try again later.');
      setCertifications([]);
      setTotalCertifications(0);
    } finally {
      setLoading(false);
    }
  }, [page, pageSize, filters]);

  // Fetch certifications on initial load and when dependencies change
  useEffect(() => {
    fetchCertifications();
  }, [fetchCertifications]);

  // Fetch providers list
  useEffect(() => {
    const fetchProviders = async () => {
      try {
        console.log(`Fetching providers from ${API_ENDPOINTS.PROVIDERS}`);
        const response = await axios.get<string[]>(API_ENDPOINTS.PROVIDERS);
        
        if (response.data && Array.isArray(response.data)) {
          console.log(`Loaded ${response.data.length} providers`);
          setProviders(response.data);
        } else {
          console.error('Invalid providers response format:', response.data);
          setProviders([]);
        }
      } catch (err) {
        console.error('Error fetching providers:', err);
        setProviders([]);
      }
    };

    fetchProviders();
  }, []);

  // Fetch related certifications when a certification is selected
  useEffect(() => {
    const fetchRelatedCertifications = async () => {
      if (!selectedCertification) {
        setRelatedCertifications([]);
        return;
      }

      try {
        console.log(`Fetching related certifications for ID ${selectedCertification.id}`);
        const response = await axios.get<ICertification[]>(`${API_ENDPOINTS.RELATIONSHIPS}/${selectedCertification.id}`);
        
        if (response.data && Array.isArray(response.data)) {
          console.log(`Loaded ${response.data.length} related certifications`);
          setRelatedCertifications(response.data);
        } else {
          console.error('Invalid related certifications response format:', response.data);
          setRelatedCertifications([]);
        }
      } catch (err) {
        console.error('Error fetching related certifications:', err);
        setRelatedCertifications([]);
      }
    };

    fetchRelatedCertifications();
  }, [selectedCertification]);

  const handleFilterChange = (name: string, value: string | number) => {
    setFilters(prev => ({ ...prev, [name]: value }));
    // Reset to first page when filters change
    setPage(1);
  };

  const handleCertificationSelect = (certification: ICertification) => {
    setSelectedCertification(certification);
  };

  const handleBackToList = () => {
    setSelectedCertification(null);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    // Reset to first page when page size changes
    setPage(1);
  };

  return {
    certifications,
    loading,
    error,
    page,
    pageSize,
    totalCertifications,
    filters,
    providers,
    selectedCertification,
    relatedCertifications,
    handleFilterChange,
    handleCertificationSelect,
    handleBackToList,
    handlePageChange,
    handlePageSizeChange,
  };
}; 