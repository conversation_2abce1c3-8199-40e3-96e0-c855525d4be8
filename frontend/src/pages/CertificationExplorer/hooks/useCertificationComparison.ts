import { useCallback, useState } from 'react';
import { ICertification, ICertificationComparison, SecurityDomain } from '../types/certification.types';

export const useCertificationComparison = () => {
    // State for certifications to compare
    const [certificationsToCompare, setCertificationsToCompare] = useState<ICertification[]>([]);
    
    // State for comparison data
    const [comparisonData, setComparisonData] = useState<ICertificationComparison | null>(null);
    
    // State for comparison UI
    const [isComparing, setIsComparing] = useState(false);
    
    // State for loading and error
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Maximum number of certifications that can be compared
    const MAX_COMPARED_CERTIFICATIONS = 4;

    // Add a certification to the comparison list
    const addCertificationToCompare = useCallback((certification: ICertification) => {
        setCertificationsToCompare(prev => {
            // Check if certification is already in the list
            if (prev.some(c => c.id === certification.id)) {
                return prev;
            }
            
            // Check if we've reached the maximum
            if (prev.length >= MAX_COMPARED_CERTIFICATIONS) {
                console.warn(`Maximum of ${MAX_COMPARED_CERTIFICATIONS} certifications can be compared at once`);
                return prev;
            }
            
            // Add the certification to the list
            return [...prev, certification];
        });
    }, []);

    // Remove a certification from the comparison list
    const removeCertificationFromCompare = useCallback((certificationId: number) => {
        setCertificationsToCompare(prev => 
            prev.filter(c => c.id !== certificationId)
        );
    }, []);

    // Clear all certifications from comparison
    const clearComparison = useCallback(() => {
        setCertificationsToCompare([]);
        setComparisonData(null);
        setIsComparing(false);
    }, []);

    // Compare the selected certifications
    const startComparison = useCallback(async () => {
        // Check if we have at least 2 certifications to compare
        if (certificationsToCompare.length < 2) {
            setError('Select at least two certifications to compare');
            return;
        }

        try {
            setLoading(true);
            setError(null);

            // In a real API scenario, we would make a request to the server
            // For now, we'll simulate the comparison locally
            
            // Get all unique skills across all certifications
            const allSkills = new Set<string>();
            const skillsMap = new Map<number, Set<string>>();
            
            // Get all domains
            const allDomains = new Set<SecurityDomain>();
            const domainsMap = new Map<number, Set<SecurityDomain>>();
            
            // Populate skills and domains
            certificationsToCompare.forEach(cert => {
                // Skills
                const certSkills = new Set<string>();
                if (cert.skills_covered) {
                    cert.skills_covered.forEach(skill => {
                        allSkills.add(skill);
                        certSkills.add(skill);
                    });
                }
                skillsMap.set(cert.id, certSkills);
                
                // Domains
                const certDomains = new Set<SecurityDomain>();
                if (cert.domain) {
                    allDomains.add(cert.domain);
                    certDomains.add(cert.domain);
                }
                if (cert.domains) {
                    cert.domains.forEach(domain => {
                        const domainValue = domain.name as SecurityDomain;
                        allDomains.add(domainValue);
                        certDomains.add(domainValue);
                    });
                }
                domainsMap.set(cert.id, certDomains);
            });
            
            // Find common skills
            const commonSkills: string[] = [];
            allSkills.forEach(skill => {
                let isCommon = true;
                for (const cert of certificationsToCompare) {
                    if (!skillsMap.get(cert.id)?.has(skill)) {
                        isCommon = false;
                        break;
                    }
                }
                if (isCommon) {
                    commonSkills.push(skill);
                }
            });
            
            // Find unique skills for each certification
            const uniqueSkills: Record<number, string[]> = {};
            certificationsToCompare.forEach(cert => {
                uniqueSkills[cert.id] = [];
                skillsMap.get(cert.id)?.forEach(skill => {
                    let isUnique = true;
                    for (const otherCert of certificationsToCompare) {
                        if (otherCert.id !== cert.id && skillsMap.get(otherCert.id)?.has(skill)) {
                            isUnique = false;
                            break;
                        }
                    }
                    if (isUnique) {
                        uniqueSkills[cert.id].push(skill);
                    }
                });
            });
            
            // Find common domains
            const commonDomains: SecurityDomain[] = [];
            allDomains.forEach(domain => {
                let isCommon = true;
                for (const cert of certificationsToCompare) {
                    if (!domainsMap.get(cert.id)?.has(domain)) {
                        isCommon = false;
                        break;
                    }
                }
                if (isCommon) {
                    commonDomains.push(domain);
                }
            });
            
            // Find unique domains for each certification
            const uniqueDomains: Record<number, SecurityDomain[]> = {};
            certificationsToCompare.forEach(cert => {
                uniqueDomains[cert.id] = [];
                domainsMap.get(cert.id)?.forEach(domain => {
                    let isUnique = true;
                    for (const otherCert of certificationsToCompare) {
                        if (otherCert.id !== cert.id && domainsMap.get(otherCert.id)?.has(domain)) {
                            isUnique = false;
                            break;
                        }
                    }
                    if (isUnique) {
                        uniqueDomains[cert.id].push(domain);
                    }
                });
            });
            
            // Get prerequisites for each certification
            const prerequisites: Record<number, string[]> = {};
            certificationsToCompare.forEach(cert => {
                prerequisites[cert.id] = cert.prerequisites || [];
            });
            
            // Create comparison data
            const comparison: ICertificationComparison = {
                certifications: certificationsToCompare,
                commonSkills,
                uniqueSkills,
                commonDomains,
                uniqueDomains,
                prerequisites
            };
            
            setComparisonData(comparison);
            setIsComparing(true);
        } catch (err) {
            console.error('Error comparing certifications:', err);
            setError('Failed to compare certifications. Please try again later.');
        } finally {
            setLoading(false);
        }
    }, [certificationsToCompare]);

    // Cancel comparison and return to normal view
    const cancelComparison = useCallback(() => {
        setIsComparing(false);
    }, []);

    return {
        certificationsToCompare,
        comparisonData,
        loading,
        error,
        isComparing,
        addCertificationToCompare,
        removeCertificationFromCompare,
        clearComparison,
        startComparison,
        cancelComparison
    };
}; 