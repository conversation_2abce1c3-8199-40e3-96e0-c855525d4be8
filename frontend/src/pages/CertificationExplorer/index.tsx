import axios from 'axios';
import React, { useEffect, useState } from 'react';
import {
  Certification,
  adaptCertification,
  adaptCertificationListResponse
} from '../../adapters/certificationAdapter';
import './styles.css';

interface FilterState {
  provider: string;
  type: string;
  level: string;
  search: string;
  min_price: string;
  max_price: string;
}

const CertificationExplorer: React.FC = () => {
  // State for certifications data
  const [certifications, setCertifications] = useState<Certification[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // State for pagination
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [totalCertifications, setTotalCertifications] = useState<number>(0);

  // State for filters
  const [filters, setFilters] = useState<FilterState>({
    provider: '',
    type: '',
    level: '',
    search: '',
    min_price: '',
    max_price: ''
  });

  // State for providers list
  const [providers, setProviders] = useState<string[]>([]);

  // State for selected certification
  const [selectedCertification, setSelectedCertification] = useState<Certification | null>(null);

  // State for related certifications
  const [relatedCertifications, setRelatedCertifications] = useState<Certification[]>([]);

  // Fetch certifications data
  useEffect(() => {
    const fetchCertifications = async () => {
      try {
        setLoading(true);

        // Calculate skip for pagination
        const skip = (page - 1) * pageSize;

        // Build query parameters
        const params = new URLSearchParams();
        params.append('skip', skip.toString());
        params.append('limit', pageSize.toString());
        if (filters.provider) params.append('provider', filters.provider);
        if (filters.type) params.append('type', filters.type);
        if (filters.level) params.append('level', filters.level);
        if (filters.search) params.append('search', filters.search);
        if (filters.min_price) params.append('min_price', filters.min_price);
        if (filters.max_price) params.append('max_price', filters.max_price);

        // Make API request
        const response = await axios.get(`/api/certifications?${params.toString()}`);

        // Use adapter to convert API response to frontend format
        const adaptedResponse = adaptCertificationListResponse(response.data);

        setCertifications(adaptedResponse.certifications);
        setTotalCertifications(adaptedResponse.total);
        setError(null);

        console.log('Fetched certifications:', adaptedResponse.certifications.length);
      } catch (err) {
        console.error('Error fetching certifications:', err);
        setError('Failed to load certifications. Please try again later.');
        // For development, use mock data when API is not available
        const mockCertifications: Certification[] = [
          {
            id: 1,
            name: 'Certified Ethical Hacker (CEH)',
            provider: 'EC-Council',
            description: 'The Certified Ethical Hacker certification focuses on ethical hacking methodologies.',
            type: 'security',
            level: 'intermediate',
            exam_code: '312-50',
            price: 1199,
            duration_minutes: 240,
            passing_score: 70,
            skills_covered: ['penetration testing', 'vulnerability assessment', 'network security'],
            url: 'https://www.eccouncil.org/programs/certified-ethical-hacker-ceh/',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 2,
            name: 'CompTIA Security+',
            provider: 'CompTIA',
            description: 'CompTIA Security+ is a global certification that validates the baseline skills necessary to perform core security functions.',
            type: 'security',
            level: 'beginner',
            exam_code: 'SY0-601',
            price: 349,
            duration_minutes: 90,
            passing_score: 750,
            skills_covered: ['network security', 'compliance', 'operational security'],
            url: 'https://www.comptia.org/certifications/security',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ];
        setCertifications(mockCertifications);
        setTotalCertifications(mockCertifications.length);
      } finally {
        setLoading(false);
      }
    };

    fetchCertifications();
  }, [page, pageSize, filters]);

  // Fetch providers list
  useEffect(() => {
    const fetchProviders = async () => {
      try {
        const response = await axios.get<string[]>('/api/certifications/providers');
        setProviders(response.data);
        console.log('Fetched providers:', response.data);
      } catch (err) {
        console.error('Error fetching providers:', err);
        // For development, use mock data when API is not available
        setProviders(['EC-Council', 'CompTIA', 'ISC2', 'Microsoft', 'Cisco', 'AWS']);
      }
    };

    fetchProviders();
  }, []);

  // Fetch related certifications when a certification is selected
  useEffect(() => {
    const fetchRelatedCertifications = async () => {
      if (!selectedCertification) return;

      try {
        const response = await axios.get(`/api/certifications/related/${selectedCertification.id}`);
        // Use adapter to convert API response to frontend format
        const adaptedCertifications = response.data.map(adaptCertification);
        setRelatedCertifications(adaptedCertifications);
        console.log('Fetched related certifications:', adaptedCertifications.length);
      } catch (err) {
        console.error('Error fetching related certifications:', err);
        setRelatedCertifications([]);
      }
    };

    fetchRelatedCertifications();
  }, [selectedCertification]);

  // Handle filter changes
  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters((prev: FilterState) => ({ ...prev, [name]: value }));
    setPage(1); // Reset to first page when filters change
  };

  // Handle certification selection
  const handleCertificationSelect = (certification: Certification) => {
    setSelectedCertification(certification);
  };

  // Handle certification deselection
  const handleCertificationDeselect = () => {
    setSelectedCertification(null);
    setRelatedCertifications([]);
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalCertifications / pageSize);

  // Format price display
  const formatPrice = (price: number | null) => {
    if (price === null) return 'Not available';
    return `$${price.toFixed(2)}`;
  };

  // Format duration display
  const formatDuration = (minutes: number | null) => {
    if (minutes === null) return 'Not available';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  if (loading && !certifications.length) {
    return <div className="certification-explorer-loading" data-testid="loading-state">Loading certifications...</div>;
  }

  return (
    <div className="certification-explorer-container" data-testid="certification-explorer">
      <h1 data-testid="explorer-title">Certification Explorer</h1>
      <p className="explorer-description">
        Explore and compare security certifications to find the right path for your career.
      </p>

      <div className="certification-explorer-content">
        {/* Filters Panel */}
        <div className="filters-panel" data-testid="filters-panel">
          <h2>Filters</h2>

          <div className="filter-group">
            <label htmlFor="search">Search:</label>
            <input
              type="text"
              id="search"
              name="search"
              value={filters.search}
              onChange={handleFilterChange}
              placeholder="Search certifications..."
              data-testid="search-input"
            />
          </div>

          <div className="filter-group">
            <label htmlFor="provider">Provider:</label>
            <select
              id="provider"
              name="provider"
              value={filters.provider}
              onChange={handleFilterChange}
              data-testid="provider-filter"
            >
              <option value="">All Providers</option>
              {providers.map(provider => (
                <option key={provider} value={provider}>{provider}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="type">Type:</label>
            <select
              id="type"
              name="type"
              value={filters.type}
              onChange={handleFilterChange}
              data-testid="type-filter"
            >
              <option value="">All Types</option>
              <option value="security">Security</option>
              <option value="cloud">Cloud</option>
              <option value="networking">Networking</option>
              <option value="development">Development</option>
              <option value="devops">DevOps</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="level">Level:</label>
            <select
              id="level"
              name="level"
              value={filters.level}
              onChange={handleFilterChange}
              data-testid="level-filter"
            >
              <option value="">All Levels</option>
              <option value="beginner">Beginner</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
              <option value="expert">Expert</option>
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="min_price">Min Price:</label>
            <input
              type="number"
              id="min_price"
              name="min_price"
              value={filters.min_price}
              onChange={handleFilterChange}
              placeholder="Min price"
              data-testid="min-price-filter"
            />
          </div>

          <div className="filter-group">
            <label htmlFor="max_price">Max Price:</label>
            <input
              type="number"
              id="max_price"
              name="max_price"
              value={filters.max_price}
              onChange={handleFilterChange}
              placeholder="Max price"
              data-testid="max-price-filter"
            />
          </div>
        </div>

        {/* Main Content */}
        <div className="main-content" data-testid="main-content">
          {/* Error Message */}
          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          {/* Selected Certification Detail */}
          {selectedCertification ? (
            <div className="certification-details" data-testid="certification-details">
              <button
                className="back-button"
                onClick={handleCertificationDeselect}
                data-testid="back-button"
              >
                Back to List
              </button>

              <h2 data-testid="certification-name">{selectedCertification.name}</h2>
              <div className="certification-meta" data-testid="certification-meta">
                <span data-testid="certification-provider">Provider: {selectedCertification.provider}</span>
                <span data-testid="certification-type">Type: {selectedCertification.type}</span>
                <span data-testid="certification-level">Level: {selectedCertification.level}</span>
                <span data-testid="certification-price">Price: {formatPrice(selectedCertification.price)}</span>
                <span data-testid="certification-duration">Duration: {formatDuration(selectedCertification.duration_minutes)}</span>
                <span data-testid="certification-passing-score">Passing Score: {selectedCertification.passing_score || 'Not available'}</span>
              </div>

              <div className="certification-description" data-testid="certification-description">
                <h3>Description</h3>
                <p>{selectedCertification.description || 'No description available.'}</p>
              </div>

              {selectedCertification.skills_covered && selectedCertification.skills_covered.length > 0 && (
                <div className="certification-skills" data-testid="certification-skills">
                  <h3>Skills Covered</h3>
                  <ul>
                    {selectedCertification.skills_covered.map((skill, index) => (
                      <li key={index} data-testid={`skill-${index}`}>{skill}</li>
                    ))}
                  </ul>
                </div>
              )}

              {selectedCertification.url && (
                <div className="certification-url" data-testid="certification-url">
                  <a
                    href={selectedCertification.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    data-testid="certification-link"
                  >
                    Learn More
                  </a>
                </div>
              )}

              {/* Related Certifications */}
              {relatedCertifications.length > 0 && (
                <div className="related-certifications" data-testid="related-certifications">
                  <h3>Related Certifications</h3>
                  <div className="related-certifications-grid" data-testid="related-certifications-grid">
                    {relatedCertifications.map(cert => (
                      <div
                        key={cert.id}
                        className="related-certification-card"
                        onClick={() => handleCertificationSelect(cert)}
                        data-testid={`related-certification-${cert.id}`}
                      >
                        <h4>{cert.name}</h4>
                        <span>{cert.provider}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            /* Certifications List */
            <>
              <h2>Available Certifications</h2>

              {certifications.length === 0 ? (
                <div className="no-results">
                  No certifications found matching your criteria.
                </div>
              ) : (
                <div className="certification-list" data-testid="certification-list">
                  {certifications.map(cert => (
                    <div
                      key={cert.id}
                      className="certification-card"
                      onClick={() => handleCertificationSelect(cert)}
                      data-testid={`certification-card-${cert.id}`}
                    >
                      <h3>{cert.name}</h3>
                      <span>{cert.provider}</span>
                      <span>{cert.type}</span>
                      <span>{cert.level}</span>
                      <span>{formatPrice(cert.price)}</span>
                    </div>
                  ))}
                </div>
              )}

              {/* Pagination */}
              <div className="pagination">
                <button
                  onClick={() => setPage((p: number) => Math.max(1, p - 1))}
                  disabled={page === 1}
                >
                  Previous
                </button>

                <span className="page-info">
                  Page {page} of {totalPages || 1}
                </span>

                <button
                  onClick={() => setPage((p: number) => Math.min(totalPages || 1, p + 1))}
                  disabled={page === totalPages || totalPages === 0}
                >
                  Next
                </button>

                <select
                  value={pageSize}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                    setPageSize(Number(e.target.value));
                    setPage(1); // Reset to first page when page size changes
                  }}
                >
                  <option value="5">5 per page</option>
                  <option value="10">10 per page</option>
                  <option value="20">20 per page</option>
                  <option value="50">50 per page</option>
                </select>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default CertificationExplorer;
