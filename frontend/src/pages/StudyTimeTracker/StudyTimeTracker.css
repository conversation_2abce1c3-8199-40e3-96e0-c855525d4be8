.study-time-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.study-time-container h1 {
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.icon {
  margin-right: 0.5rem;
  font-size: 1.2em;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 1.2rem;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.success-message {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.warning-message {
  background-color: #fff8e1;
  color: #ff8f00;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.description {
  margin-bottom: 2rem;
  line-height: 1.5;
  color: #555;
}

/* Settings Panel */
.settings-panel {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group select,
.form-group input[type="number"],
.form-group input[type="date"] {
  width: 100%;
  max-width: 300px;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  min-height: 100px;
  resize: vertical;
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-group input[type="checkbox"] {
  margin-right: 0.5rem;
}

.range-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 0.25rem;
  font-size: 0.8rem;
  color: #666;
}

.value-display {
  margin-left: 0.5rem;
  font-weight: normal;
  color: #3f51b5;
}

/* Results Section */
.results-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.results-section h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.metric-card {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
}

.metric-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #3f51b5;
}

.metric-unit {
  font-size: 0.9rem;
  color: #555;
  margin-top: 0.25rem;
}

/* Progress Section */
.progress-section {
  margin-top: 2rem;
  margin-bottom: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.progress-section h3 {
  margin-top: 0;
  margin-bottom: 1rem;
}

.progress-message {
  background-color: #e8f5e9;
  border-radius: 4px;
  padding: 1rem;
  margin-top: 1rem;
}

.progress-message p {
  margin: 0;
  color: #2e7d32;
}

/* ROI Section */
.roi-section {
  margin-top: 2rem;
  margin-bottom: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.roi-metrics {
  margin-top: 1.5rem;
}

.roi-metrics h3 {
  margin-top: 0;
  margin-bottom: 1rem;
}

/* Recommendations Section */
.recommendations-section {
  margin-top: 2rem;
  margin-bottom: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.recommendations-section h3 {
  margin-top: 0;
  margin-bottom: 1rem;
}

.recommendations-section h4 {
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  color: #3f51b5;
}

.tips-list li,
.additional-tips li {
  margin-bottom: 0.5rem;
}

/* Weekly Schedule Section */
.weekly-schedule-section {
  margin-top: 2rem;
  margin-bottom: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.weekly-schedule-section h3 {
  margin-top: 0;
  margin-bottom: 1rem;
}

.accordion {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.accordion-item {
  border-bottom: 1px solid #e0e0e0;
}

.accordion-item:last-child {
  border-bottom: none;
}

.accordion-header {
  padding: 1rem;
  background-color: #f5f5f5;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.accordion-header:hover {
  background-color: #eee;
}

.accordion-content {
  padding: 1rem;
  background-color: #fff;
}

.accordion-content h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #3f51b5;
}

.accordion-content ul {
  margin-top: 0.5rem;
  margin-bottom: 1.5rem;
}

/* Custom Estimate Section */
.custom-estimate-section {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.toggle-button {
  background-color: #3f51b5;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.25rem;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.toggle-button:hover {
  background-color: #303f9f;
}

.custom-estimate-form {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.submit-button {
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.25rem;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.submit-button:hover {
  background-color: #388e3c;
}

.submit-button:disabled {
  background-color: #a5d6a7;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .study-time-container {
    padding: 1rem;
  }

  .metrics-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .custom-estimate-form {
    padding: 1rem;
  }
}
