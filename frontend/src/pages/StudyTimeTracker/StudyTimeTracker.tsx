import React, { useEffect, useState } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import api from '../../services/api';
import './StudyTimeTracker.css';

// Types
interface Certification {
  id: number;
  name: string;
  difficulty: number;
  cost: number;
  prerequisites: string;
  custom_hours?: number;
  study_notes?: string;
}

interface StudySchedule {
  status: 'success' | 'warning' | 'error';
  message?: string;
  suggested_target?: string;
  total_hours_required: number;
  weeks_required: number;
  weekly_schedule: WeeklySchedule[];
  study_tips: string[];
}

interface WeeklySchedule {
  week: number;
  hours_planned: number;
  focus_areas: FocusArea[];
  milestones: Milestone[];
}

interface FocusArea {
  topic: string;
  hours_suggested: number;
  resources: string[];
}

interface Milestone {
  description: string;
  type: string;
  target_date: string;
}

interface ROIMetrics {
  status: 'success' | 'error';
  message?: string;
  percent_roi: number;
  break_even_months: number;
  five_year_value: number;
}

const StudyTimeTracker: React.FC = () => {
  const { translate } = useLanguage();
  
  // State
  const [certifications, setCertifications] = useState<Certification[]>([]);
  const [selectedCertId, setSelectedCertId] = useState<number | null>(null);
  const [selectedCert, setSelectedCert] = useState<Certification | null>(null);
  const [hoursPerWeek, setHoursPerWeek] = useState<number>(10);
  const [useTargetDate, setUseTargetDate] = useState<boolean>(false);
  const [targetDate, setTargetDate] = useState<string>(
    new Date(Date.now() + 12 * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 12 weeks from now
  );
  const [progress, setProgress] = useState<number>(0);
  const [showROI, setShowROI] = useState<boolean>(false);
  const [currentSalary, setCurrentSalary] = useState<number>(75000);
  const [isCreatingCustomEstimate, setIsCreatingCustomEstimate] = useState<boolean>(false);
  
  // Form states for custom estimate
  const [customCertId, setCustomCertId] = useState<number | null>(null);
  const [estimatedHours, setEstimatedHours] = useState<number>(120);
  const [studyMaterials, setStudyMaterials] = useState<string>('');
  
  // Results
  const [studySchedule, setStudySchedule] = useState<StudySchedule | null>(null);
  const [roiMetrics, setRoiMetrics] = useState<ROIMetrics | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Calculate study weeks and hours based on difficulty
  const calculateStudyWeeks = (difficulty: number, hoursPerWeek: number): { weeks: number, totalHours: number } => {
    const baseHours: Record<number, number> = {
      1: 60,   // Entry level (~60 hours total)
      2: 120,  // Intermediate (~120 hours total)
      3: 240,  // Advanced (~240 hours total)
      4: 400   // Expert (~400 hours total)
    };

    const totalHours = baseHours[difficulty] || 120;
    const weeks = Math.round((totalHours / hoursPerWeek) * 10) / 10; // Round to 1 decimal place
    
    return { weeks, totalHours };
  };

  // Load certifications
  useEffect(() => {
    const fetchCertifications = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const data = await api.certifications.getAll();
        setCertifications(data);
        
        if (data.length > 0) {
          setSelectedCertId(data[0].id);
          setSelectedCert(data[0]);
          setCustomCertId(data[0].id);
        }
      } catch (err) {
        console.error('Error fetching certifications:', err);
        setError('Failed to load certifications. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCertifications();
  }, []);

  // Update selected certification when ID changes
  useEffect(() => {
    if (selectedCertId !== null) {
      const cert = certifications.find(c => c.id === selectedCertId) || null;
      setSelectedCert(cert);
    }
  }, [selectedCertId, certifications]);

  // Generate study schedule
  const generateStudySchedule = async () => {
    if (!selectedCertId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      setSuccessMessage(null);
      
      const data = await api.study.getEstimate({
        certification_id: selectedCertId,
        hours_per_week: hoursPerWeek,
        target_date: useTargetDate ? targetDate : null
      });
      
      setStudySchedule(data);
      
      if (data.status === 'success') {
        setSuccessMessage('Study schedule generated successfully!');
      }
    } catch (err) {
      console.error('Error generating study schedule:', err);
      setError('Failed to generate study schedule. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate ROI
  const calculateROI = async () => {
    if (!selectedCertId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      const data = await api.study.getROI(selectedCertId);
      setRoiMetrics(data);
    } catch (err) {
      console.error('Error calculating ROI:', err);
      setError('Failed to calculate ROI. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  // Effect to generate study schedule when needed parameters change
  useEffect(() => {
    if (selectedCertId && hoursPerWeek > 0) {
      generateStudySchedule();
    }
  }, [selectedCertId, hoursPerWeek, useTargetDate && targetDate]);

  // Effect to calculate ROI when showROI changes
  useEffect(() => {
    if (showROI && selectedCertId) {
      calculateROI();
    }
  }, [showROI, selectedCertId, currentSalary]);

  // Add custom estimate
  const handleAddCustomEstimate = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!customCertId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      await api.study.updateProgress({
        certification_id: customCertId,
        custom_hours: estimatedHours,
        study_notes: studyMaterials
      });
      
      setSuccessMessage('Custom estimate added successfully!');
      setIsCreatingCustomEstimate(false);
      
      // Refresh certifications
      const data = await api.certifications.getAll();
      setCertifications(data);
    } catch (err) {
      console.error('Error adding custom estimate:', err);
      setError('Failed to add custom estimate. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  // Update progress
  const handleProgressUpdate = async (newProgress: number) => {
    if (!selectedCertId) return;
    
    setProgress(newProgress);
    
    try {
      await api.study.updateProgress({
        certification_id: selectedCertId,
        progress: newProgress
      });
      
      // No need to show success message for progress updates
    } catch (err) {
      console.error('Error updating progress:', err);
      setError('Failed to update progress. Please try again later.');
    }
  };

  // If loading
  if (isLoading && !studySchedule) {
    return <div className="loading-spinner">Loading...</div>;
  }

  return (
    <div className="study-time-container">
      <h1>
        <span className="icon">🎓</span> 
        {translate('study.time.title', 'Study Time Estimator')}
      </h1>
      
      {error && <div className="error-message">{error}</div>}
      {successMessage && <div className="success-message">{successMessage}</div>}
      
      <div className="description">
        {translate('study.time.description', 'Plan your certification journey by estimating the time needed based on your schedule. This tool provides personalized study time estimates and recommendations.')}
      </div>
      
      {/* Main Settings Panel */}
      <div className="settings-panel">
        {/* Certification Selection */}
        <div className="form-group">
          <label htmlFor="certification-select">
            {translate('study.select.certification', 'Select Certification')}:
          </label>
          <select 
            id="certification-select"
            value={selectedCertId || ''}
            onChange={(e) => setSelectedCertId(Number(e.target.value))}
          >
            {certifications.map(cert => (
              <option key={cert.id} value={cert.id}>
                {cert.name}
              </option>
            ))}
          </select>
        </div>
        
        {/* Hours Per Week */}
        <div className="form-group">
          <label htmlFor="hours-per-week">
            {translate('study.hours.per.week', 'Available Study Hours per Week')}:
            <span className="value-display">{hoursPerWeek}</span>
          </label>
          <input 
            type="range"
            id="hours-per-week"
            min="1"
            max="40"
            value={hoursPerWeek}
            onChange={(e) => setHoursPerWeek(Number(e.target.value))}
          />
          <div className="range-labels">
            <span>1</span>
            <span>20</span>
            <span>40</span>
          </div>
        </div>
        
        {/* Target Date */}
        <div className="form-group checkbox-group">
          <label>
            <input 
              type="checkbox"
              checked={useTargetDate}
              onChange={() => setUseTargetDate(!useTargetDate)}
            />
            {translate('study.target.date', 'Set Target Completion Date')}
          </label>
        </div>
        
        {useTargetDate && (
          <div className="form-group">
            <label htmlFor="target-date">
              {translate('study.target.date.select', 'Target Completion Date')}:
            </label>
            <input 
              type="date"
              id="target-date"
              value={targetDate}
              min={new Date().toISOString().split('T')[0]}
              onChange={(e) => setTargetDate(e.target.value)}
            />
          </div>
        )}
      </div>
      
      {/* Results Section */}
      {studySchedule && selectedCert && (
        <div className="results-section">
          <h2>
            <span className="icon">📅</span> 
            {translate('study.schedule', 'Study Schedule')}
          </h2>
          
          {/* Warning Message */}
          {studySchedule.status === 'warning' && (
            <div className="warning-message">
              {studySchedule.message}
              <div className="suggested">
                {translate('study.suggested.date', 'Suggested target date')}: {studySchedule.suggested_target}
              </div>
            </div>
          )}
          
          {/* Metrics */}
          <div className="metrics-grid">
            <div className="metric-card">
              <div className="metric-label">{translate('study.total.hours', 'Total Study Hours')}</div>
              <div className="metric-value">{studySchedule.total_hours_required}</div>
              <div className="metric-unit">{translate('hours', 'hours')}</div>
            </div>
            
            <div className="metric-card">
              <div className="metric-label">{translate('study.completion.time', 'Completion Time')}</div>
              <div className="metric-value">{studySchedule.weeks_required}</div>
              <div className="metric-unit">{translate('weeks', 'weeks')}</div>
            </div>
            
            <div className="metric-card">
              <div className="metric-label">{translate('study.target.completion', 'Target Completion')}</div>
              <div className="metric-value">
                {new Date(
                  Date.now() + studySchedule.weeks_required * 7 * 24 * 60 * 60 * 1000
                ).toLocaleDateString(undefined, { month: 'long', year: 'numeric' })}
              </div>
            </div>
            
            {selectedCert.cost > 0 && (
              <div className="metric-card">
                <div className="metric-label">{translate('study.cert.cost', 'Certification Cost')}</div>
                <div className="metric-value">${selectedCert.cost.toLocaleString()}</div>
              </div>
            )}
          </div>
          
          {/* Progress Tracking */}
          <div className="progress-section">
            <h3>{translate('study.progress.tracking', 'Progress Tracking')}</h3>
            <label htmlFor="progress-slider">
              {translate('study.update.progress', 'Update your progress')}:
              <span className="value-display">{progress}%</span>
            </label>
            <input 
              type="range"
              id="progress-slider"
              min="0"
              max="100"
              value={progress}
              onChange={(e) => handleProgressUpdate(Number(e.target.value))}
            />
            <div className="range-labels">
              <span>0%</span>
              <span>50%</span>
              <span>100%</span>
            </div>
            
            {progress > 0 && (
              <div className="progress-message">
                <p>
                  {translate('study.progress.message', `You're ${progress}% complete! Expected completion: `)}
                  {new Date(
                    Date.now() + (studySchedule.weeks_required * (100 - progress) / 100) * 7 * 24 * 60 * 60 * 1000
                  ).toLocaleDateString(undefined, { month: 'long', year: 'numeric' })}
                </p>
              </div>
            )}
          </div>
          
          {/* ROI Analysis */}
          <div className="roi-section">
            <div className="form-group checkbox-group">
              <label>
                <input 
                  type="checkbox"
                  checked={showROI}
                  onChange={() => setShowROI(!showROI)}
                />
                {translate('study.calculate.roi', 'Calculate Return on Investment (ROI)')}
              </label>
            </div>
            
            {showROI && (
              <>
                <div className="form-group">
                  <label htmlFor="current-salary">
                    {translate('study.current.salary', 'Current Annual Salary ($)')}:
                  </label>
                  <input 
                    type="number"
                    id="current-salary"
                    min="0"
                    step="5000"
                    value={currentSalary}
                    onChange={(e) => setCurrentSalary(Number(e.target.value))}
                  />
                </div>
                
                {roiMetrics && roiMetrics.status === 'success' && (
                  <div className="roi-metrics">
                    <h3>{translate('study.roi.analysis', 'ROI Analysis')}</h3>
                    <div className="metrics-grid">
                      <div className="metric-card">
                        <div className="metric-label">{translate('study.expected.roi', 'Expected ROI')}</div>
                        <div className="metric-value">{roiMetrics.percent_roi}%</div>
                      </div>
                      
                      <div className="metric-card">
                        <div className="metric-label">{translate('study.break.even', 'Break-even Time')}</div>
                        <div className="metric-value">{roiMetrics.break_even_months}</div>
                        <div className="metric-unit">{translate('months', 'months')}</div>
                      </div>
                      
                      <div className="metric-card">
                        <div className="metric-label">{translate('study.five.year', '5-Year Value')}</div>
                        <div className="metric-value">${roiMetrics.five_year_value.toLocaleString()}</div>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
          
          {/* Study Recommendations */}
          <div className="recommendations-section">
            <h3>{translate('study.recommendations', 'Study Recommendations')}</h3>
            
            <h4>{translate('study.weekly.schedule', 'Weekly Schedule')}</h4>
            <p>
              {translate('study.with.hours', `With ${hoursPerWeek} hours per week:`)}
            </p>
            <ul>
              <li>
                {translate('study.weekday.hours', `${Math.round((hoursPerWeek/5) * 10) / 10} hours per weekday`)}
              </li>
              <li>
                {translate('study.weekend.hours', `${Math.round((hoursPerWeek/2) * 10) / 10} hours per weekend day`)}
              </li>
            </ul>
            
            <h4>{translate('study.prerequisites', 'Prerequisites')}</h4>
            <p>
              {selectedCert.prerequisites || translate('study.no.prerequisites', 'No prerequisites required')}
            </p>
            
            <h4>{translate('study.tips', 'Tips for Success')}</h4>
            <ul className="tips-list">
              <li>{translate('study.tip.1', 'Break down study sessions into 25-minute focused intervals')}</li>
              <li>{translate('study.tip.2', 'Review material regularly to reinforce learning')}</li>
              <li>{translate('study.tip.3', 'Take practice exams in the final weeks')}</li>
              <li>{translate('study.tip.4', 'Join study groups or online communities')}</li>
              <li>{translate('study.tip.5', 'Create a dedicated study environment')}</li>
            </ul>
            
            {studySchedule.study_tips && studySchedule.study_tips.length > 0 && (
              <>
                <h4>{translate('study.additional.tips', 'Additional Tips')}</h4>
                <ul className="additional-tips">
                  {studySchedule.study_tips.map((tip, index) => (
                    <li key={index}>{tip}</li>
                  ))}
                </ul>
              </>
            )}
          </div>
          
          {/* Weekly Schedule */}
          {studySchedule.weekly_schedule && studySchedule.weekly_schedule.length > 0 && (
            <div className="weekly-schedule-section">
              <h3>{translate('study.weekly.plan', 'Weekly Study Plan')}</h3>
              <div className="accordion">
                {studySchedule.weekly_schedule.map((week) => (
                  <details key={week.week} className="accordion-item">
                    <summary className="accordion-header">
                      {translate('study.week', 'Week')} {week.week} - {week.hours_planned} {translate('hours', 'hours')}
                    </summary>
                    <div className="accordion-content">
                      <h4>{translate('study.focus.areas', 'Focus Areas')}</h4>
                      <ul>
                        {week.focus_areas.map((focus, idx) => (
                          <li key={idx}>
                            <strong>{focus.topic}</strong> ({focus.hours_suggested} {translate('hours', 'hours')})
                            <ul>
                              <li>{translate('study.resources', 'Resources')}: {focus.resources.join(', ')}</li>
                            </ul>
                          </li>
                        ))}
                      </ul>
                      
                      <h4>{translate('study.milestones', 'Milestones')}</h4>
                      <ul>
                        {week.milestones.map((milestone, idx) => (
                          <li key={idx}>
                            {milestone.description}
                            <ul>
                              <li>{translate('study.type', 'Type')}: {milestone.type}</li>
                              <li>{translate('study.target', 'Target')}: {milestone.target_date}</li>
                            </ul>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </details>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* Custom Time Estimate */}
      <div className="custom-estimate-section">
        <button 
          className="toggle-button"
          onClick={() => setIsCreatingCustomEstimate(!isCreatingCustomEstimate)}
        >
          {isCreatingCustomEstimate 
            ? translate('study.hide.custom', 'Hide Custom Estimate') 
            : translate('study.add.custom', 'Add Custom Time Estimate')}
        </button>
        
        {isCreatingCustomEstimate && (
          <div className="custom-estimate-form">
            <p>
              {translate('study.custom.description', 'Add your own time estimate for a certification based on personal experience or specific study materials.')}
            </p>
            
            <form onSubmit={handleAddCustomEstimate}>
              <div className="form-group">
                <label htmlFor="custom-cert-select">
                  {translate('study.select.certification', 'Select Certification')}:
                </label>
                <select 
                  id="custom-cert-select"
                  value={customCertId || ''}
                  onChange={(e) => setCustomCertId(Number(e.target.value))}
                >
                  {certifications.map(cert => (
                    <option key={cert.id} value={cert.id}>
                      {cert.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="form-group">
                <label htmlFor="estimated-hours">
                  {translate('study.total.hours.required', 'Total Study Hours Required')}:
                </label>
                <input 
                  type="number"
                  id="estimated-hours"
                  min="1"
                  max="1000"
                  value={estimatedHours}
                  onChange={(e) => setEstimatedHours(Number(e.target.value))}
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="study-materials">
                  {translate('study.materials.notes', 'Study Materials/Notes')}:
                </label>
                <textarea 
                  id="study-materials"
                  value={studyMaterials}
                  onChange={(e) => setStudyMaterials(e.target.value)}
                  placeholder={translate('study.optional.notes', 'Optional: Add notes about study materials or approach')}
                />
              </div>
              
              <button 
                type="submit" 
                className="submit-button"
                disabled={isLoading}
              >
                {isLoading 
                  ? translate('study.adding', 'Adding...') 
                  : translate('study.add.estimate', 'Add Estimate')}
              </button>
            </form>
          </div>
        )}
      </div>
    </div>
  );
};

export default StudyTimeTracker; 