import { cleanup, fireEvent, render, screen, waitFor, waitForElementToBeRemoved } from '@testing-library/react';
import React from 'react';
import { AuthProvider } from '../../contexts/AuthContext';
import { LanguageProvider } from '../../contexts/LanguageContext';
import api from '../../services/api';
import StudyTimeTracker from './StudyTimeTracker';

// Mock the API
jest.mock('../../services/api', () => ({
  certifications: {
    getAll: jest.fn(),
  },
  study: {
    getEstimate: jest.fn(),
    getROI: jest.fn(),
    updateProgress: jest.fn(),
  },
}));

// Mock data
const mockCertifications = [
  {
    id: 1,
    name: 'Certified Ethical Hacker (CEH)',
    difficulty: 3,
    cost: 1199,
    prerequisites: 'Basic networking knowledge',
  },
  {
    id: 2,
    name: 'CompTIA Security+',
    difficulty: 2,
    cost: 349,
    prerequisites: 'None',
  },
  {
    id: 3,
    name: 'Certified Information Systems Security Professional (CISSP)',
    difficulty: 4,
    cost: 699,
    prerequisites: '5 years of experience in information security',
  },
];

const mockStudySchedule = {
  status: 'success',
  total_hours_required: 240,
  weeks_required: 24,
  weekly_schedule: [
    {
      week: 1,
      hours_planned: 10,
      focus_areas: [
        {
          topic: 'Introduction to Ethical Hacking',
          hours_suggested: 6,
          resources: ['Course Chapter 1', 'Online Videos'],
        },
        {
          topic: 'Basic Networking Concepts',
          hours_suggested: 4,
          resources: ['Course Chapter 2'],
        },
      ],
      milestones: [
        {
          description: 'Complete introduction modules',
          type: 'Learning',
          target_date: '2023-07-10',
        },
      ],
    },
  ],
  study_tips: [
    'Focus on hands-on labs',
    'Join the official study group',
  ],
};

const mockRoiMetrics = {
  status: 'success',
  percent_roi: 250,
  break_even_months: 6,
  five_year_value: 30000,
};

// Set up global test config
jest.setTimeout(15000); // Increase timeout for async tests

// Helper to render with providers and wait for initial loading
const renderWithProviders = async (ui: React.ReactElement) => {
  const utils = render(ui, {
    wrapper: ({ children }) => (
      <AuthProvider>
        <LanguageProvider>
          {children}
        </LanguageProvider>
      </AuthProvider>
    ),
  });

  // Wait for loading state to be removed
  try {
    await waitForElementToBeRemoved(() => screen.queryByText(/loading/i), { timeout: 5000 });
  } catch (error) {
    // No loading indicator found or it disappeared too quickly, which is fine
  }

  // Wait for API call to complete
  await waitFor(() => {
    expect(api.certifications.getAll).toHaveBeenCalled();
  }, { timeout: 5000 });

  return utils;
};

describe('StudyTimeTracker Component', () => {
  // Run cleanup after each test to prevent test pollution
  afterEach(() => {
    cleanup();
    jest.clearAllMocks();
  });

  beforeEach(() => {
    // Setup default mock implementations
    (api.certifications.getAll as jest.Mock).mockResolvedValue(mockCertifications);
    (api.study.getEstimate as jest.Mock).mockResolvedValue(mockStudySchedule);
    (api.study.getROI as jest.Mock).mockResolvedValue(mockRoiMetrics);
    (api.study.updateProgress as jest.Mock).mockResolvedValue({ success: true });
  });

  test('renders loading state initially', async () => {
    // Use a delayed promise to ensure loading state is visible
    (api.certifications.getAll as jest.Mock).mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve(mockCertifications), 100))
    );

    render(<StudyTimeTracker />, {
      wrapper: ({ children }) => (
        <AuthProvider>
          <LanguageProvider>
            {children}
          </LanguageProvider>
        </AuthProvider>
      ),
    });

    // Check for loading state
    expect(screen.getByText(/loading/i)).toBeInTheDocument();

    // Wait for loading to complete
    await waitForElementToBeRemoved(() => screen.queryByText(/loading/i), { timeout: 5000 });

    // Wait for API call
    await waitFor(() => {
      expect(api.certifications.getAll).toHaveBeenCalled();
    }, { timeout: 5000 });
  });

  test('loads and displays certifications', async () => {
    await renderWithProviders(<StudyTimeTracker />);

    // Check that certification options are displayed
    await waitFor(() => {
      expect(screen.getByText('Certified Ethical Hacker (CEH)')).toBeInTheDocument();
      expect(screen.getByText('CompTIA Security+')).toBeInTheDocument();
      expect(screen.getByText('Certified Information Systems Security Professional (CISSP)')).toBeInTheDocument();
    });
  });

  test('generates study schedule when certification is selected', async () => {
    await renderWithProviders(<StudyTimeTracker />);

    // Select a certification
    const certSelect = screen.getByLabelText(/Select Certification/i);
    fireEvent.change(certSelect, { target: { value: '1' } });

    // Wait for study schedule to be generated and displayed
    await waitFor(() => {
      expect(api.study.getEstimate).toHaveBeenCalledWith(expect.objectContaining({
        certification_id: 1,
        hours_per_week: 10
      }));
    }, { timeout: 5000 });

    // Check study schedule metrics
    await waitFor(() => {
      const totalHours = screen.getByText(/Total Study Hours/i).closest('.metric-card');
      const completionTime = screen.getByText(/Completion Time/i).closest('.metric-card');

      expect(totalHours).toBeInTheDocument();
      expect(completionTime).toBeInTheDocument();
      expect(totalHours?.querySelector('.metric-value')?.textContent).toBe('240');
      expect(completionTime?.querySelector('.metric-value')?.textContent).toBe('24');
    }, { timeout: 5000 });
  });

  test('updates hours per week slider', async () => {
    await renderWithProviders(<StudyTimeTracker />);

    const hoursSlider = screen.getByLabelText(/Available Study Hours per Week/i);
    fireEvent.change(hoursSlider, { target: { value: '20' } });

    await waitFor(() => {
      expect(api.study.getEstimate).toHaveBeenCalledWith(expect.objectContaining({
        hours_per_week: 20
      }));
    }, { timeout: 5000 });
  });

  test('toggles target date input', async () => {
    await renderWithProviders(<StudyTimeTracker />);

    const targetDateCheckbox = screen.getByRole('checkbox', { name: /Set Target Completion Date/i });
    fireEvent.click(targetDateCheckbox);

    const datePicker = await waitFor(() => screen.getByLabelText(/Target Completion Date:/i), { timeout: 5000 });
    expect(datePicker).toBeInTheDocument();
    expect(datePicker).toHaveAttribute('type', 'date');

    fireEvent.click(targetDateCheckbox);

    await waitFor(() => {
      expect(screen.queryByLabelText(/Target Completion Date:/i)).not.toBeInTheDocument();
    }, { timeout: 5000 });
  });

  test('shows ROI calculator when checkbox is clicked', async () => {
    await renderWithProviders(<StudyTimeTracker />);

    // Select a certification
    const certSelect = screen.getByRole('combobox', { name: /Select Certification/i });
    fireEvent.change(certSelect, { target: { value: '1' } });

    await waitFor(() => {
      expect(api.study.getEstimate).toHaveBeenCalled();
      // Use a more specific selector to avoid ambiguity with multiple elements having the same text
      expect(screen.getByRole('heading', { name: /Study Schedule/i })).toBeInTheDocument();
    }, { timeout: 5000 });

    // Toggle ROI calculator
    const roiCheckbox = screen.getByRole('checkbox', { name: /Calculate Return on Investment/i });
    fireEvent.click(roiCheckbox);

    // Enter salary
    const salaryInput = await waitFor(() => screen.getByRole('spinbutton', { name: /Current Annual Salary/i }), { timeout: 5000 });
    expect(salaryInput).toBeInTheDocument();
    expect(salaryInput).toHaveAttribute('type', 'number');

    fireEvent.change(salaryInput, { target: { value: '75000' } });

    // Check ROI metrics
    await waitFor(() => {
      expect(api.study.getROI).toHaveBeenCalled();
    }, { timeout: 5000 });

    await waitFor(() => {
      const roiCard = screen.getByText(/Expected ROI/i).closest('.metric-card');
      const breakEvenCard = screen.getByText(/Break-even Time/i).closest('.metric-card');
      const fiveYearCard = screen.getByText(/5-Year Value/i).closest('.metric-card');

      expect(roiCard?.querySelector('.metric-value')?.textContent).toBe('250%');
      expect(breakEvenCard?.querySelector('.metric-value')?.textContent).toBe('6');
      expect(fiveYearCard?.querySelector('.metric-value')?.textContent).toBe('$30,000');
    }, { timeout: 5000 });

    // Hide ROI section
    fireEvent.click(roiCheckbox);

    await waitFor(() => {
      expect(screen.queryByRole('spinbutton', { name: /Current Annual Salary/i })).not.toBeInTheDocument();
    }, { timeout: 5000 });
  });

  test('toggling custom estimate form works', async () => {
    await renderWithProviders(<StudyTimeTracker />);

    // Find and click custom estimate button
    const customEstimateButton = await waitFor(() => screen.getByRole('button', { name: /Add Custom Time Estimate/i }), { timeout: 5000 });
    fireEvent.click(customEstimateButton);

    // Check that form appears
    const hoursInput = await waitFor(() => screen.getByRole('spinbutton', { name: /Total Study Hours Required/i }), { timeout: 5000 });
    const notesInput = await waitFor(() => screen.getByRole('textbox', { name: /Study Materials\/Notes/i }), { timeout: 5000 });
    expect(hoursInput).toBeInTheDocument();
    expect(notesInput).toBeInTheDocument();

    // Close the form
    const hideButton = await waitFor(() => screen.getByRole('button', { name: /Hide Custom Estimate/i }), { timeout: 5000 });
    fireEvent.click(hideButton);

    // Check form is hidden
    await waitFor(() => {
      expect(screen.queryByRole('spinbutton', { name: /Total Study Hours Required/i })).not.toBeInTheDocument();
    }, { timeout: 5000 });
  });

  test('submitting custom estimate works', async () => {
    await renderWithProviders(<StudyTimeTracker />);

    // Open custom estimate form
    const customEstimateButton = await waitFor(() => screen.getByText(/Add Custom Time Estimate/i), { timeout: 5000 });
    fireEvent.click(customEstimateButton);

    // Fill form
    const hoursInput = await waitFor(() => screen.getByLabelText(/Total Study Hours Required/i), { timeout: 5000 });
    fireEvent.change(hoursInput, { target: { value: 150 } });

    const notesInput = await waitFor(() => screen.getByLabelText(/Study Materials\/Notes/i), { timeout: 5000 });
    fireEvent.change(notesInput, { target: { value: 'Using official course materials' } });

    // Submit form
    const submitButton = await waitFor(() => screen.getByText(/Add Estimate/i), { timeout: 5000 });
    fireEvent.click(submitButton);

    // Verify API call
    await waitFor(() => {
      expect(api.study.updateProgress).toHaveBeenCalledWith(expect.objectContaining({
        custom_hours: 150,
        study_notes: 'Using official course materials'
      }));
    }, { timeout: 5000 });

    // Check success message
    await waitFor(() => {
      expect(screen.getByText(/Custom estimate added successfully/i)).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  test('handles API errors gracefully', async () => {
    // Mock API error
    (api.certifications.getAll as jest.Mock).mockRejectedValue(new Error('Network error'));

    render(<StudyTimeTracker />, {
      wrapper: ({ children }) => (
        <AuthProvider>
          <LanguageProvider>
            {children}
          </LanguageProvider>
        </AuthProvider>
      ),
    });

    // Check for error message
    await waitFor(() => {
      expect(screen.getByText(/Failed to load certifications/i)).toBeInTheDocument();
    }, { timeout: 5000 });
  });
}); 