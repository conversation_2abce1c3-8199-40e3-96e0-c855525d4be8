import React, { useEffect, useState } from 'react';

/**
 * Test component that intentionally throws errors to test our error handling
 * This component is used by the error-handling.spec.ts Playwright tests
 */
const TestErrorBoundary: React.FC = () => {
    // Check localStorage for the disableTestError flag set by our test
    const shouldDisableError = localStorage.getItem('disableTestError') === 'true';
    const [shouldThrowError, setShouldThrowError] = useState(!shouldDisableError);
    const [errorType, setErrorType] = useState<'render' | 'api' | 'async'>('render');

    // Listen for localStorage changes
    useEffect(() => {
        const handleStorageChange = () => {
            if (localStorage.getItem('disableTestError') === 'true') {
                setShouldThrowError(false);
            }
        };

        window.addEventListener('storage', handleStorageChange);
        return () => window.removeEventListener('storage', handleStorageChange);
    }, []);

    // This will cause a render error when shouldThrowError is true
    if (errorType === 'render' && shouldThrowError) {
        throw new Error('Intentional render error for testing');
    }

    // Function to simulate API error
    const triggerApiError = async () => {
        if (errorType === 'api' && shouldThrowError) {
            throw new Error('Intentional API error for testing');
        }
        return { success: true };
    };

    // Function to simulate async error
    const triggerAsyncError = () => {
        if (errorType === 'async' && shouldThrowError) {
            // This will cause an unhandled promise rejection
            Promise.reject(new Error('Intentional async error for testing'));
        }
    };

    return (
        <div className="test-error-boundary">
            <h1>Error Boundary Test Page</h1>

            <div className="error-controls">
                <h2>Error Controls</h2>
                <div>
                    <label>
                        <input
                            type="checkbox"
                            checked={shouldThrowError}
                            onChange={() => {
                                const newValue = !shouldThrowError;
                                setShouldThrowError(newValue);
                                // Update localStorage
                                if (!newValue) {
                                    localStorage.setItem('disableTestError', 'true');
                                } else {
                                    localStorage.removeItem('disableTestError');
                                }
                            }}
                            data-testid="toggle-error"
                        />
                        Enable Error
                    </label>
                </div>

                <div>
                    <h3>Error Type</h3>
                    <div>
                        <label>
                            <input
                                type="radio"
                                name="errorType"
                                value="render"
                                checked={errorType === 'render'}
                                onChange={() => setErrorType('render')}
                                data-testid="render-error-type"
                            />
                            Render Error
                        </label>
                    </div>

                    <div>
                        <label>
                            <input
                                type="radio"
                                name="errorType"
                                value="api"
                                checked={errorType === 'api'}
                                onChange={() => setErrorType('api')}
                                data-testid="api-error-type"
                            />
                            API Error
                        </label>
                    </div>

                    <div>
                        <label>
                            <input
                                type="radio"
                                name="errorType"
                                value="async"
                                checked={errorType === 'async'}
                                onChange={() => setErrorType('async')}
                                data-testid="async-error-type"
                            />
                            Async Error
                        </label>
                    </div>
                </div>

                <div className="action-buttons">
                    <button
                        onClick={() => triggerApiError()}
                        data-testid="trigger-api-error"
                    >
                        Trigger API Error
                    </button>

                    <button
                        onClick={triggerAsyncError}
                        data-testid="trigger-async-error"
                    >
                        Trigger Async Error
                    </button>
                </div>
            </div>

            {!shouldThrowError && (
                <div className="success-message" data-testid="success-message">
                    <h2>Success!</h2>
                    <p>The component rendered without errors.</p>
                </div>
            )}
        </div>
    );
};

export default TestErrorBoundary; 