/* Reports Styles */
.reports-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.reports-container h1 {
  margin-bottom: 20px;
  color: #333;
  font-size: 28px;
}

/* Main Layout */
.reports-content {
  display: flex;
  gap: 30px;
}

/* Filters Panel */
.filters-panel {
  flex: 0 0 250px;
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  align-self: flex-start;
  position: sticky;
  top: 20px;
}

.filters-panel h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  color: #333;
}

.filter-group {
  margin-bottom: 15px;
}

.filter-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #555;
}

.filter-group input,
.filter-group select {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.add-report-btn {
  width: 100%;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  margin-top: 15px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
}

.add-report-btn:hover {
  background-color: #2980b9;
}

/* Main Content */
.main-content {
  flex: 1;
}

.main-content h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 22px;
  color: #333;
}

/* Error Message */
.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

/* Reports List */
.reports-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.report-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.report-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.report-card h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 18px;
  color: #2c3e50;
}

.report-card-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.report-card-meta span {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: capitalize;
}

.type {
  color: white;
}

.type-certification_progress {
  background-color: #3498db;
}

.type-user_activity {
  background-color: #2ecc71;
}

.type-certification_popularity {
  background-color: #9b59b6;
}

.type-skill_gaps {
  background-color: #e74c3c;
}

.type-custom {
  background-color: #f39c12;
}

.format {
  background-color: #34495e;
  color: white;
}

.report-card-description {
  margin-bottom: 15px;
  font-size: 14px;
  color: #555;
  line-height: 1.5;
}

.report-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #7f8c8d;
}

.scheduled-badge {
  background-color: #3498db;
  color: white;
  padding: 3px 6px;
  border-radius: 10px;
  font-size: 11px;
}

/* No Results */
.no-results {
  padding: 30px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
  font-size: 16px;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
}

.pagination button {
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination button:hover:not(:disabled) {
  background-color: #e9ecef;
}

.page-info {
  font-size: 14px;
  color: #555;
}

.pagination select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

/* Report Form */
.report-form-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.report-form {
  background-color: white;
  border-radius: 8px;
  padding: 25px;
  width: 600px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.report-form h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  font-size: 22px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  font-weight: normal;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.save-btn {
  background-color: #2ecc71;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  font-weight: 600;
}

.save-btn:hover {
  background-color: #27ae60;
}

.cancel-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  font-weight: 600;
}

.cancel-btn:hover {
  background-color: #c0392b;
}

/* Report Detail */
.report-detail {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 25px;
}

.back-button {
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 20px;
  cursor: pointer;
  font-size: 14px;
  display: inline-block;
}

.back-button:hover {
  background-color: #e9ecef;
}

.report-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.report-type,
.report-format,
.report-scheduled {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 14px;
  font-weight: 600;
  text-transform: capitalize;
}

.report-scheduled {
  background-color: #3498db;
  color: white;
}

.report-description {
  margin-bottom: 25px;
  line-height: 1.6;
  color: #333;
}

.report-info {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
}

.info-item {
  font-size: 14px;
}

.info-item strong {
  display: block;
  margin-bottom: 5px;
  color: #555;
}

.report-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 25px;
}

.generate-btn,
.download-btn,
.edit-btn,
.delete-btn {
  padding: 10px 15px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  font-size: 14px;
}

.generate-btn {
  background-color: #3498db;
  color: white;
}

.generate-btn:hover:not(:disabled) {
  background-color: #2980b9;
}

.generate-btn:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.download-btn {
  background-color: #2ecc71;
  color: white;
  text-decoration: none;
  display: inline-block;
}

.download-btn:hover {
  background-color: #27ae60;
}

.edit-btn {
  background-color: #f39c12;
  color: white;
}

.edit-btn:hover {
  background-color: #d35400;
}

.delete-btn {
  background-color: #e74c3c;
  color: white;
}

.delete-btn:hover {
  background-color: #c0392b;
}

/* Loading State */
.reports-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 18px;
  color: #555;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .reports-content {
    flex-direction: column;
  }
  
  .filters-panel {
    position: static;
    width: 100%;
    margin-bottom: 20px;
  }
  
  .reports-list {
    grid-template-columns: 1fr;
  }
  
  .report-info {
    grid-template-columns: 1fr;
  }
  
  .report-actions {
    flex-direction: column;
  }
  
  .generate-btn,
  .download-btn,
  .edit-btn,
  .delete-btn {
    width: 100%;
    text-align: center;
  }
}
