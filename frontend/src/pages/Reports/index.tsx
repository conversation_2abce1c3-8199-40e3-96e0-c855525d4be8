import React, { useEffect, useState } from 'react';
import axios from 'axios';
import './styles.css';

// Define interfaces based on our API models
interface Report {
  id: number;
  title: string;
  description: string | null;
  type: string;
  parameters: Record<string, any>;
  format: string;
  is_scheduled: boolean;
  frequency: string | null;
  next_run_date: string | null;
  last_run_at: string | null;
  user_id: number | null;
  created_at: string;
  updated_at: string;
}

interface ReportListResponse {
  reports: Report[];
  total: number;
  page: number;
  page_size: number;
}

interface ReportFormData {
  title: string;
  description: string;
  type: string;
  parameters: Record<string, any>;
  format: string;
  is_scheduled: boolean;
  frequency: string | null;
  next_run_date: string | null;
}

interface FilterState {
  type: string;
  is_scheduled: string;
  frequency: string;
  search: string;
}

const Reports: React.FC = () => {
  // State for reports data
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // State for pagination
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [totalReports, setTotalReports] = useState<number>(0);
  
  // State for filters
  const [filters, setFilters] = useState<FilterState>({
    type: '',
    is_scheduled: '',
    frequency: '',
    search: ''
  });
  
  // State for form
  const [showForm, setShowForm] = useState<boolean>(false);
  const [formData, setFormData] = useState<ReportFormData>({
    title: '',
    description: '',
    type: 'certification_progress',
    parameters: {},
    format: 'pdf',
    is_scheduled: false,
    frequency: null,
    next_run_date: null
  });
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [editReportId, setEditReportId] = useState<number | null>(null);
  
  // State for report types, formats, and frequencies
  const [reportTypes, setReportTypes] = useState<string[]>([]);
  const [reportFormats, setReportFormats] = useState<string[]>([]);
  const [reportFrequencies, setReportFrequencies] = useState<string[]>([]);
  
  // State for selected report
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [generatingReport, setGeneratingReport] = useState<boolean>(false);
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null);
  
  // Fetch reports data
  useEffect(() => {
    const fetchReports = async () => {
      try {
        setLoading(true);
        
        // Calculate skip for pagination
        const skip = (page - 1) * pageSize;
        
        // Build query parameters
        const params = new URLSearchParams();
        params.append('skip', skip.toString());
        params.append('limit', pageSize.toString());
        if (filters.type) params.append('type', filters.type);
        if (filters.is_scheduled === 'true') params.append('is_scheduled', 'true');
        if (filters.is_scheduled === 'false') params.append('is_scheduled', 'false');
        if (filters.frequency) params.append('frequency', filters.frequency);
        if (filters.search) params.append('search', filters.search);
        
        const response = await axios.get<ReportListResponse>(`/api/reports?${params.toString()}`);
        setReports(response.data.reports);
        setTotalReports(response.data.total);
        setError(null);
      } catch (err) {
        console.error('Error fetching reports:', err);
        setError('Failed to load reports. Please try again later.');
        // For development, use mock data when API is not available
        const mockReports: Report[] = [
          {
            id: 1,
            title: 'Certification Progress Report',
            description: 'Monthly report on certification progress across all users',
            type: 'certification_progress',
            parameters: {},
            format: 'pdf',
            is_scheduled: true,
            frequency: 'monthly',
            next_run_date: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString(),
            last_run_at: new Date().toISOString(),
            user_id: 1,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 2,
            title: 'User Activity Report',
            description: 'Weekly report on user activity',
            type: 'user_activity',
            parameters: {},
            format: 'csv',
            is_scheduled: true,
            frequency: 'weekly',
            next_run_date: new Date(new Date().setDate(new Date().getDate() + 7)).toISOString(),
            last_run_at: new Date().toISOString(),
            user_id: 1,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ];
        setReports(mockReports);
        setTotalReports(mockReports.length);
      } finally {
        setLoading(false);
      }
    };
    
    fetchReports();
  }, [page, pageSize, filters]);
  
  // Fetch report types, formats, and frequencies
  useEffect(() => {
    const fetchReportOptions = async () => {
      try {
        // Fetch report types
        const typesResponse = await axios.get<string[]>('/api/reports/types');
        setReportTypes(typesResponse.data);
        
        // Fetch report formats
        const formatsResponse = await axios.get<string[]>('/api/reports/formats');
        setReportFormats(formatsResponse.data);
        
        // Fetch report frequencies
        const frequenciesResponse = await axios.get<string[]>('/api/reports/frequencies');
        setReportFrequencies(frequenciesResponse.data);
      } catch (err) {
        console.error('Error fetching report options:', err);
        // Set mock data for development
        setReportTypes(['certification_progress', 'user_activity', 'certification_popularity', 'skill_gaps', 'custom']);
        setReportFormats(['pdf', 'csv', 'json', 'html']);
        setReportFrequencies(['daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'once']);
      }
    };
    
    fetchReportOptions();
  }, []);
  
  // Handle filter changes
  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters((prev: FilterState) => ({ ...prev, [name]: value }));
    setPage(1); // Reset to first page when filters change
  };
  
  // Handle form input changes
  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData((prev: ReportFormData) => ({ 
        ...prev, 
        [name]: checked,
        // Reset frequency and next_run_date if is_scheduled is false
        ...(name === 'is_scheduled' && !checked ? { frequency: null, next_run_date: null } : {})
      }));
    } else {
      setFormData((prev: ReportFormData) => ({ ...prev, [name]: value }));
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (formMode === 'create') {
        // Create new report
        await axios.post('/api/reports', formData);
      } else {
        // Update existing report
        if (editReportId) {
          await axios.put(`/api/reports/${editReportId}`, formData);
        }
      }
      
      // Reset form and refresh data
      setShowForm(false);
      setFormData({
        title: '',
        description: '',
        type: 'certification_progress',
        parameters: {},
        format: 'pdf',
        is_scheduled: false,
        frequency: null,
        next_run_date: null
      });
      setEditReportId(null);
      
      // Refresh reports list
      const skip = (page - 1) * pageSize;
      const params = new URLSearchParams();
      params.append('skip', skip.toString());
      params.append('limit', pageSize.toString());
      if (filters.type) params.append('type', filters.type);
      if (filters.is_scheduled === 'true') params.append('is_scheduled', 'true');
      if (filters.is_scheduled === 'false') params.append('is_scheduled', 'false');
      if (filters.frequency) params.append('frequency', filters.frequency);
      if (filters.search) params.append('search', filters.search);
      
      const response = await axios.get<ReportListResponse>(`/api/reports?${params.toString()}`);
      setReports(response.data.reports);
      setTotalReports(response.data.total);
    } catch (err) {
      console.error('Error submitting form:', err);
      setError('Failed to save report. Please try again later.');
    }
  };
  
  // Handle edit report
  const handleEditReport = (report: Report) => {
    setFormData({
      title: report.title,
      description: report.description || '',
      type: report.type,
      parameters: report.parameters,
      format: report.format,
      is_scheduled: report.is_scheduled,
      frequency: report.frequency,
      next_run_date: report.next_run_date
    });
    setEditReportId(report.id);
    setFormMode('edit');
    setShowForm(true);
  };
  
  // Handle delete report
  const handleDeleteReport = async (reportId: number) => {
    if (window.confirm('Are you sure you want to delete this report?')) {
      try {
        await axios.delete(`/api/reports/${reportId}`);
        
        // Refresh reports list
        const skip = (page - 1) * pageSize;
        const params = new URLSearchParams();
        params.append('skip', skip.toString());
        params.append('limit', pageSize.toString());
        if (filters.type) params.append('type', filters.type);
        if (filters.is_scheduled === 'true') params.append('is_scheduled', 'true');
        if (filters.is_scheduled === 'false') params.append('is_scheduled', 'false');
        if (filters.frequency) params.append('frequency', filters.frequency);
        if (filters.search) params.append('search', filters.search);
        
        const response = await axios.get<ReportListResponse>(`/api/reports?${params.toString()}`);
        setReports(response.data.reports);
        setTotalReports(response.data.total);
      } catch (err) {
        console.error('Error deleting report:', err);
        setError('Failed to delete report. Please try again later.');
      }
    }
  };
  
  // Handle report selection
  const handleReportSelect = (report: Report) => {
    setSelectedReport(report);
  };
  
  // Handle report deselection
  const handleReportDeselect = () => {
    setSelectedReport(null);
    setDownloadUrl(null);
  };
  
  // Handle report generation
  const handleGenerateReport = async (reportId: number) => {
    try {
      setGeneratingReport(true);
      
      // Generate report
      await axios.post(`/api/reports/${reportId}/generate`);
      
      // Set download URL
      setDownloadUrl(`/api/reports/${reportId}/download`);
      
      // Refresh report data
      const response = await axios.get<Report>(`/api/reports/${reportId}`);
      setSelectedReport(response.data);
    } catch (err) {
      console.error('Error generating report:', err);
      setError('Failed to generate report. Please try again later.');
    } finally {
      setGeneratingReport(false);
    }
  };
  
  // Format date display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleString();
  };
  
  // Calculate total pages
  const totalPages = Math.ceil(totalReports / pageSize);
  
  if (loading && !reports.length) {
    return <div className="reports-loading">Loading reports...</div>;
  }
  
  return (
    <div className="reports-container">
      <h1>Reports</h1>
      
      <div className="reports-content">
        {/* Filters Panel */}
        <div className="filters-panel">
          <h2>Filters</h2>
          
          <div className="filter-group">
            <label htmlFor="search">Search:</label>
            <input
              type="text"
              id="search"
              name="search"
              value={filters.search}
              onChange={handleFilterChange}
              placeholder="Search reports..."
            />
          </div>
          
          <div className="filter-group">
            <label htmlFor="type">Report Type:</label>
            <select
              id="type"
              name="type"
              value={filters.type}
              onChange={handleFilterChange}
            >
              <option value="">All Types</option>
              {reportTypes.map(type => (
                <option key={type} value={type}>{type.replace('_', ' ')}</option>
              ))}
            </select>
          </div>
          
          <div className="filter-group">
            <label htmlFor="is_scheduled">Scheduled:</label>
            <select
              id="is_scheduled"
              name="is_scheduled"
              value={filters.is_scheduled}
              onChange={handleFilterChange}
            >
              <option value="">All</option>
              <option value="true">Yes</option>
              <option value="false">No</option>
            </select>
          </div>
          
          <div className="filter-group">
            <label htmlFor="frequency">Frequency:</label>
            <select
              id="frequency"
              name="frequency"
              value={filters.frequency}
              onChange={handleFilterChange}
              disabled={filters.is_scheduled !== 'true'}
            >
              <option value="">All Frequencies</option>
              {reportFrequencies.map(frequency => (
                <option key={frequency} value={frequency}>{frequency}</option>
              ))}
            </select>
          </div>
          
          <button 
            className="add-report-btn"
            onClick={() => {
              setFormMode('create');
              setFormData({
                title: '',
                description: '',
                type: 'certification_progress',
                parameters: {},
                format: 'pdf',
                is_scheduled: false,
                frequency: null,
                next_run_date: null
              });
              setEditReportId(null);
              setShowForm(true);
            }}
          >
            Create Report
          </button>
        </div>
        
        {/* Main Content */}
        <div className="main-content">
          {/* Error Message */}
          {error && (
            <div className="error-message">
              {error}
            </div>
          )}
          
          {/* Report Form */}
          {showForm && (
            <div className="report-form-container">
              <div className="report-form">
                <h2>{formMode === 'create' ? 'Create New Report' : 'Edit Report'}</h2>
                <form onSubmit={handleSubmit}>
                  <div className="form-group">
                    <label htmlFor="title">Title:</label>
                    <input
                      type="text"
                      id="title"
                      name="title"
                      value={formData.title}
                      onChange={handleFormChange}
                      required
                    />
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="description">Description:</label>
                    <textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleFormChange}
                      rows={3}
                    />
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="type">Report Type:</label>
                    <select
                      id="type"
                      name="type"
                      value={formData.type}
                      onChange={handleFormChange}
                      required
                    >
                      {reportTypes.map(type => (
                        <option key={type} value={type}>{type.replace('_', ' ')}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="form-group">
                    <label htmlFor="format">Format:</label>
                    <select
                      id="format"
                      name="format"
                      value={formData.format}
                      onChange={handleFormChange}
                      required
                    >
                      {reportFormats.map(format => (
                        <option key={format} value={format}>{format.toUpperCase()}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="form-group checkbox-group">
                    <label>
                      <input
                        type="checkbox"
                        name="is_scheduled"
                        checked={formData.is_scheduled}
                        onChange={handleFormChange}
                      />
                      Schedule this report
                    </label>
                  </div>
                  
                  {formData.is_scheduled && (
                    <>
                      <div className="form-group">
                        <label htmlFor="frequency">Frequency:</label>
                        <select
                          id="frequency"
                          name="frequency"
                          value={formData.frequency || ''}
                          onChange={handleFormChange}
                          required={formData.is_scheduled}
                        >
                          <option value="">Select Frequency</option>
                          {reportFrequencies.map(frequency => (
                            <option key={frequency} value={frequency}>{frequency}</option>
                          ))}
                        </select>
                      </div>
                      
                      <div className="form-group">
                        <label htmlFor="next_run_date">Next Run Date:</label>
                        <input
                          type="datetime-local"
                          id="next_run_date"
                          name="next_run_date"
                          value={formData.next_run_date ? new Date(formData.next_run_date).toISOString().slice(0, 16) : ''}
                          onChange={handleFormChange}
                          required={formData.is_scheduled}
                        />
                      </div>
                    </>
                  )}
                  
                  {formData.type === 'custom' && (
                    <div className="form-group">
                      <label htmlFor="parameters">Custom Parameters (JSON):</label>
                      <textarea
                        id="parameters"
                        name="parameters"
                        value={JSON.stringify(formData.parameters, null, 2)}
                        onChange={(e) => {
                          try {
                            const params = JSON.parse(e.target.value);
                            setFormData((prev: ReportFormData) => ({ ...prev, parameters: params }));
                          } catch (err) {
                            // Don't update if JSON is invalid
                          }
                        }}
                        rows={5}
                      />
                    </div>
                  )}
                  
                  <div className="form-actions">
                    <button type="submit" className="save-btn">
                      {formMode === 'create' ? 'Create Report' : 'Update Report'}
                    </button>
                    <button 
                      type="button" 
                      className="cancel-btn"
                      onClick={() => setShowForm(false)}
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
          
          {/* Selected Report Detail */}
          {selectedReport ? (
            <div className="report-detail">
              <button 
                className="back-button"
                onClick={handleReportDeselect}
              >
                ← Back to List
              </button>
              
              <h2>{selectedReport.title}</h2>
              <div className="report-meta">
                <span className={`report-type type-${selectedReport.type}`}>
                  {selectedReport.type.replace('_', ' ')}
                </span>
                <span className={`report-format format-${selectedReport.format}`}>
                  {selectedReport.format.toUpperCase()}
                </span>
                {selectedReport.is_scheduled && (
                  <span className="report-scheduled">
                    Scheduled ({selectedReport.frequency})
                  </span>
                )}
              </div>
              
              {selectedReport.description && (
                <div className="report-description">
                  <p>{selectedReport.description}</p>
                </div>
              )}
              
              <div className="report-info">
                <div className="info-item">
                  <strong>Created:</strong> {formatDate(selectedReport.created_at)}
                </div>
                {selectedReport.last_run_at && (
                  <div className="info-item">
                    <strong>Last Generated:</strong> {formatDate(selectedReport.last_run_at)}
                  </div>
                )}
                {selectedReport.is_scheduled && selectedReport.next_run_date && (
                  <div className="info-item">
                    <strong>Next Run:</strong> {formatDate(selectedReport.next_run_date)}
                  </div>
                )}
              </div>
              
              <div className="report-actions">
                <button 
                  className="generate-btn"
                  onClick={() => handleGenerateReport(selectedReport.id)}
                  disabled={generatingReport}
                >
                  {generatingReport ? 'Generating...' : 'Generate Report'}
                </button>
                
                {downloadUrl && (
                  <a 
                    href={downloadUrl}
                    className="download-btn"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Download Report
                  </a>
                )}
                
                <button 
                  className="edit-btn"
                  onClick={() => handleEditReport(selectedReport)}
                >
                  Edit Report
                </button>
                
                <button 
                  className="delete-btn"
                  onClick={() => {
                    handleDeleteReport(selectedReport.id);
                    handleReportDeselect();
                  }}
                >
                  Delete Report
                </button>
              </div>
            </div>
          ) : (
            /* Reports List */
            <>
              <h2>Available Reports</h2>
              
              {reports.length === 0 ? (
                <div className="no-results">
                  No reports found matching your criteria.
                </div>
              ) : (
                <div className="reports-list">
                  {reports.map(report => (
                    <div 
                      key={report.id} 
                      className="report-card"
                      onClick={() => handleReportSelect(report)}
                    >
                      <h3>{report.title}</h3>
                      <div className="report-card-meta">
                        <span className={`type type-${report.type}`}>{report.type.replace('_', ' ')}</span>
                        <span className={`format format-${report.format}`}>{report.format.toUpperCase()}</span>
                      </div>
                      <p className="report-card-description">
                        {report.description && report.description.length > 100
                          ? `${report.description.substring(0, 100)}...`
                          : report.description}
                      </p>
                      <div className="report-card-footer">
                        {report.is_scheduled && (
                          <span className="scheduled-badge">
                            Scheduled ({report.frequency})
                          </span>
                        )}
                        <span className="last-run">
                          {report.last_run_at 
                            ? `Last run: ${new Date(report.last_run_at).toLocaleDateString()}`
                            : 'Never generated'}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {/* Pagination */}
              <div className="pagination">
                <button 
                  onClick={() => setPage((p: number) => Math.max(1, p - 1))}
                  disabled={page === 1}
                >
                  Previous
                </button>
                
                <span className="page-info">
                  Page {page} of {totalPages}
                </span>
                
                <button 
                  onClick={() => setPage((p: number) => Math.min(totalPages, p + 1))}
                  disabled={page === totalPages}
                >
                  Next
                </button>
                
                <select
                  value={pageSize}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                    setPageSize(Number(e.target.value));
                    setPage(1); // Reset to first page when page size changes
                  }}
                >
                  <option value="5">5 per page</option>
                  <option value="10">10 per page</option>
                  <option value="20">20 per page</option>
                  <option value="50">50 per page</option>
                </select>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Reports;
