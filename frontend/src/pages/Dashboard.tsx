import React, { useEffect, useState } from 'react';
import axios from 'axios';

interface Certification {
  id: number;
  name: string;
  organization: string;
}

const Dashboard: React.FC = () => {
  const [certifications, setCertifications] = useState<Certification[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCertifications = async () => {
      try {
        setLoading(true);
        // This will be replaced with actual API endpoint once implemented
        const response = await axios.get('/api/certifications');
        setCertifications(response.data);
        setError(null);
      } catch (err) {
        console.error('Error fetching certifications:', err);
        setError('Failed to load certifications. Please try again later.');
        // For development, use mock data when API is not available
        setCertifications([
          { id: 1, name: 'CISSP', organization: 'ISC2' },
          { id: 2, name: 'CEH', organization: 'EC-Council' },
          { id: 3, name: 'Security+', organization: 'CompTIA' }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchCertifications();
  }, []);

  if (loading) {
    return <div>Loading certifications...</div>;
  }

  if (error) {
    return (
      <div>
        <p>{error}</p>
        <h2>Sample Certifications (Mock Data)</h2>
        <ul>
          {certifications.map(cert => (
            <li key={cert.id}>
              {cert.name} - {cert.organization}
            </li>
          ))}
        </ul>
      </div>
    );
  }

  return (
    <div>
      <h1>Security Certification Dashboard</h1>
      <p>This is the new React-based dashboard that will replace the Streamlit interface.</p>
      
      <h2>Available Certifications</h2>
      {certifications.length === 0 ? (
        <p>No certifications found.</p>
      ) : (
        <ul>
          {certifications.map(cert => (
            <li key={cert.id}>
              {cert.name} - {cert.organization}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default Dashboard; 