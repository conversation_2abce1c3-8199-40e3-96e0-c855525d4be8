import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import './MainPage.css';

const MainPage: React.FC = () => {
    const { translate } = useLanguage();
    const { isAuthenticated } = useAuth();

    return (
        <div className="main-page">
            {/* Hero Section */}
            <section className="hero-section">
                <div className="hero-content">
                    <h1>{translate('main.hero.title', 'Navigate Your Cybersecurity Career Path')}</h1>
                    <h2>{translate('main.hero.subtitle', 'Find, Plan, and Achieve the Right Certifications for Your Career Goals')}</h2>
                    <div className="hero-cta">
                        {!isAuthenticated ? (
                            <Link to="/login" className="cta-button primary">
                                {translate('main.hero.cta.start', 'Start Your Journey')}
                            </Link>
                        ) : (
                            <Link to="/dashboard" className="cta-button primary">
                                {translate('main.hero.cta.dashboard', 'Go to Dashboard')}
                            </Link>
                        )}
                        <Link to="/certification-explorer" className="cta-button secondary">
                            {translate('main.hero.cta.explore', 'Explore Certifications')}
                        </Link>
                    </div>
                </div>
                <div className="hero-visual">
                    {/* Placeholder for hero visualization */}
                    <div className="certification-path-visual">
                        {/* This will be replaced with an actual visualization component */}
                        <div className="visualization-placeholder">
                            {translate('main.hero.visual.placeholder', 'Career Path Visualization')}
                        </div>
                    </div>
                </div>
            </section>

            {/* Dashboard Preview Section */}
            <section className="dashboard-preview-section">
                <h2>{translate('main.dashboard.title', 'Track Your Certification Journey')}</h2>
                <p>{translate('main.dashboard.description', 'Monitor your progress, study time, and ROI with our comprehensive dashboard')}</p>
                <div className="dashboard-preview">
                    {/* Dashboard preview visualization */}
                    <div className="dashboard-preview-placeholder">
                        {translate('main.dashboard.placeholder', 'Dashboard Preview')}
                    </div>
                    <div className="dashboard-features">
                        <div className="feature">
                            <h3>{translate('main.dashboard.feature1.title', 'Progress Tracking')}</h3>
                            <p>{translate('main.dashboard.feature1.description', 'Track completion percentage across all certifications')}</p>
                        </div>
                        <div className="feature">
                            <h3>{translate('main.dashboard.feature2.title', 'Study Time Management')}</h3>
                            <p>{translate('main.dashboard.feature2.description', 'Optimize your study schedule and monitor hours invested')}</p>
                        </div>
                        <div className="feature">
                            <h3>{translate('main.dashboard.feature3.title', 'ROI Calculator')}</h3>
                            <p>{translate('main.dashboard.feature3.description', 'Calculate the return on investment for each certification')}</p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Career Path Navigator Section */}
            <section className="career-path-section">
                <h2>{translate('main.careerPath.title', 'Visualize Your Career Path')}</h2>
                <p>{translate('main.careerPath.description', 'Discover certification paths aligned with your career goals')}</p>
                <div className="career-path-preview">
                    {/* Career path visualization preview */}
                    <div className="career-path-placeholder">
                        {translate('main.careerPath.placeholder', 'Career Path Visualization')}
                    </div>
                    <div className="popular-paths">
                        <h3>{translate('main.careerPath.popular.title', 'Popular Career Paths')}</h3>
                        <ul>
                            <li>
                                <Link to="/career-path?path=security-analyst">
                                    {translate('main.careerPath.popular.path1', 'Security Analyst')}
                                </Link>
                            </li>
                            <li>
                                <Link to="/career-path?path=penetration-tester">
                                    {translate('main.careerPath.popular.path2', 'Penetration Tester')}
                                </Link>
                            </li>
                            <li>
                                <Link to="/career-path?path=security-architect">
                                    {translate('main.careerPath.popular.path3', 'Security Architect')}
                                </Link>
                            </li>
                            <li>
                                <Link to="/career-path?path=cloud-security">
                                    {translate('main.careerPath.popular.path4', 'Cloud Security Specialist')}
                                </Link>
                            </li>
                        </ul>
                    </div>
                </div>
            </section>

            {/* Certification Catalog Section */}
            <section className="certification-catalog-section">
                <h2>{translate('main.catalog.title', 'Explore Certifications')}</h2>
                <p>{translate('main.catalog.description', 'Browse our comprehensive catalog of security certifications')}</p>
                <div className="certification-filters">
                    <div className="filter-group">
                        <h4>{translate('main.catalog.filter.domain', 'Domain')}</h4>
                        <div className="filter-options">
                            <span className="filter-option active">{translate('main.catalog.filter.all', 'All')}</span>
                            <span className="filter-option">{translate('main.catalog.filter.network', 'Network')}</span>
                            <span className="filter-option">{translate('main.catalog.filter.cloud', 'Cloud')}</span>
                            <span className="filter-option">{translate('main.catalog.filter.app', 'Application')}</span>
                        </div>
                    </div>
                    <div className="filter-group">
                        <h4>{translate('main.catalog.filter.level', 'Level')}</h4>
                        <div className="filter-options">
                            <span className="filter-option active">{translate('main.catalog.filter.all', 'All')}</span>
                            <span className="filter-option">{translate('main.catalog.filter.beginner', 'Beginner')}</span>
                            <span className="filter-option">{translate('main.catalog.filter.intermediate', 'Intermediate')}</span>
                            <span className="filter-option">{translate('main.catalog.filter.advanced', 'Advanced')}</span>
                        </div>
                    </div>
                </div>
                <div className="certification-previews">
                    {/* Placeholder for certification cards */}
                    <div className="certification-card">
                        <h3>CompTIA Security+</h3>
                        <div className="certification-details">
                            <span className="cert-level">Beginner</span>
                            <span className="cert-cost">$370</span>
                        </div>
                        <p>Foundation-level security certification covering network security, compliance, and risk management.</p>
                    </div>
                    <div className="certification-card">
                        <h3>CISSP</h3>
                        <div className="certification-details">
                            <span className="cert-level">Advanced</span>
                            <span className="cert-cost">$749</span>
                        </div>
                        <p>Advanced certification for security professionals with extensive experience.</p>
                    </div>
                    <div className="certification-card">
                        <h3>AWS Certified Security</h3>
                        <div className="certification-details">
                            <span className="cert-level">Intermediate</span>
                            <span className="cert-cost">$300</span>
                        </div>
                        <p>Specialized certification for securing workloads and data in AWS environments.</p>
                    </div>
                </div>
                <div className="view-all-link">
                    <Link to="/certification-explorer">
                        {translate('main.catalog.viewAll', 'View All Certifications')}
                    </Link>
                </div>
            </section>

            {/* Success Stories Section */}
            <section className="success-stories-section">
                <h2>{translate('main.stories.title', 'Success Stories')}</h2>
                <p>{translate('main.stories.description', 'Real outcomes from security professionals using our platform')}</p>
                <div className="stories-container">
                    <div className="success-story">
                        <div className="story-header">
                            <div className="user-avatar">
                                {/* User image placeholder */}
                                <div className="avatar-placeholder">JD</div>
                            </div>
                            <div className="user-info">
                                <h3>John Doe</h3>
                                <p>Security Analyst → Security Engineer</p>
                            </div>
                        </div>
                        <p className="story-content">
                            "The certification roadmap helped me identify the exact certifications I needed to advance my career.
                            Within 8 months, I earned my CISSP and landed a senior security engineer position with a 30% salary increase."
                        </p>
                        <div className="story-metrics">
                            <div className="metric">
                                <span className="metric-value">3</span>
                                <span className="metric-label">Certifications</span>
                            </div>
                            <div className="metric">
                                <span className="metric-value">30%</span>
                                <span className="metric-label">Salary Increase</span>
                            </div>
                            <div className="metric">
                                <span className="metric-value">8</span>
                                <span className="metric-label">Months</span>
                            </div>
                        </div>
                    </div>
                    <div className="success-story">
                        <div className="story-header">
                            <div className="user-avatar">
                                {/* User image placeholder */}
                                <div className="avatar-placeholder">JS</div>
                            </div>
                            <div className="user-info">
                                <h3>Jane Smith</h3>
                                <p>IT Support → Penetration Tester</p>
                            </div>
                        </div>
                        <p className="story-content">
                            "I wanted to transition to offensive security but didn't know where to start.
                            The platform guided me through the right certifications and study materials.
                            I'm now working as a junior penetration tester!"
                        </p>
                        <div className="story-metrics">
                            <div className="metric">
                                <span className="metric-value">4</span>
                                <span className="metric-label">Certifications</span>
                            </div>
                            <div className="metric">
                                <span className="metric-value">45%</span>
                                <span className="metric-label">Salary Increase</span>
                            </div>
                            <div className="metric">
                                <span className="metric-value">14</span>
                                <span className="metric-label">Months</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Getting Started Guide Section */}
            <section className="getting-started-section">
                <h2>{translate('main.gettingStarted.title', 'How to Get Started')}</h2>
                <p>{translate('main.gettingStarted.description', 'Begin your certification journey in three easy steps')}</p>
                <div className="steps-container">
                    <div className="step">
                        <div className="step-number">1</div>
                        <h3>{translate('main.gettingStarted.step1.title', 'Create Your Profile')}</h3>
                        <p>{translate('main.gettingStarted.step1.description', 'Tell us about your current skills, experience, and career goals')}</p>
                    </div>
                    <div className="step">
                        <div className="step-number">2</div>
                        <h3>{translate('main.gettingStarted.step2.title', 'Discover Your Path')}</h3>
                        <p>{translate('main.gettingStarted.step2.description', 'Get personalized certification recommendations based on your goals')}</p>
                    </div>
                    <div className="step">
                        <div className="step-number">3</div>
                        <h3>{translate('main.gettingStarted.step3.title', 'Track Your Progress')}</h3>
                        <p>{translate('main.gettingStarted.step3.description', 'Monitor your study time, preparation, and certification achievements')}</p>
                    </div>
                </div>
                <div className="getting-started-cta">
                    <Link to={isAuthenticated ? "/dashboard" : "/signup"} className="cta-button primary">
                        {isAuthenticated
                            ? translate('main.gettingStarted.cta.dashboard', 'Go to Dashboard')
                            : translate('main.gettingStarted.cta.start', 'Start Now')}
                    </Link>
                    <Link to="/tutorial" className="cta-button secondary">
                        {translate('main.gettingStarted.cta.tutorial', 'Take a Tour')}
                    </Link>
                </div>
            </section>
        </div>
    );
};

export default MainPage; 