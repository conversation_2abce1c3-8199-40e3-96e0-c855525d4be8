/* Main Page Styles */
.main-page {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.main-page section {
  margin: 80px 0;
  padding: 20px 0;
}

.main-page h1 {
  font-size: 48px;
  font-weight: 800;
  margin-bottom: 16px;
  color: #1a1a2e;
  line-height: 1.2;
}

.main-page h2 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 16px;
  color: #1a1a2e;
}

.main-page h3 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #16213e;
}

.main-page h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #16213e;
}

.main-page p {
  font-size: 18px;
  line-height: 1.6;
  color: #4a4a68;
  margin-bottom: 24px;
}

/* Hero Section */
.hero-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  min-height: 600px;
  padding: 40px 0;
}

.hero-content {
  flex: 1;
  max-width: 550px;
}

.hero-content h1 {
  color: #0f3460;
}

.hero-content h2 {
  font-size: 24px;
  font-weight: 400;
  margin-bottom: 32px;
  color: #4a4a68;
}

.hero-visual {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 550px;
}

.certification-path-visual {
  width: 100%;
  height: 400px;
  background-color: #f5f7fb;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.06);
}

.visualization-placeholder {
  font-size: 18px;
  color: #8d8d9d;
  text-align: center;
}

.hero-cta {
  display: flex;
  gap: 16px;
  margin-top: 32px;
}

.cta-button {
  padding: 14px 28px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
}

.cta-button.primary {
  background-color: #0f3460;
  color: white;
  border: 2px solid #0f3460;
}

.cta-button.primary:hover {
  background-color: #0a2549;
  border-color: #0a2549;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(15, 52, 96, 0.3);
}

.cta-button.secondary {
  background-color: transparent;
  color: #0f3460;
  border: 2px solid #0f3460;
}

.cta-button.secondary:hover {
  background-color: rgba(15, 52, 96, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(15, 52, 96, 0.1);
}

/* Dashboard Preview Section */
.dashboard-preview-section {
  text-align: center;
}

.dashboard-preview {
  margin-top: 40px;
}

.dashboard-preview-placeholder {
  width: 100%;
  height: 300px;
  background-color: #f5f7fb;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.06);
}

.dashboard-features {
  display: flex;
  justify-content: space-between;
  gap: 24px;
  margin-top: 30px;
}

.feature {
  flex: 1;
  padding: 24px;
  background-color: #f8f9fa;
  border-radius: 8px;
  text-align: left;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.feature h3 {
  font-size: 20px;
  margin-bottom: 8px;
  color: #0f3460;
}

.feature p {
  font-size: 16px;
  color: #4a4a68;
  margin: 0;
}

/* Career Path Section */
.career-path-section {
  text-align: center;
}

.career-path-preview {
  display: flex;
  gap: 40px;
  margin-top: 40px;
}

.career-path-placeholder {
  flex: 2;
  height: 350px;
  background-color: #f5f7fb;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.06);
}

.popular-paths {
  flex: 1;
  text-align: left;
}

.popular-paths h3 {
  margin-bottom: 20px;
  color: #0f3460;
}

.popular-paths ul {
  list-style: none;
  padding: 0;
}

.popular-paths li {
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.popular-paths li:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.popular-paths a {
  text-decoration: none;
  color: #16213e;
  font-weight: 500;
  display: block;
}

/* Certification Catalog Section */
.certification-catalog-section {
  text-align: center;
}

.certification-filters {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin: 30px 0;
}

.filter-group {
  text-align: left;
}

.filter-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-option {
  padding: 6px 12px;
  background-color: #f5f7fb;
  border-radius: 20px;
  font-size: 14px;
  color: #4a4a68;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-option:hover, .filter-option.active {
  background-color: #0f3460;
  color: white;
}

.certification-previews {
  display: flex;
  justify-content: space-between;
  gap: 24px;
  margin-top: 40px;
}

.certification-card {
  flex: 1;
  padding: 24px;
  background-color: white;
  border-radius: 12px;
  text-align: left;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.certification-card:hover {
  transform: translateY(-5px);
}

.certification-card h3 {
  margin-bottom: 12px;
  color: #0f3460;
}

.certification-details {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.cert-level, .cert-cost {
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.cert-level {
  background-color: #e6f7ff;
  color: #0083ca;
}

.cert-cost {
  background-color: #fff3e0;
  color: #ff8f00;
}

.certification-card p {
  font-size: 14px;
  margin: 0;
}

.view-all-link {
  margin-top: 30px;
}

.view-all-link a {
  color: #0f3460;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
}

.view-all-link a:hover {
  text-decoration: underline;
}

/* Success Stories Section */
.success-stories-section {
  text-align: center;
}

.stories-container {
  display: flex;
  gap: 30px;
  margin-top: 40px;
}

.success-story {
  flex: 1;
  padding: 30px;
  background-color: white;
  border-radius: 12px;
  text-align: left;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
}

.story-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #0f3460;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-placeholder {
  color: white;
  font-weight: 600;
}

.user-info h3 {
  margin: 0;
  font-size: 20px;
}

.user-info p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.story-content {
  font-style: italic;
  margin-bottom: 24px;
  line-height: 1.5;
}

.story-metrics {
  display: flex;
  justify-content: space-between;
  text-align: center;
  background-color: #f5f7fb;
  padding: 16px;
  border-radius: 8px;
}

.metric {
  display: flex;
  flex-direction: column;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #0f3460;
}

.metric-label {
  font-size: 12px;
  color: #666;
}

/* Getting Started Section */
.getting-started-section {
  text-align: center;
}

.steps-container {
  display: flex;
  justify-content: space-between;
  gap: 30px;
  margin: 40px 0;
}

.step {
  flex: 1;
  position: relative;
  padding: 30px;
  background-color: white;
  border-radius: 12px;
  text-align: left;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
}

.step-number {
  position: absolute;
  top: -20px;
  left: 30px;
  width: 40px;
  height: 40px;
  background-color: #0f3460;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  font-weight: 700;
}

.step h3 {
  margin-top: 10px;
  color: #0f3460;
}

.step p {
  margin: 0;
  font-size: 16px;
}

.getting-started-cta {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 40px;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .hero-section {
    flex-direction: column;
    min-height: auto;
  }

  .hero-content, .hero-visual {
    max-width: 100%;
    text-align: center;
  }

  .hero-cta {
    justify-content: center;
  }

  .career-path-preview {
    flex-direction: column;
  }

  .certification-previews {
    flex-direction: column;
  }

  .stories-container {
    flex-direction: column;
  }

  .steps-container {
    flex-direction: column;
  }

  .dashboard-features {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .main-page h1 {
    font-size: 36px;
  }

  .main-page h2 {
    font-size: 28px;
  }

  .main-page section {
    margin: 60px 0;
  }

  .certification-filters {
    flex-direction: column;
    align-items: center;
  }

  .hero-cta {
    flex-direction: column;
  }

  .cta-button {
    width: 100%;
  }

  .getting-started-cta {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .main-page h1 {
    font-size: 32px;
  }

  .main-page h2 {
    font-size: 24px;
  }

  .main-page section {
    margin: 40px 0;
  }

  .hero-content h2 {
    font-size: 18px;
  }

  .certification-path-visual,
  .dashboard-preview-placeholder,
  .career-path-placeholder {
    height: 250px;
  }
} 