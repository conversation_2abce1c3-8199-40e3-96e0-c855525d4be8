import axios from 'axios';
import React, { useEffect, useState } from 'react';
import CertificationManagement from './components/CertificationManagement';
import FeedbackManagement from './components/FeedbackManagement';
import OrganizationManagement from './components/OrganizationManagement';
import TranslationManagement from './components/TranslationManagement';
import './styles.css';

// Define interfaces based on our API models
interface AdminSetting {
  id: number;
  key: string;
  value: any;
  description: string | null;
  category: string;
  created_at: string;
  updated_at: string | null;
}

interface SystemStatusComponent {
  component: string;
  status: string;
  message: string | null;
  last_checked: string;
}

interface SystemStatus {
  status: string;
  components: SystemStatusComponent[];
  resources: {
    cpu: {
      percent: number;
      cores: number;
    };
    memory: {
      total: number;
      available: number;
      percent: number;
    };
    disk: {
      total: number;
      free: number;
      percent: number;
    };
    platform: string;
  };
  uptime: number;
}

interface AuditLog {
  id: number;
  user_id: number | null;
  action: string;
  resource_type: string;
  resource_id: string | null;
  details: any;
  ip_address: string | null;
  timestamp: string;
}

interface SettingFormData {
  key: string;
  value: any;
  description: string;
  category: string;
}

const AdminInterface: React.FC = () => {
  // State for settings
  const [settings, setSettings] = useState<AdminSetting[]>([]);
  const [filteredSettings, setFilteredSettings] = useState<AdminSetting[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [categories, setCategories] = useState<string[]>([]);

  // State for system status
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);

  // State for audit logs
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [auditLogPage, setAuditLogPage] = useState<number>(1);
  const [auditLogPageSize, setAuditLogPageSize] = useState<number>(10);
  const [totalAuditLogs, setTotalAuditLogs] = useState<number>(0);

  // UI state
  const [activeTab, setActiveTab] = useState<string>('settings');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showSettingForm, setShowSettingForm] = useState<boolean>(false);
  const [editSettingKey, setEditSettingKey] = useState<string | null>(null);

  // Form state
  const [settingForm, setSettingForm] = useState<SettingFormData>({
    key: '',
    value: '',
    description: '',
    category: ''
  });

  // Fetch settings
  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/admin/settings');
      setSettings(response.data);

      // Extract unique categories
      const uniqueCategories = Array.from(
        new Set(response.data.map((setting: AdminSetting) => setting.category))
      ) as string[];
      setCategories(uniqueCategories);

      setError(null);
    } catch (err) {
      console.error('Error fetching settings:', err);
      setError('Failed to load settings. Please try again later.');
      // Mock data for development
      const mockSettings = [
        {
          id: 1,
          key: 'site_name',
          value: 'CertRats',
          description: 'Site name displayed in the header',
          category: 'general',
          created_at: new Date().toISOString(),
          updated_at: null
        },
        {
          id: 2,
          key: 'maintenance_mode',
          value: false,
          description: 'Enable maintenance mode',
          category: 'system',
          created_at: new Date().toISOString(),
          updated_at: null
        },
        {
          id: 3,
          key: 'default_page_size',
          value: 10,
          description: 'Default number of items per page',
          category: 'display',
          created_at: new Date().toISOString(),
          updated_at: null
        }
      ];
      setSettings(mockSettings);
      setCategories(['general', 'system', 'display']);
    } finally {
      setLoading(false);
    }
  };

  // Fetch system status
  const fetchSystemStatus = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/admin/system/status');
      setSystemStatus(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching system status:', err);
      setError('Failed to load system status. Please try again later.');
      // Mock data for development
      setSystemStatus({
        status: 'operational',
        components: [
          {
            component: 'Database',
            status: 'healthy',
            message: 'Connected',
            last_checked: new Date().toISOString()
          },
          {
            component: 'API',
            status: 'healthy',
            message: 'Running',
            last_checked: new Date().toISOString()
          },
          {
            component: 'Frontend',
            status: 'healthy',
            message: 'Serving',
            last_checked: new Date().toISOString()
          }
        ],
        resources: {
          cpu: {
            percent: 25.5,
            cores: 8
          },
          memory: {
            total: 16000000000,
            available: **********,
            percent: 50.0
          },
          disk: {
            total: 500000000000,
            free: 250000000000,
            percent: 50.0
          },
          platform: 'Linux-5.4.0-x86_64-with-glibc2.29'
        },
        uptime: 3600
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch audit logs
  const fetchAuditLogs = async () => {
    try {
      setLoading(true);
      const skip = (auditLogPage - 1) * auditLogPageSize;
      const response = await axios.get(`/api/admin/audit-logs?skip=${skip}&limit=${auditLogPageSize}`);
      setAuditLogs(response.data);
      setTotalAuditLogs(response.data.total || response.data.length);
      setError(null);
    } catch (err) {
      console.error('Error fetching audit logs:', err);
      setError('Failed to load audit logs. Please try again later.');
      // Mock data for development
      const mockLogs = [
        {
          id: 1,
          user_id: 1,
          action: 'create',
          resource_type: 'user',
          resource_id: '2',
          details: { username: 'newuser' },
          ip_address: '***********',
          timestamp: new Date().toISOString()
        },
        {
          id: 2,
          user_id: 1,
          action: 'update',
          resource_type: 'setting',
          resource_id: 'site_name',
          details: { old_value: 'CertRats', new_value: 'Updated CertRats' },
          ip_address: '***********',
          timestamp: new Date(Date.now() - 3600000).toISOString()
        }
      ];
      setAuditLogs(mockLogs);
      setTotalAuditLogs(mockLogs.length);
    } finally {
      setLoading(false);
    }
  };

  // Create or update setting
  const saveSetting = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);

      if (editSettingKey) {
        // Update existing setting
        await axios.put(`/api/admin/settings/${editSettingKey}`, {
          value: settingForm.value,
          description: settingForm.description,
          category: settingForm.category
        });
      } else {
        // Create new setting
        await axios.post('/api/admin/settings', settingForm);
      }

      // Refresh settings
      await fetchSettings();

      // Reset form
      setSettingForm({
        key: '',
        value: '',
        description: '',
        category: ''
      });
      setShowSettingForm(false);
      setEditSettingKey(null);

    } catch (err) {
      console.error('Error saving setting:', err);
      setError('Failed to save setting. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Delete setting
  const deleteSetting = async (key: string) => {
    if (!window.confirm(`Are you sure you want to delete the setting "${key}"?`)) {
      return;
    }

    try {
      setLoading(true);
      await axios.delete(`/api/admin/settings/${key}`);

      // Refresh settings
      await fetchSettings();

    } catch (err) {
      console.error('Error deleting setting:', err);
      setError('Failed to delete setting. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Edit setting
  const handleEditSetting = (setting: AdminSetting) => {
    setSettingForm({
      key: setting.key,
      value: setting.value,
      description: setting.description || '',
      category: setting.category
    });
    setEditSettingKey(setting.key);
    setShowSettingForm(true);
  };

  // Format uptime
  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    return `${days}d ${hours}h ${minutes}m`;
  };

  // Format bytes
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Filter settings by category
  useEffect(() => {
    if (selectedCategory === 'all') {
      setFilteredSettings(settings);
    } else {
      setFilteredSettings(settings.filter(setting => setting.category === selectedCategory));
    }
  }, [selectedCategory, settings]);

  // Load data based on active tab
  useEffect(() => {
    if (activeTab === 'settings') {
      fetchSettings();
    } else if (activeTab === 'system') {
      fetchSystemStatus();
    } else if (activeTab === 'audit') {
      fetchAuditLogs();
    }
  }, [activeTab, auditLogPage, auditLogPageSize]);

  // Handle form change
  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setSettingForm(prev => ({ ...prev, [name]: value }));
  };

  // Handle admin logout
  const handleLogout = async () => {
    try {
      await axios.post('/api/admin/logout');
      // Reset authentication state
      window.location.reload();
    } catch (error) {
      console.error('Error logging out:', error);
      setError('Failed to logout. Please try again.');
    }
  };

  // Render loading state
  if (loading && !settings.length && !systemStatus && !auditLogs.length) {
    return <div className="admin-loading">Loading admin interface...</div>;
  }

  return (
    <div className="admin-container">
      <div className="admin-header">
        <h1>Admin Dashboard</h1>
        <button className="logout-button" onClick={handleLogout}>Logout</button>
      </div>

      <div className="breadcrumb">Home &gt; Admin</div>

      {loading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading...</p>
        </div>
      ) : (
        <>
          <div className="admin-tabs">
            <div
              className={`tab ${activeTab === 'settings' ? 'active' : ''}`}
              onClick={() => setActiveTab('settings')}
            >
              Settings
            </div>
            <div
              className={`tab ${activeTab === 'status' ? 'active' : ''}`}
              onClick={() => setActiveTab('status')}
            >
              System Status
            </div>
            <div
              className={`tab ${activeTab === 'audit' ? 'active' : ''}`}
              onClick={() => setActiveTab('audit')}
            >
              Audit Logs
            </div>
            <div
              className={`tab ${activeTab === 'certifications' ? 'active' : ''}`}
              onClick={() => setActiveTab('certifications')}
            >
              Certifications
            </div>
            <div
              className={`tab ${activeTab === 'organizations' ? 'active' : ''}`}
              onClick={() => setActiveTab('organizations')}
            >
              Organizations
            </div>
            <div
              className={`tab ${activeTab === 'feedback' ? 'active' : ''}`}
              onClick={() => setActiveTab('feedback')}
            >
              Feedback
            </div>
            <div
              className={`tab ${activeTab === 'translations' ? 'active' : ''}`}
              onClick={() => setActiveTab('translations')}
            >
              Translations
            </div>
          </div>

          <div className="tab-content">
            {activeTab === 'settings' && (
              <div className="settings-tab">
                <div className="settings-header">
                  <h2>System Settings</h2>
                  <button
                    className="add-setting-btn"
                    onClick={() => {
                      setSettingForm({
                        key: '',
                        value: '',
                        description: '',
                        category: categories[0] || 'general'
                      });
                      setEditSettingKey(null);
                      setShowSettingForm(true);
                    }}
                  >
                    Add Setting
                  </button>
                </div>

                {/* Category Filter */}
                <div className="category-filter">
                  <label htmlFor="category-select">Filter by Category:</label>
                  <select
                    id="category-select"
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                  >
                    <option value="all">All Categories</option>
                    {categories.map(category => (
                      <option key={category} value={category}>
                        {category.charAt(0).toUpperCase() + category.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Setting Form */}
                {showSettingForm && (
                  <div className="setting-form-container">
                    <h3>{editSettingKey ? 'Edit Setting' : 'Add New Setting'}</h3>
                    <form onSubmit={saveSetting}>
                      <div className="form-group">
                        <label htmlFor="key">Key:</label>
                        <input
                          type="text"
                          id="key"
                          name="key"
                          value={settingForm.key}
                          onChange={handleFormChange}
                          required
                          disabled={!!editSettingKey}
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="value">Value:</label>
                        <input
                          type="text"
                          id="value"
                          name="value"
                          value={settingForm.value}
                          onChange={handleFormChange}
                          required
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="description">Description:</label>
                        <textarea
                          id="description"
                          name="description"
                          value={settingForm.description}
                          onChange={handleFormChange}
                          rows={3}
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="category">Category:</label>
                        <select
                          id="category"
                          name="category"
                          value={settingForm.category}
                          onChange={handleFormChange}
                          required
                        >
                          {categories.map(category => (
                            <option key={category} value={category}>
                              {category.charAt(0).toUpperCase() + category.slice(1)}
                            </option>
                          ))}
                          <option value="new">New Category...</option>
                        </select>
                      </div>

                      {settingForm.category === 'new' && (
                        <div className="form-group">
                          <label htmlFor="new-category">New Category Name:</label>
                          <input
                            type="text"
                            id="new-category"
                            name="category"
                            value=""
                            onChange={handleFormChange}
                            required
                          />
                        </div>
                      )}

                      <div className="form-actions">
                        <button type="submit" className="save-btn">
                          {editSettingKey ? 'Update Setting' : 'Add Setting'}
                        </button>
                        <button
                          type="button"
                          className="cancel-btn"
                          onClick={() => {
                            setShowSettingForm(false);
                            setEditSettingKey(null);
                          }}
                        >
                          Cancel
                        </button>
                      </div>
                    </form>
                  </div>
                )}

                {/* Settings List */}
                {filteredSettings.length === 0 ? (
                  <p className="no-settings">No settings found.</p>
                ) : (
                  <div className="settings-list">
                    {filteredSettings.map(setting => (
                      <div key={setting.id} className="setting-card">
                        <div className="setting-header">
                          <h3>{setting.key}</h3>
                          <span className="setting-category">{setting.category}</span>
                        </div>

                        <div className="setting-value">
                          <strong>Value:</strong>
                          <span>{typeof setting.value === 'object'
                            ? JSON.stringify(setting.value)
                            : String(setting.value)}
                          </span>
                        </div>

                        {setting.description && (
                          <div className="setting-description">
                            {setting.description}
                          </div>
                        )}

                        <div className="setting-meta">
                          <span>Created: {formatDate(setting.created_at)}</span>
                          {setting.updated_at && (
                            <span>Updated: {formatDate(setting.updated_at)}</span>
                          )}
                        </div>

                        <div className="setting-actions">
                          <button
                            className="edit-btn"
                            onClick={() => handleEditSetting(setting)}
                          >
                            Edit
                          </button>
                          <button
                            className="delete-btn"
                            onClick={() => deleteSetting(setting.key)}
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'status' && systemStatus && (
              <div className="system-tab">
                <div className="system-header">
                  <h2>System Status</h2>
                  <button
                    className="refresh-btn"
                    onClick={fetchSystemStatus}
                  >
                    Refresh
                  </button>
                </div>

                <div className="status-overview">
                  <div className={`system-status status-${systemStatus.status}`}>
                    <h3>Overall Status: {systemStatus.status.toUpperCase()}</h3>
                    <p>Uptime: {formatUptime(systemStatus.uptime)}</p>
                    <p>Platform: {systemStatus.resources.platform}</p>
                  </div>
                </div>

                <div className="status-sections">
                  {/* Components Status */}
                  <div className="status-section">
                    <h3>Components</h3>
                    <div className="components-list">
                      {systemStatus.components.map((component, index) => (
                        <div key={index} className={`component-card status-${component.status}`}>
                          <h4>{component.component}</h4>
                          <p className="component-status">{component.status.toUpperCase()}</p>
                          {component.message && <p>{component.message}</p>}
                          <p className="component-time">Last checked: {formatDate(component.last_checked)}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Resources */}
                  <div className="status-section">
                    <h3>Resources</h3>

                    <div className="resource-card">
                      <h4>CPU</h4>
                      <div className="resource-meter">
                        <div
                          className="resource-fill"
                          style={{ width: `${systemStatus.resources.cpu.percent}%` }}
                        ></div>
                      </div>
                      <p>{systemStatus.resources.cpu.percent}% (Cores: {systemStatus.resources.cpu.cores})</p>
                    </div>

                    <div className="resource-card">
                      <h4>Memory</h4>
                      <div className="resource-meter">
                        <div
                          className="resource-fill"
                          style={{ width: `${systemStatus.resources.memory.percent}%` }}
                        ></div>
                      </div>
                      <p>{formatBytes(systemStatus.resources.memory.available)} available of {formatBytes(systemStatus.resources.memory.total)}</p>
                    </div>

                    <div className="resource-card">
                      <h4>Disk</h4>
                      <div className="resource-meter">
                        <div
                          className="resource-fill"
                          style={{ width: `${systemStatus.resources.disk.percent}%` }}
                        ></div>
                      </div>
                      <p>{formatBytes(systemStatus.resources.disk.free)} free of {formatBytes(systemStatus.resources.disk.total)}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'audit' && (
              <div className="audit-tab">
                <div className="audit-header">
                  <h2>Audit Logs</h2>
                  <button
                    className="refresh-btn"
                    onClick={fetchAuditLogs}
                  >
                    Refresh
                  </button>
                </div>

                {auditLogs.length === 0 ? (
                  <p className="no-logs">No audit logs found.</p>
                ) : (
                  <>
                    <div className="audit-logs-list">
                      {auditLogs.map(log => (
                        <div key={log.id} className="audit-log-card">
                          <div className="log-header">
                            <span className={`log-action action-${log.action}`}>{log.action.toUpperCase()}</span>
                            <span className="log-resource">{log.resource_type}</span>
                            {log.resource_id && <span className="log-resource-id">ID: {log.resource_id}</span>}
                          </div>

                          <div className="log-details">
                            {log.details && (
                              <pre>{JSON.stringify(log.details, null, 2)}</pre>
                            )}
                          </div>

                          <div className="log-meta">
                            <span>User ID: {log.user_id || 'System'}</span>
                            {log.ip_address && <span>IP: {log.ip_address}</span>}
                            <span>Time: {formatDate(log.timestamp)}</span>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Pagination */}
                    <div className="pagination">
                      <button
                        onClick={() => setAuditLogPage(p => Math.max(1, p - 1))}
                        disabled={auditLogPage === 1}
                      >
                        Previous
                      </button>

                      <span className="page-info">
                        Page {auditLogPage} of {Math.ceil(totalAuditLogs / auditLogPageSize)}
                      </span>

                      <button
                        onClick={() => setAuditLogPage(p => p + 1)}
                        disabled={auditLogPage >= Math.ceil(totalAuditLogs / auditLogPageSize)}
                      >
                        Next
                      </button>

                      <select
                        value={auditLogPageSize}
                        onChange={(e) => {
                          setAuditLogPageSize(Number(e.target.value));
                          setAuditLogPage(1);
                        }}
                      >
                        <option value="10">10 per page</option>
                        <option value="25">25 per page</option>
                        <option value="50">50 per page</option>
                        <option value="100">100 per page</option>
                      </select>
                    </div>
                  </>
                )}
              </div>
            )}

            {activeTab === 'certifications' && (
              <CertificationManagement />
            )}

            {activeTab === 'organizations' && (
              <OrganizationManagement />
            )}

            {activeTab === 'feedback' && (
              <FeedbackManagement />
            )}

            {activeTab === 'translations' && (
              <TranslationManagement />
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default AdminInterface;
