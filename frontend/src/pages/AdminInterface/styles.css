.admin-interface-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.admin-interface-container h1 {
  margin-bottom: 20px;
  color: #333;
  font-size: 28px;
}

.admin-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  font-size: 18px;
  color: #666;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  border-left: 4px solid #c62828;
}

/* Tabs */
.admin-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #ddd;
}

.tab-button {
  padding: 10px 20px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  color: #666;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: #333;
}

.tab-button.active {
  color: #2196f3;
  border-bottom-color: #2196f3;
}

/* Settings Tab */
.settings-tab {
  margin-top: 20px;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.settings-header h2 {
  margin: 0;
}

.add-setting-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.add-setting-btn:hover {
  background-color: #388e3c;
}

.category-filter {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.category-filter label {
  margin-right: 10px;
  font-weight: 500;
}

.category-filter select {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
  min-width: 200px;
}

.setting-form-container {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #ddd;
}

.setting-form-container h3 {
  margin-top: 0;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.save-btn {
  background-color: #2196f3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.save-btn:hover {
  background-color: #1976d2;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}

.settings-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.setting-card {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 15px;
  border: 1px solid #ddd;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.setting-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.setting-category {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.setting-value {
  margin-bottom: 10px;
  word-break: break-word;
}

.setting-description {
  color: #666;
  margin-bottom: 10px;
  font-size: 14px;
}

.setting-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #888;
  margin-bottom: 10px;
}

.setting-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.edit-btn {
  background-color: #ff9800;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.edit-btn:hover {
  background-color: #f57c00;
}

.delete-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.delete-btn:hover {
  background-color: #d32f2f;
}

.no-settings {
  text-align: center;
  color: #666;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

/* System Status Tab */
.system-tab {
  margin-top: 20px;
}

.system-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.system-header h2 {
  margin: 0;
}

.refresh-btn {
  background-color: #2196f3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.refresh-btn:hover {
  background-color: #1976d2;
}

.status-overview {
  margin-bottom: 30px;
}

.system-status {
  padding: 20px;
  border-radius: 4px;
  text-align: center;
}

.status-operational {
  background-color: #e8f5e9;
  border: 1px solid #4caf50;
  color: #2e7d32;
}

.status-degraded {
  background-color: #fff8e1;
  border: 1px solid #ffc107;
  color: #ff8f00;
}

.status-down {
  background-color: #ffebee;
  border: 1px solid #f44336;
  color: #c62828;
}

.status-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.status-section {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  border: 1px solid #ddd;
}

.status-section h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
}

.components-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.component-card {
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.component-card h4 {
  margin-top: 0;
  margin-bottom: 10px;
}

.component-status {
  font-weight: bold;
  margin-bottom: 10px;
}

.component-time {
  font-size: 12px;
  color: #888;
  margin-top: 10px;
}

.status-healthy {
  background-color: #e8f5e9;
  border-color: #4caf50;
}

.status-degraded {
  background-color: #fff8e1;
  border-color: #ffc107;
}

.status-down {
  background-color: #ffebee;
  border-color: #f44336;
}

.resource-card {
  margin-bottom: 20px;
}

.resource-card h4 {
  margin-top: 0;
  margin-bottom: 10px;
}

.resource-meter {
  height: 20px;
  background-color: #f5f5f5;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 5px;
}

.resource-fill {
  height: 100%;
  background-color: #2196f3;
  border-radius: 10px;
  transition: width 0.3s ease;
}

/* Audit Logs Tab */
.audit-tab {
  margin-top: 20px;
}

.audit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.audit-header h2 {
  margin: 0;
}

.audit-logs-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.audit-log-card {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 15px;
  border: 1px solid #ddd;
}

.log-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.log-action {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.action-create {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.action-update {
  background-color: #e3f2fd;
  color: #1565c0;
}

.action-delete {
  background-color: #ffebee;
  color: #c62828;
}

.action-view {
  background-color: #f3e5f5;
  color: #6a1b9a;
}

.log-resource {
  font-weight: 500;
}

.log-resource-id {
  color: #666;
  font-size: 12px;
}

.log-details {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow-x: auto;
}

.log-details pre {
  margin: 0;
  font-size: 12px;
}

.log-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #888;
}

.no-logs {
  text-align: center;
  color: #666;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
}

.pagination button {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
}

.pagination button:hover:not(:disabled) {
  background-color: #e0e0e0;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination select {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
}
