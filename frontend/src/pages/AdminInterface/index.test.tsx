import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import axios from 'axios';
import AdminInterface from './index';

// Mock axios
jest.mock('axios');
const mockAxios = axios as jest.Mocked<typeof axios>;

describe('AdminInterface Component', () => {
    // Mock data
    const mockSettings = [
        {
            id: 1,
            key: 'site_name',
            value: 'CertRats',
            description: 'Site name displayed in the header',
            category: 'general',
            created_at: new Date().toISOString(),
            updated_at: null
        },
        {
            id: 2,
            key: 'maintenance_mode',
            value: false,
            description: 'Enable maintenance mode',
            category: 'system',
            created_at: new Date().toISOString(),
            updated_at: null
        }
    ];

    const mockSystemStatus = {
        status: 'operational',
        components: [
            {
                component: 'Database',
                status: 'healthy',
                message: 'Connected',
                last_checked: new Date().toISOString()
            },
            {
                component: 'API',
                status: 'healthy',
                message: 'Running',
                last_checked: new Date().toISOString()
            }
        ],
        resources: {
            cpu: {
                percent: 25.5,
                cores: 8
            },
            memory: {
                total: 16000000000,
                available: **********,
                percent: 50.0
            },
            disk: {
                total: 500000000000,
                free: 250000000000,
                percent: 50.0
            },
            platform: 'Linux-5.4.0-x86_64-with-glibc2.29'
        },
        uptime: 3600
    };

    const mockAuditLogs = [
        {
            id: 1,
            user_id: 1,
            action: 'create',
            resource_type: 'user',
            resource_id: '2',
            details: { username: 'newuser' },
            ip_address: '***********',
            timestamp: new Date().toISOString()
        },
        {
            id: 2,
            user_id: 1,
            action: 'update',
            resource_type: 'setting',
            resource_id: 'site_name',
            details: { old_value: 'CertRats', new_value: 'Updated CertRats' },
            ip_address: '***********',
            timestamp: new Date(Date.now() - 3600000).toISOString()
        }
    ];

    // Setup mock responses
    beforeEach(() => {
        mockAxios.get.mockImplementation((url) => {
            if (url === '/api/admin/settings') {
                return Promise.resolve({ data: mockSettings });
            } else if (url === '/api/admin/system/status') {
                return Promise.resolve({ data: mockSystemStatus });
            } else if (url.startsWith('/api/admin/audit-logs')) {
                return Promise.resolve({ data: mockAuditLogs, total: mockAuditLogs.length });
            }
            return Promise.reject(new Error('not found'));
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('renders AdminInterface component successfully', async () => {
        render(<AdminInterface />);

        // Check loading state
        expect(screen.getByText(/Loading admin interface/i)).toBeInTheDocument();

        // Wait for settings to load
        await waitFor(() => {
            expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
        });

        // Check if tabs are rendered
        expect(screen.getByText('Settings')).toBeInTheDocument();
        expect(screen.getByText('System Status')).toBeInTheDocument();
        expect(screen.getByText('Audit Logs')).toBeInTheDocument();
        expect(screen.getByText('Certifications')).toBeInTheDocument();
        expect(screen.getByText('Organizations')).toBeInTheDocument();
        expect(screen.getByText('Feedback')).toBeInTheDocument();
        expect(screen.getByText('Translations')).toBeInTheDocument();
    });

    test('switches between tabs correctly', async () => {
        render(<AdminInterface />);

        // Wait for the component to load
        await waitFor(() => {
            expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
        });

        // Initially on Settings tab
        expect(screen.getByText('System Settings')).toBeInTheDocument();

        // Switch to System Status tab
        fireEvent.click(screen.getByText('System Status'));
        await waitFor(() => {
            expect(screen.getByText(/Overall Status:/i)).toBeInTheDocument();
        });

        // Switch to Audit Logs tab
        fireEvent.click(screen.getByText('Audit Logs'));
        await waitFor(() => {
            expect(screen.getByText('Audit Logs')).toBeInTheDocument();
        });

        // Switch to Certifications tab
        fireEvent.click(screen.getByText('Certifications'));

        // Switch to Organizations tab
        fireEvent.click(screen.getByText('Organizations'));

        // Switch to Feedback tab
        fireEvent.click(screen.getByText('Feedback'));

        // Switch to Translations tab
        fireEvent.click(screen.getByText('Translations'));
    });

    test('adds a new setting successfully', async () => {
        // Mock the post request
        mockAxios.post.mockResolvedValue({ data: { success: true } });

        render(<AdminInterface />);

        // Wait for the component to load
        await waitFor(() => {
            expect(screen.getByText('System Settings')).toBeInTheDocument();
        });

        // Click on "Add Setting" button
        fireEvent.click(screen.getByText('Add Setting'));

        // Fill in the form
        fireEvent.change(screen.getByLabelText('Key:'), { target: { value: 'new_setting' } });
        fireEvent.change(screen.getByLabelText('Value:'), { target: { value: 'test value' } });
        fireEvent.change(screen.getByLabelText('Description:'), { target: { value: 'Test description' } });

        // Submit the form
        fireEvent.click(screen.getByText('Add Setting'));

        // Verify that the API was called
        await waitFor(() => {
            expect(mockAxios.post).toHaveBeenCalledWith('/api/admin/settings', {
                key: 'new_setting',
                value: 'test value',
                description: 'Test description',
                category: 'general'
            });
        });
    });

    test('edits an existing setting successfully', async () => {
        // Mock the put request
        mockAxios.put.mockResolvedValue({ data: { success: true } });

        render(<AdminInterface />);

        // Wait for the component to load
        await waitFor(() => {
            expect(screen.getByText('System Settings')).toBeInTheDocument();
        });

        // Find and click the Edit button for the first setting
        const editButtons = screen.getAllByText('Edit');
        fireEvent.click(editButtons[0]);

        // Modify the value
        fireEvent.change(screen.getByLabelText('Value:'), { target: { value: 'updated value' } });

        // Submit the form
        fireEvent.click(screen.getByText('Update Setting'));

        // Verify that the API was called
        await waitFor(() => {
            expect(mockAxios.put).toHaveBeenCalledWith('/api/admin/settings/site_name', {
                value: 'updated value',
                description: 'Site name displayed in the header',
                category: 'general'
            });
        });
    });

    test('deletes a setting after confirmation', async () => {
        // Mock the delete request
        mockAxios.delete.mockResolvedValue({ data: { success: true } });

        // Mock window.confirm to return true
        const confirmSpy = jest.spyOn(window, 'confirm');
        confirmSpy.mockImplementation(() => true);

        render(<AdminInterface />);

        // Wait for the component to load
        await waitFor(() => {
            expect(screen.getByText('System Settings')).toBeInTheDocument();
        });

        // Find and click the Delete button for the first setting
        const deleteButtons = screen.getAllByText('Delete');
        fireEvent.click(deleteButtons[0]);

        // Verify that confirm was called
        expect(confirmSpy).toHaveBeenCalled();

        // Verify that the API was called
        await waitFor(() => {
            expect(mockAxios.delete).toHaveBeenCalledWith('/api/admin/settings/site_name');
        });

        // Restore the original implementation
        confirmSpy.mockRestore();
    });

    test('logs out successfully', async () => {
        // Mock the post request for logout
        mockAxios.post.mockResolvedValue({ data: { success: true } });

        // Mock window.location.reload
        const reloadMock = jest.fn();
        Object.defineProperty(window, 'location', {
            value: { reload: reloadMock },
            writable: true
        });

        render(<AdminInterface />);

        // Wait for the component to load
        await waitFor(() => {
            expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
        });

        // Click on logout button
        fireEvent.click(screen.getByText('Logout'));

        // Verify that the API was called
        await waitFor(() => {
            expect(mockAxios.post).toHaveBeenCalledWith('/api/admin/logout');
            expect(reloadMock).toHaveBeenCalled();
        });
    });
}); 