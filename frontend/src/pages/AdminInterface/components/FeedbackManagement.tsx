import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import DeleteIcon from '@mui/icons-material/Delete';
import InfoIcon from '@mui/icons-material/Info';
import RefreshIcon from '@mui/icons-material/Refresh';
import StarIcon from '@mui/icons-material/Star';
import VisibilityIcon from '@mui/icons-material/Visibility';
import {
    Alert,
    AlertColor,
    Box,
    Button,
    Chip,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    IconButton,
    InputLabel,
    MenuItem,
    Pagination,
    Paper,
    Select,
    Snackbar,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TextField,
    Tooltip,
    Typography
} from '@mui/material';
import axios from 'axios';
import React, { useEffect, useState } from 'react';

interface Feedback {
    id: number;
    user_id: number | null;
    user_email: string | null;
    type: string;
    subject: string;
    message: string;
    status: string;
    rating: number | null;
    admin_notes: string | null;
    created_at: string;
    updated_at: string | null;
}

interface FeedbackDetailProps {
    feedback: Feedback;
    onClose: () => void;
    onStatusChange: (id: number, status: string, notes: string) => void;
    onDelete: (id: number) => void;
}

// Feedback detail dialog component
const FeedbackDetail: React.FC<FeedbackDetailProps> = ({
    feedback,
    onClose,
    onStatusChange,
    onDelete
}) => {
    const [status, setStatus] = useState(feedback.status);
    const [notes, setNotes] = useState(feedback.admin_notes || '');

    const handleStatusChange = () => {
        onStatusChange(feedback.id, status, notes);
    };

    const handleDeleteClick = () => {
        if (window.confirm('Are you sure you want to delete this feedback?')) {
            onDelete(feedback.id);
            onClose();
        }
    };

    return (
        <Dialog open={true} onClose={onClose} maxWidth="md" fullWidth>
            <DialogTitle>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="h6">{feedback.subject}</Typography>
                    <Chip
                        label={feedback.status}
                        color={
                            feedback.status === 'new' ? 'info' :
                                feedback.status === 'in_progress' ? 'warning' :
                                    feedback.status === 'resolved' ? 'success' : 'default'
                        }
                    />
                </Box>
            </DialogTitle>
            <DialogContent dividers>
                <Box mb={3}>
                    <Typography variant="subtitle2" gutterBottom>
                        From: {feedback.user_email || 'Anonymous'}
                        {feedback.user_id && ` (User ID: ${feedback.user_id})`}
                    </Typography>
                    <Typography variant="subtitle2" gutterBottom>
                        Type: {feedback.type.charAt(0).toUpperCase() + feedback.type.slice(1)}
                    </Typography>
                    {feedback.rating && (
                        <Box display="flex" alignItems="center">
                            <Typography variant="subtitle2" sx={{ mr: 1 }}>Rating:</Typography>
                            {[...Array(5)].map((_, index) => (
                                <StarIcon
                                    key={index}
                                    color={index < feedback.rating! ? 'warning' : 'disabled'}
                                    fontSize="small"
                                />
                            ))}
                        </Box>
                    )}
                    <Typography variant="subtitle2" gutterBottom>
                        Submitted: {new Date(feedback.created_at).toLocaleString()}
                    </Typography>
                </Box>

                <Typography variant="h6" gutterBottom>Message:</Typography>
                <Paper elevation={0} sx={{ p: 2, bgcolor: '#f5f5f5', mb: 3 }}>
                    <Typography variant="body1" style={{ whiteSpace: 'pre-line' }}>
                        {feedback.message}
                    </Typography>
                </Paper>

                <Typography variant="h6" gutterBottom>Admin Notes:</Typography>
                <TextField
                    fullWidth
                    multiline
                    rows={4}
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Add notes about this feedback..."
                    variant="outlined"
                    sx={{ mb: 3 }}
                />

                <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel id="status-label">Status</InputLabel>
                    <Select
                        labelId="status-label"
                        value={status}
                        label="Status"
                        onChange={(e) => setStatus(e.target.value)}
                    >
                        <MenuItem value="new">New</MenuItem>
                        <MenuItem value="in_progress">In Progress</MenuItem>
                        <MenuItem value="resolved">Resolved</MenuItem>
                        <MenuItem value="spam">Spam</MenuItem>
                    </Select>
                </FormControl>
            </DialogContent>
            <DialogActions>
                <Button
                    startIcon={<DeleteIcon />}
                    color="error"
                    onClick={handleDeleteClick}
                >
                    Delete
                </Button>
                <Button onClick={onClose} color="primary">
                    Cancel
                </Button>
                <Button
                    onClick={handleStatusChange}
                    color="primary"
                    variant="contained"
                    startIcon={<CheckCircleIcon />}
                >
                    Save Changes
                </Button>
            </DialogActions>
        </Dialog>
    );
};

const FeedbackManagement: React.FC = () => {
    // State for feedback data
    const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [selectedFeedback, setSelectedFeedback] = useState<Feedback | null>(null);

    // State for pagination
    const [page, setPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(10);

    // State for filters
    const [statusFilter, setStatusFilter] = useState<string>('all');
    const [typeFilter, setTypeFilter] = useState<string>('all');

    // State for notification
    const [notification, setNotification] = useState<{
        open: boolean;
        message: string;
        severity: AlertColor;
    }>({
        open: false,
        message: '',
        severity: 'info'
    });

    // Fetch feedback on component mount
    useEffect(() => {
        fetchFeedback();
    }, [page, rowsPerPage, statusFilter, typeFilter]);

    // Fetch feedback from API
    const fetchFeedback = async () => {
        try {
            setLoading(true);
            const params: Record<string, string | number> = {
                page,
                limit: rowsPerPage
            };

            if (statusFilter !== 'all') {
                params.status = statusFilter;
            }

            if (typeFilter !== 'all') {
                params.type = typeFilter;
            }

            const response = await axios.get('/api/admin/feedback', { params });
            setFeedbacks(response.data.items);
            setTotalPages(Math.ceil(response.data.total / rowsPerPage));
            setLoading(false);
        } catch (error) {
            console.error('Error fetching feedback:', error);
            setNotification({
                open: true,
                message: 'Failed to fetch feedback',
                severity: 'error'
            });
            setLoading(false);
        }
    };

    // View feedback details
    const handleViewFeedback = (feedback: Feedback) => {
        setSelectedFeedback(feedback);
    };

    // Update feedback status and notes
    const handleStatusChange = async (id: number, status: string, notes: string) => {
        try {
            await axios.put(`/api/admin/feedback/${id}`, { status, admin_notes: notes });
            setNotification({
                open: true,
                message: 'Feedback updated successfully',
                severity: 'success'
            });
            setSelectedFeedback(null);
            fetchFeedback();
        } catch (error) {
            console.error('Error updating feedback:', error);
            setNotification({
                open: true,
                message: 'Failed to update feedback',
                severity: 'error'
            });
        }
    };

    // Delete feedback
    const handleDeleteFeedback = async (id: number) => {
        try {
            await axios.delete(`/api/admin/feedback/${id}`);
            setNotification({
                open: true,
                message: 'Feedback deleted successfully',
                severity: 'success'
            });
            fetchFeedback();
        } catch (error) {
            console.error('Error deleting feedback:', error);
            setNotification({
                open: true,
                message: 'Failed to delete feedback',
                severity: 'error'
            });
        }
    };

    // Close notification
    const handleCloseNotification = () => {
        setNotification({
            ...notification,
            open: false
        });
    };

    // Handle page change
    const handlePageChange = (event: React.ChangeEvent<unknown>, newPage: number) => {
        setPage(newPage);
    };

    // Format date for display
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleString(undefined, {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Truncate text
    const truncateText = (text: string, maxLength: number) => {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    };

    return (
        <Box sx={{ padding: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h5" component="h2">
                    <strong>Feedback Management</strong>
                </Typography>
                <Button
                    variant="outlined"
                    color="primary"
                    startIcon={<RefreshIcon />}
                    onClick={fetchFeedback}
                >
                    Refresh
                </Button>
            </Box>

            {/* Filters */}
            <Box display="flex" gap={2} mb={3}>
                <FormControl size="small" sx={{ minWidth: 150 }}>
                    <InputLabel id="status-filter-label">Status</InputLabel>
                    <Select
                        labelId="status-filter-label"
                        value={statusFilter}
                        label="Status"
                        onChange={(e) => setStatusFilter(e.target.value)}
                    >
                        <MenuItem value="all">All Statuses</MenuItem>
                        <MenuItem value="new">New</MenuItem>
                        <MenuItem value="in_progress">In Progress</MenuItem>
                        <MenuItem value="resolved">Resolved</MenuItem>
                        <MenuItem value="spam">Spam</MenuItem>
                    </Select>
                </FormControl>

                <FormControl size="small" sx={{ minWidth: 150 }}>
                    <InputLabel id="type-filter-label">Type</InputLabel>
                    <Select
                        labelId="type-filter-label"
                        value={typeFilter}
                        label="Type"
                        onChange={(e) => setTypeFilter(e.target.value)}
                    >
                        <MenuItem value="all">All Types</MenuItem>
                        <MenuItem value="bug">Bug Report</MenuItem>
                        <MenuItem value="feature">Feature Request</MenuItem>
                        <MenuItem value="question">Question</MenuItem>
                        <MenuItem value="other">Other</MenuItem>
                    </Select>
                </FormControl>
            </Box>

            {loading ? (
                <Box display="flex" justifyContent="center" mt={4}>
                    <CircularProgress />
                </Box>
            ) : feedbacks.length === 0 ? (
                <Paper sx={{ p: 4, textAlign: 'center' }}>
                    <InfoIcon color="disabled" sx={{ fontSize: 48, mb: 2 }} />
                    <Typography variant="h6" color="textSecondary">
                        No feedback found
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                        Try changing the filters or check back later
                    </Typography>
                </Paper>
            ) : (
                <>
                    <TableContainer component={Paper}>
                        <Table sx={{ minWidth: 650 }} aria-label="feedback table">
                            <TableHead>
                                <TableRow>
                                    <TableCell><strong>Subject</strong></TableCell>
                                    <TableCell><strong>From</strong></TableCell>
                                    <TableCell><strong>Type</strong></TableCell>
                                    <TableCell><strong>Status</strong></TableCell>
                                    <TableCell><strong>Submitted</strong></TableCell>
                                    <TableCell><strong>Actions</strong></TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {feedbacks.map((feedback) => (
                                    <TableRow key={feedback.id}>
                                        <TableCell>
                                            <Tooltip title={feedback.subject}>
                                                <Typography variant="body2">
                                                    {truncateText(feedback.subject, 40)}
                                                </Typography>
                                            </Tooltip>
                                        </TableCell>
                                        <TableCell>{feedback.user_email || 'Anonymous'}</TableCell>
                                        <TableCell>
                                            <Chip
                                                label={feedback.type}
                                                size="small"
                                                color={
                                                    feedback.type === 'bug' ? 'error' :
                                                        feedback.type === 'feature' ? 'success' :
                                                            feedback.type === 'question' ? 'info' : 'default'
                                                }
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <Chip
                                                label={feedback.status}
                                                size="small"
                                                color={
                                                    feedback.status === 'new' ? 'info' :
                                                        feedback.status === 'in_progress' ? 'warning' :
                                                            feedback.status === 'resolved' ? 'success' : 'default'
                                                }
                                            />
                                        </TableCell>
                                        <TableCell>{formatDate(feedback.created_at)}</TableCell>
                                        <TableCell>
                                            <IconButton
                                                aria-label="view"
                                                color="primary"
                                                onClick={() => handleViewFeedback(feedback)}
                                            >
                                                <VisibilityIcon />
                                            </IconButton>
                                            <IconButton
                                                aria-label="delete"
                                                color="error"
                                                onClick={() => handleDeleteFeedback(feedback.id)}
                                            >
                                                <DeleteIcon />
                                            </IconButton>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>

                    <Box display="flex" justifyContent="center" mt={3}>
                        <Pagination
                            count={totalPages}
                            page={page}
                            onChange={handlePageChange}
                            color="primary"
                            showFirstButton
                            showLastButton
                        />
                    </Box>
                </>
            )}

            {/* Feedback Detail Dialog */}
            {selectedFeedback && (
                <FeedbackDetail
                    feedback={selectedFeedback}
                    onClose={() => setSelectedFeedback(null)}
                    onStatusChange={handleStatusChange}
                    onDelete={handleDeleteFeedback}
                />
            )}

            {/* Notification */}
            <Snackbar
                open={notification.open}
                autoHideDuration={6000}
                onClose={handleCloseNotification}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
            >
                <Alert onClose={handleCloseNotification} severity={notification.severity}>
                    {notification.message}
                </Alert>
            </Snackbar>
        </Box>
    );
};

export default FeedbackManagement; 