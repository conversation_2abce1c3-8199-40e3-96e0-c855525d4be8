import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import LanguageIcon from '@mui/icons-material/Language';
import RefreshIcon from '@mui/icons-material/Refresh';
import {
    Alert,
    AlertColor,
    Box,
    Button,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    IconButton,
    Link,
    Paper,
    Snackbar,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TextField,
    Typography
} from '@mui/material';
import axios from 'axios';
import React, { useEffect, useState } from 'react';

interface Organization {
    id: number;
    name: string;
    website: string | null;
    description: string | null;
    created_at: string;
    updated_at: string | null;
}

interface OrganizationFormData {
    name: string;
    website: string;
    description: string;
}

const OrganizationManagement: React.FC = () => {
    // State for organizations data
    const [organizations, setOrganizations] = useState<Organization[]>([]);
    const [loading, setLoading] = useState<boolean>(true);

    // State for form
    const [openForm, setOpenForm] = useState<boolean>(false);
    const [editMode, setEditMode] = useState<boolean>(false);
    const [currentOrganizationId, setCurrentOrganizationId] = useState<number | null>(null);

    // State for notification
    const [notification, setNotification] = useState<{
        open: boolean;
        message: string;
        severity: AlertColor;
    }>({
        open: false,
        message: '',
        severity: 'info'
    });

    // State for form data
    const [formData, setFormData] = useState<OrganizationFormData>({
        name: '',
        website: '',
        description: ''
    });

    // Fetch organizations on component mount
    useEffect(() => {
        fetchOrganizations();
    }, []);

    // Fetch organizations from API
    const fetchOrganizations = async () => {
        try {
            setLoading(true);
            const response = await axios.get('/api/admin/organizations');
            setOrganizations(response.data);
            setLoading(false);
        } catch (error) {
            console.error('Error fetching organizations:', error);
            setNotification({
                open: true,
                message: 'Failed to fetch organizations',
                severity: 'error'
            });
            setLoading(false);
        }
    };

    // Handle form input changes
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData({
            ...formData,
            [name]: value
        });
    };

    // Open form for adding new organization
    const handleAddNew = () => {
        setFormData({
            name: '',
            website: '',
            description: ''
        });
        setEditMode(false);
        setCurrentOrganizationId(null);
        setOpenForm(true);
    };

    // Open form for editing existing organization
    const handleEdit = (organization: Organization) => {
        setFormData({
            name: organization.name,
            website: organization.website || '',
            description: organization.description || ''
        });
        setEditMode(true);
        setCurrentOrganizationId(organization.id);
        setOpenForm(true);
    };

    // Handle form submission
    const handleSubmit = async () => {
        try {
            if (editMode && currentOrganizationId) {
                // Update existing organization
                await axios.put(`/api/admin/organizations/${currentOrganizationId}`, formData);
                setNotification({
                    open: true,
                    message: 'Organization updated successfully',
                    severity: 'success'
                });
            } else {
                // Add new organization
                await axios.post('/api/admin/organizations', formData);
                setNotification({
                    open: true,
                    message: 'Organization added successfully',
                    severity: 'success'
                });
            }
            setOpenForm(false);
            fetchOrganizations();
        } catch (error) {
            console.error('Error saving organization:', error);
            setNotification({
                open: true,
                message: 'Failed to save organization',
                severity: 'error'
            });
        }
    };

    // Handle organization deletion
    const handleDelete = async (id: number) => {
        // Check if there are certifications using this organization
        try {
            const response = await axios.get(`/api/admin/organizations/${id}/certifications`);
            const certifications = response.data;

            if (certifications.length > 0) {
                setNotification({
                    open: true,
                    message: `Cannot delete organization used by ${certifications.length} certification(s)`,
                    severity: 'warning'
                });
                return;
            }

            if (window.confirm('Are you sure you want to delete this organization?')) {
                try {
                    await axios.delete(`/api/admin/organizations/${id}`);
                    setNotification({
                        open: true,
                        message: 'Organization deleted successfully',
                        severity: 'success'
                    });
                    fetchOrganizations();
                } catch (error) {
                    console.error('Error deleting organization:', error);
                    setNotification({
                        open: true,
                        message: 'Failed to delete organization',
                        severity: 'error'
                    });
                }
            }
        } catch (error) {
            console.error('Error checking certifications:', error);
            setNotification({
                open: true,
                message: 'Failed to check if organization can be deleted',
                severity: 'error'
            });
        }
    };

    // Close notification
    const handleCloseNotification = () => {
        setNotification({
            ...notification,
            open: false
        });
    };

    // Close form
    const handleCloseForm = () => {
        setOpenForm(false);
    };

    // Format date for display
    const formatDate = (dateString: string | null): string => {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleString();
    };

    return (
        <Box sx={{ padding: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h5" component="h2">
                    <strong>Organization Management</strong>
                </Typography>
                <Box>
                    <Button
                        variant="outlined"
                        color="primary"
                        startIcon={<RefreshIcon />}
                        onClick={fetchOrganizations}
                        sx={{ mr: 1 }}
                    >
                        Refresh
                    </Button>
                    <Button
                        variant="contained"
                        color="primary"
                        startIcon={<AddIcon />}
                        onClick={handleAddNew}
                    >
                        Add New
                    </Button>
                </Box>
            </Box>

            {loading ? (
                <Box display="flex" justifyContent="center" mt={4}>
                    <CircularProgress />
                </Box>
            ) : (
                <TableContainer component={Paper}>
                    <Table sx={{ minWidth: 650 }} aria-label="organizations table">
                        <TableHead>
                            <TableRow>
                                <TableCell><strong>Name</strong></TableCell>
                                <TableCell><strong>Website</strong></TableCell>
                                <TableCell><strong>Description</strong></TableCell>
                                <TableCell><strong>Created</strong></TableCell>
                                <TableCell><strong>Updated</strong></TableCell>
                                <TableCell><strong>Actions</strong></TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {organizations.map((org) => (
                                <TableRow key={org.id}>
                                    <TableCell>{org.name}</TableCell>
                                    <TableCell>
                                        {org.website ? (
                                            <Link
                                                href={org.website}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                display="flex"
                                                alignItems="center"
                                            >
                                                <LanguageIcon fontSize="small" sx={{ mr: 0.5 }} />
                                                {org.website}
                                            </Link>
                                        ) : (
                                            'N/A'
                                        )}
                                    </TableCell>
                                    <TableCell>
                                        {org.description ? (
                                            org.description.length > 100
                                                ? `${org.description.substring(0, 100)}...`
                                                : org.description
                                        ) : (
                                            'N/A'
                                        )}
                                    </TableCell>
                                    <TableCell>{formatDate(org.created_at)}</TableCell>
                                    <TableCell>{formatDate(org.updated_at)}</TableCell>
                                    <TableCell>
                                        <IconButton
                                            aria-label="edit"
                                            color="primary"
                                            onClick={() => handleEdit(org)}
                                        >
                                            <EditIcon />
                                        </IconButton>
                                        <IconButton
                                            aria-label="delete"
                                            color="error"
                                            onClick={() => handleDelete(org.id)}
                                        >
                                            <DeleteIcon />
                                        </IconButton>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            )}

            {/* Form Dialog */}
            <Dialog open={openForm} onClose={handleCloseForm} maxWidth="md" fullWidth>
                <DialogTitle>{editMode ? 'Edit Organization' : 'Add New Organization'}</DialogTitle>
                <DialogContent>
                    <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid item xs={12}>
                            <TextField
                                name="name"
                                label="Organization Name *"
                                value={formData.name}
                                onChange={handleInputChange}
                                fullWidth
                                required
                                margin="normal"
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField
                                name="website"
                                label="Website URL"
                                value={formData.website}
                                onChange={handleInputChange}
                                fullWidth
                                margin="normal"
                                placeholder="https://example.com"
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField
                                name="description"
                                label="Description"
                                value={formData.description}
                                onChange={handleInputChange}
                                fullWidth
                                multiline
                                rows={4}
                                margin="normal"
                            />
                        </Grid>
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseForm} color="primary">
                        Cancel
                    </Button>
                    <Button onClick={handleSubmit} color="primary" variant="contained">
                        {editMode ? 'Update' : 'Add'} Organization
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Notification */}
            <Snackbar
                open={notification.open}
                autoHideDuration={6000}
                onClose={handleCloseNotification}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
            >
                <Alert onClose={handleCloseNotification} severity={notification.severity}>
                    {notification.message}
                </Alert>
            </Snackbar>
        </Box>
    );
};

export default OrganizationManagement; 