import React, { useEffect, useState } from 'react';
import '../AdminInterface.css';

// Remove Material-UI imports and define custom icons
const CustomIcon = ({ type }: { type: string }) => {
    switch (type) {
        case 'add':
            return <span className="custom-icon">➕</span>;
        case 'delete':
            return <span className="custom-icon">🗑️</span>;
        case 'edit':
            return <span className="custom-icon">✏️</span>;
        case 'refresh':
            return <span className="custom-icon">🔄</span>;
        case 'upload':
            return <span className="custom-icon">📤</span>;
        default:
            return <span className="custom-icon">❓</span>;
    }
};

interface Certification {
    id: number;
    name: string;
    provider: string;
    level: string;
    examFee: number;
    status: 'active' | 'inactive' | 'deprecated';
}

const CertificationManagement: React.FC = () => {
    const [certifications, setCertifications] = useState<Certification[]>([
        {
            id: 1,
            name: 'CompTIA Security+',
            provider: 'CompTIA',
            level: 'Entry',
            examFee: 349,
            status: 'active'
        },
        {
            id: 2,
            name: 'CISSP',
            provider: 'ISC²',
            level: 'Advanced',
            examFee: 749,
            status: 'active'
        },
        {
            id: 3,
            name: 'CEH',
            provider: 'EC-Council',
            level: 'Intermediate',
            examFee: 950,
            status: 'active'
        }
    ]);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedCert, setSelectedCert] = useState<Certification | null>(null);
    const [isEditing, setIsEditing] = useState(false);

    // Mock loading certifications
    useEffect(() => {
        // In a real app, this would fetch from API
    }, []);

    const handleEdit = (cert: Certification) => {
        setSelectedCert(cert);
        setIsEditing(true);
    };

    const handleDelete = (certId: number) => {
        if (window.confirm('Are you sure you want to delete this certification?')) {
            setCertifications(certifications.filter(cert => cert.id !== certId));
        }
    };

    const handleRefresh = () => {
        setIsLoading(true);
        // Simulate loading
        setTimeout(() => {
            setIsLoading(false);
        }, 1000);
    };

    return (
        <div className="admin-component">
            <div className="component-header">
                <h3>Certification Management</h3>
                <div className="action-buttons">
                    <button className="admin-button">
                        <CustomIcon type="add" /> Add New
                    </button>
                    <button className="admin-button" onClick={handleRefresh} disabled={isLoading}>
                        <CustomIcon type="refresh" /> Refresh
                    </button>
                    <button className="admin-button">
                        <CustomIcon type="upload" /> Import
                    </button>
                </div>
            </div>

            <div className="data-table-container">
                {isLoading ? (
                    <div className="loading-spinner">Loading...</div>
                ) : (
                    <table className="data-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Provider</th>
                                <th>Level</th>
                                <th>Exam Fee</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {certifications.map(cert => (
                                <tr key={cert.id}>
                                    <td>{cert.id}</td>
                                    <td>{cert.name}</td>
                                    <td>{cert.provider}</td>
                                    <td>{cert.level}</td>
                                    <td>${cert.examFee}</td>
                                    <td>
                                        <span className={`status-badge ${cert.status}`}>{cert.status}</span>
                                    </td>
                                    <td className="action-cell">
                                        <button className="icon-button" onClick={() => handleEdit(cert)}>
                                            <CustomIcon type="edit" />
                                        </button>
                                        <button className="icon-button" onClick={() => handleDelete(cert.id)}>
                                            <CustomIcon type="delete" />
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                )}
            </div>

            {isEditing && selectedCert && (
                <div className="modal-backdrop">
                    <div className="modal-content">
                        <h3>Edit Certification</h3>
                        <form className="edit-form">
                            <div className="form-group">
                                <label>Name</label>
                                <input type="text" defaultValue={selectedCert.name} />
                            </div>
                            <div className="form-group">
                                <label>Provider</label>
                                <input type="text" defaultValue={selectedCert.provider} />
                            </div>
                            <div className="form-group">
                                <label>Level</label>
                                <select defaultValue={selectedCert.level}>
                                    <option>Entry</option>
                                    <option>Intermediate</option>
                                    <option>Advanced</option>
                                    <option>Expert</option>
                                </select>
                            </div>
                            <div className="form-group">
                                <label>Exam Fee</label>
                                <input type="number" defaultValue={selectedCert.examFee} />
                            </div>
                            <div className="form-group">
                                <label>Status</label>
                                <select defaultValue={selectedCert.status}>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="deprecated">Deprecated</option>
                                </select>
                            </div>
                            <div className="form-actions">
                                <button type="button" onClick={() => setIsEditing(false)}>Cancel</button>
                                <button type="submit">Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default CertificationManagement; 