import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import axios from 'axios';
import CertificationManagement from './CertificationManagement';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('CertificationManagement Component', () => {
    // Sample data for tests
    const mockCertifications = [
        {
            id: 1,
            name: 'CISSP',
            level: 'Advanced',
            domain: 'Security Management',
            cost: 699,
            difficulty: 'High',
            description: 'Certified Information Systems Security Professional',
            prerequisites: 'Five years of experience',
            url: 'https://www.isc2.org/cissp',
            organization_id: 1,
            organization_name: 'ISC2',
            created_at: '2023-01-01T00:00:00Z',
            updated_at: null
        },
        {
            id: 2,
            name: 'Security+',
            level: 'Entry Level',
            domain: 'Network Security',
            cost: 349,
            difficulty: 'Low',
            description: 'CompTIA Security+',
            prerequisites: 'None',
            url: 'https://www.comptia.org/certifications/security',
            organization_id: 2,
            organization_name: 'CompTIA',
            created_at: '2023-01-01T00:00:00Z',
            updated_at: null
        }
    ];

    const mockOrganizations = [
        {
            id: 1,
            name: 'ISC2',
            website: 'https://www.isc2.org',
            description: 'International Information System Security Certification Consortium'
        },
        {
            id: 2,
            name: 'CompTIA',
            website: 'https://www.comptia.org',
            description: 'Computing Technology Industry Association'
        }
    ];

    beforeEach(() => {
        // Reset mocks between tests
        jest.clearAllMocks();

        // Mock API responses
        mockedAxios.get.mockImplementation((url) => {
            if (url === '/api/admin/certifications') {
                return Promise.resolve({ data: mockCertifications });
            } else if (url === '/api/admin/organizations') {
                return Promise.resolve({ data: mockOrganizations });
            }
            return Promise.reject(new Error('Not found'));
        });
    });

    test('renders certification management title', async () => {
        render(<CertificationManagement />);

        // Check if the title is rendered
        const titleElement = screen.getByText(/Certification Management/i);
        expect(titleElement).toBeInTheDocument();

        // Check loading indicator appears initially
        expect(screen.getByRole('progressbar')).toBeInTheDocument();

        // Wait for data to load
        await waitFor(() => {
            expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/certifications');
        });
    });

    test('displays certifications in a table after loading', async () => {
        render(<CertificationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Check if certifications are displayed
        expect(screen.getByText('CISSP')).toBeInTheDocument();
        expect(screen.getByText('Security+')).toBeInTheDocument();

        // Check organization names
        expect(screen.getByText('ISC2')).toBeInTheDocument();
        expect(screen.getByText('CompTIA')).toBeInTheDocument();
    });

    test('opens add form when Add New button is clicked', async () => {
        render(<CertificationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Click Add New button
        fireEvent.click(screen.getByText('Add New'));

        // Check if form is displayed
        expect(screen.getByText('Add New Certification')).toBeInTheDocument();

        // Check if form fields are empty
        expect(screen.getByLabelText(/certification name/i)).toHaveValue('');
    });

    test('opens edit form with data when Edit button is clicked', async () => {
        render(<CertificationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Find and click edit button for first certification
        const editButtons = screen.getAllByLabelText('edit');
        fireEvent.click(editButtons[0]);

        // Check if form is displayed with correct title
        expect(screen.getByText('Edit Certification')).toBeInTheDocument();

        // Check if form fields are populated with certification data
        expect(screen.getByLabelText(/certification name/i)).toHaveValue('CISSP');
    });

    test('calls API to add certification when form is submitted', async () => {
        // Mock post request
        mockedAxios.post.mockResolvedValueOnce({ data: { success: true } });

        render(<CertificationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Click Add New button
        fireEvent.click(screen.getByText('Add New'));

        // Fill in form
        fireEvent.change(screen.getByLabelText(/certification name/i), {
            target: { value: 'CEH' }
        });

        // Submit form
        fireEvent.click(screen.getByText('Add Certification'));

        // Check if API was called with correct data
        await waitFor(() => {
            expect(mockedAxios.post).toHaveBeenCalledWith('/api/admin/certifications', expect.objectContaining({
                name: 'CEH'
            }));
        });
    });
}); 