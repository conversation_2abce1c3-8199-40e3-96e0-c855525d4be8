import AddIcon from '@mui/icons-material/Add';
import DownloadIcon from '@mui/icons-material/Download';
import EditIcon from '@mui/icons-material/Edit';
import RefreshIcon from '@mui/icons-material/Refresh';
import SearchIcon from '@mui/icons-material/Search';
import TranslateIcon from '@mui/icons-material/Translate';
import UploadIcon from '@mui/icons-material/Upload';
import {
    Alert,
    AlertColor,
    Box,
    Button,
    Chip,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    IconButton,
    InputLabel,
    MenuItem,
    Paper,
    Select,
    SelectChangeEvent,
    Snackbar,
    Tab,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Tabs,
    TextField,
    Tooltip,
    Typography
} from '@mui/material';
import axios from 'axios';
import React, { useEffect, useState } from 'react';

interface Translation {
    id: number;
    key: string;
    language: string;
    text: string;
    is_approved: boolean;
    created_at: string;
    updated_at: string | null;
}

interface LanguageStat {
    language: string;
    count: number;
    approved: number;
    approval_percentage: number;
}

interface TranslationFormData {
    key: string;
    language: string;
    text: string;
    is_approved: boolean;
}

// Supported languages in the application
const LANGUAGES = [
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Spanish' },
    { code: 'de', name: 'German' },
    { code: 'fr', name: 'French' },
    { code: 'af', name: 'Afrikaans' },
    { code: 'zu', name: 'Zulu' },
    { code: 'ro', name: 'Romanian' }
];

const TranslationManagement: React.FC = () => {
    // State for translations data
    const [translations, setTranslations] = useState<Translation[]>([]);
    const [filteredTranslations, setFilteredTranslations] = useState<Translation[]>([]);
    const [languageStats, setLanguageStats] = useState<LanguageStat[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [searchQuery, setSearchQuery] = useState<string>('');

    // State for dialog
    const [openForm, setOpenForm] = useState<boolean>(false);
    const [editMode, setEditMode] = useState<boolean>(false);
    const [currentTranslationId, setCurrentTranslationId] = useState<number | null>(null);

    // State for tab and filters
    const [activeTab, setActiveTab] = useState<string>('all');
    const [languageFilter, setLanguageFilter] = useState<string>('all');
    const [approvalFilter, setApprovalFilter] = useState<string>('all');

    // State for form data
    const [formData, setFormData] = useState<TranslationFormData>({
        key: '',
        language: '',
        text: '',
        is_approved: false
    });

    // State for notification
    const [notification, setNotification] = useState<{
        open: boolean;
        message: string;
        severity: AlertColor;
    }>({
        open: false,
        message: '',
        severity: 'info'
    });

    // Fetch translations on component mount
    useEffect(() => {
        fetchTranslations();
    }, []);

    // Filter translations based on filters
    useEffect(() => {
        filterTranslations();
    }, [translations, activeTab, languageFilter, approvalFilter, searchQuery]);

    // Fetch translations from API
    const fetchTranslations = async () => {
        try {
            setLoading(true);
            const response = await axios.get('/api/admin/translations');
            setTranslations(response.data);
            calculateLanguageStats(response.data);
            setLoading(false);
        } catch (error) {
            console.error('Error fetching translations:', error);
            setNotification({
                open: true,
                message: 'Failed to fetch translations',
                severity: 'error'
            });
            setLoading(false);
        }
    };

    // Calculate language statistics
    const calculateLanguageStats = (data: Translation[]) => {
        const statsByLanguage: Record<string, { count: number; approved: number }> = {};

        // Initialize stats for all supported languages
        LANGUAGES.forEach(lang => {
            statsByLanguage[lang.code] = { count: 0, approved: 0 };
        });

        // Count translations by language
        data.forEach(translation => {
            if (statsByLanguage[translation.language]) {
                statsByLanguage[translation.language].count++;
                if (translation.is_approved) {
                    statsByLanguage[translation.language].approved++;
                }
            }
        });

        // Convert to array with percentages
        const stats = Object.entries(statsByLanguage).map(([language, stats]) => ({
            language,
            count: stats.count,
            approved: stats.approved,
            approval_percentage: stats.count ? Math.round((stats.approved / stats.count) * 100) : 0
        }));

        setLanguageStats(stats);
    };

    // Filter translations based on current filters
    const filterTranslations = () => {
        let filtered = [...translations];

        // Filter by language
        if (languageFilter !== 'all') {
            filtered = filtered.filter(t => t.language === languageFilter);
        }

        // Filter by approval status
        if (approvalFilter === 'approved') {
            filtered = filtered.filter(t => t.is_approved);
        } else if (approvalFilter === 'pending') {
            filtered = filtered.filter(t => !t.is_approved);
        }

        // Filter by missing/incomplete
        if (activeTab === 'missing') {
            // Find keys that don't have translations in all languages
            const keysByLanguage: Record<string, Set<string>> = {};

            // Initialize sets for each language
            LANGUAGES.forEach(lang => {
                keysByLanguage[lang.code] = new Set();
            });

            // Collect keys by language
            translations.forEach(translation => {
                if (keysByLanguage[translation.language]) {
                    keysByLanguage[translation.language].add(translation.key);
                }
            });

            // Get all unique keys
            const allKeys = new Set<string>();
            translations.forEach(t => allKeys.add(t.key));

            // Find missing translations
            const missingTranslations = new Set<Translation>();
            allKeys.forEach(key => {
                LANGUAGES.forEach(lang => {
                    if (!keysByLanguage[lang.code].has(key)) {
                        // This key is missing for this language
                        translations.forEach(t => {
                            if (t.key === key) {
                                missingTranslations.add(t);
                            }
                        });
                    }
                });
            });

            filtered = filtered.filter(t => missingTranslations.has(t));
        }

        // Filter by search query
        if (searchQuery) {
            const query = searchQuery.toLowerCase();
            filtered = filtered.filter(
                t => t.key.toLowerCase().includes(query) ||
                    t.text.toLowerCase().includes(query)
            );
        }

        setFilteredTranslations(filtered);
    };

    // Handle form input changes
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData({
            ...formData,
            [name]: value
        });
    };

    // Handle select changes
    const handleSelectChange = (e: SelectChangeEvent<string>) => {
        const name = e.target.name as string;
        const value = e.target.value;
        setFormData({
            ...formData,
            [name]: value
        });
    };

    // Handle checkbox changes
    const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, checked } = e.target;
        setFormData({
            ...formData,
            [name]: checked
        });
    };

    // Open form for adding new translation
    const handleAddNew = () => {
        setFormData({
            key: '',
            language: LANGUAGES[0].code,
            text: '',
            is_approved: false
        });
        setEditMode(false);
        setCurrentTranslationId(null);
        setOpenForm(true);
    };

    // Open form for editing existing translation
    const handleEdit = (translation: Translation) => {
        setFormData({
            key: translation.key,
            language: translation.language,
            text: translation.text,
            is_approved: translation.is_approved
        });
        setEditMode(true);
        setCurrentTranslationId(translation.id);
        setOpenForm(true);
    };

    // Handle form submission
    const handleSubmit = async () => {
        try {
            if (editMode && currentTranslationId) {
                // Update existing translation
                await axios.put(`/api/admin/translations/${currentTranslationId}`, formData);
                setNotification({
                    open: true,
                    message: 'Translation updated successfully',
                    severity: 'success'
                });
            } else {
                // Add new translation
                await axios.post('/api/admin/translations', formData);
                setNotification({
                    open: true,
                    message: 'Translation added successfully',
                    severity: 'success'
                });
            }
            setOpenForm(false);
            fetchTranslations();
        } catch (error) {
            console.error('Error saving translation:', error);
            setNotification({
                open: true,
                message: 'Failed to save translation',
                severity: 'error'
            });
        }
    };

    // Export translations
    const handleExport = async () => {
        try {
            const response = await axios.get('/api/admin/translations/export', {
                responseType: 'blob'
            });

            // Create download link
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', 'translations.json');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            setNotification({
                open: true,
                message: 'Translations exported successfully',
                severity: 'success'
            });
        } catch (error) {
            console.error('Error exporting translations:', error);
            setNotification({
                open: true,
                message: 'Failed to export translations',
                severity: 'error'
            });
        }
    };

    // Import translations
    const handleImport = () => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.onchange = async (e: Event) => {
            const file = (e.target as HTMLInputElement).files?.[0];
            if (!file) return;

            const formData = new FormData();
            formData.append('file', file);

            try {
                await axios.post('/api/admin/translations/import', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });

                setNotification({
                    open: true,
                    message: 'Translations imported successfully',
                    severity: 'success'
                });

                fetchTranslations();
            } catch (error) {
                console.error('Error importing translations:', error);
                setNotification({
                    open: true,
                    message: 'Failed to import translations',
                    severity: 'error'
                });
            }
        };

        input.click();
    };

    // Close notification
    const handleCloseNotification = () => {
        setNotification({
            ...notification,
            open: false
        });
    };

    // Close form
    const handleCloseForm = () => {
        setOpenForm(false);
    };

    // Get language name from code
    const getLanguageName = (code: string): string => {
        const language = LANGUAGES.find(l => l.code === code);
        return language ? language.name : code;
    };

    // Handle tab change
    const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
        setActiveTab(newValue);
    };

    // Toggle approval status
    const toggleApproval = async (translation: Translation) => {
        try {
            await axios.put(`/api/admin/translations/${translation.id}`, {
                ...translation,
                is_approved: !translation.is_approved
            });

            setNotification({
                open: true,
                message: `Translation ${!translation.is_approved ? 'approved' : 'unapproved'} successfully`,
                severity: 'success'
            });

            fetchTranslations();
        } catch (error) {
            console.error('Error updating translation:', error);
            setNotification({
                open: true,
                message: 'Failed to update translation',
                severity: 'error'
            });
        }
    };

    return (
        <Box sx={{ padding: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h5" component="h2">
                    <strong>Translation Management</strong>
                </Typography>
                <Box>
                    <Button
                        variant="outlined"
                        color="primary"
                        startIcon={<DownloadIcon />}
                        onClick={handleExport}
                        sx={{ mr: 1 }}
                    >
                        Export
                    </Button>
                    <Button
                        variant="outlined"
                        color="primary"
                        startIcon={<UploadIcon />}
                        onClick={handleImport}
                        sx={{ mr: 1 }}
                    >
                        Import
                    </Button>
                    <Button
                        variant="outlined"
                        color="primary"
                        startIcon={<RefreshIcon />}
                        onClick={fetchTranslations}
                        sx={{ mr: 1 }}
                    >
                        Refresh
                    </Button>
                    <Button
                        variant="contained"
                        color="primary"
                        startIcon={<AddIcon />}
                        onClick={handleAddNew}
                    >
                        Add Translation
                    </Button>
                </Box>
            </Box>

            {/* Language Statistics */}
            <Box mb={3}>
                <Typography variant="h6" gutterBottom>
                    Translation Status
                </Typography>
                <TableContainer component={Paper} sx={{ mb: 3 }}>
                    <Table size="small">
                        <TableHead>
                            <TableRow>
                                <TableCell>Language</TableCell>
                                <TableCell align="right">Total</TableCell>
                                <TableCell align="right">Approved</TableCell>
                                <TableCell align="right">Completion</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {languageStats.map((stat) => (
                                <TableRow key={stat.language}>
                                    <TableCell>
                                        <Box display="flex" alignItems="center">
                                            <TranslateIcon fontSize="small" sx={{ mr: 1 }} />
                                            {getLanguageName(stat.language)} ({stat.language})
                                        </Box>
                                    </TableCell>
                                    <TableCell align="right">{stat.count}</TableCell>
                                    <TableCell align="right">{stat.approved}</TableCell>
                                    <TableCell align="right">
                                        <Box display="flex" alignItems="center" justifyContent="flex-end">
                                            <Box
                                                sx={{
                                                    width: 100,
                                                    bgcolor: '#f0f0f0',
                                                    borderRadius: 1,
                                                    mr: 1,
                                                    height: 10,
                                                    position: 'relative'
                                                }}
                                            >
                                                <Box
                                                    sx={{
                                                        position: 'absolute',
                                                        height: '100%',
                                                        bgcolor: stat.approval_percentage >= 90 ? 'success.main' :
                                                            stat.approval_percentage >= 50 ? 'warning.main' :
                                                                'error.main',
                                                        width: `${stat.approval_percentage}%`,
                                                        borderRadius: 1
                                                    }}
                                                />
                                            </Box>
                                            {stat.approval_percentage}%
                                        </Box>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            </Box>

            {/* Tabs and Filters */}
            <Box mb={3}>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Tabs
                        value={activeTab}
                        onChange={handleTabChange}
                        sx={{ mb: 2 }}
                    >
                        <Tab value="all" label="All Translations" />
                        <Tab value="missing" label="Missing Translations" />
                    </Tabs>

                    <Box display="flex" alignItems="center">
                        <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                            <SearchIcon sx={{ mr: 1, color: 'grey.500' }} />
                            <TextField
                                placeholder="Search translations..."
                                variant="outlined"
                                size="small"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                        </Box>

                        <FormControl size="small" sx={{ minWidth: 120, mr: 2 }}>
                            <InputLabel id="language-filter-label">Language</InputLabel>
                            <Select
                                labelId="language-filter-label"
                                value={languageFilter}
                                label="Language"
                                onChange={(e) => setLanguageFilter(e.target.value)}
                                size="small"
                            >
                                <MenuItem value="all">All Languages</MenuItem>
                                {LANGUAGES.map(lang => (
                                    <MenuItem key={lang.code} value={lang.code}>
                                        {lang.name}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>

                        <FormControl size="small" sx={{ minWidth: 120 }}>
                            <InputLabel id="approval-filter-label">Status</InputLabel>
                            <Select
                                labelId="approval-filter-label"
                                value={approvalFilter}
                                label="Status"
                                onChange={(e) => setApprovalFilter(e.target.value)}
                                size="small"
                            >
                                <MenuItem value="all">All</MenuItem>
                                <MenuItem value="approved">Approved</MenuItem>
                                <MenuItem value="pending">Pending</MenuItem>
                            </Select>
                        </FormControl>
                    </Box>
                </Box>
            </Box>

            {loading ? (
                <Box display="flex" justifyContent="center" mt={4}>
                    <CircularProgress />
                </Box>
            ) : filteredTranslations.length === 0 ? (
                <Paper sx={{ p: 4, textAlign: 'center' }}>
                    <TranslateIcon color="disabled" sx={{ fontSize: 48, mb: 2 }} />
                    <Typography variant="h6" color="textSecondary">
                        No translations found
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                        Try changing the filters or add a new translation
                    </Typography>
                </Paper>
            ) : (
                <TableContainer component={Paper}>
                    <Table sx={{ minWidth: 650 }} aria-label="translations table">
                        <TableHead>
                            <TableRow>
                                <TableCell><strong>Key</strong></TableCell>
                                <TableCell><strong>Language</strong></TableCell>
                                <TableCell><strong>Text</strong></TableCell>
                                <TableCell><strong>Status</strong></TableCell>
                                <TableCell><strong>Actions</strong></TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {filteredTranslations.map((translation) => (
                                <TableRow key={translation.id}>
                                    <TableCell>
                                        <Typography variant="body2" fontFamily="monospace">
                                            {translation.key}
                                        </Typography>
                                    </TableCell>
                                    <TableCell>
                                        <Chip
                                            label={getLanguageName(translation.language)}
                                            size="small"
                                            icon={<TranslateIcon />}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <Tooltip title={translation.text} arrow placement="top">
                                            <Typography variant="body2" noWrap sx={{ maxWidth: 300 }}>
                                                {translation.text.length > 50
                                                    ? `${translation.text.substring(0, 50)}...`
                                                    : translation.text
                                                }
                                            </Typography>
                                        </Tooltip>
                                    </TableCell>
                                    <TableCell>
                                        <Chip
                                            label={translation.is_approved ? 'Approved' : 'Pending'}
                                            color={translation.is_approved ? 'success' : 'warning'}
                                            size="small"
                                            onClick={() => toggleApproval(translation)}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <IconButton
                                            onClick={() => handleEdit(translation)}
                                            color="primary"
                                            aria-label="edit translation"
                                        >
                                            <EditIcon />
                                        </IconButton>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            )}

            {/* Translation Form Dialog */}
            <Dialog open={openForm} onClose={handleCloseForm} maxWidth="md" fullWidth>
                <DialogTitle>
                    {editMode ? 'Edit Translation' : 'Add New Translation'}
                </DialogTitle>
                <DialogContent>
                    <Box sx={{ pt: 1 }}>
                        <TextField
                            name="key"
                            label="Translation Key"
                            value={formData.key}
                            onChange={handleInputChange}
                            fullWidth
                            required
                            margin="normal"
                            placeholder="e.g., common.welcome"
                            disabled={editMode}
                        />

                        <FormControl fullWidth margin="normal">
                            <InputLabel id="language-label">Language</InputLabel>
                            <Select
                                labelId="language-label"
                                name="language"
                                value={formData.language}
                                onChange={handleSelectChange}
                                label="Language"
                                required
                            >
                                {LANGUAGES.map(lang => (
                                    <MenuItem key={lang.code} value={lang.code}>
                                        {lang.name} ({lang.code})
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>

                        <TextField
                            name="text"
                            label="Translation Text"
                            value={formData.text}
                            onChange={handleInputChange}
                            fullWidth
                            required
                            margin="normal"
                            multiline
                            rows={4}
                            placeholder="Enter the translated text here"
                        />

                        <FormControl fullWidth margin="normal">
                            <Box display="flex" alignItems="center">
                                <input
                                    type="checkbox"
                                    id="is-approved"
                                    name="is_approved"
                                    checked={formData.is_approved}
                                    onChange={handleCheckboxChange}
                                    style={{ marginRight: 8 }}
                                />
                                <InputLabel htmlFor="is-approved" sx={{ transform: 'none', position: 'relative' }}>
                                    Mark as approved
                                </InputLabel>
                            </Box>
                        </FormControl>
                    </Box>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseForm} color="primary">
                        Cancel
                    </Button>
                    <Button onClick={handleSubmit} color="primary" variant="contained">
                        {editMode ? 'Update' : 'Add'} Translation
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Notification */}
            <Snackbar
                open={notification.open}
                autoHideDuration={6000}
                onClose={handleCloseNotification}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
            >
                <Alert onClose={handleCloseNotification} severity={notification.severity}>
                    {notification.message}
                </Alert>
            </Snackbar>
        </Box>
    );
};

export default TranslationManagement; 