import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import axios from 'axios';
import FeedbackManagement from './FeedbackManagement';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('FeedbackManagement Component', () => {
    // Sample data for tests
    const mockFeedbacks = {
        items: [
            {
                id: 1,
                user_id: 101,
                user_email: '<EMAIL>',
                type: 'bug',
                subject: 'Error when filtering certifications',
                message: 'When I try to filter certifications by domain, the page crashes.',
                status: 'new',
                rating: null,
                admin_notes: null,
                created_at: '2023-01-01T00:00:00Z',
                updated_at: null
            },
            {
                id: 2,
                user_id: null,
                user_email: '<EMAIL>',
                type: 'feature',
                subject: 'Request for dark mode',
                message: 'It would be nice to have a dark mode option for the application.',
                status: 'in_progress',
                rating: 4,
                admin_notes: 'Will be included in next sprint',
                created_at: '2023-01-02T00:00:00Z',
                updated_at: '2023-01-03T00:00:00Z'
            }
        ],
        total: 2
    };

    beforeEach(() => {
        // Reset mocks between tests
        jest.clearAllMocks();

        // Mock API responses
        mockedAxios.get.mockImplementation((url, config) => {
            if (url === '/api/admin/feedback') {
                return Promise.resolve({ data: mockFeedbacks });
            }
            return Promise.reject(new Error('Not found'));
        });
    });

    test('renders feedback management title', async () => {
        render(<FeedbackManagement />);

        // Check if the title is rendered
        const titleElement = screen.getByText(/Feedback Management/i);
        expect(titleElement).toBeInTheDocument();

        // Check loading indicator appears initially
        expect(screen.getByRole('progressbar')).toBeInTheDocument();

        // Wait for data to load
        await waitFor(() => {
            expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/feedback', expect.anything());
        });
    });

    test('displays feedback items in a table after loading', async () => {
        render(<FeedbackManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Check if feedback items are displayed
        expect(screen.getByText(/Error when filtering certifications/i)).toBeInTheDocument();
        expect(screen.getByText(/Request for dark mode/i)).toBeInTheDocument();

        // Check status chips
        const statusChips = screen.getAllByText(/new|in_progress/i);
        expect(statusChips.length).toBe(2);

        // Check email display
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    test('opens detail dialog when view button is clicked', async () => {
        render(<FeedbackManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Find and click view button for first feedback
        const viewButtons = screen.getAllByLabelText('view');
        fireEvent.click(viewButtons[0]);

        // Check if detail dialog is displayed
        expect(screen.getByText('Error when filtering certifications')).toBeInTheDocument();
        expect(screen.getByText('When I try to filter certifications by domain, the page crashes.')).toBeInTheDocument();

        // Check if status selector is available
        expect(screen.getByLabelText('Status')).toBeInTheDocument();

        // Check if admin notes field is available
        expect(screen.getByPlaceholderText(/Add notes about this feedback/i)).toBeInTheDocument();
    });

    test('updates feedback status when save changes is clicked', async () => {
        // Mock put request
        mockedAxios.put.mockResolvedValueOnce({ data: { success: true } });

        render(<FeedbackManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Open feedback detail
        const viewButtons = screen.getAllByLabelText('view');
        fireEvent.click(viewButtons[0]);

        // Change status
        const statusSelect = screen.getByLabelText('Status');
        fireEvent.mouseDown(statusSelect);
        const inProgressOption = screen.getByText('In Progress');
        fireEvent.click(inProgressOption);

        // Add admin notes
        const notesField = screen.getByPlaceholderText(/Add notes about this feedback/i);
        fireEvent.change(notesField, { target: { value: 'Working on a fix' } });

        // Save changes
        const saveButton = screen.getByText('Save Changes');
        fireEvent.click(saveButton);

        // Check if API was called with correct data
        await waitFor(() => {
            expect(mockedAxios.put).toHaveBeenCalledWith('/api/admin/feedback/1', {
                status: 'in_progress',
                admin_notes: 'Working on a fix'
            });
        });
    });

    test('deletes feedback when delete button is clicked', async () => {
        // Mock window.confirm to return true
        window.confirm = jest.fn().mockImplementation(() => true);

        // Mock delete request
        mockedAxios.delete.mockResolvedValueOnce({ data: { success: true } });

        render(<FeedbackManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Find and click delete button for first feedback
        const deleteButtons = screen.getAllByLabelText('delete');
        fireEvent.click(deleteButtons[0]);

        // Check if confirmation was requested
        expect(window.confirm).toHaveBeenCalled();

        // Check if API was called to delete
        await waitFor(() => {
            expect(mockedAxios.delete).toHaveBeenCalledWith('/api/admin/feedback/1');
        });
    });

    test('filters feedback when filter options are changed', async () => {
        // Mock API response for filtered results
        mockedAxios.get.mockImplementation((url, config) => {
            // Initial load
            if (!config || !config.params) {
                return Promise.resolve({ data: mockFeedbacks });
            }

            // Filtered response
            if (config.params.status === 'new') {
                return Promise.resolve({
                    data: {
                        items: [mockFeedbacks.items[0]],
                        total: 1
                    }
                });
            }

            return Promise.resolve({ data: mockFeedbacks });
        });

        render(<FeedbackManagement />);

        // Wait for initial data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Change status filter
        const statusFilter = screen.getByLabelText('Status');
        fireEvent.mouseDown(statusFilter);
        const newOption = screen.getByText('New');
        fireEvent.click(newOption);

        // Check if API was called with filter params
        await waitFor(() => {
            expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/feedback', {
                params: expect.objectContaining({
                    status: 'new'
                })
            });
        });

        // Should only show the bug feedback now
        await waitFor(() => {
            expect(screen.getByText(/Error when filtering certifications/i)).toBeInTheDocument();
            expect(screen.queryByText(/Request for dark mode/i)).not.toBeInTheDocument();
        });
    });
}); 