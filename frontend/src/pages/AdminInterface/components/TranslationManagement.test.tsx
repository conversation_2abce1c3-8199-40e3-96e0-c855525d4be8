import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import axios from 'axios';
import TranslationManagement from './TranslationManagement';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('TranslationManagement Component', () => {
    // Sample data for tests
    const mockTranslations = [
        {
            id: 1,
            key: 'common.welcome',
            language: 'en',
            text: 'Welcome to CertRats',
            is_approved: true,
            created_at: '2023-01-01T00:00:00Z',
            updated_at: null
        },
        {
            id: 2,
            key: 'common.welcome',
            language: 'es',
            text: 'Bienvenido a CertRats',
            is_approved: true,
            created_at: '2023-01-01T00:00:00Z',
            updated_at: null
        },
        {
            id: 3,
            key: 'common.logout',
            language: 'en',
            text: 'Logout',
            is_approved: true,
            created_at: '2023-01-01T00:00:00Z',
            updated_at: null
        },
        {
            id: 4,
            key: 'common.logout',
            language: 'es',
            text: 'Cerrar sesi<PERSON>',
            is_approved: false,
            created_at: '2023-01-01T00:00:00Z',
            updated_at: null
        }
    ];

    beforeEach(() => {
        // Reset mocks between tests
        jest.clearAllMocks();

        // Mock API responses
        mockedAxios.get.mockImplementation((url) => {
            if (url === '/api/admin/translations') {
                return Promise.resolve({ data: mockTranslations });
            }
            return Promise.reject(new Error('Not found'));
        });
    });

    test('renders translation management title', async () => {
        render(<TranslationManagement />);

        // Check if the title is rendered
        const titleElement = screen.getByText(/Translation Management/i);
        expect(titleElement).toBeInTheDocument();

        // Check loading indicator appears initially
        expect(screen.getByRole('progressbar')).toBeInTheDocument();

        // Wait for data to load
        await waitFor(() => {
            expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/translations');
        });
    });

    test('displays translation status table', async () => {
        render(<TranslationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Check if the status table is rendered
        expect(screen.getByText('Translation Status')).toBeInTheDocument();

        // Check language rows are displayed
        expect(screen.getByText(/English \(en\)/)).toBeInTheDocument();
        expect(screen.getByText(/Spanish \(es\)/)).toBeInTheDocument();
    });

    test('displays translations in a table after loading', async () => {
        render(<TranslationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Check if translations are displayed
        expect(screen.getByText('common.welcome')).toBeInTheDocument();
        expect(screen.getByText('common.logout')).toBeInTheDocument();
        expect(screen.getByText('Welcome to CertRats')).toBeInTheDocument();
        expect(screen.getByText('Bienvenido a CertRats')).toBeInTheDocument();

        // Check status chips
        const approvedChips = screen.getAllByText('Approved');
        const pendingChips = screen.getAllByText('Pending');
        expect(approvedChips.length).toBe(3); // 3 approved translations
        expect(pendingChips.length).toBe(1);  // 1 pending translation
    });

    test('opens translation form dialog when Add Translation button is clicked', async () => {
        render(<TranslationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Click Add Translation button
        fireEvent.click(screen.getByText('Add Translation'));

        // Check if dialog is displayed
        expect(screen.getByText('Add New Translation')).toBeInTheDocument();

        // Check if form fields are displayed
        expect(screen.getByLabelText('Translation Key')).toBeInTheDocument();
        expect(screen.getByLabelText('Language')).toBeInTheDocument();
        expect(screen.getByLabelText('Translation Text')).toBeInTheDocument();
        expect(screen.getByLabelText('Mark as approved')).toBeInTheDocument();
    });

    test('filters translations when filter is changed', async () => {
        render(<TranslationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Check all translations are displayed
        expect(screen.getAllByText(/common/).length).toBe(4); // 4 translation rows

        // Change language filter to Spanish
        fireEvent.mouseDown(screen.getByLabelText('Language'));
        fireEvent.click(screen.getByText('Spanish'));

        // Check only Spanish translations are displayed
        await waitFor(() => {
            const rows = screen.getAllByText(/common/);
            expect(rows.length).toBe(2); // Only Spanish translations
            expect(screen.getByText('Bienvenido a CertRats')).toBeInTheDocument();
            expect(screen.getByText('Cerrar sesión')).toBeInTheDocument();
        });
    });

    test('searches translations by query', async () => {
        render(<TranslationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Enter search query
        fireEvent.change(screen.getByPlaceholderText('Search translations...'), {
            target: { value: 'welcome' }
        });

        // Check only welcome-related translations are displayed
        await waitFor(() => {
            expect(screen.getByText('common.welcome')).toBeInTheDocument();
            expect(screen.queryByText('common.logout')).not.toBeInTheDocument();
        });
    });

    test('calls API to add translation when form is submitted', async () => {
        // Mock post request
        mockedAxios.post.mockResolvedValueOnce({ data: { success: true } });

        render(<TranslationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Click Add Translation button
        fireEvent.click(screen.getByText('Add Translation'));

        // Fill in form
        fireEvent.change(screen.getByLabelText('Translation Key'), {
            target: { value: 'common.hello' }
        });

        // Select language
        fireEvent.mouseDown(screen.getByLabelText('Language'));
        fireEvent.click(screen.getByText(/English/));

        // Enter translation text
        fireEvent.change(screen.getByLabelText('Translation Text'), {
            target: { value: 'Hello' }
        });

        // Submit form
        fireEvent.click(screen.getByText('Add Translation'));

        // Check if API was called with correct data
        await waitFor(() => {
            expect(mockedAxios.post).toHaveBeenCalledWith('/api/admin/translations', expect.objectContaining({
                key: 'common.hello',
                text: 'Hello'
            }));
        });
    });

    test('calls API to update translation when form is submitted in edit mode', async () => {
        // Mock put request
        mockedAxios.put.mockResolvedValueOnce({ data: { success: true } });

        render(<TranslationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Find and click edit button for first translation
        const editButtons = screen.getAllByLabelText('edit translation');
        fireEvent.click(editButtons[0]);

        // Check if form is displayed with correct title
        expect(screen.getByText('Edit Translation')).toBeInTheDocument();

        // Modify translation text
        fireEvent.change(screen.getByLabelText('Translation Text'), {
            target: { value: 'Updated welcome message' }
        });

        // Submit form
        fireEvent.click(screen.getByText('Update Translation'));

        // Check if API was called with correct data
        await waitFor(() => {
            expect(mockedAxios.put).toHaveBeenCalledWith('/api/admin/translations/1', expect.objectContaining({
                text: 'Updated welcome message'
            }));
        });
    });

    test('calls API to export translations', async () => {
        // Mock the Blob API
        global.URL.createObjectURL = jest.fn();
        const mockCreateElement = jest.spyOn(document, 'createElement');
        const mockAppendChild = jest.spyOn(document.body, 'appendChild');
        const mockRemoveChild = jest.spyOn(document.body, 'removeChild');
        const mockClick = jest.fn();

        mockCreateElement.mockImplementation((tag) => {
            if (tag === 'a') {
                return {
                    href: '',
                    setAttribute: jest.fn(),
                    click: mockClick
                } as unknown as HTMLAnchorElement;
            }
            return document.createElement(tag);
        });

        mockAppendChild.mockImplementation(() => document.body);
        mockRemoveChild.mockImplementation(() => document.body);

        // Mock get request with responseType
        mockedAxios.get.mockImplementation((url, config) => {
            if (url === '/api/admin/translations/export') {
                return Promise.resolve({ data: new Blob(['{}']) });
            }
            if (url === '/api/admin/translations') {
                return Promise.resolve({ data: mockTranslations });
            }
            return Promise.reject(new Error('Not found'));
        });

        render(<TranslationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Click Export button
        fireEvent.click(screen.getByText('Export'));

        // Check if API was called with correct URL and click was triggered
        await waitFor(() => {
            expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/translations/export', expect.anything());
            expect(mockClick).toHaveBeenCalled();
        });

        // Clean up mocks
        mockCreateElement.mockRestore();
        mockAppendChild.mockRestore();
        mockRemoveChild.mockRestore();
    });
}); 