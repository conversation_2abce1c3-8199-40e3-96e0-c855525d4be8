import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import axios from 'axios';
import OrganizationManagement from './OrganizationManagement';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('OrganizationManagement Component', () => {
    // Sample data for tests
    const mockOrganizations = [
        {
            id: 1,
            name: 'ISC2',
            website: 'https://www.isc2.org',
            description: 'International Information System Security Certification Consortium',
            created_at: '2023-01-01T00:00:00Z',
            updated_at: null
        },
        {
            id: 2,
            name: 'CompTIA',
            website: 'https://www.comptia.org',
            description: 'Computing Technology Industry Association',
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-02-01T00:00:00Z'
        }
    ];

    beforeEach(() => {
        // Reset mocks between tests
        jest.clearAllMocks();

        // Mock API responses
        mockedAxios.get.mockImplementation((url) => {
            if (url === '/api/admin/organizations') {
                return Promise.resolve({ data: mockOrganizations });
            } else if (url.includes('/certifications')) {
                // Mock response for organization's certifications check
                return Promise.resolve({ data: [] });
            }
            return Promise.reject(new Error('Not found'));
        });
    });

    test('renders organization management title', async () => {
        render(<OrganizationManagement />);

        // Check if the title is rendered
        const titleElement = screen.getByText(/Organization Management/i);
        expect(titleElement).toBeInTheDocument();

        // Check loading indicator appears initially
        expect(screen.getByRole('progressbar')).toBeInTheDocument();

        // Wait for data to load
        await waitFor(() => {
            expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/organizations');
        });
    });

    test('displays organizations in a table after loading', async () => {
        render(<OrganizationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Check if organizations are displayed
        expect(screen.getByText('ISC2')).toBeInTheDocument();
        expect(screen.getByText('CompTIA')).toBeInTheDocument();

        // Check website links
        const links = screen.getAllByRole('link');
        expect(links[0]).toHaveAttribute('href', 'https://www.isc2.org');
        expect(links[1]).toHaveAttribute('href', 'https://www.comptia.org');
    });

    test('opens add form when Add New button is clicked', async () => {
        render(<OrganizationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Click Add New button
        fireEvent.click(screen.getByText('Add New'));

        // Check if form is displayed
        expect(screen.getByText('Add New Organization')).toBeInTheDocument();

        // Check if form fields are empty
        expect(screen.getByLabelText(/organization name/i)).toHaveValue('');
    });

    test('opens edit form with data when Edit button is clicked', async () => {
        render(<OrganizationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Find and click edit button for first organization
        const editButtons = screen.getAllByLabelText('edit');
        fireEvent.click(editButtons[0]);

        // Check if form is displayed with correct title
        expect(screen.getByText('Edit Organization')).toBeInTheDocument();

        // Check if form fields are populated with organization data
        expect(screen.getByLabelText(/organization name/i)).toHaveValue('ISC2');
        expect(screen.getByLabelText(/website url/i)).toHaveValue('https://www.isc2.org');
    });

    test('calls API to add organization when form is submitted', async () => {
        // Mock post request
        mockedAxios.post.mockResolvedValueOnce({ data: { success: true } });

        render(<OrganizationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Click Add New button
        fireEvent.click(screen.getByText('Add New'));

        // Fill in form
        fireEvent.change(screen.getByLabelText(/organization name/i), {
            target: { value: 'EC-Council' }
        });

        fireEvent.change(screen.getByLabelText(/website url/i), {
            target: { value: 'https://www.eccouncil.org' }
        });

        // Submit form
        fireEvent.click(screen.getByText('Add Organization'));

        // Check if API was called with correct data
        await waitFor(() => {
            expect(mockedAxios.post).toHaveBeenCalledWith('/api/admin/organizations', expect.objectContaining({
                name: 'EC-Council',
                website: 'https://www.eccouncil.org'
            }));
        });
    });

    test('prevents deletion if organization has certifications', async () => {
        // Mock certifications check to return certifications
        mockedAxios.get.mockImplementation((url) => {
            if (url === '/api/admin/organizations') {
                return Promise.resolve({ data: mockOrganizations });
            } else if (url.includes('/certifications')) {
                return Promise.resolve({ data: [{ id: 1, name: 'CISSP' }] });
            }
            return Promise.reject(new Error('Not found'));
        });

        render(<OrganizationManagement />);

        // Wait for data to load
        await waitFor(() => {
            expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
        });

        // Find and click delete button for first organization
        const deleteButtons = screen.getAllByLabelText('delete');
        fireEvent.click(deleteButtons[0]);

        // Check if warning is displayed
        await waitFor(() => {
            expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/organizations/1/certifications');
        });

        // The delete API should not be called
        expect(mockedAxios.delete).not.toHaveBeenCalled();
    });
}); 