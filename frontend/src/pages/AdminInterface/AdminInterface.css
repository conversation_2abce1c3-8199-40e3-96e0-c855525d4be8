.admin-interface {
  padding: 20px;
}

.admin-section {
  margin-bottom: 30px;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
}

.admin-table th,
.admin-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.admin-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.admin-table tr:hover {
  background-color: #f5f5f5;
}

.admin-btn {
  padding: 5px 10px;
  margin-right: 5px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.admin-btn.edit {
  background-color: #007bff;
  color: white;
}

.admin-btn.delete {
  background-color: #dc3545;
  color: white;
}

.admin-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 18px;
  color: #6c757d;
}

.admin-error {
  color: #dc3545;
  padding: 20px;
  text-align: center;
  font-size: 18px;
}

.admin-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.stat-card {
  width: 30%;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  margin: 10px 0;
  color: #007bff;
} 