import React, { useEffect, useState } from 'react';
import './AdminInterface.css';

interface AdminUser {
    id: number;
    username: string;
    email: string;
    role: string;
    lastLogin: string;
}

const mockUsers: AdminUser[] = [
    {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 'Administrator',
        lastLogin: '2023-05-01T10:30:00Z'
    },
    {
        id: 2,
        username: 'manager',
        email: '<EMAIL>',
        role: 'Manager',
        lastLogin: '2023-05-02T14:45:00Z'
    },
    {
        id: 3,
        username: 'analyst',
        email: '<EMAIL>',
        role: 'Analyst',
        lastLogin: '2023-05-03T09:15:00Z'
    }
];

const AdminInterface: React.FC = () => {
    const [users, setUsers] = useState<AdminUser[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        // Simulate API call
        const fetchUsers = async () => {
            try {
                // In a real app, this would be an API call
                // const response = await fetch('/api/v1/admin/users');
                // const data = await response.json();

                // Using mock data for now
                setTimeout(() => {
                    setUsers(mockUsers);
                    setIsLoading(false);
                }, 800);
            } catch (err) {
                setError('Failed to load user data');
                setIsLoading(false);
            }
        };

        fetchUsers();
    }, []);

    if (isLoading) {
        return <div className="admin-loading">Loading admin data...</div>;
    }

    if (error) {
        return <div className="admin-error">{error}</div>;
    }

    return (
        <div className="admin-interface">
            <h2>Admin Interface</h2>

            <div className="admin-section">
                <h3>User Management</h3>
                <table className="admin-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {users.map(user => (
                            <tr key={user.id}>
                                <td>{user.id}</td>
                                <td>{user.username}</td>
                                <td>{user.email}</td>
                                <td>{user.role}</td>
                                <td>{new Date(user.lastLogin).toLocaleString()}</td>
                                <td>
                                    <button className="admin-btn edit">Edit</button>
                                    <button className="admin-btn delete">Delete</button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            <div className="admin-section">
                <h3>System Statistics</h3>
                <div className="admin-stats">
                    <div className="stat-card">
                        <h4>Total Users</h4>
                        <p className="stat-value">1,245</p>
                    </div>
                    <div className="stat-card">
                        <h4>Active Certifications</h4>
                        <p className="stat-value">87</p>
                    </div>
                    <div className="stat-card">
                        <h4>Study Sessions Today</h4>
                        <p className="stat-value">142</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AdminInterface; 