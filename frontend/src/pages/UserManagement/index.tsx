import React, { useEffect, useState } from 'react';
import axios from 'axios';
import './styles.css';

// Define interfaces based on our API models
interface User {
  id: number;
  username: string;
  email: string;
  full_name: string | null;
  role: string;
  status: string;
  created_at: string;
  updated_at: string;
  last_login: string | null;
}

interface UserStats {
  total_users: number;
  active_users: number;
  users_by_role: Record<string, number>;
  users_by_status: Record<string, number>;
  new_users_last_30_days: number;
}

interface UserListResponse {
  users: User[];
  total: number;
  page: number;
  page_size: number;
}

interface UserFormData {
  username: string;
  email: string;
  full_name: string;
  password: string;
  role: string;
  status: string;
}

interface FilterState {
  role: string;
  status: string;
  search: string;
}

const UserManagement: React.FC = () => {
  // State for users data
  const [users, setUsers] = useState<User[]>([]);
  const [stats, setStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // State for pagination
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [totalUsers, setTotalUsers] = useState<number>(0);
  
  // State for filters
  const [filters, setFilters] = useState<FilterState>({
    role: '',
    status: '',
    search: ''
  });
  
  // State for form
  const [showForm, setShowForm] = useState<boolean>(false);
  const [formData, setFormData] = useState<UserFormData>({
    username: '',
    email: '',
    full_name: '',
    password: '',
    role: 'user',
    status: 'active'
  });
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [editUserId, setEditUserId] = useState<number | null>(null);
  
  // Fetch users data
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        
        // Calculate skip for pagination
        const skip = (page - 1) * pageSize;
        
        // Build query parameters
        const params = new URLSearchParams();
        params.append('skip', skip.toString());
        params.append('limit', pageSize.toString());
        if (filters.role) params.append('role', filters.role);
        if (filters.status) params.append('status', filters.status);
        if (filters.search) params.append('search', filters.search);
        
        const response = await axios.get<UserListResponse>(`/api/users?${params.toString()}`);
        setUsers(response.data.users);
        setTotalUsers(response.data.total);
        setError(null);
      } catch (err) {
        console.error('Error fetching users:', err);
        setError('Failed to load users. Please try again later.');
        // For development, use mock data when API is not available
        const mockUsers: User[] = [
          {
            id: 1,
            username: 'admin',
            email: '<EMAIL>',
            full_name: 'Admin User',
            role: 'admin',
            status: 'active',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            last_login: new Date().toISOString()
          },
          {
            id: 2,
            username: 'user1',
            email: '<EMAIL>',
            full_name: 'Regular User',
            role: 'user',
            status: 'active',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            last_login: null
          }
        ];
        setUsers(mockUsers);
        setTotalUsers(mockUsers.length);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUsers();
  }, [page, pageSize, filters]);
  
  // Fetch user stats
  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await axios.get<UserStats>('/api/users/stats');
        setStats(response.data);
      } catch (err) {
        console.error('Error fetching user stats:', err);
        // For development, use mock data when API is not available
        setStats({
          total_users: 10,
          active_users: 8,
          users_by_role: {
            admin: 2,
            user: 8
          },
          users_by_status: {
            active: 8,
            inactive: 2
          },
          new_users_last_30_days: 3
        });
      }
    };
    
    fetchStats();
  }, []);
  
  // Handle filter changes
  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters((prev: FilterState) => ({ ...prev, [name]: value }));
    setPage(1); // Reset to first page when filters change
  };
  
  // Handle form input changes
  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev: UserFormData) => ({ ...prev, [name]: value }));
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (formMode === 'create') {
        // Create new user
        await axios.post('/api/users', formData);
      } else {
        // Update existing user
        if (editUserId) {
          // Remove password if empty (not changing password)
          type UserUpdateData = {
            [key: string]: any;
            password?: string;
          };
          
          const updateData: UserUpdateData = { ...formData };
          if (!updateData.password) {
            delete updateData.password;
          }
          
          await axios.put(`/api/users/${editUserId}`, updateData);
        }
      }
      
      // Reset form and refresh data
      setShowForm(false);
      setFormData({
        username: '',
        email: '',
        full_name: '',
        password: '',
        role: 'user',
        status: 'active'
      });
      setEditUserId(null);
      
      // Refresh users list
      const skip = (page - 1) * pageSize;
      const params = new URLSearchParams();
      params.append('skip', skip.toString());
      params.append('limit', pageSize.toString());
      if (filters.role) params.append('role', filters.role);
      if (filters.status) params.append('status', filters.status);
      if (filters.search) params.append('search', filters.search);
      
      const response = await axios.get<UserListResponse>(`/api/users?${params.toString()}`);
      setUsers(response.data.users);
      setTotalUsers(response.data.total);
      
      // Refresh stats
      const statsResponse = await axios.get<UserStats>('/api/users/stats');
      setStats(statsResponse.data);
    } catch (err) {
      console.error('Error submitting form:', err);
      setError('Failed to save user. Please try again later.');
    }
  };
  
  // Handle edit user
  const handleEditUser = (user: User) => {
    setFormData({
      username: user.username,
      email: user.email,
      full_name: user.full_name || '',
      password: '', // Don't include password in edit form
      role: user.role,
      status: user.status
    });
    setEditUserId(user.id);
    setFormMode('edit');
    setShowForm(true);
  };
  
  // Handle delete user
  const handleDeleteUser = async (userId: number) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      try {
        await axios.delete(`/api/users/${userId}`);
        
        // Refresh users list
        const skip = (page - 1) * pageSize;
        const params = new URLSearchParams();
        params.append('skip', skip.toString());
        params.append('limit', pageSize.toString());
        if (filters.role) params.append('role', filters.role);
        if (filters.status) params.append('status', filters.status);
        if (filters.search) params.append('search', filters.search);
        
        const response = await axios.get<UserListResponse>(`/api/users?${params.toString()}`);
        setUsers(response.data.users);
        setTotalUsers(response.data.total);
        
        // Refresh stats
        const statsResponse = await axios.get<UserStats>('/api/users/stats');
        setStats(statsResponse.data);
      } catch (err) {
        console.error('Error deleting user:', err);
        setError('Failed to delete user. Please try again later.');
      }
    }
  };
  
  // Calculate total pages
  const totalPages = Math.ceil(totalUsers / pageSize);
  
  if (loading && !users.length) {
    return <div className="user-management-loading">Loading users...</div>;
  }
  
  return (
    <div className="user-management-container">
      <h1>User Management</h1>
      
      {/* Stats Overview */}
      {stats && (
        <div className="user-stats">
          <div className="stat-card">
            <h3>{stats.total_users}</h3>
            <p>Total Users</p>
          </div>
          <div className="stat-card">
            <h3>{stats.active_users}</h3>
            <p>Active Users</p>
          </div>
          <div className="stat-card">
            <h3>{stats.new_users_last_30_days}</h3>
            <p>New Users (30 days)</p>
          </div>
        </div>
      )}
      
      {/* Filters */}
      <div className="user-filters">
        <div className="filter-item">
          <label htmlFor="search">Search:</label>
          <input
            type="text"
            id="search"
            name="search"
            value={filters.search}
            onChange={handleFilterChange}
            placeholder="Search users..."
          />
        </div>
        
        <div className="filter-item">
          <label htmlFor="role">Role:</label>
          <select
            id="role"
            name="role"
            value={filters.role}
            onChange={handleFilterChange}
          >
            <option value="">All Roles</option>
            <option value="admin">Admin</option>
            <option value="user">User</option>
            <option value="guest">Guest</option>
          </select>
        </div>
        
        <div className="filter-item">
          <label htmlFor="status">Status:</label>
          <select
            id="status"
            name="status"
            value={filters.status}
            onChange={handleFilterChange}
          >
            <option value="">All Statuses</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="pending">Pending</option>
            <option value="suspended">Suspended</option>
          </select>
        </div>
        
        <button 
          className="add-user-btn"
          onClick={() => {
            setFormMode('create');
            setFormData({
              username: '',
              email: '',
              full_name: '',
              password: '',
              role: 'user',
              status: 'active'
            });
            setEditUserId(null);
            setShowForm(true);
          }}
        >
          Add User
        </button>
      </div>
      
      {/* User Form */}
      {showForm && (
        <div className="user-form-container">
          <div className="user-form">
            <h2>{formMode === 'create' ? 'Add New User' : 'Edit User'}</h2>
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="username">Username:</label>
                <input
                  type="text"
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleFormChange}
                  required
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="email">Email:</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleFormChange}
                  required
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="full_name">Full Name:</label>
                <input
                  type="text"
                  id="full_name"
                  name="full_name"
                  value={formData.full_name}
                  onChange={handleFormChange}
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="password">
                  {formMode === 'create' ? 'Password:' : 'Password (leave empty to keep current):'}
                </label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleFormChange}
                  required={formMode === 'create'}
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="role">Role:</label>
                <select
                  id="role"
                  name="role"
                  value={formData.role}
                  onChange={handleFormChange}
                  required
                >
                  <option value="admin">Admin</option>
                  <option value="user">User</option>
                  <option value="guest">Guest</option>
                </select>
              </div>
              
              <div className="form-group">
                <label htmlFor="status">Status:</label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleFormChange}
                  required
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="pending">Pending</option>
                  <option value="suspended">Suspended</option>
                </select>
              </div>
              
              <div className="form-actions">
                <button type="submit" className="save-btn">
                  {formMode === 'create' ? 'Create User' : 'Update User'}
                </button>
                <button 
                  type="button" 
                  className="cancel-btn"
                  onClick={() => setShowForm(false)}
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
      
      {/* Error Message */}
      {error && (
        <div className="error-message">
          {error}
        </div>
      )}
      
      {/* Users Table */}
      <div className="users-table-container">
        <table className="users-table">
          <thead>
            <tr>
              <th>ID</th>
              <th>Username</th>
              <th>Email</th>
              <th>Full Name</th>
              <th>Role</th>
              <th>Status</th>
              <th>Created</th>
              <th>Last Login</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {users.map((user: User) => (
              <tr key={user.id}>
                <td>{user.id}</td>
                <td>{user.username}</td>
                <td>{user.email}</td>
                <td>{user.full_name || '-'}</td>
                <td>
                  <span className={`role-badge role-${user.role}`}>
                    {user.role}
                  </span>
                </td>
                <td>
                  <span className={`status-badge status-${user.status}`}>
                    {user.status}
                  </span>
                </td>
                <td>{new Date(user.created_at).toLocaleDateString()}</td>
                <td>{user.last_login ? new Date(user.last_login).toLocaleDateString() : '-'}</td>
                <td>
                  <div className="action-buttons">
                    <button 
                      className="edit-btn"
                      onClick={() => handleEditUser(user)}
                    >
                      Edit
                    </button>
                    <button 
                      className="delete-btn"
                      onClick={() => handleDeleteUser(user.id)}
                    >
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {/* Pagination */}
      <div className="pagination">
        <button 
          onClick={() => setPage((p: number) => Math.max(1, p - 1))}
          disabled={page === 1}
        >
          Previous
        </button>
        
        <span className="page-info">
          Page {page} of {totalPages}
        </span>
        
        <button 
          onClick={() => setPage((p: number) => Math.min(totalPages, p + 1))}
          disabled={page === totalPages}
        >
          Next
        </button>
        
        <select
          value={pageSize}
          onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
            setPageSize(Number(e.target.value));
            setPage(1); // Reset to first page when page size changes
          }}
        >
          <option value="5">5 per page</option>
          <option value="10">10 per page</option>
          <option value="20">20 per page</option>
          <option value="50">50 per page</option>
        </select>
      </div>
    </div>
  );
};

export default UserManagement;
