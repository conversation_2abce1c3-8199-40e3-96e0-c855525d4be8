import React from 'react';
import { Link } from 'react-router-dom';
import '../utils/error/ErrorStyles.css';

/**
 * NotFound component displayed when a route doesn't match any defined routes
 * Using our standard error styling for consistent UI
 */
const NotFound: React.FC = () => {
  return (
    <div className="not-found-container">
      <h1>404</h1>
      <h2>Page Not Found</h2>
      <p>The page you are looking for does not exist or has been moved.</p>

      <div className="error-actions">
        <Link to="/">
          <button className="retry-button">Return to Dashboard</button>
        </Link>
        <button
          className="home-button"
          onClick={() => window.history.back()}
        >
          Go Back
        </button>
      </div>
    </div>
  );
};

export default NotFound; 