import axios from 'axios';
import React, { useEffect, useState } from 'react';
import './styles.css';

// Define interfaces for data models
interface Certification {
    id: number;
    name: string;
    vendor: string;
    level: string;
}

interface Skill {
    id: number;
    name: string;
    category: string;
}

interface JobListing {
    id: number;
    title: string;
    company: string;
    location: string;
    remote: boolean;
    salary_range: {
        min: number;
        max: number;
        currency: string;
    };
    description: string;
    requirements: {
        required_certifications: number[];
        preferred_certifications: number[];
        required_skills: number[];
        preferred_skills: number[];
        experience_years: number;
    };
    posted_date: string;
    application_url: string;
    job_type: 'full-time' | 'part-time' | 'contract' | 'internship';
    match_score?: number; // Optional field to indicate match with user profile
}

interface UserProfile {
    certifications: number[];
    skills: number[];
    experience_years: number;
}

const JobSearch: React.FC = () => {
    // State variables
    const [jobListings, setJobListings] = useState<JobListing[]>([]);
    const [filteredJobs, setFilteredJobs] = useState<JobListing[]>([]);
    const [certifications, setCertifications] = useState<Record<number, Certification>>({});
    const [skills, setSkills] = useState<Record<number, Skill>>({});
    const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [locationFilter, setLocationFilter] = useState<string>('');
    const [remoteFilter, setRemoteFilter] = useState<string>('all');
    const [experienceFilter, setExperienceFilter] = useState<string>('all');
    const [sortBy, setSortBy] = useState<string>('relevance');
    const [selectedJob, setSelectedJob] = useState<number | null>(null);
    const [showCertificationFilter, setShowCertificationFilter] = useState<boolean>(false);
    const [certificationFilter, setCertificationFilter] = useState<number[]>([]);

    // Fetch data
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);

                // Fetch job listings
                const jobsResponse = await axios.get('/api/jobs');
                setJobListings(jobsResponse.data);

                // Fetch certifications
                const certificationsResponse = await axios.get('/api/certifications');
                const certMap: Record<number, Certification> = {};
                certificationsResponse.data.forEach((cert: Certification) => {
                    certMap[cert.id] = cert;
                });
                setCertifications(certMap);

                // Fetch skills
                const skillsResponse = await axios.get('/api/skills');
                const skillMap: Record<number, Skill> = {};
                skillsResponse.data.forEach((skill: Skill) => {
                    skillMap[skill.id] = skill;
                });
                setSkills(skillMap);

                // Fetch user profile
                const userProfileResponse = await axios.get('/api/user/profile');
                setUserProfile(userProfileResponse.data);

                setError(null);
            } catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load job data. Please try again later.');

                // Mock data for development
                const mockCertifications = [
                    { id: 1, name: 'CompTIA A+', vendor: 'CompTIA', level: 'beginner' },
                    { id: 2, name: 'CompTIA Network+', vendor: 'CompTIA', level: 'beginner' },
                    { id: 3, name: 'CompTIA Security+', vendor: 'CompTIA', level: 'intermediate' },
                    { id: 4, name: 'CCNA', vendor: 'Cisco', level: 'intermediate' },
                    { id: 5, name: 'AWS Solutions Architect Associate', vendor: 'AWS', level: 'intermediate' },
                    { id: 6, name: 'AWS Solutions Architect Professional', vendor: 'AWS', level: 'advanced' },
                    { id: 7, name: 'CISSP', vendor: 'ISC2', level: 'advanced' },
                    { id: 8, name: 'Microsoft Azure Administrator', vendor: 'Microsoft', level: 'intermediate' }
                ] as Certification[];

                const mockSkills = [
                    { id: 1, name: 'Linux', category: 'Operating Systems' },
                    { id: 2, name: 'Windows Server', category: 'Operating Systems' },
                    { id: 3, name: 'Python', category: 'Programming' },
                    { id: 4, name: 'SQL', category: 'Database' },
                    { id: 5, name: 'Networking', category: 'Infrastructure' },
                    { id: 6, name: 'Cloud Computing', category: 'Infrastructure' },
                    { id: 7, name: 'Cybersecurity', category: 'Security' },
                    { id: 8, name: 'Docker', category: 'DevOps' },
                    { id: 9, name: 'Kubernetes', category: 'DevOps' },
                    { id: 10, name: 'AWS', category: 'Cloud' }
                ] as Skill[];

                const mockJobs = [
                    {
                        id: 1,
                        title: 'Network Administrator',
                        company: 'TechCorp Solutions',
                        location: 'New York, NY',
                        remote: false,
                        salary_range: {
                            min: 65000,
                            max: 85000,
                            currency: 'USD'
                        },
                        description: 'We are seeking a skilled Network Administrator to join our IT team. You will be responsible for maintaining our network infrastructure, ensuring uptime, and implementing security measures.',
                        requirements: {
                            required_certifications: [2, 4],
                            preferred_certifications: [3],
                            required_skills: [5, 2],
                            preferred_skills: [8, 1],
                            experience_years: 2
                        },
                        posted_date: '2023-05-15',
                        application_url: 'https://example.com/jobs/network-admin',
                        job_type: 'full-time',
                        match_score: 85
                    },
                    {
                        id: 2,
                        title: 'Cloud Solutions Architect',
                        company: 'InnoCloud',
                        location: 'Seattle, WA',
                        remote: true,
                        salary_range: {
                            min: 110000,
                            max: 150000,
                            currency: 'USD'
                        },
                        description: 'Join our team as a Cloud Solutions Architect to design and implement scalable cloud solutions for our clients. You will work with cutting-edge technologies and have the opportunity to innovate.',
                        requirements: {
                            required_certifications: [5, 8],
                            preferred_certifications: [6],
                            required_skills: [6, 10],
                            preferred_skills: [8, 9, 3],
                            experience_years: 4
                        },
                        posted_date: '2023-05-10',
                        application_url: 'https://example.com/jobs/cloud-architect',
                        job_type: 'full-time',
                        match_score: 92
                    },
                    {
                        id: 3,
                        title: 'Cybersecurity Analyst',
                        company: 'SecureDefense',
                        location: 'Washington, DC',
                        remote: false,
                        salary_range: {
                            min: 80000,
                            max: 110000,
                            currency: 'USD'
                        },
                        description: 'As a Cybersecurity Analyst, you will be responsible for monitoring our security systems, identifying vulnerabilities, and implementing security measures to protect our infrastructure.',
                        requirements: {
                            required_certifications: [3],
                            preferred_certifications: [7],
                            required_skills: [7, 1],
                            preferred_skills: [5, 3],
                            experience_years: 3
                        },
                        posted_date: '2023-05-08',
                        application_url: 'https://example.com/jobs/security-analyst',
                        job_type: 'full-time',
                        match_score: 78
                    },
                    {
                        id: 4,
                        title: 'DevOps Engineer',
                        company: 'TechInnovate',
                        location: 'San Francisco, CA',
                        remote: true,
                        salary_range: {
                            min: 100000,
                            max: 140000,
                            currency: 'USD'
                        },
                        description: 'We are looking for a DevOps Engineer to help us streamline our development and deployment processes. You will be responsible for maintaining our CI/CD pipelines and cloud infrastructure.',
                        requirements: {
                            required_certifications: [5],
                            preferred_certifications: [6, 8],
                            required_skills: [8, 9, 3],
                            preferred_skills: [6, 10, 4],
                            experience_years: 3
                        },
                        posted_date: '2023-05-05',
                        application_url: 'https://example.com/jobs/devops-engineer',
                        job_type: 'full-time',
                        match_score: 88
                    },
                    {
                        id: 5,
                        title: 'IT Support Specialist',
                        company: 'Global Solutions Inc.',
                        location: 'Chicago, IL',
                        remote: false,
                        salary_range: {
                            min: 45000,
                            max: 60000,
                            currency: 'USD'
                        },
                        description: 'Join our IT Support team to provide technical support to our employees. You will troubleshoot hardware and software issues, manage user accounts, and maintain our IT systems.',
                        requirements: {
                            required_certifications: [1],
                            preferred_certifications: [2],
                            required_skills: [2, 1],
                            preferred_skills: [5, 3],
                            experience_years: 1
                        },
                        posted_date: '2023-05-01',
                        application_url: 'https://example.com/jobs/it-support',
                        job_type: 'full-time',
                        match_score: 70
                    },
                    {
                        id: 6,
                        title: 'Part-time Network Technician',
                        company: 'Local IT Services',
                        location: 'Austin, TX',
                        remote: false,
                        salary_range: {
                            min: 25,
                            max: 35,
                            currency: 'USD/hour'
                        },
                        description: 'We are seeking a part-time Network Technician to assist with network maintenance and troubleshooting. This is a great opportunity for someone looking to gain experience in the field.',
                        requirements: {
                            required_certifications: [2],
                            preferred_certifications: [4],
                            required_skills: [5],
                            preferred_skills: [1, 2],
                            experience_years: 1
                        },
                        posted_date: '2023-04-28',
                        application_url: 'https://example.com/jobs/network-tech',
                        job_type: 'part-time',
                        match_score: 65
                    }
                ] as JobListing[];

                const mockUserProfile = {
                    certifications: [1, 2, 5],
                    skills: [1, 2, 3, 5, 10],
                    experience_years: 3
                } as UserProfile;

                // Convert certifications and skills arrays to records
                const certMap: Record<number, Certification> = {};
                mockCertifications.forEach(cert => {
                    certMap[cert.id] = cert;
                });

                const skillMap: Record<number, Skill> = {};
                mockSkills.forEach(skill => {
                    skillMap[skill.id] = skill;
                });

                setCertifications(certMap);
                setSkills(skillMap);
                setJobListings(mockJobs);
                setUserProfile(mockUserProfile);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    // Apply filters to job listings
    useEffect(() => {
        let filtered = [...jobListings];

        // Apply search query filter
        if (searchQuery) {
            const query = searchQuery.toLowerCase();
            filtered = filtered.filter(job =>
                job.title.toLowerCase().includes(query) ||
                job.company.toLowerCase().includes(query) ||
                job.description.toLowerCase().includes(query)
            );
        }

        // Apply location filter
        if (locationFilter) {
            const location = locationFilter.toLowerCase();
            filtered = filtered.filter(job =>
                job.location.toLowerCase().includes(location)
            );
        }

        // Apply remote filter
        if (remoteFilter !== 'all') {
            const isRemote = remoteFilter === 'remote';
            filtered = filtered.filter(job => job.remote === isRemote);
        }

        // Apply experience filter
        if (experienceFilter !== 'all') {
            const maxExperience = parseInt(experienceFilter);
            filtered = filtered.filter(job =>
                job.requirements.experience_years <= maxExperience
            );
        }

        // Apply certification filter
        if (certificationFilter.length > 0) {
            filtered = filtered.filter(job => {
                // Check if any of the selected certifications are required or preferred for the job
                return certificationFilter.some(certId =>
                    job.requirements.required_certifications.includes(certId) ||
                    job.requirements.preferred_certifications.includes(certId)
                );
            });
        }

        // Apply sorting
        if (sortBy === 'relevance') {
            filtered.sort((a, b) => (b.match_score || 0) - (a.match_score || 0));
        } else if (sortBy === 'date') {
            filtered.sort((a, b) => new Date(b.posted_date).getTime() - new Date(a.posted_date).getTime());
        } else if (sortBy === 'salary') {
            filtered.sort((a, b) => b.salary_range.max - a.salary_range.max);
        }

        setFilteredJobs(filtered);
    }, [jobListings, searchQuery, locationFilter, remoteFilter, experienceFilter, certificationFilter, sortBy]);

    // Reset filters
    const resetFilters = () => {
        setSearchQuery('');
        setLocationFilter('');
        setRemoteFilter('all');
        setExperienceFilter('all');
        setCertificationFilter([]);
        setSortBy('relevance');
    };

    // Toggle certification selection in filter
    const toggleCertificationFilter = (certId: number) => {
        if (certificationFilter.includes(certId)) {
            setCertificationFilter(certificationFilter.filter(id => id !== certId));
        } else {
            setCertificationFilter([...certificationFilter, certId]);
        }
    };

    // Format date for display
    const formatDate = (dateString: string): string => {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now.getTime() - date.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) {
            return 'Yesterday';
        } else if (diffDays < 7) {
            return `${diffDays} days ago`;
        } else if (diffDays < 30) {
            const weeks = Math.floor(diffDays / 7);
            return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;
        } else {
            return date.toLocaleDateString(undefined, {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }
    };

    // Format salary for display
    const formatSalary = (salaryRange: { min: number, max: number, currency: string }): string => {
        if (salaryRange.currency === 'USD/hour') {
            return `$${salaryRange.min}-$${salaryRange.max}/hr`;
        }

        // Format as yearly salary
        const formatNumber = (num: number): string => {
            if (num >= 1000000) {
                return `$${(num / 1000000).toFixed(1)}M`;
            } else if (num >= 1000) {
                return `$${(num / 1000).toFixed(0)}K`;
            }
            return `$${num}`;
        };

        return `${formatNumber(salaryRange.min)} - ${formatNumber(salaryRange.max)}`;
    };

    // Get match score color class
    const getMatchScoreColor = (score: number): string => {
        if (score >= 90) return 'excellent-match';
        if (score >= 75) return 'good-match';
        if (score >= 60) return 'fair-match';
        return 'poor-match';
    };

    // Calculate how many of the user's certifications match the job requirements
    const calculateCertificationMatch = (job: JobListing): { matched: number, total: number } => {
        if (!userProfile) return { matched: 0, total: 0 };

        const requiredCerts = job.requirements.required_certifications;
        const matchedCerts = requiredCerts.filter(certId => userProfile.certifications.includes(certId));

        return {
            matched: matchedCerts.length,
            total: requiredCerts.length
        };
    };

    // Calculate how many of the user's skills match the job requirements
    const calculateSkillMatch = (job: JobListing): { matched: number, total: number } => {
        if (!userProfile) return { matched: 0, total: 0 };

        const requiredSkills = job.requirements.required_skills;
        const matchedSkills = requiredSkills.filter(skillId => userProfile.skills.includes(skillId));

        return {
            matched: matchedSkills.length,
            total: requiredSkills.length
        };
    };

    return (
        <div className="job-search-container">
            <h1>IT Job Search</h1>

            <div className="search-tools">
                <div className="search-bar">
                    <input
                        type="text"
                        placeholder="Search job title, company, or keywords..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    <button className="search-button">
                        <span role="img" aria-label="search">🔍</span>
                    </button>
                </div>

                <div className="filters">
                    <div className="filter-row">
                        <div className="filter-group">
                            <label htmlFor="location-filter">Location:</label>
                            <input
                                id="location-filter"
                                type="text"
                                placeholder="City, state, or country"
                                value={locationFilter}
                                onChange={(e) => setLocationFilter(e.target.value)}
                            />
                        </div>

                        <div className="filter-group">
                            <label htmlFor="remote-filter">Work Type:</label>
                            <select
                                id="remote-filter"
                                value={remoteFilter}
                                onChange={(e) => setRemoteFilter(e.target.value)}
                            >
                                <option value="all">All</option>
                                <option value="remote">Remote</option>
                                <option value="on-site">On-site</option>
                            </select>
                        </div>

                        <div className="filter-group">
                            <label htmlFor="experience-filter">Experience:</label>
                            <select
                                id="experience-filter"
                                value={experienceFilter}
                                onChange={(e) => setExperienceFilter(e.target.value)}
                            >
                                <option value="all">Any</option>
                                <option value="1">1 Year or Less</option>
                                <option value="2">2 Years or Less</option>
                                <option value="3">3 Years or Less</option>
                                <option value="5">5 Years or Less</option>
                            </select>
                        </div>

                        <div className="filter-group">
                            <label htmlFor="sort-by">Sort By:</label>
                            <select
                                id="sort-by"
                                value={sortBy}
                                onChange={(e) => setSortBy(e.target.value)}
                            >
                                <option value="relevance">Best Match</option>
                                <option value="date">Most Recent</option>
                                <option value="salary">Highest Salary</option>
                            </select>
                        </div>
                    </div>

                    <div className="filter-row">
                        <div className="certification-filter">
                            <button
                                className="certification-filter-toggle"
                                onClick={() => setShowCertificationFilter(!showCertificationFilter)}
                            >
                                Certifications {certificationFilter.length > 0 && `(${certificationFilter.length})`} {showCertificationFilter ? '▲' : '▼'}
                            </button>

                            {showCertificationFilter && (
                                <div className="certification-checkboxes">
                                    {Object.values(certifications).map(cert => (
                                        <div key={cert.id} className="certification-checkbox">
                                            <input
                                                type="checkbox"
                                                id={`cert-${cert.id}`}
                                                checked={certificationFilter.includes(cert.id)}
                                                onChange={() => toggleCertificationFilter(cert.id)}
                                            />
                                            <label htmlFor={`cert-${cert.id}`}>{cert.name}</label>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>

                        <div className="filter-actions">
                            <button
                                className="reset-filters-button"
                                onClick={resetFilters}
                            >
                                Reset Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div className="job-search-content">
                {loading ? (
                    <div className="loading-container">
                        <div className="loading-spinner"></div>
                        <p>Loading job listings...</p>
                    </div>
                ) : error ? (
                    <div className="error-container">
                        <p className="error-message">{error}</p>
                        <button onClick={() => window.location.reload()}>Try Again</button>
                    </div>
                ) : (
                    <div className="job-search-results">
                        <div className="results-header">
                            <h2>{filteredJobs.length} {filteredJobs.length === 1 ? 'Job' : 'Jobs'} Found</h2>
                        </div>

                        {filteredJobs.length === 0 ? (
                            <div className="no-results">
                                <h3>No jobs match your search criteria</h3>
                                <p>Try adjusting your filters or search query to see more results.</p>
                                <button
                                    className="reset-filters-button"
                                    onClick={resetFilters}
                                >
                                    Reset All Filters
                                </button>
                            </div>
                        ) : (
                            <div className="job-listings">
                                {filteredJobs.map(job => {
                                    const certMatch = calculateCertificationMatch(job);
                                    const skillMatch = calculateSkillMatch(job);

                                    return (
                                        <div
                                            key={job.id}
                                            className={`job-card ${selectedJob === job.id ? 'expanded' : ''}`}
                                            onClick={() => setSelectedJob(selectedJob === job.id ? null : job.id)}
                                        >
                                            <div className="job-card-header">
                                                <div className="job-title-company">
                                                    <h3>{job.title}</h3>
                                                    <div className="company-name">{job.company}</div>
                                                </div>

                                                {job.match_score !== undefined && (
                                                    <div className={`match-score ${getMatchScoreColor(job.match_score)}`}>
                                                        {job.match_score}% Match
                                                    </div>
                                                )}
                                            </div>

                                            <div className="job-card-details">
                                                <div className="job-detail-item">
                                                    <span className="detail-icon">📍</span>
                                                    <span>{job.location} {job.remote && '(Remote)'}</span>
                                                </div>

                                                <div className="job-detail-item">
                                                    <span className="detail-icon">💰</span>
                                                    <span>{formatSalary(job.salary_range)}</span>
                                                </div>

                                                <div className="job-detail-item">
                                                    <span className="detail-icon">🕒</span>
                                                    <span>{job.job_type.replace('-', ' ')}</span>
                                                </div>

                                                <div className="job-detail-item">
                                                    <span className="detail-icon">📅</span>
                                                    <span>Posted {formatDate(job.posted_date)}</span>
                                                </div>
                                            </div>

                                            {selectedJob === job.id && (
                                                <div className="job-card-expanded">
                                                    <div className="job-description">
                                                        <h4>Job Description</h4>
                                                        <p>{job.description}</p>
                                                    </div>

                                                    <div className="job-qualifications">
                                                        <div className="qualifications-section">
                                                            <h4>Required Certifications</h4>
                                                            {job.requirements.required_certifications.length > 0 ? (
                                                                <ul className="certification-list">
                                                                    {job.requirements.required_certifications.map(certId => {
                                                                        const cert = certifications[certId];
                                                                        const hasMatch = userProfile?.certifications.includes(certId);

                                                                        return (
                                                                            <li key={certId} className={hasMatch ? 'matched' : ''}>
                                                                                {cert?.name || `Certification #${certId}`}
                                                                                {hasMatch && <span className="match-indicator">✓</span>}
                                                                            </li>
                                                                        );
                                                                    })}
                                                                </ul>
                                                            ) : (
                                                                <p className="no-items">No specific certifications required</p>
                                                            )}
                                                        </div>

                                                        <div className="qualifications-section">
                                                            <h4>Preferred Certifications</h4>
                                                            {job.requirements.preferred_certifications.length > 0 ? (
                                                                <ul className="certification-list">
                                                                    {job.requirements.preferred_certifications.map(certId => {
                                                                        const cert = certifications[certId];
                                                                        const hasMatch = userProfile?.certifications.includes(certId);

                                                                        return (
                                                                            <li key={certId} className={hasMatch ? 'matched' : ''}>
                                                                                {cert?.name || `Certification #${certId}`}
                                                                                {hasMatch && <span className="match-indicator">✓</span>}
                                                                            </li>
                                                                        );
                                                                    })}
                                                                </ul>
                                                            ) : (
                                                                <p className="no-items">No preferred certifications listed</p>
                                                            )}
                                                        </div>

                                                        <div className="qualifications-section">
                                                            <h4>Required Skills</h4>
                                                            {job.requirements.required_skills.length > 0 ? (
                                                                <ul className="skills-list">
                                                                    {job.requirements.required_skills.map(skillId => {
                                                                        const skill = skills[skillId];
                                                                        const hasMatch = userProfile?.skills.includes(skillId);

                                                                        return (
                                                                            <li key={skillId} className={hasMatch ? 'matched' : ''}>
                                                                                {skill?.name || `Skill #${skillId}`}
                                                                                {hasMatch && <span className="match-indicator">✓</span>}
                                                                            </li>
                                                                        );
                                                                    })}
                                                                </ul>
                                                            ) : (
                                                                <p className="no-items">No specific skills required</p>
                                                            )}
                                                        </div>

                                                        <div className="qualifications-section">
                                                            <h4>Experience Required</h4>
                                                            <p className="experience-required">
                                                                {job.requirements.experience_years} {job.requirements.experience_years === 1 ? 'year' : 'years'}
                                                                {userProfile && userProfile.experience_years >= job.requirements.experience_years && (
                                                                    <span className="match-indicator">✓</span>
                                                                )}
                                                            </p>
                                                        </div>
                                                    </div>

                                                    <div className="match-summary">
                                                        <h4>Your Match Summary</h4>
                                                        <div className="match-metrics">
                                                            <div className="match-metric">
                                                                <div className="match-label">Certifications</div>
                                                                <div className="match-progress">
                                                                    <div
                                                                        className="match-progress-fill"
                                                                        style={{ width: `${certMatch.total === 0 ? 100 : (certMatch.matched / certMatch.total) * 100}%` }}
                                                                    ></div>
                                                                </div>
                                                                <div className="match-value">{certMatch.matched}/{certMatch.total}</div>
                                                            </div>

                                                            <div className="match-metric">
                                                                <div className="match-label">Skills</div>
                                                                <div className="match-progress">
                                                                    <div
                                                                        className="match-progress-fill"
                                                                        style={{ width: `${skillMatch.total === 0 ? 100 : (skillMatch.matched / skillMatch.total) * 100}%` }}
                                                                    ></div>
                                                                </div>
                                                                <div className="match-value">{skillMatch.matched}/{skillMatch.total}</div>
                                                            </div>

                                                            <div className="match-metric">
                                                                <div className="match-label">Experience</div>
                                                                <div className="match-progress">
                                                                    <div
                                                                        className="match-progress-fill"
                                                                        style={{
                                                                            width: `${userProfile ?
                                                                                Math.min(100, (userProfile.experience_years / job.requirements.experience_years) * 100) : 0}%`
                                                                        }}
                                                                    ></div>
                                                                </div>
                                                                <div className="match-value">
                                                                    {userProfile ? `${userProfile.experience_years}/${job.requirements.experience_years}` : '0/0'}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div className="job-actions">
                                                        <a
                                                            href={job.application_url}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                            className="apply-button"
                                                        >
                                                            Apply Now
                                                        </a>
                                                        <button className="save-job-button">
                                                            Save Job
                                                        </button>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    );
                                })}
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};

export default JobSearch; 