import React, { useEffect, useState } from 'react';
import './JobSearch.css';

interface Job {
    id: number;
    title: string;
    company: string;
    location: string;
    type: string;
    salary: string;
    description: string;
    requirements: string[];
    certifications: string[];
    postedDate: string;
    url: string;
}

const mockJobs: Job[] = [
    {
        id: 1,
        title: "Junior Security Analyst",
        company: "SecureTech Solutions",
        location: "Remote",
        type: "Full-time",
        salary: "$65,000 - $80,000",
        description: "We are seeking a Junior Security Analyst to join our Security Operations Center team. The ideal candidate will monitor security alerts, analyze potential threats, and assist in incident response activities.",
        requirements: [
            "Bachelor's degree in Cybersecurity, Computer Science, or related field",
            "0-2 years of experience in IT security",
            "Knowledge of security tools and technologies",
            "Strong analytical and problem-solving skills"
        ],
        certifications: [
            "Security+",
            "Network+"
        ],
        postedDate: "2023-04-15",
        url: "https://example.com/jobs/1"
    },
    {
        id: 2,
        title: "Cybersecurity Engineer",
        company: "Global Financial Corp",
        location: "New York, NY",
        type: "Full-time",
        salary: "$95,000 - $120,000",
        description: "Join our cybersecurity team to design, implement, and maintain security solutions that protect our critical financial systems and data. You will work on security architecture, vulnerability assessments, and security automation.",
        requirements: [
            "Bachelor's degree in Cybersecurity, Information Technology, or related field",
            "3-5 years of experience in cybersecurity",
            "Experience with security tools, firewalls, IDS/IPS",
            "Knowledge of cloud security (AWS/Azure)",
            "Scripting/programming skills (Python, PowerShell)"
        ],
        certifications: [
            "CISSP",
            "Security+",
            "AWS Security Specialty (preferred)"
        ],
        postedDate: "2023-04-10",
        url: "https://example.com/jobs/2"
    },
    {
        id: 3,
        title: "Penetration Tester",
        company: "CyberGuard Consulting",
        location: "Chicago, IL (Hybrid)",
        type: "Full-time",
        salary: "$100,000 - $130,000",
        description: "We're looking for an experienced Penetration Tester to identify vulnerabilities in our clients' systems through controlled cyberattacks. You will conduct security assessments, write detailed reports, and provide remediation recommendations.",
        requirements: [
            "3+ years of experience in penetration testing",
            "Strong knowledge of OWASP Top 10 vulnerabilities",
            "Experience with penetration testing tools (Metasploit, Burp Suite, etc.)",
            "Excellent documentation and communication skills"
        ],
        certifications: [
            "OSCP",
            "CEH",
            "PenTest+"
        ],
        postedDate: "2023-04-05",
        url: "https://example.com/jobs/3"
    },
    {
        id: 4,
        title: "Security Compliance Analyst",
        company: "HealthTech Innovations",
        location: "Boston, MA",
        type: "Full-time",
        salary: "$85,000 - $105,000",
        description: "Join our team to ensure compliance with security regulations and standards including HIPAA, HITRUST, and SOC 2. You will conduct security assessments, manage the compliance program, and work with teams to maintain security posture.",
        requirements: [
            "Bachelor's degree in Information Security, Compliance, or related field",
            "2+ years of experience in security compliance",
            "Knowledge of regulations such as HIPAA, HITRUST, SOC 2",
            "Experience with security frameworks (NIST, ISO 27001)"
        ],
        certifications: [
            "CISA",
            "CISSP",
            "CISM"
        ],
        postedDate: "2023-03-28",
        url: "https://example.com/jobs/4"
    }
];

const JobSearch: React.FC = () => {
    const [jobs, setJobs] = useState<Job[]>([]);
    const [filteredJobs, setFilteredJobs] = useState<Job[]>([]);
    const [selectedJob, setSelectedJob] = useState<Job | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [filterCertifications, setFilterCertifications] = useState<string[]>([]);

    // List of common security certifications for filtering
    const commonCertifications = [
        "Security+", "CISSP", "CEH", "OSCP", "CySA+", "CISM", "Network+", "PenTest+"
    ];

    useEffect(() => {
        // Simulate API call to fetch jobs
        const fetchJobs = async () => {
            try {
                // In a real app, this would be an API call
                // const response = await fetch('/api/v1/jobs');
                // const data = await response.json();

                // Using mock data for now
                setTimeout(() => {
                    setJobs(mockJobs);
                    setFilteredJobs(mockJobs);
                    setIsLoading(false);
                }, 800);
            } catch (err) {
                console.error("Failed to load jobs:", err);
                setIsLoading(false);
            }
        };

        fetchJobs();
    }, []);

    useEffect(() => {
        // Filter jobs based on search term and certifications
        let result = jobs;

        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            result = result.filter(job =>
                job.title.toLowerCase().includes(term) ||
                job.company.toLowerCase().includes(term) ||
                job.description.toLowerCase().includes(term)
            );
        }

        if (filterCertifications.length > 0) {
            result = result.filter(job =>
                filterCertifications.some(cert =>
                    job.certifications.includes(cert)
                )
            );
        }

        setFilteredJobs(result);
    }, [searchTerm, filterCertifications, jobs]);

    const handleJobSelect = (job: Job) => {
        setSelectedJob(job);
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
    };

    const handleCertificationFilter = (cert: string) => {
        if (filterCertifications.includes(cert)) {
            setFilterCertifications(filterCertifications.filter(c => c !== cert));
        } else {
            setFilterCertifications([...filterCertifications, cert]);
        }
    };

    if (isLoading) {
        return <div className="jobs-loading">Loading job listings...</div>;
    }

    return (
        <div className="job-search-container">
            <h2>Cybersecurity Job Search</h2>

            <div className="search-filters">
                <div className="search-bar">
                    <input
                        type="text"
                        placeholder="Search job titles, companies, or keywords..."
                        value={searchTerm}
                        onChange={handleSearchChange}
                    />
                </div>

                <div className="certification-filters">
                    <h4>Filter by Certification</h4>
                    <div className="certification-buttons">
                        {commonCertifications.map(cert => (
                            <button
                                key={cert}
                                className={`cert-filter-btn ${filterCertifications.includes(cert) ? 'active' : ''}`}
                                onClick={() => handleCertificationFilter(cert)}
                            >
                                {cert}
                            </button>
                        ))}
                    </div>
                </div>
            </div>

            <div className="job-search-content">
                <div className="job-listings">
                    <h3>Job Listings ({filteredJobs.length})</h3>
                    {filteredJobs.length === 0 ? (
                        <div className="no-jobs-found">
                            <p>No jobs match your search criteria. Try adjusting your filters.</p>
                        </div>
                    ) : (
                        <ul>
                            {filteredJobs.map(job => (
                                <li
                                    key={job.id}
                                    className={selectedJob?.id === job.id ? 'selected' : ''}
                                    onClick={() => handleJobSelect(job)}
                                >
                                    <h4>{job.title}</h4>
                                    <p className="job-company">{job.company}</p>
                                    <div className="job-meta">
                                        <span className="job-location">{job.location}</span>
                                        <span className="job-salary">{job.salary}</span>
                                    </div>
                                    <div className="job-certifications">
                                        {job.certifications.map((cert, index) => (
                                            <span key={index} className="job-cert-tag">{cert}</span>
                                        ))}
                                    </div>
                                </li>
                            ))}
                        </ul>
                    )}
                </div>

                <div className="job-details">
                    {selectedJob ? (
                        <>
                            <div className="job-header">
                                <h3>{selectedJob.title}</h3>
                                <div className="job-subheader">
                                    <div className="job-company-info">
                                        <h4>{selectedJob.company}</h4>
                                        <p>{selectedJob.location} • {selectedJob.type}</p>
                                    </div>
                                    <div className="job-salary-info">
                                        <p className="salary">{selectedJob.salary}</p>
                                        <p className="posted-date">Posted: {new Date(selectedJob.postedDate).toLocaleDateString()}</p>
                                    </div>
                                </div>
                            </div>

                            <div className="job-description">
                                <h4>Description</h4>
                                <p>{selectedJob.description}</p>
                            </div>

                            <div className="job-requirements">
                                <h4>Requirements</h4>
                                <ul>
                                    {selectedJob.requirements.map((req, index) => (
                                        <li key={index}>{req}</li>
                                    ))}
                                </ul>
                            </div>

                            <div className="job-certifications-section">
                                <h4>Required/Preferred Certifications</h4>
                                <div className="cert-tags">
                                    {selectedJob.certifications.map((cert, index) => (
                                        <span key={index} className="cert-tag">{cert}</span>
                                    ))}
                                </div>
                            </div>

                            <div className="job-apply">
                                <a href={selectedJob.url} target="_blank" rel="noopener noreferrer" className="apply-button">
                                    Apply for this position
                                </a>
                            </div>
                        </>
                    ) : (
                        <div className="select-job-prompt">
                            <p>Select a job from the list to view details</p>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default JobSearch; 