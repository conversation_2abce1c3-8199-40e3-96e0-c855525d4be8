.job-search-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: '<PERSON>o', sans-serif;
}

.job-search-container h1 {
  text-align: center;
  margin-bottom: 2rem;
  color: #333;
  font-size: 2rem;
}

/* Search tools section */
.search-tools {
  margin-bottom: 2rem;
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-bar {
  display: flex;
  margin-bottom: 1.5rem;
}

.search-bar input {
  flex-grow: 1;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  font-size: 1rem;
}

.search-bar input:focus {
  outline: none;
  border-color: #4a6cf7;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2);
}

.search-button {
  background-color: #4a6cf7;
  color: white;
  border: none;
  padding: 0 1.5rem;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
}

.search-button:hover {
  background-color: #3a5ce5;
}

/* Filters */
.filters {
  border-top: 1px solid #eee;
  padding-top: 1.5rem;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: center;
  justify-content: space-between;
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 170px;
  flex: 1;
}

.filter-group label {
  font-size: 0.85rem;
  font-weight: 500;
  color: #555;
  margin-bottom: 0.5rem;
}

.filter-group input,
.filter-group select {
  padding: 0.6rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.filter-group input:focus,
.filter-group select:focus {
  outline: none;
  border-color: #4a6cf7;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2);
}

/* Certification filter */
.certification-filter {
  position: relative;
  flex: 3;
}

.certification-filter-toggle {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.6rem 1rem;
  width: 100%;
  text-align: left;
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.certification-filter-toggle:hover {
  background-color: #f5f5f5;
}

.certification-checkboxes {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #ddd;
  border-top: none;
  border-radius: 0 0 4px 4px;
  z-index: 10;
  padding: 0.5rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.certification-checkbox {
  padding: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.certification-checkbox:hover {
  background-color: #f5f7ff;
}

.certification-checkbox input {
  margin: 0;
}

.certification-checkbox label {
  font-size: 0.9rem;
  color: #555;
  cursor: pointer;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  flex: 1;
}

.reset-filters-button {
  background-color: transparent;
  color: #4a6cf7;
  border: 1px solid #4a6cf7;
  border-radius: 4px;
  padding: 0.6rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.reset-filters-button:hover {
  background-color: #4a6cf7;
  color: white;
}

/* Loading state */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #4a6cf7;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Error state */
.error-container {
  text-align: center;
  padding: 2rem;
  background-color: #fff8f8;
  border: 1px solid #ffefef;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.error-message {
  color: #e53935;
  margin-bottom: 1rem;
}

.error-container button {
  padding: 0.5rem 1rem;
  background-color: #e53935;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.error-container button:hover {
  background-color: #c62828;
}

/* Job search results */
.job-search-content {
  min-height: 400px;
}

.results-header {
  margin-bottom: 1.5rem;
}

.results-header h2 {
  color: #333;
  font-size: 1.2rem;
  font-weight: 500;
}

.no-results {
  text-align: center;
  padding: 3rem;
  background-color: #f9fafb;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.no-results h3 {
  color: #333;
  margin-bottom: 0.5rem;
}

.no-results p {
  color: #666;
  margin-bottom: 1.5rem;
}

/* Job listings */
.job-listings {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.job-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
  cursor: pointer;
  transition: box-shadow 0.2s, transform 0.2s;
}

.job-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.job-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.job-title-company h3 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.company-name {
  color: #666;
  font-size: 0.95rem;
  margin-top: 0.3rem;
}

.match-score {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
}

.excellent-match {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.good-match {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.fair-match {
  background-color: #fff3e0;
  color: #e65100;
}

.poor-match {
  background-color: #ffebee;
  color: #b71c1c;
}

.job-card-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.job-detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #555;
  font-size: 0.9rem;
}

.detail-icon {
  font-size: 1rem;
}

/* Expanded job card */
.job-card-expanded {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
}

.job-description h4,
.job-qualifications h4,
.match-summary h4 {
  color: #333;
  font-size: 1.1rem;
  margin-top: 0;
  margin-bottom: 1rem;
}

.job-description p {
  color: #555;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.job-qualifications {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.qualifications-section {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 1rem;
}

.qualifications-section h4 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 1rem;
  color: #333;
}

.certification-list,
.skills-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.certification-list li,
.skills-list li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
  color: #555;
  font-size: 0.9rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.certification-list li:last-child,
.skills-list li:last-child {
  border-bottom: none;
}

.certification-list li.matched,
.skills-list li.matched {
  color: #4a6cf7;
  font-weight: 500;
}

.match-indicator {
  color: #4caf50;
  font-weight: bold;
}

.no-items {
  color: #999;
  font-size: 0.9rem;
  font-style: italic;
}

.experience-required {
  color: #555;
  font-size: 0.9rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Match summary */
.match-summary {
  background-color: #f5f7ff;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.match-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.match-metric {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.match-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #555;
}

.match-progress {
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.match-progress-fill {
  height: 100%;
  background-color: #4a6cf7;
  border-radius: 4px;
}

.match-value {
  font-size: 0.85rem;
  color: #666;
  text-align: right;
}

/* Job actions */
.job-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.apply-button {
  padding: 0.75rem 1.5rem;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
  flex: 1;
}

.apply-button:hover {
  background-color: #3a5ce5;
}

.save-job-button {
  padding: 0.75rem 1.5rem;
  background-color: transparent;
  color: #4a6cf7;
  border: 1px solid #4a6cf7;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
}

.save-job-button:hover {
  background-color: #f5f7ff;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .job-search-container {
    padding: 1rem;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group,
  .certification-filter,
  .filter-actions {
    width: 100%;
    min-width: auto;
  }

  .job-card-header {
    flex-direction: column;
  }

  .match-score {
    margin-top: 0.5rem;
    align-self: flex-start;
  }

  .job-card-details {
    flex-direction: column;
    gap: 0.5rem;
  }

  .job-qualifications {
    grid-template-columns: 1fr;
  }

  .job-actions {
    flex-direction: column;
  }
} 