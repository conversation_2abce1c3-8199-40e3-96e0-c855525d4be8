.job-search-container {
  padding: 20px;
}

.jobs-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 18px;
  color: #6c757d;
}

.search-filters {
  margin-bottom: 25px;
}

.search-bar {
  margin-bottom: 15px;
}

.search-bar input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 16px;
}

.certification-filters h4 {
  margin-bottom: 10px;
  color: #495057;
}

.certification-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.cert-filter-btn {
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cert-filter-btn:hover {
  background-color: #e9ecef;
}

.cert-filter-btn.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.job-search-content {
  display: flex;
  gap: 30px;
}

.job-listings {
  flex: 1;
}

.job-listings ul {
  list-style-type: none;
  padding: 0;
}

.job-listings li {
  padding: 15px;
  margin-bottom: 15px;
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.job-listings li:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.job-listings li.selected {
  border-left: 4px solid #007bff;
  background-color: #f8f9fa;
}

.job-listings h4 {
  margin: 0 0 8px 0;
  color: #343a40;
}

.job-company {
  color: #495057;
  margin: 0 0 8px 0;
}

.job-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 14px;
}

.job-location {
  color: #6c757d;
}

.job-salary {
  color: #28a745;
  font-weight: bold;
}

.job-certifications {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.job-cert-tag {
  display: inline-block;
  padding: 3px 8px;
  background-color: #e7f5ff;
  color: #007bff;
  border-radius: 4px;
  font-size: 12px;
}

.job-details {
  flex: 2;
  background: #f8f9fa;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.select-job-prompt {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #6c757d;
  font-style: italic;
}

.job-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #dee2e6;
}

.job-subheader {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.job-company-info h4 {
  margin: 0 0 5px 0;
  color: #495057;
}

.job-company-info p {
  color: #6c757d;
  margin: 0;
}

.job-salary-info {
  text-align: right;
}

.job-salary-info .salary {
  color: #28a745;
  font-weight: bold;
  font-size: 18px;
  margin: 0 0 5px 0;
}

.job-salary-info .posted-date {
  color: #6c757d;
  font-size: 14px;
  margin: 0;
}

.job-description, .job-requirements, .job-certifications-section {
  margin-bottom: 25px;
}

.job-description h4, .job-requirements h4, .job-certifications-section h4 {
  margin-bottom: 10px;
  color: #495057;
}

.job-requirements ul {
  margin: 0;
  padding-left: 20px;
}

.job-requirements li {
  margin-bottom: 8px;
}

.cert-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.cert-tag {
  display: inline-block;
  padding: 6px 12px;
  background-color: #e3f2fd;
  color: #0d47a1;
  border-radius: 20px;
  font-size: 14px;
}

.job-apply {
  margin-top: 30px;
  text-align: center;
}

.apply-button {
  display: inline-block;
  padding: 12px 24px;
  background-color: #007bff;
  color: white;
  border-radius: 4px;
  text-decoration: none;
  font-weight: bold;
  transition: background-color 0.2s;
}

.apply-button:hover {
  background-color: #0069d9;
}

.no-jobs-found {
  padding: 30px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
} 