import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import axios from 'axios';
import JobSearch from './index';

// Mock axios
jest.mock('axios');
const mockAxios = axios as jest.Mocked<typeof axios>;

describe('JobSearch Component', () => {
    // Mock data
    const mockCertifications = [
        { id: 1, name: 'CompTIA A+', vendor: 'CompTIA', level: 'beginner' },
        { id: 2, name: 'CompTIA Network+', vendor: 'CompTIA', level: 'beginner' }
    ];

    const mockSkills = [
        { id: 1, name: 'Linux', category: 'Operating Systems' },
        { id: 2, name: 'Windows Server', category: 'Operating Systems' }
    ];

    const mockJobs = [
        {
            id: 1,
            title: 'Network Administrator',
            company: 'TechCorp Solutions',
            location: 'New York, NY',
            remote: false,
            salary_range: {
                min: 65000,
                max: 85000,
                currency: 'USD'
            },
            description: 'We are seeking a skilled Network Administrator to join our IT team.',
            requirements: {
                required_certifications: [2],
                preferred_certifications: [3],
                required_skills: [1, 2],
                preferred_skills: [8, 1],
                experience_years: 2
            },
            posted_date: '2023-05-15',
            application_url: 'https://example.com/jobs/network-admin',
            job_type: 'full-time',
            match_score: 85
        },
        {
            id: 2,
            title: 'IT Support Specialist',
            company: 'Global Solutions Inc.',
            location: 'Chicago, IL',
            remote: false,
            salary_range: {
                min: 45000,
                max: 60000,
                currency: 'USD'
            },
            description: 'Join our IT Support team to provide technical support to our employees.',
            requirements: {
                required_certifications: [1],
                preferred_certifications: [2],
                required_skills: [2],
                preferred_skills: [1],
                experience_years: 1
            },
            posted_date: '2023-05-01',
            application_url: 'https://example.com/jobs/it-support',
            job_type: 'full-time',
            match_score: 70
        }
    ];

    const mockUserProfile = {
        certifications: [1, 2],
        skills: [1, 2],
        experience_years: 3
    };

    beforeEach(() => {
        // Mock API calls to fail so we use our mock data
        mockAxios.get.mockImplementation(() => {
            return Promise.reject(new Error('API not available'));
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('renders loading state initially', () => {
        render(<JobSearch />);
        expect(screen.getByText(/Loading job listings/i)).toBeInTheDocument();
    });

    test('renders job listings after loading', async () => {
        render(<JobSearch />);

        // Wait for the loading to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading job listings/i)).not.toBeInTheDocument();
        });

        // Check if job titles are rendered
        expect(screen.getByText('Network Administrator')).toBeInTheDocument();
        expect(screen.getByText('IT Support Specialist')).toBeInTheDocument();

        // Check if job details are rendered
        expect(screen.getByText('TechCorp Solutions')).toBeInTheDocument();
        expect(screen.getByText('Global Solutions Inc.')).toBeInTheDocument();
        expect(screen.getByText('New York, NY')).toBeInTheDocument();
        expect(screen.getByText('Chicago, IL')).toBeInTheDocument();
    });

    test('search filters job listings correctly', async () => {
        render(<JobSearch />);

        // Wait for the loading to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading job listings/i)).not.toBeInTheDocument();
        });

        // Initially, both jobs should be visible
        expect(screen.getByText('Network Administrator')).toBeInTheDocument();
        expect(screen.getByText('IT Support Specialist')).toBeInTheDocument();

        // Enter search query
        const searchInput = screen.getByPlaceholderText(/Search job title, company, or keywords/i);
        fireEvent.change(searchInput, { target: { value: 'network' } });

        // Only the Network Administrator job should be visible
        expect(screen.getByText('Network Administrator')).toBeInTheDocument();
        expect(screen.queryByText('IT Support Specialist')).not.toBeInTheDocument();

        // Clear search
        fireEvent.change(searchInput, { target: { value: '' } });

        // Both jobs should be visible again
        expect(screen.getByText('Network Administrator')).toBeInTheDocument();
        expect(screen.getByText('IT Support Specialist')).toBeInTheDocument();
    });

    test('location filter works correctly', async () => {
        render(<JobSearch />);

        // Wait for the loading to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading job listings/i)).not.toBeInTheDocument();
        });

        // Enter location filter
        const locationInput = screen.getByLabelText(/Location:/i);
        fireEvent.change(locationInput, { target: { value: 'Chicago' } });

        // Only Chicago jobs should be visible
        expect(screen.queryByText('Network Administrator')).not.toBeInTheDocument();
        expect(screen.getByText('IT Support Specialist')).toBeInTheDocument();
    });

    test('experience filter works correctly', async () => {
        render(<JobSearch />);

        // Wait for the loading to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading job listings/i)).not.toBeInTheDocument();
        });

        // Set experience filter to 1 year or less
        const experienceSelect = screen.getByLabelText(/Experience:/i);
        fireEvent.change(experienceSelect, { target: { value: '1' } });

        // Only jobs requiring 1 year or less should be visible
        expect(screen.queryByText('Network Administrator')).not.toBeInTheDocument();
        expect(screen.getByText('IT Support Specialist')).toBeInTheDocument();

        // Change to 2 years or less
        fireEvent.change(experienceSelect, { target: { value: '2' } });

        // Now both jobs should be visible
        expect(screen.getByText('Network Administrator')).toBeInTheDocument();
        expect(screen.getByText('IT Support Specialist')).toBeInTheDocument();
    });

    test('reset filters button works correctly', async () => {
        render(<JobSearch />);

        // Wait for the loading to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading job listings/i)).not.toBeInTheDocument();
        });

        // Apply filters
        fireEvent.change(screen.getByPlaceholderText(/Search job title, company, or keywords/i), { target: { value: 'network' } });

        // Only Network Administrator should be visible
        expect(screen.getByText('Network Administrator')).toBeInTheDocument();
        expect(screen.queryByText('IT Support Specialist')).not.toBeInTheDocument();

        // Reset filters
        fireEvent.click(screen.getByText('Reset Filters'));

        // Both jobs should be visible again
        expect(screen.getByText('Network Administrator')).toBeInTheDocument();
        expect(screen.getByText('IT Support Specialist')).toBeInTheDocument();
    });

    test('expands job card when clicked', async () => {
        render(<JobSearch />);

        // Wait for the loading to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading job listings/i)).not.toBeInTheDocument();
        });

        // Check that job description is not visible initially
        expect(screen.queryByText(/We are seeking a skilled Network Administrator to join our IT team./i)).not.toBeInTheDocument();

        // Click on the job card
        fireEvent.click(screen.getByText('Network Administrator').closest('.job-card') as HTMLElement);

        // Now job description should be visible
        expect(screen.getByText(/We are seeking a skilled Network Administrator to join our IT team./i)).toBeInTheDocument();

        // Check if certification requirements are shown
        expect(screen.getByText('Required Certifications')).toBeInTheDocument();

        // Check if Apply Now button is visible
        expect(screen.getByText('Apply Now')).toBeInTheDocument();

        // Click again to collapse
        fireEvent.click(screen.getByText('Network Administrator').closest('.job-card') as HTMLElement);

        // Job description should be hidden again
        expect(screen.queryByText(/We are seeking a skilled Network Administrator to join our IT team./i)).not.toBeInTheDocument();
    });

    test('shows certification filter dropdown', async () => {
        render(<JobSearch />);

        // Wait for the loading to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading job listings/i)).not.toBeInTheDocument();
        });

        // Certification checkboxes should not be visible initially
        expect(screen.queryByLabelText('CompTIA A+')).not.toBeInTheDocument();

        // Click the certification filter button
        fireEvent.click(screen.getByText(/Certifications/i));

        // Now certification checkboxes should be visible
        expect(screen.getByLabelText('CompTIA A+')).toBeInTheDocument();
        expect(screen.getByLabelText('CompTIA Network+')).toBeInTheDocument();

        // Select a certification
        fireEvent.click(screen.getByLabelText('CompTIA A+'));

        // Only jobs requiring CompTIA A+ should be visible
        expect(screen.queryByText('Network Administrator')).not.toBeInTheDocument();
        expect(screen.getByText('IT Support Specialist')).toBeInTheDocument();
    });

    test('handles no results state', async () => {
        render(<JobSearch />);

        // Wait for the loading to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading job listings/i)).not.toBeInTheDocument();
        });

        // Apply a filter that will return no results
        fireEvent.change(screen.getByPlaceholderText(/Search job title, company, or keywords/i), { target: { value: 'nonexistent job' } });

        // Should show no results message
        expect(screen.getByText(/No jobs match your search criteria/i)).toBeInTheDocument();

        // Reset All Filters button should be visible
        expect(screen.getByText('Reset All Filters')).toBeInTheDocument();

        // Click Reset All Filters
        fireEvent.click(screen.getByText('Reset All Filters'));

        // Both jobs should be visible again
        expect(screen.getByText('Network Administrator')).toBeInTheDocument();
        expect(screen.getByText('IT Support Specialist')).toBeInTheDocument();
    });

    test('formats salary correctly', async () => {
        render(<JobSearch />);

        // Wait for the loading to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading job listings/i)).not.toBeInTheDocument();
        });

        // Check if salaries are formatted correctly
        expect(screen.getByText('$65K - $85K')).toBeInTheDocument();
        expect(screen.getByText('$45K - $60K')).toBeInTheDocument();
    });

    test('sorting works correctly', async () => {
        render(<JobSearch />);

        // Wait for the loading to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading job listings/i)).not.toBeInTheDocument();
        });

        // Initially sorted by relevance (match score), so Network Administrator (85%) should be first
        const jobListings = screen.getAllByText(/Network Administrator|IT Support Specialist/);
        expect(jobListings[0].textContent).toContain('Network Administrator');

        // Change sorting to salary
        const sortSelect = screen.getByLabelText(/Sort By:/i);
        fireEvent.change(sortSelect, { target: { value: 'salary' } });

        // Network Administrator should still be first (higher salary)
        const jobListingsAfterSort = screen.getAllByText(/Network Administrator|IT Support Specialist/);
        expect(jobListingsAfterSort[0].textContent).toContain('Network Administrator');

        // Change sorting to date
        fireEvent.change(sortSelect, { target: { value: 'date' } });

        // Network Administrator should still be first (more recent date)
        const jobListingsAfterDateSort = screen.getAllByText(/Network Administrator|IT Support Specialist/);
        expect(jobListingsAfterDateSort[0].textContent).toContain('Network Administrator');
    });
}); 