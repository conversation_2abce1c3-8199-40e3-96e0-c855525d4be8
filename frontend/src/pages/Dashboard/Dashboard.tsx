import React, { useEffect } from 'react';
import FeedbackButton from '../../components/shared/FeedbackButton';
import { FeedbackData } from '../../components/shared/FeedbackPopup';
import { TutorialStep } from '../../components/shared/OnboardingTutorial';
import useFeedback from '../../components/shared/useFeedback';
import useOnboarding from '../../components/shared/useOnboarding';
import api from '../../services/api';
import './Dashboard.css';

const Dashboard: React.FC = () => {
    // Setup the feedback functionality
    const handleSubmitFeedback = async (data: FeedbackData) => {
        await api.feedback.submit(data);
    };

    const { openFeedback, FeedbackPopupComponent } = useFeedback(handleSubmitFeedback);

    // Define tutorial steps for the dashboard
    const dashboardTutorialSteps: TutorialStep[] = [
        {
            id: 'welcome',
            title: 'Welcome to the Dashboard',
            content: 'This is your central hub for tracking certification progress and recommendations.',
            targetElement: '.dashboard-container',
            placement: 'top',
        },
        {
            id: 'certifications',
            title: 'Your Certifications',
            content: 'Track your earned and in-progress certifications here.',
            targetElement: '.dashboard-section:nth-child(1)',
            placement: 'right',
        },
        {
            id: 'study-progress',
            title: 'Study Progress',
            content: 'Monitor your study hours and progress toward your certification goals.',
            targetElement: '.dashboard-section:nth-child(2)',
            placement: 'right',
        },
        {
            id: 'recommended-paths',
            title: 'Recommended Paths',
            content: 'Discover certification paths aligned with your career goals.',
            targetElement: '.dashboard-section:nth-child(3)',
            placement: 'left',
        },
        {
            id: 'job-postings',
            title: 'Job Postings',
            content: 'Find security job opportunities that match your certification profile.',
            targetElement: '.dashboard-section:nth-child(4)',
            placement: 'left',
        },
        {
            id: 'feedback',
            title: 'Feedback',
            content: 'We value your input! Click this button anytime to share your thoughts.',
            targetElement: '.feedback-button',
            placement: 'top',
        },
    ];

    // Setup tutorial
    const {
        startTutorial,
        OnboardingTutorialComponent,
        isOnboardingComplete
    } = useOnboarding('dashboard-tutorial', dashboardTutorialSteps);

    // Check for first-time users
    useEffect(() => {
        if (!isOnboardingComplete) {
            // Auto-show tutorial for first-time users with a slight delay
            const timer = setTimeout(() => startTutorial(), 1000);
            return () => clearTimeout(timer);
        }
    }, [isOnboardingComplete, startTutorial]);

    return (
        <div className="dashboard-container">
            <h1>Dashboard</h1>
            <p>Welcome to the Security Certification Explorer. This dashboard provides an overview of your certifications, progress, and recommendations.</p>

            {/* Tutorial button */}
            <button
                className="tutorial-button"
                onClick={startTutorial}
                aria-label="Start tutorial"
            >
                <span className="tutorial-icon">?</span>
                <span>Tutorial</span>
            </button>

            {/* Dashboard content */}
            <div className="dashboard-sections">
                <div className="dashboard-section">
                    <h2>Your Certifications</h2>
                    <p>You haven't added any certifications yet. Start by exploring the certification catalog.</p>
                </div>

                <div className="dashboard-section">
                    <h2>Study Progress</h2>
                    <p>Track your study hours and progress toward certification goals.</p>
                </div>

                <div className="dashboard-section">
                    <h2>Recommended Paths</h2>
                    <p>Based on your interests, we recommend exploring the following certification paths.</p>
                </div>

                <div className="dashboard-section">
                    <h2>Recent Job Postings</h2>
                    <p>Security jobs that match your certification profile.</p>
                </div>
            </div>

            {/* Include the FeedbackButton and FeedbackPopup */}
            <FeedbackButton onClick={openFeedback} position="bottom-right" />
            {FeedbackPopupComponent}

            {/* Include the OnboardingTutorial */}
            {OnboardingTutorialComponent}
        </div>
    );
};

export default Dashboard; 