import React, { useEffect, useState } from 'react';
import axios from 'axios';
import './styles.css';

// Define interfaces based on our API models
interface CertificationSummary {
  id: number;
  name: string;
  organization: string;
  domain: string;
  level: string;
  cost: number | null;
  popularity: number | null;
}

interface OrganizationStat {
  name: string;
  certification_count: number;
  logo_url: string | null;
}

interface DomainStat {
  name: string;
  certification_count: number;
  description: string | null;
}

interface DashboardStats {
  total_certifications: number;
  total_organizations: number;
  total_domains: number;
  certifications_by_level: Record<string, number>;
  average_cost: number | null;
}

interface DashboardData {
  stats: DashboardStats;
  recent_certifications: CertificationSummary[];
  popular_certifications: CertificationSummary[];
  organizations: OrganizationStat[];
  domains: DomainStat[];
}

const Dashboard: React.FC = () => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    organization_id: '',
    domain: '',
    level: '',
    cost_min: '',
    cost_max: ''
  });
  
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        
        // Build query parameters
        const params = new URLSearchParams();
        if (filters.organization_id) params.append('organization_id', filters.organization_id);
        if (filters.domain) params.append('domain', filters.domain);
        if (filters.level) params.append('level', filters.level);
        if (filters.cost_min) params.append('cost_min', filters.cost_min);
        if (filters.cost_max) params.append('cost_max', filters.cost_max);
        
        const queryString = params.toString() ? `?${params.toString()}` : '';
        const response = await axios.get(`/api/dashboard${queryString}`);
        setData(response.data);
        setError(null);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try again later.');
        // For development, use mock data when API is not available
        setData(getMockData());
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [filters]);

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  const getMockData = (): DashboardData => {
    return {
      stats: {
        total_certifications: 150,
        total_organizations: 25,
        total_domains: 12,
        certifications_by_level: {
          'Beginner': 45,
          'Intermediate': 65,
          'Advanced': 40
        },
        average_cost: 350.75
      },
      recent_certifications: [
        { id: 1, name: 'CISSP', organization: 'ISC2', domain: 'Security Management', level: 'Advanced', cost: 699, popularity: 95 },
        { id: 2, name: 'CEH', organization: 'EC-Council', domain: 'Ethical Hacking', level: 'Intermediate', cost: 950, popularity: 88 },
        { id: 3, name: 'Security+', organization: 'CompTIA', domain: 'General Security', level: 'Beginner', cost: 349, popularity: 92 }
      ],
      popular_certifications: [
        { id: 1, name: 'CISSP', organization: 'ISC2', domain: 'Security Management', level: 'Advanced', cost: 699, popularity: 95 },
        { id: 4, name: 'CISM', organization: 'ISACA', domain: 'Security Management', level: 'Advanced', cost: 575, popularity: 85 },
        { id: 3, name: 'Security+', organization: 'CompTIA', domain: 'General Security', level: 'Beginner', cost: 349, popularity: 92 }
      ],
      organizations: [
        { name: 'ISC2', certification_count: 5, logo_url: null },
        { name: 'CompTIA', certification_count: 8, logo_url: null },
        { name: 'EC-Council', certification_count: 6, logo_url: null }
      ],
      domains: [
        { name: 'Security Management', certification_count: 12, description: null },
        { name: 'Network Security', certification_count: 15, description: null },
        { name: 'Ethical Hacking', certification_count: 8, description: null }
      ]
    };
  };

  if (loading) {
    return <div className="dashboard-loading">Loading dashboard data...</div>;
  }

  if (error || !data) {
    return (
      <div className="dashboard-error">
        <p>{error}</p>
        <h2>Sample Dashboard Data (Mock)</h2>
        {data && (
          <div>
            <p>Total Certifications: {data.stats.total_certifications}</p>
            <p>Total Organizations: {data.stats.total_organizations}</p>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      <h1>Security Certification Dashboard</h1>
      <p>Explore security certifications, organizations, and domains.</p>
      
      {/* Filters */}
      <div className="dashboard-filters">
        <h3>Filters</h3>
        <div className="filter-grid">
          <div className="filter-item">
            <label htmlFor="organization_id">Organization:</label>
            <select 
              id="organization_id" 
              name="organization_id" 
              value={filters.organization_id} 
              onChange={handleFilterChange}
            >
              <option value="">All Organizations</option>
              {data.organizations.map(org => (
                <option key={org.name} value={org.name}>{org.name}</option>
              ))}
            </select>
          </div>
          
          <div className="filter-item">
            <label htmlFor="domain">Domain:</label>
            <select 
              id="domain" 
              name="domain" 
              value={filters.domain} 
              onChange={handleFilterChange}
            >
              <option value="">All Domains</option>
              {data.domains.map(domain => (
                <option key={domain.name} value={domain.name}>{domain.name}</option>
              ))}
            </select>
          </div>
          
          <div className="filter-item">
            <label htmlFor="level">Level:</label>
            <select 
              id="level" 
              name="level" 
              value={filters.level} 
              onChange={handleFilterChange}
            >
              <option value="">All Levels</option>
              {Object.keys(data.stats.certifications_by_level).map(level => (
                <option key={level} value={level}>{level}</option>
              ))}
            </select>
          </div>
          
          <div className="filter-item">
            <label htmlFor="cost_min">Min Cost:</label>
            <input 
              type="number" 
              id="cost_min" 
              name="cost_min" 
              value={filters.cost_min} 
              onChange={handleFilterChange}
              placeholder="Min Cost"
            />
          </div>
          
          <div className="filter-item">
            <label htmlFor="cost_max">Max Cost:</label>
            <input 
              type="number" 
              id="cost_max" 
              name="cost_max" 
              value={filters.cost_max} 
              onChange={handleFilterChange}
              placeholder="Max Cost"
            />
          </div>
        </div>
      </div>
      
      {/* Stats Overview */}
      <div className="stats-overview">
        <h2>Overview</h2>
        <div className="stats-grid">
          <div className="stat-card">
            <h3>{data.stats.total_certifications}</h3>
            <p>Total Certifications</p>
          </div>
          <div className="stat-card">
            <h3>{data.stats.total_organizations}</h3>
            <p>Organizations</p>
          </div>
          <div className="stat-card">
            <h3>{data.stats.total_domains}</h3>
            <p>Security Domains</p>
          </div>
          <div className="stat-card">
            <h3>${data.stats.average_cost ? data.stats.average_cost.toFixed(2) : 'N/A'}</h3>
            <p>Average Cost</p>
          </div>
        </div>
      </div>
      
      {/* Certifications by Level */}
      <div className="level-distribution">
        <h2>Certifications by Level</h2>
        <div className="level-bars">
          {Object.entries(data.stats.certifications_by_level).map(([level, count]) => (
            <div key={level} className="level-bar-container">
              <div className="level-label">{level}</div>
              <div className="level-bar" style={{ 
                width: `${(count / data.stats.total_certifications) * 100}%`,
                backgroundColor: level === 'Beginner' ? '#4caf50' : level === 'Intermediate' ? '#2196f3' : '#f44336'
              }}>
                {count}
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Recent and Popular Certifications */}
      <div className="certifications-section">
        <div className="recent-certifications">
          <h2>Recent Certifications</h2>
          <ul className="cert-list">
            {data.recent_certifications.map(cert => (
              <li key={cert.id} className="cert-item">
                <h3>{cert.name}</h3>
                <div className="cert-details">
                  <span className="cert-org">{cert.organization}</span>
                  <span className="cert-domain">{cert.domain}</span>
                  <span className={`cert-level level-${cert.level.toLowerCase()}`}>{cert.level}</span>
                </div>
                <div className="cert-cost">
                  {cert.cost ? `$${cert.cost.toFixed(2)}` : 'Cost not available'}
                </div>
              </li>
            ))}
          </ul>
        </div>
        
        <div className="popular-certifications">
          <h2>Popular Certifications</h2>
          <ul className="cert-list">
            {data.popular_certifications.map(cert => (
              <li key={cert.id} className="cert-item">
                <h3>{cert.name}</h3>
                <div className="cert-details">
                  <span className="cert-org">{cert.organization}</span>
                  <span className="cert-domain">{cert.domain}</span>
                  <span className={`cert-level level-${cert.level.toLowerCase()}`}>{cert.level}</span>
                </div>
                <div className="cert-cost">
                  {cert.cost ? `$${cert.cost.toFixed(2)}` : 'Cost not available'}
                </div>
                {cert.popularity && (
                  <div className="popularity-meter">
                    <div className="popularity-fill" style={{ width: `${cert.popularity}%` }}></div>
                    <span className="popularity-text">{cert.popularity}% Popularity</span>
                  </div>
                )}
              </li>
            ))}
          </ul>
        </div>
      </div>
      
      {/* Organizations and Domains */}
      <div className="taxonomy-section">
        <div className="organizations">
          <h2>Top Organizations</h2>
          <ul className="taxonomy-list">
            {data.organizations.map(org => (
              <li key={org.name} className="taxonomy-item">
                <div className="taxonomy-name">{org.name}</div>
                <div className="taxonomy-count">{org.certification_count} certifications</div>
              </li>
            ))}
          </ul>
        </div>
        
        <div className="domains">
          <h2>Top Domains</h2>
          <ul className="taxonomy-list">
            {data.domains.map(domain => (
              <li key={domain.name} className="taxonomy-item">
                <div className="taxonomy-name">{domain.name}</div>
                <div className="taxonomy-count">{domain.certification_count} certifications</div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
