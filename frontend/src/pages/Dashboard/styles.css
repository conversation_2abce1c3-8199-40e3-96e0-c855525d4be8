.dashboard-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-container h1 {
  color: var(--primary-color);
  margin-bottom: 10px;
}

.dashboard-loading, .dashboard-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
}

.dashboard-error {
  color: #f44336;
}

/* Filters */
.dashboard-filters {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: var(--border-radius);
  margin-bottom: 20px;
}

.dashboard-filters h3 {
  margin-top: 0;
  margin-bottom: 15px;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.filter-item {
  display: flex;
  flex-direction: column;
}

.filter-item label {
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 0.9rem;
}

.filter-item select, .filter-item input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

/* Stats Overview */
.stats-overview {
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-card h3 {
  font-size: 2rem;
  margin: 0 0 10px 0;
  color: var(--primary-color);
}

.stat-card p {
  margin: 0;
  color: #666;
}

/* Level Distribution */
.level-distribution {
  margin-bottom: 30px;
}

.level-bars {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.level-bar-container {
  display: flex;
  align-items: center;
}

.level-label {
  width: 120px;
  font-weight: bold;
}

.level-bar {
  height: 30px;
  min-width: 30px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  transition: width 0.3s ease;
}

/* Certifications Section */
.certifications-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

@media (max-width: 768px) {
  .certifications-section {
    grid-template-columns: 1fr;
  }
}

.cert-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.cert-item {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cert-item h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: var(--primary-color);
}

.cert-details {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
}

.cert-org, .cert-domain, .cert-level {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
}

.cert-org {
  background-color: #e3f2fd;
  color: #1976d2;
}

.cert-domain {
  background-color: #e8f5e9;
  color: #388e3c;
}

.cert-level {
  color: white;
}

.level-beginner {
  background-color: #4caf50;
}

.level-intermediate {
  background-color: #2196f3;
}

.level-advanced {
  background-color: #f44336;
}

.cert-cost {
  font-weight: bold;
  margin-bottom: 10px;
}

.popularity-meter {
  height: 8px;
  background-color: #eee;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  margin-top: 10px;
}

.popularity-fill {
  height: 100%;
  background-color: #ff9800;
  border-radius: 4px;
}

.popularity-text {
  display: block;
  font-size: 0.8rem;
  color: #666;
  margin-top: 5px;
  text-align: right;
}

/* Taxonomy Section */
.taxonomy-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

@media (max-width: 768px) {
  .taxonomy-section {
    grid-template-columns: 1fr;
  }
}

.taxonomy-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.taxonomy-item {
  display: flex;
  justify-content: space-between;
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
}

.taxonomy-item:last-child {
  border-bottom: none;
}

.taxonomy-name {
  font-weight: bold;
}

.taxonomy-count {
  color: #666;
}
