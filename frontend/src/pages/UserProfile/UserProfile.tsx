import React, { useEffect, useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import api from '../../services/api';
import './UserProfile.css';

// Types
interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  emailNotifications: boolean;
  studyReminders: boolean;
  weeklyGoalHours: number;
  preferredDomains: string[];
}

interface UserCertification {
  id: number;
  name: string;
  progress: number;
  targetCompletionDate: string | null;
}

const UserProfile: React.FC = () => {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { translate } = useLanguage();
  
  // State
  const [preferences, setPreferences] = useState<UserPreferences>({
    theme: 'system',
    emailNotifications: true,
    studyReminders: false,
    weeklyGoalHours: 10,
    preferredDomains: [],
  });
  const [userCertifications, setUserCertifications] = useState<UserCertification[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  
  // Available domains for selection
  const availableDomains = [
    'Network Security',
    'Cloud Security',
    'Application Security',
    'Identity Management',
    'Security Operations',
    'Governance, Risk & Compliance',
    'Penetration Testing',
    'Cryptography',
  ];

  // Load user data
  useEffect(() => {
    if (isAuthenticated && user) {
      loadUserData();
    } else {
      setIsLoading(false);
    }
  }, [isAuthenticated, user]);

  const loadUserData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Load user preferences
      const userPreferences = await api.users.getPreferences();
      setPreferences(userPreferences);

      // Load user certifications
      const certifications = await api.users.getCertifications();
      setUserCertifications(certifications);
    } catch (err) {
      console.error('Error loading user data:', err);
      setError('Failed to load user data. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePreferenceChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setPreferences((prev) => ({ ...prev, [name]: checked }));
    } else {
      setPreferences((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleDomainToggle = (domain: string) => {
    setPreferences((prev) => {
      const currentDomains = [...prev.preferredDomains];
      
      if (currentDomains.includes(domain)) {
        // Remove domain if already selected
        return {
          ...prev,
          preferredDomains: currentDomains.filter(d => d !== domain)
        };
      } else {
        // Add domain if not selected
        return {
          ...prev,
          preferredDomains: [...currentDomains, domain]
        };
      }
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      await api.users.updatePreferences(preferences);
      setSuccessMessage('Preferences updated successfully');
      setIsEditing(false);
    } catch (err) {
      console.error('Error updating preferences:', err);
      setError('Failed to update preferences. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Render loading state
  if (authLoading || isLoading) {
    return <div className="loading-spinner">Loading...</div>;
  }

  // Render login prompt if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="profile-container">
        <h2>{translate('profile.login.required')}</h2>
        <p>{translate('profile.login.message')}</p>
        <button className="login-button">{translate('login')}</button>
      </div>
    );
  }

  return (
    <div className="profile-container">
      <h1>{translate('profile.title')}</h1>
      
      {error && <div className="error-message">{error}</div>}
      {successMessage && <div className="success-message">{successMessage}</div>}
      
      {/* User Info Section */}
      <section className="profile-section">
        <h2>{translate('profile.user.info')}</h2>
        <div className="user-info">
          <div className="avatar">
            {user?.username.charAt(0).toUpperCase() || 'U'}
          </div>
          <div className="user-details">
            <p><strong>{translate('profile.username')}:</strong> {user?.username}</p>
            <p><strong>{translate('profile.email')}:</strong> {user?.email}</p>
            <p><strong>{translate('profile.member.since')}:</strong> {new Date(user?.createdAt || Date.now()).toLocaleDateString()}</p>
          </div>
        </div>
      </section>
      
      {/* Preferences Section */}
      <section className="profile-section">
        <div className="section-header">
          <h2>{translate('profile.preferences')}</h2>
          <button 
            className="edit-button"
            onClick={() => setIsEditing(!isEditing)}
          >
            {isEditing ? translate('cancel') : translate('edit')}
          </button>
        </div>
        
        {isEditing ? (
          <form onSubmit={handleSubmit}>
            <div className="preference-group">
              <label htmlFor="theme">{translate('profile.theme')}:</label>
              <select 
                id="theme" 
                name="theme" 
                value={preferences.theme}
                onChange={handlePreferenceChange}
              >
                <option value="light">{translate('theme.light')}</option>
                <option value="dark">{translate('theme.dark')}</option>
                <option value="system">{translate('theme.system')}</option>
              </select>
            </div>
            
            <div className="preference-group">
              <label>
                <input 
                  type="checkbox" 
                  name="emailNotifications" 
                  checked={preferences.emailNotifications}
                  onChange={handlePreferenceChange}
                />
                {translate('profile.email.notifications')}
              </label>
            </div>
            
            <div className="preference-group">
              <label>
                <input 
                  type="checkbox" 
                  name="studyReminders" 
                  checked={preferences.studyReminders}
                  onChange={handlePreferenceChange}
                />
                {translate('profile.study.reminders')}
              </label>
            </div>
            
            <div className="preference-group">
              <label htmlFor="weeklyGoalHours">
                {translate('profile.weekly.goal')}:
              </label>
              <input 
                type="number" 
                id="weeklyGoalHours" 
                name="weeklyGoalHours"
                min="1" 
                max="40"
                value={preferences.weeklyGoalHours}
                onChange={handlePreferenceChange}
              />
            </div>
            
            <div className="preference-group">
              <label>{translate('profile.preferred.domains')}:</label>
              <div className="domain-buttons">
                {availableDomains.map(domain => (
                  <button
                    key={domain}
                    type="button"
                    className={`domain-button ${preferences.preferredDomains.includes(domain) ? 'selected' : ''}`}
                    onClick={() => handleDomainToggle(domain)}
                  >
                    {domain}
                  </button>
                ))}
              </div>
            </div>
            
            <div className="button-group">
              <button type="submit" className="save-button" disabled={isLoading}>
                {isLoading ? translate('saving') : translate('save')}
              </button>
              <button 
                type="button" 
                className="cancel-button" 
                onClick={() => setIsEditing(false)}
              >
                {translate('cancel')}
              </button>
            </div>
          </form>
        ) : (
          <div className="preferences-display">
            <p><strong>{translate('profile.theme')}:</strong> {translate(`theme.${preferences.theme}`)}</p>
            <p><strong>{translate('profile.email.notifications')}:</strong> {preferences.emailNotifications ? translate('yes') : translate('no')}</p>
            <p><strong>{translate('profile.study.reminders')}:</strong> {preferences.studyReminders ? translate('yes') : translate('no')}</p>
            <p><strong>{translate('profile.weekly.goal')}:</strong> {preferences.weeklyGoalHours} {translate('hours')}</p>
            <p><strong>{translate('profile.preferred.domains')}:</strong></p>
            <div className="domains-display">
              {preferences.preferredDomains.length > 0 ? 
                preferences.preferredDomains.map(domain => (
                  <span key={domain} className="domain-tag">{domain}</span>
                )) : 
                <em>{translate('profile.no.preferred.domains')}</em>
              }
            </div>
          </div>
        )}
      </section>
      
      {/* Certifications Section */}
      <section className="profile-section">
        <h2>{translate('profile.certifications')}</h2>
        
        {userCertifications.length > 0 ? (
          <div className="certifications-list">
            {userCertifications.map(cert => (
              <div key={cert.id} className="certification-card">
                <h3>{cert.name}</h3>
                <div className="progress-container">
                  <div 
                    className="progress-bar" 
                    style={{ width: `${cert.progress}%` }}
                  />
                  <span className="progress-text">{cert.progress}%</span>
                </div>
                {cert.targetCompletionDate && (
                  <p className="target-date">
                    <strong>{translate('profile.target.date')}:</strong> {new Date(cert.targetCompletionDate).toLocaleDateString()}
                  </p>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="empty-state">
            <p>{translate('profile.no.certifications')}</p>
            <button className="primary-button">
              {translate('profile.explore.certifications')}
            </button>
          </div>
        )}
      </section>
    </div>
  );
};

export default UserProfile; 