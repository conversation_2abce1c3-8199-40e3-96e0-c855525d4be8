import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import React from 'react';
import { AuthProvider } from '../../contexts/AuthContext';
import { LanguageProvider } from '../../contexts/LanguageContext';
import api from '../../services/api';
import UserProfile from './UserProfile';

// Mock the API
jest.mock('../../services/api', () => ({
  auth: {
    getCurrentUser: jest.fn(),
  },
  users: {
    getPreferences: jest.fn(),
    updatePreferences: jest.fn(),
    getCertifications: jest.fn(),
  },
}));

// Mock data
const mockUser = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  isAdmin: false,
  createdAt: '2023-01-01T00:00:00.000Z',
};

const mockPreferences = {
  theme: 'dark',
  emailNotifications: true,
  studyReminders: false,
  weeklyGoalHours: 15,
  preferredDomains: ['Network Security', 'Cloud Security'],
};

const mockCertifications = [
  {
    id: 1,
    name: 'Certified Ethical Hacker (CEH)',
    progress: 75,
    targetCompletionDate: '2023-12-31T00:00:00.000Z',
  },
  {
    id: 2,
    name: 'Certified Information Systems Security Professional (CISSP)',
    progress: 30,
    targetCompletionDate: null,
  },
];

// Mock context to simulate authenticated user
jest.mock('../../contexts/AuthContext', () => ({
  ...jest.requireActual('../../contexts/AuthContext'),
  useAuth: jest.fn().mockReturnValue({
    user: mockUser,
    isAuthenticated: true,
    isLoading: false,
  }),
}));

// Helper to render with providers
const renderWithProviders = (ui: React.ReactElement) => {
  return render(
    <AuthProvider>
      <LanguageProvider>
        {ui}
      </LanguageProvider>
    </AuthProvider>
  );
};

describe('UserProfile Component', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Setup default mock implementations
    (api.users.getPreferences as jest.Mock).mockResolvedValue(mockPreferences);
    (api.users.getCertifications as jest.Mock).mockResolvedValue(mockCertifications);
  });

  test('renders user information correctly', async () => {
    renderWithProviders(<UserProfile />);
    
    // Check for loading state first
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
    });
    
    // Check username is displayed
    expect(screen.getByText(/testuser/i)).toBeInTheDocument();
    
    // Check email is displayed
    expect(screen.getByText(/<EMAIL>/i)).toBeInTheDocument();
  });

  test('displays user preferences correctly', async () => {
    renderWithProviders(<UserProfile />);
    
    await waitFor(() => {
      expect(api.users.getPreferences).toHaveBeenCalled();
    });
    
    // Check theme preference
    expect(await screen.findByText(/dark/i)).toBeInTheDocument();
    
    // Check email notifications
    const emailNotificationsText = await screen.findByText(/email notifications/i);
    expect(emailNotificationsText).toBeInTheDocument();
    
    // Check preferred domains
    expect(await screen.findByText(/network security/i)).toBeInTheDocument();
    expect(await screen.findByText(/cloud security/i)).toBeInTheDocument();
  });

  test('displays certifications list correctly', async () => {
    renderWithProviders(<UserProfile />);
    
    await waitFor(() => {
      expect(api.users.getCertifications).toHaveBeenCalled();
    });
    
    // Check certification names
    expect(await screen.findByText(/certified ethical hacker/i)).toBeInTheDocument();
    expect(await screen.findByText(/cissp/i)).toBeInTheDocument();
    
    // Check progress is displayed
    expect(await screen.findByText('75%')).toBeInTheDocument();
    expect(await screen.findByText('30%')).toBeInTheDocument();
  });

  test('can edit preferences', async () => {
    renderWithProviders(<UserProfile />);
    
    await waitFor(() => {
      expect(api.users.getPreferences).toHaveBeenCalled();
    });
    
    // Click edit button
    const editButton = await screen.findByText(/edit/i);
    fireEvent.click(editButton);
    
    // Should now show form controls
    expect(screen.getByLabelText(/theme/i)).toBeInTheDocument();
    
    // Change theme
    const themeSelect = screen.getByLabelText(/theme/i);
    fireEvent.change(themeSelect, { target: { value: 'light' } });
    
    // Change email notifications
    const emailCheckbox = screen.getByLabelText(/email notifications/i);
    fireEvent.click(emailCheckbox);
    
    // Submit form
    const saveButton = screen.getByText(/save/i);
    fireEvent.click(saveButton);
    
    // Verify API was called with updated preferences
    await waitFor(() => {
      expect(api.users.updatePreferences).toHaveBeenCalledWith(
        expect.objectContaining({
          theme: 'light',
          emailNotifications: false,
        })
      );
    });
    
    // Should show success message
    expect(await screen.findByText(/preferences updated successfully/i)).toBeInTheDocument();
  });

  test('shows login prompt when not authenticated', async () => {
    // Mock unauthenticated state
    require('../../contexts/AuthContext').useAuth.mockReturnValue({
      user: null,
      isAuthenticated: false,
      isLoading: false,
    });
    
    renderWithProviders(<UserProfile />);
    
    // Should show login prompt
    expect(await screen.findByText(/login required/i)).toBeInTheDocument();
    expect(screen.getByText(/login/i)).toBeInTheDocument();
  });

  test('handles API errors gracefully', async () => {
    // Mock API error
    (api.users.getPreferences as jest.Mock).mockRejectedValue(new Error('Failed to fetch'));
    
    renderWithProviders(<UserProfile />);
    
    // Should show error message
    expect(await screen.findByText(/failed to load user data/i)).toBeInTheDocument();
  });
}); 