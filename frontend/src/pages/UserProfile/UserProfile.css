.profile-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.profile-container h1 {
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 0.5rem;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 1.2rem;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.success-message {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.profile-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h2 {
  margin: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #3f51b5;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 2rem;
  font-weight: bold;
}

.user-details p {
  margin: 0.5rem 0;
}

.edit-button,
.login-button,
.primary-button {
  background-color: #3f51b5;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.edit-button:hover,
.login-button:hover,
.primary-button:hover {
  background-color: #303f9f;
}

.preference-group {
  margin-bottom: 1.2rem;
}

.preference-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.preference-group select,
.preference-group input[type="number"] {
  width: 100%;
  max-width: 300px;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
}

.preference-group input[type="checkbox"] {
  margin-right: 0.5rem;
}

.domain-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.domain-button {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 0.3rem 0.8rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.domain-button.selected {
  background-color: #3f51b5;
  color: white;
  border-color: #3f51b5;
}

.button-group {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.save-button {
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1.5rem;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.save-button:hover {
  background-color: #388e3c;
}

.save-button:disabled {
  background-color: #a5d6a7;
  cursor: not-allowed;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.5rem 1.5rem;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.cancel-button:hover {
  background-color: #e0e0e0;
}

.preferences-display p {
  margin: 0.7rem 0;
}

.domains-display {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.domain-tag {
  background-color: #e8eaf6;
  color: #3f51b5;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.9rem;
}

.certifications-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.certification-card {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.certification-card h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.progress-container {
  background-color: #e0e0e0;
  border-radius: 4px;
  height: 20px;
  margin-bottom: 1rem;
  position: relative;
}

.progress-bar {
  background-color: #4caf50;
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease-in-out;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #333;
  font-size: 0.8rem;
  font-weight: 500;
}

.target-date {
  font-size: 0.9rem;
  margin: 0;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.empty-state p {
  margin-bottom: 1rem;
  color: #757575;
}
