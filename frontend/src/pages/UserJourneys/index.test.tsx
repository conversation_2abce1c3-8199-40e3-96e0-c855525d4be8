import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import axios from 'axios';
import UserJourneys from './index';

// Mock axios
jest.mock('axios');
const mockAxios = axios as jest.Mocked<typeof axios>;

describe('UserJourneys Component', () => {
    // Mock journey data
    const mockJourney = {
        user_id: 1,
        username: 'jsmith',
        display_name: '<PERSON>',
        avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
        events: [
            {
                id: 101,
                date: '2021-03-15',
                type: 'started',
                certification_id: 1,
                notes: 'Excited to begin my IT journey!'
            },
            {
                id: 102,
                date: '2021-06-20',
                type: 'completed',
                certification_id: 1,
                notes: 'Passed with a score of 825/900'
            }
        ],
        current_goals: [4, 5],
        completed_certifications: [1]
    };

    // Mock certifications data
    const mockCertifications = [
        {
            id: 1,
            name: 'CompTIA A+',
            vendor: 'CompTIA',
            level: 'beginner',
            description: 'Entry-level certification for IT technicians',
            icon: '💻'
        },
        {
            id: 4,
            name: 'CCNA',
            vendor: 'Cisco',
            level: 'intermediate',
            description: 'Cisco Certified Network Associate',
            icon: '📡'
        },
        {
            id: 5,
            name: 'AWS Solutions Architect Associate',
            vendor: 'AWS',
            level: 'intermediate',
            description: 'Cloud architecture certification for AWS',
            icon: '☁️'
        }
    ];

    beforeEach(() => {
        // Mock API rejected requests to use our mock data
        mockAxios.get.mockImplementation(() => {
            return Promise.reject(new Error('API error'));
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('renders loading state initially', () => {
        render(<UserJourneys />);
        expect(screen.getByText(/Loading journey data/i)).toBeInTheDocument();
    });

    test('renders user profile and timeline after loading', async () => {
        render(<UserJourneys />);

        // Wait for the loading to finish and mock data to be displayed
        await waitFor(() => {
            expect(screen.queryByText(/Loading journey data/i)).not.toBeInTheDocument();
        });

        // Check if the user profile is rendered
        expect(screen.getByText('John Smith')).toBeInTheDocument();
        expect(screen.getByText('@jsmith')).toBeInTheDocument();

        // Check if journey stats are displayed
        expect(screen.getByText('Total Certifications')).toBeInTheDocument();
        expect(screen.getByText('Completed')).toBeInTheDocument();

        // Check if timeline events are rendered
        expect(screen.getByText('Started')).toBeInTheDocument();
        expect(screen.getByText('Completed')).toBeInTheDocument();
        expect(screen.getByText('CompTIA A+')).toBeInTheDocument();
    });

    test('expands timeline event details when clicked', async () => {
        render(<UserJourneys />);

        // Wait for the loading to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading journey data/i)).not.toBeInTheDocument();
        });

        // Initially, the event notes should not be visible
        expect(screen.queryByText('Excited to begin my IT journey!')).not.toBeInTheDocument();

        // Find and click the first timeline event (Started)
        const startedEvent = screen.getAllByText('Started')[0];
        fireEvent.click(startedEvent.closest('.timeline-event') as HTMLElement);

        // Now the event notes should be visible
        expect(screen.getByText('Excited to begin my IT journey!')).toBeInTheDocument();

        // Click again to collapse
        fireEvent.click(startedEvent.closest('.timeline-event') as HTMLElement);

        // Event notes should be hidden again
        await waitFor(() => {
            expect(screen.queryByText('Excited to begin my IT journey!')).not.toBeInTheDocument();
        });
    });

    test('displays certification goals correctly', async () => {
        render(<UserJourneys />);

        // Wait for the loading to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading journey data/i)).not.toBeInTheDocument();
        });

        // Check if certification goals are displayed
        expect(screen.getByText('Current Certification Goals')).toBeInTheDocument();
        expect(screen.getByText('CCNA')).toBeInTheDocument();
        expect(screen.getByText('AWS Solutions Architect Associate')).toBeInTheDocument();
    });

    test('opens and closes add event form', async () => {
        render(<UserJourneys />);

        // Wait for the loading to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading journey data/i)).not.toBeInTheDocument();
        });

        // Check if the "Add Event" button is displayed
        const addEventButton = screen.getByText('Add Event');
        expect(addEventButton).toBeInTheDocument();

        // Click the button to open the form
        fireEvent.click(addEventButton);

        // Check if the form is displayed
        expect(screen.getByText('Add New Journey Event')).toBeInTheDocument();
        expect(screen.getByLabelText('Event Type:')).toBeInTheDocument();
        expect(screen.getByLabelText('Certification:')).toBeInTheDocument();
        expect(screen.getByLabelText('Date:')).toBeInTheDocument();
        expect(screen.getByLabelText('Notes:')).toBeInTheDocument();

        // Click the cancel button to close the form
        const cancelButton = screen.getByText('Cancel');
        fireEvent.click(cancelButton);

        // Check if the form is no longer displayed
        expect(screen.queryByText('Add New Journey Event')).not.toBeInTheDocument();
    });

    test('adds a new journey event', async () => {
        render(<UserJourneys />);

        // Wait for the loading to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading journey data/i)).not.toBeInTheDocument();
        });

        // Open the add event form
        fireEvent.click(screen.getByText('Add Event'));

        // Fill in the form
        const eventTypeSelect = screen.getByLabelText('Event Type:');
        fireEvent.change(eventTypeSelect, { target: { value: 'completed' } });

        const certificationSelect = screen.getByLabelText('Certification:');
        fireEvent.change(certificationSelect, { target: { value: '4' } });

        const dateInput = screen.getByLabelText('Date:');
        fireEvent.change(dateInput, { target: { value: '2023-05-15' } });

        const notesInput = screen.getByLabelText('Notes:');
        fireEvent.change(notesInput, { target: { value: 'Passed with distinction' } });

        // Submit the form
        fireEvent.click(screen.getByText('Save Event'));

        // Wait for the form to close and the new event to be added
        await waitFor(() => {
            expect(screen.queryByText('Add New Journey Event')).not.toBeInTheDocument();
        });

        // Check if the stats have been updated (completed count should increase)
        const completedStats = screen.getAllByText('Completed');
        const completedValue = completedStats[0].parentElement?.querySelector('.stat-value');
        expect(completedValue).toHaveTextContent('2'); // Was 1, now should be 2
    });

    test('validates required fields when adding a new event', async () => {
        render(<UserJourneys />);

        // Wait for the loading to finish
        await waitFor(() => {
            expect(screen.queryByText(/Loading journey data/i)).not.toBeInTheDocument();
        });

        // Open the add event form
        fireEvent.click(screen.getByText('Add Event'));

        // Submit without selecting a certification
        fireEvent.click(screen.getByText('Save Event'));

        // Check for error message
        expect(screen.getByText('Please fill out all required fields')).toBeInTheDocument();

        // Form should still be open
        expect(screen.getByText('Add New Journey Event')).toBeInTheDocument();
    });

    test('handles error state gracefully', async () => {
        // This is already testing error state since we're rejecting API calls
        render(<UserJourneys />);

        // The component should recover with mock data
        await waitFor(() => {
            expect(screen.queryByText(/Failed to load journey data/i)).not.toBeInTheDocument();
            expect(screen.getByText('John Smith')).toBeInTheDocument();
        });
    });
}); 