.user-journey-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
  font-family: '<PERSON><PERSON>', sans-serif;
}

.user-journey-container h1 {
  text-align: center;
  margin-bottom: 2rem;
  color: #333;
}

/* Loading and error states */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #4a6cf7;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-container {
  text-align: center;
  padding: 2rem;
  background-color: #fff8f8;
  border: 1px solid #ffefef;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.error-message {
  color: #e53935;
  margin-bottom: 1rem;
}

.error-container button {
  padding: 0.5rem 1rem;
  background-color: #e53935;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.error-container button:hover {
  background-color: #c62828;
}

/* User profile section */
.user-profile-section {
  margin-bottom: 2rem;
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.user-profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 1.5rem;
  border: 3px solid #4a6cf7;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #4a6cf7;
  color: white;
  font-size: 2rem;
  font-weight: bold;
}

.user-details h2 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.user-details .username {
  color: #666;
  margin: 0;
  font-size: 0.9rem;
}

.journey-stats {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
}

.stat-item {
  flex: 1;
  min-width: 100px;
  padding: 1rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: #4a6cf7;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
}

.stat-item.completed .stat-value {
  color: #4caf50;
}

.stat-item.in-progress .stat-value {
  color: #ff9800;
}

.stat-item.planned .stat-value {
  color: #9c27b0;
}

/* Journey content */
.journey-content {
  margin-bottom: 2rem;
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.journey-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.journey-header h3 {
  margin: 0;
  color: #333;
}

.add-event-button {
  padding: 0.5rem 1rem;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.add-event-button:hover {
  background-color: #3a5ce5;
}

/* Add event form */
.add-event-form {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.add-event-form h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #333;
}

.form-error-message {
  color: #e53935;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: #ffebee;
  border-radius: 4px;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.95rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4a6cf7;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.25);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.cancel-button {
  padding: 0.75rem 1.5rem;
  background-color: #f5f5f5;
  color: #333;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.cancel-button:hover {
  background-color: #e0e0e0;
}

.save-button {
  padding: 0.75rem 1.5rem;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.save-button:hover:not(:disabled) {
  background-color: #3a5ce5;
}

.save-button:disabled {
  background-color: #a5b4f9;
  cursor: not-allowed;
}

/* Timeline */
.certification-timeline {
  position: relative;
  padding-left: 2rem;
}

.certification-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 8px;
  width: 2px;
  background-color: #e0e0e0;
}

.timeline-event {
  position: relative;
  margin-bottom: 2rem;
  cursor: pointer;
  transition: all 0.2s;
}

.timeline-event:hover {
  transform: translateX(5px);
}

.timeline-date {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.timeline-connector {
  position: absolute;
  top: 0;
  left: -2rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.timeline-line {
  flex-grow: 1;
  width: 2px;
  background-color: #e0e0e0;
}

.timeline-icon {
  position: absolute;
  top: 0;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid #4a6cf7;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.timeline-event.started .timeline-icon {
  border-color: #2196f3;
}

.timeline-event.completed .timeline-icon {
  border-color: #4caf50;
}

.timeline-event.milestone .timeline-icon {
  border-color: #ff9800;
}

.timeline-event.goal .timeline-icon {
  border-color: #9c27b0;
}

.timeline-content {
  background-color: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.timeline-event.started .timeline-content {
  border-left: 3px solid #2196f3;
}

.timeline-event.completed .timeline-content {
  border-left: 3px solid #4caf50;
}

.timeline-event.milestone .timeline-content {
  border-left: 3px solid #ff9800;
}

.timeline-event.goal .timeline-content {
  border-left: 3px solid #9c27b0;
}

.timeline-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.event-type-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  background-color: #f0f0f0;
  color: #666;
}

.timeline-event.started .event-type-badge {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.timeline-event.completed .event-type-badge {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.timeline-event.milestone .event-type-badge {
  background-color: #fff3e0;
  color: #e65100;
}

.timeline-event.goal .event-type-badge {
  background-color: #f3e5f5;
  color: #4a148c;
}

.certification-name {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cert-icon {
  font-size: 1.2rem;
}

.certification-vendor {
  font-size: 0.85rem;
  color: #666;
}

.timeline-details {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.certification-description {
  color: #555;
  margin-bottom: 1rem;
}

.event-notes {
  background-color: #f9f9f9;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.event-notes h5 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #333;
  font-size: 0.9rem;
}

.event-notes p {
  margin: 0;
  color: #555;
  font-size: 0.9rem;
}

.certification-level {
  font-size: 0.85rem;
  color: #555;
}

.level-badge {
  display: inline-block;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.level-badge.beginner {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.level-badge.intermediate {
  background-color: #fff3e0;
  color: #e65100;
}

.level-badge.advanced {
  background-color: #ffebee;
  color: #b71c1c;
}

.level-badge.expert {
  background-color: #f3e5f5;
  color: #4a148c;
}

.empty-timeline {
  text-align: center;
  padding: 2rem;
  background-color: white;
  border-radius: 8px;
  color: #666;
}

/* Journey goals */
.journey-goals {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.journey-goals h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #333;
}

.certification-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.certification-card {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.certification-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.cert-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.cert-icon-large {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #f5f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.cert-header h4 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.cert-vendor {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.cert-level {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  margin-bottom: 1rem;
}

.cert-level.beginner {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.cert-level.intermediate {
  background-color: #fff3e0;
  color: #e65100;
}

.cert-level.advanced {
  background-color: #ffebee;
  color: #b71c1c;
}

.cert-level.expert {
  background-color: #f3e5f5;
  color: #4a148c;
}

.cert-description {
  color: #555;
  font-size: 0.95rem;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.cert-progress {
  margin-top: 1rem;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: #555;
  margin-bottom: 0.5rem;
}

.progress-bar {
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #4a6cf7;
  border-radius: 4px;
}

.empty-goals {
  text-align: center;
  padding: 2rem;
  background-color: white;
  border-radius: 8px;
  color: #666;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .user-journey-container {
    padding: 1rem;
  }
  
  .user-profile-header {
    flex-direction: column;
    text-align: center;
  }
  
  .user-avatar {
    margin-right: 0;
    margin-bottom: 1rem;
  }
  
  .journey-stats {
    flex-direction: column;
  }
  
  .stat-item {
    min-width: 100%;
  }
  
  .journey-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .certification-timeline {
    padding-left: 1.5rem;
  }
  
  .timeline-connector {
    left: -1.5rem;
  }
} 