import axios from 'axios';
import React, { useEffect, useState } from 'react';
import './styles.css';

// Define interfaces for data models
interface Certification {
    id: number;
    name: string;
    vendor: string;
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    description: string;
    icon?: string;
}

interface JourneyEvent {
    id: number;
    date: string;
    type: 'started' | 'completed' | 'goal' | 'milestone';
    certification_id: number;
    notes?: string;
}

interface UserJourney {
    user_id: number;
    username: string;
    display_name: string;
    avatar?: string;
    events: JourneyEvent[];
    current_goals: number[];
    completed_certifications: number[];
}

const UserJourneys: React.FC = () => {
    const [userJourney, setUserJourney] = useState<UserJourney | null>(null);
    const [certifications, setCertifications] = useState<Record<number, Certification>>({});
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedEvent, setSelectedEvent] = useState<number | null>(null);
    const [addingEvent, setAddingEvent] = useState<boolean>(false);
    const [newEvent, setNewEvent] = useState<Partial<JourneyEvent>>({
        type: 'started',
        date: new Date().toISOString().split('T')[0],
    });
    const [availableCertifications, setAvailableCertifications] = useState<Certification[]>([]);

    // Fetch user journey and certification data
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);

                // Fetch user journey
                const journeyResponse = await axios.get('/api/user/journey');
                setUserJourney(journeyResponse.data);

                // Fetch certifications
                const certificationsResponse = await axios.get('/api/certifications');

                // Convert array to record for easier lookup
                const certMap: Record<number, Certification> = {};
                certificationsResponse.data.forEach((cert: Certification) => {
                    certMap[cert.id] = cert;
                });

                setCertifications(certMap);
                setAvailableCertifications(certificationsResponse.data);

                setError(null);
            } catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load journey data. Please try again later.');

                // Mock data for development
                const mockCertifications = [
                    {
                        id: 1,
                        name: 'CompTIA A+',
                        vendor: 'CompTIA',
                        level: 'beginner',
                        description: 'Entry-level certification for IT technicians',
                        icon: '💻'
                    },
                    {
                        id: 2,
                        name: 'CompTIA Network+',
                        vendor: 'CompTIA',
                        level: 'beginner',
                        description: 'Certification for networking professionals',
                        icon: '🌐'
                    },
                    {
                        id: 3,
                        name: 'CompTIA Security+',
                        vendor: 'CompTIA',
                        level: 'intermediate',
                        description: 'Certification for security professionals',
                        icon: '🔒'
                    },
                    {
                        id: 4,
                        name: 'CCNA',
                        vendor: 'Cisco',
                        level: 'intermediate',
                        description: 'Cisco Certified Network Associate',
                        icon: '📡'
                    },
                    {
                        id: 5,
                        name: 'AWS Solutions Architect Associate',
                        vendor: 'AWS',
                        level: 'intermediate',
                        description: 'Cloud architecture certification for AWS',
                        icon: '☁️'
                    },
                    {
                        id: 6,
                        name: 'Microsoft Azure Administrator',
                        vendor: 'Microsoft',
                        level: 'intermediate',
                        description: 'Cloud administration certification for Azure',
                        icon: '☁️'
                    }
                ] as Certification[];

                const mockJourney = {
                    user_id: 1,
                    username: 'jsmith',
                    display_name: 'John Smith',
                    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
                    events: [
                        {
                            id: 101,
                            date: '2021-03-15',
                            type: 'started',
                            certification_id: 1,
                            notes: 'Excited to begin my IT journey!'
                        },
                        {
                            id: 102,
                            date: '2021-06-20',
                            type: 'completed',
                            certification_id: 1,
                            notes: 'Passed with a score of 825/900'
                        },
                        {
                            id: 103,
                            date: '2021-07-10',
                            type: 'started',
                            certification_id: 2,
                            notes: 'Building on my A+ knowledge'
                        },
                        {
                            id: 104,
                            date: '2021-10-05',
                            type: 'completed',
                            certification_id: 2,
                            notes: 'Passed with a score of 790/900'
                        },
                        {
                            id: 105,
                            date: '2021-11-15',
                            type: 'started',
                            certification_id: 3,
                            notes: 'Moving into security'
                        },
                        {
                            id: 106,
                            date: '2022-02-28',
                            type: 'completed',
                            certification_id: 3,
                            notes: 'This was challenging but worth it'
                        },
                        {
                            id: 107,
                            date: '2022-04-10',
                            type: 'started',
                            certification_id: 4,
                            notes: 'Focusing on network engineering skills'
                        },
                        {
                            id: 108,
                            date: '2022-05-15',
                            type: 'milestone',
                            certification_id: 4,
                            notes: 'Completed 50% of the material'
                        },
                        {
                            id: 109,
                            date: '2022-08-01',
                            type: 'goal',
                            certification_id: 5,
                            notes: 'Planning to start AWS training in Q4'
                        }
                    ],
                    current_goals: [4, 5],
                    completed_certifications: [1, 2, 3]
                } as UserJourney;

                // Convert certifications array to record
                const certMap: Record<number, Certification> = {};
                mockCertifications.forEach(cert => {
                    certMap[cert.id] = cert;
                });

                setCertifications(certMap);
                setUserJourney(mockJourney);
                setAvailableCertifications(mockCertifications);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    // Get certification details by ID
    const getCertification = (id: number): Certification | undefined => {
        return certifications[id];
    };

    // Format date for display
    const formatDate = (dateString: string): string => {
        const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
        return new Date(dateString).toLocaleDateString(undefined, options);
    };

    // Get icon for event type
    const getEventTypeIcon = (type: string): string => {
        switch (type) {
            case 'started':
                return '🚀';
            case 'completed':
                return '🏆';
            case 'goal':
                return '🎯';
            case 'milestone':
                return '🏁';
            default:
                return '📌';
        }
    };

    // Format event type for display
    const formatEventType = (type: string): string => {
        return type.charAt(0).toUpperCase() + type.slice(1);
    };

    // Add a new event
    const handleAddEvent = async () => {
        if (!newEvent.certification_id || !newEvent.date || !newEvent.type) {
            setError('Please fill out all required fields');
            return;
        }

        try {
            setLoading(true);

            // Send the new event to the API
            // const response = await axios.post('/api/user/journey/events', newEvent);

            // Simulating API response for development
            const mockResponse = {
                ...newEvent,
                id: Date.now(),
            } as JourneyEvent;

            // Update the local state
            if (userJourney) {
                const updatedJourney = {
                    ...userJourney,
                    events: [...userJourney.events, mockResponse]
                };

                // Update goals or completed certifications if applicable
                if (newEvent.type === 'goal' && !updatedJourney.current_goals.includes(newEvent.certification_id as number)) {
                    updatedJourney.current_goals.push(newEvent.certification_id as number);
                } else if (newEvent.type === 'completed' && !updatedJourney.completed_certifications.includes(newEvent.certification_id as number)) {
                    updatedJourney.completed_certifications.push(newEvent.certification_id as number);
                }

                setUserJourney(updatedJourney);
            }

            // Reset the form
            setNewEvent({
                type: 'started',
                date: new Date().toISOString().split('T')[0],
            });
            setAddingEvent(false);
            setError(null);
        } catch (err) {
            console.error('Error adding event:', err);
            setError('Failed to add event. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    // Calculate journey stats
    const calculateStats = () => {
        if (!userJourney) return { total: 0, completed: 0, inProgress: 0, planned: 0 };

        const completed = userJourney.completed_certifications.length;

        // Count events with 'started' type where there's no matching 'completed' event
        const inProgress = userJourney.events
            .filter(event => event.type === 'started')
            .filter(startedEvent => !userJourney.events.some(
                completedEvent => completedEvent.type === 'completed' && completedEvent.certification_id === startedEvent.certification_id
            ))
            .length;

        const planned = userJourney.events
            .filter(event => event.type === 'goal')
            .filter(goalEvent => !userJourney.events.some(
                startedEvent => startedEvent.type === 'started' && startedEvent.certification_id === goalEvent.certification_id
            ))
            .length;

        return {
            total: completed + inProgress + planned,
            completed,
            inProgress,
            planned
        };
    };

    const stats = calculateStats();

    // Sort events chronologically
    const sortedEvents = userJourney?.events.slice().sort((a, b) => {
        return new Date(b.date).getTime() - new Date(a.date).getTime();
    });

    return (
        <div className="user-journey-container">
            <h1>Certification Journey</h1>

            {loading && !userJourney ? (
                <div className="loading-container">
                    <div className="loading-spinner"></div>
                    <p>Loading journey data...</p>
                </div>
            ) : error && !userJourney ? (
                <div className="error-container">
                    <p className="error-message">{error}</p>
                    <button onClick={() => window.location.reload()}>Try Again</button>
                </div>
            ) : userJourney ? (
                <>
                    <div className="user-profile-section">
                        <div className="user-profile-header">
                            <div className="user-avatar">
                                {userJourney.avatar ? (
                                    <img src={userJourney.avatar} alt={userJourney.display_name} />
                                ) : (
                                    <div className="avatar-placeholder">
                                        {userJourney.display_name.charAt(0)}
                                    </div>
                                )}
                            </div>
                            <div className="user-details">
                                <h2>{userJourney.display_name}</h2>
                                <p className="username">@{userJourney.username}</p>
                            </div>
                        </div>

                        <div className="journey-stats">
                            <div className="stat-item">
                                <div className="stat-value">{stats.total}</div>
                                <div className="stat-label">Total Certifications</div>
                            </div>
                            <div className="stat-item completed">
                                <div className="stat-value">{stats.completed}</div>
                                <div className="stat-label">Completed</div>
                            </div>
                            <div className="stat-item in-progress">
                                <div className="stat-value">{stats.inProgress}</div>
                                <div className="stat-label">In Progress</div>
                            </div>
                            <div className="stat-item planned">
                                <div className="stat-value">{stats.planned}</div>
                                <div className="stat-label">Planned</div>
                            </div>
                        </div>
                    </div>

                    <div className="journey-content">
                        <div className="journey-header">
                            <h3>Certification Timeline</h3>
                            <button
                                className="add-event-button"
                                onClick={() => setAddingEvent(true)}
                            >
                                Add Event
                            </button>
                        </div>

                        {/* Add Event Form */}
                        {addingEvent && (
                            <div className="add-event-form">
                                <h4>Add New Journey Event</h4>

                                {error && (
                                    <div className="form-error-message">{error}</div>
                                )}

                                <div className="form-group">
                                    <label htmlFor="event-type">Event Type:</label>
                                    <select
                                        id="event-type"
                                        value={newEvent.type}
                                        onChange={(e) => setNewEvent({ ...newEvent, type: e.target.value as JourneyEvent['type'] })}
                                    >
                                        <option value="started">Started</option>
                                        <option value="completed">Completed</option>
                                        <option value="milestone">Milestone</option>
                                        <option value="goal">Future Goal</option>
                                    </select>
                                </div>

                                <div className="form-group">
                                    <label htmlFor="certification">Certification:</label>
                                    <select
                                        id="certification"
                                        value={newEvent.certification_id || ''}
                                        onChange={(e) => setNewEvent({ ...newEvent, certification_id: Number(e.target.value) })}
                                    >
                                        <option value="">Select a certification</option>
                                        {availableCertifications.map(cert => (
                                            <option key={cert.id} value={cert.id}>
                                                {cert.name} ({cert.vendor})
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div className="form-group">
                                    <label htmlFor="event-date">Date:</label>
                                    <input
                                        id="event-date"
                                        type="date"
                                        value={newEvent.date}
                                        onChange={(e) => setNewEvent({ ...newEvent, date: e.target.value })}
                                    />
                                </div>

                                <div className="form-group">
                                    <label htmlFor="event-notes">Notes:</label>
                                    <textarea
                                        id="event-notes"
                                        value={newEvent.notes || ''}
                                        onChange={(e) => setNewEvent({ ...newEvent, notes: e.target.value })}
                                        rows={3}
                                        placeholder="Add any details about this event..."
                                    ></textarea>
                                </div>

                                <div className="form-actions">
                                    <button
                                        className="cancel-button"
                                        onClick={() => {
                                            setAddingEvent(false);
                                            setError(null);
                                        }}
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        className="save-button"
                                        onClick={handleAddEvent}
                                        disabled={loading}
                                    >
                                        {loading ? 'Saving...' : 'Save Event'}
                                    </button>
                                </div>
                            </div>
                        )}

                        <div className="certification-timeline">
                            {sortedEvents?.map(event => {
                                const certification = getCertification(event.certification_id);

                                if (!certification) return null;

                                return (
                                    <div
                                        key={event.id}
                                        className={`timeline-event ${event.type} ${selectedEvent === event.id ? 'expanded' : ''}`}
                                        onClick={() => setSelectedEvent(selectedEvent === event.id ? null : event.id)}
                                    >
                                        <div className="timeline-date">
                                            {formatDate(event.date)}
                                        </div>

                                        <div className="timeline-connector">
                                            <div className="timeline-line"></div>
                                            <div className="timeline-icon">
                                                {getEventTypeIcon(event.type)}
                                            </div>
                                        </div>

                                        <div className="timeline-content">
                                            <div className="timeline-header">
                                                <span className="event-type-badge">
                                                    {formatEventType(event.type)}
                                                </span>
                                                <h4 className="certification-name">
                                                    {certification.icon && <span className="cert-icon">{certification.icon}</span>}
                                                    {certification.name}
                                                </h4>
                                                <span className="certification-vendor">{certification.vendor}</span>
                                            </div>

                                            {selectedEvent === event.id && (
                                                <div className="timeline-details">
                                                    <p className="certification-description">
                                                        {certification.description}
                                                    </p>
                                                    {event.notes && (
                                                        <div className="event-notes">
                                                            <h5>Notes:</h5>
                                                            <p>{event.notes}</p>
                                                        </div>
                                                    )}
                                                    <div className="certification-level">
                                                        Level: <span className={`level-badge ${certification.level}`}>
                                                            {certification.level.charAt(0).toUpperCase() + certification.level.slice(1)}
                                                        </span>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                );
                            })}

                            {sortedEvents?.length === 0 && (
                                <div className="empty-timeline">
                                    <p>No journey events yet. Add your first certification event!</p>
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="journey-goals">
                        <h3>Current Certification Goals</h3>

                        <div className="goals-container">
                            {userJourney.current_goals.length > 0 ? (
                                <div className="certification-cards">
                                    {userJourney.current_goals.map(goalId => {
                                        const certification = getCertification(goalId);
                                        if (!certification) return null;

                                        return (
                                            <div key={goalId} className="certification-card">
                                                <div className="cert-header">
                                                    <div className="cert-icon-large">
                                                        {certification.icon || '🎓'}
                                                    </div>
                                                    <h4>{certification.name}</h4>
                                                </div>
                                                <div className="cert-vendor">{certification.vendor}</div>
                                                <div className={`cert-level ${certification.level}`}>
                                                    {certification.level.charAt(0).toUpperCase() + certification.level.slice(1)}
                                                </div>
                                                <p className="cert-description">{certification.description}</p>

                                                {/* Show progress if in progress */}
                                                {userJourney.events.some(e =>
                                                    e.certification_id === goalId && e.type === 'started' &&
                                                    !userJourney.completed_certifications.includes(goalId)
                                                ) && (
                                                        <div className="cert-progress">
                                                            <div className="progress-label">In Progress</div>
                                                            <div className="progress-bar">
                                                                <div className="progress-fill" style={{ width: '50%' }}></div>
                                                            </div>
                                                        </div>
                                                    )}
                                            </div>
                                        );
                                    })}
                                </div>
                            ) : (
                                <div className="empty-goals">
                                    <p>No certification goals set. Add a goal to your journey!</p>
                                </div>
                            )}
                        </div>
                    </div>
                </>
            ) : null}
        </div>
    );
};

export default UserJourneys; 