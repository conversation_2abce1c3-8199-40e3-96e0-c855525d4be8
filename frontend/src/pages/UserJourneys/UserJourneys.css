.journeys-container {
  padding: 20px;
}

.journey-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 18px;
  color: #6c757d;
}

.journeys-intro {
  max-width: 800px;
  line-height: 1.6;
  margin-bottom: 25px;
}

.journeys-content {
  display: flex;
  gap: 30px;
}

.journeys-list {
  flex: 1;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.journeys-list ul {
  list-style-type: none;
  padding: 0;
}

.journeys-list li {
  padding: 15px;
  margin-bottom: 15px;
  background-color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.journeys-list li:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.journeys-list li.selected {
  border-left: 4px solid #007bff;
  background-color: #f8f9fa;
}

.journeys-list h4 {
  margin: 0 0 8px 0;
  color: #343a40;
}

.journey-user {
  color: #6c757d;
  font-size: 14px;
  margin: 0 0 5px 0;
}

.journey-role {
  color: #28a745;
  font-weight: bold;
  font-size: 14px;
  margin: 0;
}

.journey-details {
  flex: 2;
  background: #f8f9fa;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.journey-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #dee2e6;
}

.journey-meta {
  display: flex;
  gap: 20px;
  color: #6c757d;
  font-size: 14px;
}

.journey-background {
  margin-bottom: 25px;
  line-height: 1.6;
}

.journey-timeline h4 {
  margin-bottom: 20px;
}

.timeline {
  list-style-type: none;
  padding: 0;
  margin: 0;
  position: relative;
}

.timeline:before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 15px;
  width: 2px;
  background: #dee2e6;
}

.timeline-item {
  position: relative;
  padding-left: 40px;
  padding-bottom: 30px;
}

.timeline-marker {
  position: absolute;
  left: 0;
  top: 5px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: white;
  border: 2px solid #6c757d;
  z-index: 1;
}

.timeline-item.certification .timeline-marker {
  border-color: #28a745;
}

.timeline-item.study .timeline-marker {
  border-color: #007bff;
}

.timeline-item.job .timeline-marker {
  border-color: #6f42c1;
}

.timeline-item.achievement .timeline-marker {
  border-color: #fd7e14;
}

.timeline-content {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.timeline-content h5 {
  margin-top: 0;
  color: #343a40;
}

.event-date {
  font-weight: normal;
  color: #6c757d;
  margin-right: 10px;
}

.event-type {
  display: inline-block;
  padding: 3px 8px;
  font-size: 12px;
  font-weight: bold;
  border-radius: 4px;
  text-transform: uppercase;
  margin-top: 10px;
}

.timeline-item.certification .event-type {
  background-color: #e9f7ef;
  color: #28a745;
}

.timeline-item.study .event-type {
  background-color: #e7f5ff;
  color: #007bff;
}

.timeline-item.job .event-type {
  background-color: #f3e9fa;
  color: #6f42c1;
}

.timeline-item.achievement .event-type {
  background-color: #fff3e6;
  color: #fd7e14;
} 