import React, { useEffect, useState } from 'react';
import './UserJourneys.css';

interface JourneyEvent {
    id: number;
    date: string;
    title: string;
    description: string;
    type: 'certification' | 'study' | 'job' | 'achievement';
}

interface Journey {
    id: number;
    userId: number;
    username: string;
    title: string;
    startDate: string;
    currentRole: string;
    background: string;
    events: JourneyEvent[];
}

const mockJourneys: Journey[] = [
    {
        id: 1,
        userId: 101,
        username: "sarah_cyber",
        title: "From Network Admin to Security Specialist",
        startDate: "2020-01-15",
        currentRole: "Security Operations Analyst",
        background: "Started as a network administrator with no security experience. Decided to pivot to cybersecurity after experiencing a security incident at my company.",
        events: [
            {
                id: 1,
                date: "2020-02-10",
                title: "Started studying for Security+",
                description: "Dedicated 10 hours per week to Security+ preparation using official CompTIA materials",
                type: "study"
            },
            {
                id: 2,
                date: "2020-05-15",
                title: "Earned Security+ Certification",
                description: "Passed with a score of 825/900",
                type: "certification"
            },
            {
                id: 3,
                date: "2020-08-20",
                title: "Started Junior SOC Analyst position",
                description: "Found my first security role at a medium-sized financial company",
                type: "job"
            },
            {
                id: 4,
                date: "2021-03-10",
                title: "Completed CySA+ Certification",
                description: "Focused on threat detection and vulnerability management",
                type: "certification"
            },
            {
                id: 5,
                date: "2022-01-05",
                title: "Promoted to Security Operations Analyst",
                description: "Taking on more responsibility for incident response and threat hunting",
                type: "achievement"
            }
        ]
    },
    {
        id: 2,
        userId: 102,
        username: "mike_secure",
        title: "Self-taught Ethical Hacker Journey",
        startDate: "2019-06-20",
        currentRole: "Penetration Tester",
        background: "Computer science graduate who discovered a passion for finding vulnerabilities. Started with CTF competitions and bug bounties.",
        events: [
            {
                id: 1,
                date: "2019-07-15",
                title: "Started learning ethical hacking",
                description: "Began with Hack The Box and various online courses",
                type: "study"
            },
            {
                id: 2,
                date: "2019-12-10",
                title: "Earned CEH Certification",
                description: "Passed on first attempt after 4 months of study",
                type: "certification"
            },
            {
                id: 3,
                date: "2020-06-25",
                title: "First paid vulnerability disclosure",
                description: "Found and reported critical SQL injection vulnerability",
                type: "achievement"
            },
            {
                id: 4,
                date: "2021-02-18",
                title: "Achieved OSCP Certification",
                description: "Passed practical exam after 6 months of preparation",
                type: "certification"
            },
            {
                id: 5,
                date: "2021-05-10",
                title: "Hired as Junior Penetration Tester",
                description: "Joined cybersecurity consulting firm",
                type: "job"
            },
            {
                id: 6,
                date: "2022-04-15",
                title: "Promoted to Penetration Tester",
                description: "After completing 25+ successful engagements",
                type: "achievement"
            }
        ]
    }
];

const UserJourneys: React.FC = () => {
    const [journeys, setJourneys] = useState<Journey[]>([]);
    const [selectedJourney, setSelectedJourney] = useState<Journey | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true);

    useEffect(() => {
        // Simulate API call to fetch user journeys
        const fetchJourneys = async () => {
            try {
                // In a real app, this would be an API call
                // const response = await fetch('/api/v1/user/journey');
                // const data = await response.json();

                // Using mock data for now
                setTimeout(() => {
                    setJourneys(mockJourneys);
                    setSelectedJourney(mockJourneys[0]);
                    setIsLoading(false);
                }, 800);
            } catch (err) {
                console.error("Failed to load user journeys:", err);
                setIsLoading(false);
            }
        };

        fetchJourneys();
    }, []);

    const handleJourneySelect = (journey: Journey) => {
        setSelectedJourney(journey);
    };

    if (isLoading) {
        return <div className="journey-loading">Loading user journeys...</div>;
    }

    return (
        <div className="journeys-container">
            <h2>Cybersecurity Career Journeys</h2>
            <p className="journeys-intro">
                Explore real career paths of professionals who have successfully navigated the cybersecurity landscape.
                Learn from their experiences, challenges, and achievements.
            </p>

            <div className="journeys-content">
                <div className="journeys-list">
                    <h3>Featured Journeys</h3>
                    <ul>
                        {journeys.map(journey => (
                            <li
                                key={journey.id}
                                className={selectedJourney?.id === journey.id ? 'selected' : ''}
                                onClick={() => handleJourneySelect(journey)}
                            >
                                <h4>{journey.title}</h4>
                                <p className="journey-user">by {journey.username}</p>
                                <p className="journey-role">{journey.currentRole}</p>
                            </li>
                        ))}
                    </ul>
                </div>

                {selectedJourney && (
                    <div className="journey-details">
                        <div className="journey-header">
                            <h3>{selectedJourney.title}</h3>
                            <div className="journey-meta">
                                <p className="journey-started">Started: {new Date(selectedJourney.startDate).toLocaleDateString()}</p>
                                <p className="journey-current">Current Role: <strong>{selectedJourney.currentRole}</strong></p>
                            </div>
                        </div>

                        <div className="journey-background">
                            <h4>Background</h4>
                            <p>{selectedJourney.background}</p>
                        </div>

                        <div className="journey-timeline">
                            <h4>Career Timeline</h4>
                            <ul className="timeline">
                                {selectedJourney.events.map(event => (
                                    <li key={event.id} className={`timeline-item ${event.type}`}>
                                        <div className="timeline-marker"></div>
                                        <div className="timeline-content">
                                            <h5>
                                                <span className="event-date">{new Date(event.date).toLocaleDateString()}</span>
                                                {event.title}
                                            </h5>
                                            <p>{event.description}</p>
                                            <span className="event-type">{event.type}</span>
                                        </div>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default UserJourneys; 