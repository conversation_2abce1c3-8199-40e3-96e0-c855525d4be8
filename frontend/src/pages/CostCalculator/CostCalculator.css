.cost-calculator-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.calculator-intro {
  margin-bottom: 25px;
  color: #555;
  line-height: 1.6;
  max-width: 800px;
}

.calculator-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.control-group {
  flex: 1;
  min-width: 200px;
}

.control-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #495057;
}

.control-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 16px;
}

.options-group {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-label input {
  width: 18px;
  height: 18px;
}

.results-container {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cost-breakdown {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
}

.cost-item {
  flex: 1;
  min-width: 200px;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cost-info h4 {
  margin: 0 0 8px 0;
  color: #343a40;
}

.cost-info p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

.cost-value {
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
}

.cost-visualization {
  margin-top: 20px;
}

.bar-chart {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.bar-container {
  display: flex;
  align-items: center;
}

.bar-container label {
  width: 80px;
  font-weight: bold;
  color: #495057;
}

.bar {
  height: 40px;
  background-color: #007bff;
  color: white;
  display: flex;
  align-items: center;
  padding: 0 15px;
  border-radius: 4px;
  font-weight: bold;
  transition: width 0.5s ease;
  min-width: 80px;
}

.bar-full {
  background-color: #28a745;
}

.additional-info {
  margin-top: 30px;
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.additional-info h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #343a40;
}

.additional-info p {
  line-height: 1.6;
  color: #495057;
}

.cost-saving-tips {
  margin-top: 20px;
  padding: 15px;
  background-color: #e7f5ff;
  border-radius: 8px;
}

.cost-saving-tips h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #0056b3;
}

.cost-saving-tips ul {
  margin: 0;
  padding-left: 20px;
}

.cost-saving-tips li {
  margin-bottom: 8px;
  color: #495057;
}
