import React, { useEffect, useState } from 'react';
import './CostCalculator.css';

interface CertificationCost {
  name: string;
  examFee: number;
  trainingCost: number;
  studyMaterials: number;
  maintenanceYearly: number;
}

interface CalculationResult {
  totalInitialCost: number;
  threeYearCost: number;
  fiveYearCost: number;
}

const mockCertifications: CertificationCost[] = [
  {
    name: "CompTIA Security+",
    examFee: 381,
    trainingCost: 1299,
    studyMaterials: 120,
    maintenanceYearly: 50
  },
  {
    name: "CISSP",
    examFee: 749,
    trainingCost: 2995,
    studyMaterials: 200,
    maintenanceYearly: 125
  },
  {
    name: "CEH",
    examFee: 950,
    trainingCost: 1899,
    studyMaterials: 150,
    maintenanceYearly: 80
  },
  {
    name: "OSCP",
    examFee: 999,
    trainingCost: 1499,
    studyMaterials: 100,
    maintenanceYearly: 0
  },
  {
    name: "AWS Certified Security - Specialty",
    examFee: 300,
    trainingCost: 499,
    studyMaterials: 80,
    maintenanceYearly: 75
  }
];

const CostCalculator: React.FC = () => {
  const [selectedCert, setSelectedCert] = useState<string>(mockCertifications[0].name);
  const [includeTraining, setIncludeTraining] = useState<boolean>(true);
  const [includeStudyMaterials, setIncludeStudyMaterials] = useState<boolean>(true);
  const [result, setResult] = useState<CalculationResult | null>(null);
  const [currency, setCurrency] = useState<string>('USD');

  useEffect(() => {
    calculateCost();
  }, [selectedCert, includeTraining, includeStudyMaterials, currency]);

  const calculateCost = () => {
    const cert = mockCertifications.find(c => c.name === selectedCert);
    if (!cert) return;

    let initialCost = cert.examFee;

    if (includeTraining) {
      initialCost += cert.trainingCost;
    }

    if (includeStudyMaterials) {
      initialCost += cert.studyMaterials;
    }

    const threeYearCost = initialCost + (cert.maintenanceYearly * 3);
    const fiveYearCost = initialCost + (cert.maintenanceYearly * 5);

    setResult({
      totalInitialCost: initialCost,
      threeYearCost,
      fiveYearCost
    });
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  return (
    <div className="cost-calculator-container">
      <h2>Certification Cost Calculator</h2>
      <p className="calculator-intro">
        Estimate the total cost of obtaining and maintaining a security certification.
      </p>

      <div className="calculator-controls">
        <div className="control-group">
          <label>Certification</label>
          <select
            value={selectedCert}
            onChange={(e) => setSelectedCert(e.target.value)}
          >
            {mockCertifications.map(cert => (
              <option key={cert.name} value={cert.name}>{cert.name}</option>
            ))}
          </select>
        </div>

        <div className="control-group">
          <label>Currency</label>
          <select
            value={currency}
            onChange={(e) => setCurrency(e.target.value)}
          >
            <option value="USD">USD ($)</option>
            <option value="EUR">EUR (€)</option>
            <option value="GBP">GBP (£)</option>
          </select>
        </div>

        <div className="options-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={includeTraining}
              onChange={(e) => setIncludeTraining(e.target.checked)}
            />
            Include Official Training
          </label>

          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={includeStudyMaterials}
              onChange={(e) => setIncludeStudyMaterials(e.target.checked)}
            />
            Include Study Materials
          </label>
        </div>
      </div>

      {result && (
        <div className="results-container">
          <h3>Cost Breakdown</h3>

          <div className="cost-breakdown">
            <div className="cost-item">
              <div className="cost-info">
                <h4>Initial Cost</h4>
                <p>Exam fee{includeTraining ? ', training' : ''}{includeStudyMaterials ? ', study materials' : ''}</p>
              </div>
              <div className="cost-value">{formatCurrency(result.totalInitialCost)}</div>
            </div>

            <div className="cost-item">
              <div className="cost-info">
                <h4>3-Year Cost</h4>
                <p>Initial cost + 3 years of maintenance fees</p>
              </div>
              <div className="cost-value">{formatCurrency(result.threeYearCost)}</div>
            </div>

            <div className="cost-item">
              <div className="cost-info">
                <h4>5-Year Cost</h4>
                <p>Initial cost + 5 years of maintenance fees</p>
              </div>
              <div className="cost-value">{formatCurrency(result.fiveYearCost)}</div>
            </div>
          </div>

          <div className="cost-visualization">
            <div className="bar-chart">
              <div className="bar-container">
                <label>Initial</label>
                <div className="bar" style={{ width: `${(result.totalInitialCost / result.fiveYearCost) * 100}%` }}>
                  {formatCurrency(result.totalInitialCost)}
                </div>
              </div>
              <div className="bar-container">
                <label>3 Years</label>
                <div className="bar" style={{ width: `${(result.threeYearCost / result.fiveYearCost) * 100}%` }}>
                  {formatCurrency(result.threeYearCost)}
                </div>
              </div>
              <div className="bar-container">
                <label>5 Years</label>
                <div className="bar bar-full" style={{ width: '100%' }}>
                  {formatCurrency(result.fiveYearCost)}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="additional-info">
        <h3>About Certification Costs</h3>
        <p>
          The total cost of a certification includes more than just the exam fee.
          Official training courses, study materials, and ongoing maintenance fees
          all contribute to the total investment. Some certifications like OSCP are
          lifetime certifications with no renewal fees, while others require annual
          membership fees or continuing education.
        </p>
        <div className="cost-saving-tips">
          <h4>Cost-Saving Tips</h4>
          <ul>
            <li>Look for exam voucher discounts through professional organizations or during sales events</li>
            <li>Consider self-study options if you're already familiar with the material</li>
            <li>Use free or low-cost resources like YouTube tutorials and practice exams</li>
            <li>Some employers offer certification reimbursement programs</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default CostCalculator; 