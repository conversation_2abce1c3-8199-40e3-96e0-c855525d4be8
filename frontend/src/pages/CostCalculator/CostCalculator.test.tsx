import { fireEvent, render, screen, waitFor, within } from '@testing-library/react';
import React from 'react';
import { LanguageProvider } from '../../contexts/LanguageContext';
import api from '../../services/api';
import CostCalculator from './CostCalculator';

// Mock the react-chartjs-2 component to avoid chart rendering issues in tests
jest.mock('react-chartjs-2', () => ({
  Doughnut: () => <div data-testid="doughnut-chart">Doughnut Chart</div>
}));

// Mock the API
jest.mock('../../services/api', () => ({
  certifications: {
    getAll: jest.fn(),
  },
  costs: {
    getExchangeRates: jest.fn(),
  }
}));

// Mock data
const mockCertifications = [
  {
    id: 1,
    name: 'Certified Ethical Hacker (CEH)',
    cost: 1200,
    level: 'Intermediate',
    domain: 'Security',
  },
  {
    id: 2,
    name: 'CompTIA Security+',
    cost: 350,
    level: 'Entry',
    domain: 'Security',
  },
  {
    id: 3,
    name: 'CISSP',
    cost: 700,
    level: 'Advanced',
    domain: 'Security',
  },
];

const mockExchangeRates = {
  base: 'USD',
  rates: {
    'USD': 1,
    'EUR': 0.92,
    'GBP': 0.78,
    'JPY': 145.5,
    'CAD': 1.35,
  }
};

// Helper to render with providers
const renderWithProviders = (ui: React.ReactElement) => {
  return render(
    <LanguageProvider>
      {ui}
    </LanguageProvider>
  );
};

describe('CostCalculator Component', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Setup default mock implementations
    (api.certifications.getAll as jest.Mock).mockResolvedValue(mockCertifications);
    (api.costs.getExchangeRates as jest.Mock).mockResolvedValue(mockExchangeRates);
    
    // Mock the formatCurrency functionality since we're testing in an environment where Intl might not exist
    Object.defineProperty(global, 'Intl', {
      value: {
        NumberFormat: jest.fn().mockImplementation(() => ({
          format: (number: number) => `$${number.toFixed(2)}`
        }))
      },
      writable: true
    });
  });

  test('renders loading state initially', async () => {
    renderWithProviders(<CostCalculator />);
    
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
    
    await waitFor(() => {
      expect(api.certifications.getAll).toHaveBeenCalled();
    });
  });

  test('renders certification options after loading', async () => {
    renderWithProviders(<CostCalculator />);
    
    await waitFor(() => {
      expect(api.certifications.getAll).toHaveBeenCalled();
    });
    
    const certSelect = screen.getByLabelText(/select certifications/i);
    expect(certSelect).toBeInTheDocument();
    
    const options = within(certSelect).getAllByRole('option');
    expect(options).toHaveLength(3);
    expect(options[0]).toHaveTextContent('Certified Ethical Hacker (CEH)');
    expect(options[1]).toHaveTextContent('CompTIA Security+');
    expect(options[2]).toHaveTextContent('CISSP');
  });

  test('loads exchange rates and shows currency options', async () => {
    renderWithProviders(<CostCalculator />);
    
    await waitFor(() => {
      expect(api.costs.getExchangeRates).toHaveBeenCalled();
    });
    
    const currencySelect = screen.getByLabelText(/select currency/i);
    expect(currencySelect).toBeInTheDocument();
    
    const options = within(currencySelect).getAllByRole('option');
    expect(options).toHaveLength(5);
    expect(options[0]).toHaveTextContent('USD');
    expect(options[1]).toHaveTextContent('EUR');
  });

  test('allows setting study materials cost', async () => {
    renderWithProviders(<CostCalculator />);
    
    await waitFor(() => {
      expect(api.certifications.getAll).toHaveBeenCalled();
    });
    
    const materialsInput = screen.getByLabelText(/study materials cost/i);
    expect(materialsInput).toHaveValue(100); // Default value
    
    fireEvent.change(materialsInput, { target: { value: 200 } });
    expect(materialsInput).toHaveValue(200);
  });

  test('allows configuring retake policy settings', async () => {
    renderWithProviders(<CostCalculator />);
    
    await waitFor(() => {
      expect(api.certifications.getAll).toHaveBeenCalled();
    });
    
    // Test waiting period slider
    const waitingPeriodSlider = screen.getByLabelText(/minimum days between attempts/i);
    expect(waitingPeriodSlider).toBeInTheDocument();
    fireEvent.change(waitingPeriodSlider, { target: { value: 30 } });
    expect(screen.getByText(/30/)).toBeInTheDocument();
    
    // Test discount slider
    const discountSlider = screen.getByLabelText(/retake discount/i);
    expect(discountSlider).toBeInTheDocument();
    fireEvent.change(discountSlider, { target: { value: 25 } });
    expect(screen.getByText(/25%/)).toBeInTheDocument();
    
    // Test max attempts input
    const maxAttemptsInput = screen.getByLabelText(/maximum attempts considered/i);
    expect(maxAttemptsInput).toHaveValue(2); // Default value
    fireEvent.change(maxAttemptsInput, { target: { value: 3 } });
    expect(maxAttemptsInput).toHaveValue(3);
  });

  test('shows empty state when no certifications selected', async () => {
    renderWithProviders(<CostCalculator />);
    
    await waitFor(() => {
      expect(api.certifications.getAll).toHaveBeenCalled();
    });
    
    expect(screen.getByText(/select certifications to see cost breakdown/i)).toBeInTheDocument();
    expect(screen.queryByTestId('doughnut-chart')).not.toBeInTheDocument();
  });

  test('selects certification and shows cost breakdown', async () => {
    renderWithProviders(<CostCalculator />);
    
    await waitFor(() => {
      expect(api.certifications.getAll).toHaveBeenCalled();
    });
    
    // Select a certification
    const certSelect = screen.getByLabelText(/select certifications/i) as HTMLSelectElement;
    const option = within(certSelect).getByText(/certified ethical hacker/i);
    
    // Simulate selecting the option (need to mock the HTML multi-select behavior)
    fireEvent.change(certSelect, { target: { value: '1' } });
    Object.defineProperty(certSelect, 'selectedOptions', {
      get: () => [option],
      configurable: true
    });
    
    fireEvent.change(certSelect);
    
    // Now the cost breakdown should appear
    await waitFor(() => {
      expect(screen.getByText(/cost distribution/i)).toBeInTheDocument();
      expect(screen.getByText(/cost breakdown/i)).toBeInTheDocument();
      expect(screen.getByText(/selected certifications/i)).toBeInTheDocument();
    });
    
    // Check for metric values
    expect(screen.getByText(/base exam fees/i)).toBeInTheDocument();
    expect(screen.getByText(/study materials/i)).toBeInTheDocument();
    expect(screen.getByText(/potential retake costs/i)).toBeInTheDocument();
    expect(screen.getByText(/total investment/i)).toBeInTheDocument();
    
    // The doughnut chart should be rendered
    expect(screen.getByTestId('doughnut-chart')).toBeInTheDocument();
  });

  test('changes currency and updates displayed values', async () => {
    renderWithProviders(<CostCalculator />);
    
    await waitFor(() => {
      expect(api.costs.getExchangeRates).toHaveBeenCalled();
    });
    
    // Select a certification first
    const certSelect = screen.getByLabelText(/select certifications/i) as HTMLSelectElement;
    fireEvent.change(certSelect, { target: { value: '1' } });
    
    // Mock the selection
    Object.defineProperty(certSelect, 'selectedOptions', {
      get: () => [certSelect.options[0]],
      configurable: true
    });
    
    fireEvent.change(certSelect);
    
    // Change currency to EUR
    const currencySelect = screen.getByLabelText(/select currency/i);
    fireEvent.change(currencySelect, { target: { value: 'EUR' } });
    
    // Now values should be converted to EUR
    // With our mock implementation, the actual values won't change correctly,
    // but we can at least verify the currency selection worked
    expect(currencySelect).toHaveValue('EUR');
  });

  test('shows certification details when certifications are selected', async () => {
    renderWithProviders(<CostCalculator />);
    
    await waitFor(() => {
      expect(api.certifications.getAll).toHaveBeenCalled();
    });
    
    // Select a certification
    const certSelect = screen.getByLabelText(/select certifications/i) as HTMLSelectElement;
    fireEvent.change(certSelect, { target: { value: '1' } });
    
    // Mock the selection
    Object.defineProperty(certSelect, 'selectedOptions', {
      get: () => [certSelect.options[0]],
      configurable: true
    });
    
    fireEvent.change(certSelect);
    
    // Certification details should appear
    await waitFor(() => {
      expect(screen.getByText('Certified Ethical Hacker (CEH)')).toBeInTheDocument();
    });
    
    // Expand the details
    fireEvent.click(screen.getByText('Certified Ethical Hacker (CEH)'));
    
    // Details should now be visible
    expect(screen.getByText(/base cost/i)).toBeInTheDocument();
    expect(screen.getByText(/level/i)).toBeInTheDocument();
    expect(screen.getByText(/domain/i)).toBeInTheDocument();
    expect(screen.getByText('Intermediate')).toBeInTheDocument();
    expect(screen.getByText('Security')).toBeInTheDocument();
    
    // Retake cost analysis should be shown
    expect(screen.getByText(/retake cost analysis/i)).toBeInTheDocument();
  });

  test('handles API errors gracefully', async () => {
    // Mock API error
    (api.certifications.getAll as jest.Mock).mockRejectedValue(new Error('Network error'));
    
    renderWithProviders(<CostCalculator />);
    
    // Error message should be displayed
    await waitFor(() => {
      expect(screen.getByText(/failed to load certifications/i)).toBeInTheDocument();
    });
  });

  test('calculates retake costs correctly', async () => {
    renderWithProviders(<CostCalculator />);
    
    await waitFor(() => {
      expect(api.certifications.getAll).toHaveBeenCalled();
    });
    
    // Set up test scenario: select a cert, set 0% discount, and 3 max attempts
    const certSelect = screen.getByLabelText(/select certifications/i) as HTMLSelectElement;
    fireEvent.change(certSelect, { target: { value: '1' } });
    
    // Mock the selection (CEH with $1200 cost)
    Object.defineProperty(certSelect, 'selectedOptions', {
      get: () => [certSelect.options[0]],
      configurable: true
    });
    
    fireEvent.change(certSelect);
    
    // Set 3 max attempts (default is 2)
    const maxAttemptsInput = screen.getByLabelText(/maximum attempts considered/i);
    fireEvent.change(maxAttemptsInput, { target: { value: 3 } });
    
    // Expand certification details to see retake analysis
    await waitFor(() => {
      expect(screen.getByText('Certified Ethical Hacker (CEH)')).toBeInTheDocument();
    });
    
    fireEvent.click(screen.getByText('Certified Ethical Hacker (CEH)'));
    
    // With 0% discount and 3 max attempts, we should see 2 retake fees of $1200 each
    expect(screen.getByText(/attempt 2/i)).toBeInTheDocument();
    expect(screen.getByText(/attempt 3/i)).toBeInTheDocument();
  });
}); 