import React, { useEffect, useState } from 'react';
import './FAQ.css';

interface FAQItem {
    id: number;
    question: string;
    answer: string;
    category: string;
}

const mockFAQs: FAQItem[] = [
    {
        id: 1,
        question: "What is the difference between Security+ and CySA+?",
        answer: "Security+ is an entry-level certification that covers fundamental cybersecurity concepts like network security, threats, and vulnerabilities. CySA+ (Cybersecurity Analyst) is more advanced and focuses on security analytics, intrusion detection, and incident response. Security+ is generally recommended for beginners, while CySA+ is ideal for those with some experience looking to move into security analysis roles.",
        category: "certifications"
    },
    {
        id: 2,
        question: "How long does it typically take to prepare for the CISSP exam?",
        answer: "The preparation time for CISSP varies depending on your experience and background. Most candidates study for 3-6 months, dedicating 10-15 hours per week. The CISSP exam covers 8 domains and requires both broad and deep knowledge. Additionally, you need a minimum of 5 years of professional experience in at least 2 of the 8 domains to earn the full certification.",
        category: "certifications"
    },
    {
        id: 3,
        question: "What study materials are recommended for the OSCP certification?",
        answer: "For OSCP preparation, the course materials provided by Offensive Security are essential as the exam is based on them. Additionally, practicing on platforms like Hack The Box and TryHackMe is highly recommended. Creating a methodical approach to penetration testing, taking detailed notes, and developing custom scripts will help during the 24-hour exam. The OSCP is a hands-on certification, so practical experience is crucial.",
        category: "study"
    },
    {
        id: 4,
        question: "How do I track my study hours and progress?",
        answer: "Our platform offers a Study Time Tracker feature that allows you to log your study sessions, track progress through practice exams, and set goals. You can categorize your study by certification or topic, view analytics on your study patterns, and receive recommendations for optimizing your preparation. Many successful candidates report that consistent, scheduled study sessions are more effective than irregular cramming.",
        category: "study"
    },
    {
        id: 5,
        question: "What is the average ROI for cybersecurity certifications?",
        answer: "The return on investment for cybersecurity certifications varies by certification and career stage. On average, professionals see a 15-30% salary increase after earning advanced certifications like CISSP or OSCP. Entry-level certifications like Security+ typically yield a 5-15% increase. Beyond monetary returns, certifications open doors to specialized roles and advancement opportunities. Use our Cost Calculator tool to estimate the ROI for specific certifications based on your situation.",
        category: "cost"
    },
    {
        id: 6,
        question: "Are certification exam fees tax-deductible?",
        answer: "In many countries, certification exam fees, study materials, and related education expenses may be tax-deductible if they relate to maintaining or improving skills needed in your current job. However, if you're pursuing certifications to change careers, these might not qualify. We recommend consulting with a tax professional as tax laws vary by location and individual circumstances.",
        category: "cost"
    }
];

const FAQ: React.FC = () => {
    const [faqs, setFaqs] = useState<FAQItem[]>([]);
    const [expandedId, setExpandedId] = useState<number | null>(null);
    const [activeCategory, setActiveCategory] = useState<string>('all');
    const [isLoading, setIsLoading] = useState<boolean>(true);

    useEffect(() => {
        // Simulate API call to fetch FAQs
        const fetchFAQs = async () => {
            try {
                // In a real app, this would be an API call
                // const response = await fetch('/api/v1/faqs');
                // const data = await response.json();

                // Using mock data for now
                setTimeout(() => {
                    setFaqs(mockFAQs);
                    setIsLoading(false);
                }, 800);
            } catch (err) {
                console.error("Failed to load FAQs:", err);
                setIsLoading(false);
            }
        };

        fetchFAQs();
    }, []);

    const toggleFAQ = (id: number) => {
        setExpandedId(expandedId === id ? null : id);
    };

    const filterByCategory = (category: string) => {
        setActiveCategory(category);
    };

    const getFilteredFAQs = () => {
        if (activeCategory === 'all') {
            return faqs;
        }
        return faqs.filter(faq => faq.category === activeCategory);
    };

    const getCategoryIcon = (category: string) => {
        switch (category) {
            case 'certifications':
                return <span className="category-icon certifications">🏆</span>;
            case 'study':
                return <span className="category-icon study">📚</span>;
            case 'cost':
                return <span className="category-icon cost">💲</span>;
            default:
                return <span className="category-icon">❓</span>;
        }
    };

    if (isLoading) {
        return <div className="faq-loading">Loading FAQs...</div>;
    }

    return (
        <div className="faq-container">
            <h2>Frequently Asked Questions</h2>

            <div className="faq-category-filter">
                <button
                    className={`category-btn ${activeCategory === 'all' ? 'active' : ''}`}
                    onClick={() => filterByCategory('all')}
                >
                    All
                </button>
                <button
                    className={`category-btn ${activeCategory === 'certifications' ? 'active' : ''}`}
                    onClick={() => filterByCategory('certifications')}
                >
                    Certifications
                </button>
                <button
                    className={`category-btn ${activeCategory === 'study' ? 'active' : ''}`}
                    onClick={() => filterByCategory('study')}
                >
                    Study Tips
                </button>
                <button
                    className={`category-btn ${activeCategory === 'cost' ? 'active' : ''}`}
                    onClick={() => filterByCategory('cost')}
                >
                    Cost & ROI
                </button>
            </div>

            <div className="faq-list">
                {getFilteredFAQs().length === 0 ? (
                    <div className="no-faqs">No FAQs found in this category.</div>
                ) : (
                    getFilteredFAQs().map(faq => (
                        <div
                            key={faq.id}
                            className={`faq-item ${expandedId === faq.id ? 'expanded' : ''}`}
                        >
                            <div
                                className="faq-question"
                                onClick={() => toggleFAQ(faq.id)}
                            >
                                <div className="question-content">
                                    {getCategoryIcon(faq.category)}
                                    <h3>{faq.question}</h3>
                                </div>
                                <span className={`expand-icon ${expandedId === faq.id ? 'rotated' : ''}`}>
                                    ▼
                                </span>
                            </div>
                            <div className="faq-answer">
                                <p>{faq.answer}</p>
                            </div>
                        </div>
                    ))
                )}
            </div>

            <div className="faq-contact">
                <h3>Can't find an answer?</h3>
                <p>If you couldn't find the information you're looking for, please contact our support team.</p>
                <button className="contact-btn">Contact Support</button>
            </div>
        </div>
    );
};

export default FAQ; 