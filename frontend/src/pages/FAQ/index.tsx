import axios from 'axios';
import React, { useEffect, useState } from 'react';
import './styles.css';

interface FAQItem {
    id: number;
    question: string;
    answer: string;
    category: string;
    order: number;
}

interface FAQCategory {
    name: string;
    items: FAQItem[];
}

const FAQ: React.FC = () => {
    const [faqItems, setFaqItems] = useState<FAQItem[]>([]);
    const [categories, setCategories] = useState<FAQCategory[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());
    const [activeCategory, setActiveCategory] = useState<string>('all');

    useEffect(() => {
        const fetchFAQs = async () => {
            try {
                setLoading(true);
                const response = await axios.get('/api/faqs');
                setFaqItems(response.data);

                // Extract categories and group items
                processFaqItems(response.data);

                setError(null);
            } catch (err) {
                console.error('Error fetching FAQs:', err);
                setError('Failed to load FAQ items. Please try again later.');

                // Mock data for development
                const mockFaqs = [
                    {
                        id: 1,
                        question: 'What is CertRats?',
                        answer: 'CertRats is a comprehensive platform for IT certification tracking, study planning, and career pathing. We help professionals in the IT industry plan and track their certification journey.',
                        category: 'general',
                        order: 1
                    },
                    {
                        id: 2,
                        question: 'How do I track my study time?',
                        answer: 'You can use our Study Time Tracker feature to log your daily study sessions. Navigate to the Study Time Tracker in the main menu, set your certification goal, and start tracking your progress.',
                        category: 'features',
                        order: 1
                    },
                    {
                        id: 3,
                        question: 'Can I calculate the ROI of a certification?',
                        answer: 'Yes! Our Cost Calculator feature helps you estimate the return on investment for different certifications based on exam costs, study materials, and potential salary increases.',
                        category: 'features',
                        order: 2
                    },
                    {
                        id: 4,
                        question: 'How do I get started?',
                        answer: 'Create an account, set up your profile with your current certifications and goals, and start using our tools like Study Time Tracker and Cost Calculator to plan your certification journey.',
                        category: 'general',
                        order: 2
                    },
                    {
                        id: 5,
                        question: 'Is my data secure?',
                        answer: 'Yes, we take data security very seriously. All your personal information and study data are encrypted and stored securely. We never share your information with third parties without your consent.',
                        category: 'security',
                        order: 1
                    },
                    {
                        id: 6,
                        question: 'Can I visualize my career path?',
                        answer: 'Our Career Path Visualization tool helps you map out possible career trajectories based on your current skills and certifications. It provides a visual roadmap of potential paths and the certifications needed.',
                        category: 'features',
                        order: 3
                    }
                ];

                setFaqItems(mockFaqs);
                processFaqItems(mockFaqs);
            } finally {
                setLoading(false);
            }
        };

        fetchFAQs();
    }, []);

    const processFaqItems = (items: FAQItem[]) => {
        // Extract unique categories
        const uniqueCategories = Array.from(
            new Set(items.map(item => item.category))
        );

        // Group items by category
        const groupedCategories: FAQCategory[] = uniqueCategories.map(category => ({
            name: category,
            items: items
                .filter(item => item.category === category)
                .sort((a, b) => a.order - b.order)
        }));

        setCategories(groupedCategories);
    };

    const toggleItem = (id: number) => {
        const newExpandedItems = new Set(expandedItems);
        if (newExpandedItems.has(id)) {
            newExpandedItems.delete(id);
        } else {
            newExpandedItems.add(id);
        }
        setExpandedItems(newExpandedItems);
    };

    const filteredItems = faqItems.filter(item => {
        const matchesSearch = searchTerm === '' ||
            item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.answer.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesCategory = activeCategory === 'all' || item.category === activeCategory;

        return matchesSearch && matchesCategory;
    });

    const expandAll = () => {
        const allIds = new Set(filteredItems.map(item => item.id));
        setExpandedItems(allIds);
    };

    const collapseAll = () => {
        setExpandedItems(new Set());
    };

    const formatCategoryName = (name: string): string => {
        return name.charAt(0).toUpperCase() + name.slice(1);
    };

    return (
        <div className="faq-container">
            <h1>Frequently Asked Questions</h1>

            <div className="faq-search">
                <input
                    type="text"
                    placeholder="Search FAQs..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                />
            </div>

            <div className="faq-controls">
                <div className="faq-categories">
                    <button
                        className={`category-btn ${activeCategory === 'all' ? 'active' : ''}`}
                        onClick={() => setActiveCategory('all')}
                    >
                        All
                    </button>

                    {categories.map(category => (
                        <button
                            key={category.name}
                            className={`category-btn ${activeCategory === category.name ? 'active' : ''}`}
                            onClick={() => setActiveCategory(category.name)}
                        >
                            {formatCategoryName(category.name)}
                        </button>
                    ))}
                </div>

                <div className="faq-actions">
                    <button className="expand-all-btn" onClick={expandAll}>Expand All</button>
                    <button className="collapse-all-btn" onClick={collapseAll}>Collapse All</button>
                </div>
            </div>

            {loading ? (
                <div className="loading-container">
                    <div className="loading-spinner"></div>
                    <p>Loading FAQs...</p>
                </div>
            ) : error ? (
                <div className="error-container">
                    <p className="error-message">{error}</p>
                    <button onClick={() => window.location.reload()}>Try Again</button>
                </div>
            ) : filteredItems.length === 0 ? (
                <div className="no-results">
                    <p>No FAQ items found matching your search criteria.</p>
                    <button onClick={() => {
                        setSearchTerm('');
                        setActiveCategory('all');
                    }}>Clear Filters</button>
                </div>
            ) : (
                <div className="faq-accordion">
                    {filteredItems.map(item => (
                        <div key={item.id} className="faq-item">
                            <div
                                className={`faq-question ${expandedItems.has(item.id) ? 'expanded' : ''}`}
                                onClick={() => toggleItem(item.id)}
                            >
                                <h3>{item.question}</h3>
                                <span className="toggle-icon">
                                    {expandedItems.has(item.id) ? '−' : '+'}
                                </span>
                            </div>

                            {expandedItems.has(item.id) && (
                                <div className="faq-answer">
                                    <p>{item.answer}</p>
                                    <span className="faq-category-tag">{formatCategoryName(item.category)}</span>
                                </div>
                            )}
                        </div>
                    ))}
                </div>
            )}

            <div className="faq-feedback">
                <h3>Can't find what you're looking for?</h3>
                <p>If you have a question that's not answered here, feel free to contact our support team.</p>
                <button className="contact-support-btn">Contact Support</button>
            </div>
        </div>
    );
};

export default FAQ; 