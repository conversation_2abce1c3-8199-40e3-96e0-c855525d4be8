.faq-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem;
  font-family: 'Roboto', sans-serif;
}

.faq-container h1 {
  text-align: center;
  margin-bottom: 2rem;
  color: #333;
  font-size: 2.5rem;
}

/* Search box */
.faq-search {
  margin-bottom: 2rem;
}

.faq-search input {
  width: 100%;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.faq-search input:focus {
  border-color: #4a6cf7;
  box-shadow: 0 2px 8px rgba(74, 108, 247, 0.2);
  outline: none;
}

/* Controls section */
.faq-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.faq-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.category-btn {
  padding: 0.5rem 1rem;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
  color: #555;
}

.category-btn:hover {
  background-color: #e9e9e9;
}

.category-btn.active {
  background-color: #4a6cf7;
  color: white;
  border-color: #4a6cf7;
}

.faq-actions {
  display: flex;
  gap: 0.5rem;
}

.expand-all-btn,
.collapse-all-btn {
  padding: 0.5rem 1rem;
  background-color: transparent;
  border: 1px solid #4a6cf7;
  border-radius: 4px;
  color: #4a6cf7;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.expand-all-btn:hover,
.collapse-all-btn:hover {
  background-color: #4a6cf7;
  color: white;
}

/* Loading state */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #4a6cf7;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Error state */
.error-container {
  text-align: center;
  padding: 2rem;
  background-color: #fff8f8;
  border: 1px solid #ffefef;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.error-message {
  color: #e53935;
  margin-bottom: 1rem;
}

.error-container button {
  padding: 0.5rem 1rem;
  background-color: #e53935;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.error-container button:hover {
  background-color: #c62828;
}

/* No results state */
.no-results {
  text-align: center;
  padding: 2rem;
  background-color: #f9f9f9;
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.no-results button {
  padding: 0.5rem 1rem;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 1rem;
  transition: background-color 0.2s;
}

.no-results button:hover {
  background-color: #3a5ce5;
}

/* FAQ Accordion */
.faq-accordion {
  margin-bottom: 3rem;
}

.faq-item {
  margin-bottom: 1rem;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.faq-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.2rem;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: background-color 0.2s;
}

.faq-question:hover {
  background-color: #f5f5f5;
}

.faq-question.expanded {
  background-color: #4a6cf7;
  color: white;
}

.faq-question h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.toggle-icon {
  font-size: 1.5rem;
  font-weight: bold;
  transition: transform 0.3s;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
}

.faq-question.expanded .toggle-icon {
  transform: rotate(180deg);
}

.faq-answer {
  padding: 1.5rem;
  background-color: white;
  border-top: 1px solid #eee;
  line-height: 1.6;
}

.faq-answer p {
  margin: 0 0 1rem 0;
  color: #555;
}

.faq-category-tag {
  display: inline-block;
  padding: 0.3rem 0.8rem;
  background-color: #f1f5ff;
  color: #4a6cf7;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Feedback section */
.faq-feedback {
  text-align: center;
  padding: 2rem;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #eee;
}

.faq-feedback h3 {
  margin-top: 0;
  color: #333;
}

.faq-feedback p {
  color: #666;
  margin-bottom: 1.5rem;
}

.contact-support-btn {
  padding: 0.8rem 1.5rem;
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.contact-support-btn:hover {
  background-color: #3a5ce5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .faq-container {
    padding: 1.5rem;
  }
  
  .faq-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .faq-categories {
    margin-bottom: 1rem;
  }
  
  .faq-actions {
    justify-content: center;
  }
  
  .faq-question h3 {
    font-size: 1rem;
  }
} 