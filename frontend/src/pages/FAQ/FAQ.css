.faq-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.faq-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 18px;
  color: #6c757d;
}

.faq-category-filter {
  display: flex;
  gap: 10px;
  margin-bottom: 25px;
  flex-wrap: wrap;
}

.category-btn {
  padding: 10px 15px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.category-btn:hover {
  background-color: #e9ecef;
}

.category-btn.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.faq-list {
  margin-bottom: 40px;
}

.faq-item {
  margin-bottom: 15px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
}

.faq-question {
  padding: 15px 20px;
  background-color: #f8f9fa;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s ease;
}

.faq-question:hover {
  background-color: #e9ecef;
}

.question-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.question-content h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #343a40;
}

.category-icon {
  font-size: 20px;
  display: inline-block;
  width: 24px;
  height: 24px;
  text-align: center;
}

.category-icon.certifications {
  color: #007bff;
}

.category-icon.study {
  color: #28a745;
}

.category-icon.cost {
  color: #fd7e14;
}

.expand-icon {
  transition: transform 0.3s ease;
  display: inline-block;
  font-size: 12px;
}

.expand-icon.rotated {
  transform: rotate(180deg);
}

.faq-answer {
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  background-color: white;
}

.faq-item.expanded .faq-answer {
  padding: 20px;
  max-height: 1000px; /* Large enough to contain content */
}

.faq-answer p {
  margin: 0;
  line-height: 1.6;
  color: #495057;
}

.no-faqs {
  padding: 30px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
}

.faq-contact {
  background-color: #f8f9fa;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
}

.faq-contact h3 {
  margin-top: 0;
  color: #343a40;
}

.faq-contact p {
  margin-bottom: 20px;
  color: #6c757d;
}

.contact-btn {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.2s;
}

.contact-btn:hover {
  background-color: #0069d9;
}

@media (max-width: 600px) {
  .faq-container {
    padding: 1rem 0.5rem;
  }
  
  .faq-accordion .MuiAccordionDetails-root {
    padding: 12px 16px 16px;
  }
}

/* Dark theme overrides for FAQ */
[data-theme='dark'] .faq-container {
  color: var(--color-text);
}

[data-theme='dark'] .category-btn {
  background-color: #1a1a2e;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

[data-theme='dark'] .category-btn:hover {
  background-color: #222342;
}

[data-theme='dark'] .category-btn.active {
  background-color: var(--color-primary);
  color: #000;
  border-color: var(--color-primary-dark);
}

[data-theme='dark'] .faq-item {
  border: 1px solid var(--color-border);
  background-color: var(--color-card-background);
}

[data-theme='dark'] .faq-question {
  background-color: #1a1a2e;
  color: var(--color-text);
}

[data-theme='dark'] .faq-question:hover {
  background-color: #222342;
}

[data-theme='dark'] .question-content h3 {
  color: var(--color-text);
}

[data-theme='dark'] .category-icon.certifications {
  color: #6bddbe;
}

[data-theme='dark'] .category-icon.study {
  color: #7bdba8;
}

[data-theme='dark'] .category-icon.cost {
  color: #6bddbe;
}

[data-theme='dark'] .faq-answer {
  background-color: var(--color-card-background);
}

[data-theme='dark'] .faq-answer p {
  color: var(--color-text);
}

[data-theme='dark'] .no-faqs {
  background-color: var(--color-card-background);
  color: var(--color-text-muted);
  border: 1px solid var(--color-border);
}

[data-theme='dark'] .faq-contact {
  background-color: var(--color-card-background);
  border: 1px solid var(--color-border);
}

[data-theme='dark'] .faq-contact h3 {
  color: var(--color-primary);
}

[data-theme='dark'] .faq-contact p {
  color: var(--color-text);
}

[data-theme='dark'] .contact-btn {
  background-color: var(--color-primary);
  color: #000;
}

[data-theme='dark'] .contact-btn:hover {
  background-color: var(--color-primary-dark);
}

[data-theme='dark'] .faq-loading {
  color: var(--color-text-muted);
} 