import { fireEvent, render, screen } from '@testing-library/react';
import FAQ from './FAQ';

describe('FAQ Component', () => {
    test('renders FAQ page with title', () => {
        render(<FAQ />);

        // Check if the title is rendered
        const titleElement = screen.getByText(/Frequently Asked Questions/i);
        expect(titleElement).toBeInTheDocument();
    });

    test('renders CPE section accordion', () => {
        render(<FAQ />);

        // Check if CPE section is rendered
        const cpeElement = screen.getByText(/What is CPE?/i);
        expect(cpeElement).toBeInTheDocument();
    });

    test('expands and collapses accordion on click', () => {
        render(<FAQ />);

        // Find the renewal section (which should be collapsed initially)
        const renewalAccordion = screen.getByText(/Certification Renewal Process/i);

        // Check if it's in the document but not expanded
        expect(renewalAccordion).toBeInTheDocument();
        expect(screen.queryByText(/How to Renew Your Certification/i)).not.toBeInTheDocument();

        // Click to expand
        fireEvent.click(renewalAccordion);

        // Now the content should be visible
        expect(screen.getByText(/How to Renew Your Certification/i)).toBeInTheDocument();
        expect(screen.getByText(/Typical Renewal Requirements:/i)).toBeInTheDocument();

        // Click again to collapse
        fireEvent.click(renewalAccordion);

        // Content should be hidden again (this might be tricky to test with MUI accordions)
        // We'll check for a specific element that should only be visible when expanded
        const renewalTipsElement = screen.queryByText(/Tips for Successful Renewal:/i);
        expect(renewalTipsElement).not.toBeVisible();
    });

    test('displays breadcrumb navigation', () => {
        render(<FAQ />);

        // Check breadcrumb elements
        const homeBreadcrumb = screen.getByText('Home');
        const faqBreadcrumb = screen.getByText('FAQ');

        expect(homeBreadcrumb).toBeInTheDocument();
        expect(faqBreadcrumb).toBeInTheDocument();
    });
}); 