import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import axios from 'axios';
import FAQ from './index';

// Mock axios
jest.mock('axios');
const mockAxios = axios as jest.Mocked<typeof axios>;

describe('FAQ Component', () => {
    // Mock data for testing
    const mockFaqs = [
        {
            id: 1,
            question: 'What is CertRats?',
            answer: 'CertRats is a comprehensive platform for IT certification tracking.',
            category: 'general',
            order: 1
        },
        {
            id: 2,
            question: 'How do I track my study time?',
            answer: 'You can use our Study Time Tracker feature.',
            category: 'features',
            order: 1
        },
        {
            id: 3,
            question: 'Is my data secure?',
            answer: 'Yes, we take data security very seriously.',
            category: 'security',
            order: 1
        }
    ];

    beforeEach(() => {
        mockAxios.get.mockImplementation(() => {
            return Promise.resolve({ data: mockFaqs });
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('renders FAQ component with loading state', () => {
        render(<FAQ />);
        expect(screen.getByText(/Loading FAQs/i)).toBeInTheDocument();
    });

    test('renders FAQ items after loading', async () => {
        render(<FAQ />);

        // Wait for the component to load data
        await waitFor(() => {
            expect(screen.getByText('What is CertRats?')).toBeInTheDocument();
        });

        // Check if all questions are rendered
        expect(screen.getByText('What is CertRats?')).toBeInTheDocument();
        expect(screen.getByText('How do I track my study time?')).toBeInTheDocument();
        expect(screen.getByText('Is my data secure?')).toBeInTheDocument();
    });

    test('expands and collapses FAQ items when clicked', async () => {
        render(<FAQ />);

        // Wait for the component to load data
        await waitFor(() => {
            expect(screen.getByText('What is CertRats?')).toBeInTheDocument();
        });

        // Initially, answers should not be visible
        expect(screen.queryByText('CertRats is a comprehensive platform for IT certification tracking.')).not.toBeInTheDocument();

        // Click on the first question to expand it
        fireEvent.click(screen.getByText('What is CertRats?'));

        // Now the answer should be visible
        expect(screen.getByText('CertRats is a comprehensive platform for IT certification tracking.')).toBeInTheDocument();

        // Click again to collapse
        fireEvent.click(screen.getByText('What is CertRats?'));

        // Answer should no longer be visible
        await waitFor(() => {
            expect(screen.queryByText('CertRats is a comprehensive platform for IT certification tracking.')).not.toBeInTheDocument();
        });
    });

    test('filters FAQ items by search term', async () => {
        render(<FAQ />);

        // Wait for the component to load data
        await waitFor(() => {
            expect(screen.getByText('What is CertRats?')).toBeInTheDocument();
        });

        // Enter search term
        fireEvent.change(screen.getByPlaceholderText('Search FAQs...'), { target: { value: 'secure' } });

        // Only matching FAQs should be visible
        expect(screen.queryByText('What is CertRats?')).not.toBeInTheDocument();
        expect(screen.queryByText('How do I track my study time?')).not.toBeInTheDocument();
        expect(screen.getByText('Is my data secure?')).toBeInTheDocument();
    });

    test('filters FAQ items by category', async () => {
        render(<FAQ />);

        // Wait for the component to load data
        await waitFor(() => {
            expect(screen.getByText('What is CertRats?')).toBeInTheDocument();
        });

        // All categories should be available
        expect(screen.getByText('All')).toBeInTheDocument();
        expect(screen.getByText('General')).toBeInTheDocument();
        expect(screen.getByText('Features')).toBeInTheDocument();
        expect(screen.getByText('Security')).toBeInTheDocument();

        // Click on the "Features" category
        fireEvent.click(screen.getByText('Features'));

        // Only feature-related FAQs should be visible
        expect(screen.queryByText('What is CertRats?')).not.toBeInTheDocument();
        expect(screen.getByText('How do I track my study time?')).toBeInTheDocument();
        expect(screen.queryByText('Is my data secure?')).not.toBeInTheDocument();
    });

    test('expands all FAQ items when "Expand All" is clicked', async () => {
        render(<FAQ />);

        // Wait for the component to load data
        await waitFor(() => {
            expect(screen.getByText('What is CertRats?')).toBeInTheDocument();
        });

        // Initially, answers should not be visible
        expect(screen.queryByText('CertRats is a comprehensive platform for IT certification tracking.')).not.toBeInTheDocument();
        expect(screen.queryByText('You can use our Study Time Tracker feature.')).not.toBeInTheDocument();
        expect(screen.queryByText('Yes, we take data security very seriously.')).not.toBeInTheDocument();

        // Click on "Expand All"
        fireEvent.click(screen.getByText('Expand All'));

        // All answers should now be visible
        expect(screen.getByText('CertRats is a comprehensive platform for IT certification tracking.')).toBeInTheDocument();
        expect(screen.getByText('You can use our Study Time Tracker feature.')).toBeInTheDocument();
        expect(screen.getByText('Yes, we take data security very seriously.')).toBeInTheDocument();
    });

    test('collapses all FAQ items when "Collapse All" is clicked', async () => {
        render(<FAQ />);

        // Wait for the component to load data
        await waitFor(() => {
            expect(screen.getByText('What is CertRats?')).toBeInTheDocument();
        });

        // First expand all items
        fireEvent.click(screen.getByText('Expand All'));

        // Verify all answers are visible
        expect(screen.getByText('CertRats is a comprehensive platform for IT certification tracking.')).toBeInTheDocument();
        expect(screen.getByText('You can use our Study Time Tracker feature.')).toBeInTheDocument();
        expect(screen.getByText('Yes, we take data security very seriously.')).toBeInTheDocument();

        // Now collapse all
        fireEvent.click(screen.getByText('Collapse All'));

        // No answers should be visible
        await waitFor(() => {
            expect(screen.queryByText('CertRats is a comprehensive platform for IT certification tracking.')).not.toBeInTheDocument();
            expect(screen.queryByText('You can use our Study Time Tracker feature.')).not.toBeInTheDocument();
            expect(screen.queryByText('Yes, we take data security very seriously.')).not.toBeInTheDocument();
        });
    });

    test('handles API error gracefully', async () => {
        // Mock API error
        mockAxios.get.mockRejectedValueOnce(new Error('API Error'));

        render(<FAQ />);

        // Should show error message
        await waitFor(() => {
            expect(screen.getByText(/Failed to load FAQ items/i)).toBeInTheDocument();
        });

        // Should have a retry button
        expect(screen.getByText('Try Again')).toBeInTheDocument();
    });

    test('shows no results message when search has no matches', async () => {
        render(<FAQ />);

        // Wait for the component to load data
        await waitFor(() => {
            expect(screen.getByText('What is CertRats?')).toBeInTheDocument();
        });

        // Enter search term that won't match anything
        fireEvent.change(screen.getByPlaceholderText('Search FAQs...'), { target: { value: 'nonexistent' } });

        // Should show no results message
        expect(screen.getByText(/No FAQ items found matching your search criteria/i)).toBeInTheDocument();

        // Should have a clear filters button
        expect(screen.getByText('Clear Filters')).toBeInTheDocument();

        // Click clear filters
        fireEvent.click(screen.getByText('Clear Filters'));

        // Should show all FAQs again
        expect(screen.getByText('What is CertRats?')).toBeInTheDocument();
        expect(screen.getByText('How do I track my study time?')).toBeInTheDocument();
        expect(screen.getByText('Is my data secure?')).toBeInTheDocument();
    });
}); 