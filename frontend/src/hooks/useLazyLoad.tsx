import { useEffect, useRef, useState } from 'react';

interface UseLazyLoadOptions {
    threshold?: number;
    rootMargin?: string;
    once?: boolean;
}

/**
 * A hook that observes an element and returns whether it's visible in the viewport
 * Used for lazy loading components or triggering animations when elements come into view
 */
export function useLazyLoad(options: UseLazyLoadOptions = {}) {
    const [isVisible, setIsVisible] = useState(false);
    const elementRef = useRef<HTMLDivElement>(null);

    const { threshold = 0.1, rootMargin = '0px', once = true } = options;

    useEffect(() => {
        const element = elementRef.current;
        if (!element || typeof IntersectionObserver === 'undefined') {
            // If IntersectionObserver isn't supported, show the element immediately
            setIsVisible(true);
            return;
        }

        // Create an observer instance
        const observer = new IntersectionObserver(
            (entries) => {
                // We only care about the first element
                const [entry] = entries;

                // Update state when element becomes visible
                if (entry.isIntersecting) {
                    setIsVisible(true);

                    // If once is true, unobserve after becoming visible
                    if (once) {
                        observer.unobserve(element);
                    }
                } else if (!once) {
                    // If once is false, update visibility to false when out of viewport
                    setIsVisible(false);
                }
            },
            { threshold, rootMargin }
        );

        // Start observing the element
        observer.observe(element);

        // Cleanup observer on component unmount
        return () => {
            observer.unobserve(element);
        };
    }, [threshold, rootMargin, once]);

    return { isVisible, elementRef };
}

export default useLazyLoad; 