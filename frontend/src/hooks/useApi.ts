/**
 * useApi Hook
 * 
 * A React hook for accessing the API service and making API requests.
 * This ensures components use the centralized API endpoint handling.
 */

import { AxiosError, AxiosRequestConfig } from 'axios';
import { useCallback } from 'react';
import apiEndpoints from '../constants/apiEndpoints';
import apiService from '../services/apiService';

export interface UseApiReturn {
  request: <T>(config: AxiosRequestConfig) => Promise<T>;
  getEndpoint: (path: string, version?: string) => string;
  endpoints: typeof apiEndpoints;
  apiVersion: string;
}

/**
 * Hook for accessing API functionality in React components
 */
export const useApi = (): UseApiReturn => {
  /**
   * Make a type-safe request to the API
   */
  const request = useCallback(async <T>(config: AxiosRequestConfig): Promise<T> => {
    try {
      return await apiService.request<T>(config);
    } catch (error) {
      // Log errors in development
      if (process.env.NODE_ENV === 'development') {
        const axiosError = error as AxiosError;
        console.error('API request error:', {
          status: axiosError.response?.status,
          data: axiosError.response?.data,
          url: axiosError.config?.url,
          method: axiosError.config?.method,
        });
      }
      throw error;
    }
  }, []);

  /**
   * Get a properly versioned API endpoint
   */
  const getEndpoint = useCallback((path: string, version?: string): string => {
    return apiService.getEndpoint(path, version);
  }, []);

  return {
    request,
    getEndpoint,
    endpoints: apiEndpoints,
    apiVersion: apiService.getApiVersion(),
  };
};

export default useApi; 