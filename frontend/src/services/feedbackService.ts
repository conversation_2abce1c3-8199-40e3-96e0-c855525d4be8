import { FeedbackData } from '../components/shared/FeedbackPopup';
import api from './api';

export default class FeedbackService {
  /**
   * Submit user feedback to the API
   * @param feedbackData User feedback data
   * @returns Promise that resolves when feedback is submitted successfully
   */
  public async submitFeedback(feedbackData: FeedbackData): Promise<void> {
    try {
      await api.feedback.submit(feedbackData);
    } catch (error) {
      console.error('Failed to submit feedback:', error);
      throw new Error('Failed to submit feedback. Please try again later.');
    }
  }
} 