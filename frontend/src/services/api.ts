import { mockFetch } from '../mocks/handlers';

const API_BASE_URL = '/api/v1';

// Type definitions for API responses
export interface TutorialStatus {
  completed: boolean;
  lastStep: number;
  lastSeenDate: string | null;
}

export interface TutorialStatusResponse {
  [tutorialId: string]: TutorialStatus;
}

// API client interface definitions
interface AuthAPI {
  login: (credentials: { username: string, password: string }) => Promise<any>;
  logout: () => Promise<any>;
  me: () => Promise<any>;
}

interface UsersAPI {
  getProfile: () => Promise<any>;
  updateProfile: (profileData: any) => Promise<any>;
  getCertifications: () => Promise<any>;
  getPreferences: () => Promise<any>;
  updatePreferences: (preferencesData: any) => Promise<any>;
}

interface CertificationsAPI {
  getAll: () => Promise<any>;
  getById: (id: number) => Promise<any>;
  searchByName: (query: string) => Promise<any>;
}

interface FAQsAPI {
  getAll: () => Promise<any>;
  getByCategory: (category: string) => Promise<any>;
}

interface JobsAPI {
  getAll: (filters?: Record<string, any>) => Promise<any>;
  getById: (id: number) => Promise<any>;
}

interface CostsAPI {
  getExchangeRates: () => Promise<any>;
  getCertificationCosts: () => Promise<any>;
}

interface TutorialAPI {
  getStatus: () => Promise<TutorialStatusResponse>;
  updateStatus: (statusData: Record<string, any>) => Promise<any>;
}

interface StudyAPI {
  getEstimate: (params: {
    certification_id: number;
    hours_per_week: number;
    target_date: string | null;
  }) => Promise<any>;
  getROI: (certificationId: number) => Promise<any>;
  updateProgress: (params: {
    certification_id: number;
    progress?: number;
    custom_hours?: number;
    study_notes?: string;
  }) => Promise<any>;
}

interface FeedbackAPI {
  submit: (feedbackData: any) => Promise<any>;
  getAll: () => Promise<any>;
  getById: (id: number) => Promise<any>;
  updateStatus: (id: number, status: string, notes?: string) => Promise<any>;
}

// Combined API client interface
export interface APIClient {
  auth: AuthAPI;
  users: UsersAPI;
  certifications: CertificationsAPI;
  faqs: FAQsAPI;
  jobs: JobsAPI;
  costs: CostsAPI;
  tutorial: TutorialAPI;
  study: StudyAPI;
  feedback: FeedbackAPI;
}

// Helper to get auth token
const getToken = () => localStorage.getItem('auth_token');

// API request method with authentication
const apiRequest = async (endpoint: string, options: RequestInit = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  const token = getToken();
  
  // Set up headers
  const headers = {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` }),
    ...options.headers
  };
  
  // Combine options
  const requestOptions = {
    ...options,
    headers
  };
  
  try {
    // Use our mock implementation instead of the real fetch
    const response = await mockFetch(url, requestOptions);
    
    // Handle errors
    if (!response.ok) {
      const errorData = await response.json();
      throw {
        status: response.status,
        data: errorData,
        message: errorData.message || 'API request failed'
      };
    }
    
    return await response.json();
  } catch (error) {
    console.error(`API request error for ${endpoint}:`, error);
    throw error;
  }
};

// Auth API
const auth = {
  login: async (credentials: { username: string, password: string }) => {
    return apiRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials)
    });
  },
  
  logout: async () => {
    return apiRequest('/auth/logout', {
      method: 'POST'
    });
  },
  
  me: async () => {
    return apiRequest('/auth/me');
  }
};

// Users API
const users = {
  getProfile: async () => {
    return apiRequest('/users/profile');
  },
  
  updateProfile: async (profileData: any) => {
    return apiRequest('/users/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData)
    });
  },
  
  getCertifications: async () => {
    return apiRequest('/users/certifications');
  },
  
  getPreferences: async () => {
    return apiRequest('/users/preferences');
  },
  
  updatePreferences: async (preferencesData: any) => {
    return apiRequest('/users/preferences', {
      method: 'PUT',
      body: JSON.stringify(preferencesData)
    });
  }
};

// Certifications API
const certifications = {
  getAll: async () => {
    return apiRequest('/certifications');
  },
  
  getById: async (id: number) => {
    return apiRequest(`/certifications/${id}`);
  },
  
  searchByName: async (query: string) => {
    return apiRequest(`/certifications/search?q=${encodeURIComponent(query)}`);
  }
};

// FAQ API
const faqs = {
  getAll: async () => {
    return apiRequest('/faqs');
  },
  
  getByCategory: async (category: string) => {
    return apiRequest(`/faqs?category=${encodeURIComponent(category)}`);
  }
};

// Job search API
const jobs = {
  getAll: async (filters = {}) => {
    // Convert filters to query string
    const queryParams = Object.entries(filters)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`)
      .join('&');
    
    const endpoint = queryParams ? `/jobs?${queryParams}` : '/jobs';
    return apiRequest(endpoint);
  },
  
  getById: async (id: number) => {
    return apiRequest(`/jobs/${id}`);
  }
};

// Cost calculator API
const costs = {
  getExchangeRates: async () => {
    return apiRequest('/currency/rates');
  },
  
  getCertificationCosts: async () => {
    return apiRequest('/study/roi');
  }
};

// Tutorial and onboarding API
const tutorial = {
  getStatus: async () => {
    return apiRequest('/users/tutorial');
  },
  
  updateStatus: async (statusData: Record<string, any>) => {
    return apiRequest('/users/tutorial', {
      method: 'PUT',
      body: JSON.stringify(statusData)
    });
  }
};

// Study time tracker API
const study = {
  getEstimate: async (params: {
    certification_id: number;
    hours_per_week: number;
    target_date: string | null;
  }) => {
    return apiRequest('/study/estimate', {
      method: 'POST',
      body: JSON.stringify(params)
    });
  },
  
  getROI: async (certificationId: number) => {
    return apiRequest(`/study/roi/${certificationId}`);
  },
  
  updateProgress: async (params: {
    certification_id: number;
    progress?: number;
    custom_hours?: number;
    study_notes?: string;
  }) => {
    return apiRequest('/study/progress', {
      method: 'POST',
      body: JSON.stringify(params)
    });
  }
};

// Feedback API
const feedback = {
  submit: async (feedbackData: any) => {
    return apiRequest('/feedback', {
      method: 'POST',
      body: JSON.stringify(feedbackData)
    });
  },
  
  getAll: async () => {
    return apiRequest('/feedback');
  },
  
  getById: async (id: number) => {
    return apiRequest(`/feedback/${id}`);
  },
  
  updateStatus: async (id: number, status: string, notes?: string) => {
    return apiRequest(`/feedback/${id}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status, notes })
    });
  }
};

// Export the API client
const api: APIClient = {
  auth,
  users,
  certifications,
  faqs,
  jobs,
  costs,
  tutorial,
  study,
  feedback
};

export default api;
