/**
 * Centralized API Service
 * 
 * This service provides a central way to manage API endpoints and versioning,
 * ensuring that both frontend components and the API client use the same endpoints.
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { API_VERSION, getVersionedEndpoint } from '../constants/apiEndpoints';

class ApiService {
  private static instance: ApiService;
  private axiosInstance: AxiosInstance;
  private apiVersion: string;
  
  private constructor() {
    this.apiVersion = API_VERSION;
    
    // Create axios instance
    this.axiosInstance = axios.create({
      baseURL: process.env.REACT_APP_API_URL || '',
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 15000, // 15 seconds
    });
    
    // Add request interceptor for authentication
    this.axiosInstance.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token && config.headers) {
          config.headers['Authorization'] = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );
    
    // Add response interceptor for error handling
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => {
        // Handle errors (401, 403, etc.)
        if (error.response) {
          const { status } = error.response;
          
          if (status === 401) {
            // Unauthorized - redirect to login
            window.location.href = '/login';
          }
          
          if (status === 403) {
            // Forbidden - redirect to unauthorized page
            window.location.href = '/unauthorized';
          }
        }
        
        return Promise.reject(error);
      }
    );
  }
  
  /**
   * Get the singleton instance of ApiService
   */
  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }
  
  /**
   * Get an endpoint with the proper version
   */
  public getEndpoint(path: string, version?: string): string {
    return getVersionedEndpoint(path, version || this.apiVersion);
  }
  
  /**
   * Set the API version to use
   */
  public setApiVersion(version: string): void {
    this.apiVersion = version;
  }
  
  /**
   * Get the current API version
   */
  public getApiVersion(): string {
    return this.apiVersion;
  }
  
  /**
   * Get the axios instance for direct use
   */
  public getAxiosInstance(): AxiosInstance {
    return this.axiosInstance;
  }
  
  /**
   * Make a typed request to the API
   */
  public async request<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.axiosInstance(config);
      return response.data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const apiService = ApiService.getInstance();
export default apiService; 