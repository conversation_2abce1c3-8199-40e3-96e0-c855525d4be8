// Mock API client for development without a backend

class MockApiClient {
  private mockResponses: Record<string, any> = {
    // Existing mock responses
    // ...

    // Feedback endpoint
    '/api/feedback': {
      post: async (data: any) => {
        console.log('Mock API: Feedback submitted', data);
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 800));
        return { success: true };
      }
    }
  };

  // Existing methods
  // ... existing code ...

  public async post(endpoint: string, data: any = {}): Promise<any> {
    if (this.mockResponses[endpoint] && this.mockResponses[endpoint].post) {
      try {
        return await this.mockResponses[endpoint].post(data);
      } catch (error) {
        console.error(`Mock API POST error for ${endpoint}:`, error);
        throw error;
      }
    }
    
    console.warn(`No mock implementation for POST ${endpoint}`);
    throw new Error(`No mock implementation for POST ${endpoint}`);
  }

  // ... existing code ...
}

export default MockApiClient; 