/**
 * API endpoint definitions for the React frontend.
 * This file should be kept in sync with the backend api/constants.py file.
 */

// API version
export const API_VERSION = 'v1';
export const API_PREFIX = `/api/${API_VERSION}`;

/**
 * Creates a versioned API endpoint
 * @param endpoint - The endpoint path without leading slash
 * @param version - Optional version override
 * @returns The full versioned API path
 */
export const getVersionedEndpoint = (endpoint: string, version?: string): string => {
  const apiVersion = version || API_VERSION;
  return `/api/${apiVersion}/${endpoint.replace(/^\/+/, '')}`;
};

// Authentication endpoints
export const AUTH = {
  PREFIX: `${API_PREFIX}/auth`,
  LOGIN: `${API_PREFIX}/auth/login`,
  LOGOUT: `${API_PREFIX}/auth/logout`,
  ME: `${API_PREFIX}/auth/me`,
};

// User endpoints
export const USERS = {
  PREFIX: `${API_PREFIX}/users`,
  PROFILE: `${API_PREFIX}/users/profile`,
  PREFERENCES: `${API_PREFIX}/users/preferences`,
  CERTIFICATIONS: `${API_PREFIX}/users/certifications`,
  TUTORIAL: `${API_PREFIX}/users/tutorial`,
};

// Study endpoints
export const STUDY = {
  PREFIX: `${API_PREFIX}/study`,
  ESTIMATE: `${API_PREFIX}/study/estimate`,
  PROGRESS: `${API_PREFIX}/study/progress`,
  ROI: `${API_PREFIX}/study/roi`,
};

// Certification endpoints
export const CERTIFICATIONS = {
  PREFIX: `${API_PREFIX}/certifications`,
  LIST: `${API_PREFIX}/certifications`,
  DETAIL: (id: string | number) => `${API_PREFIX}/certifications/${id}`,
  RELATIONSHIPS: `${API_PREFIX}/certifications/relationships`,
};

// Career path endpoints
export const CAREER = {
  PREFIX: `${API_PREFIX}/career`,
  PATHS: `${API_PREFIX}/career/paths`,
  RECOMMENDATIONS: `${API_PREFIX}/career/recommendations`,
  ROLES: `${API_PREFIX}/career/roles`,
};

// Job endpoints
export const JOBS = {
  PREFIX: `${API_PREFIX}/jobs`,
  SEARCH: `${API_PREFIX}/jobs/search`,
  DETAIL: (id: string | number) => `${API_PREFIX}/jobs/${id}`,
  MARKET: `${API_PREFIX}/jobs/market`,
  CERTIFICATIONS: (id: string | number) => `${API_PREFIX}/jobs/${id}/certifications`,
};

// FAQ endpoints
export const FAQ = {
  PREFIX: `${API_PREFIX}/faq`,
  LIST: `${API_PREFIX}/faq`,
};

// Admin endpoints
export const ADMIN = {
  PREFIX: `${API_PREFIX}/admin`,
  CERTIFICATIONS: `${API_PREFIX}/admin/certifications`,
  ORGANIZATIONS: `${API_PREFIX}/admin/organizations`,
  FEEDBACK: `${API_PREFIX}/admin/feedback`,
  TRANSLATIONS: `${API_PREFIX}/admin/translations`,
  ENRICHMENT: `${API_PREFIX}/admin/enrichment`,
  STATS: `${API_PREFIX}/admin/stats`,
  JOBS: `${API_PREFIX}/admin/jobs`,
};

// Currency endpoints
export const CURRENCY = {
  PREFIX: `${API_PREFIX}/currency`,
  RATES: `${API_PREFIX}/currency/rates`,
  CONVERT: `${API_PREFIX}/currency/convert`,
};

// Feedback endpoints
export const FEEDBACK = {
  PREFIX: `${API_PREFIX}/feedback`,
  SUBMIT: `${API_PREFIX}/feedback`,
};

// Journey endpoints
export const JOURNEYS = {
  PREFIX: `${API_PREFIX}/journeys`,
  LIST: `${API_PREFIX}/journeys`,
  DETAIL: (id: string | number) => `${API_PREFIX}/journeys/${id}`,
  MILESTONES: (id: string | number) => `${API_PREFIX}/journeys/${id}/milestones`,
  COMMUNITY: `${API_PREFIX}/journeys/community`,
};

// Health check
export const HEALTH = `${API_PREFIX}/health`;

// Default export for convenient importing
export default {
  API_VERSION,
  API_PREFIX,
  AUTH,
  USERS,
  STUDY,
  CERTIFICATIONS,
  CAREER,
  JOBS,
  FAQ,
  ADMIN,
  CURRENCY,
  FEEDBACK,
  JOURNEYS,
  HEALTH,
}; 