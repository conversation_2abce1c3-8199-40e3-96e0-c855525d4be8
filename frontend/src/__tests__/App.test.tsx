import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import App from '../App';

// Mock any global modules or context providers if needed
jest.mock('axios');

describe('App Component', () => {
  beforeEach(() => {
    // Setup any mocks or test data
  });

  test('renders security certification explorer heading', () => {
    render(
      <BrowserRouter>
        <App />
      </BrowserRouter>
    );
    
    // Using an async find method since content might be loaded asynchronously
    expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
  });

  test('navigates between main pages', async () => {
    const { user } = render(
      <BrowserRouter>
        <App />
      </BrowserRouter>
    );
    
    // Check navigation elements are present
    const navElements = screen.getAllByRole('navigation');
    expect(navElements.length).toBeGreaterThan(0);
  });
}); 