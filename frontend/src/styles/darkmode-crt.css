@import url('https://fonts.googleapis.com/css2?family=VT323&display=swap');

body.crt-darkmode {
  background: #101010;
  color: #39ff14;
  font-family: 'VT323', '<PERSON>ra Mono', 'Consolas', monospace;
  text-shadow: 0 0 8px #39ff14, 0 0 2px #39ff14;
}

.crt-darkmode .editor,
.crt-darkmode pre,
.crt-darkmode code {
  background: #101010;
  color: #39ff14;
  border: 2px solid #39ff14;
  border-radius: 6px;
  box-shadow: 0 0 20px #39ff1433;
  font-family: inherit;
  padding: 1em;
}

.crt-darkmode .scanlines {
  pointer-events: none;
  position: fixed;
  top: 0; left: 0; width: 100vw; height: 100vh;
  background: repeating-linear-gradient(
    to bottom,
    transparent,
    transparent 2px,
    rgba(60,255,20,0.07) 3px,
    transparent 4px
  );
  z-index: 9999;
  mix-blend-mode: lighten;
}

.crt-darkmode .crt-cursor {
  display: inline-block;
  width: 0.6em;
  height: 1em;
  background: #39ff14;
  margin-left: 2px;
  animation: blink 1s steps(1) infinite;
  vertical-align: bottom;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
} 