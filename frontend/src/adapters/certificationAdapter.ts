/**
 * Adapters for mapping between backend API certification data and frontend certification model
 */

// Type definitions matching the backend API response
export interface APICertification {
  id: number;
  name: string;
  provider: string;
  description: string | null;
  type: string;
  level: string;
  domain: string | null;
  exam_code: string | null;
  price: number | null;
  duration_minutes: number | null;
  passing_score: number | null;
  prerequisites_list: string[] | null;
  skills_covered: string[] | null;
  url: string | null;
  created_at: string;
  updated_at: string;
}

export interface APIListResponse {
  certifications: APICertification[];
  total: number;
  page: number;
  page_size: number;
}

// Frontend expected certification model
export interface Certification {
  id: number;
  name: string;
  provider: string;
  description: string | null;
  type: string;
  level: string;
  exam_code: string | null;
  price: number | null;
  duration_minutes: number | null;
  passing_score: number | null;
  skills_covered: string[] | null;
  url: string | null;
  created_at: string;
  updated_at: string;
}

export interface CertificationListResponse {
  certifications: Certification[];
  total: number;
  page: number;
  page_size: number;
}

/**
 * Adapts a backend certification to the frontend certification format
 */
export function adaptCertification(apiCert: APICertification): Certification {
  return {
    id: apiCert.id,
    name: apiCert.name,
    provider: apiCert.provider,
    description: apiCert.description,
    type: apiCert.type.toLowerCase(), // Ensure consistent casing
    level: apiCert.level.toLowerCase(), // Ensure consistent casing
    exam_code: apiCert.exam_code,
    price: apiCert.price,
    duration_minutes: apiCert.duration_minutes,
    passing_score: apiCert.passing_score,
    skills_covered: apiCert.skills_covered || [],
    url: apiCert.url,
    created_at: apiCert.created_at,
    updated_at: apiCert.updated_at,
  };
}

/**
 * Adapts a backend certification list response to the frontend expected format
 */
export function adaptCertificationListResponse(apiResponse: APIListResponse): CertificationListResponse {
  return {
    certifications: apiResponse.certifications.map(adaptCertification),
    total: apiResponse.total,
    page: apiResponse.page,
    page_size: apiResponse.page_size
  };
} 