import { render, RenderOptions } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ReactElement } from 'react';
import { ErrorProvider } from './error';

// Providers for all tests
const AllProviders = ({ children }: { children: React.ReactNode }) => {
    return (
        <ErrorProvider>
            {children}
        </ErrorProvider>
    );
};

// Custom render with all providers
const customRender = (
    ui: ReactElement,
    options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllProviders, ...options });

// Create a custom version of userEvent
const customUserEvent = {
    ...userEvent,
    // For older versions of testing-library that don't support setup()
    setup: () => userEvent,
};

// Custom render with providers that returns userEvent
export const renderWithProviders = (
    ui: ReactElement,
    options?: Omit<RenderOptions, 'wrapper'>
) => {
    return {
        ...render(ui, { wrapper: AllProviders, ...options }),
        // Return userEvent directly without calling setup()
        user: userEvent,
    };
};

// Re-export everything from React Testing Library
export * from '@testing-library/react';

// Override render method
export { customRender as render };
