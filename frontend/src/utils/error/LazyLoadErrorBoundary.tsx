import { Component, ErrorInfo, ReactNode } from 'react';
import { logModuleError } from './errorLogger';
import './ErrorStyles.css';

interface LazyLoadErrorBoundaryProps {
    children: ReactNode;
    fallback?: ReactNode;
    modulePath: string;
    retry?: () => void;
}

interface LazyLoadErrorBoundaryState {
    hasError: boolean;
    error: Error | null;
}

/**
 * Error boundary specifically designed to catch and handle errors in lazy-loaded components
 * Provides specialized error logging for module loading failures and a retry mechanism
 */
class LazyLoadErrorBoundary extends Component<LazyLoadErrorBoundaryProps, LazyLoadErrorBoundaryState> {
    constructor(props: LazyLoadErrorBoundaryProps) {
        super(props);
        this.state = {
            hasError: false,
            error: null
        };
    }

    static getDerivedStateFromError(error: Error): LazyLoadErrorBoundaryState {
        return {
            hasError: true,
            error
        };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
        // Use specialized module error logging
        logModuleError(this.props.modulePath, error);
    }

    handleRetry = (): void => {
        this.setState({ hasError: false, error: null });
        if (this.props.retry) {
            this.props.retry();
        }
    };

    render(): ReactNode {
        const { children, fallback } = this.props;
        const { hasError, error } = this.state;

        if (hasError) {
            // If a custom fallback is provided, use it
            if (fallback) {
                return fallback;
            }

            // Default fallback UI with retry option
            return (
                <div className="error-boundary-container module-error" data-testid="error-boundary">
                    <h3>Failed to load component</h3>
                    <p>
                        {error?.message || 'An error occurred while loading this component.'}
                    </p>
                    <div className="error-actions">
                        <button
                            className="retry-button"
                            onClick={this.handleRetry}
                            data-testid="retry-button"
                        >
                            Retry
                        </button>
                        <button
                            className="home-button"
                            onClick={() => window.location.href = '/'}
                        >
                            Go to Homepage
                        </button>
                    </div>
                </div>
            );
        }

        return children;
    }
}

export default LazyLoadErrorBoundary; 