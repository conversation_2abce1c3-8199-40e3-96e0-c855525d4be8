import React from 'react';
import './ErrorStyles.css';

interface ErrorFallbackProps {
    error: Error | null;
    resetError?: () => void;
}

// Define a constant for development mode
const IS_DEV_MODE = process.env.NODE_ENV === 'development';

/**
 * Error Fallback component to display when an error occurs
 * Shows a user-friendly error message with the option to try again
 */
const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, resetError }) => {
    return (
        <div className="error-fallback">
            <div className="error-container">
                <div className="error-icon">
                    <svg viewBox="0 0 24 24" width="36" height="36" fill="none" stroke="currentColor" strokeWidth="2">
                        <circle cx="12" cy="12" r="10" />
                        <line x1="12" y1="8" x2="12" y2="12" />
                        <line x1="12" y1="16" x2="12.01" y2="16" />
                    </svg>
                </div>
                <h2>Something went wrong</h2>

                <div className="error-details">
                    <p>We encountered an unexpected error. Our team has been notified.</p>
                    {error && IS_DEV_MODE && (
                        <div className="error-message">
                            <p><strong>Error details (visible in development only):</strong></p>
                            <pre>{error.message}</pre>
                            <pre>{error.stack}</pre>
                        </div>
                    )}
                </div>

                <div className="error-actions">
                    {resetError && (
                        <button
                            onClick={resetError}
                            className="error-retry-button"
                        >
                            Try Again
                        </button>
                    )}
                    <button
                        onClick={() => window.location.href = '/'}
                        className="error-home-button"
                    >
                        Go to Homepage
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ErrorFallback; 