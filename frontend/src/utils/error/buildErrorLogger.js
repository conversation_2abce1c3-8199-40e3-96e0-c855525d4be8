/**
 * Custom Webpack plugin to log build-time errors
 * This helps capture compilation errors and send them to logging services
 */
class BuildErrorLoggerPlugin {
  constructor(options = {}) {
    this.options = {
      logToFile: true,
      logFilePath: './build-errors.log',
      notifyDevelopers: process.env.NODE_ENV === 'production',
      ...options
    };
    this.fs = require('fs');
    this.path = require('path');
  }

  /**
   * Format error for logging
   */
  formatError(error) {
    return {
      message: error.message || 'Unknown error',
      module: error.module?.resource || 'Unknown module',
      stack: error.stack || '',
      details: error.details || '',
      time: new Date().toISOString()
    };
  }

  /**
   * Log error to file
   */
  logToFile(error) {
    if (!this.options.logToFile) return;

    try {
      const formattedError = this.formatError(error);
      const logEntry = JSON.stringify(formattedError, null, 2) + ',\n';
      
      // Ensure directory exists
      const dir = this.path.dirname(this.options.logFilePath);
      if (!this.fs.existsSync(dir)) {
        this.fs.mkdirSync(dir, { recursive: true });
      }

      // Append to log file
      this.fs.appendFileSync(
        this.options.logFilePath,
        logEntry,
        { encoding: 'utf8' }
      );
    } catch (loggingError) {
      console.error('Failed to log build error to file:', loggingError);
    }
  }

  /**
   * Notify developers about build errors (in production)
   */
  notifyDevelopers(errors) {
    if (!this.options.notifyDevelopers || errors.length === 0) return;
    
    // Here you would implement code to notify developers
    // This could be via email, Slack, or other notification systems
    console.log(`[BuildErrorLogger] ${errors.length} errors would be sent to developers`);
    
    // Example of what would be implemented in production:
    /*
    const errorSummary = errors.map(error => ({
      message: error.message,
      module: error.module?.resource
    }));
    
    fetch('/api/v1/build-notifications', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        errors: errorSummary,
        buildTime: new Date().toISOString(),
        environment: process.env.NODE_ENV
      })
    }).catch(e => console.error('Failed to send build notification:', e));
    */
  }

  /**
   * Apply plugin to webpack
   */
  apply(compiler) {
    // Handle compilation errors
    compiler.hooks.failed.tap('BuildErrorLoggerPlugin', (error) => {
      console.error('[BuildErrorLogger] Compilation failed:', error.message);
      this.logToFile(error);
      this.notifyDevelopers([error]);
    });

    // Handle compilation warnings/errors
    compiler.hooks.done.tap('BuildErrorLoggerPlugin', (stats) => {
      if (stats.hasErrors()) {
        const errors = stats.compilation.errors;
        console.error(`[BuildErrorLogger] ${errors.length} compilation errors`);
        
        errors.forEach(error => {
          this.logToFile(error);
        });
        
        this.notifyDevelopers(errors);
      }
    });
  }
}

module.exports = BuildErrorLoggerPlugin; 