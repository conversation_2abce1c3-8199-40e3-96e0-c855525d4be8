# Error Handling System

This directory contains a comprehensive error handling system designed to:

1. Catch and log UI errors during runtime
2. Handle and report module loading errors
3. Log build-time compilation errors
4. Provide user-friendly error experiences

## Components

- `ErrorBoundary.tsx` - React error boundary for catching runtime errors
- `LazyLoadErrorBoundary.tsx` - Specialized error boundary for lazy-loaded components
- `errorLogger.ts` - Centralized error logging utilities
- `ErrorContext.tsx` - React context for global error state management
- `ErrorFallback.tsx` - User-friendly error display component
- `ErrorToast.tsx` - Toast notification for non-critical errors
- `FormErrorMessage.tsx` - Form-specific error display
- `buildErrorLogger.js` - Webpack plugin for capturing build errors

## Integration

### Runtime Error Handling

Add the `ErrorProvider` at the root of your application:

```tsx
import { ErrorProvider } from './utils/error';

function App() {
  return (
    <ErrorProvider>
      {/* Your app content */}
    </ErrorProvider>
  );
}
```

### Lazy-Loaded Component Error Handling

Use the `LazyLoadErrorBoundary` for lazy-loaded components:

```tsx
import { LazyLoadErrorBoundary } from './utils/error';

const LazyComponent = ({ component: Component, path }) => (
  <LazyLoadErrorBoundary modulePath={path}>
    <Suspense fallback={<Loading />}>
      <Component />
    </Suspense>
  </LazyLoadErrorBoundary>
);
```

### Webpack Build Error Logging

To capture build-time errors, add the `BuildErrorLoggerPlugin` to your webpack config:

```js
// webpack.config.js
const BuildErrorLoggerPlugin = require('./src/utils/error/buildErrorLogger');

module.exports = {
  // ... other webpack config
  plugins: [
    // ... other plugins
    new BuildErrorLoggerPlugin({
      logFilePath: './logs/build-errors.log',
      notifyDevelopers: process.env.NODE_ENV === 'production'
    })
  ]
};
```

### Error Logging in Code

Use the provided logging utilities directly in your code:

```tsx
import { logError, logWarning, ERROR_CATEGORY } from './utils/error';

try {
  // Your code
} catch (error) {
  logError(error, undefined, { 
    category: ERROR_CATEGORY.UI,
    component: 'MyComponent'
  });
}
```

For module loading errors, use:

```tsx
import { logModuleError } from './utils/error';

const MyComponent = React.lazy(() => import('./path/to/Component')
  .catch(error => {
    logModuleError('./path/to/Component', error);
    throw error;
  })
);
```

## Environment Configuration

For production, set up proper error tracking services:

1. Modify the `sendToErrorTracking` function in `errorLogger.ts`
2. Configure alert thresholds for critical errors
3. Set up developer notifications for build errors
4. Consider adding crash reporting for production builds

## Best Practices

1. Always wrap dynamic imports with error handling
2. Use appropriate error categories for classification
3. Include relevant context when logging errors
4. Present user-friendly error messages to end-users
5. Log detailed error information for developers
6. Add retry mechanisms where appropriate 