import { ErrorInfo } from 'react';

/**
 * Environment configuration
 */
const isProduction = process.env.NODE_ENV === 'production';
const LOG_LEVEL = {
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
  CRITICAL: 'critical',
};

/**
 * Error categories for better classification
 */
export const ERROR_CATEGORY = {
  RUNTIME: 'runtime',
  NETWORK: 'network',
  UI: 'ui',
  MODULE: 'module',
  COMPILATION: 'compilation',
  BUILD: 'build',
};

/**
 * Log error to the console and to any configured error tracking services
 */
export const logError = (error: Error, errorInfo?: ErrorInfo, context?: Record<string, any>): void => {
  if (!isProduction) {
    // In development, log detailed error info to console
    console.group('Error occurred:');
    console.error(error);
    if (errorInfo?.componentStack) {
      console.error('Component Stack:', errorInfo.componentStack);
    }
    if (context) {
      console.log('Additional Context:', context);
    }
    console.groupEnd();
  } else {
    // In production, we would send this to our error tracking service
    sendToErrorTracking(error, errorInfo, context);
  }
};

/**
 * Log a warning (less severe issues)
 */
export const logWarning = (message: string, context?: Record<string, any>): void => {
  if (!isProduction) {
    console.warn(message, context || '');
  } else {
    // In production, send warnings to the error tracking service with warning level
    const warningError = new Error(message);
    sendToErrorTracking(warningError, undefined, context, LOG_LEVEL.WARNING);
  }
};

/**
 * Log module loading errors specifically
 */
export const logModuleError = (modulePath: string, error: Error): void => {
  const context = {
    category: ERROR_CATEGORY.MODULE,
    modulePath,
    suggestion: 'Check if the module exists and its path is correct'
  };
  
  logError(error, undefined, context);
  
  // Log a more specific error to help with debugging
  console.error(`Failed to load module: ${modulePath}. ${error.message}`);
};

/**
 * Log compilation errors that occur during build process
 */
export const logCompilationError = (error: Error, file?: string): void => {
  const context = {
    category: ERROR_CATEGORY.COMPILATION,
    file,
    buildTime: new Date().toISOString(),
  };
  
  // Compilation errors are critical as they prevent the app from running
  sendToErrorTracking(error, undefined, context, LOG_LEVEL.CRITICAL);
  
  if (!isProduction) {
    console.error(`Compilation Error in ${file || 'unknown file'}:`, error);
  }
};

/**
 * Send error to an error tracking service (like Sentry, Rollbar, etc.)
 * This is a placeholder - implement with your actual error tracking service
 */
const sendToErrorTracking = (
  error: Error,
  errorInfo?: ErrorInfo,
  context?: Record<string, any>,
  level: string = LOG_LEVEL.ERROR
): void => {
  // This would be replaced with actual implementation for your error tracking service
  // Example for Sentry:
  // Sentry.captureException(error, {
  //   level,
  //   contexts: {
  //     react: errorInfo,
  //     ...context
  //   }
  // });
  
  // For now, just log to console in production as a fallback
  console.error(`[${level.toUpperCase()}] Error sent to tracking service:`, error.message);
  
  // In a real implementation, you would add code to send the error to your tracking service
  // Consider adding a flag to track if the error was properly sent
  const errorReport = createErrorReport(error, context?.componentName, {
    ...context,
    errorInfo,
    level
  });
  
  // Here you would typically POST this errorReport to your backend API
  // fetch('/api/v1/error-logs', {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(errorReport)
  // }).catch(e => console.error('Failed to send error report:', e));
};

/**
 * Format error message for display
 */
export const formatErrorMessage = (error: Error | null | unknown): string => {
  if (!error) {
    return 'An unknown error occurred';
  }
  
  if (error instanceof Error) {
    return error.message || 'An error occurred';
  }
  
  // Handle non-Error objects
  if (typeof error === 'string') {
    return error;
  }
  
  return 'An unexpected error occurred';
};

/**
 * Combine error information into a single object 
 */
export const createErrorReport = (
  error: Error | null | unknown,
  componentName?: string,
  additionalInfo?: Record<string, any>
): Record<string, any> => {
  const timestamp = new Date().toISOString();
  const browserInfo = {
    userAgent: navigator.userAgent,
    language: navigator.language,
    platform: navigator.platform,
  };
  
  return {
    timestamp,
    error: error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
    } : String(error),
    componentName,
    browserInfo,
    additionalInfo,
    url: window.location.href,
  };
}; 