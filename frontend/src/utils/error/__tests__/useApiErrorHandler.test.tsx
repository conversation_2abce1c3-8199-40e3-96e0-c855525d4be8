import { act, renderHook } from '@testing-library/react-hooks';
import React from 'react';
import { ErrorProvider } from '../ErrorContext';
import useApiErrorHandler from '../useApiErrorHandler';

// Mock axios error
const createAxiosError = (status: number, message: string) => {
    const error: any = new Error(message);
    error.isAxiosError = true;
    error.response = {
        status,
        data: {
            message,
        },
    };
    return error;
};

// Mock the error context
jest.mock('../ErrorContext', () => {
    const original = jest.requireActual('../ErrorContext');
    return {
        ...original,
        useError: () => ({
            addError: jest.fn(),
            dismissError: jest.fn(),
            clearAllErrors: jest.fn(),
            errors: [],
            hasErrors: false,
        }),
    };
});

// Wrap the hook in the ErrorProvider
const wrapper = ({ children }: { children: React.ReactNode }) => (
    <ErrorProvider>{children}</ErrorProvider>
);

describe('useApiErrorHandler Hook', () => {
    it('should handle successful API calls', async () => {
        const { result } = renderHook(() => useApiErrorHandler(), { wrapper });

        const mockApiCall = jest.fn().mockResolvedValue({ data: 'success' });
        const mockOnSuccess = jest.fn();

        let response;
        await act(async () => {
            response = await result.current.handleApiCall(mockApiCall, {
                onSuccess: mockOnSuccess,
            });
        });

        expect(mockApiCall).toHaveBeenCalled();
        expect(mockOnSuccess).toHaveBeenCalledWith({ data: 'success' });
        expect(response).toEqual({ data: 'success' });
        expect(result.current.isLoading).toBe(false);
        expect(result.current.error).toBeNull();
    });

    it('should handle API errors', async () => {
        const { result } = renderHook(() => useApiErrorHandler(), { wrapper });

        const error = new Error('API failed');
        const mockApiCall = jest.fn().mockRejectedValue(error);
        const mockOnError = jest.fn();

        let response;
        await act(async () => {
            response = await result.current.handleApiCall(mockApiCall, {
                onError: mockOnError,
                errorMessage: 'Custom error message',
            });
        });

        expect(mockApiCall).toHaveBeenCalled();
        expect(mockOnError).toHaveBeenCalled();
        expect(response).toBeNull();
        expect(result.current.isLoading).toBe(false);
        expect(result.current.error).toEqual(error);
    });

    it('should handle axios errors with special formatting', async () => {
        const { result } = renderHook(() => useApiErrorHandler(), { wrapper });

        const axiosError = createAxiosError(404, 'Resource not found');
        const mockApiCall = jest.fn().mockRejectedValue(axiosError);

        await act(async () => {
            await result.current.handleApiCall(mockApiCall);
        });

        expect(mockApiCall).toHaveBeenCalled();
        expect(result.current.isLoading).toBe(false);
        expect(result.current.error).toBeTruthy();
        expect(result.current.error?.message).toBe('Resource not found');
    });

    it('should not show error toast when showErrorToast is false', async () => {
        const { result } = renderHook(() => useApiErrorHandler(), { wrapper });

        const error = new Error('API failed');
        const mockApiCall = jest.fn().mockRejectedValue(error);
        const addError = jest.spyOn((result.current as any).addError, 'mock');

        await act(async () => {
            await result.current.handleApiCall(mockApiCall, {
                showErrorToast: false,
            });
        });

        expect(mockApiCall).toHaveBeenCalled();
        expect(addError).not.toHaveBeenCalled();
        expect(result.current.error).toEqual(error);
    });

    it('should clear error when clearError is called', async () => {
        const { result } = renderHook(() => useApiErrorHandler(), { wrapper });

        const error = new Error('API failed');
        const mockApiCall = jest.fn().mockRejectedValue(error);

        await act(async () => {
            await result.current.handleApiCall(mockApiCall);
        });

        expect(result.current.error).toEqual(error);

        act(() => {
            result.current.clearError();
        });

        expect(result.current.error).toBeNull();
    });
}); 