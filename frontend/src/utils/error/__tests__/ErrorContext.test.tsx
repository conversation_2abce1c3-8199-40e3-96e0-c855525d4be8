import { fireEvent, render, screen } from '@testing-library/react';
import { act } from '@testing-library/react-hooks';
import { ErrorProvider, useError } from '../ErrorContext';

// Mock the errorLogger to avoid actual logging during tests
jest.mock('../errorLogger', () => ({
    logError: jest.fn(),
    formatErrorMessage: jest.fn(error =>
        error instanceof Error ? error.message : String(error)
    ),
}));

describe('ErrorContext', () => {
    beforeEach(() => {
        // Clear all mocks before each test
        jest.clearAllMocks();

        // Mock setTimeout to execute immediately
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    it('should add an error and show toast', async () => {
        const TestComponent = () => {
            const { addError, errors } = useError();

            return (
                <div>
                    <button onClick={() => addError(new Error('Test error message'))}>
                        Add Error
                    </button>
                    <p>Error count: {errors.length}</p>
                </div>
            );
        };

        render(
            <ErrorProvider>
                <TestComponent />
            </ErrorProvider>
        );

        // Add an error
        fireEvent.click(screen.getByText('Add Error'));

        // Check that error was added
        expect(screen.getByText('Error count: 1')).toBeInTheDocument();

        // Check that toast is rendered
        expect(screen.getByText('Test error message')).toBeInTheDocument();
    });

    it('should dismiss errors when requested', async () => {
        const TestComponent = () => {
            const { addError, dismissError, errors } = useError();

            return (
                <div>
                    <button onClick={() => {
                        const err = new Error('Test error message');
                        addError(err);
                        return err;
                    }}>
                        Add Error
                    </button>
                    <p>Error count: {errors.length}</p>
                    {errors.length > 0 && (
                        <button onClick={() => dismissError(errors[0].id)}>
                            Dismiss Error
                        </button>
                    )}
                </div>
            );
        };

        render(
            <ErrorProvider>
                <TestComponent />
            </ErrorProvider>
        );

        // Add an error
        fireEvent.click(screen.getByText('Add Error'));

        // Check that error was added
        expect(screen.getByText('Error count: 1')).toBeInTheDocument();

        // Dismiss the error
        fireEvent.click(screen.getByText('Dismiss Error'));

        // Check that error was dismissed after animation
        act(() => {
            jest.advanceTimersByTime(300);
        });

        expect(screen.getByText('Error count: 0')).toBeInTheDocument();
    });

    it('should clear all errors at once', async () => {
        const TestComponent = () => {
            const { addError, clearAllErrors, errors } = useError();

            return (
                <div>
                    <button onClick={() => addError('First error')}>
                        Add First Error
                    </button>
                    <button onClick={() => addError('Second error')}>
                        Add Second Error
                    </button>
                    <button onClick={clearAllErrors}>
                        Clear All Errors
                    </button>
                    <p>Error count: {errors.length}</p>
                </div>
            );
        };

        render(
            <ErrorProvider>
                <TestComponent />
            </ErrorProvider>
        );

        // Add two errors
        fireEvent.click(screen.getByText('Add First Error'));
        fireEvent.click(screen.getByText('Add Second Error'));

        // Check that errors were added
        expect(screen.getByText('Error count: 2')).toBeInTheDocument();

        // Clear all errors
        fireEvent.click(screen.getByText('Clear All Errors'));

        // Check that all errors were cleared
        expect(screen.getByText('Error count: 0')).toBeInTheDocument();
    });

    it('should auto-dismiss errors after timeout', async () => {
        const TestComponent = () => {
            const { addError, errors } = useError();

            return (
                <div>
                    <button onClick={() => addError('Auto-dismiss test')}>
                        Add Error
                    </button>
                    <p>Error count: {errors.length}</p>
                </div>
            );
        };

        render(
            <ErrorProvider>
                <TestComponent />
            </ErrorProvider>
        );

        // Add an error
        fireEvent.click(screen.getByText('Add Error'));

        // Check that error was added
        expect(screen.getByText('Error count: 1')).toBeInTheDocument();

        // Fast-forward time to trigger auto-dismiss
        act(() => {
            jest.advanceTimersByTime(5000); // 5 seconds for fade out
        });

        // Fast-forward time for the animation
        act(() => {
            jest.advanceTimersByTime(300); // 300ms for animation
        });

        // Check that error was auto-dismissed
        expect(screen.getByText('Error count: 0')).toBeInTheDocument();
    });
}); 