import { render, screen } from '@testing-library/react';
import ErrorBoundary from '../ErrorBoundary';

// Mock the error logger
jest.mock('../errorLogger', () => ({
    logError: jest.fn(),
    formatErrorMessage: jest.fn((error) => error.message),
}));

// Component that throws an error
const ErrorThrowingComponent = ({ shouldThrow = true }: { shouldThrow?: boolean }) => {
    if (shouldThrow) {
        throw new Error('Test error message');
    }
    return <div>No error</div>;
};

// Silence React's error boundary console errors during tests
const originalConsoleError = console.error;
beforeAll(() => {
    console.error = jest.fn();
});

afterAll(() => {
    console.error = originalConsoleError;
});

describe('ErrorBoundary Component', () => {
    it('should render children when no error occurs', () => {
        render(
            <ErrorBoundary>
                <div>Child component</div>
            </ErrorBoundary>
        );

        expect(screen.getByText('Child component')).toBeInTheDocument();
    });

    it('should render the default fallback UI when an error occurs', () => {
        render(
            <ErrorBoundary>
                <ErrorThrowingComponent />
            </ErrorBoundary>
        );

        expect(screen.getByText(/Something went wrong/i)).toBeInTheDocument();
        expect(screen.getByText(/Test error message/i)).toBeInTheDocument();
        expect(screen.getByText(/Try Again/i)).toBeInTheDocument();
    });

    it('should render a custom fallback when provided', () => {
        const CustomFallback = () => <div>Custom fallback UI</div>;

        render(
            <ErrorBoundary fallback={<CustomFallback />}>
                <ErrorThrowingComponent />
            </ErrorBoundary>
        );

        expect(screen.getByText('Custom fallback UI')).toBeInTheDocument();
    });

    it('should call onError callback when an error occurs', () => {
        const mockOnError = jest.fn();

        render(
            <ErrorBoundary onError={mockOnError}>
                <ErrorThrowingComponent />
            </ErrorBoundary>
        );

        expect(mockOnError).toHaveBeenCalled();
        expect(mockOnError.mock.calls[0][0].message).toBe('Test error message');
    });
}); 