import React from 'react';
import './ErrorStyles.css';

interface FormErrorMessageProps {
    error?: string | string[] | null;
    className?: string;
}

/**
 * Component to display form validation errors
 */
const FormErrorMessage: React.FC<FormErrorMessageProps> = ({ error, className = '' }) => {
    if (!error) return null;

    // Convert error to array if it's a string
    const errors = Array.isArray(error) ? error : [error];

    if (errors.length === 0) return null;

    return (
        <div className={`form-error-message ${className}`} role="alert">
            <ul>
                {errors.map((message, index) => (
                    <li key={index}>{message}</li>
                ))}
            </ul>
        </div>
    );
};

export default FormErrorMessage; 