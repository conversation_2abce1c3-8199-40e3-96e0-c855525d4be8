import { AxiosError } from 'axios';
import { useCallback, useState } from 'react';
import { useError } from './ErrorContext';

/**
 * Error handling hook for API requests
 * Provides loading state, error state, and a wrapper function to handle errors
 */
export const useApiErrorHandler = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { addError } = useError();

  /**
   * Wrap an API call in error handling logic
   */
  const handleApiCall = useCallback(async <T>(
    apiCall: () => Promise<T>,
    options?: {
      errorMessage?: string;
      showErrorToast?: boolean;
      onSuccess?: (data: T) => void;
      onError?: (error: Error) => void;
    }
  ): Promise<T | null> => {
    const {
      errorMessage = 'Failed to complete the request',
      showErrorToast = true,
      onSuccess,
      onError,
    } = options || {};

    setIsLoading(true);
    setError(null);

    try {
      const response = await apiCall();
      setIsLoading(false);

      if (onSuccess) {
        onSuccess(response);
      }

      return response;
    } catch (err) {
      setIsLoading(false);
      
      // Format and handle the error
      const error = err as Error | AxiosError;
      let formattedError: Error;
      let details = '';

      if ('isAxiosError' in error && error.isAxiosError) {
        // Handle Axios errors
        const status = error.response?.status;
        const data = error.response?.data as any;
        const errorMessage = data?.message || data?.error || error.message;
        
        details = `Status: ${status || 'Unknown'}`;
        formattedError = new Error(errorMessage);
      } else {
        formattedError = error as Error;
      }

      setError(formattedError);
      
      if (showErrorToast) {
        addError(formattedError, details);
      }
      
      if (onError) {
        onError(formattedError);
      }
      
      return null;
    }
  }, [addError]);

  /**
   * Clear any error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    isLoading,
    error,
    handleApiCall,
    clearError,
  };
};

export default useApiErrorHandler; 