import React, { createContext, ReactNode, useCallback, useContext, useState } from 'react';
import { formatErrorMessage, logError } from './errorLogger';
import ErrorToast from './ErrorToast';

// Types for error notifications
export type ErrorNotification = {
    id: string;
    message: string;
    details?: string;
    timestamp: number;
};

// Context type
interface ErrorContextType {
    errors: ErrorNotification[];
    addError: (error: Error | string, details?: string) => void;
    dismissError: (id: string) => void;
    clearAllErrors: () => void;
    hasErrors: boolean;
}

// Create context with default values
const ErrorContext = createContext<ErrorContextType>({
    errors: [],
    addError: () => { },
    dismissError: () => { },
    clearAllErrors: () => { },
    hasErrors: false,
});

// Props for the provider
interface ErrorProviderProps {
    children: ReactNode;
}

/**
 * Error Context Provider component that manages application-wide errors
 */
export const ErrorProvider: React.FC<ErrorProviderProps> = ({ children }) => {
    const [errors, setErrors] = useState<ErrorNotification[]>([]);

    // Generate a unique ID for each error
    const generateErrorId = useCallback((): string => {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }, []);

    // Add a new error
    const addError = useCallback((error: Error | string, details?: string) => {
        const message = typeof error === 'string' ? error : formatErrorMessage(error);

        // Log the error
        if (typeof error !== 'string') {
            logError(error instanceof Error ? error : new Error(message));
        }

        const newError: ErrorNotification = {
            id: generateErrorId(),
            message,
            details,
            timestamp: Date.now(),
        };

        setErrors(prevErrors => [...prevErrors, newError]);

        // Auto-dismiss error after 5 seconds
        setTimeout(() => {
            dismissError(newError.id);
        }, 5000);
    }, [generateErrorId]);

    // Dismiss a specific error
    const dismissError = useCallback((id: string) => {
        setErrors(prevErrors => prevErrors.filter(error => error.id !== id));
    }, []);

    // Clear all errors
    const clearAllErrors = useCallback(() => {
        setErrors([]);
    }, []);

    // Value for the context
    const contextValue: ErrorContextType = {
        errors,
        addError,
        dismissError,
        clearAllErrors,
        hasErrors: errors.length > 0,
    };

    return (
        <ErrorContext.Provider value={contextValue}>
            {children}

            {/* Render error toasts */}
            <div className="error-toast-container">
                {errors.map(error => (
                    <ErrorToast
                        key={error.id}
                        error={error}
                        onDismiss={() => dismissError(error.id)}
                    />
                ))}
            </div>
        </ErrorContext.Provider>
    );
};

/**
 * Custom hook to use the error context
 */
export const useError = (): ErrorContextType => {
    return useContext(ErrorContext);
};

export default ErrorContext; 