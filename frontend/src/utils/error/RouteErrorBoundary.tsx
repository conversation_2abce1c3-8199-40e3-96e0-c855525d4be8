import React from 'react';
import { useNavigate } from 'react-router-dom';
import ErrorBoundary from './ErrorBoundary';

interface RouteErrorBoundaryProps {
    children: React.ReactNode;
    routeName: string;
}

// Define a constant for development mode
const IS_DEV_MODE = process.env.NODE_ENV === 'development';

/**
 * Error boundary specific for routes
 * Includes navigation capabilities in the error UI
 */
const RouteErrorBoundary: React.FC<RouteErrorBoundaryProps> = ({ children, routeName }) => {
    const navigate = useNavigate();

    const handleReset = () => {
        // Refresh the current route
        navigate(0);
    };

    const handleGoHome = () => {
        // Navigate to home page
        navigate('/');
    };

    const Fallback = ({ error }: { error: Error | null }) => (
        <div className="route-error-fallback">
            <div className="error-container">
                <div className="error-icon">
                    <svg viewBox="0 0 24 24" width="36" height="36" fill="none" stroke="currentColor" strokeWidth="2">
                        <circle cx="12" cy="12" r="10" />
                        <line x1="12" y1="8" x2="12" y2="12" />
                        <line x1="12" y1="16" x2="12.01" y2="16" />
                    </svg>
                </div>

                <h2>Error in {routeName}</h2>

                <div className="error-details">
                    <p>We encountered an error while trying to display this page.</p>
                    {error && IS_DEV_MODE && (
                        <div className="error-message">
                            <p><strong>Error details (visible in development only):</strong></p>
                            <pre>{error.message}</pre>
                            <pre>{error.stack}</pre>
                        </div>
                    )}
                </div>

                <div className="error-actions">
                    <button
                        onClick={handleReset}
                        className="error-retry-button"
                    >
                        Retry
                    </button>
                    <button
                        onClick={handleGoHome}
                        className="error-home-button"
                    >
                        Go to Dashboard
                    </button>
                </div>
            </div>
        </div>
    );

    return (
        <ErrorBoundary
            fallback={<Fallback error={null} />}
            onError={(error) => {
                console.error(`Error in route ${routeName}:`, error);
            }}
        >
            {children}
        </ErrorBoundary>
    );
};

export default RouteErrorBoundary; 