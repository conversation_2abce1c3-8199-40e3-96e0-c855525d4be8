.error-fallback {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background-color: #f8f9fa;
  padding: 1rem;
}

.error-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  width: 100%;
  max-width: 600px;
  text-align: center;
}

.error-icon {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.error-container h2 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #333;
  font-weight: 600;
}

.error-details {
  margin-bottom: 1.5rem;
}

.error-details p {
  color: #666;
  margin-bottom: 1rem;
}

.error-message {
  text-align: left;
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 1rem;
  margin-top: 1rem;
  overflow: auto;
  max-height: 200px;
}

.error-message pre {
  margin: 0.5rem 0;
  font-size: 0.85rem;
  white-space: pre-wrap;
  word-break: break-word;
  color: #e74c3c;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.error-retry-button,
.error-home-button {
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.error-retry-button {
  background-color: #4A90E2;
  color: white;
}

.error-retry-button:hover {
  background-color: #3A80D2;
}

.error-home-button {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
}

.error-home-button:hover {
  background-color: #e5e5e5;
}

/* Form error message styles */
.form-error-message {
  background-color: #fdedeb;
  color: #e74c3c;
  border-radius: 4px;
  padding: 0.75rem 1rem;
  margin: 0.5rem 0 1rem;
  font-size: 0.875rem;
}

.form-error-message ul {
  margin: 0;
  padding-left: 1.5rem;
}

.form-error-message li {
  margin-bottom: 0.25rem;
}

.form-error-message li:last-child {
  margin-bottom: 0;
}

/* Field-level error message */
.field-error {
  color: #e74c3c;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Toast error notification styles */
.error-toast-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1100;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
}

.error-toast {
  background-color: #fff;
  color: #333;
  padding: 1rem;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1100;
  display: flex;
  align-items: flex-start;
  border-left: 4px solid #e74c3c;
  transition: all 0.3s ease-in-out;
  opacity: 0;
  transform: translateX(100%);
}

.error-toast.show {
  opacity: 1;
  transform: translateX(0);
}

.error-toast.hide {
  opacity: 0;
  transform: translateX(100%);
}

.error-toast-icon {
  color: #e74c3c;
  margin-right: 10px;
  flex-shrink: 0;
}

.error-toast-content {
  flex: 1;
}

.error-toast-title {
  font-weight: 600;
  margin-bottom: 5px;
}

.error-toast-message {
  font-size: 14px;
  margin: 0;
}

.error-toast-details {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.error-toast-close {
  background: transparent;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0;
  font-size: 18px;
  line-height: 1;
  margin-left: 10px;
}

.error-toast-close:hover {
  color: #333;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.error-toast.show {
  animation: slideIn 0.3s forwards;
}

.error-toast.hide {
  animation: slideOut 0.3s forwards;
}

@media (max-width: 768px) {
  .error-container {
    padding: 1.5rem;
  }
  
  .error-actions {
    flex-direction: column;
  }
  
  .error-toast-container {
    left: 20px;
    right: 20px;
    max-width: unset;
  }
}

/* Error boundary styling */
.error-boundary-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  margin: 1rem;
  border-radius: 8px;
  background-color: #fff3f3;
  border: 1px solid #ffcdd2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 0 auto;
}

/* Specialized styling for module errors */
.module-error {
  background-color: #fff8e1;
  border-color: #ffe082;
}

/* Specialized styling for network errors */
.network-error {
  background-color: #e8f5e9;
  border-color: #a5d6a7;
}

/* Error heading */
.error-boundary-container h3 {
  color: #d32f2f;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.module-error h3 {
  color: #ff8f00;
}

.network-error h3 {
  color: #2e7d32;
}

/* Error message */
.error-boundary-container p {
  color: #424242;
  margin-bottom: 1.5rem;
  text-align: center;
  max-width: 600px;
}

/* Technical details */
.error-details {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  width: 100%;
  max-width: 600px;
  margin-bottom: 1.5rem;
  font-family: monospace;
  font-size: 0.9rem;
  color: #616161;
  max-height: 200px;
  overflow-y: auto;
  display: none; /* Hidden by default */
}

/* Show details toggle */
.show-details-toggle {
  background: none;
  border: none;
  color: #2196f3;
  cursor: pointer;
  text-decoration: underline;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.show-details-toggle:hover {
  color: #0d47a1;
}

/* Retry button */
.retry-button {
  background-color: #2196f3;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #1976d2;
}

.module-error .retry-button {
  background-color: #ff9800;
}

.module-error .retry-button:hover {
  background-color: #f57c00;
}

.network-error .retry-button {
  background-color: #4caf50;
}

.network-error .retry-button:hover {
  background-color: #388e3c;
}

/* Home button */
.home-button {
  background-color: transparent;
  color: #757575;
  border: 1px solid #bdbdbd;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  margin-left: 1rem;
  transition: all 0.2s;
}

.home-button:hover {
  background-color: #eeeeee;
  color: #424242;
}

/* Container for buttons */
.error-actions {
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 1rem;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .error-boundary-container {
    padding: 1.5rem;
    margin: 0.5rem;
  }
  
  .error-actions {
    flex-direction: column;
  }
  
  .home-button {
    margin-left: 0;
    margin-top: 0.5rem;
  }
}

/* Special styling for 404 errors */
.not-found-container {
  text-align: center;
  padding: 3rem 1rem;
}

.not-found-container h1 {
  font-size: 6rem;
  margin-bottom: 0;
  color: #d32f2f;
}

.not-found-container h2 {
  margin-top: 0.5rem;
  color: #616161;
}

.not-found-container p {
  margin: 1.5rem 0;
  color: #757575;
}

/* Animation for error boundaries when they appear */
@keyframes errorAppear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-boundary-container {
  animation: errorAppear 0.3s ease-out;
} 