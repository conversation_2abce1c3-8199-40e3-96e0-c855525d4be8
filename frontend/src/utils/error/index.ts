// Export all error handling utilities
export { default as ErrorBoundary } from './ErrorBoundary';
export { ErrorProvider, useError } from './ErrorContext';
export type { ErrorNotification } from './ErrorContext';
export { default as ErrorFallback } from './ErrorFallback';
export {
    ERROR_CATEGORY, createErrorReport,
    formatErrorMessage, logCompilationError, logModuleError, logWarning
} from './errorLogger';
export { default as ErrorToast } from './ErrorToast';
export { default as FormErrorMessage } from './FormErrorMessage';
export { default as LazyLoadErrorBoundary } from './LazyLoadErrorBoundary';
export { default as RouteErrorBoundary } from './RouteErrorBoundary';
export { default as useApiErrorHandler } from './useApiErrorHandler';

// Define interface for Axios errors
interface AxiosError {
  isAxiosError: boolean;
  response?: {
    data?: any;
    status?: number;
    headers?: Record<string, string>;
  };
  request?: any;
  config?: any;
}

// Export additional utility functions
export const isAxiosError = (error: any): error is AxiosError => {
  return error && error.isAxiosError === true;
};

// Helper to extract error message from various error types
export const getErrorMessage = (error: unknown): string => {
  if (!error) {
    return 'An unknown error occurred';
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  if (error instanceof Error) {
    return error.message;
  }
  
  // Check if it's an Axios error with a response
  if (isAxiosError(error) && error.response?.data) {
    const data = error.response.data;
    return data.message || data.error || 'API request failed';
  }
  
  return 'An unexpected error occurred';
}; 