import React, { useEffect, useState } from 'react';
import { ErrorNotification } from './ErrorContext';
import './ErrorStyles.css';

interface ErrorToastProps {
    error: ErrorNotification;
    onDismiss: () => void;
}

/**
 * Error Toast component for displaying error notifications
 */
const ErrorToast: React.FC<ErrorToastProps> = ({ error, onDismiss }) => {
    const [isVisible, setIsVisible] = useState(true);

    // Handle animation when dismissing
    const handleDismiss = () => {
        setIsVisible(false);
        setTimeout(onDismiss, 300); // Wait for animation to complete
    };

    // Auto-dismiss after 5 seconds
    useEffect(() => {
        const timer = setTimeout(() => {
            setIsVisible(false);
            setTimeout(onDismiss, 300);
        }, 5000);

        return () => {
            clearTimeout(timer);
        };
    }, [onDismiss]);

    return (
        <div
            className={`error-toast ${isVisible ? 'show' : 'hide'}`}
            role="alert"
        >
            <div className="error-toast-icon">
                <svg viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="12" cy="12" r="10" />
                    <line x1="12" y1="8" x2="12" y2="12" />
                    <line x1="12" y1="16" x2="12.01" y2="16" />
                </svg>
            </div>

            <div className="error-toast-content">
                <div className="error-toast-title">Error</div>
                <p className="error-toast-message">{error.message}</p>
                {error.details && (
                    <p className="error-toast-details">{error.details}</p>
                )}
            </div>

            <button
                className="error-toast-close"
                onClick={handleDismiss}
                aria-label="Close error notification"
            >
                &times;
            </button>
        </div>
    );
};

export default ErrorToast; 