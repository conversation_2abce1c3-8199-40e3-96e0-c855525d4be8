import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../../contexts/LanguageContext';
import './CertificationCatalogPreview.css';

interface ICertification {
    id: number;
    name: string;
    provider: string;
    level: string;
    domain: string;
    type: string;
    description?: string;
    price?: number;
}

// Mock certifications data
const mockCertifications: ICertification[] = [
    {
        id: 1,
        name: 'CompTIA Security+',
        provider: 'CompTIA',
        level: 'Entry',
        domain: 'General Security',
        type: 'Vendor-Neutral',
        description: 'Establishes the core knowledge required of any cybersecurity role and provides a springboard to intermediate-level cybersecurity jobs.',
        price: 349
    },
    {
        id: 2,
        name: 'CEH (Certified Ethical Hacker)',
        provider: 'EC-Council',
        level: 'Intermediate',
        domain: 'Penetration Testing',
        type: 'Vendor-Neutral',
        description: 'A mid-level certification for security professionals who want to understand how to find weaknesses and vulnerabilities in target systems.',
        price: 1199
    },
    {
        id: 3,
        name: 'AWS Certified Security - Specialty',
        provider: 'Amazon Web Services',
        level: 'Advanced',
        domain: 'Cloud Security',
        type: 'Vendor-Specific',
        description: 'Validates expertise in security best practices and how to leverage AWS services to increase security posture.',
        price: 300
    },
    {
        id: 4,
        name: 'CISSP',
        provider: 'ISC²',
        level: 'Advanced',
        domain: 'Security Management',
        type: 'Vendor-Neutral',
        description: 'A globally recognized standard of achievement that confirms an individual\'s knowledge in the field of information security.',
        price: 749
    },
    {
        id: 5,
        name: 'GPEN (GIAC Penetration Tester)',
        provider: 'GIAC',
        level: 'Intermediate',
        domain: 'Penetration Testing',
        type: 'Vendor-Neutral',
        description: 'Validates a practitioner\'s ability to conduct penetration tests using best practice techniques and methodologies.',
        price: 1899
    },
    {
        id: 6,
        name: 'Microsoft Certified: Security, Compliance, and Identity Fundamentals',
        provider: 'Microsoft',
        level: 'Entry',
        domain: 'Cloud Security',
        type: 'Vendor-Specific',
        description: 'Demonstrates understanding of Microsoft security, compliance, and identity solutions.',
        price: 99
    }
];

interface CertificationCatalogPreviewProps {
    className?: string;
}

// Domain filter options
const domainFilters = ['All', 'General Security', 'Cloud Security', 'Penetration Testing', 'Security Management'];

// Level filter options  
const levelFilters = ['All', 'Entry', 'Intermediate', 'Advanced'];

const CertificationCatalogPreview: React.FC<CertificationCatalogPreviewProps> = ({ className = '' }) => {
    const { translate } = useLanguage();
    const [selectedDomain, setSelectedDomain] = useState<string>('All');
    const [selectedLevel, setSelectedLevel] = useState<string>('All');

    // Filter certifications based on selected filters
    const filteredCertifications = mockCertifications.filter(cert => {
        const domainMatch = selectedDomain === 'All' || cert.domain === selectedDomain;
        const levelMatch = selectedLevel === 'All' || cert.level === selectedLevel;
        return domainMatch && levelMatch;
    });

    return (
        <div className={`certification-catalog-preview ${className}`}>
            <div className="certification-preview-filters">
                <div className="filter-group">
                    <span className="filter-label">{translate('certifications.preview.domain', 'Domain')}:</span>
                    <div className="filter-buttons">
                        {domainFilters.map(domain => (
                            <button
                                key={domain}
                                className={`filter-button ${selectedDomain === domain ? 'active' : ''}`}
                                onClick={() => setSelectedDomain(domain)}
                            >
                                {translate(`certifications.domain.${domain.toLowerCase().replace(/\s+/g, '_')}`, domain)}
                            </button>
                        ))}
                    </div>
                </div>

                <div className="filter-group">
                    <span className="filter-label">{translate('certifications.preview.level', 'Level')}:</span>
                    <div className="filter-buttons">
                        {levelFilters.map(level => (
                            <button
                                key={level}
                                className={`filter-button ${selectedLevel === level ? 'active' : ''}`}
                                onClick={() => setSelectedLevel(level)}
                            >
                                {translate(`certifications.level.${level.toLowerCase()}`, level)}
                            </button>
                        ))}
                    </div>
                </div>
            </div>

            <div className="certification-preview-grid">
                {filteredCertifications.map(cert => (
                    <div className="preview-cert-card" key={cert.id}>
                        <div className="preview-cert-header">
                            <h4 className="preview-cert-name">{cert.name}</h4>
                            <span className="preview-cert-provider">{cert.provider}</span>
                        </div>

                        <div className="preview-cert-meta">
                            <span className={`preview-cert-domain domain-${cert.domain.toLowerCase().replace(/\s+/g, '-')}`}>
                                {translate(`certifications.domain.${cert.domain.toLowerCase().replace(/\s+/g, '_')}`, cert.domain)}
                            </span>
                            <span className={`preview-cert-level level-${cert.level.toLowerCase()}`}>
                                {translate(`certifications.level.${cert.level.toLowerCase()}`, cert.level)}
                            </span>
                        </div>

                        {cert.price && (
                            <div className="preview-cert-price">
                                ${cert.price}
                            </div>
                        )}
                    </div>
                ))}
            </div>

            <div className="certification-preview-cta">
                <Link to="/certification-explorer" className="preview-explore-button">
                    {translate('certifications.preview.explore_all', 'Explore All Certifications')}
                </Link>
            </div>
        </div>
    );
};

export default CertificationCatalogPreview; 