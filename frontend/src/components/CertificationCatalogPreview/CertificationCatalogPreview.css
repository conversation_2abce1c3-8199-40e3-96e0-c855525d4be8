.certification-catalog-preview {
  background-color: var(--card-bg-color, #ffffff);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  overflow: hidden;
}

/* Filters section */
.certification-preview-filters {
  margin-bottom: 1.5rem;
}

.filter-group {
  margin-bottom: 1rem;
}

.filter-label {
  display: block;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-color, #333333);
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-button {
  background-color: var(--card-bg-color-secondary, #f8f9fa);
  border: 1px solid var(--border-color, #eaeaea);
  color: var(--text-color, #333333);
  font-size: 0.8rem;
  padding: 0.35rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-button:hover {
  background-color: var(--primary-color-light, #d4e6f7);
  border-color: var(--primary-color, #4a90e2);
}

.filter-button.active {
  background-color: var(--primary-color, #4a90e2);
  border-color: var(--primary-color, #4a90e2);
  color: white;
}

/* Grid of certification cards */
.certification-preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.preview-cert-card {
  background-color: var(--card-bg-color-secondary, #f8f9fa);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;
}

.preview-cert-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.preview-cert-header {
  margin-bottom: 0.75rem;
}

.preview-cert-name {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: var(--heading-color, #333333);
}

.preview-cert-provider {
  font-size: 0.8rem;
  color: var(--text-color-secondary, #666666);
}

.preview-cert-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.preview-cert-domain,
.preview-cert-level {
  font-size: 0.7rem;
  padding: 0.15rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
}

/* Domain badges */
.preview-cert-domain {
  background-color: var(--primary-color-light, #d4e6f7);
  color: var(--primary-color-dark, #3a7dd1);
}

.domain-general-security {
  background-color: #d4e6f7;
  color: #3a7dd1;
}

.domain-cloud-security {
  background-color: #d7f5e8;
  color: #2a9d8f;
}

.domain-penetration-testing {
  background-color: #fbe9e7;
  color: #d84315;
}

.domain-security-management {
  background-color: #e8e8fc;
  color: #5c6bc0;
}

/* Level badges */
.preview-cert-level {
  background-color: var(--warning-color-light, #fff8e1);
  color: var(--warning-color-dark, #f57c00);
}

.level-entry {
  background-color: #e8f5e9;
  color: #388e3c;
}

.level-intermediate {
  background-color: #fff8e1;
  color: #f57c00;
}

.level-advanced {
  background-color: #fbe9e7;
  color: #c62828;
}

/* Price */
.preview-cert-price {
  margin-top: auto;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-color, #4a90e2);
}

/* CTA section */
.certification-preview-cta {
  text-align: center;
  margin-top: 1rem;
}

.preview-explore-button {
  display: inline-block;
  background-color: var(--primary-color, #4a90e2);
  color: white;
  font-size: 0.95rem;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  text-decoration: none;
  transition: background-color 0.2s ease;
}

.preview-explore-button:hover {
  background-color: var(--primary-color-dark, #3a7dd1);
}

/* Dark mode adjustments */
[data-theme="dark"] .certification-catalog-preview {
  background-color: var(--card-bg-color, #2b2b2b);
}

[data-theme="dark"] .preview-cert-card {
  background-color: var(--card-bg-color-secondary, #222222);
}

[data-theme="dark"] .filter-button {
  background-color: var(--card-bg-color-secondary, #333333);
  border-color: var(--border-color, #444444);
  color: var(--text-color, #e0e0e0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .certification-catalog-preview {
    padding: 1rem;
  }
  
  .certification-preview-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  }
  
  .preview-cert-card {
    padding: 0.75rem;
  }
  
  .preview-cert-name {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .certification-preview-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .filter-buttons {
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 0.5rem;
    margin-bottom: 0.5rem;
    -webkit-overflow-scrolling: touch;
  }
  
  .filter-button {
    flex: 0 0 auto;
  }
} 