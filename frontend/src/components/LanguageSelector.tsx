import React, { useEffect, useRef, useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import './LanguageSelector.css';

const LanguageSelector: React.FC = () => {
    const { currentLanguage, supportedLanguages, changeLanguage } = useLanguage();
    const [isOpen, setIsOpen] = useState(false);
    const [isChanging, setIsChanging] = useState(false);
    const [loadingStatus, setLoadingStatus] = useState('');
    const dropdownRef = useRef<HTMLDivElement>(null);

    // Handle clicks outside to close dropdown
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Toggle dropdown menu
    const toggleDropdown = () => {
        setIsOpen(!isOpen);
    };

    // Handle language selection
    const handleSelectLanguage = (languageCode: string) => {
        if (languageCode === currentLanguage) {
            setIsOpen(false);
            return;
        }

        setIsChanging(true);
        setLoadingStatus(`Changing to ${supportedLanguages[languageCode as keyof typeof supportedLanguages].name}...`);

        // Slight delay to show loading state
        setTimeout(() => {
            try {
                changeLanguage(languageCode as keyof typeof supportedLanguages);
                setLoadingStatus('Language changed successfully');
                console.log(`Language changed to ${languageCode}`);
            } catch (error) {
                console.error('Error changing language:', error);
                setLoadingStatus(`Error: ${error}`);
            }

            setIsOpen(false);

            // Reset changing state after a delay
            setTimeout(() => {
                setIsChanging(false);
                setLoadingStatus('');
            }, 800);
        }, 300);
    };

    return (
        <div
            className={`language-selector ${isChanging ? 'changing' : ''}`}
            ref={dropdownRef}
            data-current-lang={currentLanguage}
            data-loading={isChanging ? 'true' : 'false'}
        >
            <button
                className="language-toggle"
                onClick={toggleDropdown}
                aria-label="Select language"
                aria-expanded={isOpen}
                disabled={isChanging}
            >
                {supportedLanguages[currentLanguage].flag}
            </button>
            {isOpen && (
                <div className="language-dropdown">
                    {Object.entries(supportedLanguages).map(([code, { name, flag }]) => (
                        <button
                            key={code}
                            className={`language-option ${code === currentLanguage ? 'active' : ''}`}
                            onClick={() => handleSelectLanguage(code)}
                        >
                            <span className="language-flag">{flag}</span>
                            <span className="language-name">{name}</span>
                        </button>
                    ))}
                </div>
            )}
        </div>
    );
};

export default LanguageSelector; 