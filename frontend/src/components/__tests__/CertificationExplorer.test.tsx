import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import { CertificationExplorer } from '../CertificationExplorer';

// Mock the API calls
jest.mock('../../api/certifications', () => ({
    fetchCertifications: jest.fn().mockResolvedValue([
        {
            id: 1,
            name: 'CompTIA Security+',
            type: 'security',
            level: 'intermediate',
            provider: 'CompTIA',
            price: 349,
            duration: '40 hours',
            passingScore: 750,
            description: 'A comprehensive security certification',
            prerequisites: ['CompTIA Network+']
        },
        {
            id: 2,
            name: 'AWS Certified Solutions Architect',
            type: 'cloud',
            level: 'advanced',
            provider: 'Amazon',
            price: 150,
            duration: '60 hours',
            passingScore: 720,
            description: 'Cloud architecture certification',
            prerequisites: []
        }
    ])
}));

const renderWithRouter = (component: React.ReactElement) => {
    return render(
        <BrowserRouter>
            {component}
        </BrowserRouter>
    );
};

describe('CertificationExplorer', () => {
    beforeEach(() => {
        // Clear all mocks before each test
        jest.clearAllMocks();
    });

    test('renders the certification explorer with initial state', async () => {
        renderWithRouter(<CertificationExplorer />);

        // Check for main elements
        expect(screen.getByTestId('certification-explorer')).toBeInTheDocument();
        expect(screen.getByTestId('explorer-title')).toHaveTextContent('Certification Explorer');
        expect(screen.getByTestId('filters-panel')).toBeInTheDocument();
        expect(screen.getByTestId('certifications-grid')).toBeInTheDocument();

        // Wait for certifications to load
        await waitFor(() => {
            expect(screen.getByTestId('certification-card-1')).toBeInTheDocument();
            expect(screen.getByTestId('certification-card-2')).toBeInTheDocument();
        });
    });

    test('filters certifications by type', async () => {
        renderWithRouter(<CertificationExplorer />);

        // Wait for initial load
        await waitFor(() => {
            expect(screen.getByTestId('certification-card-1')).toBeInTheDocument();
        });

        // Select security type filter
        const typeFilter = screen.getByTestId('type-filter');
        fireEvent.change(typeFilter, { target: { value: 'security' } });

        // Verify only security certifications are shown
        await waitFor(() => {
            expect(screen.getByTestId('certification-card-1')).toBeInTheDocument();
            expect(screen.queryByTestId('certification-card-2')).not.toBeInTheDocument();
        });
    });

    test('filters certifications by level', async () => {
        renderWithRouter(<CertificationExplorer />);

        // Wait for initial load
        await waitFor(() => {
            expect(screen.getByTestId('certification-card-1')).toBeInTheDocument();
        });

        // Select intermediate level filter
        const levelFilter = screen.getByTestId('level-filter');
        fireEvent.change(levelFilter, { target: { value: 'intermediate' } });

        // Verify only intermediate certifications are shown
        await waitFor(() => {
            expect(screen.getByTestId('certification-card-1')).toBeInTheDocument();
            expect(screen.queryByTestId('certification-card-2')).not.toBeInTheDocument();
        });
    });

    test('searches certifications', async () => {
        renderWithRouter(<CertificationExplorer />);

        // Wait for initial load
        await waitFor(() => {
            expect(screen.getByTestId('certification-card-1')).toBeInTheDocument();
        });

        // Enter search term
        const searchInput = screen.getByTestId('search-input');
        fireEvent.change(searchInput, { target: { value: 'Security+' } });

        // Verify only matching certifications are shown
        await waitFor(() => {
            expect(screen.getByTestId('certification-card-1')).toBeInTheDocument();
            expect(screen.queryByTestId('certification-card-2')).not.toBeInTheDocument();
        });
    });

    test('sorts certifications', async () => {
        renderWithRouter(<CertificationExplorer />);

        // Wait for initial load
        await waitFor(() => {
            expect(screen.getByTestId('certification-card-1')).toBeInTheDocument();
        });

        // Sort by name ascending
        const sortSelect = screen.getByTestId('sort-select');
        fireEvent.change(sortSelect, { target: { value: 'name-asc' } });

        // Get certification titles
        const titles = screen.getAllByTestId(/^certification-title-/);
        const titlesText = titles.map(title => title.textContent);

        // Verify ascending order
        expect(titlesText).toEqual([...titlesText].sort());

        // Sort by price descending
        fireEvent.change(sortSelect, { target: { value: 'price-desc' } });

        // Get prices
        const prices = screen.getAllByTestId(/^certification-price-/);
        const pricesText = prices.map(price =>
            parseFloat(price.textContent?.replace(/[^0-9.-]+/g, '') || '0')
        );

        // Verify descending order
        expect(pricesText).toEqual([...pricesText].sort((a, b) => b - a));
    });

    test('displays certification details', async () => {
        renderWithRouter(<CertificationExplorer />);

        // Wait for initial load
        await waitFor(() => {
            expect(screen.getByTestId('certification-card-1')).toBeInTheDocument();
        });

        // Click on a certification card
        fireEvent.click(screen.getByTestId('certification-card-1'));

        // Verify details are displayed
        await waitFor(() => {
            expect(screen.getByTestId('certification-details')).toBeInTheDocument();
            expect(screen.getByTestId('certification-name')).toHaveTextContent('CompTIA Security+');
            expect(screen.getByTestId('certification-type')).toHaveTextContent('security');
            expect(screen.getByTestId('certification-level')).toHaveTextContent('intermediate');
            expect(screen.getByTestId('certification-price')).toHaveTextContent('$349');
        });
    });

    test('handles keyboard navigation', async () => {
        renderWithRouter(<CertificationExplorer />);

        // Wait for initial load
        await waitFor(() => {
            expect(screen.getByTestId('certification-card-1')).toBeInTheDocument();
        });

        // Focus search input
        const searchInput = screen.getByTestId('search-input');
        searchInput.focus();

        // Tab through filters
        userEvent.tab();
        expect(screen.getByTestId('type-filter')).toHaveFocus();

        userEvent.tab();
        expect(screen.getByTestId('level-filter')).toHaveFocus();

        // Test filter selection with keyboard
        userEvent.keyboard('{enter}');
        userEvent.keyboard('{arrowdown}');
        userEvent.keyboard('{enter}');

        // Verify filter was applied
        expect(screen.getByTestId('level-filter')).toHaveValue('intermediate');
    });

    test('handles loading state', async () => {
        // Mock a slow API response
        jest.spyOn(global, 'fetch').mockImplementationOnce(() =>
            new Promise(resolve => setTimeout(resolve, 100))
        );

        renderWithRouter(<CertificationExplorer />);

        // Verify loading state is shown
        expect(screen.getByTestId('loading-state')).toBeInTheDocument();
        expect(screen.getByTestId('loading-state')).toHaveTextContent('Loading certifications...');

        // Wait for content to load
        await waitFor(() => {
            expect(screen.queryByTestId('loading-state')).not.toBeInTheDocument();
            expect(screen.getByTestId('certification-card-1')).toBeInTheDocument();
        });
    });

    test('handles error state', async () => {
        // Mock an API error
        jest.spyOn(global, 'fetch').mockImplementationOnce(() =>
            Promise.reject(new Error('Failed to fetch'))
        );

        renderWithRouter(<CertificationExplorer />);

        // Verify error message is shown
        await waitFor(() => {
            expect(screen.getByTestId('error-message')).toBeInTheDocument();
            expect(screen.getByTestId('error-message')).toHaveTextContent('Failed to load certifications');
        });
    });
}); 