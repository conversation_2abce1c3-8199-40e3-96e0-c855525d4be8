import { screen, waitFor } from '@testing-library/react';
import { rest } from 'msw';
import { server } from '../../mocks/server';
import { render } from '../../utils/test-utils';
import ApiExample from '../ApiExample';

// Mock the useApi hook
jest.mock('../../hooks/useApi', () => ({
    __esModule: true,
    default: () => ({
        request: jest.fn().mockImplementation(async () => ({
            status: 'healthy',
            database: 'connected',
            anthropic_client: 'ready',
            api_version: 'v1',
            timestamp: '2023-03-21T12:00:00Z'
        })),
        getEndpoint: (path: string) => `/api/v1/${path}`,
        endpoints: {
            HEALTH: '/api/v1/health'
        },
        apiVersion: 'v1'
    })
}));

describe('ApiExample Component', () => {
    test('renders API information', async () => {
        render(<ApiExample />);

        // Check for heading
        expect(screen.getByText('API Example')).toBeInTheDocument();

        // Check for version information
        expect(screen.getByText(/Current API Version:/i)).toBeInTheDocument();
        expect(screen.getByText('v1')).toBeInTheDocument();

        // Wait for the loading state to disappear and data to appear
        await waitFor(() => {
            expect(screen.queryByText('Loading API status...')).not.toBeInTheDocument();
        });

        // Check health status was loaded and displayed
        expect(screen.getByText('API Health Status')).toBeInTheDocument();
        expect(screen.getByText('Status:')).toBeInTheDocument();
        expect(screen.getByText('healthy')).toBeInTheDocument();
        expect(screen.getByText('Database:')).toBeInTheDocument();
        expect(screen.getByText('connected')).toBeInTheDocument();
    });

    test('shows error message when API request fails', async () => {
        // Override the default mock for this specific test
        server.use(
            rest.get('/api/v1/health', (req, res, ctx) => {
                return res(ctx.status(500));
            })
        );

        // Use a mock that throws an error for this specific test
        jest.spyOn(console, 'error').mockImplementation(() => { });
        jest.mock('../../hooks/useApi', () => ({
            __esModule: true,
            default: () => ({
                request: jest.fn().mockRejectedValue(new Error('API Error')),
                getEndpoint: (path: string) => `/api/v1/${path}`,
                endpoints: {
                    HEALTH: '/api/v1/health'
                },
                apiVersion: 'v1'
            })
        }));

        render(<ApiExample />);

        // Wait for the error message to appear
        await waitFor(() => {
            const errorMessage = screen.getByText('Failed to fetch API health status');
            expect(errorMessage).toBeInTheDocument();
        });

        // Restore console.error
        jest.restoreAllMocks();
    });
}); 