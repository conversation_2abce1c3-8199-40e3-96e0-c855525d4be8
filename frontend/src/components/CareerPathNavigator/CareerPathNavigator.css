.career-path-navigator {
  background-color: var(--card-bg-color, #ffffff);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  overflow: hidden;
}

.career-path-visualization {
  margin-bottom: 1.5rem;
  width: 100%;
  background-color: var(--card-bg-color-secondary, #f8f9fa);
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.path-visualization {
  width: 100%;
  height: 300px;
  overflow: visible;
}

/* D3 visualization elements */
.path-link {
  stroke: var(--text-color-secondary, #999999);
  stroke-opacity: 0.6;
  transition: stroke-opacity 0.3s ease;
}

.path-node {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.path-node:hover {
  transform: scale(1.1);
}

.path-node:hover .node-text {
  font-weight: bold;
}

.node-entry circle {
  stroke: var(--primary-color, #4a90e2);
  stroke-width: 2px;
}

.node-mid circle {
  stroke: var(--success-color, #7cb342);
  stroke-width: 2px;
}

.node-advanced circle {
  stroke: var(--warning-color, #e6974d);
  stroke-width: 2px;
}

.node-text {
  fill: var(--text-color, #333333);
  font-size: 12px;
  pointer-events: none;
}

.level-label {
  fill: var(--text-color, #333333);
  font-size: 14px;
  font-weight: bold;
}

/* Popular paths section */
.popular-paths {
  margin-top: 1rem;
}

.paths-heading {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--heading-color, #333333);
  text-align: center;
}

.paths-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1rem;
}

.path-card {
  background-color: var(--card-bg-color-secondary, #f8f9fa);
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.path-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.path-title {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: var(--heading-color, #333333);
}

.path-description {
  font-size: 0.9rem;
  color: var(--text-color-secondary, #666666);
  margin-bottom: 0.75rem;
}

.path-certifications {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.path-cert-badge {
  background-color: var(--primary-color-light, #d4e6f7);
  color: var(--primary-color-dark, #3a7dd1);
  font-size: 0.8rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.path-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  border-top: 1px solid var(--border-color, #eaeaea);
  padding-top: 0.75rem;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.metric-value {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--primary-color, #4a90e2);
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.75rem;
  color: var(--text-color-secondary, #666666);
}

/* Dark mode adjustments */
[data-theme="dark"] .career-path-navigator {
  background-color: var(--card-bg-color, #2b2b2b);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

[data-theme="dark"] .career-path-visualization,
[data-theme="dark"] .path-card {
  background-color: var(--card-bg-color-secondary, #222222);
}

[data-theme="dark"] .node-text,
[data-theme="dark"] .level-label {
  fill: var(--text-color, #e0e0e0);
}

[data-theme="dark"] .path-link {
  stroke: var(--text-color-secondary, #777777);
}

[data-theme="dark"] .path-cert-badge {
  background-color: var(--primary-color-dark, #3a7dd1);
  color: #ffffff;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .career-path-navigator {
    padding: 1rem;
  }
  
  .path-visualization {
    height: 250px;
  }
  
  .paths-list {
    grid-template-columns: 1fr;
  }
  
  .level-label {
    font-size: 12px;
  }
  
  .node-text {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .path-visualization {
    height: 200px;
  }
  
  .path-metrics {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
} 