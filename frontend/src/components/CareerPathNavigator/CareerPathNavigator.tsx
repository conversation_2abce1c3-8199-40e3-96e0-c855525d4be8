import * as d3 from 'd3';
import React, { useEffect, useRef } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import './CareerPathNavigator.css';

interface CareerPathNavigatorProps {
    className?: string;
}

interface PathNode {
    id: string;
    level: string;
    title: string;
    color: string;
    size: number;
    certifications: string[];
}

const CareerPathNavigator: React.FC<CareerPathNavigatorProps> = ({ className = '' }) => {
    const { translate } = useLanguage();
    const svgRef = useRef<SVGSVGElement>(null);

    // Mock data for popular career paths
    const popularPaths = [
        {
            title: translate('careerPath.path.security_analyst', 'Security Analyst Path'),
            description: translate('careerPath.path.security_analyst_desc', 'Start your journey with fundamental security certifications'),
            certifications: ['Security+', 'CySA+', 'CASP+'],
            metrics: {
                avgSalary: '$95,000',
                jobGrowth: '33%',
                timeToComplete: '14 months'
            }
        },
        {
            title: translate('careerPath.path.cloud_security', 'Cloud Security Path'),
            description: translate('careerPath.path.cloud_security_desc', 'Specialize in securing cloud environments'),
            certifications: ['CompTIA Cloud+', 'AWS Security Specialty', 'CCSP'],
            metrics: {
                avgSalary: '$115,000',
                jobGrowth: '28%',
                timeToComplete: '18 months'
            }
        },
        {
            title: translate('careerPath.path.pen_testing', 'Penetration Testing Path'),
            description: translate('careerPath.path.pen_testing_desc', 'Learn offensive security and ethical hacking'),
            certifications: ['Security+', 'PenTest+', 'OSCP'],
            metrics: {
                avgSalary: '$103,000',
                jobGrowth: '25%',
                timeToComplete: '16 months'
            }
        }
    ];

    // Create D3 visualization
    useEffect(() => {
        if (!svgRef.current) return;

        // Career path nodes
        const nodes: PathNode[] = [
            { id: 'entry1', level: 'entry', title: 'Security+', color: '#4a90e2', size: 40, certifications: ['Security+'] },
            { id: 'entry2', level: 'entry', title: 'SSCP', color: '#4a90e2', size: 35, certifications: ['SSCP'] },
            { id: 'entry3', level: 'entry', title: 'Network+', color: '#4a90e2', size: 30, certifications: ['Network+'] },

            { id: 'mid1', level: 'mid', title: 'CySA+', color: '#7cb342', size: 45, certifications: ['CySA+'] },
            { id: 'mid2', level: 'mid', title: 'PenTest+', color: '#7cb342', size: 40, certifications: ['PenTest+'] },
            { id: 'mid3', level: 'mid', title: 'CEH', color: '#7cb342', size: 38, certifications: ['CEH'] },

            { id: 'advanced1', level: 'advanced', title: 'CISSP', color: '#e6974d', size: 50, certifications: ['CISSP'] },
            { id: 'advanced2', level: 'advanced', title: 'OSCP', color: '#e6974d', size: 45, certifications: ['OSCP'] },
            { id: 'advanced3', level: 'advanced', title: 'CASP+', color: '#e6974d', size: 40, certifications: ['CASP+'] },
        ];

        // Define links between nodes
        const links = [
            { source: 'entry1', target: 'mid1' },
            { source: 'entry1', target: 'mid2' },
            { source: 'entry2', target: 'mid1' },
            { source: 'entry2', target: 'mid3' },
            { source: 'entry3', target: 'mid2' },
            { source: 'mid1', target: 'advanced1' },
            { source: 'mid1', target: 'advanced3' },
            { source: 'mid2', target: 'advanced2' },
            { source: 'mid3', target: 'advanced1' },
        ];

        // Clear previous visualization
        d3.select(svgRef.current).selectAll('*').remove();

        // Set up SVG dimensions
        const width = svgRef.current.clientWidth;
        const height = 300;
        const svg = d3.select(svgRef.current)
            .attr('width', width)
            .attr('height', height);

        // Create a force simulation
        const simulation = d3.forceSimulation()
            .force('link', d3.forceLink().id((d: any) => d.id).distance(100))
            .force('charge', d3.forceManyBody().strength(-300))
            .force('center', d3.forceCenter(width / 2, height / 2))
            .force('x', d3.forceX().x((d: any) => {
                // Position nodes by level
                if (d.level === 'entry') return width * 0.2;
                if (d.level === 'mid') return width * 0.5;
                return width * 0.8;
            }).strength(0.5))
            .force('y', d3.forceY().y(height / 2).strength(0.1))
            .force('collision', d3.forceCollide().radius((d: any) => d.size + 10));

        // Create links
        const link = svg.append('g')
            .attr('class', 'links')
            .selectAll('line')
            .data(links)
            .enter()
            .append('line')
            .attr('class', 'path-link')
            .attr('stroke-width', 2);

        // Create node groups
        const node = svg.append('g')
            .attr('class', 'nodes')
            .selectAll('g')
            .data(nodes)
            .enter()
            .append('g')
            .attr('class', 'path-node')
            .call(d3.drag<SVGGElement, PathNode>()
                .on('start', dragstarted)
                .on('drag', dragged)
                .on('end', dragended) as any);

        // Add circles to nodes
        node.append('circle')
            .attr('r', (d: PathNode) => d.size / 2)
            .attr('fill', (d: PathNode) => d.color)
            .attr('class', (d: PathNode) => `node-${d.level}`);

        // Add text labels to nodes
        node.append('text')
            .text((d: PathNode) => d.title)
            .attr('text-anchor', 'middle')
            .attr('dy', '.35em')
            .attr('class', 'node-text');

        // Set up node titles (tooltips)
        node.append('title')
            .text((d: PathNode) => d.title);

        // Update node and link positions on simulation tick
        simulation.nodes(nodes as any).on('tick', () => {
            link
                .attr('x1', (d: any) => d.source.x)
                .attr('y1', (d: any) => d.source.y)
                .attr('x2', (d: any) => d.target.x)
                .attr('y2', (d: any) => d.target.y);

            node
                .attr('transform', (d: any) => `translate(${d.x},${d.y})`);
        });

        // Set up drag functions
        function dragstarted(event: any, d: any) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event: any, d: any) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event: any, d: any) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }

        // Add level labels
        const levelLabels = [
            { level: 'entry', x: width * 0.2, y: 25, text: translate('careerPath.level.entry', 'Entry Level') },
            { level: 'mid', x: width * 0.5, y: 25, text: translate('careerPath.level.mid', 'Mid Level') },
            { level: 'advanced', x: width * 0.8, y: 25, text: translate('careerPath.level.advanced', 'Advanced') }
        ];

        svg.selectAll('.level-label')
            .data(levelLabels)
            .enter()
            .append('text')
            .attr('class', 'level-label')
            .attr('x', (d) => d.x)
            .attr('y', (d) => d.y)
            .attr('text-anchor', 'middle')
            .text((d) => d.text);

    }, [translate]);

    return (
        <div className={`career-path-navigator ${className}`}>
            <div className="career-path-visualization">
                <svg ref={svgRef} className="path-visualization"></svg>
            </div>

            <div className="popular-paths">
                <h3 className="paths-heading">{translate('careerPath.popular', 'Popular Career Paths')}</h3>
                <div className="paths-list">
                    {popularPaths.map((path, index) => (
                        <div className="path-card" key={index}>
                            <h4 className="path-title">{path.title}</h4>
                            <p className="path-description">{path.description}</p>
                            <div className="path-certifications">
                                {path.certifications.map((cert, idx) => (
                                    <span className="path-cert-badge" key={idx}>{cert}</span>
                                ))}
                            </div>
                            <div className="path-metrics">
                                <div className="metric">
                                    <span className="metric-value">{path.metrics.avgSalary}</span>
                                    <span className="metric-label">{translate('careerPath.metrics.salary', 'Avg. Salary')}</span>
                                </div>
                                <div className="metric">
                                    <span className="metric-value">{path.metrics.jobGrowth}</span>
                                    <span className="metric-label">{translate('careerPath.metrics.growth', 'Job Growth')}</span>
                                </div>
                                <div className="metric">
                                    <span className="metric-value">{path.metrics.timeToComplete}</span>
                                    <span className="metric-label">{translate('careerPath.metrics.time', 'Est. Time')}</span>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default CareerPathNavigator; 