import React, { useState } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import './SuccessStories.css';

interface SuccessStoriesProps {
    className?: string;
}

interface Testimonial {
    id: number;
    name: string;
    role: string;
    company: string;
    avatar: string;
    quote: string;
    certifications: string[];
    stats: {
        salaryIncrease: string;
        timeToCompletion: string;
        jobOffers: number;
    };
    progression: {
        before: {
            role: string;
            salary: string;
        };
        after: {
            role: string;
            salary: string;
        };
    };
}

// Mock testimonial data
const testimonials: Testimonial[] = [
    {
        id: 1,
        name: "<PERSON>",
        role: "Senior Security Analyst",
        company: "TechDefend Solutions",
        avatar: "👩‍💼",
        quote: "CertRats helped me map out exactly which certifications I needed to advance my career. The ROI calculator was spot on - I saw a 30% salary increase after completing the recommended certs!",
        certifications: ["Security+", "CySA+", "CISSP"],
        stats: {
            salaryIncrease: "+30%",
            timeToCompletion: "14 months",
            jobOffers: 5
        },
        progression: {
            before: {
                role: "Junior Security Analyst",
                salary: "$65,000"
            },
            after: {
                role: "Senior Security Analyst",
                salary: "$95,000"
            }
        }
    },
    {
        id: 2,
        name: "Michael Chen",
        role: "Cloud Security Architect",
        company: "CloudGuard Enterprise",
        avatar: "👨‍💻",
        quote: "I was overwhelmed by the number of cloud security certifications available. CertRats narrowed it down to the three that actually mattered for my career goals, saving me time and money.",
        certifications: ["AWS Certified Security", "CCSP", "Azure Security Engineer"],
        stats: {
            salaryIncrease: "+42%",
            timeToCompletion: "18 months",
            jobOffers: 7
        },
        progression: {
            before: {
                role: "Systems Administrator",
                salary: "$78,000"
            },
            after: {
                role: "Cloud Security Architect",
                salary: "$135,000"
            }
        }
    },
    {
        id: 3,
        name: "Aisha Patel",
        role: "Penetration Tester",
        company: "SecurityBreak Consulting",
        avatar: "👩‍💻",
        quote: "The study time estimator and structured roadmap kept me on track. I was able to earn my certifications while working full-time, and landed my dream pentesting job shortly after!",
        certifications: ["Security+", "PenTest+", "OSCP"],
        stats: {
            salaryIncrease: "+35%",
            timeToCompletion: "16 months",
            jobOffers: 4
        },
        progression: {
            before: {
                role: "IT Support Specialist",
                salary: "$62,000"
            },
            after: {
                role: "Penetration Tester",
                salary: "$98,000"
            }
        }
    }
];

const SuccessStories: React.FC<SuccessStoriesProps> = ({ className = '' }) => {
    const { translate } = useLanguage();
    const [activeIndex, setActiveIndex] = useState(0);

    const nextTestimonial = () => {
        setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
    };

    const prevTestimonial = () => {
        setActiveIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);
    };

    const setTestimonialIndex = (index: number) => {
        setActiveIndex(index);
    };

    return (
        <div className={`success-stories ${className}`}>
            <div className="testimonial-carousel">
                <div className="testimonials-container" style={{ transform: `translateX(-${activeIndex * 100}%)` }}>
                    {testimonials.map((testimonial) => (
                        <div className="testimonial" key={testimonial.id}>
                            <div className="testimonial-content">
                                <div className="testimonial-header">
                                    <div className="testimonial-avatar">{testimonial.avatar}</div>
                                    <div className="testimonial-author">
                                        <h4>{testimonial.name}</h4>
                                        <p>{testimonial.role} at {testimonial.company}</p>
                                    </div>
                                </div>

                                <blockquote className="testimonial-quote">
                                    "{testimonial.quote}"
                                </blockquote>

                                <div className="testimonial-certifications">
                                    <h5>{translate('success.certifications', 'Certifications Earned')}:</h5>
                                    <div className="certification-badges">
                                        {testimonial.certifications.map((cert, index) => (
                                            <span className="cert-badge" key={index}>{cert}</span>
                                        ))}
                                    </div>
                                </div>
                            </div>

                            <div className="career-progression">
                                <h5>{translate('success.progression', 'Career Progression')}</h5>

                                <div className="progression-container">
                                    <div className="progression-stage before">
                                        <div className="stage-label">{translate('success.before', 'Before')}</div>
                                        <div className="stage-role">{testimonial.progression.before.role}</div>
                                        <div className="stage-salary">{testimonial.progression.before.salary}</div>
                                    </div>

                                    <div className="progression-arrow">
                                        <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round">
                                            <line x1="5" y1="12" x2="19" y2="12"></line>
                                            <polyline points="12 5 19 12 12 19"></polyline>
                                        </svg>
                                    </div>

                                    <div className="progression-stage after">
                                        <div className="stage-label">{translate('success.after', 'After')}</div>
                                        <div className="stage-role">{testimonial.progression.after.role}</div>
                                        <div className="stage-salary">{testimonial.progression.after.salary}</div>
                                    </div>
                                </div>

                                <div className="success-metrics">
                                    <div className="metric">
                                        <div className="metric-value">{testimonial.stats.salaryIncrease}</div>
                                        <div className="metric-label">{translate('success.salary_increase', 'Salary Increase')}</div>
                                    </div>
                                    <div className="metric">
                                        <div className="metric-value">{testimonial.stats.timeToCompletion}</div>
                                        <div className="metric-label">{translate('success.completion_time', 'Completion Time')}</div>
                                    </div>
                                    <div className="metric">
                                        <div className="metric-value">{testimonial.stats.jobOffers}</div>
                                        <div className="metric-label">{translate('success.job_offers', 'Job Offers')}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                <button className="carousel-control prev" onClick={prevTestimonial} aria-label="Previous testimonial">
                    <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round">
                        <polyline points="15 18 9 12 15 6"></polyline>
                    </svg>
                </button>

                <button className="carousel-control next" onClick={nextTestimonial} aria-label="Next testimonial">
                    <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round">
                        <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                </button>
            </div>

            <div className="testimonial-indicators">
                {testimonials.map((_, index) => (
                    <button
                        key={index}
                        className={`indicator ${index === activeIndex ? 'active' : ''}`}
                        onClick={() => setTestimonialIndex(index)}
                        aria-label={`Go to testimonial ${index + 1}`}
                    />
                ))}
            </div>
        </div>
    );
};

export default SuccessStories; 