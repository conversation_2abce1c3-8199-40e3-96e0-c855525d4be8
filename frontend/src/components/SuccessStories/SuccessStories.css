.success-stories {
  background-color: var(--card-bg-color, #ffffff);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  overflow: hidden;
}

/* Testimonial carousel */
.testimonial-carousel {
  position: relative;
  overflow: hidden;
}

.testimonials-container {
  display: flex;
  transition: transform 0.5s ease;
  width: 100%;
}

.testimonial {
  flex: 0 0 100%;
  padding: 0 0.5rem;
  display: flex;
  flex-direction: column;
}

/* Testimonial content */
.testimonial-content {
  margin-bottom: 1.5rem;
}

.testimonial-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.testimonial-avatar {
  font-size: 2rem;
  margin-right: 1rem;
  background-color: var(--primary-color-light, #d4e6f7);
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.testimonial-author h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  color: var(--heading-color, #333333);
}

.testimonial-author p {
  margin: 0;
  font-size: 0.85rem;
  color: var(--text-color-secondary, #666666);
}

.testimonial-quote {
  margin: 0 0 1.5rem 0;
  padding: 0.5rem 0;
  font-style: italic;
  color: var(--text-color, #333333);
  position: relative;
}

.testimonial-quote::before,
.testimonial-quote::after {
  content: "";
  position: absolute;
  height: 1px;
  width: 20%;
  background-color: var(--border-color, #eaeaea);
}

.testimonial-quote::before {
  top: 0;
  left: 0;
}

.testimonial-quote::after {
  bottom: 0;
  right: 0;
}

.testimonial-certifications h5 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--heading-color, #333333);
}

.certification-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.cert-badge {
  background-color: var(--primary-color-light, #d4e6f7);
  color: var(--primary-color-dark, #3a7dd1);
  font-size: 0.8rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

/* Career progression */
.career-progression {
  background-color: var(--card-bg-color-secondary, #f8f9fa);
  border-radius: 8px;
  padding: 1rem;
}

.career-progression h5 {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: var(--heading-color, #333333);
  text-align: center;
}

.progression-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.progression-stage {
  flex: 1;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
}

.progression-stage.before {
  background-color: var(--card-bg-color, #ffffff);
}

.progression-stage.after {
  background-color: var(--success-color-light, #d7f5e8);
}

.stage-label {
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-color-secondary, #666666);
}

.stage-role {
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--heading-color, #333333);
}

.stage-salary {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-color, #4a90e2);
}

.progression-stage.after .stage-salary {
  color: var(--success-color, #7cb342);
}

.progression-arrow {
  display: flex;
  align-items: center;
  color: var(--text-color-secondary, #666666);
  margin: 0 0.5rem;
}

/* Success metrics */
.success-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.metric {
  text-align: center;
}

.metric-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-color, #4a90e2);
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.75rem;
  color: var(--text-color-secondary, #666666);
}

/* Carousel controls */
.carousel-control {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 2.5rem;
  height: 2.5rem;
  background-color: var(--card-bg-color, #ffffff);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
  color: var(--text-color, #333333);
  transition: background-color 0.2s ease;
}

.carousel-control:hover {
  background-color: var(--primary-color-light, #d4e6f7);
}

.carousel-control.prev {
  left: 0.5rem;
}

.carousel-control.next {
  right: 0.5rem;
}

/* Carousel indicators */
.testimonial-indicators {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.indicator {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background-color: var(--border-color, #eaeaea);
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  padding: 0;
}

.indicator.active {
  background-color: var(--primary-color, #4a90e2);
}

/* Dark mode adjustments */
[data-theme="dark"] .success-stories {
  background-color: var(--card-bg-color, #2b2b2b);
}

[data-theme="dark"] .career-progression {
  background-color: var(--card-bg-color-secondary, #222222);
}

[data-theme="dark"] .progression-stage.before {
  background-color: var(--card-bg-color, #2b2b2b);
}

[data-theme="dark"] .progression-stage.after {
  background-color: var(--success-color-dark, #22543d);
  color: #e0e0e0;
}

[data-theme="dark"] .carousel-control {
  background-color: var(--card-bg-color, #2b2b2b);
  color: var(--text-color, #e0e0e0);
}

[data-theme="dark"] .carousel-control:hover {
  background-color: var(--primary-color-dark, #3a7dd1);
}

[data-theme="dark"] .indicator {
  background-color: var(--border-color, #444444);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .success-stories {
    padding: 1rem;
  }
  
  .testimonial {
    flex-direction: column;
  }
  
  .testimonial-content {
    margin-bottom: 1rem;
  }
  
  .testimonial-quote {
    font-size: 0.9rem;
  }
  
  .testimonial-avatar {
    font-size: 1.5rem;
    width: 3rem;
    height: 3rem;
  }
  
  .progression-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .progression-arrow {
    transform: rotate(90deg);
    margin: 0.5rem 0;
  }
}

@media (max-width: 480px) {
  .success-metrics {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .testimonial-header {
    flex-direction: column;
    text-align: center;
  }
  
  .testimonial-avatar {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
  
  .carousel-control {
    width: 2rem;
    height: 2rem;
  }
} 