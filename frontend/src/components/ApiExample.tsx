import React, { useEffect, useState } from 'react';
import useApi from '../hooks/useApi';
import './ApiExample.css';

interface HealthStatus {
    status: string;
    database: string;
    anthropic_client: string;
    api_version: string;
    timestamp: string;
}

/**
 * Example component demonstrating API usage with versioning
 */
const ApiExample: React.FC = () => {
    const { request, getEndpoint, endpoints, apiVersion } = useApi();
    const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchHealthStatus = async () => {
            setIsLoading(true);
            setError(null);

            try {
                // Using the endpoints constant
                const health = await request<HealthStatus>({
                    method: 'GET',
                    url: endpoints.HEALTH,
                });

                setHealthStatus(health);
            } catch (err) {
                setError('Failed to fetch API health status');
                console.error(err);
            } finally {
                setIsLoading(false);
            }
        };

        fetchHealthStatus();
    }, [request, endpoints.HEALTH]);

    return (
        <div className="api-example">
            <h2>API Example</h2>
            <div className="api-info">
                <p>Current API Version: <strong>{apiVersion}</strong></p>
                <p>Health endpoint: <code>{getEndpoint('health')}</code></p>
            </div>

            {isLoading && <p>Loading API status...</p>}

            {error && (
                <div className="error-message">
                    <p>{error}</p>
                </div>
            )}

            {healthStatus && (
                <div className="health-status">
                    <h3>API Health Status</h3>
                    <ul>
                        <li>Status: <span className={healthStatus.status === 'healthy' ? 'status-good' : 'status-bad'}>{healthStatus.status}</span></li>
                        <li>Database: <span className={healthStatus.database === 'connected' ? 'status-good' : 'status-bad'}>{healthStatus.database}</span></li>
                        <li>Anthropic Client: <span className={healthStatus.anthropic_client === 'ready' ? 'status-good' : 'status-bad'}>{healthStatus.anthropic_client}</span></li>
                        <li>API Version: {healthStatus.api_version}</li>
                        <li>Timestamp: {new Date(healthStatus.timestamp).toLocaleString()}</li>
                    </ul>
                </div>
            )}

            <div className="endpoint-examples">
                <h3>Example Endpoints</h3>
                <p>These endpoints are automatically versioned:</p>
                <ul>
                    <li>Authentication: <code>{getEndpoint('auth/login')}</code></li>
                    <li>User Profile: <code>{getEndpoint('users/profile')}</code></li>
                    <li>Certifications: <code>{getEndpoint('certifications')}</code></li>
                </ul>
            </div>
        </div>
    );
};

export default ApiExample; 