import { ArcElement, BarElement, CategoryScale, Chart as ChartJ<PERSON>, Legend, LinearScale, Tooltip } from 'chart.js';
import React from 'react';
import { Bar, Doughnut } from 'react-chartjs-2';
import { useLanguage } from '../../contexts/LanguageContext';
import './DashboardPreview.css';

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement);

interface DashboardPreviewProps {
    className?: string;
}

const DashboardPreview: React.FC<DashboardPreviewProps> = ({ className = '' }) => {
    const { translate } = useLanguage();

    // Mock data for certification progress chart
    const progressData = {
        labels: [
            translate('dashboard.preview.completed', 'Completed'),
            translate('dashboard.preview.in_progress', 'In Progress'),
            translate('dashboard.preview.planned', 'Planned')
        ],
        datasets: [
            {
                data: [3, 2, 4],
                backgroundColor: [
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(153, 102, 255, 0.8)'
                ],
                borderColor: [
                    'rgba(75, 192, 192, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(153, 102, 255, 1)'
                ],
                borderWidth: 1,
            },
        ],
    };

    // Mock data for study hours chart
    const studyHoursData = {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [
            {
                label: translate('dashboard.preview.study_hours', 'Study Hours'),
                data: [2, 3, 1, 4, 2, 5, 3],
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }
        ]
    };

    // Mock ROI metrics
    const roiMetrics = [
        {
            label: translate('dashboard.preview.avg_salary_increase', 'Avg. Salary Increase'),
            value: '+$15,000',
            icon: '📈'
        },
        {
            label: translate('dashboard.preview.cost_savings', 'Training Cost Savings'),
            value: '$2,500',
            icon: '💰'
        },
        {
            label: translate('dashboard.preview.time_to_complete', 'Est. Time to Complete'),
            value: '4 months',
            icon: '⏱️'
        }
    ];

    return (
        <div className={`dashboard-preview ${className}`}>
            <div className="dashboard-preview-header">
                <h3>{translate('dashboard.preview.title', 'Dashboard Preview')}</h3>
                <p className="preview-subtitle">
                    {translate('dashboard.preview.subtitle', 'Track your certification journey with interactive metrics')}
                </p>
            </div>

            <div className="dashboard-preview-content">
                <div className="chart-container">
                    <div className="chart-box certification-progress">
                        <h4>{translate('dashboard.preview.certifications', 'Your Certifications')}</h4>
                        <div className="chart-wrapper">
                            <Doughnut
                                data={progressData}
                                options={{
                                    responsive: true,
                                    plugins: {
                                        legend: {
                                            position: 'bottom',
                                            labels: {
                                                font: {
                                                    size: 12
                                                }
                                            }
                                        }
                                    }
                                }}
                            />
                        </div>
                    </div>

                    <div className="chart-box study-progress">
                        <h4>{translate('dashboard.preview.study_progress', 'Study Progress')}</h4>
                        <div className="chart-wrapper">
                            <Bar
                                data={studyHoursData}
                                options={{
                                    responsive: true,
                                    scales: {
                                        y: {
                                            beginAtZero: true,
                                            title: {
                                                display: true,
                                                text: translate('dashboard.preview.hours', 'Hours')
                                            }
                                        },
                                        x: {
                                            title: {
                                                display: true,
                                                text: translate('dashboard.preview.day', 'Day')
                                            }
                                        }
                                    },
                                    plugins: {
                                        legend: {
                                            display: false
                                        }
                                    }
                                }}
                            />
                        </div>
                    </div>
                </div>

                <div className="metrics-container">
                    <h4>{translate('dashboard.preview.roi_metrics', 'ROI Metrics')}</h4>
                    <div className="metrics-grid">
                        {roiMetrics.map((metric, index) => (
                            <div className="metric-box" key={index}>
                                <div className="metric-icon">{metric.icon}</div>
                                <div className="metric-value">{metric.value}</div>
                                <div className="metric-label">{metric.label}</div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            <div className="dashboard-preview-actions">
                <button className="preview-action-button">
                    {translate('dashboard.preview.go_to_dashboard', 'Go to Dashboard')}
                </button>
            </div>
        </div>
    );
};

export default DashboardPreview; 