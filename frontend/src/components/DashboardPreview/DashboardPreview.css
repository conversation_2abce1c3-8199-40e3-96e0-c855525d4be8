.dashboard-preview {
  background-color: var(--card-bg-color, #ffffff);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-preview:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.dashboard-preview-header {
  margin-bottom: 1.5rem;
  text-align: center;
}

.dashboard-preview-header h3 {
  font-size: 1.6rem;
  margin-bottom: 0.5rem;
  color: var(--heading-color, #333333);
}

.preview-subtitle {
  font-size: 1rem;
  color: var(--text-color-secondary, #666666);
  margin-bottom: 0;
}

.dashboard-preview-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.chart-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.chart-box {
  background-color: var(--card-bg-color-secondary, #f8f9fa);
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.chart-box h4 {
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
  color: var(--heading-color, #333333);
  text-align: center;
}

.chart-wrapper {
  height: 200px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.metrics-container {
  background-color: var(--card-bg-color-secondary, #f8f9fa);
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.metrics-container h4 {
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
  color: var(--heading-color, #333333);
  text-align: center;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.metric-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 0.5rem;
  border-radius: 8px;
  background-color: var(--card-bg-color, #ffffff);
  transition: transform 0.2s ease;
}

.metric-box:hover {
  transform: translateY(-2px);
}

.metric-icon {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.metric-value {
  font-size: 1.3rem;
  font-weight: bold;
  color: var(--primary-color, #4a90e2);
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.9rem;
  color: var(--text-color-secondary, #666666);
  text-align: center;
}

.dashboard-preview-actions {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

.preview-action-button {
  background-color: var(--primary-color, #4a90e2);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 1.5rem;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.preview-action-button:hover {
  background-color: var(--primary-color-dark, #3a7dd1);
}

/* Dark mode adjustments */
[data-theme="dark"] .dashboard-preview {
  background-color: var(--card-bg-color, #2b2b2b);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

[data-theme="dark"] .chart-box,
[data-theme="dark"] .metrics-container {
  background-color: var(--card-bg-color-secondary, #222222);
}

[data-theme="dark"] .metric-box {
  background-color: var(--card-bg-color, #2b2b2b);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-preview {
    padding: 1rem;
  }
  
  .chart-container {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
  
  .chart-wrapper {
    height: 180px;
  }
  
  .dashboard-preview-header h3 {
    font-size: 1.4rem;
  }
}

@media (max-width: 480px) {
  .metrics-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .chart-wrapper {
    height: 160px;
  }
} 