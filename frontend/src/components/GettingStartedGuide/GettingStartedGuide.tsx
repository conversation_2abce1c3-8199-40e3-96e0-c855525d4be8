import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAnalytics } from '../../contexts/AnalyticsContext';
import { useLanguage } from '../../contexts/LanguageContext';
import './GettingStartedGuide.css';

interface GettingStartedGuideProps {
    className?: string;
}

interface Step {
    number: number;
    title: string;
    description: string;
    icon: string;
    details: string[];
    ctaLink?: string;
    ctaText?: string;
}

const GettingStartedGuide: React.FC<GettingStartedGuideProps> = ({ className = '' }) => {
    const { translate } = useLanguage();
    const { trackEvent } = useAnalytics();
    const [activeStep, setActiveStep] = useState<number>(1);
    const [showTutorial, setShowTutorial] = useState<boolean>(false);

    // Define steps
    const steps: Step[] = [
        {
            number: 1,
            title: translate('gettingStarted.step1.title', 'Create Your Profile'),
            description: translate('gettingStarted.step1.description', 'Set up your profile with your current skills, experience, and career goals.'),
            icon: '👤',
            details: [
                translate('gettingStarted.step1.detail1', 'Sign up for a free account'),
                translate('gettingStarted.step1.detail2', 'Complete your skill assessment'),
                translate('gettingStarted.step1.detail3', 'Set your career objectives'),
                translate('gettingStarted.step1.detail4', 'Specify your available study time')
            ],
            ctaLink: '/signup',
            ctaText: translate('gettingStarted.step1.cta', 'Create Account')
        },
        {
            number: 2,
            title: translate('gettingStarted.step2.title', 'Explore Certification Paths'),
            description: translate('gettingStarted.step2.description', 'Browse recommended certification paths based on your career objectives.'),
            icon: '🔍',
            details: [
                translate('gettingStarted.step2.detail1', 'View personalized certification recommendations'),
                translate('gettingStarted.step2.detail2', 'Compare certification requirements and costs'),
                translate('gettingStarted.step2.detail3', 'See career potential for each path'),
                translate('gettingStarted.step2.detail4', 'Filter by domain, level, and cost')
            ],
            ctaLink: '/certification-explorer',
            ctaText: translate('gettingStarted.step2.cta', 'Browse Certifications')
        },
        {
            number: 3,
            title: translate('gettingStarted.step3.title', 'Track Your Progress'),
            description: translate('gettingStarted.step3.description', 'Monitor your study time, exam readiness, and career progression.'),
            icon: '📊',
            details: [
                translate('gettingStarted.step3.detail1', 'Log your study hours'),
                translate('gettingStarted.step3.detail2', 'Track completion of study materials'),
                translate('gettingStarted.step3.detail3', 'Take practice assessments'),
                translate('gettingStarted.step3.detail4', 'Monitor your ROI and career growth')
            ],
            ctaLink: '/dashboard',
            ctaText: translate('gettingStarted.step3.cta', 'View Dashboard')
        }
    ];

    // Handle step selection
    const handleStepClick = (stepNumber: number) => {
        setActiveStep(stepNumber);
        trackEvent('engagement', 'click_step', `Step ${stepNumber}`, stepNumber);
    };

    // Handle tutorial button click
    const handleTutorialClick = () => {
        setShowTutorial(true);
        trackEvent('engagement', 'start_tutorial', 'Getting Started Guide');

        // This would normally trigger an actual tutorial component
        // For now we'll just simulate it
        setTimeout(() => {
            setShowTutorial(false);
            trackEvent('engagement', 'complete_tutorial', 'Getting Started Guide');
        }, 3000);
    };

    // Get current active step
    const currentStep = steps.find(step => step.number === activeStep) || steps[0];

    return (
        <div className={`getting-started-guide ${className}`}>
            <div className="guide-header">
                <h2>{translate('gettingStarted.title', 'How to Get Started')}</h2>
                <p className="guide-description">{translate('gettingStarted.description', 'Begin your certification journey in three easy steps')}</p>

                <button
                    className="tutorial-button"
                    onClick={handleTutorialClick}
                    disabled={showTutorial}
                >
                    <span className="tutorial-icon">?</span>
                    <span>{translate('gettingStarted.tutorial.button', 'Interactive Tutorial')}</span>
                </button>

                {showTutorial && (
                    <div className="tutorial-message">
                        {translate('gettingStarted.tutorial.message', 'Tutorial mode activated! In a full implementation, this would launch an interactive walkthrough.')}
                    </div>
                )}
            </div>

            <div className="guide-steps-nav">
                {steps.map(step => (
                    <button
                        key={step.number}
                        className={`step-nav-button ${activeStep === step.number ? 'active' : ''}`}
                        onClick={() => handleStepClick(step.number)}
                    >
                        <span className="step-number">{step.number}</span>
                        <span className="step-nav-title">{step.title}</span>
                    </button>
                ))}
            </div>

            <div className="active-step-details">
                <div className="step-header">
                    <div className="step-icon">{currentStep.icon}</div>
                    <div className="step-title-container">
                        <h3 className="step-title">{currentStep.title}</h3>
                        <p className="step-description">{currentStep.description}</p>
                    </div>
                </div>

                <div className="step-details-list">
                    <h4>{translate('gettingStarted.details.heading', 'What to expect:')}</h4>
                    <ul>
                        {currentStep.details.map((detail, index) => (
                            <li key={index}>{detail}</li>
                        ))}
                    </ul>
                </div>

                {currentStep.ctaLink && (
                    <div className="step-action">
                        <Link to={currentStep.ctaLink} className="step-cta-button">
                            {currentStep.ctaText}
                        </Link>
                    </div>
                )}
            </div>

            <div className="getting-started-cta">
                <Link to="/signup" className="cta-button primary">
                    {translate('gettingStarted.cta.signup', 'Create Free Account')}
                </Link>
                <Link to="/certification-explorer" className="cta-button secondary">
                    {translate('gettingStarted.cta.explore', 'Browse Certifications')}
                </Link>
            </div>

            <div className="recommendation-teaser">
                <h4>{translate('gettingStarted.recommendation.title', 'Personal Recommendation Engine')}</h4>
                <p>
                    {translate('gettingStarted.recommendation.description', 'Our AI-powered recommendation engine analyzes your profile, goals, and learning style to suggest the perfect certification path.')}
                </p>
                <div className="recommendation-badge">
                    <span className="badge-icon">🧠</span>
                    <span className="badge-text">{translate('gettingStarted.recommendation.badge', 'AI-Powered')}</span>
                </div>
            </div>
        </div>
    );
};

export default GettingStartedGuide; 