.getting-started-guide {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
  border-radius: 12px;
  background-color: var(--card-bg-color, #ffffff);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Guide header */
.guide-header {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}

.guide-header h2 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--heading-color, #333333);
}

.guide-description {
  font-size: 1.1rem;
  color: var(--text-color-secondary, #666666);
  margin-bottom: 1.5rem;
}

/* Tutorial button */
.tutorial-button {
  display: inline-flex;
  align-items: center;
  background-color: var(--primary-color-light, #d4e6f7);
  color: var(--primary-color-dark, #3a7dd1);
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.tutorial-button:hover {
  background-color: var(--primary-color, #4a90e2);
  color: white;
}

.tutorial-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.tutorial-icon {
  font-size: 1.1rem;
  margin-right: 0.5rem;
  width: 20px;
  height: 20px;
  background-color: var(--primary-color-dark, #3a7dd1);
  color: white;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.tutorial-message {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--card-bg-color, #ffffff);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  font-size: 0.9rem;
  max-width: 80%;
  z-index: 10;
  border-left: 4px solid var(--primary-color, #4a90e2);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -10px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

/* Steps navigation */
.guide-steps-nav {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.step-nav-button {
  display: flex;
  align-items: center;
  background-color: var(--card-bg-color-secondary, #f8f9fa);
  border: 1px solid var(--border-color, #eaeaea);
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.step-nav-button:hover {
  background-color: var(--primary-color-light, #d4e6f7);
  border-color: var(--primary-color, #4a90e2);
}

.step-nav-button.active {
  background-color: var(--primary-color, #4a90e2);
  border-color: var(--primary-color, #4a90e2);
  color: white;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--border-color, #eaeaea);
  font-weight: 600;
  font-size: 0.9rem;
  margin-right: 0.75rem;
}

.step-nav-button.active .step-number {
  background-color: white;
  color: var(--primary-color, #4a90e2);
}

.step-nav-title {
  font-size: 0.9rem;
  font-weight: 600;
}

/* Active step details */
.active-step-details {
  background-color: var(--card-bg-color-secondary, #f8f9fa);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.step-header {
  display: flex;
  margin-bottom: 1.5rem;
}

.step-icon {
  font-size: 2rem;
  margin-right: 1.25rem;
  width: 3.5rem;
  height: 3.5rem;
  background-color: var(--primary-color-light, #d4e6f7);
  color: var(--primary-color-dark, #3a7dd1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-title-container {
  flex: 1;
}

.step-title {
  font-size: 1.4rem;
  margin: 0 0 0.5rem 0;
  color: var(--heading-color, #333333);
}

.step-description {
  font-size: 1rem;
  color: var(--text-color-secondary, #666666);
  margin: 0;
}

.step-details-list {
  margin-bottom: 1.5rem;
}

.step-details-list h4 {
  font-size: 1rem;
  margin: 0 0 0.75rem 0;
  color: var(--heading-color, #333333);
}

.step-details-list ul {
  padding-left: 1.5rem;
  margin: 0;
}

.step-details-list li {
  margin-bottom: 0.5rem;
  color: var(--text-color, #333333);
}

.step-action {
  text-align: center;
}

.step-cta-button {
  display: inline-block;
  background-color: var(--primary-color, #4a90e2);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: background-color 0.2s ease;
}

.step-cta-button:hover {
  background-color: var(--primary-color-dark, #3a7dd1);
}

/* CTA section */
.getting-started-cta {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.cta-button {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.2s ease;
}

.cta-button.primary {
  background-color: var(--primary-color, #4a90e2);
  color: white;
}

.cta-button.primary:hover {
  background-color: var(--primary-color-dark, #3a7dd1);
}

.cta-button.secondary {
  background-color: transparent;
  border: 2px solid var(--primary-color, #4a90e2);
  color: var(--primary-color, #4a90e2);
}

.cta-button.secondary:hover {
  background-color: var(--primary-color-light, #d4e6f7);
}

/* Recommendation teaser */
.recommendation-teaser {
  background-color: var(--card-bg-color-secondary, #f8f9fa);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  border-left: 4px solid var(--success-color, #7cb342);
}

.recommendation-teaser h4 {
  font-size: 1.2rem;
  margin: 0 0 0.75rem 0;
  color: var(--heading-color, #333333);
}

.recommendation-teaser p {
  font-size: 0.95rem;
  color: var(--text-color, #333333);
  margin: 0 0 1rem 0;
}

.recommendation-badge {
  display: inline-flex;
  align-items: center;
  background-color: var(--success-color-light, #d7f5e8);
  color: var(--success-color-dark, #2e7d32);
  border-radius: 50px;
  padding: 0.5rem 1rem;
}

.badge-icon {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

.badge-text {
  font-size: 0.9rem;
  font-weight: 600;
}

/* Dark mode adjustments */
[data-theme="dark"] .getting-started-guide {
  background-color: var(--card-bg-color, #2b2b2b);
}

[data-theme="dark"] .step-nav-button {
  background-color: var(--card-bg-color-secondary, #333333);
  border-color: var(--border-color, #444444);
}

[data-theme="dark"] .active-step-details,
[data-theme="dark"] .recommendation-teaser {
  background-color: var(--card-bg-color-secondary, #222222);
}

[data-theme="dark"] .tutorial-message {
  background-color: var(--card-bg-color, #2b2b2b);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .getting-started-guide {
    padding: 1.5rem;
  }
  
  .guide-header h2 {
    font-size: 1.6rem;
  }
  
  .guide-description {
    font-size: 1rem;
  }
  
  .guide-steps-nav {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .step-nav-button {
    width: 100%;
    justify-content: flex-start;
  }
  
  .step-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .step-icon {
    margin-right: 0;
    margin-bottom: 1rem;
  }
  
  .getting-started-cta {
    flex-direction: column;
  }
  
  .cta-button {
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .tutorial-message {
    max-width: 95%;
  }
  
  .step-title {
    font-size: 1.2rem;
  }
  
  .step-nav-title {
    font-size: 0.8rem;
  }
  
  .recommendation-teaser h4 {
    font-size: 1.1rem;
  }
} 