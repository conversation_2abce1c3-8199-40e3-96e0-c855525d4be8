.language-selector {
  position: relative;
  margin-left: 10px;
  transition: opacity 0.3s ease;
}

.language-selector.changing {
  opacity: 0.5;
  pointer-events: none;
}

.language-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transition: background-color 0.2s, opacity 0.3s ease;
}

.language-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.language-toggle:disabled {
  cursor: not-allowed;
}

.language-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 5px;
  background-color: var(--color-card);
  border-radius: 8px;
  box-shadow: 0 4px 12px var(--color-shadow);
  overflow: hidden;
  width: 160px;
  z-index: 200;
}

.language-option {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 15px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;
  color: var(--color-text);
}

.language-option:hover {
  background-color: var(--color-hover);
}

.language-option.active {
  background-color: var(--color-primary-light);
  font-weight: 500;
}

.language-flag {
  margin-right: 8px;
  font-size: 18px;
}

.language-name {
  font-size: 14px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .language-selector {
    position: absolute;
    top: 16px;
    right: 66px;
  }
} 