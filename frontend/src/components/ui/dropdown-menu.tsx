import { Button, Menu, MenuItem, MenuItemProps, MenuProps } from '@mui/material';
import React from 'react';

// Dropdown Menu Component
export interface DropdownMenuProps {
    children: React.ReactNode;
}

export function DropdownMenu({ children }: DropdownMenuProps) {
    return <>{children}</>;
}

// Dropdown Menu Trigger
export interface DropdownMenuTriggerProps {
    children: React.ReactNode;
    asChild?: boolean;
}

export function DropdownMenuTrigger({ children, asChild }: DropdownMenuTriggerProps) {
    // If asChild is true, we're expected to render the children directly
    // otherwise wrap in a button
    if (asChild) {
        return <>{children}</>;
    }

    return <Button>{children}</Button>;
}

// Dropdown Menu Content
export interface DropdownMenuContentProps extends Omit<MenuProps, 'children'> {
    children: React.ReactNode;
    align?: 'start' | 'center' | 'end';
}

export function DropdownMenuContent({
    children,
    align = 'center',
    ...props
}: DropdownMenuContentProps) {
    // Transform align to anchorOrigin
    const alignMap = {
        start: 'left',
        center: 'center',
        end: 'right'
    };

    const horizontalAlignment = alignMap[align] || 'center';

    return (
        <Menu
            anchorOrigin={{
                vertical: 'bottom',
                horizontal: horizontalAlignment as 'left' | 'center' | 'right',
            }}
            transformOrigin={{
                vertical: 'top',
                horizontal: horizontalAlignment as 'left' | 'center' | 'right',
            }}
            {...props}
        >
            {children}
        </Menu>
    );
}

// Dropdown Menu Item
export interface DropdownMenuItemProps extends MenuItemProps {
    children: React.ReactNode;
}

export function DropdownMenuItem({ children, ...props }: DropdownMenuItemProps) {
    return <MenuItem {...props}>{children}</MenuItem>;
} 