import { Button as <PERSON>i<PERSON>utt<PERSON>, ButtonProps as MuiButtonProps } from '@mui/material';
import React from 'react';

export interface ButtonProps {
    asChild?: boolean;
    size?: 'default' | 'sm' | 'lg' | 'icon';
    variant?: 'text' | 'contained' | 'outlined' | 'outline';
    children?: React.ReactNode;
    className?: string;
    onClick?: () => void;
    style?: React.CSSProperties;
    [key: string]: any; // Allow other props
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
    ({
        asChild,
        size = 'default',
        variant = 'contained',
        children,
        ...props
    }, ref) => {
        // Map shadcn-ui sizes to Material UI styling
        const sizeStyles: Record<string, React.CSSProperties> = {
            default: { padding: '10px 16px' },
            sm: { padding: '6px 12px' },
            lg: { padding: '12px 24px' },
            icon: { minWidth: 'unset', width: '40px', height: '40px', padding: '8px' }
        };

        // Map our variant names to MUI variants
        let muiVariant: MuiButtonProps['variant'] = 'contained';
        if (variant === 'outline' || variant === 'outlined') {
            muiVariant = 'outlined';
        } else if (variant === 'text') {
            muiVariant = 'text';
        }

        return (
            <MuiButton
                ref={ref}
                variant={muiVariant}
                size={size === 'sm' ? 'small' : size === 'lg' ? 'large' : 'medium'}
                style={{
                    ...(sizeStyles[size] || {}),
                    ...props.style
                }}
                {...props}
            >
                {children}
            </MuiButton>
        );
    }
); 