'use client';

import { Brightness4, <PERSON>ness7, Monitor } from '@mui/icons-material';
import { Button, Menu, MenuItem } from '@mui/material';
import { useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';

export function ModeToggle() {
    const { setTheme, theme } = useTheme();
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    // Pick icon based on theme
    let icon = <Brightness7 style={{ width: '1.2rem', height: '1.2rem' }} />;
    if (theme === 'dark') icon = <Brightness4 style={{ width: '1.2rem', height: '1.2rem' }} />;
    if (theme === 'crt') icon = <Monitor style={{ width: '1.2rem', height: '1.2rem', color: '#4ade80' }} />;

    return (
        <div>
            <Button
                id="theme-button"
                aria-controls={open ? 'theme-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={open ? 'true' : undefined}
                onClick={handleClick}
                variant="outlined"
                style={{ minWidth: 'unset', padding: '8px' }}
            >
                {icon}
                <span className="sr-only">Toggle theme</span>
            </Button>
            <Menu
                id="theme-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                MenuListProps={{
                    'aria-labelledby': 'theme-button',
                }}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
            >
                <MenuItem onClick={() => { setTheme('light'); handleClose(); }}>
                    Light
                </MenuItem>
                <MenuItem onClick={() => { setTheme('dark'); handleClose(); }}>
                    Dark
                </MenuItem>
                <MenuItem onClick={() => { setTheme('crt'); handleClose(); }}>
                    CRT Terminal
                </MenuItem>
            </Menu>
        </div>
    );
} 