import React from 'react';
import './FeedbackButton.css';

interface FeedbackButtonProps {
    onClick: () => void;
    position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
    label?: string;
}

const FeedbackButton: React.FC<FeedbackButtonProps> = ({
    onClick,
    position = 'bottom-right',
    label = 'Feedback',
}) => {
    return (
        <button
            className={`feedback-button ${position}`}
            onClick={onClick}
            aria-label="Open feedback form"
        >
            <span className="feedback-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
            </span>
            <span className="feedback-label">{label}</span>
        </button>
    );
};

export default FeedbackButton; 