.feedback-button {
  position: fixed;
  display: flex;
  align-items: center;
  background-color: #4A90E2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 999;
  transition: all 0.2s ease;
}

.feedback-button:hover {
  background-color: #3A80D2;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.feedback-button.bottom-right {
  bottom: 20px;
  right: 20px;
}

.feedback-button.bottom-left {
  bottom: 20px;
  left: 20px;
}

.feedback-button.top-right {
  top: 20px;
  right: 20px;
}

.feedback-button.top-left {
  top: 20px;
  left: 20px;
}

.feedback-icon {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

@media (max-width: 600px) {
  .feedback-button {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .feedback-button.bottom-right,
  .feedback-button.bottom-left,
  .feedback-button.top-right,
  .feedback-button.top-left {
    bottom: 12px;
    right: 12px;
  }
} 