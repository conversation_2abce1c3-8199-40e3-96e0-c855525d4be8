import React from 'react';

// Type for icon names
export type IconName =
  | 'add'
  | 'cancel'
  | 'check'
  | 'close'
  | 'delete'
  | 'download'
  | 'edit'
  | 'error'
  | 'home'
  | 'info'
  | 'language'
  | 'next'
  | 'previous'
  | 'refresh'
  | 'search'
  | 'settings'
  | 'star'
  | 'translate'
  | 'upload'
  | 'view'
  | 'warning';

// Props for the Icon component
interface IconProps {
  name: IconName;
  size?: 'small' | 'medium' | 'large';
  color?: string;
  className?: string;
}

/**
 * Simple SVG-based icon component to use as a fallback until MUI icons are properly installed
 */
export const Icon: React.FC<IconProps> = ({
  name,
  size = 'medium',
  color = 'currentColor',
  className
}) => {
  // Convert size to pixel values
  const getSize = () => {
    switch (size) {
      case 'small': return 16;
      case 'large': return 32;
      default: return 24;
    }
  };

  const pixelSize = getSize();
  const commonProps = {
    width: pixelSize,
    height: pixelSize,
    fill: 'none',
    stroke: color,
    strokeWidth: 2,
    strokeLinecap: 'round' as 'inherit' | 'round' | 'butt' | 'square',
    strokeLinejoin: 'round' as 'inherit' | 'round' | 'miter' | 'bevel',
    className
  };

  // Return the appropriate SVG icon based on name
  switch (name) {
    case 'add':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <line x1="12" y1="5" x2="12" y2="19" />
          <line x1="5" y1="12" x2="19" y2="12" />
        </svg>
      );
    case 'cancel':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <circle cx="12" cy="12" r="10" />
          <line x1="15" y1="9" x2="9" y2="15" />
          <line x1="9" y1="9" x2="15" y2="15" />
        </svg>
      );
    case 'check':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <circle cx="12" cy="12" r="10" />
          <polyline points="8,12 11,15 16,9" />
        </svg>
      );
    case 'close':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <line x1="18" y1="6" x2="6" y2="18" />
          <line x1="6" y1="6" x2="18" y2="18" />
        </svg>
      );
    case 'delete':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <polyline points="3,6 5,6 21,6" />
          <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6" />
          <line x1="8" y1="10" x2="8" y2="16" />
          <line x1="12" y1="10" x2="12" y2="16" />
          <line x1="16" y1="10" x2="16" y2="16" />
          <line x1="10" y1="3" x2="14" y2="3" />
        </svg>
      );
    case 'download':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <path d="M21,15v4a2,2,0,0,1-2,2H5a2,2,0,0,1-2-2V15" />
          <polyline points="7,10 12,15 17,10" />
          <line x1="12" y1="15" x2="12" y2="3" />
        </svg>
      );
    case 'edit':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <path d="M20,16v4a2,2,0,0,1-2,2H4a2,2,0,0,1-2-2V6A2,2,0,0,1,4,4H8" />
          <polygon points="12,4 20,4 20,12" />
          <line x1="14" y1="10" x2="21" y2="3" />
        </svg>
      );
    case 'error':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <circle cx="12" cy="12" r="10" />
          <line x1="12" y1="8" x2="12" y2="12" />
          <line x1="12" y1="16" x2="12" y2="16" />
        </svg>
      );
    case 'home':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <path d="M3,10.2V20a2,2,0,0,0,2,2H19a2,2,0,0,0,2-2V10.2" />
          <polyline points="1,10 12,2 23,10" />
          <polyline points="9,14 9,21" />
          <polyline points="15,14 15,21" />
        </svg>
      );
    case 'info':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <circle cx="12" cy="12" r="10" />
          <line x1="12" y1="16" x2="12" y2="12" />
          <line x1="12" y1="8" x2="12" y2="8" />
        </svg>
      );
    case 'language':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <circle cx="12" cy="12" r="10" />
          <line x1="2" y1="12" x2="22" y2="12" />
          <path d="M12,2 a15,15 0 0,1 0,20 a15,15 0 0,1 0,-20" />
        </svg>
      );
    case 'next':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <polyline points="9,18 15,12 9,6" />
        </svg>
      );
    case 'previous':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <polyline points="15,18 9,12 15,6" />
        </svg>
      );
    case 'refresh':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <path d="M21,12 a9,9 0 1,1 -9,-9" />
          <polyline points="16,5 21,3 21,8" />
        </svg>
      );
    case 'search':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <circle cx="11" cy="11" r="8" />
          <line x1="21" y1="21" x2="16.65" y2="16.65" />
        </svg>
      );
    case 'settings':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <circle cx="12" cy="12" r="3" />
          <path d="M19.4,15a1.65,1.65,0,0,0,.33,1.82l.06.06a2,2,0,0,1,0,2.83h0a2,2,0,0,1-2.83,0l-.06-.06a1.65,1.65,0,0,0-1.82-.33,1.65,1.65,0,0,0-1,1.51V21a2,2,0,0,1-2,2h0a2,2,0,0,1-2-2v-.09A1.65,1.65,0,0,0,9,19.4a1.65,1.65,0,0,0-1.82.33l-.06.06a2,2,0,0,1-2.83,0h0a2,2,0,0,1,0-2.83l.06-.06a1.65,1.65,0,0,0,.33-1.82,1.65,1.65,0,0,0-1.51-1H3a2,2,0,0,1-2-2V12a2,2,0,0,1,2-2h.09A1.65,1.65,0,0,0,4.6,9a1.65,1.65,0,0,0-.33-1.82l-.06-.06a2,2,0,0,1,0-2.83h0a2,2,0,0,1,2.83,0l.06.06a1.65,1.65,0,0,0,1.82.33H9a1.65,1.65,0,0,0,1-1.51V3a2,2,0,0,1,2-2h0a2,2,0,0,1,2,2v.09a1.65,1.65,0,0,0,1,1.51,1.65,1.65,0,0,0,1.82-.33l.06-.06a2,2,0,0,1,2.83,0h0a2,2,0,0,1,0,2.83l-.06.06a1.65,1.65,0,0,0-.33,1.82V9a1.65,1.65,0,0,0,1.51,1H21a2,2,0,0,1,2,2v0a2,2,0,0,1-2,2h-.09a1.65,1.65,0,0,0-1.51,1Z" />
        </svg>
      );
    case 'star':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
        </svg>
      );
    case 'translate':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <polyline points="5,8 10,8 15,21" />
          <line x1="4" y1="14" x2="10" y2="14" />
          <polyline points="18,2 18,12" />
          <polyline points="14,6 18,6 22,6" />
        </svg>
      );
    case 'upload':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <path d="M21,15v4a2,2,0,0,1-2,2H5a2,2,0,0,1-2-2V15" />
          <polyline points="17,8 12,3 7,8" />
          <line x1="12" y1="3" x2="12" y2="15" />
        </svg>
      );
    case 'view':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <path d="M1,12s4-8,11-8,11,8,11,8-4,8-11,8S1,12,1,12Z" />
          <circle cx="12" cy="12" r="3" />
        </svg>
      );
    case 'warning':
      return (
        <svg viewBox="0 0 24 24" {...commonProps}>
          <path d="M10.29,3.86,1.82,18a2,2,0,0,0,1.71,3H20.47a2,2,0,0,0,1.71-3L13.71,3.86a2,2,0,0,0-3.42,0Z" />
          <line x1="12" y1="9" x2="12" y2="13" />
          <line x1="12" y1="17" x2="12.01" y2="17" />
        </svg>
      );
    default:
      return null;
  }
};

export default Icon; 