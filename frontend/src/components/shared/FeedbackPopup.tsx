import React, { useState } from 'react';
import './FeedbackPopup.css';

type FeedbackType = 'general' | 'bug' | 'feature' | 'content';

interface FeedbackPopupProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (feedback: FeedbackData) => Promise<void>;
}

export interface FeedbackData {
    type: FeedbackType;
    message: string;
    email?: string;
    rating: number;
    pagePath: string;
}

const FeedbackPopup: React.FC<FeedbackPopupProps> = ({ isOpen, onClose, onSubmit }) => {
    const [feedbackType, setFeedbackType] = useState<FeedbackType>('general');
    const [message, setMessage] = useState('');
    const [email, setEmail] = useState('');
    const [rating, setRating] = useState(3);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState(false);

    if (!isOpen) return null;

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);
        setError(null);

        try {
            await onSubmit({
                type: feedbackType,
                message,
                email: email || undefined,
                rating,
                pagePath: window.location.pathname,
            });

            setSuccess(true);
            // Reset form after successful submission
            setFeedbackType('general');
            setMessage('');
            setEmail('');
            setRating(3);

            // Close popup after a delay
            setTimeout(() => {
                onClose();
                setSuccess(false);
            }, 2000);
        } catch (err) {
            setError('Failed to submit feedback. Please try again later.');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="feedback-popup-overlay" onClick={onClose} data-testid="feedback-popup-overlay">
            <div className="feedback-popup-container" onClick={(e) => e.stopPropagation()}>
                <button className="feedback-popup-close" onClick={onClose} aria-label="Close">
                    &times;
                </button>

                <h2>Share Your Feedback</h2>

                {success ? (
                    <div className="feedback-success">
                        <p>Thank you for your feedback!</p>
                    </div>
                ) : (
                    <form onSubmit={handleSubmit} className="feedback-form">
                        <div className="form-group">
                            <label htmlFor="feedback-type">Feedback Type</label>
                            <select
                                id="feedback-type"
                                value={feedbackType}
                                onChange={(e) => setFeedbackType(e.target.value as FeedbackType)}
                                required
                            >
                                <option value="general">General Feedback</option>
                                <option value="bug">Report a Bug</option>
                                <option value="feature">Feature Request</option>
                                <option value="content">Content Issue</option>
                            </select>
                        </div>

                        <div className="form-group">
                            <label htmlFor="feedback-message">Your Feedback</label>
                            <textarea
                                id="feedback-message"
                                value={message}
                                onChange={(e) => setMessage(e.target.value)}
                                rows={4}
                                required
                                placeholder="Please describe your feedback in detail..."
                            />
                        </div>

                        <div className="form-group">
                            <label htmlFor="feedback-email">Email (optional)</label>
                            <input
                                type="email"
                                id="feedback-email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                placeholder="<EMAIL>"
                            />
                            <small>We'll only use this to follow up on your feedback if needed.</small>
                        </div>

                        <div className="form-group">
                            <label>Rate Your Experience</label>
                            <div className="rating-container">
                                {[1, 2, 3, 4, 5].map((value) => (
                                    <button
                                        key={value}
                                        type="button"
                                        className={`rating-button ${rating === value ? 'active' : ''}`}
                                        onClick={() => setRating(value)}
                                        aria-label={`Rate ${value} out of 5`}
                                    >
                                        {value}
                                    </button>
                                ))}
                            </div>
                        </div>

                        {error && <div className="feedback-error">{error}</div>}

                        <button
                            type="submit"
                            className="submit-button"
                            disabled={isSubmitting || !message.trim()}
                        >
                            {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
                        </button>
                    </form>
                )}
            </div>
        </div>
    );
};

export default FeedbackPopup; 