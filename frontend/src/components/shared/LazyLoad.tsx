import React from 'react';
import useLazyLoad from '../../hooks/useLazyLoad';

interface LazyLoadProps {
    children: React.ReactNode;
    className?: string;
    height?: string | number;
    threshold?: number;
    rootMargin?: string;
    placeholder?: React.ReactNode;
    style?: React.CSSProperties;
}

/**
 * LazyLoad component that renders children only when they come into view
 * Uses IntersectionObserver API for efficient lazy loading
 */
const LazyLoad: React.FC<LazyLoadProps> = ({
    children,
    className = '',
    height,
    threshold = 0.1,
    rootMargin = '0px',
    placeholder,
    style = {},
}) => {
    const { isVisible, elementRef } = useLazyLoad({ threshold, rootMargin });

    const placeholderStyle: React.CSSProperties = {
        height: height || 'auto',
        ...style,
    };

    return (
        <div
            ref={elementRef}
            className={`lazy-load-container ${className}`}
            style={placeholderStyle}
            data-loaded={isVisible ? 'true' : 'false'}
        >
            {isVisible ? children : placeholder || null}
        </div>
    );
};

export default LazyLoad; 