import { fireEvent, render, screen } from '@testing-library/react';
import FeedbackButton from '../FeedbackButton';

describe('FeedbackButton Component', () => {
    const mockOnClick = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should render correctly with default props', () => {
        render(<FeedbackButton onClick={mockOnClick} />);

        const button = screen.getByRole('button', { name: /open feedback form/i });
        expect(button).toBeInTheDocument();
        expect(button).toHaveClass('bottom-right');
        expect(screen.getByText('Feedback')).toBeInTheDocument();
    });

    it('should call onClick when clicked', () => {
        render(<FeedbackButton onClick={mockOnClick} />);

        fireEvent.click(screen.getByRole('button', { name: /open feedback form/i }));
        expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it('should use the provided label', () => {
        render(<FeedbackButton onClick={mockOnClick} label="Give Feedback" />);

        expect(screen.getByText('Give Feedback')).toBeInTheDocument();
    });

    it('should apply the provided position class', () => {
        render(<FeedbackButton onClick={mockOnClick} position="top-left" />);

        const button = screen.getByRole('button', { name: /open feedback form/i });
        expect(button).toHaveClass('top-left');
        expect(button).not.toHaveClass('bottom-right');
    });
}); 