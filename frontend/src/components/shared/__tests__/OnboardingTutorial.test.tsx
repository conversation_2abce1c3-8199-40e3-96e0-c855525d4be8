import { fireEvent, render, screen } from '@testing-library/react';
import OnboardingTutorial, { TutorialStep } from '../OnboardingTutorial';

describe('OnboardingTutorial Component', () => {
    const mockOnComplete = jest.fn();
    const mockOnClose = jest.fn();
    const mockOnStepChange = jest.fn();

    const mockSteps: TutorialStep[] = [
        {
            title: 'Step 1',
            content: 'This is the first step',
        },
        {
            title: 'Step 2',
            content: 'This is the second step',
        },
        {
            title: 'Step 3',
            content: 'This is the final step',
        },
    ];

    beforeEach(() => {
        jest.clearAllMocks();

        // Mock getBoundingClientRect for positioning tests
        Element.prototype.getBoundingClientRect = jest.fn(() => ({
            width: 100,
            height: 100,
            top: 100,
            left: 100,
            right: 200,
            bottom: 200,
            x: 100,
            y: 100,
        }));
    });

    it('should not render when isOpen is false', () => {
        render(
            <OnboardingTutorial
                steps={mockSteps}
                isOpen={false}
                onComplete={mockOnComplete}
                onClose={mockOnClose}
            />
        );

        expect(screen.queryByText('Step 1')).not.toBeInTheDocument();
    });

    it('should render the first step when isOpen is true', () => {
        render(
            <OnboardingTutorial
                steps={mockSteps}
                isOpen={true}
                onComplete={mockOnComplete}
                onClose={mockOnClose}
            />
        );

        expect(screen.getByText('Step 1')).toBeInTheDocument();
        expect(screen.getByText('This is the first step')).toBeInTheDocument();
        expect(screen.queryByText('Step 2')).not.toBeInTheDocument();
    });

    it('should move to the next step when Next is clicked', () => {
        render(
            <OnboardingTutorial
                steps={mockSteps}
                isOpen={true}
                onComplete={mockOnComplete}
                onClose={mockOnClose}
                onStepChange={mockOnStepChange}
            />
        );

        // First step is displayed
        expect(screen.getByText('Step 1')).toBeInTheDocument();

        // Click Next
        fireEvent.click(screen.getByText('Next'));

        // Second step should be displayed
        expect(screen.getByText('Step 2')).toBeInTheDocument();
        expect(mockOnStepChange).toHaveBeenCalledWith(1);
    });

    it('should call onComplete when Done is clicked on the last step', () => {
        render(
            <OnboardingTutorial
                steps={mockSteps}
                isOpen={true}
                onComplete={mockOnComplete}
                onClose={mockOnClose}
                startAtStep={2} // Start at the last step
            />
        );

        // Last step is displayed
        expect(screen.getByText('Step 3')).toBeInTheDocument();

        // Click Done
        fireEvent.click(screen.getByText('Done'));

        // onComplete should be called
        expect(mockOnComplete).toHaveBeenCalledTimes(1);
    });

    it('should move to the previous step when Previous is clicked', () => {
        render(
            <OnboardingTutorial
                steps={mockSteps}
                isOpen={true}
                onComplete={mockOnComplete}
                onClose={mockOnClose}
                onStepChange={mockOnStepChange}
                startAtStep={1} // Start at the second step
            />
        );

        // Second step is displayed
        expect(screen.getByText('Step 2')).toBeInTheDocument();

        // Click Previous
        fireEvent.click(screen.getByText('Previous'));

        // First step should be displayed
        expect(screen.getByText('Step 1')).toBeInTheDocument();
        expect(mockOnStepChange).toHaveBeenCalledWith(0);
    });

    it('should call onClose when close button is clicked', () => {
        render(
            <OnboardingTutorial
                steps={mockSteps}
                isOpen={true}
                onComplete={mockOnComplete}
                onClose={mockOnClose}
            />
        );

        // Click the close button
        fireEvent.click(screen.getByLabelText('Close tutorial'));

        // onClose should be called
        expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should call onComplete when Skip is clicked', () => {
        render(
            <OnboardingTutorial
                steps={mockSteps}
                isOpen={true}
                onComplete={mockOnComplete}
                onClose={mockOnClose}
            />
        );

        // Click Skip
        fireEvent.click(screen.getByText('Skip'));

        // onComplete should be called
        expect(mockOnComplete).toHaveBeenCalledTimes(1);
    });

    it('should display the correct number of progress dots', () => {
        render(
            <OnboardingTutorial
                steps={mockSteps}
                isOpen={true}
                onComplete={mockOnComplete}
                onClose={mockOnClose}
            />
        );

        // Should have 3 progress dots (one for each step)
        const dots = screen.getAllByLabelText(/Step \d of 3/);
        expect(dots).toHaveLength(3);

        // First dot should be active
        expect(dots[0]).toHaveClass('active');
        expect(dots[1]).not.toHaveClass('active');
        expect(dots[2]).not.toHaveClass('active');
    });

    it('should not show Previous button on the first step', () => {
        render(
            <OnboardingTutorial
                steps={mockSteps}
                isOpen={true}
                onComplete={mockOnComplete}
                onClose={mockOnClose}
            />
        );

        // Previous button should not be visible on first step
        expect(screen.queryByText('Previous')).not.toBeInTheDocument();
    });

    it('should show Previous button after the first step', () => {
        render(
            <OnboardingTutorial
                steps={mockSteps}
                isOpen={true}
                onComplete={mockOnComplete}
                onClose={mockOnClose}
                startAtStep={1} // Start at the second step
            />
        );

        // Previous button should be visible after first step
        expect(screen.getByText('Previous')).toBeInTheDocument();
    });
}); 