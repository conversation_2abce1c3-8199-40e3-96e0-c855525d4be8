import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { renderHook, act } from '@testing-library/react-hooks';
import useFeedback from '../useFeedback';
import { FeedbackData } from '../FeedbackPopup';

describe('useFeedback Hook', () => {
  const mockOnSubmitFeedback = jest.fn().mockResolvedValue(undefined);
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('should return openFeedback function and FeedbackPopupComponent', () => {
    const { result } = renderHook(() => useFeedback(mockOnSubmitFeedback));
    
    expect(result.current.openFeedback).toBeInstanceOf(Function);
    expect(result.current.FeedbackPopupComponent).toBeDefined();
  });
  
  it('should not render FeedbackPopup initially', () => {
    const TestComponent = () => {
      const { FeedbackPopupComponent } = useFeedback(mockOnSubmitFeedback);
      return <div>{FeedbackPopupComponent}</div>;
    };
    
    render(<TestComponent />);
    
    expect(screen.queryByText('Share Your Feedback')).not.toBeInTheDocument();
  });
  
  it('should display FeedbackPopup when openFeedback is called', () => {
    const TestComponent = () => {
      const { openFeedback, FeedbackPopupComponent } = useFeedback(mockOnSubmitFeedback);
      return (
        <div>
          <button onClick={openFeedback}>Open Feedback</button>
          {FeedbackPopupComponent}
        </div>
      );
    };
    
    render(<TestComponent />);
    
    // Initially not visible
    expect(screen.queryByText('Share Your Feedback')).not.toBeInTheDocument();
    
    // Click to open
    fireEvent.click(screen.getByText('Open Feedback'));
    
    // Should be visible now
    expect(screen.getByText('Share Your Feedback')).toBeInTheDocument();
  });
  
  it('should call onSubmitFeedback with correct data when form is submitted', async () => {
    const TestComponent = () => {
      const { openFeedback, FeedbackPopupComponent } = useFeedback(mockOnSubmitFeedback);
      return (
        <div>
          <button onClick={openFeedback}>Open Feedback</button>
          {FeedbackPopupComponent}
        </div>
      );
    };
    
    render(<TestComponent />);
    
    // Open feedback popup
    fireEvent.click(screen.getByText('Open Feedback'));
    
    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: { pathname: '/test-path' },
      writable: true
    });
    
    // Fill out the form
    fireEvent.change(screen.getByLabelText('Your Feedback'), { target: { value: 'Test feedback' } });
    
    // Submit the form
    fireEvent.click(screen.getByText('Submit Feedback'));
    
    // Check that onSubmitFeedback was called with the correct data
    await waitFor(() => {
      expect(mockOnSubmitFeedback).toHaveBeenCalledWith({
        type: 'general',
        message: 'Test feedback',
        email: undefined,
        rating: 3,
        pagePath: '/test-path'
      });
    });
  });
}); 