import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import FeedbackPopup from '../FeedbackPopup';

describe('FeedbackPopup Component', () => {
    const mockOnSubmit = jest.fn().mockResolvedValue(undefined);
    const mockOnClose = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should not render when isOpen is false', () => {
        render(
            <FeedbackPopup
                isOpen={false}
                onClose={mockOnClose}
                onSubmit={mockOnSubmit}
            />
        );

        expect(screen.queryByText('Share Your Feedback')).not.toBeInTheDocument();
    });

    it('should render correctly when isOpen is true', () => {
        render(
            <FeedbackPopup
                isOpen={true}
                onClose={mockOnClose}
                onSubmit={mockOnSubmit}
            />
        );

        expect(screen.getByText('Share Your Feedback')).toBeInTheDocument();
        expect(screen.getByLabelText('Feedback Type')).toBeInTheDocument();
        expect(screen.getByLabelText('Your Feedback')).toBeInTheDocument();
        expect(screen.getByText('Submit Feedback')).toBeInTheDocument();
    });

    it('should call onClose when the close button is clicked', () => {
        render(
            <FeedbackPopup
                isOpen={true}
                onClose={mockOnClose}
                onSubmit={mockOnSubmit}
            />
        );

        fireEvent.click(screen.getByLabelText('Close'));
        expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should call onClose when clicking outside the popup', () => {
        render(
            <FeedbackPopup
                isOpen={true}
                onClose={mockOnClose}
                onSubmit={mockOnSubmit}
            />
        );

        // Click on the overlay
        fireEvent.click(screen.getByTestId('feedback-popup-overlay'));
        expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should not call onClose when clicking inside the popup', () => {
        render(
            <FeedbackPopup
                isOpen={true}
                onClose={mockOnClose}
                onSubmit={mockOnSubmit}
            />
        );

        // Click inside the popup container
        fireEvent.click(screen.getByText('Share Your Feedback'));
        expect(mockOnClose).not.toHaveBeenCalled();
    });

    it('should call onSubmit with correct data when form is submitted', async () => {
        render(
            <FeedbackPopup
                isOpen={true}
                onClose={mockOnClose}
                onSubmit={mockOnSubmit}
            />
        );

        // Mock window.location
        Object.defineProperty(window, 'location', {
            value: { pathname: '/test-page' },
            writable: true
        });

        // Fill out the form
        fireEvent.change(screen.getByLabelText('Feedback Type'), { target: { value: 'bug' } });
        fireEvent.change(screen.getByLabelText('Your Feedback'), { target: { value: 'Test feedback message' } });
        fireEvent.change(screen.getByLabelText('Email (optional)'), { target: { value: '<EMAIL>' } });

        // Submit the form
        fireEvent.click(screen.getByText('Submit Feedback'));

        // Check that onSubmit was called with the correct data
        await waitFor(() => {
            expect(mockOnSubmit).toHaveBeenCalledWith({
                type: 'bug',
                message: 'Test feedback message',
                email: '<EMAIL>',
                rating: 3, // Default rating
                pagePath: '/test-page'
            });
        });
    });

    it('should show success message after submission', async () => {
        render(
            <FeedbackPopup
                isOpen={true}
                onClose={mockOnClose}
                onSubmit={mockOnSubmit}
            />
        );

        // Fill out the form
        fireEvent.change(screen.getByLabelText('Your Feedback'), { target: { value: 'Test feedback' } });

        // Submit the form
        fireEvent.click(screen.getByText('Submit Feedback'));

        // Check for success message
        await waitFor(() => {
            expect(screen.getByText('Thank you for your feedback!')).toBeInTheDocument();
        });
    });

    it('should show error message when submission fails', async () => {
        const mockFailedSubmit = jest.fn().mockRejectedValue(new Error('Submit failed'));

        render(
            <FeedbackPopup
                isOpen={true}
                onClose={mockOnClose}
                onSubmit={mockFailedSubmit}
            />
        );

        // Fill out the form
        fireEvent.change(screen.getByLabelText('Your Feedback'), { target: { value: 'Test feedback' } });

        // Submit the form
        fireEvent.click(screen.getByText('Submit Feedback'));

        // Check for error message
        await waitFor(() => {
            expect(screen.getByText('Failed to submit feedback. Please try again later.')).toBeInTheDocument();
        });
    });

    it('should disable submit button when no message is provided', () => {
        render(
            <FeedbackPopup
                isOpen={true}
                onClose={mockOnClose}
                onSubmit={mockOnSubmit}
            />
        );

        const submitButton = screen.getByText('Submit Feedback');
        expect(submitButton).toBeDisabled();

        // Add text to enable button
        fireEvent.change(screen.getByLabelText('Your Feedback'), { target: { value: 'Test' } });
        expect(submitButton).not.toBeDisabled();

        // Remove text to disable button again
        fireEvent.change(screen.getByLabelText('Your Feedback'), { target: { value: '' } });
        expect(submitButton).toBeDisabled();
    });
}); 