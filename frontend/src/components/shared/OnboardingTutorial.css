/* Tutorial overlay - covers the entire screen when not targeting specific elements */
.tutorial-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Modal for non-targeted tutorials */
.tutorial-modal {
  width: 90%;
  max-width: 500px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: fadeIn 0.3s ease-in-out;
}

/* Tooltip for targeted elements */
.tutorial-tooltip {
  position: absolute;
  width: 300px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  animation: fadeIn 0.3s ease-in-out;
}

/* Tooltip placement variations */
.tutorial-tooltip::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

.tutorial-placement-top::before {
  border-width: 10px 10px 0 10px;
  border-color: white transparent transparent transparent;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.tutorial-placement-right::before {
  border-width: 10px 10px 10px 0;
  border-color: transparent white transparent transparent;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
}

.tutorial-placement-bottom::before {
  border-width: 0 10px 10px 10px;
  border-color: transparent transparent white transparent;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.tutorial-placement-left::before {
  border-width: 10px 0 10px 10px;
  border-color: transparent transparent transparent white;
  right: -10px;
  top: 50%;
  transform: translateY(-50%);
}

/* Tutorial header */
.tutorial-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.tutorial-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
  font-weight: 600;
}

.tutorial-close-btn {
  background: none;
  border: none;
  color: #999;
  font-size: 24px;
  cursor: pointer;
  padding: 0 5px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tutorial-close-btn:hover {
  color: #333;
}

/* Tutorial content */
.tutorial-content {
  padding: 20px;
}

.tutorial-content p {
  margin: 0;
  font-size: 15px;
  line-height: 1.5;
  color: #666;
}

/* Tutorial footer */
.tutorial-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background-color: #f9f9f9;
}

.tutorial-progress {
  font-size: 14px;
  color: #666;
}

.tutorial-actions {
  display: flex;
  gap: 10px;
}

/* Button styles */
.tutorial-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: flex;
  align-items: center;
  gap: 6px;
}

.tutorial-back-btn {
  background-color: transparent;
  color: #666;
  border: 1px solid #ddd;
}

.tutorial-back-btn:hover {
  background-color: #f5f5f5;
}

.tutorial-skip-btn {
  background-color: transparent;
  color: #666;
  border: 1px solid #ddd;
}

.tutorial-skip-btn:hover {
  background-color: #f5f5f5;
}

.tutorial-next-btn {
  background-color: #2196F3;
  color: white;
}

.tutorial-next-btn:hover {
  background-color: #1976D2;
}

/* Icon styling */
.tutorial-btn svg {
  font-size: 18px;
}

.tutorial-close-btn svg {
  font-size: 20px;
}

/* Animation */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tutorial-tooltip {
    width: 90%;
    max-width: 300px;
  }
  
  .tutorial-footer {
    flex-direction: column;
    gap: 10px;
  }
  
  .tutorial-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .tutorial-btn {
    padding: 8px 12px;
    font-size: 13px;
  }
} 