import React, { useCallback, useState } from 'react';
import FeedbackPopup, { FeedbackData } from './FeedbackPopup';

interface UseFeedbackResult {
    openFeedback: () => void;
    FeedbackPopupComponent: React.ReactNode;
}

const useFeedback = (onSubmitFeedback: (data: FeedbackData) => Promise<void>): UseFeedbackResult => {
    const [isOpen, setIsOpen] = useState(false);

    const openFeedback = useCallback(() => {
        setIsOpen(true);
    }, []);

    const closeFeedback = useCallback(() => {
        setIsOpen(false);
    }, []);

    const handleSubmit = useCallback(
        async (data: FeedbackData) => {
            await onSubmitFeedback(data);
        },
        [onSubmitFeedback]
    );

    const FeedbackPopupComponent = (
        <FeedbackPopup
            isOpen={isOpen}
            onClose={closeFeedback}
            onSubmit={handleSubmit}
        />
    );

    return {
        openFeedback,
        FeedbackPopupComponent,
    };
};

export default useFeedback; 