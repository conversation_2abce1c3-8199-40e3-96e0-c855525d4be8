import React, { useEffect, useRef, useState } from 'react';
import './OnboardingTutorial.css';
// Import from our centralized Icon component
import { Icon } from './Icons';

export interface TutorialStep {
    id: string;
    title: string;
    content: string;
    target?: string; // CSS selector for the element to highlight
    targetElement?: string; // Alternative CSS selector for targeting elements
    placement?: 'top' | 'right' | 'bottom' | 'left';
}

interface OnboardingTutorialProps {
    steps: TutorialStep[];
    isOpen: boolean;
    startAtStep?: number;
    onComplete: () => void;
    onClose: () => void;
    onStepChange: (stepIndex: number) => void;
}

interface Position {
    top: number;
    left: number;
}

/**
 * OnboardingTutorial component that provides step-by-step guidance for users
 */
const OnboardingTutorial: React.FC<OnboardingTutorialProps> = ({
    steps,
    isOpen,
    startAtStep = 0,
    onComplete,
    onClose,
    onStepChange
}) => {
    const [currentStep, setCurrentStep] = useState(startAtStep);
    const [position, setPosition] = useState<Position>({ top: 0, left: 0 });
    const tooltipRef = useRef<HTMLDivElement>(null);

    // Calculate position based on target element and placement
    useEffect(() => {
        if (!isOpen || !steps[currentStep]) {
            return;
        }

        // Use either target or targetElement property
        const targetSelector = steps[currentStep].target || steps[currentStep].targetElement;
        if (!targetSelector) {
            return;
        }

        const targetElement = document.querySelector(targetSelector);
        if (!targetElement || !tooltipRef.current) {
            return;
        }

        const targetRect = targetElement.getBoundingClientRect();
        const tooltipRect = tooltipRef.current.getBoundingClientRect();
        const placement = steps[currentStep].placement || 'bottom';

        let top = 0;
        let left = 0;

        switch (placement) {
            case 'top':
                top = targetRect.top - tooltipRect.height - 10;
                left = targetRect.left + (targetRect.width - tooltipRect.width) / 2;
                break;
            case 'right':
                top = targetRect.top + (targetRect.height - tooltipRect.height) / 2;
                left = targetRect.right + 10;
                break;
            case 'bottom':
                top = targetRect.bottom + 10;
                left = targetRect.left + (targetRect.width - tooltipRect.width) / 2;
                break;
            case 'left':
                top = targetRect.top + (targetRect.height - tooltipRect.height) / 2;
                left = targetRect.left - tooltipRect.width - 10;
                break;
        }

        // Ensure tooltip stays within viewport
        const viewport = {
            width: window.innerWidth || document.documentElement.clientWidth,
            height: window.innerHeight || document.documentElement.clientHeight
        };

        // Adjust horizontal position to keep tooltip within viewport
        if (left < 10) left = 10;
        if (left + tooltipRect.width > viewport.width - 10) {
            left = viewport.width - tooltipRect.width - 10;
        }

        // Adjust vertical position to keep tooltip within viewport
        if (top < 10) top = 10;
        if (top + tooltipRect.height > viewport.height - 10) {
            top = viewport.height - tooltipRect.height - 10;
        }

        setPosition({ top, left });
    }, [currentStep, isOpen, steps]);

    // Reset to start step when tutorial is reopened
    useEffect(() => {
        if (isOpen) {
            setCurrentStep(startAtStep);
        }
    }, [isOpen, startAtStep]);

    if (!isOpen || steps.length === 0) {
        return null;
    }

    const step = steps[currentStep];
    const isTargeted = !!(step.target || step.targetElement);

    const handleNext = () => {
        if (currentStep < steps.length - 1) {
            const nextStep = currentStep + 1;
            setCurrentStep(nextStep);
            onStepChange(nextStep);
        } else {
            // Last step, complete the tutorial
            onComplete();
        }
    };

    const handlePrevious = () => {
        if (currentStep > 0) {
            const prevStep = currentStep - 1;
            setCurrentStep(prevStep);
            onStepChange(prevStep);
        }
    };

    const handleSkip = () => {
        onClose();
    };

    return (
        <div className="tutorial-overlay">
            {isTargeted ? (
                <div
                    ref={tooltipRef}
                    className={`tutorial-tooltip tutorial-placement-${step.placement || 'bottom'}`}
                    style={{
                        top: `${position.top}px`,
                        left: `${position.left}px`
                    }}
                >
                    <div className="tutorial-header">
                        <h3>{step.title}</h3>
                        <button className="tutorial-close-btn" onClick={handleSkip}>
                            <Icon name="close" size="small" />
                        </button>
                    </div>

                    <div className="tutorial-content">
                        <p>{step.content}</p>
                    </div>

                    <div className="tutorial-footer">
                        <div className="tutorial-progress">
                            {currentStep + 1} of {steps.length}
                        </div>
                        <div className="tutorial-actions">
                            {currentStep > 0 && (
                                <button className="tutorial-btn tutorial-back-btn" onClick={handlePrevious}>
                                    <Icon name="previous" size="small" /> Previous
                                </button>
                            )}
                            <button className="tutorial-btn tutorial-skip-btn" onClick={handleSkip}>
                                <Icon name="cancel" size="small" /> Skip
                            </button>
                            <button className="tutorial-btn tutorial-next-btn" onClick={handleNext}>
                                {currentStep < steps.length - 1 ? (
                                    <>Next <Icon name="next" size="small" /></>
                                ) : (
                                    <>Finish <Icon name="check" size="small" /></>
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="tutorial-modal">
                    <div className="tutorial-header">
                        <h3>{step.title}</h3>
                        <button className="tutorial-close-btn" onClick={handleSkip}>
                            <Icon name="close" size="small" />
                        </button>
                    </div>

                    <div className="tutorial-content">
                        <p>{step.content}</p>
                    </div>

                    <div className="tutorial-footer">
                        <div className="tutorial-progress">
                            {currentStep + 1} of {steps.length}
                        </div>
                        <div className="tutorial-actions">
                            {currentStep > 0 && (
                                <button className="tutorial-btn tutorial-back-btn" onClick={handlePrevious}>
                                    <Icon name="previous" size="small" /> Previous
                                </button>
                            )}
                            <button className="tutorial-btn tutorial-skip-btn" onClick={handleSkip}>
                                <Icon name="cancel" size="small" /> Skip
                            </button>
                            <button className="tutorial-btn tutorial-next-btn" onClick={handleNext}>
                                {currentStep < steps.length - 1 ? (
                                    <>Next <Icon name="next" size="small" /></>
                                ) : (
                                    <>Finish <Icon name="check" size="small" /></>
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default OnboardingTutorial; 