import React, { useEffect, useState } from 'react';
import api, { TutorialStatus } from '../../services/api';
import OnboardingTutorial, { TutorialStep } from './OnboardingTutorial';

// We're using the TutorialStatus from the API client
type OnboardingStatus = TutorialStatus;

interface UseOnboardingResult {
    startTutorial: () => void;
    OnboardingTutorialComponent: React.ReactNode;
    isOnboardingComplete: boolean;
}

/**
 * A custom hook that manages the onboarding experience with the tutorial UI
 * @param tutorialId Unique identifier for this specific tutorial
 * @param steps Array of tutorial steps to display
 * @param forceShow Whether to automatically show the tutorial if not completed
 */
const useOnboarding = (
    tutorialId: string,
    steps: TutorialStep[],
    forceShow = false
): UseOnboardingResult => {
    const [isOpen, setIsOpen] = useState(false);
    const [currentStep, setCurrentStep] = useState(0);
    const [onboardingStatus, setOnboardingStatus] = useState<OnboardingStatus>({
        completed: false,
        lastStep: 0,
        lastSeenDate: null,
    });

    // Check if the tutorial API is available
    const tutorialApiAvailable = Boolean(api.tutorial &&
        typeof api.tutorial.getStatus === 'function' &&
        typeof api.tutorial.updateStatus === 'function');

    // Load onboarding status from API
    useEffect(() => {
        const loadOnboardingStatus = async () => {
            // Skip if the API is not available
            if (!tutorialApiAvailable) {
                console.warn('Tutorial API is not available, using default status');
                return;
            }

            try {
                const status = await api.tutorial.getStatus();

                if (status && status[tutorialId]) {
                    setOnboardingStatus(status[tutorialId]);
                    setCurrentStep(status[tutorialId].lastStep || 0);

                    // Auto-show tutorial if not completed and forceShow is true
                    if (forceShow && !status[tutorialId].completed) {
                        setIsOpen(true);
                    }
                }
            } catch (error) {
                console.error('Failed to load onboarding status:', error);
            }
        };

        loadOnboardingStatus();
    }, [tutorialId, forceShow, tutorialApiAvailable]);

    // Update onboarding status in API
    const updateOnboardingStatus = async (status: Partial<OnboardingStatus>) => {
        // Skip if the API is not available
        if (!tutorialApiAvailable) {
            console.warn('Tutorial API is not available, status not saved');
            // Update local state anyway
            setOnboardingStatus(prev => ({
                ...prev,
                ...status,
                lastSeenDate: new Date().toISOString(),
            }));
            return;
        }

        try {
            await api.tutorial.updateStatus({
                [tutorialId]: {
                    ...onboardingStatus,
                    ...status,
                    lastSeenDate: new Date().toISOString(),
                },
            });
        } catch (error) {
            console.error('Failed to update onboarding status:', error);
        }
    };

    // Handle tutorial step change
    const handleStepChange = (stepIndex: number) => {
        setCurrentStep(stepIndex);
        updateOnboardingStatus({ lastStep: stepIndex });
    };

    // Handle tutorial completion
    const handleComplete = () => {
        setIsOpen(false);
        setOnboardingStatus({
            ...onboardingStatus,
            completed: true,
            lastStep: steps.length - 1,
            lastSeenDate: new Date().toISOString(),
        });
        updateOnboardingStatus({
            completed: true,
            lastStep: steps.length - 1,
        });
    };

    // Handle tutorial close without completion
    const handleClose = () => {
        setIsOpen(false);
        updateOnboardingStatus({ lastStep: currentStep });
    };

    // Start the tutorial
    const startTutorial = () => {
        setIsOpen(true);
    };

    const OnboardingTutorialComponent = (
        <OnboardingTutorial
            steps={steps}
            isOpen={isOpen}
            onComplete={handleComplete}
            onClose={handleClose}
            onStepChange={handleStepChange}
            startAtStep={currentStep}
        />
    );

    return {
        startTutorial,
        OnboardingTutorialComponent,
        isOnboardingComplete: onboardingStatus.completed,
    };
};

export default useOnboarding; 