.feedback-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.feedback-popup-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
}

.feedback-popup-close {
  position: absolute;
  top: 12px;
  right: 12px;
  background: transparent;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.feedback-popup-close:hover {
  color: #333;
}

.feedback-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: #4A90E2;
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.form-group small {
  margin-top: 4px;
  color: #666;
  font-size: 12px;
}

.rating-container {
  display: flex;
  gap: 8px;
}

.rating-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid #ddd;
  background-color: #fff;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rating-button.active {
  background-color: #4A90E2;
  color: white;
  border-color: #4A90E2;
}

.submit-button {
  background-color: #4A90E2;
  color: white;
  padding: 12px;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 8px;
}

.submit-button:hover:not(:disabled) {
  background-color: #3A80D2;
}

.submit-button:disabled {
  background-color: #B8B8B8;
  cursor: not-allowed;
}

.feedback-error {
  color: #e74c3c;
  background-color: #fdedeb;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
}

.feedback-success {
  text-align: center;
  padding: 20px 0;
}

.feedback-success p {
  font-size: 18px;
  color: #2ecc71;
  margin-bottom: 12px;
}

@media (max-width: 600px) {
  .feedback-popup-container {
    width: 95%;
    padding: 16px;
  }
  
  .form-group input,
  .form-group textarea,
  .form-group select {
    font-size: 14px;
  }
  
  .rating-button {
    width: 36px;
    height: 36px;
  }
} 