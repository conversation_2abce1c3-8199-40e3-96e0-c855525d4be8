.api-example {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
  margin: 20px 0;
  max-width: 800px;
}

.api-info {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f0f8ff;
  border-radius: 4px;
}

.health-status {
  margin-top: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.health-status ul {
  list-style: none;
  padding: 0;
}

.health-status li {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

.status-good {
  color: #2e7d32;
  font-weight: bold;
}

.status-bad {
  color: #c62828;
  font-weight: bold;
}

.error-message {
  padding: 10px;
  background-color: #ffebee;
  color: #c62828;
  border-radius: 4px;
  margin: 10px 0;
}

.endpoint-examples {
  margin-top: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.endpoint-examples ul {
  list-style: none;
  padding: 0;
}

.endpoint-examples li {
  padding: 5px 0;
}

code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
  color: #d32f2f;
} 