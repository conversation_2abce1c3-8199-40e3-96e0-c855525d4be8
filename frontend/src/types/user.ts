export interface User {
  id: number;
  username: string;
  email: string;
  fullName?: string;
  role?: string;
  createdAt: string;
  preferences?: {
    theme?: string;
    language?: string;
    notifications?: boolean;
  };
}

export interface UserProfile extends User {
  certifications?: UserCertification[];
  skills?: Skill[];
}

export interface UserCertification {
  id: number;
  certificationId: number;
  certificationName: string;
  dateEarned: string;
  expirationDate?: string;
  status: 'active' | 'expired' | 'pending';
}

export interface Skill {
  id: number;
  name: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
}

export interface UserPreferences {
  theme: string;
  language: string;
  notifications: boolean;
} 