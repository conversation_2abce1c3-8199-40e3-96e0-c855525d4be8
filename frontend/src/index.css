:root {
  /* Common variables */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans',
    'Helvetica Neue', sans-serif;
  --border-radius: 8px;
  --transition-speed: 0.3s;
  
  /* Light theme (default) - improved contrast */
  --color-primary: #0f3460;
  --color-primary-dark: #0a2348;
  --color-secondary: #16213e;
  --color-accent: #e63946;
  --color-accent-dark: #c1121f;
  --color-background: #f8f9fa;
  --color-card-background: #ffffff;
  --color-text: #333333;
  --color-text-muted: #555555;
  --color-border: #dce1e6;
  --color-shadow: rgba(0, 0, 0, 0.12);
  --color-overlay: rgba(255, 255, 255, 0.8);
  --color-hover: #f0f4f8;
}

[data-theme='dark'] {
  /* Dark theme with softer terminal colors */
  --color-primary: #4ecca3;         /* Softer teal-green */
  --color-primary-dark: #2e8b70;    /* Darker version */
  --color-secondary: #6bddbe;       /* Lighter accent */
  --color-accent: #7bdba8;          /* Soft mint green */
  --color-accent-dark: #57b485;     /* Darker accent */
  --color-background: #1a1a2e;      /* Deep navy blue background */
  --color-card-background: #222342; /* Slightly lighter navy card background */
  --color-text: #c8e0d0;            /* Soft pale green text */
  --color-text-muted: #7d9989;      /* Muted text */
  --color-border: #364c45;          /* Subtle border color */
  --color-shadow: rgba(15, 30, 22, 0.4); /* Softer shadow */
  --color-overlay: rgba(0, 10, 20, 0.85); /* Dark overlay */
  --color-hover: #283442;           /* Subtle hover state */
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  line-height: 1.6;
  background-color: var(--color-background);
  color: var(--color-text);
  transition: background-color var(--transition-speed), color var(--transition-speed);
}

a {
  color: var(--color-primary);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

button {
  cursor: pointer;
}

img {
  max-width: 100%;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--color-accent);
}

/* Animation for smooth theme transition */
*, *::before, *::after {
  transition: all 0.2s ease-in-out;
}

:root {
  /* Colors */
  --primary-color: #3f51b5;
  --secondary-color: #f50057;
  --background-color: #f5f5f5;
  --text-color: #333333;
  --error-color: #f44336;
  --success-color: #4caf50;
  
  /* Typography */
  --font-size-small: 0.875rem;
  --font-size-medium: 1rem;
  --font-size-large: 1.25rem;
  --font-size-xlarge: 1.5rem;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* Borders */
  --border-width: 1px;
}

/* Terminal scan line effect for dark mode */
[data-theme='dark'] body::before {
  content: "";
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    transparent 0px,
    rgba(0, 255, 0, 0.02) 1px,
    transparent 2px
  );
  pointer-events: none;
  z-index: 1000;
}

/* Add a subtle CRT flicker for dark mode */
[data-theme='dark'] body {
  animation: flicker 0.15s infinite alternate;
}

@keyframes flicker {
  0% {
    background-color: var(--color-background);
  }
  100% {
    background-color: rgba(12, 12, 12, 0.98);
  }
} 