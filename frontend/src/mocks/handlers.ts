// Mock API handlers to simulate backend responses
// This is a simplified version that doesn't rely on MSW
import { TutorialStatusResponse } from '../services/api';

// Define common HTTP methods for our mock handlers
const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH'
} as const;

// Type for HTTP methods
type HttpMethod = typeof HTTP_METHODS[keyof typeof HTTP_METHODS];

// Interface for mock handler response
interface MockResponse<T = any> {
  status: number;
  data: T;
}

// Interface for mock handler
interface MockHandler<T = any> {
  method: HttpMethod;
  url: string;
  response: MockResponse<T>;
  delay: number;
}

/**
 * Create a mock handler function
 * @param method HTTP method for the mock handler
 * @param url URL endpoint to mock
 * @param responseData Data to return in the response
 * @param statusCode HTTP status code to return
 * @param delay Artificial delay in ms to simulate network latency
 */
const createHandler = <T = any>(
  method: HttpMethod, 
  url: string, 
  responseData: T, 
  statusCode: number = 200, 
  delay: number = 0
): MockHandler<T> => {
  return {
    method,
    url,
    response: {
      status: statusCode,
      data: responseData
    },
    delay
  };
};

// Export our mock handlers
export const handlers = [
  // Mock API for user profile
  createHandler(
    HTTP_METHODS.GET,
    '/api/v1/users/profile',
    {
      id: 1,
      username: "johndoe",
      email: "<EMAIL>",
      fullName: "John Doe",
      role: "user",
      createdAt: "2023-01-15T00:00:00Z",
      preferences: {
        theme: "light",
        notifications: true,
        language: "en"
      }
    }
  ),

  // Mock API for authentication
  createHandler(
    HTTP_METHODS.POST,
    '/api/v1/auth/login',
    {
      access_token: "mock_token_12345",
      token_type: "Bearer",
      user_id: 1,
      username: "johndoe"
    }
  ),

  // Mock API for certification list
  createHandler<Array<{id: number, name: string, provider: string, level: string, category: string, description: string, examFee: number, popularity: number}>>(
    HTTP_METHODS.GET,
    '/api/v1/certifications',
    [
      {
        id: 1,
        name: "CompTIA Security+",
        provider: "CompTIA",
        level: "beginner",
        category: "general",
        description: "A foundational certification that validates baseline cybersecurity skills.",
        examFee: 381,
        popularity: 9
      },
      {
        id: 2,
        name: "CISSP",
        provider: "ISC²",
        level: "advanced",
        category: "management",
        description: "One of the most recognized security certifications. Covers security operations, engineering, architecture, and more.",
        examFee: 749,
        popularity: 10
      }
    ]
  ),

  // Mock API for user's certifications
  createHandler(
    HTTP_METHODS.GET,
    '/api/v1/users/certifications',
    [
      {
        id: 1,
        certificationId: 1,
        certificationName: "CompTIA Security+",
        dateEarned: "2022-05-20T00:00:00Z",
        expirationDate: "2025-05-20T00:00:00Z",
        status: "active"
      }
    ]
  ),

  // Mock API for FAQ
  createHandler(
    HTTP_METHODS.GET,
    '/api/v1/faqs',
    [
      {
        id: 1,
        question: "What is the difference between Security+ and CySA+?",
        answer: "Security+ is an entry-level certification while CySA+ is intermediate, focusing on security analytics and threat detection.",
        category: "certifications"
      },
      {
        id: 2,
        question: "How long does it typically take to prepare for the CISSP exam?",
        answer: "Most candidates study for 3-6 months, dedicating 10-15 hours per week.",
        category: "certifications"
      }
    ]
  ),

  // Mock API for job search
  createHandler(
    HTTP_METHODS.GET,
    '/api/v1/jobs',
    [
      {
        id: 1,
        title: "Junior Security Analyst",
        company: "SecureTech Solutions",
        location: "Remote",
        type: "Full-time",
        salary: "$65,000 - $80,000",
        description: "Looking for a junior security analyst to join our SOC team.",
        postedDate: "2023-04-15"
      },
      {
        id: 2,
        title: "Cybersecurity Engineer",
        company: "Global Financial Corp",
        location: "New York, NY",
        type: "Full-time",
        salary: "$95,000 - $120,000",
        description: "Seeking an experienced cybersecurity engineer to help protect our financial systems.",
        postedDate: "2023-04-10"
      }
    ]
  ),
  
  // Mock API for tutorial/onboarding status
  createHandler<TutorialStatusResponse>(
    HTTP_METHODS.GET,
    '/api/v1/users/tutorial',
    {
      'main-tutorial': {
        completed: false,
        lastStep: 2,
        lastSeenDate: "2023-04-10T15:30:00Z",
      },
      'feature-walkthrough': {
        completed: true,
        lastStep: 5,
        lastSeenDate: "2023-04-12T10:15:00Z",
      }
    }
  ),
  
  // Mock API for updating tutorial status
  createHandler(
    HTTP_METHODS.PUT,
    '/api/v1/users/tutorial',
    {
      success: true,
      message: "Tutorial status updated successfully"
    }
  )
];

/**
 * Get a mock response for a given API request
 * @param method HTTP method of the request
 * @param url URL endpoint of the request
 * @returns Mock response object with status and data
 */
export const getMockResponse = (method: HttpMethod, url: string): MockResponse => {
  const handler = handlers.find(h => h.method === method && h.url === url);
  if (!handler) {
    return {
      status: 404,
      data: { error: 'Not found' }
    };
  }
  return handler.response;
};

/**
 * Mock implementation of the fetch API for testing and development
 * @param url URL to fetch
 * @param options Fetch options including method, headers, body
 * @returns A promise that resolves to a Response-like object
 */
export const mockFetch = async (url: string, options: RequestInit = {}): Promise<Response> => {
  const method = (options.method || 'GET') as HttpMethod;
  
  // Wait for specified delay or default to a small random delay
  const handler = handlers.find(h => h.method === method && h.url === url);
  const delay = handler?.delay || Math.random() * 200;
  await new Promise(resolve => setTimeout(resolve, delay));
  
  const response = getMockResponse(method, url);
  
  // Return a Response-like object
  return {
    ok: response.status >= 200 && response.status < 300,
    status: response.status,
    json: async () => response.data,
    text: async () => JSON.stringify(response.data)
  } as Response;
}; 