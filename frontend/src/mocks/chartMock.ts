/**
 * Mock implementation for Chart.js to avoid the dependency
 * This is used when the actual Chart.js library is not available
 */

// Simple mock for Chart class
export class Chart {
  constructor(ctx: any, config: any) {
    // In a real implementation, this would create a chart on the canvas
    console.log('Chart constructor called with config:', config);
    
    // Store any properties we might need later
    this.config = config;
    this.ctx = ctx;
    
    // Draw a simple placeholder on the canvas
    if (ctx && ctx.getContext) {
      const context = ctx.getContext('2d');
      if (context) {
        this.drawPlaceholder(context, ctx.width, ctx.height);
      }
    }
  }
  
  // Properties
  config: any;
  ctx: any;
  
  // Methods
  update() {
    console.log('Chart update called');
    return this;
  }
  
  destroy() {
    console.log('Chart destroy called');
    return this;
  }
  
  resize() {
    console.log('Chart resize called');
    return this;
  }
  
  render() {
    console.log('Chart render called');
    return this;
  }
  
  // Helper to draw a placeholder
  private drawPlaceholder(ctx: CanvasRenderingContext2D, width: number, height: number) {
    // Draw a simple placeholder chart
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, width, height);
    
    ctx.fillStyle = '#007bff';
    ctx.fillRect(50, 50, 100, 100);
    
    ctx.fillStyle = '#28a745';
    ctx.fillRect(170, 50, 100, 100);
    
    ctx.fillStyle = '#dc3545';
    ctx.fillRect(290, 50, 100, 100);
    
    ctx.fillStyle = '#333';
    ctx.font = '14px Arial';
    ctx.fillText('Chart.js Mock', 50, 30);
  }
  
  // Static methods
  static register(...args: any[]) {
    console.log('Chart.register called with:', args);
  }
}

// Mock components
export const ArcElement = 'ArcElement (mocked)';
export const LineElement = 'LineElement (mocked)';
export const BarElement = 'BarElement (mocked)';
export const PointElement = 'PointElement (mocked)';
export const Legend = 'Legend (mocked)';
export const Tooltip = 'Tooltip (mocked)';
export const CategoryScale = 'CategoryScale (mocked)';
export const LinearScale = 'LinearScale (mocked)';

// Mock chart types
export function Line(ctx: any, config: any) {
  return new Chart(ctx, { ...config, type: 'line' });
}

export function Bar(ctx: any, config: any) {
  return new Chart(ctx, { ...config, type: 'bar' });
}

export function Doughnut(ctx: any, config: any) {
  return new Chart(ctx, { ...config, type: 'doughnut' });
}

export function Pie(ctx: any, config: any) {
  return new Chart(ctx, { ...config, type: 'pie' });
}

// Export default
export default Chart; 