/**
 * API Versioning Tests
 * 
 * Tests to ensure the API versioning system works correctly.
 */

import { API_VERSION, getVersionedEndpoint } from '../constants/apiEndpoints';
import apiService from '../services/apiService';

describe('API Versioning', () => {
  describe('getVersionedEndpoint utility', () => {
    it('should create properly versioned endpoints with current version', () => {
      const endpoint = getVersionedEndpoint('users/profile');
      expect(endpoint).toBe(`/api/${API_VERSION}/users/profile`);
    });

    it('should create properly versioned endpoints with specific version', () => {
      const endpoint = getVersionedEndpoint('users/profile', 'v2');
      expect(endpoint).toBe('/api/v2/users/profile');
    });

    it('should handle leading slashes in paths', () => {
      const endpoint = getVersionedEndpoint('/users/profile');
      expect(endpoint).toBe(`/api/${API_VERSION}/users/profile`);
    });

    it('should handle multiple leading slashes', () => {
      const endpoint = getVersionedEndpoint('///users/profile');
      expect(endpoint).toBe(`/api/${API_VERSION}/users/profile`);
    });
  });

  describe('ApiService', () => {
    it('should return the current API version', () => {
      expect(apiService.getApiVersion()).toBe(API_VERSION);
    });

    it('should create versioned endpoints', () => {
      const endpoint = apiService.getEndpoint('users/profile');
      expect(endpoint).toBe(`/api/${API_VERSION}/users/profile`);
    });

    it('should create endpoints with specific version', () => {
      const endpoint = apiService.getEndpoint('users/profile', 'v2');
      expect(endpoint).toBe('/api/v2/users/profile');
    });

    it('should allow changing API version', () => {
      const originalVersion = apiService.getApiVersion();
      
      // Set a new version
      apiService.setApiVersion('v2');
      expect(apiService.getApiVersion()).toBe('v2');
      
      // Check that endpoints use the new version
      const endpoint = apiService.getEndpoint('users/profile');
      expect(endpoint).toBe('/api/v2/users/profile');
      
      // Restore original version
      apiService.setApiVersion(originalVersion);
    });
  });
}); 