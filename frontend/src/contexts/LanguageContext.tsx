import React, { createContext, useContext, useEffect, useState } from 'react';

// Supported languages
export const SUPPORTED_LANGUAGES = {
  en: { name: 'English', flag: '🇬🇧' },
  de: { name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪' },
  es: { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸' },
  fr: { name: 'Français', flag: '🇫🇷' },
  af: { name: 'Afrikaans', flag: '🇿🇦' },
  zu: { name: 'isiZulu', flag: '🇿🇦' },
  ro: { name: 'Rom<PERSON><PERSON>', flag: '🇷🇴' },
};

type LanguageCode = keyof typeof SUPPORTED_LANGUAGES;

interface LanguageContextType {
  currentLanguage: LanguageCode;
  supportedLanguages: typeof SUPPORTED_LANGUAGES;
  changeLanguage: (code: LanguageCode) => void;
  translate: (key: string, defaultText?: string, params?: Record<string, string | number>) => string;
}

// Default translations will be loaded dynamically
const translations: Record<LanguageCode, Record<string, string>> = {
  en: {
    // Preload some essential English translations to ensure immediate rendering
    'main.hero.title': 'Navigate Your Cybersecurity Career Path',
    'main.hero.subtitle': 'Find, Plan, and Achieve the Right Certifications for Your Career Goals'
  },
  de: {},
  es: {},
  fr: {},
  af: {},
  zu: {},
  ro: {},
};

// Create context with default value
const LanguageContext = createContext<LanguageContextType>({
  currentLanguage: 'en',
  supportedLanguages: SUPPORTED_LANGUAGES,
  changeLanguage: () => { },
  translate: (key, defaultText) => defaultText || key,
});

// Hook for using the language context
export const useLanguage = () => useContext(LanguageContext);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>('en');
  const [isLoaded, setIsLoaded] = useState<Record<LanguageCode, boolean>>({
    en: true,  // English is loaded by default
    de: false,
    es: false,
    fr: false,
    af: false,
    zu: false,
    ro: false,
  });

  // Load saved language preference on mount
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as LanguageCode;
    if (savedLanguage && SUPPORTED_LANGUAGES[savedLanguage]) {
      setCurrentLanguage(savedLanguage);
      loadTranslations(savedLanguage);
    } else {
      // Ensure we have at least some translations loaded
      loadTranslations('en');
    }
  }, []);

  // Load translations for a language
  const loadTranslations = async (language: LanguageCode) => {
    if (isLoaded[language]) return;

    try {
      // In a real app, you would load translations from an API or file
      // For now, we'll mock this with a timeout
      await new Promise(resolve => setTimeout(resolve, 300));

      // This would be replaced with actual API call:
      // const response = await api.translations.getByLanguage(language);
      // translations[language] = response.data;

      // Mock some translations
      translations[language] = {
        // Hero section
        'main.hero.title': language === 'en' ? 'Navigate Your Cybersecurity Career Path' :
          language === 'de' ? 'Navigieren Sie Ihren Weg in der Cybersicherheit' :
            language === 'es' ? 'Navegue su Trayectoria en Ciberseguridad' :
              language === 'fr' ? 'Naviguez Votre Parcours en Cybersécurité' :
                language === 'af' ? 'Navigeer Jou Kubersveiligheid Loopbaan' :
                  language === 'zu' ? 'Hambisa Indlela Yakho Yezokuphepha Kwe-Cyber' :
                    'Navigați Cariera în Securitate Cibernetică',

        'main.hero.subtitle': language === 'en' ? 'Find, Plan, and Achieve the Right Certifications for Your Career Goals' :
          language === 'de' ? 'Finden, Planen und Erreichen Sie die richtigen Zertifizierungen für Ihre Karriereziele' :
            language === 'es' ? 'Encuentre, Planifique y Logre las Certificaciones Adecuadas para sus Objetivos Profesionales' :
              language === 'fr' ? 'Trouvez, Planifiez et Obtenez les Bonnes Certifications pour Vos Objectifs de Carrière' :
                language === 'af' ? 'Vind, Beplan en Bereik die Regte Sertifiserings vir Jou Loopbaandoelwitte' :
                  language === 'zu' ? 'Thola, Hlela futhi Uphumelele Izitifiketi Ezifanele Izinjongo Zomsebenzi Wakho' :
                    'Găsiți, Planificați și Obțineți Certificările Potrivite pentru Obiectivele Dvs. de Carieră',

        'main.hero.pitch.1': language === 'en' ? 'CertRats gives you the strategic edge in the competitive cybersecurity landscape. Our data-driven platform has helped thousands of professionals increase their salary by an average of 32% within 12 months.' :
          language === 'de' ? 'CertRats verschafft Ihnen den strategischen Vorteil in der wettbewerbsintensiven Cybersicherheitslandschaft. Unsere datengesteuerte Plattform hat Tausenden von Fachleuten geholfen, ihr Gehalt innerhalb von 12 Monaten um durchschnittlich 32% zu steigern.' :
            'CertRats te da la ventaja estratégica en el competitivo panorama de la ciberseguridad. Nuestra plataforma impulsada por datos ha ayudado a miles de profesionales a aumentar su salario en un promedio del 32% en 12 meses.',

        'main.hero.pitch.2': language === 'en' ? 'Stop guessing which certifications to pursue. Our intelligent algorithms analyze industry trends, salary data, and career trajectories to map your optimal certification path.' :
          language === 'de' ? 'Hören Sie auf zu raten, welche Zertifizierungen Sie verfolgen sollten. Unsere intelligenten Algorithmen analysieren Branchentrends, Gehaltsdaten und Karriereverläufe, um Ihren optimalen Zertifizierungspfad zu erstellen.' :
            'Deja de adivinar qué certificaciones obtener. Nuestros algoritmos inteligentes analizan tendencias de la industria, datos salariales y trayectorias profesionales para mapear tu camino óptimo de certificación.',

        // Benefits
        'main.hero.benefit.1': language === 'en' ? 'Save 100+ hours of research and $1000s in misdirected training' :
          'Sparen Sie über 100 Stunden Recherche und Tausende Euro für fehlgeleitete Schulungen',

        'main.hero.benefit.2': language === 'en' ? 'Receive personalized study plans tailored to your schedule' :
          'Erhalten Sie personalisierte Studienpläne, die auf Ihren Zeitplan zugeschnitten sind',

        'main.hero.benefit.3': language === 'en' ? 'Calculate accurate ROI for each certification' :
          'Berechnen Sie den genauen ROI für jede Zertifizierung',

        'main.hero.benefit.4': language === 'en' ? 'Connect with employers seeking your exact credentials' :
          'Vernetzen Sie sich mit Arbeitgebern, die genau Ihre Qualifikationen suchen',

        // CTAs
        'main.hero.cta.start': language === 'en' ? 'Start Your Journey' : 'Starten Sie Ihre Reise',
        'main.hero.cta.dashboard': language === 'en' ? 'Go to Dashboard' : 'Zum Dashboard',
        'main.hero.cta.explore': language === 'en' ? 'Explore Certifications' : 'Zertifizierungen erkunden',

        // Visual
        'main.hero.visual.placeholder': language === 'en' ? 'Career Path Visualization' : 'Visualisierung des Karrierewegs',

        // Certification Explorer
        'certifications.pagination.page_of': language === 'en' ? 'Page {{page}} of {{total}}' : 'Seite {{page}} von {{total}}',
        'certifications.list.count': language === 'en' ? '({{count}})' : '({{count}})',
        'certifications.list.showing': language === 'en' ? 'Showing {{showing}} of {{total}} certifications' :
          'Zeige {{showing}} von {{total}} Zertifizierungen',

        // More translations would be added here
      };

      setIsLoaded(prev => ({ ...prev, [language]: true }));
      console.log(`Loaded translations for ${language}`, translations[language]);
    } catch (error) {
      console.error(`Failed to load translations for ${language}:`, error);
    }
  };

  // Change language
  const changeLanguage = (language: LanguageCode) => {
    if (SUPPORTED_LANGUAGES[language]) {
      setCurrentLanguage(language);
      localStorage.setItem('language', language);

      // Load translations if not already loaded
      if (!isLoaded[language]) {
        loadTranslations(language);
      }
    }
  };

  // Helper function to replace template placeholders
  const replacePlaceholders = (text: string, params?: Record<string, string | number>): string => {
    if (!params) return text;

    return text.replace(/\{\{([^}]+)\}\}/g, (match, key) => {
      const replacement = params[key];
      return replacement !== undefined ? String(replacement) : match;
    });
  };

  // Translate a key
  const translate = (key: string, defaultText?: string, params?: Record<string, string | number>): string => {
    // First try current language
    if (translations[currentLanguage] && translations[currentLanguage][key]) {
      return replacePlaceholders(translations[currentLanguage][key], params);
    }

    // Then try English as fallback
    if (translations.en && translations.en[key]) {
      return replacePlaceholders(translations.en[key], params);
    }

    // If default text is provided, return it with placeholder substitution
    if (defaultText) {
      return replacePlaceholders(defaultText, params);
    }

    // Last resort, return the key itself
    return key;
  };

  // Context value
  const contextValue: LanguageContextType = {
    currentLanguage,
    supportedLanguages: SUPPORTED_LANGUAGES,
    changeLanguage,
    translate,
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

export default LanguageContext; 