import React, { createContext, useContext, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

// Analytics event types
export type EventCategory =
    | 'page_view'
    | 'click'
    | 'form_submit'
    | 'section_view'
    | 'conversion'
    | 'engagement';

export type PerformanceMetric =
    | 'load_time'
    | 'time_to_interactive'
    | 'first_contentful_paint'
    | 'largest_contentful_paint'
    | 'cumulative_layout_shift'
    | 'first_input_delay';

export interface AnalyticsEvent {
    category: EventCategory;
    action: string;
    label?: string;
    value?: number;
    timestamp: number;
    page?: string;
}

export interface PerformanceData {
    metric: PerformanceMetric;
    value: number;
    timestamp: number;
    page?: string;
}

interface AnalyticsContextType {
    trackEvent: (category: EventCategory, action: string, label?: string, value?: number) => void;
    trackPageView: (pageName: string) => void;
    trackPerformance: (metric: PerformanceMetric, value: number) => void;
    events: AnalyticsEvent[];
    performanceData: PerformanceData[];
}

// Create analytics context
const AnalyticsContext = createContext<AnalyticsContextType>({
    trackEvent: () => { },
    trackPageView: () => { },
    trackPerformance: () => { },
    events: [],
    performanceData: []
});

// Custom hook to use analytics
export const useAnalytics = () => useContext(AnalyticsContext);

interface AnalyticsProviderProps {
    children: React.ReactNode;
}

export const AnalyticsProvider: React.FC<AnalyticsProviderProps> = ({ children }) => {
    const [events, setEvents] = useState<AnalyticsEvent[]>([]);
    const [performanceData, setPerformanceData] = useState<PerformanceData[]>([]);
    const location = useLocation();

    // Track page views when location changes
    useEffect(() => {
        trackPageView(location.pathname);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [location]);

    // Track initial performance metrics on mount
    useEffect(() => {
        // Use Performance API if available
        if (window.performance && window.performance.timing) {
            const timing = window.performance.timing;

            // Calculate load time
            const loadTime = timing.loadEventEnd - timing.navigationStart;
            if (loadTime > 0) {
                trackPerformance('load_time', loadTime);
            }

            // Calculate time to interactive (approximation)
            const tti = timing.domInteractive - timing.navigationStart;
            if (tti > 0) {
                trackPerformance('time_to_interactive', tti);
            }
        }

        // Track Web Vitals if available
        if ('PerformanceObserver' in window) {
            try {
                // Track LCP (Largest Contentful Paint)
                const lcpObserver = new PerformanceObserver((entryList) => {
                    const entries = entryList.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    if (lastEntry) {
                        trackPerformance('largest_contentful_paint', lastEntry.startTime);
                    }
                });
                lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });

                // Track FID (First Input Delay)
                const fidObserver = new PerformanceObserver((entryList) => {
                    const entries = entryList.getEntries();
                    if (entries.length > 0) {
                        const firstInput = entries[0] as any;
                        trackPerformance('first_input_delay', firstInput.processingStart - firstInput.startTime);
                    }
                });
                fidObserver.observe({ type: 'first-input', buffered: true });

                // Track CLS (Cumulative Layout Shift)
                let clsValue = 0;
                const clsObserver = new PerformanceObserver((entryList) => {
                    const entries = entryList.getEntries();
                    entries.forEach(entry => {
                        if (!(entry as any).hadRecentInput) {
                            clsValue += (entry as any).value;
                            trackPerformance('cumulative_layout_shift', clsValue);
                        }
                    });
                });
                clsObserver.observe({ type: 'layout-shift', buffered: true });

                // Cleanup
                return () => {
                    lcpObserver.disconnect();
                    fidObserver.disconnect();
                    clsObserver.disconnect();
                };
            } catch (error) {
                console.error('Error setting up performance observers:', error);
            }
        }
    }, []);

    // Track an analytics event
    const trackEvent = (category: EventCategory, action: string, label?: string, value?: number) => {
        const event: AnalyticsEvent = {
            category,
            action,
            label,
            value,
            timestamp: Date.now(),
            page: location.pathname
        };

        // Log event to console in development
        if (process.env.NODE_ENV === 'development') {
            console.log('Analytics Event:', event);
        }

        // Add event to state
        setEvents(prevEvents => [...prevEvents, event]);

        // In a real implementation, we would send this to an analytics service
        // Example: sendToAnalyticsService(event);
    };

    // Track a page view
    const trackPageView = (pageName: string) => {
        trackEvent('page_view', 'view', pageName);

        // You would typically call your analytics service directly
        // Example: analytics.pageview(pageName);
    };

    // Track performance metrics
    const trackPerformance = (metric: PerformanceMetric, value: number) => {
        const data: PerformanceData = {
            metric,
            value,
            timestamp: Date.now(),
            page: location.pathname
        };

        // Log performance data to console in development
        if (process.env.NODE_ENV === 'development') {
            console.log('Performance Metric:', data);
        }

        // Add performance data to state
        setPerformanceData(prevData => [...prevData, data]);

        // In a real implementation, we would send this to a monitoring service
        // Example: sendToPerformanceMonitoring(data);
    };

    return (
        <AnalyticsContext.Provider value={{
            trackEvent,
            trackPageView,
            trackPerformance,
            events,
            performanceData
        }}>
            {children}
        </AnalyticsContext.Provider>
    );
};

export default AnalyticsContext; 