import React, { createContext, useContext, useEffect, useState } from 'react';

// Add 'crt' to ThemeMode
type ThemeMode = 'light' | 'dark' | 'crt';

interface ThemeContextType {
    theme: ThemeMode;
    setTheme: (mode: ThemeMode) => void;
    toggleTheme: () => void;
    isDark: boolean;
    isCRT: boolean;
}

export const ThemeContext = createContext<ThemeContextType>({
    theme: 'light',
    setTheme: () => { },
    toggleTheme: () => { },
    isDark: false,
    isCRT: false,
});

export const useTheme = () => useContext(ThemeContext);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [theme, setTheme] = useState<ThemeMode>(() => {
        const savedTheme = localStorage.getItem('theme') as ThemeMode;
        return savedTheme && ['light', 'dark', 'crt'].includes(savedTheme)
            ? savedTheme
            : 'light';
    });

    // Initialize theme from localStorage or system preference
    useEffect(() => {
        const savedTheme = localStorage.getItem('theme') as ThemeMode;
        if (savedTheme === 'light' || savedTheme === 'dark' || savedTheme === 'crt') {
            setTheme(savedTheme);
        } else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            setTheme('dark');
        }
    }, []);

    // Apply theme to the document body
    useEffect(() => {
        document.body.setAttribute('data-theme', theme === 'crt' ? 'dark' : theme);
        if (theme === 'crt') {
            document.body.classList.add('crt-darkmode');
        } else {
            document.body.classList.remove('crt-darkmode');
        }
        localStorage.setItem('theme', theme);
    }, [theme]);

    // Cycle through light -> dark -> crt -> light
    const toggleTheme = () => {
        setTheme(prev => prev === 'light' ? 'dark' : prev === 'dark' ? 'crt' : 'light');
    };

    return (
        <ThemeContext.Provider value={{
            theme,
            setTheme,
            toggleTheme,
            isDark: theme === 'dark',
            isCRT: theme === 'crt',
        }}>
            {children}
        </ThemeContext.Provider>
    );
};

export default ThemeContext; 