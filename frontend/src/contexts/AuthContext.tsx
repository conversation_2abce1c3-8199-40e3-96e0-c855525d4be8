import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';

// Define the shape of the authentication response
interface AuthResponse {
  access_token: string;
  token_type: string;
  user_id: number;
  username: string;
}

// Define the shape of a user
interface User {
  id: number;
  username: string;
  email?: string;
  role?: string;
  createdAt?: string;
}

// Define the shape of the authentication context
interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
  error: string | null;
  isLoading: boolean;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  user: null,
  login: async () => { },
  logout: () => { },
  loading: false,
  error: null,
  isLoading: true
});

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Check if user is already logged in
  useEffect(() => {
    const checkAuth = async () => {
      const storedToken = localStorage.getItem('auth_token');

      if (storedToken) {
        try {
          // Here you would validate the token with your API
          // const response = await api.auth.me();
          // setUser(response.data);

          // Mock implementation for now
          const userData = { id: 1, username: 'testuser' };
          setUser(userData);
        } catch (err) {
          // Token might be expired or invalid
          localStorage.removeItem('auth_token');
          console.error('Authentication error:', err);
        }
      }

      setLoading(false);
    };

    checkAuth();
  }, []);

  // Login function
  const login = async (username: string, password: string) => {
    setLoading(true);
    setError(null);

    try {
      // Call login API
      // const response = await api.auth.login({ username, password });

      // Mock successful response
      const response: AuthResponse = {
        access_token: 'mock_token_123',
        token_type: 'Bearer',
        user_id: 1,
        username: username
      };

      // Save token to local storage
      localStorage.setItem('auth_token', response.access_token);

      // Set user
      setUser({
        id: response.user_id,
        username: response.username
      });

      setLoading(false);
    } catch (err) {
      setError('Invalid username or password');
      setLoading(false);
      throw err;
    }
  };

  // Logout function
  const logout = () => {
    localStorage.removeItem('auth_token');
    setUser(null);
  };

  // Determine if authenticated
  const isAuthenticated = !!user;

  // Create value object
  const value = {
    isAuthenticated,
    user,
    login,
    logout,
    loading,
    error,
    isLoading: loading
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext; 