/* App-wide styles */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  font-family: var(--font-family);
}

/* Header styles */
.app-header {
  background-color: var(--color-primary);
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px var(--color-shadow);
  position: sticky;
  top: 0;
  z-index: 100;
}

.logo a {
  color: white;
  font-size: 24px;
  font-weight: 700;
  text-decoration: none;
  letter-spacing: 0.5px;
}

.app-header nav ul {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  gap: 24px;
}

.app-header nav a {
  color: rgba(255, 255, 255, 0.85);
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  transition: color 0.2s ease;
  padding: 8px 0;
  position: relative;
}

.app-header nav a:hover {
  color: white;
}

.app-header nav a:after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  background: white;
  left: 0;
  bottom: 0;
  transition: width 0.2s ease;
}

.app-header nav a:hover:after {
  width: 100%;
}

/* <PERSON><PERSON> controls container */
.header-controls {
  display: flex;
  align-items: center;
}

/* Theme toggle button */
.theme-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.theme-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Main content area */
.app-content {
  flex: 1;
  padding: 20px 0;
  background-color: var(--color-background);
}

/* Footer styles */
.app-footer {
  background-color: var(--color-secondary);
  color: white;
  padding: 24px;
  text-align: center;
}

.app-footer p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

/* Loading indicator */
.loader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  width: 100%;
}

.loader:after {
  content: '';
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-border);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive styles */
@media (max-width: 768px) {
  .app-header {
    flex-direction: column;
    padding: 16px;
  }
  
  .logo {
    margin-bottom: 16px;
  }
  
  .app-header nav ul {
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
  }
  
  .theme-toggle {
    position: absolute;
    top: 16px;
    right: 16px;
  }
}

@media (max-width: 480px) {
  .app-header nav ul {
    gap: 12px;
  }
  
  .app-header nav a {
    font-size: 14px;
  }
}

/* Terminal style for dark mode header and footer */
[data-theme='dark'] .app-header {
  background-color: #000000;
  border-bottom: 1px solid var(--color-primary);
  box-shadow: 0 0 10px var(--color-primary);
}

[data-theme='dark'] .app-header nav a {
  font-family: 'Courier New', monospace;
  color: var(--color-text);
}

[data-theme='dark'] .app-header nav a:hover {
  color: var(--color-secondary);
}

[data-theme='dark'] .app-header nav a::after {
  background: var(--color-primary);
}

[data-theme='dark'] .logo a {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: var(--color-primary);
  text-transform: uppercase;
}

[data-theme='dark'] .app-footer {
  background-color: #000000;
  border-top: 1px solid var(--color-primary);
  font-family: 'Courier New', monospace;
  color: var(--color-text);
}

/* Terminal-style loading indicator for dark mode */
[data-theme='dark'] .loader::after {
  border: 2px solid #000000;
  border-top: 2px solid var(--color-primary);
  box-shadow: 0 0 5px var(--color-primary);
} 