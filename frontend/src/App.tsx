import React, { Suspense } from 'react';
import { Link, Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import './App.css';
import LanguageSelector from './components/LanguageSelector';
import { AnalyticsProvider } from './contexts/AnalyticsContext';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider, useTheme } from './contexts/ThemeContext';
import Dashboard from './pages/Dashboard';
import MainPage from './pages/MainPage';
import NotFound from './pages/NotFound';

// Error handling utilities
import {
  ErrorBoundary,
  ErrorFallback,
  ErrorProvider,
  LazyLoadErrorBoundary,
  logCompilationError
} from './utils/error';

// Lazy-loaded components for better performance
const UserProfile = React.lazy(() => import('./pages/UserProfile/UserProfile')
  .catch(error => {
    logCompilationError(error, './pages/UserProfile/UserProfile');
    throw error;
  })
);

const AdminInterface = React.lazy(() => import('./pages/AdminInterface/AdminInterface')
  .catch(error => {
    logCompilationError(error, './pages/AdminInterface/AdminInterface');
    throw error;
  })
);

const FAQ = React.lazy(() => import('./pages/FAQ/FAQ')
  .catch(error => {
    logCompilationError(error, './pages/FAQ/FAQ');
    throw error;
  })
);

const StudyTimeTracker = React.lazy(() => import('./pages/StudyTimeTracker/StudyTimeTracker')
  .catch(error => {
    logCompilationError(error, './pages/StudyTimeTracker/StudyTimeTracker');
    throw error;
  })
);

const CostCalculator = React.lazy(() => import('./pages/CostCalculator/CostCalculator')
  .catch(error => {
    logCompilationError(error, './pages/CostCalculator/CostCalculator');
    throw error;
  })
);

const CareerPath = React.lazy(() => import('./pages/CareerPath/CareerPath')
  .catch(error => {
    logCompilationError(error, './pages/CareerPath/CareerPath');
    throw error;
  })
);

const UserJourneys = React.lazy(() => import('./pages/UserJourneys/UserJourneys')
  .catch(error => {
    logCompilationError(error, './pages/UserJourneys/UserJourneys');
    throw error;
  })
);

const JobSearch = React.lazy(() => import('./pages/JobSearch/JobSearch')
  .catch(error => {
    logCompilationError(error, './pages/JobSearch/JobSearch');
    throw error;
  })
);

const CertificationExplorer = React.lazy(() => import('./pages/CertificationExplorer/CertificationExplorer')
  .catch(error => {
    logCompilationError(error, './pages/CertificationExplorer/CertificationExplorer');
    // Create a component that matches the expected type from the import
    const FallbackComponent = () => <ErrorFallback error={error} />;
    return { default: FallbackComponent };
  }));

// Add TestErrorBoundary component for testing error handling
const TestErrorBoundary = React.lazy(() => import('./pages/TestErrorBoundary/TestErrorBoundary')
  .catch(error => {
    logCompilationError(error, './pages/TestErrorBoundary/TestErrorBoundary');
    throw error;
  })
);

// Loading component for lazy-loaded routes
const Loading = () => <div className="loader">Loading...</div>;

// Theme toggle button component
const ThemeToggle = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <button
      className="theme-toggle"
      onClick={toggleTheme}
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      {theme === 'light' ? '🌙' : '☀️'}
    </button>
  );
};

// Component wrapper with error boundary for lazy-loaded components
const LazyComponent = ({ component: Component, path }: { component: React.ComponentType, path: string }) => (
  <LazyLoadErrorBoundary modulePath={path}>
    <Suspense fallback={<Loading />}>
      <Component />
    </Suspense>
  </LazyLoadErrorBoundary>
);

function AppContent() {
  return (
    <div className="app-container">
      <header className="app-header">
        <div className="logo">
          <Link to="/">CertRats</Link>
        </div>
        <nav>
          <ul>
            <li><Link to="/">Home</Link></li>
            <li><Link to="/dashboard">Dashboard</Link></li>
            <li><Link to="/career-path">Career Path</Link></li>
            <li><Link to="/certification-explorer">Certifications</Link></li>
            <li><Link to="/faq">FAQ</Link></li>
          </ul>
        </nav>
        <div className="header-controls">
          <LanguageSelector />
          <ThemeToggle />
        </div>
      </header>
      <main className="app-content">
        <ErrorBoundary>
          <Suspense fallback={<Loading />}>
            <Routes>
              <Route path="/" element={<MainPage />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/user-profile" element={<LazyComponent component={UserProfile} path="/user-profile" />} />
              <Route path="/admin" element={
                <ErrorBoundary>
                  <LazyComponent component={AdminInterface} path="./pages/AdminInterface/AdminInterface" />
                </ErrorBoundary>
              } />
              <Route path="/faq" element={
                <ErrorBoundary>
                  <LazyComponent component={FAQ} path="./pages/FAQ/FAQ" />
                </ErrorBoundary>
              } />
              <Route path="/study-time" element={
                <ErrorBoundary>
                  <LazyComponent component={StudyTimeTracker} path="./pages/StudyTimeTracker/StudyTimeTracker" />
                </ErrorBoundary>
              } />
              <Route path="/cost-calculator" element={
                <ErrorBoundary>
                  <LazyComponent component={CostCalculator} path="./pages/CostCalculator/CostCalculator" />
                </ErrorBoundary>
              } />
              <Route path="/career-path" element={
                <ErrorBoundary>
                  <LazyComponent component={CareerPath} path="./pages/CareerPath/CareerPath" />
                </ErrorBoundary>
              } />
              <Route path="/journeys" element={
                <ErrorBoundary>
                  <LazyComponent component={UserJourneys} path="./pages/UserJourneys/UserJourneys" />
                </ErrorBoundary>
              } />
              <Route path="/jobs" element={
                <ErrorBoundary>
                  <LazyComponent component={JobSearch} path="./pages/JobSearch/JobSearch" />
                </ErrorBoundary>
              } />
              <Route path="/certification-explorer" element={
                <LazyComponent component={CertificationExplorer} path="./pages/CertificationExplorer/CertificationExplorer" />
              } />
              <Route path="/certifications" element={
                <LazyComponent component={CertificationExplorer} path="./pages/CertificationExplorer/CertificationExplorer" />
              } />
              <Route path="/test-error-boundary" element={
                <LazyComponent component={TestErrorBoundary} path="./pages/TestErrorBoundary/TestErrorBoundary" />
              } />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Suspense>
        </ErrorBoundary>
      </main>
      <footer className="app-footer">
        <p>&copy; 2023 CertRats - Security Certification Explorer</p>
      </footer>
    </div>
  );
}

function App() {
  return (
    <ErrorProvider>
      <ThemeProvider>
        <AuthProvider>
          <Router>
            <AnalyticsProvider>
              <AppContent />
            </AnalyticsProvider>
          </Router>
        </AuthProvider>
      </ThemeProvider>
    </ErrorProvider>
  );
}

export default App; 