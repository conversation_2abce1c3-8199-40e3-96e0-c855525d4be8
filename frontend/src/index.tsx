import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { LanguageProvider } from './contexts/LanguageContext';
import './index.css';
import reportWebVitals from './reportWebVitals';

// Setup mock API if we're in development mode
if (process.env.NODE_ENV === 'development') {
  console.log('Using mock API for development');
  // This would normally initialize MSW, but we're using our own mock solution
  // Import has been removed to avoid the dependency
}

// Initialize app with strict mode and providers
const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <LanguageProvider>
      <App />
    </LanguageProvider>
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals(); 