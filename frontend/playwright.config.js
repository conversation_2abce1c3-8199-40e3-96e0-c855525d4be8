// @ts-check

/** @type {import('@playwright/test').PlaywrightTestConfig} */
const config = {
  testDir: './',
  timeout: 90000, // 90 seconds - increased for complex tests
  expect: {
    timeout: 15000, // 15 seconds - increased for complex assertions
  },
  fullyParallel: false,
  forbidOnly: !!process.env.CI,
  retries: 1,
  workers: 1,
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['list']
  ],
  projects: [
    {
      name: 'chromium',
      use: {
        headless: true,
        viewport: { width: 1280, height: 720 },
        ignoreHTTPSErrors: true,
        video: 'on', // Always record video
        trace: 'on',
        screenshot: {
          mode: 'on', // Always take screenshots
          fullPage: true
        },
        baseURL: 'http://localhost:3000', // The React app inside the container
        extraHTTPHeaders: {
          'X-Test-Mode': 'true'
        }
      }
    }
  ],
  outputDir: 'test_screenshots',
  preserveOutput: 'always',
};

module.exports = config; 