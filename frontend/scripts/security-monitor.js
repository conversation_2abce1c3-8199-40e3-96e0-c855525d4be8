#!/usr/bin/env node

/**
 * Security monitoring script for dependency vulnerabilities
 * This script runs npm audit and parses the results to generate a security report
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Config
const REPORT_DIR = path.join(__dirname, '../security-reports');
const SEVERITY_WEIGHTS = {
  low: 1,
  moderate: 2,
  high: 5,
  critical: 10
};

// Create report directory if it doesn't exist
if (!fs.existsSync(REPORT_DIR)) {
  fs.mkdirSync(REPORT_DIR, { recursive: true });
}

// Run npm audit and capture the output
function runAudit() {
  try {
    const output = execSync('npm audit --json').toString();
    return JSON.parse(output);
  } catch (error) {
    // npm audit exits with non-zero code if vulnerabilities are found
    // We need to parse the output from stderr in this case
    const output = error.stdout.toString();
    try {
      return JSON.parse(output);
    } catch (parseError) {
      console.error('Failed to parse npm audit output:', parseError);
      return null;
    }
  }
}

// Generate a security report from the audit results
function generateReport(auditResults) {
  if (!auditResults) return null;

  const timestamp = new Date().toISOString();
  const reportFile = path.join(REPORT_DIR, `security-report-${timestamp.replace(/:/g, '-')}.json`);
  
  const vulnerabilities = auditResults.vulnerabilities || {};
  const metadata = auditResults.metadata || {};
  
  // Count vulnerabilities by severity
  const counts = Object.values(vulnerabilities).reduce((acc, vuln) => {
    const severity = vuln.severity || 'unknown';
    acc[severity] = (acc[severity] || 0) + 1;
    return acc;
  }, {});
  
  // Calculate security score (lower is better)
  let securityScore = 0;
  Object.entries(counts).forEach(([severity, count]) => {
    if (SEVERITY_WEIGHTS[severity]) {
      securityScore += count * SEVERITY_WEIGHTS[severity];
    }
  });
  
  // Generate recommendations
  const recommendations = [];
  Object.entries(vulnerabilities).forEach(([name, vuln]) => {
    if (vuln.fixAvailable) {
      let rec = `Update ${name} `;
      if (vuln.fixAvailable.name && vuln.fixAvailable.version) {
        rec += `by updating ${vuln.fixAvailable.name} to version ${vuln.fixAvailable.version}`;
      } else {
        rec += 'to the latest version';
      }
      recommendations.push(rec);
    }
  });
  
  // Generate report
  const report = {
    timestamp,
    packageName: process.env.npm_package_name,
    packageVersion: process.env.npm_package_version,
    vulnerabilityCount: Object.keys(vulnerabilities).length,
    severityCounts: counts,
    securityScore,
    recommendations: [...new Set(recommendations)], // Remove duplicates
    actionRequired: securityScore > 0,
    detailedResults: vulnerabilities
  };
  
  // Save report to file
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  console.log(`Security report saved to ${reportFile}`);
  
  return report;
}

// Print summary to console
function printSummary(report) {
  if (!report) {
    console.log('No security report generated.');
    return;
  }
  
  console.log('\n========== SECURITY AUDIT SUMMARY ==========');
  console.log(`Timestamp: ${report.timestamp}`);
  console.log(`Package: ${report.packageName}@${report.packageVersion}`);
  console.log(`Total vulnerabilities: ${report.vulnerabilityCount}`);
  
  // Print severity counts
  console.log('\nVulnerabilities by severity:');
  Object.entries(report.severityCounts).forEach(([severity, count]) => {
    console.log(`  ${severity}: ${count}`);
  });
  
  // Print security score
  console.log(`\nSecurity score: ${report.securityScore} (lower is better)`);
  
  // Print recommendations
  if (report.recommendations.length > 0) {
    console.log('\nRecommendations:');
    report.recommendations.forEach(rec => {
      console.log(`  - ${rec}`);
    });
  }
  
  console.log('\nAction required:', report.actionRequired ? 'YES' : 'NO');
  console.log('==========================================\n');
}

// Main function
function main() {
  console.log('Running security audit...');
  const auditResults = runAudit();
  const report = generateReport(auditResults);
  printSummary(report);
  
  // Exit with non-zero code if action is required
  if (report && report.actionRequired) {
    process.exit(1);
  }
}

main(); 