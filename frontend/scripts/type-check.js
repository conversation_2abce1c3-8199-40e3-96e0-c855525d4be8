#!/usr/bin/env node

/**
 * This script runs the TypeScript compiler in type-checking mode
 * to detect and report TypeScript errors without emitting any output.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const sourceDir = path.resolve(__dirname, '../src');
const reportFile = path.resolve(__dirname, '../ts-errors.log');
const tsConfigPath = path.resolve(__dirname, '../tsconfig.json');

console.log('🔍 Running TypeScript type checker...');
console.log(`Source directory: ${sourceDir}`);
console.log(`Report file: ${reportFile}`);

// Make sure the TypeScript config exists
if (!fs.existsSync(tsConfigPath)) {
  console.error('❌ Could not find tsconfig.json');
  process.exit(1);
}

// Run the TypeScript compiler in noEmit mode to just check types
const tsc = spawn('npx', ['tsc', '--noEmit', '--project', tsConfigPath], {
  cwd: path.resolve(__dirname, '..'),
  shell: true
});

let errors = '';
let output = '';

// Collect stdout
tsc.stdout.on('data', (data) => {
  const text = data.toString();
  output += text;
  process.stdout.write(text);
});

// Collect stderr (where TypeScript errors are reported)
tsc.stderr.on('data', (data) => {
  const text = data.toString();
  errors += text;
  process.stderr.write(text);
});

// When the process exits
tsc.on('close', (code) => {
  if (code === 0) {
    console.log('✅ TypeScript check completed successfully. No errors found!');
  } else {
    console.error(`❌ TypeScript check failed with code ${code}`);
    
    // Write the errors to the report file
    fs.writeFileSync(reportFile, errors || output);
    console.error(`Errors have been written to ${reportFile}`);
    
    // Log specific patterns of errors that might help with debugging
    const errorLines = (errors || output).split('\n');
    
    // Count errors by file
    const errorsByFile = {};
    errorLines.forEach(line => {
      const match = line.match(/([^\s(]+\.tsx?)[:(](\d+)/);
      if (match) {
        const [, file, line] = match;
        errorsByFile[file] = (errorsByFile[file] || 0) + 1;
      }
    });
    
    console.log('\n📊 Errors by file:');
    Object.entries(errorsByFile)
      .sort((a, b) => b[1] - a[1])
      .forEach(([file, count]) => {
        console.log(`  ${file}: ${count} errors`);
      });
    
    // Look for common error patterns
    const propertyDoesNotExistErrors = errorLines.filter(line => {
      return line.includes('Property') && line.includes('does not exist on type');
    }).length;
    
    const implicitAnyErrors = errorLines.filter(line => {
      return line.includes('implicitly has an \'any\' type');
    }).length;
    
    const noOverloadMatchesErrors = errorLines.filter(line => {
      return line.includes('No overload matches this call');
    }).length;
    
    console.log('\n📌 Common error patterns:');
    console.log(`  Property does not exist: ${propertyDoesNotExistErrors} errors`);
    console.log(`  Implicit 'any' type: ${implicitAnyErrors} errors`);
    console.log(`  No overload matches: ${noOverloadMatchesErrors} errors`);
    
    process.exit(1);
  }
}); 