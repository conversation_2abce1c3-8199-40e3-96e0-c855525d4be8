#!/usr/bin/env node

/**
 * Update API Endpoints in React Files
 * 
 * This script scans the React frontend codebase for hardcoded API endpoints
 * and replaces them with imports from the shared API endpoints configuration.
 * 
 * Usage:
 *   node scripts/update_api_endpoints.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Directories to scan
const SCAN_DIRS = [
  'src',
  'src/pages',
  'src/components',
  'src/api',
  'src/hooks',
];

// File extensions to scan
const FILE_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

// Paths to the shared endpoints file
const SHARED_TS_PATH = '../shared/api_endpoints.ts';

// Helper function to find files recursively
function findFiles(dir, extensions, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      findFiles(filePath, extensions, fileList);
    } else if (extensions.includes(path.extname(file))) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Helper function to copy the shared endpoints file to the frontend
function copySharedEndpoints() {
  const sourcePath = path.resolve(__dirname, SHARED_TS_PATH);
  const destPath = path.resolve(__dirname, '../src/shared/api_endpoints.ts');
  
  // Create the directory if it doesn't exist
  const destDir = path.dirname(destPath);
  if (!fs.existsSync(destDir)) {
    fs.mkdirSync(destDir, { recursive: true });
  }
  
  // Copy the file
  fs.copyFileSync(sourcePath, destPath);
  console.log(`✅ Copied ${SHARED_TS_PATH} to src/shared/api_endpoints.ts`);
}

// Find files that import API endpoints from constants
function findApiConstantsImports() {
  let fileSet = new Set();
  
  for (const dir of SCAN_DIRS) {
    const baseDir = path.resolve(__dirname, '..', dir);
    if (!fs.existsSync(baseDir)) {
      console.log(`Directory ${baseDir} does not exist, skipping...`);
      continue;
    }
    
    const files = findFiles(baseDir, FILE_EXTENSIONS);
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      
      // Check for API endpoint imports or constants
      if (
        content.includes('API_ENDPOINTS') ||
        content.includes('/api/v1/') ||
        content.includes('import') && content.includes('constants')
      ) {
        fileSet.add(file);
      }
    }
  }
  
  return Array.from(fileSet);
}

// Update imports in a file
function updateFile(filePath) {
  console.log(`Processing ${filePath}...`);
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    
    // Replace import statements
    content = content.replace(
      /import\s*{\s*API_ENDPOINTS\s*}\s*from\s*['"].*?constants['"];?/,
      `import API_ENDPOINTS from '../../shared/api_endpoints';`
    );
    
    // Replace hardcoded API paths
    content = content.replace(
      /['"`]\/api\/v1\/([^'"`]+)['"`]/g,
      (match, path) => {
        // Check if this matches any of our endpoints
        for (const [category, endpoints] of Object.entries(API_ENDPOINTS)) {
          for (const [key, value] of Object.entries(endpoints)) {
            if (value.endsWith(`/${path}`)) {
              return `API_ENDPOINTS.${category}.${key}`;
            }
          }
        }
        return match; // Keep as is if not found
      }
    );
    
    // If file was changed, write it back
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content);
      console.log(`  ✅ Updated file`);
      return true;
    } else {
      console.log(`  No changes needed`);
      return false;
    }
  } catch (error) {
    console.error(`  ❌ Error updating file: ${error.message}`);
    return false;
  }
}

// Main function
function main() {
  console.log('Updating API endpoints in React files...');
  
  // First, copy the shared endpoints file
  copySharedEndpoints();
  
  // Find files with API constants imports
  const filesToUpdate = findApiConstantsImports();
  console.log(`\nFound ${filesToUpdate.length} files to check:`);
  
  // Update each file
  let updatedCount = 0;
  for (const file of filesToUpdate) {
    if (updateFile(file)) {
      updatedCount++;
    }
  }
  
  console.log(`\n✅ Done! Updated ${updatedCount} of ${filesToUpdate.length} files.`);
  
  if (updatedCount > 0) {
    console.log('\nPlease run tests to ensure all changes work correctly:');
    console.log('  npm run test');
  }
}

// Run the script
main(); 