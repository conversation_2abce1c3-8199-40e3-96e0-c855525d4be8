/**
 * Custom build script with enhanced error handling and logging
 * This script wraps the standard React Scripts build process with our error handling utilities
 */
const cp = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const CONFIG = {
  logDirectory: path.resolve(__dirname, '../logs'),
  errorLogFile: 'build-errors.log',
  notifyOnError: process.env.NODE_ENV === 'production',
  buildCommand: 'react-scripts build',
};

// Ensure log directory exists
try {
  if (!fs.existsSync(CONFIG.logDirectory)) {
    fs.mkdirSync(CONFIG.logDirectory, { recursive: true });
    console.log(`Created log directory at ${CONFIG.logDirectory}`);
  }
} catch (error) {
  console.error(`Failed to create log directory: ${error.message}`);
}

// Clear previous error log
const errorLogPath = path.join(CONFIG.logDirectory, CONFIG.errorLogFile);
try {
  if (fs.existsSync(errorLogPath)) {
    fs.writeFileSync(errorLogPath, '');
    console.log('Cleared previous error log');
  }
} catch (error) {
  console.error(`Failed to clear error log: ${error.message}`);
}

// Log error information
function logError(error, details = {}) {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    error: error.message || String(error),
    stack: error.stack,
    details,
  };

  try {
    fs.appendFileSync(
      errorLogPath,
      JSON.stringify(logEntry, null, 2) + ',\n',
      { encoding: 'utf8' }
    );
  } catch (logError) {
    console.error('Failed to write to error log:', logError);
  }

  // In production, we would implement code to notify developers
  if (CONFIG.notifyOnError) {
    console.log('Would notify developers in production mode');
    // Example notification code would go here
  }
}

console.log('Starting build with enhanced error logging...');

// Run the build process
try {
  // We use execSync for simplicity, but for more control you could use spawn
  const buildOutput = cp.execSync(CONFIG.buildCommand, {
    stdio: 'inherit',
    encoding: 'utf8',
  });

  console.log('Build completed successfully');
} catch (error) {
  console.error('Build failed with errors');
  
  logError(error, {
    type: 'build_failure',
    command: CONFIG.buildCommand,
    environment: process.env.NODE_ENV || 'development',
  });

  // Exit with error code
  process.exit(1);
}

// Apply webpack config wrapper if we're using a custom webpack config
try {
  console.log('Checking for webpack.config.js to apply error handling...');
  const webpackConfigPath = path.resolve(__dirname, '../webpack.config.js');
  
  if (fs.existsSync(webpackConfigPath)) {
    console.log('Found webpack config, applying error handling wrapper...');
    const enhanceErrorLogging = require('../webpack.error.config');
    
    // This is a demonstration - in reality, we'd modify the webpack config
    // at build time before running the build command
    console.log('Error handling would be applied to webpack config');
  } else {
    console.log('No custom webpack config found, using default React Scripts configuration');
  }
} catch (error) {
  console.error('Error while checking webpack config:', error.message);
  logError(error, { type: 'webpack_config_error' });
}

console.log(`Build process completed. Any errors will be logged to ${errorLogPath}`);

// Reminder about checking for module resolution errors
console.log('\nIMPORTANT: Check for module resolution errors');
console.log('If you see "Module not found" errors, ensure all required components exist and dependencies are installed.');
console.log('The enhanced error logging system has been set up to capture these errors for debugging.'); 