import { defineConfig, devices } from "@playwright/test";

/**
 * Playwright configuration for E2E testing
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: "./",
  testMatch: ['**/e2e/**/*.spec.ts', '**/tests/**/*.spec.ts'],
  timeout: 60 * 1000,  // Increased timeout for Docker environment
  fullyParallel: false, // No parallel tests in Docker to reduce resource contention
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 1, // Add retries for stability
  workers: 1, // Limit to single worker for stability in Docker
  reporter: [["html", { outputFolder: "playwright-report" }], ["list"]],

  use: {
    baseURL: process.env.PLAYWRIGHT_BASE_URL || "http://localhost:8080",
    trace: "on-first-retry",
    screenshot: "only-on-failure",
    // Lower expectations for resource-constrained environments
    navigationTimeout: 45000,
    actionTimeout: 30000,
    // Bypass permissions and geolocation prompts
    permissions: ['geolocation'],
    // Run headless by default
    headless: true,
  },

  projects: [
    {
      name: "chromium",
      use: { 
        ...devices["Desktop Chrome"],
        launchOptions: {
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--disable-gpu',
            '--disable-extensions'
          ]
        }
      },
    },
    {
      name: "firefox",
      use: { ...devices["Desktop Firefox"] },
    },
    {
      name: "webkit",
      use: { ...devices["Desktop Safari"] },
    },
    {
      name: "Mobile Chrome",
      use: { ...devices["Pixel 5"] },
    },
    {
      name: "Mobile Safari",
      use: { ...devices["iPhone 12"] },
    },
  ],

  webServer: process.env.CI
    ? undefined
    : {
        command: "npm run start",
        url: "http://localhost:3000",
        reuseExistingServer: !process.env.CI,
        timeout: 120 * 1000,
      },
});
