<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>UI Component Validation</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
        Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.5;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    .card {
      border: 1px solid #eee;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }
    
    h1, h2 {
      margin-top: 0;
    }
    
    button {
      padding: 10px 15px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .question {
      background-color: #f8f9fa;
      padding: 15px;
      margin-bottom: 10px;
      border-radius: 8px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
    }
    
    .answer {
      padding: 15px;
      margin-bottom: 20px;
      background-color: #f8f9fa;
      border-radius: 8px;
    }
    
    .calculator {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    
    .control {
      flex: 1;
      min-width: 200px;
    }
    
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: bold;
    }
    
    select, input {
      width: 100%;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    
    .result {
      margin-top: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .bar {
      height: 30px;
      background-color: #007bff;
      margin-bottom: 10px;
      color: white;
      display: flex;
      align-items: center;
      padding: 0 10px;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>UI Component Validation</h1>
    <p>Simple page to verify UI components are working</p>
  </div>
  
  <div class="card">
    <h2>Cost Calculator</h2>
    <div class="calculator">
      <div class="control">
        <label for="certification">Certification</label>
        <select id="certification">
          <option>CompTIA Security+</option>
          <option>CISSP</option>
          <option>CEH</option>
          <option>OSCP</option>
        </select>
      </div>
      
      <div class="control">
        <label for="currency">Currency</label>
        <select id="currency">
          <option>USD</option>
          <option>EUR</option>
          <option>GBP</option>
        </select>
      </div>
      
      <div class="control">
        <label>
          <input type="checkbox" checked> Include Training
        </label>
        <br>
        <label>
          <input type="checkbox" checked> Include Study Materials
        </label>
      </div>
    </div>
    
    <div class="result">
      <h3>Results</h3>
      <p>Initial Cost: $1,500</p>
      <p>3-Year Cost: $1,650</p>
      <p>5-Year Cost: $1,750</p>
      
      <h4>Visualization</h4>
      <div class="bar" style="width: 85%;">Initial Cost: $1,500</div>
      <div class="bar" style="width: 94%;">3-Year Cost: $1,650</div>
      <div class="bar" style="width: 100%;">5-Year Cost: $1,750</div>
    </div>
  </div>
  
  <div class="card">
    <h2>Frequently Asked Questions</h2>
    
    <div class="question">
      <span>📚 What is the difference between Security+ and CySA+?</span>
      <span>▼</span>
    </div>
    <div class="answer">
      Security+ is an entry-level certification while CySA+ is intermediate, focusing on security analytics and threat detection.
    </div>
    
    <div class="question">
      <span>🏆 How long does it typically take to prepare for the CISSP exam?</span>
      <span>▼</span>
    </div>
    <div class="answer">
      Most candidates study for 3-6 months, dedicating 10-15 hours per week.
    </div>
  </div>
  
  <div class="card">
    <h2>Career Path</h2>
    <div style="display: flex; justify-content: space-between; margin-top: 20px;">
      <div style="text-align: center;">
        <div style="width: 80px; height: 80px; background-color: #4caf50; border-radius: 50%; margin: 0 auto; display: flex; justify-content: center; align-items: center; color: white;">Entry</div>
        <p>Security Analyst</p>
      </div>
      <div style="font-size: 24px; padding-top: 30px;">→</div>
      <div style="text-align: center;">
        <div style="width: 80px; height: 80px; background-color: #2196f3; border-radius: 50%; margin: 0 auto; display: flex; justify-content: center; align-items: center; color: white;">Mid</div>
        <p>Penetration Tester</p>
      </div>
      <div style="font-size: 24px; padding-top: 30px;">→</div>
      <div style="text-align: center;">
        <div style="width: 80px; height: 80px; background-color: #ff9800; border-radius: 50%; margin: 0 auto; display: flex; justify-content: center; align-items: center; color: white;">Senior</div>
        <p>Security Architect</p>
      </div>
      <div style="font-size: 24px; padding-top: 30px;">→</div>
      <div style="text-align: center;">
        <div style="width: 80px; height: 80px; background-color: #f44336; border-radius: 50%; margin: 0 auto; display: flex; justify-content: center; align-items: center; color: white;">Exec</div>
        <p>CISO</p>
      </div>
    </div>
  </div>
  
  <script>
    // Simple JS to toggle FAQs
    document.querySelectorAll('.question').forEach(question => {
      question.addEventListener('click', () => {
        const answer = question.nextElementSibling;
        const arrow = question.querySelector('span:last-child');
        
        if (answer.style.display === 'none') {
          answer.style.display = 'block';
          arrow.textContent = '▲';
        } else {
          answer.style.display = 'none';
          arrow.textContent = '▼';
        }
      });
    });
    
    // Hide all answers initially except first one
    document.querySelectorAll('.answer').forEach((answer, index) => {
      if (index > 0) {
        answer.style.display = 'none';
      }
    });
  </script>
</body>
</html> 