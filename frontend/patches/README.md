# Security Patches

This directory contains patch files for fixing security vulnerabilities in dependencies. These patches are applied automatically during the `npm install` process using [patch-package](https://github.com/ds300/patch-package).

## Current Patches

### svgo+1.3.2.patch

**Vulnerability:** Inefficient Regular Expression Complexity in nth-check < 2.0.1
**CVE:** [GHSA-rp65-9cf3-cjxr](https://github.com/advisories/GHSA-rp65-9cf3-cjxr)
**Severity:** High
**Description:** Updates the version of nth-check used by the SVGO package to version 2.1.1, which fixes the vulnerability.

### msw+1.3.0.patch

**Vulnerability:** <PERSON><PERSON> accepts cookie name, path, and domain with out of bounds characters
**CVE:** [GHSA-pxg6-pf52-xh8x](https://github.com/advisories/GHSA-pxg6-pf52-xh8x) 
**Severity:** Low
**Description:** Updates the version of cookie used by the MSW package to version 0.7.0, which fixes the vulnerability.

### resolve-url-loader+4.0.0.patch

**Vulnerability:** PostCSS line return parsing error
**CVE:** [GHSA-7fh5-64p2-3v2j](https://github.com/advisories/GHSA-7fh5-64p2-3v2j)
**Severity:** Moderate
**Description:** Updates the version of PostCSS used by the resolve-url-loader package to version 8.4.31, which fixes the vulnerability.

## How to Update Patches

If you need to update a patch or create a new one:

1. Make the necessary changes to files in `node_modules/`
2. Run `npx patch-package package-name` to create/update the patch
3. Document the patch in this README
4. Commit the patch file to the repository

## How to Apply Patches

Patches are automatically applied after `npm install` through the `postinstall` script. If you need to manually apply patches:

```
npx patch-package
```

## Troubleshooting

If you encounter issues with patches:

1. Delete the `node_modules` directory
2. Clear npm cache: `npm cache clean --force`
3. Reinstall dependencies: `npm install`
4. Verify patches are applied: `npx patch-package --list` 