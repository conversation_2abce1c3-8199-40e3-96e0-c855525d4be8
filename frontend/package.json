{"name": "certrats-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/d3": "^7.4.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.11", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "axios": "^1.3.2", "chart.js": "^4.3.0", "d3": "^7.8.5", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "devDependencies": {"@playwright/test": "^1.51.1", "msw": "^1.3.0", "npm-force-resolutions": "^0.0.10", "patch-package": "^7.0.0"}, "resolutions": {"nth-check": "2.1.1", "cookie": "0.7.0", "postcss": "8.4.31", "**/svgo/css-select/nth-check": "2.1.1"}, "scripts": {"preinstall": "npx npm-force-resolutions", "fix-vulnerabilities": "npm install nth-check@2.1.1 --save-dev && npm install cookie@0.7.0 --save-dev && npm install postcss@8.4.31 --save-dev", "postinstall": "patch-package", "start": "react-scripts start", "build": "react-scripts build", "build:safe": "node scripts/build-with-error-logging.js", "build:debug": "REACT_APP_DEBUG=true node scripts/build-with-error-logging.js", "test": "react-scripts test", "test:watch": "react-scripts test --watch", "test:coverage": "react-scripts test --coverage --watchAll=false", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:all": "npm run test && npm run test:e2e", "eject": "react-scripts eject", "type-check": "node scripts/type-check.js", "security-audit": "node scripts/security-monitor.js", "security-monitor": "npm run security-audit -- --ci"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/reportWebVitals.ts", "!src/index.tsx"], "coverageThreshold": {"global": {"statements": 80, "branches": 80, "functions": 80, "lines": 80}}}}