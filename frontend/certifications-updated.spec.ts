import { expect, test } from '@playwright/test';

test.describe('Certification Pages', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the certifications page before each test
    await page.goto('/certifications');
  });

  test('should display the certifications list', async ({ page }) => {
    // Check if the main container is visible
    await expect(page.locator('.certification-explorer-container')).toBeVisible();
    await expect(page.locator('h1')).toHaveText('Certification Explorer');

    // Check if the filters panel is visible
    await expect(page.locator('.filters-panel')).toBeVisible();
    await expect(page.locator('#search')).toBeVisible();
    await expect(page.locator('#provider')).toBeVisible();
    await expect(page.locator('#type')).toBeVisible();
    await expect(page.locator('#level')).toBeVisible();

    // Load sample certifications if needed
    const loadSampleButton = page.locator('.load-sample-button');
    if (await loadSampleButton.isVisible()) {
      await loadSampleButton.click();
      // Wait for certifications to load
      await page.waitForTimeout(1000);
    }

    // Check if the certifications grid is visible
    await expect(page.locator('[data-testid="certifications-grid"]')).toBeVisible();
  });

  test('should filter certifications by type', async ({ page }) => {
    // Load sample data if needed
    const loadSampleButton = page.locator('.load-sample-button');
    if (await loadSampleButton.isVisible()) {
      await loadSampleButton.click();
      // Wait for certifications to load
      await page.waitForTimeout(1000);
    }

    // Select a type from the filter
    await page.locator('#type').selectOption('Security');

    // Wait for the grid to update
    await page.waitForSelector('[data-testid="certifications-grid"]');
  });

  test('should filter certifications by level', async ({ page }) => {
    // Load sample data if needed
    const loadSampleButton = page.locator('.load-sample-button');
    if (await loadSampleButton.isVisible()) {
      await loadSampleButton.click();
      // Wait for certifications to load
      await page.waitForTimeout(1000);
    }

    // Select a level from the filter
    await page.locator('#level').selectOption('Intermediate');

    // Wait for the grid to update
    await page.waitForSelector('[data-testid="certifications-grid"]');
  });

  test('should search certifications', async ({ page }) => {
    // Load sample data if needed
    const loadSampleButton = page.locator('.load-sample-button');
    if (await loadSampleButton.isVisible()) {
      await loadSampleButton.click();
      // Wait for certifications to load
      await page.waitForTimeout(1000);
    }

    // Enter a search term
    await page.locator('#search').fill('Security');

    // Wait for the grid to update
    await page.waitForSelector('[data-testid="certifications-grid"]');
  });

  test('should handle pagination', async ({ page }) => {
    // Load sample data if needed
    const loadSampleButton = page.locator('.load-sample-button');
    if (await loadSampleButton.isVisible()) {
      await loadSampleButton.click();
      // Wait for certifications to load
      await page.waitForTimeout(1000);
    }

    // Check if pagination controls are visible
    await expect(page.locator('[data-testid="pagination-controls"]')).toBeVisible();
    await expect(page.locator('[data-testid="current-page"]')).toBeVisible();

    // Click next page button
    await page.locator('.page-nav.next').click();

    // Wait for the grid to update
    await page.waitForSelector('[data-testid="certifications-grid"]');
  });

  test('should be responsive', async ({ page }) => {
    // Load sample data if needed
    const loadSampleButton = page.locator('.load-sample-button');
    if (await loadSampleButton.isVisible()) {
      await loadSampleButton.click();
      // Wait for certifications to load
      await page.waitForTimeout(1000);
    }

    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('.filters-panel')).toBeVisible();
    await expect(page.locator('[data-testid="certifications-grid"]')).toBeVisible();

    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('.filters-panel')).toBeVisible();
    await expect(page.locator('[data-testid="certifications-grid"]')).toBeVisible();

    // Test desktop view
    await page.setViewportSize({ width: 1280, height: 800 });
    await expect(page.locator('.filters-panel')).toBeVisible();
    await expect(page.locator('[data-testid="certifications-grid"]')).toBeVisible();
  });
});
