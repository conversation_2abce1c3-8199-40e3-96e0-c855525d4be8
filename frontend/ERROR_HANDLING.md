# Error Handling System

This document provides an overview of the error handling system implemented in this project to prevent, capture, and log errors for better debugging and user experience.

## Key Components

### 1. Error Utilities

Located in `src/utils/error/`, these utilities provide comprehensive error handling:

- **errorLogger.ts** - Core logging functionality with different severity levels
- **ErrorBoundary.tsx** - React error boundary for catching runtime errors
- **LazyLoadErrorBoundary.tsx** - Specialized boundary for lazy-loaded components
- **ErrorContext.tsx** - Context provider for global error state
- **buildErrorLogger.js** - Webpack plugin for build-time errors

### 2. Server Integration

The API has endpoints to receive and store error logs:

- `/api/v1/error-logs` - For runtime errors from the frontend
- `/api/v1/error-logs/build-notifications` - For build-time errors

## How to Prevent Module Import Errors

The errors you're seeing (e.g., `Can't resolve './pages/AdminInterface/AdminInterface'`) are module resolution errors. To fix these:

1. Ensure all required component files exist in the correct directories
2. Install all necessary dependencies with `npm install`
3. Use the `LazyLoadErrorBoundary` when importing dynamic components

## Build with Error Logging

Use our enhanced build scripts:

```bash
# Build with error logging
npm run build:safe

# Build with additional debug information
npm run build:debug
```

These scripts will:
1. Create a log directory if it doesn't exist
2. Log any build errors to `logs/build-errors.log`
3. Provide detailed error information for debugging

## Properly Handling Lazy-Loaded Components

When creating lazy-loaded components, always use this pattern:

```typescript
const MyComponent = React.lazy(() => import('./path/to/Component')
  .catch(error => {
    logModuleError('./path/to/Component', error);
    throw error;
  })
);

// Then use it with the LazyLoadErrorBoundary
<LazyLoadErrorBoundary modulePath="./path/to/Component">
  <Suspense fallback={<Loading />}>
    <MyComponent />
  </Suspense>
</LazyLoadErrorBoundary>
```

## Error Categorization

Errors are categorized for better filtering:

- **RUNTIME** - General runtime errors
- **NETWORK** - API and connection errors
- **UI** - User interface errors
- **MODULE** - Module loading/resolution errors
- **COMPILATION** - Build-time compilation errors
- **BUILD** - General build process errors

## Adding Material-UI or Other Dependencies

If you see errors related to missing UI components or libraries:

1. Install the required dependencies:
   ```bash
   npm install @mui/material @mui/icons-material @emotion/react @emotion/styled
   ```

2. Add the dependency to package.json if it's not already there

## Environments and Error Handling

- **Development**: Errors are shown in console with detailed information
- **Production**: Errors are logged to the API and not shown to users
- **Both**: Critical errors can trigger notifications to developers

## Best Practices

1. **Use Error Boundaries**: Place error boundaries strategically around important UI sections
2. **Log Contextual Information**: Include relevant context when logging errors
3. **Handle Network Errors**: Use the API error handler for network requests
4. **Present User-Friendly Errors**: Don't show stack traces to end-users
5. **Add Retry Mechanisms**: Allow users to retry operations after transient errors

## Viewing Error Logs

Error logs are stored in:

- Browser console (development)
- `logs/build-errors.log` (build-time errors)
- API logs (runtime errors in production)

## Troubleshooting Common Errors

### Module Not Found Errors

```
Module not found: Error: Can't resolve './pages/ComponentName/ComponentName'
```

**Solution**: Create the missing component file or fix the import path.

### Missing Dependencies

```
Module not found: Error: Can't resolve 'package-name'
```

**Solution**: Install the required dependency with `npm install package-name`.

### React Hook Errors

```
React Hook "useX" is called in function "Y" that is neither a React function component nor a custom React Hook function
```

**Solution**: Move the hook to a component function or create a custom hook.

## Getting Help

If you continue to experience errors after implementing these practices:

1. Check the error logs in the locations mentioned above
2. Look for similar issues in the project issue tracker
3. Reach out to the development team with detailed error information 