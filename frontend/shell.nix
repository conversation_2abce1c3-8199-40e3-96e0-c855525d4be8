{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Node.js and npm
    nodejs-18_x
    nodePackages.npm

    # Testing tools
    nodePackages.typescript
    nodePackages.typescript-language-server

    # Development tools
    git
    curl
    wget
  ];

  shellHook = ''
    # Set up Node.js environment
    export PATH="$PWD/node_modules/.bin:$PATH"

    # Install dependencies if package.json exists
    if [ -f package.json ]; then
      echo "Installing npm dependencies..."
      npm install
    fi

    # Install Playwright browsers if needed
    if ! command -v playwright &> /dev/null; then
      echo "Installing Playwright..."
      npm install -g playwright
      npx playwright install
    fi
  '';
} 