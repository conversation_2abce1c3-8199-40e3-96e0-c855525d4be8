{"auditReportVersion": 2, "vulnerabilities": {"@svgr/plugin-svgo": {"name": "@svgr/plugin-svgo", "severity": "high", "isDirect": false, "via": ["svgo"], "effects": ["@svgr/webpack"], "range": "<=5.5.0", "nodes": ["node_modules/@svgr/plugin-svgo"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "@svgr/webpack": {"name": "@svgr/webpack", "severity": "high", "isDirect": false, "via": ["@svgr/plugin-svgo"], "effects": ["react-scripts"], "range": "4.0.0 - 5.5.0", "nodes": ["node_modules/@svgr/webpack"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "cookie": {"name": "cookie", "severity": "low", "isDirect": false, "via": [{"source": 1099846, "name": "cookie", "dependency": "cookie", "title": "cookie accepts cookie name, path, and domain with out of bounds characters", "url": "https://github.com/advisories/GHSA-pxg6-pf52-xh8x", "severity": "low", "cwe": ["CWE-74"], "cvss": {"score": 0, "vectorString": null}, "range": "<0.7.0"}], "effects": ["msw"], "range": "<0.7.0", "nodes": ["node_modules/msw/node_modules/cookie"], "fixAvailable": {"name": "msw", "version": "2.7.3", "isSemVerMajor": true}}, "css-select": {"name": "css-select", "severity": "high", "isDirect": false, "via": ["nth-check"], "effects": ["svgo"], "range": "<=3.1.0", "nodes": ["node_modules/svgo/node_modules/css-select"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "msw": {"name": "msw", "severity": "low", "isDirect": true, "via": ["cookie"], "effects": [], "range": "<=0.0.1 || 0.13.0 - 1.3.5", "nodes": ["node_modules/msw"], "fixAvailable": {"name": "msw", "version": "2.7.3", "isSemVerMajor": true}}, "nth-check": {"name": "nth-check", "severity": "high", "isDirect": false, "via": [{"source": 1095141, "name": "nth-check", "dependency": "nth-check", "title": "Inefficient Regular Expression Complexity in nth-check", "url": "https://github.com/advisories/GHSA-rp65-9cf3-cjxr", "severity": "high", "cwe": ["CWE-1333"], "cvss": {"score": 7.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H"}, "range": "<2.0.1"}], "effects": ["css-select"], "range": "<2.0.1", "nodes": ["node_modules/svgo/node_modules/nth-check"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "postcss": {"name": "postcss", "severity": "moderate", "isDirect": false, "via": [{"source": 1094544, "name": "postcss", "dependency": "postcss", "title": "PostCSS line return parsing error", "url": "https://github.com/advisories/GHSA-7fh5-64p2-3v2j", "severity": "moderate", "cwe": ["CWE-74", "CWE-144"], "cvss": {"score": 5.3, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N"}, "range": "<8.4.31"}], "effects": ["resolve-url-loader"], "range": "<8.4.31", "nodes": ["node_modules/resolve-url-loader/node_modules/postcss"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "react-scripts": {"name": "react-scripts", "severity": "high", "isDirect": true, "via": ["@svgr/webpack", "resolve-url-loader"], "effects": [], "range": ">=2.1.4", "nodes": ["node_modules/react-scripts"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "resolve-url-loader": {"name": "resolve-url-loader", "severity": "moderate", "isDirect": false, "via": ["postcss"], "effects": ["react-scripts"], "range": "0.0.1-experiment-postcss || 3.0.0-alpha.1 - 4.0.0", "nodes": ["node_modules/resolve-url-loader"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "svgo": {"name": "svgo", "severity": "high", "isDirect": false, "via": ["css-select"], "effects": ["@svgr/plugin-svgo"], "range": "1.0.0 - 1.3.2", "nodes": ["node_modules/svgo"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}}, "metadata": {"vulnerabilities": {"info": 0, "low": 2, "moderate": 2, "high": 6, "critical": 0, "total": 10}, "dependencies": {"prod": 1465, "dev": 62, "optional": 5, "peer": 1, "peerOptional": 0, "total": 1531}}}