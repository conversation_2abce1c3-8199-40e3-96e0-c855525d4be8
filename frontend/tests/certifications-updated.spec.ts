import { expect, test } from '@playwright/test';

test.describe('Certification Pages', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the certifications page before each test
    await page.goto('/certifications');
    // Wait for initial data load
    await page.waitForSelector('[data-testid="certification-explorer"]');
    // Wait for loading to complete
    await page.waitForSelector('[data-testid="loading-state"]', { state: 'hidden' });
  });

  test('should display the certifications list', async ({ page }) => {
    // Check if the main container is visible
    await expect(page.locator('[data-testid="certification-explorer"]')).toBeVisible();
    await expect(page.locator('[data-testid="explorer-title"]')).toHaveText('Certification Explorer');

    // Check if the filters panel is visible
    await expect(page.locator('[data-testid="filters-panel"]')).toBeVisible();
    await expect(page.locator('[data-testid="search-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="provider-filter"]')).toBeVisible();
    await expect(page.locator('[data-testid="type-filter"]')).toBeVisible();
    await expect(page.locator('[data-testid="level-filter"]')).toBeVisible();

    // Check if the certifications grid is visible
    await expect(page.locator('[data-testid="certifications-grid"]')).toBeVisible();
  });

  test('should filter certifications by type', async ({ page }) => {
    // Wait for filters to be ready
    await page.waitForSelector('[data-testid="type-filter"]', { state: 'visible' });
    
    // Select a type from the filter
    await page.locator('[data-testid="type-filter"]').selectOption('Security');

    // Wait for loading to complete
    await page.waitForSelector('[data-testid="loading-state"]', { state: 'hidden' });
    
    // Verify filtered results
    const gridItems = await page.locator('[data-testid^="certification-card-"]').all();
    for (const item of gridItems) {
      const id = await item.getAttribute('data-testid');
      const certId = id?.replace('certification-card-', '');
      const type = await page.locator(`[data-testid="certification-type-${certId}"]`).textContent();
      expect(type).toBe('Security');
    }
  });

  test('should filter certifications by level', async ({ page }) => {
    // Wait for filters to be ready
    await page.waitForSelector('[data-testid="level-filter"]', { state: 'visible' });
    
    // Select a level from the filter
    await page.locator('[data-testid="level-filter"]').selectOption('Intermediate');

    // Wait for loading to complete
    await page.waitForSelector('[data-testid="loading-state"]', { state: 'hidden' });
    
    // Verify filtered results
    const gridItems = await page.locator('[data-testid^="certification-card-"]').all();
    for (const item of gridItems) {
      const id = await item.getAttribute('data-testid');
      const certId = id?.replace('certification-card-', '');
      const level = await page.locator(`[data-testid="certification-level-${certId}"]`).textContent();
      expect(level).toBe('Intermediate');
    }
  });

  test('should search certifications', async ({ page }) => {
    const searchTerm = 'Security';
    
    // Wait for search input to be ready
    await page.waitForSelector('[data-testid="search-input"]', { state: 'visible' });
    
    // Enter a search term
    await page.locator('[data-testid="search-input"]').fill(searchTerm);

    // Wait for loading to complete
    await page.waitForSelector('[data-testid="loading-state"]', { state: 'hidden' });
    
    // Verify search results contain the search term
    const gridItems = await page.locator('[data-testid^="certification-card-"]').all();
    for (const item of gridItems) {
      const id = await item.getAttribute('data-testid');
      const certId = id?.replace('certification-card-', '');
      const title = await page.locator(`[data-testid="certification-title-${certId}"]`).textContent() || '';
      const description = await page.locator(`[data-testid="certification-description-${certId}"]`).textContent() || '';
      expect(title.toLowerCase() + description.toLowerCase()).toContain(searchTerm.toLowerCase());
    }
  });

  test('should handle pagination', async ({ page }) => {
    // Wait for loading to complete
    await page.waitForSelector('[data-testid="loading-state"]', { state: 'hidden' });
    
    // Check if pagination controls are visible
    await expect(page.locator('[data-testid="pagination-controls"]')).toBeVisible();
    await expect(page.locator('[data-testid="current-page"]')).toBeVisible();

    // Get current page items
    const initialItems = await page.locator('[data-testid^="certification-card-"]').all();
    const initialItemsCount = initialItems.length;

    // Click next page button
    await page.locator('.page-nav.next').click();

    // Wait for loading to complete
    await page.waitForSelector('[data-testid="loading-state"]', { state: 'hidden' });
    
    // Get new page items
    const newItems = await page.locator('[data-testid^="certification-card-"]').all();
    
    // Verify we have items on the new page
    expect(newItems.length).toBeGreaterThan(0);
    // Verify they are different from the initial items
    expect(newItems.length).toBe(initialItemsCount);
  });

  test('should be responsive', async ({ page }) => {
    // Wait for initial load
    await page.waitForSelector('[data-testid="loading-state"]', { state: 'hidden' });
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('[data-testid="filters-panel"]')).toBeVisible();
    await expect(page.locator('[data-testid="certifications-grid"]')).toBeVisible();

    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('[data-testid="filters-panel"]')).toBeVisible();
    await expect(page.locator('[data-testid="certifications-grid"]')).toBeVisible();

    // Test desktop view
    await page.setViewportSize({ width: 1280, height: 800 });
    await expect(page.locator('[data-testid="filters-panel"]')).toBeVisible();
    await expect(page.locator('[data-testid="certifications-grid"]')).toBeVisible();
  });
}); 