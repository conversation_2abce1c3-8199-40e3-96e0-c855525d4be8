import { expect, test } from '@playwright/test';

test.describe('Certification Pages', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the certifications page before each test
    await page.goto('/certifications');
  });

  test('should display the certifications list', async ({ page }) => {
    // Check if the main container is visible
    await expect(page.getByTestId('certification-explorer')).toBeVisible();
    await expect(page.getByTestId('explorer-title')).toHaveText('Certification Explorer');

    // Check if the filters panel is visible
    await expect(page.getByTestId('filters-panel')).toBeVisible();
    await expect(page.getByTestId('search-input')).toBeVisible();
    await expect(page.getByTestId('provider-filter')).toBeVisible();
    await expect(page.getByTestId('type-filter')).toBeVisible();
    await expect(page.getByTestId('level-filter')).toBeVisible();

    // Check if the certifications grid is visible and contains cards
    await expect(page.getByTestId('certifications-grid')).toBeVisible();
    const certificationCards = page.getByTestId(/^certification-card-/);
    await expect(certificationCards).toHaveCount(await certificationCards.count());
  });

  test('should filter certifications by domain', async ({ page }) => {
    // Select a domain from the filter
    await page.getByTestId('type-filter').selectOption('security');

    // Wait for the grid to update
    await page.waitForSelector('[data-testid="certifications-grid"]');

    // Verify that all visible cards have the selected domain
    const cards = page.getByTestId(/^certification-card-/);
    const count = await cards.count();
    for (let i = 0; i < count; i++) {
      const card = cards.nth(i);
      await expect(card.getByTestId(/^certification-type-/)).toContainText('security');
    }
  });

  test('should filter certifications by level', async ({ page }) => {
    // Select a level from the filter
    await page.getByTestId('level-filter').selectOption('intermediate');

    // Wait for the grid to update
    await page.waitForSelector('[data-testid="certifications-grid"]');

    // Verify that all visible cards have the selected level
    const cards = page.getByTestId(/^certification-card-/);
    const count = await cards.count();
    for (let i = 0; i < count; i++) {
      const card = cards.nth(i);
      await expect(card.getByTestId(/^certification-level-/)).toContainText('intermediate');
    }
  });

  test('should search certifications', async ({ page }) => {
    // Enter a search term
    await page.getByTestId('search-input').fill('Security+');

    // Wait for the grid to update
    await page.waitForSelector('[data-testid="certifications-grid"]');

    // Verify that the search results contain the search term
    const cards = page.getByTestId(/^certification-card-/);
    const count = await cards.count();
    for (let i = 0; i < count; i++) {
      const card = cards.nth(i);
      await expect(card.getByTestId(/^certification-title-/)).toContainText('Security+');
    }
  });

  test('should display certification details', async ({ page }) => {
    // Click on a certification card
    const firstCard = page.getByTestId(/^certification-card-/).first();
    await firstCard.click();

    // Verify that the details view is displayed
    await expect(page.getByTestId('certification-details')).toBeVisible();
    await expect(page.getByTestId('certification-name')).toBeVisible();
    await expect(page.getByTestId('certification-meta')).toBeVisible();
    await expect(page.getByTestId('certification-description')).toBeVisible();

    // Check if all meta information is displayed
    await expect(page.getByTestId('certification-provider')).toBeVisible();
    await expect(page.getByTestId('certification-type')).toBeVisible();
    await expect(page.getByTestId('certification-level')).toBeVisible();
    await expect(page.getByTestId('certification-price')).toBeVisible();
    await expect(page.getByTestId('certification-duration')).toBeVisible();
    await expect(page.getByTestId('certification-passing-score')).toBeVisible();
  });

  test('should handle prerequisites', async ({ page }) => {
    // Click on a certification card
    const firstCard = page.getByTestId(/^certification-card-/).first();
    await firstCard.click();

    // Check if prerequisites are displayed
    const prerequisitesSection = page.getByTestId('certification-prerequisites');
    if (await prerequisitesSection.isVisible()) {
      await expect(prerequisitesSection).toBeVisible();
      const prerequisitesList = prerequisitesSection.getByTestId(/^prerequisite-/);
      await expect(prerequisitesList).toHaveCount(await prerequisitesList.count());
    }
  });

  test('should handle pagination', async ({ page }) => {
    // Check if pagination controls are visible
    await expect(page.getByTestId('pagination-controls')).toBeVisible();
    await expect(page.getByTestId('current-page')).toBeVisible();

    // Click next page
    await page.getByTestId('pagination-controls').getByRole('button', { name: 'Next' }).click();

    // Wait for the grid to update
    await page.waitForSelector('[data-testid="certifications-grid"]');

    // Verify that the page number has changed
    await expect(page.getByTestId('current-page')).toContainText('Page 2');
  });

  test('should be responsive', async ({ page }) => {
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.getByTestId('filters-panel')).toBeVisible();
    await expect(page.getByTestId('certifications-grid')).toBeVisible();

    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.getByTestId('filters-panel')).toBeVisible();
    await expect(page.getByTestId('certifications-grid')).toBeVisible();

    // Test desktop view
    await page.setViewportSize({ width: 1280, height: 800 });
    await expect(page.getByTestId('filters-panel')).toBeVisible();
    await expect(page.getByTestId('certifications-grid')).toBeVisible();
  });

  test('should handle loading state', async ({ page }) => {
    // Force a slow network connection to see loading state
    await page.route('**/*', (route) => {
      route.continue({ timeout: 5000 });
    });

    // Navigate to the page
    await page.goto('/certifications');

    // Verify loading state is shown
    await expect(page.getByTestId('loading-state')).toBeVisible();
    await expect(page.getByTestId('loading-state')).toContainText('Loading certifications...');

    // Wait for content to load
    await page.waitForSelector('[data-testid="certifications-grid"]');

    // Verify loading state is hidden
    await expect(page.getByTestId('loading-state')).not.toBeVisible();
  });

  test('should handle error state', async ({ page }) => {
    // Mock an error response
    await page.route('**/api/certifications', (route) => {
      route.fulfill({
        status: 500,
        body: JSON.stringify({ error: 'Internal Server Error' })
      });
    });

    // Navigate to the page
    await page.goto('/certifications');

    // Verify error message is shown
    await expect(page.getByTestId('error-message')).toBeVisible();
    await expect(page.getByTestId('error-message')).toContainText('Failed to load certifications');
  });

  test('should sort certifications', async ({ page }) => {
    // Test sorting by name ascending
    await page.getByTestId('sort-select').selectOption('name-asc');
    await page.waitForSelector('[data-testid="certifications-grid"]');
    
    // Get all certification titles
    const titles = page.getByTestId(/^certification-title-/);
    const count = await titles.count();
    const titlesText = [];
    for (let i = 0; i < count; i++) {
      titlesText.push(await titles.nth(i).textContent());
    }
    
    // Verify ascending order
    const sortedTitles = [...titlesText].sort();
    expect(titlesText).toEqual(sortedTitles);

    // Test sorting by price descending
    await page.getByTestId('sort-select').selectOption('price-desc');
    await page.waitForSelector('[data-testid="certifications-grid"]');
    
    // Get all prices
    const prices = page.getByTestId(/^certification-price-/);
    const pricesText = [];
    for (let i = 0; i < count; i++) {
      const price = await prices.nth(i).textContent();
      pricesText.push(parseFloat(price.replace(/[^0-9.-]+/g, '')));
    }
    
    // Verify descending order
    const sortedPrices = [...pricesText].sort((a, b) => b - a);
    expect(pricesText).toEqual(sortedPrices);
  });

  test('should handle combined filters', async ({ page }) => {
    // Apply multiple filters
    await page.getByTestId('type-filter').selectOption('security');
    await page.getByTestId('level-filter').selectOption('intermediate');
    await page.getByTestId('provider-filter').selectOption('CompTIA');
    
    // Wait for the grid to update
    await page.waitForSelector('[data-testid="certifications-grid"]');

    // Verify that all visible cards match the combined filters
    const cards = page.getByTestId(/^certification-card-/);
    const count = await cards.count();
    for (let i = 0; i < count; i++) {
      const card = cards.nth(i);
      await expect(card.getByTestId(/^certification-type-/)).toContainText('security');
      await expect(card.getByTestId(/^certification-level-/)).toContainText('intermediate');
      await expect(card.getByTestId(/^certification-provider-/)).toContainText('CompTIA');
    }
  });

  test('should meet accessibility standards', async ({ page }) => {
    // Check for proper ARIA labels
    await expect(page.getByTestId('search-input')).toHaveAttribute('aria-label', 'Search certifications');
    await expect(page.getByTestId('type-filter')).toHaveAttribute('aria-label', 'Filter by type');
    await expect(page.getByTestId('level-filter')).toHaveAttribute('aria-label', 'Filter by level');
    
    // Check for proper heading hierarchy
    await expect(page.getByTestId('explorer-title')).toHaveAttribute('role', 'heading');
    await expect(page.getByTestId('explorer-title')).toHaveAttribute('aria-level', '1');
    
    // Check for proper focus management
    await page.keyboard.press('Tab');
    await expect(page.getByTestId('search-input')).toBeFocused();
  });

  test('should support keyboard navigation', async ({ page }) => {
    // Focus the search input
    await page.getByTestId('search-input').focus();
    
    // Navigate through filters using Tab
    await page.keyboard.press('Tab');
    await expect(page.getByTestId('type-filter')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.getByTestId('level-filter')).toBeFocused();
    
    // Test filter selection with keyboard
    await page.keyboard.press('Enter');
    await page.keyboard.press('ArrowDown');
    await page.keyboard.press('Enter');
    
    // Verify filter was applied
    await expect(page.getByTestId('level-filter')).toHaveValue('intermediate');
    
    // Navigate through certification cards
    const cards = page.getByTestId(/^certification-card-/);
    await cards.first().focus();
    
    // Test arrow key navigation
    await page.keyboard.press('ArrowRight');
    await expect(cards.nth(1)).toBeFocused();
  });

  test('should persist filter state between page refreshes', async ({ page }) => {
    // Apply filters
    await page.getByTestId('type-filter').selectOption('security');
    await page.getByTestId('level-filter').selectOption('intermediate');
    await page.getByTestId('search-input').fill('Security+');
    
    // Refresh the page
    await page.reload();
    
    // Verify filters are still applied
    await expect(page.getByTestId('type-filter')).toHaveValue('security');
    await expect(page.getByTestId('level-filter')).toHaveValue('intermediate');
    await expect(page.getByTestId('search-input')).toHaveValue('Security+');
    
    // Verify the grid reflects the filters
    await page.waitForSelector('[data-testid="certifications-grid"]');
    const cards = page.getByTestId(/^certification-card-/);
    const count = await cards.count();
    for (let i = 0; i < count; i++) {
      const card = cards.nth(i);
      await expect(card.getByTestId(/^certification-type-/)).toContainText('security');
      await expect(card.getByTestId(/^certification-level-/)).toContainText('intermediate');
      await expect(card.getByTestId(/^certification-title-/)).toContainText('Security+');
    }
  });
}); 