import { expect, test } from '@playwright/test';

test.describe('Certification Pages - Minimal Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the certifications page before each test
    await page.goto('/certifications');
  });

  test('should display the certifications title', async ({ page }) => {
    // Check if the title element is visible
    await expect(page.getByTestId('explorer-title')).toBeVisible();
  });

  test('should display the filters panel', async ({ page }) => {
    // Check if the filters panel is visible
    await expect(page.getByTestId('filters-panel')).toBeVisible();
  });
});
