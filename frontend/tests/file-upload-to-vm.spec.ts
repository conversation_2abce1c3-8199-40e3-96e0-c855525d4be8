import { test, expect, Page } from '@playwright/test';
import * as path from 'path';
import * as fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

// The test creates a test file, uploads it, creates a VM, and verifies the whole process works
test.describe('File Upload to VM E2E Flow', () => {
  // Define test file details
  const testFilePath = path.join(__dirname, 'test-upload.txt');
  const testFileContent = `Test file content for VM creation test
Created at: ${new Date().toISOString()}
ID: ${uuidv4()}`;
  
  // Set up the test file before all tests
  test.beforeAll(async () => {
    // Create test file
    fs.writeFileSync(testFilePath, testFileContent);
    console.log(`Created test file at ${testFilePath}`);
  });
  
  // Clean up the test file after all tests
  test.afterAll(async () => {
    // Remove test file
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
      console.log(`Removed test file at ${testFilePath}`);
    }
  });
  
  // Define helper functions for VM operations
  async function waitForVMStatus(page: Page, vmId: string, targetStatus: string, maxAttempts = 10) {
    for (let i = 0; i < maxAttempts; i++) {
      // Go to VM details page
      await page.goto(`/vagrant/vms/${vmId}`);
      
      // Check if status matches target
      const statusElement = page.locator('.vm-status');
      if (await statusElement.isVisible()) {
        const status = await statusElement.textContent();
        if (status?.toLowerCase().includes(targetStatus.toLowerCase())) {
          return true;
        }
      }
      
      // Wait before checking again
      await page.waitForTimeout(3000);
    }
    
    return false;
  }
  
  test('Should upload a file, create VM, and verify VM creation', async ({ page }) => {
    // Step 1: Navigate to the file upload page
    await page.goto('/upload');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of the upload page
    await page.screenshot({ path: 'test_screenshots/01-upload-page.png' });
    
    // Step 2: Upload a file
    // Fill description field
    await page.fill('textarea[placeholder*="description"]', 'Test file for VM creation');
    
    // Upload the file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);
    
    // Take screenshot of the form with file selected
    await page.screenshot({ path: 'test_screenshots/02-file-selected.png' });
    
    // Submit the upload
    await page.click('button:has-text("Upload")');
    
    // Wait for upload to complete and show success message
    await page.waitForSelector('.ant-message-success', { timeout: 30000 });
    await page.screenshot({ path: 'test_screenshots/03-upload-success.png' });
    
    // Step 3: Navigate to VM creation page
    await page.goto('/vagrant/vms/new');
    await page.waitForLoadState('networkidle');
    await page.screenshot({ path: 'test_screenshots/04-vm-creation-page.png' });
    
    // Step 4: Fill out VM creation form
    // Generate a unique VM name
    const vmName = `test-vm-${new Date().getTime()}`;
    
    // Fill VM name field
    await page.fill('input[placeholder*="VM name"], input#vm-name', vmName);
    
    // Fill VM description
    await page.fill('textarea[placeholder*="description"], textarea#description', 'VM created from uploaded file via E2E test');
    
    // Select a template
    await page.click('.ant-select-selector');
    await page.waitForSelector('.ant-select-dropdown');
    await page.click('.ant-select-item:has-text("Ubuntu")');
    
    // Take screenshot of the filled form
    await page.screenshot({ path: 'test_screenshots/05-vm-form-filled.png' });
    
    // Submit the form
    await page.click('button:has-text("Create")');
    
    // Wait for creation process to start and redirect to VM list
    await page.waitForURL('/vagrant/vms', { timeout: 30000 });
    await page.screenshot({ path: 'test_screenshots/06-vm-created.png' });
    
    // Step 5: Verify VM Creation
    // Find the newly created VM in the list
    await page.waitForSelector(`.ant-table-row:has-text("${vmName}")`, { timeout: 30000 });
    
    // Get the VM ID from the table row
    const vmRow = page.locator(`.ant-table-row:has-text("${vmName}")`).first();
    const vmId = await vmRow.getAttribute('data-row-key');
    
    if (!vmId) {
      throw new Error('Could not find VM ID');
    }
    
    console.log(`Found VM with ID: ${vmId}`);
    
    // Navigate to VM details page
    await page.goto(`/vagrant/vms/${vmId}`);
    await page.screenshot({ path: 'test_screenshots/07-vm-details.png' });
    
    // Step 6: Verify VM Status
    // Wait for VM to be in running state
    console.log('Waiting for VM to be in running state...');
    const success = await waitForVMStatus(page, vmId, 'running', 15);
    
    // Take screenshot of final VM state
    await page.screenshot({ path: 'test_screenshots/08-vm-running.png' });
    
    // Assert that VM is in expected state
    expect(success).toBeTruthy();
    
    console.log('Test completed successfully');
  });
}); 