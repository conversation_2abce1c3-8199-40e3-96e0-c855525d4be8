import { test, expect, Page } from '@playwright/test';
import * as path from 'path';
import * as fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

// This test uses the direct file-to-vm API endpoint to streamline the upload-to-VM process
test.describe('Direct File-to-VM API E2E Flow', () => {
  // Define test file details
  const testFilePath = path.join(__dirname, 'direct-api-test.txt');
  const testFileContent = `Test file content for VM creation test via direct API
Created at: ${new Date().toISOString()}
ID: ${uuidv4()}`;
  
  // Set up the test file before all tests
  test.beforeAll(async () => {
    // Create test file
    fs.writeFileSync(testFilePath, testFileContent);
    console.log(`Created test file at ${testFilePath}`);
  });
  
  // Clean up the test file after all tests
  test.afterAll(async () => {
    // Remove test file
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
      console.log(`Removed test file at ${testFilePath}`);
    }
  });
  
  // Define helper functions for VM operations
  async function waitForVMStatus(page: Page, vmId: string, targetStatus: string, maxAttempts = 10) {
    for (let i = 0; i < maxAttempts; i++) {
      // Go to VM details page
      await page.goto(`/vagrant/vms/${vmId}`);
      
      // Check if status matches target
      const statusElement = page.locator('.vm-status');
      if (await statusElement.isVisible()) {
        const status = await statusElement.textContent();
        if (status?.toLowerCase().includes(targetStatus.toLowerCase())) {
          return true;
        }
      }
      
      // Wait before checking again
      await page.waitForTimeout(3000);
    }
    
    return false;
  }
  
  // Get the auth token from localStorage
  async function getAuthToken(page: Page): Promise<string> {
    // Navigate to a page in the app first to ensure localStorage is available
    await page.goto('/');
    
    // Get the auth token from localStorage
    const token = await page.evaluate(() => {
      return localStorage.getItem('authToken') || '';
    });
    
    return token;
  }
  
  test('Should upload a file and create VM using direct API integration', async ({ page, request }) => {
    // Record video is enabled in the playwright.config.ts
    console.log('Starting Direct API File-to-VM test...');
    
    // Step 1: Navigate to the file upload page
    await page.goto('/upload');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of the upload page
    await page.screenshot({ path: 'test_screenshots/direct-api-01-upload-page.png' });
    
    // Get the auth token
    const token = await getAuthToken(page);
    expect(token).toBeTruthy();
    
    // Step 2: Upload a file via form
    // Fill description field
    await page.fill('textarea[placeholder*="description"]', 'Test file for direct VM API creation');
    
    // Upload the file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);
    
    // Take screenshot of the form with file selected
    await page.screenshot({ path: 'test_screenshots/direct-api-02-file-selected.png' });
    
    // Submit the upload
    await page.click('button:has-text("Upload")');
    
    // Wait for upload to complete and show success message
    await page.waitForSelector('.ant-message-success', { timeout: 30000 });
    await page.screenshot({ path: 'test_screenshots/direct-api-03-upload-success.png' });
    
    // Step 3: Get the latest uploaded file ID
    console.log('Getting list of uploaded files...');
    
    // Navigate to file list page
    await page.goto('/files');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot of file list
    await page.screenshot({ path: 'test_screenshots/direct-api-04-file-list.png' });
    
    // Get the first file ID from the list
    const firstFileRow = page.locator('.ant-table-row').first();
    await firstFileRow.waitFor();
    
    const fileId = await firstFileRow.getAttribute('data-row-key');
    expect(fileId).toBeTruthy();
    console.log(`Found file with ID: ${fileId}`);
    
    // Step 4: Create VM from file using direct API
    console.log('Creating VM from file using direct API...');
    
    // Generate VM name
    const vmName = `api-vm-${new Date().getTime()}`;
    
    // Make the API request
    const response = await request.post('/api/v1/file-to-vm/create', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      data: {
        file_id: fileId,
        vm_name: vmName,
        template: 'UBUNTU_2004',
        description: 'VM created via direct API in E2E test',
        memory_mb: 2048,
        cpus: 1,
        disk_gb: 20,
        auto_start: true
      }
    });
    
    // Verify successful response
    expect(response.ok()).toBeTruthy();
    const responseData = await response.json();
    console.log('API Response:', responseData);
    
    // Get the VM ID from the response
    const vmId = responseData.id;
    expect(vmId).toBeTruthy();
    console.log(`Created VM with ID: ${vmId}`);
    
    // Take screenshot of API response (just for the record)
    await page.screenshot({ path: 'test_screenshots/direct-api-05-api-response.png' });
    
    // Step 5: Navigate to VM list to verify creation
    await page.goto('/vagrant/vms');
    await page.waitForLoadState('networkidle');
    await page.screenshot({ path: 'test_screenshots/direct-api-06-vm-list.png' });
    
    // Verify VM appears in the list
    await page.waitForSelector(`.ant-table-row:has-text("${vmName}")`, { timeout: 30000 });
    
    // Navigate to VM details page
    await page.goto(`/vagrant/vms/${vmId}`);
    await page.screenshot({ path: 'test_screenshots/direct-api-07-vm-details.png' });
    
    // Step 6: Verify VM Status
    // Wait for VM to be in running state
    console.log('Waiting for VM to be in running state...');
    const success = await waitForVMStatus(page, vmId, 'running', 15);
    
    // Take screenshot of final VM state
    await page.screenshot({ path: 'test_screenshots/direct-api-08-vm-running.png' });
    
    // Assert that VM is in expected state
    expect(success).toBeTruthy();
    
    console.log('Direct API File-to-VM test completed successfully');
  });
}); 