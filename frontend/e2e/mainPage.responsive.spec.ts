import { expect, test } from '@playwright/test';

// Define screen sizes for different devices
const devices = {
  mobile: { width: 375, height: 667 },   // iPhone SE
  tablet: { width: 768, height: 1024 },  // iPad
  desktop: { width: 1280, height: 800 }  // Standard Desktop
};

test.describe('Main Page Responsive Design', () => {
  test('should render correctly on mobile', async ({ page }) => {
    // Set viewport to mobile size
    await page.setViewportSize(devices.mobile);
    
    // Go to the main page
    await page.goto('/');
    
    // Verify header adjusts
    const header = page.locator('.app-header');
    const headerBox = await header.boundingBox();
    
    // Header should span the full width
    expect(headerBox?.width).toBeCloseTo(devices.mobile.width, -1);
    
    // Navigation should stack on mobile
    const nav = page.locator('nav');
    const logo = page.locator('.logo');
    const navBox = await nav.boundingBox();
    const logoBox = await logo.boundingBox();
    
    // Nav should be below logo
    expect(navBox?.top).toBeGreaterThan(logoBox?.bottom || 0);
    
    // Hero section should stack
    const heroContent = page.locator('.hero-content');
    const heroVisual = page.locator('.hero-visual');
    
    const heroContentBox = await heroContent.boundingBox();
    const heroVisualBox = await heroVisual.boundingBox();
    
    // Visual should be below content
    expect(heroVisualBox?.top).toBeGreaterThan(heroContentBox?.bottom || 0);
    
    // CTA buttons should be full width
    const ctaButtons = page.locator('.hero-cta .cta-button');
    for (let i = 0; i < await ctaButtons.count(); i++) {
      const buttonBox = await ctaButtons.nth(i).boundingBox();
      // Button width should be close to container width (accounting for padding)
      expect(buttonBox?.width).toBeGreaterThan(devices.mobile.width * 0.7);
    }
  });

  test('should render correctly on tablet', async ({ page }) => {
    // Set viewport to tablet size
    await page.setViewportSize(devices.tablet);
    
    // Go to the main page
    await page.goto('/');
    
    // Check dashboard features
    const features = page.locator('.dashboard-features .feature');
    const firstFeature = features.first();
    const secondFeature = features.nth(1);
    
    const firstBox = await firstFeature.boundingBox();
    const secondBox = await secondFeature.boundingBox();
    
    // On tablet, features might be in a grid or stacked
    if (firstBox && secondBox) {
      // If in a row, they should be beside each other
      if (Math.abs(firstBox.top - secondBox.top) < 10) {
        expect(secondBox.left).toBeGreaterThan(firstBox.right);
      } 
      // If stacked, second should be below first
      else {
        expect(secondBox.top).toBeGreaterThan(firstBox.bottom);
      }
    }
    
    // Check certification grid
    const certCards = page.locator('.certification-card');
    
    if (await certCards.count() > 1) {
      const firstCard = await certCards.first().boundingBox();
      const secondCard = await certCards.nth(1).boundingBox();
      
      // Cards might be in a row or stacked on tablet
      if (firstCard && secondCard) {
        // If aligned horizontally
        if (Math.abs(firstCard.top - secondCard.top) < 10) {
          expect(secondCard.left).toBeGreaterThanOrEqual(firstCard.right);
        } 
        // If stacked
        else {
          expect(secondCard.top).toBeGreaterThanOrEqual(firstCard.bottom);
        }
      }
    }
  });

  test('should render correctly on desktop', async ({ page }) => {
    // Set viewport to desktop size
    await page.setViewportSize(devices.desktop);
    
    // Go to the main page
    await page.goto('/');
    
    // Verify header is horizontal
    const logo = page.locator('.logo');
    const nav = page.locator('nav');
    
    const logoBox = await logo.boundingBox();
    const navBox = await nav.boundingBox();
    
    // Nav should be on same level as logo
    expect(Math.abs((logoBox?.top || 0) - (navBox?.top || 0))).toBeLessThan(50);
    
    // Hero section should be side by side
    const heroContent = page.locator('.hero-content');
    const heroVisual = page.locator('.hero-visual');
    
    const heroContentBox = await heroContent.boundingBox();
    const heroVisualBox = await heroVisual.boundingBox();
    
    // Content and visual should be side by side (not stacked)
    expect(Math.abs((heroContentBox?.top || 0) - (heroVisualBox?.top || 0))).toBeLessThan(50);
    expect(heroVisualBox?.left).toBeGreaterThan(heroContentBox?.right || 0);
    
    // Dashboard features should be in a row
    const features = page.locator('.dashboard-features .feature');
    
    if (await features.count() > 1) {
      const firstFeature = await features.first().boundingBox();
      const secondFeature = await features.nth(1).boundingBox();
      
      if (firstFeature && secondFeature) {
        // Features should be side by side
        expect(Math.abs(firstFeature.top - secondFeature.top)).toBeLessThan(10);
        expect(secondFeature.left).toBeGreaterThan(firstFeature.right);
      }
    }
    
    // Certification cards should be in a row
    const certCards = page.locator('.certification-card');
    
    if (await certCards.count() > 1) {
      const firstCard = await certCards.first().boundingBox();
      const secondCard = await certCards.nth(1).boundingBox();
      
      if (firstCard && secondCard) {
        // Cards should be side by side
        expect(Math.abs(firstCard.top - secondCard.top)).toBeLessThan(10);
        expect(secondCard.left).toBeGreaterThan(firstCard.right);
      }
    }
  });

  test('should adapt text sizes for different screens', async ({ page }) => {
    // Test on mobile
    await page.setViewportSize(devices.mobile);
    await page.goto('/');
    
    const mobileH1FontSize = await page.evaluate(() => {
      const h1 = document.querySelector('.hero-content h1');
      return h1 ? window.getComputedStyle(h1).fontSize : '0px';
    });
    
    // Test on desktop
    await page.setViewportSize(devices.desktop);
    await page.goto('/');
    
    const desktopH1FontSize = await page.evaluate(() => {
      const h1 = document.querySelector('.hero-content h1');
      return h1 ? window.getComputedStyle(h1).fontSize : '0px';
    });
    
    // Desktop heading should be larger than mobile
    const mobileSize = parseInt(mobileH1FontSize);
    const desktopSize = parseInt(desktopH1FontSize);
    
    expect(desktopSize).toBeGreaterThan(mobileSize);
  });
}); 