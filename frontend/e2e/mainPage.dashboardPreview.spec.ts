import { expect, test } from '@playwright/test';

test.describe('Main Page Dashboard Preview Section', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the main page before each test
    await page.goto('/');
  });

  test('should have the correct section title and description', async ({ page }) => {
    // Check section title
    const title = page.locator('.dashboard-preview-section h2');
    await expect(title).toBeVisible();
    await expect(title).toContainText('Track Your Certification Journey');

    // Check section description
    const description = page.locator('.dashboard-preview-section > p');
    await expect(description).toBeVisible();
    await expect(description).toContainText('Monitor your progress');
  });

  test('should have a dashboard visualization placeholder', async ({ page }) => {
    // Check dashboard visualization placeholder exists
    const visualization = page.locator('.dashboard-preview-placeholder');
    await expect(visualization).toBeVisible();
  });

  test('should display three feature cards', async ({ page }) => {
    // Check we have three feature cards
    const features = page.locator('.dashboard-features .feature');
    await expect(features).toHaveCount(3);

    // Check feature titles
    const featureTitles = page.locator('.feature h3');
    const titles = await featureTitles.allTextContents();
    
    expect(titles).toContain('Progress Tracking');
    expect(titles).toContain('Study Time Management');
    expect(titles).toContain('ROI Calculator');
  });

  test('should have descriptive text in each feature card', async ({ page }) => {
    // Check feature descriptions exist
    const featureDescriptions = page.locator('.feature p');
    await expect(featureDescriptions).toHaveCount(3);
    
    // Verify descriptions are not empty
    const descriptions = await featureDescriptions.allTextContents();
    for (const desc of descriptions) {
      expect(desc.length).toBeGreaterThan(0);
    }
  });

  test('should apply hover effects on feature cards', async ({ page }) => {
    // Get the first feature and check its transformation
    const feature = page.locator('.feature').first();
    
    // Take a screenshot before hover
    const beforeHoverBox = await feature.boundingBox();
    
    // Hover over the feature and wait for animation
    await feature.hover();
    await page.waitForTimeout(300); // Wait for transition
    
    // Take a screenshot after hover to see if transform was applied
    const afterHoverBox = await feature.boundingBox();
    
    // The Y position should change due to translateY transform
    expect(afterHoverBox.y).toBeLessThan(beforeHoverBox.y);
  });
}); 