import { expect, test } from '@playwright/test';

test.describe('Job Search', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the job search page
    await page.goto('/new/jobs');
    
    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Job Search")');
  });

  test('should display job search components', async ({ page }) => {
    // Check that the search input is present
    await expect(page.locator('input[placeholder="Search jobs..."]')).toBeVisible();
    
    // Check that the filters section is present
    await expect(page.locator('.job-filters')).toBeVisible();
    
    // Check that the job list is displayed
    await expect(page.locator('.job-list')).toBeVisible();
    
    // Check that job cards are displayed
    await expect(page.locator('.job-card')).toBeVisible();
  });

  test('should filter jobs by search text', async ({ page }) => {
    // Get the initial count of job cards
    const initialCount = await page.locator('.job-card').count();
    
    // Enter a search term
    await page.fill('input[placeholder="Search jobs..."]', 'Security Analyst');
    
    // Wait for the search results to update
    await page.waitForTimeout(500);
    
    // Get the filtered count of job cards
    const filteredCount = await page.locator('.job-card').count();
    
    // The filtered count should be less than or equal to the initial count
    // (unless all jobs happen to match "Security Analyst")
    expect(filteredCount).toBeLessThanOrEqual(initialCount);
  });

  test('should filter jobs by location', async ({ page }) => {
    // Check that the location filter is present
    await expect(page.locator('.location-filter')).toBeVisible();
    
    // Enter a location in the filter
    await page.fill('.location-filter input', 'New York');
    
    // Wait for the filter to apply
    await page.waitForTimeout(500);
    
    // Check that the location input has the entered value
    await expect(page.locator('.location-filter input')).toHaveValue('New York');
  });

  test('should filter jobs by experience level', async ({ page }) => {
    // Check that the experience level filter is present
    await expect(page.locator('.experience-filter')).toBeVisible();
    
    // Select an experience level
    await page.selectOption('.experience-filter select', 'Entry-Level');
    
    // Wait for the filter to apply
    await page.waitForTimeout(300);
    
    // Check that the select has the selected value
    await expect(page.locator('.experience-filter select')).toHaveValue('Entry-Level');
  });

  test('should filter jobs by certification requirements', async ({ page }) => {
    // Check that the certification filter is present
    await expect(page.locator('.certification-filter')).toBeVisible();
    
    // Select a certification requirement
    await page.click('.certification-filter input[value="Security+"]');
    
    // Wait for the filter to apply
    await page.waitForTimeout(300);
    
    // Check that the checkbox is checked
    await expect(page.locator('.certification-filter input[value="Security+"]')).toBeChecked();
  });

  test('should display job details when clicking on a job card', async ({ page }) => {
    // Find and click on a job card
    const jobCards = await page.locator('.job-card').all();
    expect(jobCards.length).toBeGreaterThan(0);
    
    await jobCards[0].click();
    
    // Check that the job details section appears
    await expect(page.locator('.job-details')).toBeVisible();
    
    // Check that the job details contain the expected sections
    await expect(page.locator('.job-details .job-title')).toBeVisible();
    await expect(page.locator('.job-details .job-company')).toBeVisible();
    await expect(page.locator('.job-details .job-description')).toBeVisible();
    await expect(page.locator('.job-details .job-requirements')).toBeVisible();
  });

  test('should allow sorting jobs by different criteria', async ({ page }) => {
    // Check that the sort options are present
    await expect(page.locator('.sort-options')).toBeVisible();
    
    // Click on a sort option (e.g., "Salary")
    await page.click('.sort-options button:has-text("Salary")');
    
    // Check that the sort option is active
    await expect(page.locator('.sort-options button:has-text("Salary")')).toHaveClass(/active/);
  });
}); 