import { expect, test } from '@playwright/test';

test.describe('Main Page Career Path Section', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the main page before each test
    await page.goto('/');
  });

  test('should have the correct section title and description', async ({ page }) => {
    // Check section title
    const title = page.locator('.career-path-section h2');
    await expect(title).toBeVisible();
    await expect(title).toContainText('Visualize Your Career Path');

    // Check section description
    const description = page.locator('.career-path-section > p');
    await expect(description).toBeVisible();
    await expect(description).toContainText('Discover certification paths');
  });

  test('should have a career path visualization placeholder', async ({ page }) => {
    // Check visualization placeholder exists
    const visualization = page.locator('.career-path-placeholder');
    await expect(visualization).toBeVisible();
  });

  test('should display popular career paths', async ({ page }) => {
    // Check popular paths heading
    const heading = page.locator('.popular-paths h3');
    await expect(heading).toBeVisible();
    await expect(heading).toContainText('Popular Career Paths');
    
    // Check list of paths
    const pathItems = page.locator('.popular-paths li');
    await expect(pathItems).toHaveCount.greaterThan(1);
    
    // Check each path has a link
    const pathLinks = page.locator('.popular-paths li a');
    await expect(pathLinks).toHaveCount(await pathItems.count());
    
    // Check specific paths
    const pathTexts = await pathLinks.allTextContents();
    expect(pathTexts).toContain('Security Analyst');
    expect(pathTexts).toContain('Penetration Tester');
  });

  test('should navigate to specific career path on link click', async ({ page }) => {
    // Click the first career path link
    const firstPathLink = page.locator('.popular-paths li a').first();
    const pathText = await firstPathLink.textContent();
    
    await firstPathLink.click();
    
    // Verify we've navigated to the career path page with the correct path parameter
    await expect(page).toHaveURL(/career-path/);
    
    // If the text is "Security Analyst", expect the URL to contain "security-analyst"
    if (pathText?.includes('Security Analyst')) {
      await expect(page).toHaveURL(/security-analyst/);
    }
  });

  test('should apply hover effects on path items', async ({ page }) => {
    // Get the first path item and check its transformation
    const pathItem = page.locator('.popular-paths li').first();
    
    // Get the bounding box before hover
    const beforeHoverBox = await pathItem.boundingBox();
    
    // Hover over the path item and wait for animation
    await pathItem.hover();
    await page.waitForTimeout(300); // Wait for transition
    
    // Get the bounding box after hover
    const afterHoverBox = await pathItem.boundingBox();
    
    // The Y position should change due to translateY transform
    expect(afterHoverBox?.y).toBeLessThan(beforeHoverBox?.y);
  });
}); 