import { expect, test } from '@playwright/test';

test.describe('Main Page Hero Section', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the main page before each test
    await page.goto('/');
  });

  test('should have the correct title and subtitle', async ({ page }) => {
    // Check the hero title
    const title = page.locator('.hero-content h1');
    await expect(title).toBeVisible();
    await expect(title).toContainText('Navigate Your Cybersecurity Career Path');

    // Check the hero subtitle
    const subtitle = page.locator('.hero-content h2');
    await expect(subtitle).toBeVisible();
    await expect(subtitle).toContainText('Find, Plan, and Achieve the Right Certifications');
  });

  test('should have call-to-action buttons', async ({ page }) => {
    // Check primary CTA button
    const primaryCta = page.locator('.hero-cta .cta-button.primary');
    await expect(primaryCta).toBeVisible();
    
    // Check secondary CTA button
    const secondaryCta = page.locator('.hero-cta .cta-button.secondary');
    await expect(secondaryCta).toBeVisible();
    await expect(secondaryCta).toContainText('Explore Certifications');
  });

  test('should have a visualization area', async ({ page }) => {
    // Check visualization area exists
    const visualization = page.locator('.certification-path-visual');
    await expect(visualization).toBeVisible();
  });

  test('should navigate to certification explorer on secondary CTA click', async ({ page }) => {
    // Click the secondary CTA
    await page.click('.hero-cta .cta-button.secondary');
    
    // Verify we've navigated to the correct page
    await expect(page).toHaveURL(/certification-explorer/);
  });
}); 