import { expect, test } from '@playwright/test';

test.describe('Feedback Popup', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the dashboard where the feedback button is available
    await page.goto('/new');
    
    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Dashboard")');
  });

  test('should display feedback button', async ({ page }) => {
    // Check that the feedback button is visible
    await expect(page.locator('.feedback-button')).toBeVisible();
  });

  test('should open feedback popup when button is clicked', async ({ page }) => {
    // Click the feedback button
    await page.click('.feedback-button');
    
    // Check that the feedback popup is displayed
    await expect(page.locator('.feedback-popup-container')).toBeVisible();
    await expect(page.locator('h2:has-text("Share Your Feedback")')).toBeVisible();
  });

  test('should close feedback popup when close button is clicked', async ({ page }) => {
    // Open the feedback popup
    await page.click('.feedback-button');
    
    // Wait for the popup to appear
    await expect(page.locator('.feedback-popup-container')).toBeVisible();
    
    // Click the close button
    await page.click('.feedback-popup-close');
    
    // Check that the popup is no longer visible
    await expect(page.locator('.feedback-popup-container')).not.toBeVisible();
  });

  test('should close feedback popup when clicking outside', async ({ page }) => {
    // Open the feedback popup
    await page.click('.feedback-button');
    
    // Wait for the popup to appear
    await expect(page.locator('.feedback-popup-container')).toBeVisible();
    
    // Click outside the popup (on the overlay)
    await page.click('.feedback-popup-overlay', { position: { x: 10, y: 10 } });
    
    // Check that the popup is no longer visible
    await expect(page.locator('.feedback-popup-container')).not.toBeVisible();
  });

  test('should submit feedback form with valid data', async ({ page }) => {
    // Open the feedback popup
    await page.click('.feedback-button');
    
    // Fill out the form
    await page.selectOption('#feedback-type', 'bug');
    await page.fill('#feedback-message', 'This is a test feedback message');
    await page.fill('#feedback-email', '<EMAIL>');
    
    // Select a rating
    await page.click('.rating-button:nth-child(4)'); // 4-star rating
    
    // Submit the form
    await page.click('button:has-text("Submit Feedback")');
    
    // Check for success message
    await expect(page.locator('.feedback-success')).toBeVisible();
    await expect(page.locator('p:has-text("Thank you for your feedback!")')).toBeVisible();
  });

  test('should prevent submission when message is empty', async ({ page }) => {
    // Open the feedback popup
    await page.click('.feedback-button');
    
    // Try to submit without filling the required message field
    const submitButton = page.locator('button:has-text("Submit Feedback")');
    
    // Check that the button is disabled
    await expect(submitButton).toBeDisabled();
    
    // Add some text to enable the button
    await page.fill('#feedback-message', 'Test');
    await expect(submitButton).not.toBeDisabled();
    
    // Clear the text to disable the button again
    await page.fill('#feedback-message', '');
    await expect(submitButton).toBeDisabled();
  });

  test('should change feedback type', async ({ page }) => {
    // Open the feedback popup
    await page.click('.feedback-button');
    
    // Check the default type
    await expect(page.locator('#feedback-type')).toHaveValue('general');
    
    // Change the type
    await page.selectOption('#feedback-type', 'feature');
    
    // Verify the change
    await expect(page.locator('#feedback-type')).toHaveValue('feature');
  });
}); 