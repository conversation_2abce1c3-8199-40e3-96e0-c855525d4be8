import { expect, test } from "@playwright/test";

/**
 * End-to-end tests for the Cost Calculator page
 */
test.describe("Cost Calculator Page", () => {
  // Set up mocks before each test
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route("**/api/certifications", async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify([
          {
            id: 1,
            name: "Certified Ethical Hacker (CEH)",
            cost: 1200,
            level: "Intermediate",
            domain: "Security",
          },
          {
            id: 2,
            name: "CompTIA Security+",
            cost: 350,
            level: "Entry",
            domain: "Security",
          },
          {
            id: 3,
            name: "CISSP",
            cost: 700,
            level: "Advanced",
            domain: "Security",
          },
        ]),
      });
    });

    // Mock exchange rates API
    await page.route("**/api/currency/rates", async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          base: "USD",
          rates: {
            USD: 1,
            EUR: 0.92,
            GBP: 0.78,
            JPY: 145.5,
            CAD: 1.35,
          },
        }),
      });
    });

    // Navigate to the cost calculator page
    await page.goto("/cost-calculator");
  });

  test("should display the page title and description", async ({ page }) => {
    // Check page title
    await expect(page.locator("h1")).toContainText(
      "Certification Cost Calculator"
    );

    // Check description is displayed
    await expect(
      page.getByText(/Calculate the total investment needed/)
    ).toBeVisible();

    // Check list items in description
    await expect(page.getByText(/Exam fees and study materials/)).toBeVisible();
    await expect(page.getByText(/Potential retake costs/)).toBeVisible();
    await expect(page.getByText(/Currency conversion/)).toBeVisible();
    await expect(page.getByText(/Regional price variations/)).toBeVisible();
  });

  test("should load certification options", async ({ page }) => {
    // Check certification select is displayed
    const certSelect = page.locator("#cert-select");
    await expect(certSelect).toBeVisible();

    // Check options are displayed
    await expect(
      page.getByText("Certified Ethical Hacker (CEH)")
    ).toBeVisible();
    await expect(page.getByText("CompTIA Security+")).toBeVisible();
    await expect(page.getByText("CISSP")).toBeVisible();
  });

  test("should allow setting study materials cost", async ({ page }) => {
    // Check materials cost input is displayed with default value
    const materialsInput = page.locator("#materials-cost");
    await expect(materialsInput).toBeVisible();
    await expect(materialsInput).toHaveValue("100");

    // Change the value
    await materialsInput.fill("200");
    await expect(materialsInput).toHaveValue("200");
  });

  test("should display currency options", async ({ page }) => {
    // Check currency select is displayed
    const currencySelect = page.locator("#currency-select");
    await expect(currencySelect).toBeVisible();

    // Check USD is selected by default
    await expect(currencySelect).toHaveValue("USD");

    // Should have multiple currency options
    const options = page.locator("#currency-select option");
    await expect(options).toHaveCount(5);
  });

  test("should show retake analysis controls", async ({ page }) => {
    // Check retake analysis section header
    await expect(page.getByText("Exam Retake Analysis")).toBeVisible();

    // Check waiting period slider
    await expect(page.locator("#waiting-period")).toBeVisible();

    // Check discount slider
    await expect(page.locator("#retake-discount")).toBeVisible();

    // Check max attempts input
    const maxAttemptsInput = page.locator("#max-attempts");
    await expect(maxAttemptsInput).toBeVisible();
    await expect(maxAttemptsInput).toHaveValue("2");

    // Change max attempts
    await maxAttemptsInput.fill("3");
    await expect(maxAttemptsInput).toHaveValue("3");
  });

  test("should show empty message when no certifications selected", async ({
    page,
  }) => {
    // Empty selection message should be visible
    await expect(
      page.getByText("Select certifications to see cost breakdown")
    ).toBeVisible();

    // Cost breakdown should not be visible
    await expect(page.getByText("Cost Distribution")).not.toBeVisible();
  });

  test("should show cost breakdown when selecting a certification", async ({
    page,
  }) => {
    // Select a certification (using JS because multi-selects are tricky in Playwright)
    await page.evaluate(() => {
      const select = document.querySelector(
        "#cert-select"
      ) as HTMLSelectElement;
      select.options[0].selected = true;
      select.dispatchEvent(new Event("change"));
    });

    // Now cost breakdown should be visible
    await expect(page.getByText("Cost Distribution")).toBeVisible();
    await expect(page.getByText("Cost Breakdown")).toBeVisible();
    await expect(page.getByText("Base Exam Fees")).toBeVisible();
    await expect(page.getByText("Study Materials")).toBeVisible();
    await expect(page.getByText("Total Investment")).toBeVisible();

    // Chart should be visible (difficult to test directly, but the container should be there)
    await expect(page.locator(".chart-container")).toBeVisible();
  });

  test("should display certification details that can be expanded", async ({
    page,
  }) => {
    // Select a certification
    await page.evaluate(() => {
      const select = document.querySelector(
        "#cert-select"
      ) as HTMLSelectElement;
      select.options[0].selected = true;
      select.dispatchEvent(new Event("change"));
    });

    // Selected certifications section should appear
    await expect(page.getByText("Selected Certifications")).toBeVisible();

    // The CEH certification summary should be visible
    await expect(
      page.getByText("Certified Ethical Hacker (CEH)")
    ).toBeVisible();

    // Click to expand
    await page.click("summary.cert-summary");

    // Certification details should now be visible
    await expect(page.getByText("Base Cost:")).toBeVisible();
    await expect(page.getByText("Level:")).toBeVisible();
    await expect(page.getByText("Intermediate")).toBeVisible();

    // Retake analysis should be shown
    await expect(page.getByText("Retake Cost Analysis")).toBeVisible();
  });

  test("should change currency and update values", async ({ page }) => {
    // Select a certification
    await page.evaluate(() => {
      const select = document.querySelector(
        "#cert-select"
      ) as HTMLSelectElement;
      select.options[0].selected = true;
      select.dispatchEvent(new Event("change"));
    });

    // Change currency to EUR
    await page.selectOption("#currency-select", "EUR");

    // Currency should be changed
    await expect(page.locator("#currency-select")).toHaveValue("EUR");

    // Values should update, but we can't easily check the specific values
    // Just verify that the cost breakdown is still visible
    await expect(page.getByText("Cost Breakdown")).toBeVisible();
  });

  test("should be able to select multiple certifications", async ({ page }) => {
    // Select two certifications using JavaScript
    await page.evaluate(() => {
      const select = document.querySelector(
        "#cert-select"
      ) as HTMLSelectElement;
      select.options[0].selected = true;
      select.options[1].selected = true;
      select.dispatchEvent(new Event("change"));
    });

    // Cost breakdown should show
    await expect(page.getByText("Cost Breakdown")).toBeVisible();

    // Two certification summaries should be visible
    await expect(
      page.getByText("Certified Ethical Hacker (CEH)")
    ).toBeVisible();
    await expect(page.getByText("CompTIA Security+")).toBeVisible();
  });
});
