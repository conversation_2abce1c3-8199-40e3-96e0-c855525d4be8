import { expect, test } from '@playwright/test';

test.describe('Onboarding Tutorial', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the dashboard where the tutorial is available
    await page.goto('/new');
    
    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Dashboard")');
  });

  test('should display tutorial button', async ({ page }) => {
    // Check that the tutorial button is visible
    await expect(page.locator('.tutorial-button')).toBeVisible();
  });

  test('should start tutorial when button is clicked', async ({ page }) => {
    // Click the tutorial button
    await page.click('.tutorial-button');
    
    // Check that the tutorial is displayed
    await expect(page.locator('.onboarding-tooltip')).toBeVisible();
    
    // Check that the first step is displayed
    await expect(page.locator('.onboarding-tooltip h3')).toBeVisible();
  });

  test('should navigate through tutorial steps', async ({ page }) => {
    // Click the tutorial button
    await page.click('.tutorial-button');
    
    // Get the title of the first step
    const firstStepTitle = await page.locator('.onboarding-tooltip h3').textContent();
    
    // Click the Next button
    await page.click('.onboarding-tooltip .btn-next');
    
    // Wait for the transition
    await page.waitForTimeout(300);
    
    // Get the title of the second step
    const secondStepTitle = await page.locator('.onboarding-tooltip h3').textContent();
    
    // The titles should be different
    expect(secondStepTitle).not.toEqual(firstStepTitle);
    
    // Click the Previous button to go back to the first step
    await page.click('.onboarding-tooltip .btn-previous');
    
    // Wait for the transition
    await page.waitForTimeout(300);
    
    // Get the title again
    const backToFirstTitle = await page.locator('.onboarding-tooltip h3').textContent();
    
    // Should match the first step title
    expect(backToFirstTitle).toEqual(firstStepTitle);
  });

  test('should close tutorial when close button is clicked', async ({ page }) => {
    // Click the tutorial button
    await page.click('.tutorial-button');
    
    // Wait for the tutorial to appear
    await expect(page.locator('.onboarding-tooltip')).toBeVisible();
    
    // Click the close button
    await page.click('.onboarding-tooltip .onboarding-close');
    
    // Check that the tutorial is no longer visible
    await expect(page.locator('.onboarding-tooltip')).not.toBeVisible();
  });

  test('should skip tutorial when skip button is clicked', async ({ page }) => {
    // Click the tutorial button
    await page.click('.tutorial-button');
    
    // Wait for the tutorial to appear
    await expect(page.locator('.onboarding-tooltip')).toBeVisible();
    
    // Click the skip button
    await page.click('.onboarding-tooltip .btn-skip');
    
    // Check that the tutorial is no longer visible
    await expect(page.locator('.onboarding-tooltip')).not.toBeVisible();
  });

  test('should complete tutorial by reaching the last step', async ({ page }) => {
    // Click the tutorial button
    await page.click('.tutorial-button');
    
    // Wait for the tutorial to appear
    await expect(page.locator('.onboarding-tooltip')).toBeVisible();
    
    // Click Next button multiple times to reach the last step
    // This will vary based on the number of steps in your tutorial
    // Iterate through steps (assuming 6 steps as per the Dashboard implementation)
    for (let i = 0; i < 5; i++) {
      await page.click('.onboarding-tooltip .btn-next');
      await page.waitForTimeout(300);
    }
    
    // On the last step, the Next button should say "Done"
    await expect(page.locator('.onboarding-tooltip .btn-next')).toHaveText('Done');
    
    // Click Done to complete the tutorial
    await page.click('.onboarding-tooltip .btn-next');
    
    // Check that the tutorial is no longer visible
    await expect(page.locator('.onboarding-tooltip')).not.toBeVisible();
  });

  test('should show progress indicators for each step', async ({ page }) => {
    // Click the tutorial button
    await page.click('.tutorial-button');
    
    // Wait for the tutorial to appear
    await expect(page.locator('.onboarding-tooltip')).toBeVisible();
    
    // Check that progress dots are displayed
    const progressDots = await page.locator('.onboarding-progress .progress-dot').all();
    
    // There should be multiple progress dots
    expect(progressDots.length).toBeGreaterThan(1);
    
    // The first dot should be active
    await expect(progressDots[0]).toHaveClass(/active/);
  });
}); 