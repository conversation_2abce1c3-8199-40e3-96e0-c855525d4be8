import { expect, test } from '@playwright/test';

test.describe('Basic Connection Test', () => {
  test('should be able to access nginx', async ({ page }) => {
    await page.goto('http://certrats_nginx');
    
    // Take a screenshot to see what's displayed
    await page.screenshot({ path: 'nginx-homepage.png' });
    
    // Wait to ensure page has time to load
    await page.waitForTimeout(5000);
    
    // Check if the page loaded successfully (should not throw an error)
    expect(await page.title()).toBeDefined();
  });

  test('should be able to access React app root', async ({ page }) => {
    await page.goto('http://certrats_nginx:80');
    
    // Take a screenshot to see what's displayed
    await page.screenshot({ path: 'react-homepage.png' });
    
    // Wait to ensure page has time to load
    await page.waitForTimeout(5000);
    
    // Check if the page loaded successfully (should not throw an error)
    expect(await page.title()).toBeDefined();
  });
});
