import { expect, test } from '@playwright/test';

test.describe('Basic React App Test', () => {
  test('can access the React app', async ({ page }) => {
    console.log('Navigating to root page...');
    await page.goto('http://certrats_nginx');
    console.log('Page loaded');
    
    // Just check if we can find an element on the page
    const title = await page.title();
    console.log('Page title:', title);
    
    // Take a screenshot
    await page.screenshot({ path: 'screenshot.png' });
    console.log('Screenshot taken');
  });
});
