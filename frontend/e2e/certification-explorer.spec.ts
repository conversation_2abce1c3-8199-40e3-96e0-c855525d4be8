import { expect, test } from "@playwright/test";

/**
 * End-to-end tests for the Certification Explorer page
 */
test.describe("Certification Explorer", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the main page before each test
    await page.goto("/");
  });

  test("should display certification explorer title", async ({ page }) => {
    // Check that the main title is visible
    await expect(page.getByRole("heading", { level: 1 })).toBeVisible();
  });

  test("should filter certifications by domain", async ({ page }) => {
    // Get all domain filter buttons
    const domainFilters = page.getByTestId("domain-filter-button");

    // Verify there are multiple domain filters
    expect(await domainFilters.count()).toBeGreaterThan(0);

    // Click on the first domain filter
    await domainFilters.first().click();

    // Expect certifications to be filtered (results should update)
    await expect(page.getByTestId("certification-results")).toBeVisible();
  });

  test("should filter by experience level", async ({ page }) => {
    // Find and interact with experience level filter
    const experienceLevelFilter = page.getByTestId("experience-level-filter");

    // Select "Entry Level" from dropdown
    await experienceLevelFilter.selectOption("Entry Level");

    // Verify filter is applied (visual check and/or element count assertion)
    await expect(page.getByTestId("certification-results")).toBeVisible();
  });

  test("should support language switching", async ({ page }) => {
    // Find language switcher
    const languageSwitcher = page.getByTestId("language-switcher");

    // Change language to German
    await languageSwitcher.click();
    await page.getByText("Deutsch").click();

    // Verify page content is in German
    await expect(page.getByRole("heading", { level: 1 })).toContainText(
      "Zertifizierung"
    );
  });
});
