import { expect, test } from '@playwright/test';

test.describe('Theme Switching Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the main page before each test
    await page.goto('/');
  });

  test('should have theme toggle button', async ({ page }) => {
    // Check the theme toggle button exists
    const themeToggle = page.locator('.theme-toggle');
    await expect(themeToggle).toBeVisible();
  });

  test('should start with correct initial theme', async ({ page }) => {
    // Get the theme from localStorage or use light theme as default
    const theme = await page.evaluate(() => {
      return localStorage.getItem('theme') || 'light';
    });
    
    // Check body has the correct data-theme attribute
    const bodyTheme = await page.evaluate(() => document.body.getAttribute('data-theme'));
    expect(bodyTheme).toEqual(theme);
    
    // Check theme toggle button shows the correct icon
    const themeToggle = page.locator('.theme-toggle');
    const themeToggleText = await themeToggle.textContent();
    
    if (theme === 'light') {
      expect(themeToggleText).toEqual('🌙'); // Moon icon for light mode
    } else {
      expect(themeToggleText).toEqual('☀️'); // Sun icon for dark mode
    }
  });

  test('should toggle theme when clicked', async ({ page }) => {
    // Get initial theme
    const initialTheme = await page.evaluate(() => document.body.getAttribute('data-theme'));
    
    // Click the theme toggle button
    await page.click('.theme-toggle');
    
    // Get the new theme
    const newTheme = await page.evaluate(() => document.body.getAttribute('data-theme'));
    
    // Theme should be toggled
    expect(newTheme).not.toEqual(initialTheme);
    expect(newTheme).toEqual(initialTheme === 'light' ? 'dark' : 'light');
    
    // Check localStorage is updated
    const storedTheme = await page.evaluate(() => localStorage.getItem('theme'));
    expect(storedTheme).toEqual(newTheme);
    
    // Check icon is updated
    const themeToggleText = await page.locator('.theme-toggle').textContent();
    expect(themeToggleText).toEqual(newTheme === 'light' ? '🌙' : '☀️');
  });

  test('theme should persist across page navigation', async ({ page }) => {
    // Click the theme toggle to ensure we're in dark mode
    const initialTheme = await page.evaluate(() => document.body.getAttribute('data-theme'));
    if (initialTheme === 'light') {
      await page.click('.theme-toggle');
    }
    
    // Navigate to another page
    await page.click('nav a[href="/dashboard"]');
    
    // Wait for navigation
    await page.waitForURL(/dashboard/);
    
    // Check theme persists
    const newPageTheme = await page.evaluate(() => document.body.getAttribute('data-theme'));
    expect(newPageTheme).toEqual('dark');
  });

  test('should apply themed styles correctly', async ({ page }) => {
    // First, ensure we're in light mode
    const initialTheme = await page.evaluate(() => document.body.getAttribute('data-theme'));
    if (initialTheme !== 'light') {
      await page.click('.theme-toggle');
      await page.waitForTimeout(100); // Wait for transition
    }
    
    // Get background color in light mode
    const lightBackground = await page.evaluate(() => 
      window.getComputedStyle(document.body).backgroundColor
    );
    
    // Switch to dark mode
    await page.click('.theme-toggle');
    await page.waitForTimeout(300); // Wait for transition
    
    // Get background color in dark mode
    const darkBackground = await page.evaluate(() => 
      window.getComputedStyle(document.body).backgroundColor
    );
    
    // Colors should be different
    expect(darkBackground).not.toEqual(lightBackground);
    
    // Check specific elements styling changes
    const cardBgLight = await page.evaluate(() => {
      document.body.setAttribute('data-theme', 'light');
      return window.getComputedStyle(document.querySelector('.feature')).backgroundColor;
    });
    
    const cardBgDark = await page.evaluate(() => {
      document.body.setAttribute('data-theme', 'dark');
      return window.getComputedStyle(document.querySelector('.feature')).backgroundColor;
    });
    
    expect(cardBgDark).not.toEqual(cardBgLight);
  });
}); 