import { expect, test } from '@playwright/test';

test.describe('User Journeys', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the user journeys page
    await page.goto('/new/journeys');
    
    // Wait for the page to load
    await page.waitForSelector('h1:has-text("User Journeys")');
  });

  test('should display user journeys components', async ({ page }) => {
    // Check that the journey timeline is present
    await expect(page.locator('.journey-timeline')).toBeVisible();
    
    // Check that the journey details section is present
    await expect(page.locator('.journey-details')).toBeVisible();
    
    // Check that the timeline events are displayed
    await expect(page.locator('.timeline-event')).toBeVisible();
  });

  test('should display journey details when clicking on a timeline event', async ({ page }) => {
    // Find and click on a timeline event
    const timelineEvents = await page.locator('.timeline-event').all();
    expect(timelineEvents.length).toBeGreaterThan(0);
    
    await timelineEvents[0].click();
    
    // Check that the event details are displayed
    await expect(page.locator('.event-details')).toBeVisible();
    await expect(page.locator('.event-details .event-title')).toBeVisible();
    await expect(page.locator('.event-details .event-description')).toBeVisible();
  });

  test('should allow filtering timeline events by type', async ({ page }) => {
    // Check that the event type filters are present
    await expect(page.locator('.event-filters')).toBeVisible();
    
    // Get the initial count of visible events
    const initialCount = await page.locator('.timeline-event:visible').count();
    
    // Click on a specific filter (e.g., "Certification")
    await page.locator('.event-filters button:has-text("Certification")').click();
    
    // Wait for the filtering to apply
    await page.waitForTimeout(300);
    
    // Get the filtered count of visible events
    const filteredCount = await page.locator('.timeline-event:visible').count();
    
    // The filtered count should be less than or equal to the initial count
    // (unless all events happen to be certifications)
    expect(filteredCount).toBeLessThanOrEqual(initialCount);
  });

  test('should allow adding a new journey event', async ({ page }) => {
    // Click the "Add Event" button
    await page.locator('button:has-text("Add Event")').click();
    
    // Check that the add event form appears
    await expect(page.locator('.add-event-form')).toBeVisible();
    
    // Fill out the form
    await page.fill('.add-event-form input[name="title"]', 'Completed Security+ Training');
    await page.selectOption('.add-event-form select[name="type"]', 'training');
    await page.fill('.add-event-form textarea[name="description"]', 'Completed online training course for Security+');
    await page.fill('.add-event-form input[name="date"]', '2023-10-20');
    
    // Submit the form
    await page.click('.add-event-form button[type="submit"]');
    
    // Check for success message or indication that the event was added
    await expect(page.locator('.success-message')).toBeVisible();
  });

  test('should allow viewing community journeys', async ({ page }) => {
    // Click on the "Community Journeys" tab
    await page.locator('button:has-text("Community Journeys")').click();
    
    // Check that the community journeys section is displayed
    await expect(page.locator('.community-journeys')).toBeVisible();
    
    // Check that community journey cards are displayed
    await expect(page.locator('.community-journey-card')).toBeVisible();
  });

  test('should allow changing the timeline view', async ({ page }) => {
    // Check that the timeline view options are present
    await expect(page.locator('.timeline-view-options')).toBeVisible();
    
    // Click on a different view option (e.g., "Year")
    await page.locator('.timeline-view-options button:has-text("Year")').click();
    
    // Check that the year view is active
    await expect(page.locator('.timeline-view-options button:has-text("Year")')).toHaveClass(/active/);
    
    // Timeline should update to show the year view
    await expect(page.locator('.timeline.year-view')).toBeVisible();
  });
}); 