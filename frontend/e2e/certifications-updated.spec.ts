import { expect, test } from '@playwright/test';

test.describe('Certification Pages', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the certifications page before each test
    await page.goto('/certifications');
    
    // Give the page time to load
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);
  });

  test('should display certification explorer', async ({ page }) => {
    // Check if the main container and title are visible
    await expect(page.locator('.certification-explorer-container')).toBeVisible();
    await expect(page.locator('h1')).toBeVisible();
    
    // Check if the filters panel is visible
    await expect(page.locator('.filters-panel')).toBeVisible();
    
    // If the sample button exists, click it
    const loadSampleButton = page.locator('.load-sample-button');
    if (await loadSampleButton.isVisible()) {
      await loadSampleButton.click();
      await page.waitForTimeout(3000);
    }
    
    // Verify page structure is intact after dark mode changes
    const searchInput = page.locator('#search');
    if (await searchInput.isVisible()) {
      // Certification search input exists
      await expect(searchInput).toBeVisible();
    }
  });

  test('should have proper dark mode styling when theme is dark', async ({ page }) => {
    // First wait for the page to be ready
    await expect(page.locator('.certification-explorer-container')).toBeVisible();
    
    // Find the theme toggle (usually in header)
    const themeToggle = page.locator('.theme-toggle');
    
    // Check current theme
    const body = page.locator('body');
    const isDarkTheme = await body.evaluate(el => 
      window.getComputedStyle(el).backgroundColor.includes('rgb(18, 18, 18)') ||
      el.getAttribute('data-theme') === 'dark'
    );
    
    // If not dark theme, toggle it to dark
    if (!isDarkTheme && await themeToggle.isVisible()) {
      await themeToggle.click();
      await page.waitForTimeout(1000);
    }
    
    // Load sample data if button exists
    const loadSampleButton = page.locator('.load-sample-button');
    if (await loadSampleButton.isVisible()) {
      await loadSampleButton.click();
      await page.waitForTimeout(3000);
    }
    
    // Now check the dark mode styles on a certification card if any exists
    const card = page.locator('.certification-card').first();
    if (await card.isVisible()) {
      // Get background color to verify it's dark
      const bgColor = await card.evaluate(el => 
        window.getComputedStyle(el).backgroundColor
      );
      
      // Get header background color
      const cardHeader = page.locator('.certification-card-header').first();
      const headerBgColor = await cardHeader.evaluate(el => 
        window.getComputedStyle(el).backgroundColor
      );
      
      // Get footer background color 
      const cardFooter = page.locator('.certification-footer').first();
      const footerBgColor = await cardFooter.evaluate(el => 
        window.getComputedStyle(el).backgroundColor
      );
      
      // Verification checks
      expect(bgColor).not.toBe('rgb(255, 255, 255)'); // Not white
      expect(headerBgColor).not.toBe('rgb(255, 255, 255)'); // Not white
      expect(footerBgColor).not.toBe('rgb(255, 255, 255)'); // Not white
      
      // Verify our dark mode styling has darker headers than card body
      // The header should be darker than the card background in our new styling
      console.log(`Card background: ${bgColor}, Header background: ${headerBgColor}, Footer background: ${footerBgColor}`);
      
      // Take a screenshot for visual verification
      await page.screenshot({ path: '/tmp/dark-mode-cert-card.png' });
    }
  });

  test('should be responsive', async ({ page }) => {
    // First check that the page exists
    await expect(page.locator('.certification-explorer-container')).toBeVisible();
    
    // Test different viewport sizes
    const viewports = [
      { width: 375, height: 667, name: 'mobile' },
      { width: 768, height: 1024, name: 'tablet' },
      { width: 1280, height: 800, name: 'desktop' }
    ];
    
    for (const viewport of viewports) {
      // Set the viewport size
      await page.setViewportSize({ 
        width: viewport.width, 
        height: viewport.height 
      });
      
      // Wait for any responsive layout changes
      await page.waitForTimeout(500);
      
      // Check that the page is still visible without errors
      await expect(page.locator('.certification-explorer-container')).toBeVisible();
      
      // Take a screenshot for visual verification
      await page.screenshot({ 
        path: `/app/playwright-report/responsive-${viewport.name}.png` 
      });
    }
  });
});
