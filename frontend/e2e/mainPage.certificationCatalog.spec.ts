import { expect, test } from '@playwright/test';

test.describe('Main Page Certification Catalog Section', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the main page before each test
    await page.goto('/');
  });

  test('should have the correct section title and description', async ({ page }) => {
    // Check section title
    const title = page.locator('.certification-catalog-section h2');
    await expect(title).toBeVisible();
    await expect(title).toContainText('Explore Certifications');

    // Check section description
    const description = page.locator('.certification-catalog-section > p');
    await expect(description).toBeVisible();
    await expect(description).toContainText('Browse our comprehensive catalog');
  });

  test('should have filter options', async ({ page }) => {
    // Check filter groups
    const filterGroups = page.locator('.filter-group');
    await expect(filterGroups).toHaveCount(2);
    
    // Check filter labels
    const filterLabels = page.locator('.filter-group h4');
    const labels = await filterLabels.allTextContents();
    
    expect(labels).toContain('Domain');
    expect(labels).toContain('Level');
    
    // Check filter options
    const filterOptions = page.locator('.filter-options .filter-option');
    await expect(filterOptions).toHaveCount.greaterThan(5);
    
    // Check 'All' option exists in both filter groups
    const allOptions = page.locator('.filter-option.active');
    await expect(allOptions).toHaveCount(2);
  });

  test('should display certification cards', async ({ page }) => {
    // Check certification cards
    const certCards = page.locator('.certification-card');
    await expect(certCards).toHaveCount.greaterThan(0);
    
    // Check first card has title, level, cost and description
    const firstCard = certCards.first();
    
    await expect(firstCard.locator('h3')).toBeVisible();
    await expect(firstCard.locator('.cert-level')).toBeVisible();
    await expect(firstCard.locator('.cert-cost')).toBeVisible();
    await expect(firstCard.locator('p')).toBeVisible();
  });

  test('should have a view all link that navigates to certification explorer', async ({ page }) => {
    // Check view all link
    const viewAllLink = page.locator('.view-all-link a');
    await expect(viewAllLink).toBeVisible();
    await expect(viewAllLink).toContainText('View All Certifications');
    
    // Click the link and verify navigation
    await viewAllLink.click();
    await expect(page).toHaveURL(/certification-explorer/);
  });

  test('should apply hover effects on certification cards', async ({ page }) => {
    // Get the first card and check its transformation
    const card = page.locator('.certification-card').first();
    
    // Get the bounding box before hover
    const beforeHoverBox = await card.boundingBox();
    
    // Hover over the card and wait for animation
    await card.hover();
    await page.waitForTimeout(300); // Wait for transition
    
    // Get the bounding box after hover
    const afterHoverBox = await card.boundingBox();
    
    // The Y position should change due to translateY transform
    expect(afterHoverBox.y).toBeLessThan(beforeHoverBox.y);
  });

  test('should highlight filter options on hover', async ({ page }) => {
    // Get a non-active filter option
    const filterOption = page.locator('.filter-option').nth(2);
    
    // Get the computed style before hover
    const beforeHoverColor = await filterOption.evaluate(el => 
      window.getComputedStyle(el).backgroundColor
    );
    
    // Hover over the option
    await filterOption.hover();
    await page.waitForTimeout(200); // Wait for transition
    
    // Get the computed style after hover
    const afterHoverColor = await filterOption.evaluate(el => 
      window.getComputedStyle(el).backgroundColor
    );
    
    // The color should change
    expect(beforeHoverColor).not.toEqual(afterHoverColor);
  });
}); 