import { expect, test } from '@playwright/test';

test.describe('Main Page Getting Started Section', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the main page before each test
    await page.goto('/');
  });

  test('should have the correct section title and description', async ({ page }) => {
    // Check section title
    const title = page.locator('.getting-started-section h2');
    await expect(title).toBeVisible();
    await expect(title).toContainText('How to Get Started');

    // Check section description
    const description = page.locator('.getting-started-section > p');
    await expect(description).toBeVisible();
    await expect(description).toContainText('Begin your certification journey');
  });

  test('should have three step cards', async ({ page }) => {
    // Check the step cards
    const steps = page.locator('.steps-container .step');
    await expect(steps).toHaveCount(3);
    
    // Check each step has a number
    const stepNumbers = page.locator('.step-number');
    await expect(stepNumbers).toHaveCount(3);
    
    // Verify step numbers are sequential
    const numbers = await stepNumbers.allTextContents();
    expect(numbers).toEqual(['1', '2', '3']);
  });

  test('each step should have title and description', async ({ page }) => {
    // Get all steps
    const steps = page.locator('.step');
    const count = await steps.count();
    
    // Check each step has title and description
    for (let i = 0; i < count; i++) {
      const step = steps.nth(i);
      
      await expect(step.locator('h3')).toBeVisible();
      await expect(step.locator('p')).toBeVisible();
      
      // Verify content is not empty
      const title = await step.locator('h3').textContent();
      const description = await step.locator('p').textContent();
      
      expect(title?.length).toBeGreaterThan(0);
      expect(description?.length).toBeGreaterThan(0);
    }
  });

  test('should have CTA buttons', async ({ page }) => {
    // Check the CTA container
    const ctaContainer = page.locator('.getting-started-cta');
    await expect(ctaContainer).toBeVisible();
    
    // Check primary and secondary buttons
    const primaryButton = page.locator('.getting-started-cta .cta-button.primary');
    const secondaryButton = page.locator('.getting-started-cta .cta-button.secondary');
    
    await expect(primaryButton).toBeVisible();
    await expect(secondaryButton).toBeVisible();
    
    // Check button labels
    await expect(secondaryButton).toContainText('Take a Tour');
  });

  test('primary CTA should navigate appropriately', async ({ page }) => {
    // Get the primary CTA
    const primaryCta = page.locator('.getting-started-cta .cta-button.primary');
    
    // Click the button
    await primaryCta.click();
    
    // Check we navigated somewhere (either to signup or dashboard depending on auth state)
    const url = page.url();
    expect(url).toMatch(/signup|dashboard/);
  });

  test('secondary CTA should navigate to tutorial', async ({ page }) => {
    // Get the secondary CTA
    const secondaryCta = page.locator('.getting-started-cta .cta-button.secondary');
    
    // Click the button
    await secondaryCta.click();
    
    // Check we navigated to the tutorial page
    await expect(page).toHaveURL(/tutorial/);
  });

  test('should have clear visual hierarchy', async ({ page }) => {
    // Check the bounding boxes to ensure visual hierarchy
    const stepOne = page.locator('.step').first();
    const stepTwo = page.locator('.step').nth(1);
    const stepThree = page.locator('.step').nth(2);
    
    const boxOne = await stepOne.boundingBox();
    const boxTwo = await stepTwo.boundingBox();
    const boxThree = await stepThree.boundingBox();
    
    // In desktop view, steps should be side by side (horizontal)
    // In mobile view (which we're not testing here), they'd be stacked vertically
    expect(boxOne?.top).toBeCloseTo(boxTwo?.top || 0, 0);
    expect(boxTwo?.top).toBeCloseTo(boxThree?.top || 0, 0);
    
    // Steps should have proper horizontal spacing
    expect(boxTwo?.left).toBeGreaterThan(boxOne?.right || 0);
    expect(boxThree?.left).toBeGreaterThan(boxTwo?.right || 0);
  });
}); 