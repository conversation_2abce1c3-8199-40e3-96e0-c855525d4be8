import { expect, test } from '@playwright/test';

test.describe('Career Path Visualization', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the career path page
    await page.goto('/new/career-path');
    
    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Career Path Visualization")');
  });

  test('should display career path visualization components', async ({ page }) => {
    // Check that the visualization container is present
    await expect(page.locator('.visualization-container')).toBeVisible();
    
    // Check that the SVG graph is rendered
    await expect(page.locator('svg.career-path-graph')).toBeVisible();
    
    // Check that the legend is displayed
    await expect(page.locator('.legend')).toBeVisible();
  });

  test('should display nodes and links in the graph', async ({ page }) => {
    // Check that nodes are rendered
    const nodes = await page.locator('.node').all();
    expect(nodes.length).toBeGreaterThan(0);
    
    // Check that links (paths) are rendered
    const links = await page.locator('.link').all();
    expect(links.length).toBeGreaterThan(0);
  });

  test('should display node details when clicking on a node', async ({ page }) => {
    // Find and click on a node
    const nodes = await page.locator('.node').all();
    await nodes[0].click();
    
    // Check that the details panel appears
    await expect(page.locator('.node-details')).toBeVisible();
    
    // Check that the details panel contains information
    await expect(page.locator('.node-details h3')).toBeVisible();
    await expect(page.locator('.node-details .description')).toBeVisible();
  });

  test('should allow filtering the graph by difficulty level', async ({ page }) => {
    // Check that the difficulty filter is present
    await expect(page.locator('.difficulty-filter')).toBeVisible();
    
    // Click on a specific difficulty level (e.g., "Intermediate")
    await page.locator('.difficulty-filter button:has-text("Intermediate")').click();
    
    // Verify that the filter was applied (button should have an active state)
    await expect(page.locator('.difficulty-filter button:has-text("Intermediate")')).toHaveClass(/active/);
  });

  test('should allow searching for certifications', async ({ page }) => {
    // Check that the search input is present
    await expect(page.locator('input[placeholder="Search certifications..."]')).toBeVisible();
    
    // Enter a search term
    await page.fill('input[placeholder="Search certifications..."]', 'Security');
    
    // Wait for the search results to update
    await page.waitForTimeout(500);
    
    // Check that the search input has the entered value
    await expect(page.locator('input[placeholder="Search certifications..."]')).toHaveValue('Security');
  });

  test('should allow zooming and panning the graph', async ({ page }) => {
    // Get the initial transform value of the graph
    const initialTransform = await page.locator('svg.career-path-graph g.graph-container').getAttribute('transform');
    
    // Zoom in using the zoom buttons
    await page.locator('button.zoom-in').click();
    
    // Wait for the zoom transition to complete
    await page.waitForTimeout(300);
    
    // Get the new transform value after zooming
    const zoomedTransform = await page.locator('svg.career-path-graph g.graph-container').getAttribute('transform');
    
    // The transform should have changed
    expect(zoomedTransform).not.toEqual(initialTransform);
  });
}); 