import { expect, test } from '@playwright/test';

test.describe('FAQ Page', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the FAQ page
    await page.goto('/new/faq');
    
    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Frequently Asked Questions")');
  });

  test('should display FAQ page components', async ({ page }) => {
    // Check that the search input is present
    await expect(page.locator('input[placeholder="Search FAQs..."]')).toBeVisible();
    
    // Check that category filters are present
    await expect(page.locator('.category-filters')).toBeVisible();
    
    // Check that the FAQ list is displayed
    await expect(page.locator('.faq-list')).toBeVisible();
  });

  test('should expand FAQ items when clicked', async ({ page }) => {
    // Get all FAQ questions
    const faqItems = await page.locator('.faq-item').all();
    
    // Make sure we have at least one FAQ item
    expect(faqItems.length).toBeGreaterThan(0);
    
    // Click on the first FAQ item
    await faqItems[0].click();
    
    // Check that the answer is now visible
    await expect(faqItems[0].locator('.faq-answer')).toBeVisible();
    
    // Click on the same FAQ item again to collapse it
    await faqItems[0].click();
    
    // Check that the answer is now hidden
    await expect(faqItems[0].locator('.faq-answer')).not.toBeVisible();
  });

  test('should filter FAQs by search text', async ({ page }) => {
    // Count initial number of visible FAQs
    const initialCount = await page.locator('.faq-item').count();
    
    // Enter a search term
    await page.fill('input[placeholder="Search FAQs..."]', 'certification');
    
    // Wait for the filtering to apply
    await page.waitForTimeout(500);
    
    // Count number of visible FAQs after filtering
    const filteredCount = await page.locator('.faq-item').count();
    
    // The filtered count should be less than or equal to the initial count
    // Unless all FAQs happen to match the search term
    expect(filteredCount).toBeLessThanOrEqual(initialCount);
  });

  test('should filter FAQs by category', async ({ page }) => {
    // Find and click on a specific category filter (e.g., "General")
    const categoryButton = page.locator('button:has-text("General")').first();
    await categoryButton.click();
    
    // Wait for the filtering to apply
    await page.waitForTimeout(500);
    
    // Check that the active class is applied to the clicked category
    await expect(categoryButton).toHaveClass(/active/);
    
    // Click "All" category to reset filters
    await page.locator('button:has-text("All")').click();
    
    // Check that the "All" button has the active class
    await expect(page.locator('button:has-text("All")')).toHaveClass(/active/);
  });

  test('should clear search when clicking the clear button', async ({ page }) => {
    // Enter a search term
    await page.fill('input[placeholder="Search FAQs..."]', 'certification');
    
    // Click the clear button (assuming there's an 'X' button to clear the search)
    await page.locator('.search-clear-button').click();
    
    // Check that the search input is cleared
    await expect(page.locator('input[placeholder="Search FAQs..."]')).toHaveValue('');
  });
}); 