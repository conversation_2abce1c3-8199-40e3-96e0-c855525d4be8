import { expect, test } from '@playwright/test';

test.describe('Main Page Success Stories Section', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the main page before each test
    await page.goto('/');
  });

  test('should have the correct section title and description', async ({ page }) => {
    // Check section title
    const title = page.locator('.success-stories-section h2');
    await expect(title).toBeVisible();
    await expect(title).toContainText('Success Stories');

    // Check section description
    const description = page.locator('.success-stories-section > p');
    await expect(description).toBeVisible();
    await expect(description).toContainText('Real outcomes');
  });

  test('should display success story cards', async ({ page }) => {
    // Check we have success story cards
    const stories = page.locator('.success-story');
    await expect(stories).toHaveCount.greaterThan(0);
  });

  test('each success story should have proper structure', async ({ page }) => {
    // Get all success stories
    const stories = page.locator('.success-story');
    const count = await stories.count();
    
    // Check each story
    for (let i = 0; i < count; i++) {
      const story = stories.nth(i);
      
      // Check header with avatar and user info
      await expect(story.locator('.story-header')).toBeVisible();
      await expect(story.locator('.user-avatar')).toBeVisible();
      await expect(story.locator('.user-info h3')).toBeVisible();
      await expect(story.locator('.user-info p')).toBeVisible();
      
      // Check content
      await expect(story.locator('.story-content')).toBeVisible();
      const content = await story.locator('.story-content').textContent();
      expect(content?.length).toBeGreaterThan(10);
      
      // Check metrics
      await expect(story.locator('.story-metrics')).toBeVisible();
      const metrics = story.locator('.metric');
      await expect(metrics).toHaveCount.greaterThan(0);
      
      // Check metric values and labels
      await expect(story.locator('.metric-value').first()).toBeVisible();
      await expect(story.locator('.metric-label').first()).toBeVisible();
    }
  });

  test('metrics should display achievements data', async ({ page }) => {
    // Get all metric values
    const metricValues = page.locator('.metric-value');
    const metricLabels = page.locator('.metric-label');
    
    // Check we have multiple metrics
    await expect(metricValues).toHaveCount.greaterThan(2);
    
    // Get metric values and labels
    const values = await metricValues.allTextContents();
    const labels = await metricLabels.allTextContents();
    
    // Check for specific metrics (certifications, salary increase, etc.)
    expect(labels).toContain('Certifications');
    expect(labels).toContain('Salary Increase');
    
    // Check that values are numeric where appropriate
    const certCount = values.find(v => /^\d+$/.test(v.trim()));
    expect(certCount).toBeDefined();
    
    const salaryIncrease = values.find(v => v.includes('%'));
    expect(salaryIncrease).toBeDefined();
  });
  
  test('should have career progression information', async ({ page }) => {
    // Check for career progression in user info
    const progressionInfo = page.locator('.user-info p');
    
    // At least one story should have progression information with an arrow
    const progressions = await progressionInfo.allTextContents();
    const hasArrow = progressions.some(text => text.includes('→'));
    expect(hasArrow).toBeTruthy();
  });
}); 