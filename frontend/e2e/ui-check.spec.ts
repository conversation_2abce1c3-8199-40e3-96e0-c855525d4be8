import { expect, test } from '@playwright/test';
import { checkPageLoaded, visualCheck } from './visual-check';

/**
 * Basic UI checking test
 * This test just verifies that each key page in the application can load and render
 */
test.describe('Basic UI Checks', () => {
  test('Should load the Dashboard', async ({ page }) => {
    await page.goto('/');
    const success = await checkPageLoaded(page, 'Security Certification Explorer');
    expect(success).toBeTruthy();
  });

  test('Should load the Cost Calculator', async ({ page }) => {
    await page.goto('/cost-calculator');
    const success = await checkPageLoaded(page, 'Certification Cost Calculator');
    expect(success).toBeTruthy();
    
    // Take extra screenshot that shows controls
    await visualCheck(page, 'cost-calculator-controls');
  });

  test('Should load the FAQ page', async ({ page }) => {
    await page.goto('/faq');
    const success = await checkPageLoaded(page, 'Frequently Asked Questions');
    expect(success).toBeTruthy();
    
    // Click a FAQ item to expand it
    await page.click('.faq-question:first-child');
    await visualCheck(page, 'faq-expanded');
  });

  test('Should load the Career Path page', async ({ page }) => {
    await page.goto('/career-path');
    const success = await checkPageLoaded(page, 'Cybersecurity Career Paths');
    expect(success).toBeTruthy();
  });

  test('Should load the User Journeys page', async ({ page }) => {
    await page.goto('/journeys');
    const success = await checkPageLoaded(page, 'Cybersecurity Career Journeys');
    expect(success).toBeTruthy();
  });

  test('Should load the Job Search page', async ({ page }) => {
    await page.goto('/jobs');
    const success = await checkPageLoaded(page, 'Cybersecurity Job Search');
    expect(success).toBeTruthy();
  });

  test('Should handle 404 page', async ({ page }) => {
    await page.goto('/not-a-real-page');
    await visualCheck(page, '404-page');
    
    // Check if there's some 404 text visible
    const notFoundText = await page.textContent('body');
    expect(notFoundText).toContain('Not Found');
  });
}); 