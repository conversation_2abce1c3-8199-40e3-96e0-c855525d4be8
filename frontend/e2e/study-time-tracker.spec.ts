import { expect, test } from "@playwright/test";

/**
 * End-to-end tests for the Study Time Tracker page
 */
test.describe("Study Time Tracker Page", () => {
  // Set up mocks before each test
  test.beforeEach(async ({ page }) => {
    // Mock API responses
    await page.route("**/api/certifications", async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify([
          {
            id: 1,
            name: "Certified Ethical Hacker (CEH)",
            difficulty: 3,
            cost: 1199,
            prerequisites: "Basic networking knowledge",
          },
          {
            id: 2,
            name: "CompTIA Security+",
            difficulty: 2,
            cost: 349,
            prerequisites: "None",
          },
          {
            id: 3,
            name: "CISSP",
            difficulty: 4,
            cost: 699,
            prerequisites: "5 years of experience in information security",
          },
        ]),
      });
    });

    // Mock the study estimate API
    await page.route("**/api/study/estimate", async (route) => {
      const requestData = JSON.parse(route.request().postData() || "{}");
      const hoursPerWeek = requestData.hours_per_week || 10;

      const totalHours = 240;
      const weeksRequired = Math.round((totalHours / hoursPerWeek) * 10) / 10;

      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          status: "success",
          total_hours_required: totalHours,
          weeks_required: weeksRequired,
          weekly_schedule: [
            {
              week: 1,
              hours_planned: hoursPerWeek,
              focus_areas: [
                {
                  topic: "Introduction to Ethical Hacking",
                  hours_suggested: Math.round(hoursPerWeek * 0.6),
                  resources: ["Course Chapter 1", "Online Videos"],
                },
                {
                  topic: "Basic Networking Concepts",
                  hours_suggested: Math.round(hoursPerWeek * 0.4),
                  resources: ["Course Chapter 2"],
                },
              ],
              milestones: [
                {
                  description: "Complete introduction modules",
                  type: "Learning",
                  target_date: "2023-07-10",
                },
              ],
            },
          ],
          study_tips: [
            "Focus on hands-on labs",
            "Join the official study group",
          ],
        }),
      });
    });

    // Mock the ROI API
    await page.route("**/api/study/roi/**", async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({
          status: "success",
          percent_roi: 250,
          break_even_months: 6,
          five_year_value: 30000,
        }),
      });
    });

    // Mock the update progress API
    await page.route("**/api/study/progress", async (route) => {
      await route.fulfill({
        status: 200,
        body: JSON.stringify({ success: true }),
      });
    });

    // Navigate to the study time tracker page
    await page.goto("/study-time");
  });

  test("should display the page title and description", async ({ page }) => {
    // Check page title
    await expect(page.locator("h1")).toContainText("Study Time Estimator");

    // Check description
    await expect(
      page.getByText(/Plan your certification journey/)
    ).toBeVisible();
  });

  test("should load certifications in dropdown", async ({ page }) => {
    // Check certification dropdown has options
    const selectElement = page.locator("#certification-select");
    await expect(selectElement).toBeVisible();

    // Check options are loaded
    const options = page.locator("#certification-select option");
    await expect(options).toHaveCount(3);

    // Verify specific certifications
    await expect(
      page.getByText("Certified Ethical Hacker (CEH)")
    ).toBeVisible();
    await expect(page.getByText("CompTIA Security+")).toBeVisible();
    await expect(page.getByText("CISSP")).toBeVisible();
  });

  test("should update hours per week with slider", async ({ page }) => {
    // Get the slider
    const slider = page.locator("#hours-per-week");

    // Check initial value
    await expect(page.getByText("10", { exact: true })).toBeVisible();

    // Change slider value to 20
    await slider.fill("20");

    // Check updated value is displayed
    await expect(page.getByText("20", { exact: true })).toBeVisible();

    // Check that the results section updates with new values
    await expect(page.locator(".metric-value").first()).toContainText("240"); // Total hours stays the same
    await expect(page.locator(".metric-value").nth(1)).toContainText("12"); // Weeks should be half (240/20)
  });

  test("should toggle target date input", async ({ page }) => {
    // Check that target date input is not initially visible
    await expect(page.locator("#target-date")).not.toBeVisible();

    // Click the checkbox
    await page.getByText("Set Target Completion Date").click();

    // Check that target date input is now visible
    await expect(page.locator("#target-date")).toBeVisible();

    // Click again to hide
    await page.getByText("Set Target Completion Date").click();

    // Check that target date input is hidden again
    await expect(page.locator("#target-date")).not.toBeVisible();
  });

  test("should show progress tracking section", async ({ page }) => {
    // Check progress section is visible
    await expect(page.getByText("Progress Tracking")).toBeVisible();

    // Check slider is present
    await expect(page.locator("#progress-slider")).toBeVisible();

    // Check initial value is 0%
    await expect(page.getByText("0%", { exact: true })).toBeVisible();

    // Update progress to 50%
    await page.locator("#progress-slider").fill("50");

    // Check value updates
    await expect(page.getByText("50%", { exact: true })).toBeVisible();

    // Check completion message appears
    await expect(page.getByText(/You're 50% complete!/)).toBeVisible();
  });

  test("should display ROI analysis when checkbox is clicked", async ({
    page,
  }) => {
    // Check ROI section is not initially visible
    await expect(page.locator("#current-salary")).not.toBeVisible();

    // Click the checkbox
    await page.getByText("Calculate Return on Investment (ROI)").click();

    // Check that salary input is now visible
    await expect(page.locator("#current-salary")).toBeVisible();

    // Check default salary
    await expect(page.locator("#current-salary")).toHaveValue("75000");

    // Check ROI metrics are displayed
    await expect(page.getByText("250%")).toBeVisible();
    await expect(page.getByText("6", { exact: true })).toBeVisible();
    await expect(page.getByText("$30,000")).toBeVisible();

    // Update salary
    await page.locator("#current-salary").fill("100000");

    // ROI values should still be the same (since we're using a static mock)
    await expect(page.getByText("250%")).toBeVisible();
  });

  test("should toggle custom estimate form", async ({ page }) => {
    // Check that custom form is not initially visible
    await expect(page.getByText("Study Materials/Notes:")).not.toBeVisible();

    // Click the toggle button
    await page.getByText("Add Custom Time Estimate").click();

    // Check that form is visible
    await expect(page.getByText("Study Materials/Notes:")).toBeVisible();

    // Fill in the form
    await page.locator("#estimated-hours").fill("150");
    await page
      .locator("#study-materials")
      .fill("Using official course materials");

    // Submit the form
    await page.getByText("Add Estimate").click();

    // Check success message
    await expect(
      page.getByText("Custom estimate added successfully!")
    ).toBeVisible();
  });

  test("should display study recommendations", async ({ page }) => {
    // Check that recommendations section is visible
    await expect(page.getByText("Study Recommendations")).toBeVisible();

    // Check weekly schedule calculation
    await expect(page.getByText(/2 hours per weekday/)).toBeVisible();
    await expect(page.getByText(/5 hours per weekend day/)).toBeVisible();

    // Check prerequisites
    await expect(page.getByText("Basic networking knowledge")).toBeVisible();

    // Check study tips
    await expect(
      page.getByText(
        "Break down study sessions into 25-minute focused intervals"
      )
    ).toBeVisible();
    await expect(
      page.getByText("Join study groups or online communities")
    ).toBeVisible();

    // Check additional tips from API
    await expect(page.getByText("Focus on hands-on labs")).toBeVisible();
    await expect(page.getByText("Join the official study group")).toBeVisible();
  });

  test("should expand weekly schedule accordion", async ({ page }) => {
    // Check accordion exists
    await expect(page.locator(".accordion-header")).toBeVisible();

    // Initially accordion content should not be visible
    await expect(page.getByText("Resources:")).not.toBeVisible();

    // Click to expand
    await page.locator(".accordion-header").click();

    // Check content is now visible
    await expect(page.getByText("Resources:")).toBeVisible();
    await expect(
      page.getByText("Introduction to Ethical Hacking")
    ).toBeVisible();
    await expect(page.getByText("Type: Learning")).toBeVisible();
  });
});
