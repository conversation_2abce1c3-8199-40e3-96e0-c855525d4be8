import { expect, test } from '@playwright/test';
import { checkPageLoaded, visualCheck } from './visual-check';

/**
 * UI Compatibility tests
 * These tests verify that all components load without errors across 
 * different screen sizes and devices
 */
test.describe('UI Compatibility', () => {
  // Routes to test
  const routes = [
    { path: '/', title: 'Security Certification Explorer' },
    { path: '/explorer', title: 'Certification Explorer' },
    { path: '/profile', title: 'User Profile' },
    { path: '/study-time', title: 'Study Time Tracker' },
    { path: '/cost-calculator', title: 'Cost Calculator' },
    { path: '/career-path', title: 'Career Path' },
    { path: '/journeys', title: 'User Journeys' },
    { path: '/jobs', title: 'Job Search' },
    { path: '/faq', title: 'FAQ' },
    { path: '/admin', title: 'Admin' }
  ];

  // Test each route loads on desktop
  test.describe('Desktop Viewport', () => {
    test.use({ viewport: { width: 1280, height: 720 } });

    for (const route of routes) {
      test(`should load ${route.path} without errors`, async ({ page }) => {
        await page.goto(route.path);
        
        // Check if the page loaded with expected title
        const success = await checkPageLoaded(page, route.title);
        expect(success).toBeTruthy();
        
        // Take a screenshot for visual verification
        await visualCheck(page, `desktop-${route.path.replace(/\//g, '-')}`);
        
        // Verify no error messages are visible
        const errorTexts = await page.locator('.error-message, .error-boundary-container').count();
        expect(errorTexts).toBe(0);
      });
    }
  });

  // Test each route loads on tablet
  test.describe('Tablet Viewport', () => {
    test.use({ viewport: { width: 768, height: 1024 } });

    for (const route of routes) {
      test(`should load ${route.path} without errors on tablet`, async ({ page }) => {
        await page.goto(route.path);
        
        // Check if the page loaded with expected content
        const success = await checkPageLoaded(page, route.title);
        expect(success).toBeTruthy();
        
        // Take a screenshot for visual verification
        await visualCheck(page, `tablet-${route.path.replace(/\//g, '-')}`);
        
        // Verify no error messages are visible
        const errorTexts = await page.locator('.error-message, .error-boundary-container').count();
        expect(errorTexts).toBe(0);
      });
    }
  });

  // Test each route loads on mobile
  test.describe('Mobile Viewport', () => {
    test.use({ viewport: { width: 375, height: 667 } });

    for (const route of routes) {
      test(`should load ${route.path} without errors on mobile`, async ({ page }) => {
        await page.goto(route.path);
        
        // Check if the page loaded with expected content
        const success = await checkPageLoaded(page, route.title);
        expect(success).toBeTruthy();
        
        // Take a screenshot for visual verification
        await visualCheck(page, `mobile-${route.path.replace(/\//g, '-')}`);
        
        // Verify no error messages are visible
        const errorTexts = await page.locator('.error-message, .error-boundary-container').count();
        expect(errorTexts).toBe(0);
      });
    }
  });

  // Test interactions don't cause errors
  test.describe('UI Interactions', () => {
    test('should handle form interactions without errors', async ({ page }) => {
      // Test the cost calculator which has complex form interactions
      await page.goto('/cost-calculator');
      
      // Input some values
      await page.fill('input[type="number"]', '500');
      
      // Change a dropdown value
      await page.selectOption('select', { index: 1 });
      
      // Click a button
      await page.click('button:not([type="submit"])');
      
      // Take a screenshot to verify UI remains intact
      await visualCheck(page, 'interaction-test-cost-calculator');
      
      // Verify no errors appear
      const errorTexts = await page.locator('.error-message, .error-boundary-container').count();
      expect(errorTexts).toBe(0);
    });

    test('should handle rapid navigation without errors', async ({ page }) => {
      // Start on home page
      await page.goto('/');
      
      // Navigate quickly between pages
      for (let i = 0; i < 5; i++) {
        // Navigate to a page
        await page.goto('/profile');
        await page.waitForTimeout(100);
        
        // Navigate to another page
        await page.goto('/faq');
        await page.waitForTimeout(100);
        
        // Navigate to a third page
        await page.goto('/cost-calculator');
        await page.waitForTimeout(100);
      }
      
      // Take a screenshot to verify UI is still intact
      await visualCheck(page, 'rapid-navigation-test');
      
      // Verify no errors appear
      const errorTexts = await page.locator('.error-message, .error-boundary-container').count();
      expect(errorTexts).toBe(0);
    });
  });
}); 