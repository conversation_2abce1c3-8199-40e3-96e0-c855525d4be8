import { expect, test } from "@playwright/test";

/**
 * End-to-end tests for the User Profile page
 */
test.describe("User Profile Page", () => {
  // Mock user token before each test
  test.beforeEach(async ({ page }) => {
    // Simulate authenticated user by setting a fake JWT token in localStorage
    await page.addInitScript(() => {
      localStorage.setItem("auth_token", "fake-token-for-testing");

      // Mock API responses
      window.fetch = async function (url) {
        if (url.toString().includes("/api/auth/me")) {
          return {
            ok: true,
            json: async () => ({
              id: 1,
              username: "testuser",
              email: "<EMAIL>",
              isAdmin: false,
              createdAt: "2023-01-01T00:00:00.000Z",
            }),
          } as Response;
        }

        if (url.toString().includes("/api/users/preferences")) {
          return {
            ok: true,
            json: async () => ({
              theme: "dark",
              emailNotifications: true,
              studyReminders: false,
              weeklyGoalHours: 15,
              preferredDomains: ["Network Security", "Cloud Security"],
            }),
          } as Response;
        }

        if (url.toString().includes("/api/users/certifications")) {
          return {
            ok: true,
            json: async () => [
              {
                id: 1,
                name: "Certified Ethical Hacker (CEH)",
                progress: 75,
                targetCompletionDate: "2023-12-31T00:00:00.000Z",
              },
              {
                id: 2,
                name: "Certified Information Systems Security Professional (CISSP)",
                progress: 30,
                targetCompletionDate: null,
              },
            ],
          } as Response;
        }

        // Default response for unhandled URLs
        return { ok: false, status: 404 } as Response;
      };
    });

    // Navigate to the user profile page
    await page.goto("/profile");
  });

  test("should display user information correctly", async ({ page }) => {
    // Wait for the page to load
    await expect(page.locator(".profile-container")).toBeVisible();

    // Check username is displayed
    await expect(page.getByText("testuser")).toBeVisible();

    // Check email is displayed
    await expect(page.getByText("<EMAIL>")).toBeVisible();
  });

  test("should display user preferences correctly", async ({ page }) => {
    // Check theme preference
    await expect(page.getByText("dark")).toBeVisible();

    // Check preferred domains
    await expect(page.getByText("Network Security")).toBeVisible();
    await expect(page.getByText("Cloud Security")).toBeVisible();
  });

  test("should display certifications correctly", async ({ page }) => {
    // Check certification names
    await expect(
      page.getByText("Certified Ethical Hacker (CEH)")
    ).toBeVisible();
    await expect(
      page.getByText(
        "Certified Information Systems Security Professional (CISSP)"
      )
    ).toBeVisible();

    // Check progress is displayed
    await expect(page.locator(".progress-text").first()).toHaveText("75%");
    await expect(page.locator(".progress-text").nth(1)).toHaveText("30%");
  });

  test("should allow editing preferences", async ({ page }) => {
    // Mock the API call for updating preferences
    await page.route("**/api/users/preferences", async (route) => {
      const json = { success: true };
      await route.fulfill({ json });
    });

    // Click edit button
    await page.getByRole("button", { name: "edit" }).click();

    // Ensure form controls are visible
    await expect(page.getByText("Theme:")).toBeVisible();

    // Change theme
    await page.selectOption('select[name="theme"]', "light");

    // Toggle email notifications off
    await page.getByLabel("Email Notifications").uncheck();

    // Click save button
    await page.getByRole("button", { name: "save" }).click();

    // Should show success message
    await expect(
      page.getByText("Preferences updated successfully")
    ).toBeVisible();
  });

  test("should handle failed API calls gracefully", async ({ page }) => {
    // Mock a failed API response for preferences
    await page.route("**/api/users/preferences", async (route) => {
      await route.abort("failed");
    });

    // Reload the page to trigger the API failure
    await page.reload();

    // Should show error message
    await expect(page.getByText("Failed to load user data")).toBeVisible();
  });
});

test.describe("User Profile Page - Unauthenticated", () => {
  test("should show login prompt when not authenticated", async ({ page }) => {
    // Ensure no auth token exists
    await page.addInitScript(() => {
      localStorage.removeItem("auth_token");
    });

    // Navigate to the user profile page
    await page.goto("/profile");

    // Should show login prompt
    await expect(page.getByText("Login Required")).toBeVisible();
    await expect(page.getByRole("button", { name: "Login" })).toBeVisible();
  });
});
