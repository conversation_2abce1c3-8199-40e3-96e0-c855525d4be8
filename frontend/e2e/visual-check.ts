/**
 * Visual check utility for Playwright tests
 * This file provides a function to take screenshots of the app for visual verification
 */
import { Page } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Take a screenshot of the page for visual inspection
 * @param page The Playwright page
 * @param name The name of the screenshot
 * @param options Additional options for the screenshot
 */
export async function visualCheck(
  page: Page, 
  name: string, 
  options: {
    fullPage?: boolean;
    path?: string;
    timeout?: number;
  } = {}
) {
  // Default options
  const fullPage = options.fullPage ?? true;
  const timeout = options.timeout ?? 1000;
  
  // Create screenshots directory if it doesn't exist
  const screenshotsDir = path.join(process.cwd(), 'test_screenshots');
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir, { recursive: true });
  }
  
  // Wait for any animations to complete
  await page.waitForTimeout(timeout);
  
  // Take the screenshot
  const screenshotPath = options.path ?? path.join(screenshotsDir, `${name}-${Date.now()}.png`);
  await page.screenshot({
    path: screenshotPath,
    fullPage
  });
  
  console.log(`Screenshot saved to: ${screenshotPath}`);
  return screenshotPath;
}

/**
 * Simple page check to verify the page is visible and has basic content
 * @param page The Playwright page
 * @param title The title text to check for
 */
export async function checkPageLoaded(page: Page, title: string): Promise<boolean> {
  try {
    // Wait for a heading that contains the title
    await page.waitForSelector(`h1:has-text("${title}"), h2:has-text("${title}")`, { 
      timeout: 5000 
    });
    
    // Take a screenshot for visual verification
    await visualCheck(page, `page-${title.toLowerCase().replace(/\s+/g, '-')}`);
    
    return true;
  } catch (error) {
    console.error(`Page check failed for "${title}":`, error);
    
    // Take a screenshot of the error state
    await visualCheck(page, `error-${title.toLowerCase().replace(/\s+/g, '-')}`);
    
    return false;
  }
} 