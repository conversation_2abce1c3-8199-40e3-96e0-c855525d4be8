import { expect, test } from '@playwright/test';

test.describe('Admin Interface', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the admin page
    await page.goto('/new/admin');
    
    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Admin Interface")');
  });

  test('should display admin interface components', async ({ page }) => {
    // Check that all tabs are present
    await expect(page.locator('button:has-text("Certifications")')).toBeVisible();
    await expect(page.locator('button:has-text("Organizations")')).toBeVisible();
    await expect(page.locator('button:has-text("Translations")')).toBeVisible();
    await expect(page.locator('button:has-text("Feedback")')).toBeVisible();
  });

  test('should switch between tabs', async ({ page }) => {
    // Click on Organizations tab
    await page.click('button:has-text("Organizations")');
    await expect(page.locator('h2:has-text("Organization Management")')).toBeVisible();
    
    // Click on Translations tab
    await page.click('button:has-text("Translations")');
    await expect(page.locator('h2:has-text("Translation Management")')).toBeVisible();
    
    // Click on Feedback tab
    await page.click('button:has-text("Feedback")');
    await expect(page.locator('h2:has-text("Feedback Management")')).toBeVisible();
    
    // Click back to Certifications tab
    await page.click('button:has-text("Certifications")');
    await expect(page.locator('h2:has-text("Certification Management")')).toBeVisible();
  });

  test('should display certification list', async ({ page }) => {
    // Make sure we're on the Certifications tab
    await page.click('button:has-text("Certifications")');
    
    // Check that the certification table is displayed
    await expect(page.locator('table')).toBeVisible();
    
    // Check that the table headers are present
    await expect(page.locator('th:has-text("Name")')).toBeVisible();
    await expect(page.locator('th:has-text("Organization")')).toBeVisible();
    await expect(page.locator('th:has-text("Level")')).toBeVisible();
    await expect(page.locator('th:has-text("Actions")')).toBeVisible();
  });

  test('should open and close add certification modal', async ({ page }) => {
    // Make sure we're on the Certifications tab
    await page.click('button:has-text("Certifications")');
    
    // Click the Add Certification button
    await page.click('button:has-text("Add Certification")');
    
    // Check that the modal is displayed
    await expect(page.locator('h2:has-text("Add Certification")')).toBeVisible();
    
    // Close the modal
    await page.click('button:has-text("Cancel")');
    
    // Check that the modal is no longer displayed
    await expect(page.locator('h2:has-text("Add Certification")')).not.toBeVisible();
  });

  test('should filter certifications', async ({ page }) => {
    // Make sure we're on the Certifications tab
    await page.click('button:has-text("Certifications")');
    
    // Enter a search term
    await page.fill('input[placeholder="Search certifications..."]', 'Security');
    
    // Check that filtering works (this will depend on your mock data)
    // We'll just verify that the input value is retained
    await expect(page.locator('input[placeholder="Search certifications..."]')).toHaveValue('Security');
  });
}); 