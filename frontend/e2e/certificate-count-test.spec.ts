import { expect, test } from '@playwright/test';

/**
 * Test to verify certificates are properly displayed on the UI
 * The seed data should contain over 450 certificates
 */
test.describe('Certificate Count Verification', () => {
  test('should display the correct number of certificates on certification explorer page', async ({ page }) => {
    // Navigate directly to the certification explorer page
    await page.goto('/certification-explorer');
    
    // Wait for page to load completely
    await page.waitForLoadState('networkidle');
    
    // Check if certification cards are displayed
    const certCards = page.locator('.certification-card');
    
    // Get the count of certification cards
    const cardCount = await certCards.count();
    
    // Log the count for debugging
    console.log(`Number of certification cards found: ${cardCount}`);
    
    // Verify that certificates are displayed (should be over 450 based on seed data)
    expect(cardCount).toBeGreaterThan(0);
    
    // If we have pagination, check the total count if available
    const paginationInfo = page.locator('.pagination-info');
    if (await paginationInfo.isVisible()) {
      const paginationText = await paginationInfo.textContent();
      console.log(`Pagination info: ${paginationText}`);
      
      // Extract total count from pagination text if possible
      const totalMatch = paginationText?.match(/of\s+(\d+)/i);
      if (totalMatch && totalMatch[1]) {
        const totalCount = parseInt(totalMatch[1], 10);
        console.log(`Total certificates according to pagination: ${totalCount}`);
        
        // Verify the total count is at least 450
        expect(totalCount).toBeGreaterThanOrEqual(450);
      }
    }
  });

  test('should display certification details when clicking on a card', async ({ page }) => {
    // Navigate to the certification explorer page
    await page.goto('/certification-explorer');
    
    // Wait for page to load completely
    await page.waitForLoadState('networkidle');
    
    // Get certification cards
    const certCards = page.locator('.certification-card');
    
    // Check if there are any cards
    const cardCount = await certCards.count();
    if (cardCount === 0) {
      test.fail(true, 'No certification cards found to test details view');
      return;
    }
    
    // Click on the first certification card
    await certCards.first().click();
    
    // Wait for details modal/page to appear
    const certDetails = page.locator('.certification-details');
    await expect(certDetails).toBeVisible();
    
    // Verify certification details contain expected elements
    await expect(certDetails.locator('.cert-name')).toBeVisible();
    await expect(certDetails.locator('.cert-organization')).toBeVisible();
    await expect(certDetails.locator('.cert-description')).toBeVisible();
  });

  test('should filter certifications correctly', async ({ page }) => {
    // Navigate to the certification explorer page
    await page.goto('/certification-explorer');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Get the initial count of certification cards
    const initialCardCount = await page.locator('.certification-card').count();
    console.log(`Initial card count: ${initialCardCount}`);
    
    if (initialCardCount === 0) {
      test.fail(true, 'No certification cards found to test filtering');
      return;
    }
    
    // Apply a filter - select a specific domain
    const domainFilter = page.locator('.domain-filter select, .domain-filter button, [data-testid="domain-filter"]');
    if (await domainFilter.isVisible()) {
      await domainFilter.click();
      
      // Select the first domain option that's not "All"
      const domainOptions = page.locator('.domain-option:not(.all), .dropdown-item:not(:first-child), [data-testid="domain-filter-option"]');
      await domainOptions.first().click();
      
      // Wait for filtering to apply
      await page.waitForLoadState('networkidle');
      
      // Check filtered results
      const filteredCardCount = await page.locator('.certification-card').count();
      console.log(`Filtered card count: ${filteredCardCount}`);
      
      // The filtered count should be different from the initial count (unless all certs are in one domain)
      expect(filteredCardCount).toBeLessThanOrEqual(initialCardCount);
    } else {
      console.log('Domain filter not found - skipping filter test');
    }
  });
}); 