{"name": "certrats-frontend", "version": "0.1.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "certrats-frontend", "version": "0.1.0", "hasInstallScript": true, "dependencies": {"react-scripts": "5.0.1", "axios": "^1.3.2", "web-vitals": "^2.1.4", "@testing-library/user-event": "^13.5.0", "react-chartjs-2": "^5.2.0", "d3": "^7.8.5", "chart.js": "^4.3.0", "@mui/material": "^5.13.0", "@types/react-dom": "^18.0.10", "@mui/icons-material": "^5.11.16", "@types/d3": "^7.4.0", "@emotion/styled": "^11.11.0", "@testing-library/react": "^13.4.0", "typescript": "^4.9.5", "react-dom": "^18.2.0", "@types/node": "^16.18.11", "@types/jest": "^27.5.2", "react-router-dom": "^6.8.0", "@testing-library/jest-dom": "^5.16.5", "@emotion/react": "^11.11.0", "react": "^18.2.0", "@types/react": "^18.0.27"}, "devDependencies": {"@playwright/test": "^1.51.1", "msw": "^1.3.0", "npm-force-resolutions": "^0.0.10"}}, "node_modules/argparse": {"version": "1.0.10", "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/lower-case": {"version": "2.0.2", "license": "MIT", "dependencies": {"tslib": "^2.0.3"}}, "node_modules/d3-scale": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/d3-scale/-/d3-scale-4.0.2.tgz", "integrity": "sha512-GZW464g1SH7ag3Y7hXjf8RoUuAFIqklOAq3MRl4OaWabTFJY9PN/E1YklhXLh+OQ3fM9yS2nOkCoS+WLZ6kvxQ==", "license": "ISC", "dependencies": {"d3-array": "2.10.0 - 3", "d3-format": "1 - 3", "d3-interpolate": "1.2.0 - 3", "d3-time": "2.1.1 - 3", "d3-time-format": "2 - 4"}, "engines": {"node": ">=12"}}, "node_modules/foreground-child": {"version": "3.3.1", "license": "ISC", "dependencies": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/reflect.getprototypeof": {"version": "1.0.10", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.7", "get-proto": "^1.0.1", "which-builtin-type": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/multicast-dns": {"version": "7.2.5", "license": "MIT", "dependencies": {"dns-packet": "^5.2.2", "thunky": "^1.0.2"}, "bin": {"multicast-dns": "cli.js"}}, "node_modules/pkg-up/node_modules/locate-path": {"version": "3.0.0", "license": "MIT", "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/@emotion/hash": {"version": "0.9.2", "resolved": "https://registry.npmjs.org/@emotion/hash/-/hash-0.9.2.tgz", "integrity": "sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==", "license": "MIT"}, "node_modules/tsutils": {"version": "3.21.0", "license": "MIT", "dependencies": {"tslib": "^1.8.1"}, "engines": {"node": ">= 6"}, "peerDependencies": {"typescript": ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"}}, "node_modules/istanbul-lib-instrument/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/jest-runner": {"version": "27.5.1", "license": "MIT", "dependencies": {"emittery": "^0.8.1", "jest-docblock": "^27.5.1", "@jest/environment": "^27.5.1", "jest-haste-map": "^27.5.1", "jest-leak-detector": "^27.5.1", "jest-runtime": "^27.5.1", "source-map-support": "^0.5.6", "@types/node": "*", "@jest/console": "^27.5.1", "chalk": "^4.0.0", "@jest/test-result": "^27.5.1", "throat": "^6.0.1", "jest-util": "^27.5.1", "jest-message-util": "^27.5.1", "jest-environment-node": "^27.5.1", "@jest/transform": "^27.5.1", "jest-environment-jsdom": "^27.5.1", "jest-resolve": "^27.5.1", "@jest/types": "^27.5.1", "graceful-fs": "^4.2.9", "jest-worker": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/react-scripts": {"version": "5.0.1", "license": "MIT", "dependencies": {"eslint": "^8.3.0", "mini-css-extract-plugin": "^2.4.5", "style-loader": "^3.3.1", "workbox-webpack-plugin": "^6.4.1", "@babel/core": "^7.16.0", "semver": "^7.3.5", "postcss-loader": "^6.2.1", "eslint-webpack-plugin": "^3.1.1", "resolve": "^1.20.0", "dotenv": "^10.0.0", "terser-webpack-plugin": "^5.2.5", "react-refresh": "^0.11.0", "resolve-url-loader": "^4.0.0", "babel-jest": "^27.4.2", "webpack": "^5.64.4", "html-webpack-plugin": "^5.5.0", "fs-extra": "^10.0.0", "postcss": "^8.4.4", "file-loader": "^6.2.0", "react-dev-utils": "^12.0.1", "css-loader": "^6.5.1", "jest": "^27.4.3", "source-map-loader": "^3.0.0", "prompts": "^2.4.2", "bfj": "^7.0.2", "css-minimizer-webpack-plugin": "^3.2.0", "postcss-preset-env": "^7.0.1", "postcss-flexbugs-fixes": "^5.0.2", "camelcase": "^6.2.1", "webpack-manifest-plugin": "^4.0.2", "dotenv-expand": "^5.1.0", "jest-watch-typeahead": "^1.0.0", "tailwindcss": "^3.0.2", "webpack-dev-server": "^4.6.0", "babel-loader": "^8.2.3", "babel-preset-react-app": "^10.0.1", "browserslist": "^4.18.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "identity-obj-proxy": "^3.0.0", "eslint-config-react-app": "^7.0.1", "react-app-polyfill": "^3.0.0", "jest-resolve": "^27.4.2", "sass-loader": "^12.3.0", "postcss-normalize": "^10.0.1", "babel-plugin-named-asset-import": "^0.3.8", "@svgr/webpack": "^5.5.0"}, "bin": {"react-scripts": "bin/react-scripts.js"}, "engines": {"node": ">=14.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}, "peerDependencies": {"react": ">= 16", "typescript": "^3.2.1 || ^4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/thunky": {"version": "1.1.0", "license": "MIT"}, "node_modules/convert-source-map": {"version": "2.0.0", "license": "MIT"}, "node_modules/@webassemblyjs/wasm-gen": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "node_modules/string-natural-compare": {"version": "3.0.1", "license": "MIT"}, "node_modules/@types/d3-scale-chromatic": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/@types/d3-scale-chromatic/-/d3-scale-chromatic-3.1.0.tgz", "integrity": "sha512-iWMJgwkK7yTRmWqRB5plb1kadXyQ5Sj8V/zYlFGMUBbIPKQScw+Dku9cAAMgJG+z5GYDoMjWGLVOvjghDEFnKQ==", "license": "MIT"}, "node_modules/jest-watch-typeahead/node_modules/string-length": {"version": "5.0.1", "license": "MIT", "dependencies": {"char-regex": "^2.0.0", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/html-webpack-plugin": {"version": "5.6.3", "license": "MIT", "dependencies": {"@types/html-minifier-terser": "^6.0.0", "html-minifier-terser": "^6.0.2", "lodash": "^4.17.21", "pretty-error": "^4.0.0", "tapable": "^2.0.0"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/html-webpack-plugin"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "webpack": "^5.20.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}}, "node_modules/@babel/plugin-transform-unicode-sets-regex": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@alloc/quick-lru": {"version": "5.2.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@mui/utils": {"version": "5.17.1", "resolved": "https://registry.npmjs.org/@mui/utils/-/utils-5.17.1.tgz", "integrity": "sha512-jEZ8FTqInt2WzxDV8bhImWBqeQRD99c/id/fq83H0ER9tFl+sfZlaAoCdznGvbSQQ9ividMxqSV2c7cC1vBcQg==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.23.9", "@mui/types": "~7.2.15", "@types/prop-types": "^15.7.12", "clsx": "^2.1.1", "prop-types": "^15.8.1", "react-is": "^19.0.0"}, "engines": {"node": ">=12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/array.prototype.findlast": {"version": "1.2.5", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tr46": {"version": "2.1.0", "license": "MIT", "dependencies": {"punycode": "^2.1.1"}, "engines": {"node": ">=8"}}, "node_modules/acorn-globals": {"version": "6.0.0", "license": "MIT", "dependencies": {"acorn": "^7.1.1", "acorn-walk": "^7.1.1"}}, "node_modules/react-router-dom": {"version": "6.30.0", "license": "MIT", "dependencies": {"@remix-run/router": "1.23.0", "react-router": "6.30.0"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": ">=16.8"}}, "node_modules/@testing-library/user-event": {"version": "13.5.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5"}, "engines": {"node": ">=10", "npm": ">=6"}, "peerDependencies": {"@testing-library/dom": ">=7.21.4"}}, "node_modules/ajv-keywords": {"version": "3.5.2", "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/tryer": {"version": "1.0.1", "license": "MIT"}, "node_modules/es-to-primitive": {"version": "1.3.0", "license": "MIT", "dependencies": {"is-callable": "^1.2.7", "is-date-object": "^1.0.5", "is-symbol": "^1.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@babel/plugin-proposal-private-methods": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/terser": {"version": "5.39.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.8.2", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/express/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/expect": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "jest-get-type": "^27.5.1", "jest-matcher-utils": "^27.5.1", "jest-message-util": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@mswjs/cookies": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@mswjs/cookies/-/cookies-0.2.2.tgz", "integrity": "sha512-mlN83YSrcFgk7Dm1Mys40DLssI1KdJji2CMKN8eOlBqsTADYzj2+jWzsANsUTFbxDMWPD5e9bfA1RGqBpS3O1g==", "dev": true, "license": "MIT", "dependencies": {"@types/set-cookie-parser": "^2.4.0", "set-cookie-parser": "^2.4.6"}, "engines": {"node": ">=14"}}, "node_modules/d3-time": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/d3-time/-/d3-time-3.1.0.tgz", "integrity": "sha512-VqKjzBLejbSMT4IgbmVgDjpkYrNWUYJnbCGo874u7MMKIWsILRX+OpX/gTk8MqjpT1A/c6HY2dCA77ZN0lkQ2Q==", "license": "ISC", "dependencies": {"d3-array": "2 - 3"}, "engines": {"node": ">=12"}}, "node_modules/parse-json": {"version": "5.2.0", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/fast-uri": {"version": "3.0.6", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/postcss-js": {"version": "4.0.1", "license": "MIT", "dependencies": {"camelcase-css": "^2.0.1"}, "engines": {"node": "^12 || ^14 || >= 16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "peerDependencies": {"postcss": "^8.4.21"}}, "node_modules/autoprefixer": {"version": "10.4.21", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/autoprefixer"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"browserslist": "^4.24.4", "caniuse-lite": "^1.0.30001702", "fraction.js": "^4.3.7", "normalize-range": "^0.1.2", "picocolors": "^1.1.1", "postcss-value-parser": "^4.2.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "engines": {"node": "^10 || ^12 || >=14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/@babel/plugin-bugfix-firefox-class-in-computed-class-key": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/traverse": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/internal-slot": {"version": "1.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "hasown": "^2.0.2", "side-channel": "^1.1.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/postcss-normalize-positions": {"version": "5.1.1", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/asap": {"version": "2.0.6", "license": "MIT"}, "node_modules/bser": {"version": "2.1.1", "license": "Apache-2.0", "dependencies": {"node-int64": "^0.4.0"}}, "node_modules/is-obj": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array.prototype.flatmap": {"version": "1.3.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/static-eval/node_modules/escodegen": {"version": "1.14.3", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esprima": "^4.0.1", "estraverse": "^4.2.0", "esutils": "^2.0.2", "optionator": "^0.8.1"}, "bin": {"escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js"}, "engines": {"node": ">=4.0"}, "optionalDependencies": {"source-map": "~0.6.1"}}, "node_modules/playwright": {"version": "1.51.1", "resolved": "https://registry.npmjs.org/playwright/-/playwright-1.51.1.tgz", "integrity": "sha512-kkx+MB2KQRkyxjYPc3a0wLZZoDczmppyGJIvQ43l+aZihkaVvmu/21kiyaHeHjiFxjxNNFnUncKmcGIyOojsaw==", "dev": true, "license": "Apache-2.0", "dependencies": {"playwright-core": "1.51.1"}, "bin": {"playwright": "cli.js"}, "engines": {"node": ">=18"}, "optionalDependencies": {"fsevents": "2.3.2"}}, "node_modules/postcss-lab-function": {"version": "4.2.1", "license": "CC0-1.0", "dependencies": {"@csstools/postcss-progressive-custom-properties": "^1.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@babel/plugin-syntax-class-properties": {"version": "7.12.13", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/coa/node_modules/has-flag": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/webpack-dev-server": {"version": "4.15.2", "license": "MIT", "dependencies": {"colorette": "^2.0.10", "http-proxy-middleware": "^2.0.3", "p-retry": "^4.5.0", "@types/express": "^4.17.13", "compression": "^1.7.4", "webpack-dev-middleware": "^5.3.4", "launch-editor": "^2.6.0", "html-entities": "^2.3.2", "ansi-html-community": "^0.0.8", "@types/connect-history-api-fallback": "^1.3.5", "ipaddr.js": "^2.0.1", "@types/sockjs": "^0.3.33", "sockjs": "^0.3.24", "bonjour-service": "^1.0.11", "chokidar": "^3.5.3", "@types/serve-index": "^1.9.1", "@types/serve-static": "^1.13.10", "default-gateway": "^6.0.3", "schema-utils": "^4.0.0", "@types/bonjour": "^3.5.9", "express": "^4.17.3", "rimraf": "^3.0.2", "spdy": "^4.0.2", "open": "^8.0.9", "@types/ws": "^8.5.5", "selfsigned": "^2.1.1", "connect-history-api-fallback": "^2.0.0", "ws": "^8.13.0", "serve-index": "^1.9.1", "graceful-fs": "^4.2.6"}, "bin": {"webpack-dev-server": "bin/webpack-dev-server.js"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.37.0 || ^5.0.0"}, "peerDependenciesMeta": {"webpack": {"optional": true}, "webpack-cli": {"optional": true}}}, "node_modules/math-intrinsics": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/@babel/plugin-syntax-json-strings": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/get-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/svgo/node_modules/domutils/node_modules/domelementtype": {"version": "1.3.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/abab": {"version": "2.0.6", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/unpipe": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/tsconfig-paths": {"version": "3.15.0", "license": "MIT", "dependencies": {"@types/json5": "^0.0.29", "json5": "^1.0.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}}, "node_modules/resolve-from": {"version": "5.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jsonpath": {"version": "1.1.1", "license": "MIT", "dependencies": {"esprima": "1.2.2", "static-eval": "2.0.2", "underscore": "1.12.1"}}, "node_modules/@typescript-eslint/experimental-utils": {"version": "5.62.0", "license": "MIT", "dependencies": {"@typescript-eslint/utils": "5.62.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/common-tags": {"version": "1.8.2", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/is-weakset": {"version": "2.0.4", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/d3-dsv": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/d3-dsv/-/d3-dsv-3.0.1.tgz", "integrity": "sha512-UG6OvdI5afDIFP9w4G0mNq50dSOsXHJaRE8arAS5o9ApWnIElp8GZw1Dun8vP8OyHOZ/QJUKUJwxiiCCnUwm+Q==", "license": "ISC", "dependencies": {"commander": "7", "iconv-lite": "0.6", "rw": "1"}, "bin": {"csv2json": "bin/dsv2json.js", "csv2tsv": "bin/dsv2dsv.js", "dsv2dsv": "bin/dsv2dsv.js", "dsv2json": "bin/dsv2json.js", "json2csv": "bin/json2dsv.js", "json2dsv": "bin/json2dsv.js", "json2tsv": "bin/json2dsv.js", "tsv2csv": "bin/dsv2dsv.js", "tsv2json": "bin/dsv2json.js"}, "engines": {"node": ">=12"}}, "node_modules/make-dir": {"version": "3.1.0", "license": "MIT", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/postcss-selector-parser": {"version": "6.1.2", "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/@ungap/structured-clone": {"version": "1.3.0", "license": "ISC"}, "node_modules/estree-walker": {"version": "1.0.1", "license": "MIT"}, "node_modules/@istanbuljs/load-nyc-config/node_modules/camelcase": {"version": "5.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/string.prototype.includes": {"version": "2.0.1", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.3"}, "engines": {"node": ">= 0.4"}}, "node_modules/rollup-plugin-terser/node_modules/jest-worker": {"version": "26.6.2", "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/jest-watcher": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/test-result": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "jest-util": "^27.5.1", "string-length": "^4.0.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/workbox-webpack-plugin": {"version": "6.6.0", "license": "MIT", "dependencies": {"fast-json-stable-stringify": "^2.1.0", "pretty-bytes": "^5.4.1", "upath": "^1.2.0", "webpack-sources": "^1.4.3", "workbox-build": "6.6.0"}, "engines": {"node": ">=10.0.0"}, "peerDependencies": {"webpack": "^4.4.0 || ^5.9.0"}}, "node_modules/p-retry": {"version": "4.6.2", "license": "MIT", "dependencies": {"@types/retry": "0.12.0", "retry": "^0.13.1"}, "engines": {"node": ">=8"}}, "node_modules/@babel/plugin-transform-class-properties": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/workbox-build": {"version": "6.6.0", "license": "MIT", "dependencies": {"workbox-recipes": "6.6.0", "@babel/core": "^7.11.1", "@rollup/plugin-node-resolve": "^11.2.1", "fast-json-stable-stringify": "^2.1.0", "workbox-google-analytics": "6.6.0", "workbox-strategies": "6.6.0", "workbox-routing": "6.6.0", "common-tags": "^1.8.0", "workbox-broadcast-update": "6.6.0", "lodash": "^4.17.20", "workbox-precaching": "6.6.0", "rollup": "^2.43.1", "workbox-range-requests": "6.6.0", "source-map": "^0.8.0-beta.0", "@babel/runtime": "^7.11.2", "fs-extra": "^9.0.1", "workbox-expiration": "6.6.0", "stringify-object": "^3.3.0", "workbox-streams": "6.6.0", "@babel/preset-env": "^7.11.0", "@rollup/plugin-replace": "^2.4.1", "workbox-core": "6.6.0", "@apideck/better-ajv-errors": "^0.3.1", "workbox-cacheable-response": "6.6.0", "@rollup/plugin-babel": "^5.2.0", "strip-comments": "^2.0.1", "ajv": "^8.6.0", "workbox-sw": "6.6.0", "workbox-window": "6.6.0", "@surma/rollup-plugin-off-main-thread": "^2.2.3", "tempy": "^0.6.0", "workbox-background-sync": "6.6.0", "workbox-navigation-preload": "6.6.0", "pretty-bytes": "^5.3.0", "upath": "^1.2.0", "glob": "^7.1.6", "rollup-plugin-terser": "^7.0.0"}, "engines": {"node": ">=10.0.0"}}, "node_modules/istanbul-reports": {"version": "3.1.7", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-source-maps/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/@types/d3-brush": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/@types/d3-brush/-/d3-brush-3.0.6.tgz", "integrity": "sha512-nH60IZNNxEcrh6L1ZSMNA28rj27ut/2ZmI3r96Zd+1jrZD++zD3LsMIjWlvg4AYrHn/Pqz4CF3veCxGjtbqt7A==", "license": "MIT", "dependencies": {"@types/d3-selection": "*"}}, "node_modules/is-path-inside": {"version": "3.0.3", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@types/d3-hierarchy": {"version": "3.1.7", "resolved": "https://registry.npmjs.org/@types/d3-hierarchy/-/d3-hierarchy-3.1.7.tgz", "integrity": "sha512-tJFtNoYBtRtkNysX1Xq4sxtjK8YgoWUNpIiUee0/jHGRwqvzYxkq0hGVbbOGSz+JgFxxRu4K8nb3YpG3CMARtg==", "license": "MIT"}, "node_modules/chardet": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/chardet/-/chardet-0.7.0.tgz", "integrity": "sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/ieee754": {"version": "1.13.2", "license": "MIT", "dependencies": {"@xtuc/ieee754": "^1.2.0"}}, "node_modules/iterator.prototype": {"version": "1.1.5", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.6", "get-proto": "^1.0.0", "has-symbols": "^1.1.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/@emotion/babel-plugin/node_modules/source-map": {"version": "0.5.7", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/@types/resolve": {"version": "1.17.1", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/exit": {"version": "0.1.2", "engines": {"node": ">= 0.8.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/workbox-broadcast-update": {"version": "6.6.0", "license": "MIT", "dependencies": {"workbox-core": "6.6.0"}}, "node_modules/@babel/eslint-parser": {"version": "7.26.10", "license": "MIT", "dependencies": {"@nicolo-ribaudo/eslint-scope-5-internals": "5.1.1-v1", "eslint-visitor-keys": "^2.1.0", "semver": "^6.3.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || >=14.0.0"}, "peerDependencies": {"@babel/core": "^7.11.0", "eslint": "^7.5.0 || ^8.0.0 || ^9.0.0"}}, "node_modules/@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/which-builtin-type": {"version": "1.2.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "function.prototype.name": "^1.1.6", "has-tostringtag": "^1.0.2", "is-async-function": "^2.0.0", "is-date-object": "^1.1.0", "is-finalizationregistry": "^1.1.0", "is-generator-function": "^1.0.10", "is-regex": "^1.2.1", "is-weakref": "^1.0.2", "isarray": "^2.0.5", "which-boxed-primitive": "^1.1.0", "which-collection": "^1.0.2", "which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/jest-regex-util": {"version": "27.5.1", "license": "MIT", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/deep-equal": {"version": "2.2.3", "license": "MIT", "dependencies": {"is-date-object": "^1.0.5", "regexp.prototype.flags": "^1.5.1", "object-keys": "^1.1.1", "is-arguments": "^1.1.1", "is-array-buffer": "^3.0.2", "which-collection": "^1.0.1", "get-intrinsic": "^1.2.2", "object-is": "^1.1.5", "is-shared-array-buffer": "^1.0.2", "object.assign": "^4.1.4", "which-boxed-primitive": "^1.0.2", "is-regex": "^1.1.4", "array-buffer-byte-length": "^1.0.0", "call-bind": "^1.0.5", "side-channel": "^1.0.4", "isarray": "^2.0.5", "es-get-iterator": "^1.1.3", "which-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@types/sockjs": {"version": "0.3.36", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@emotion/react/node_modules/@emotion/weak-memoize": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/@emotion/weak-memoize/-/weak-memoize-0.3.1.tgz", "integrity": "sha512-EsBwpc7hBUJWAsNPBmJy4hxWx12v6bshQsldrVmjxJoc3isbxhOrF2IcCpaXxfvq03NwkI7sbsOLXbYuqF/8Ww==", "license": "MIT"}, "node_modules/@babel/plugin-transform-duplicate-named-capturing-groups-regex": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/cssom": {"version": "0.4.4", "license": "MIT"}, "node_modules/postcss-focus-within": {"version": "5.0.4", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.9"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/merge-descriptors": {"version": "1.0.3", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@sinclair/typebox": {"version": "0.24.51", "license": "MIT"}, "node_modules/jest-message-util": {"version": "27.5.1", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "^27.5.1", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^27.5.1", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/css-what": {"version": "6.1.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">= 6"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/@xtuc/long": {"version": "4.2.2", "license": "Apache-2.0"}, "node_modules/svgo/node_modules/css-select": {"version": "2.1.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-what": "^3.2.1", "domutils": "^1.7.0", "nth-check": "^1.0.2"}}, "node_modules/coa/node_modules/supports-color": {"version": "5.5.0", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/babel-plugin-named-asset-import": {"version": "0.3.8", "license": "MIT", "peerDependencies": {"@babel/core": "^7.1.0"}}, "node_modules/@babel/plugin-proposal-numeric-separator": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-optional-catch-binding": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/css-prefers-color-scheme": {"version": "6.0.3", "license": "CC0-1.0", "bin": {"css-prefers-color-scheme": "dist/cli.cjs"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/rxjs": {"version": "7.8.2", "resolved": "https://registry.npmjs.org/rxjs/-/rxjs-7.8.2.tgz", "integrity": "sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/static-eval/node_modules/prelude-ls": {"version": "1.1.2", "engines": {"node": ">= 0.8.0"}}, "node_modules/postcss-discard-empty": {"version": "5.1.1", "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/istanbul-lib-report/node_modules/make-dir": {"version": "4.0.0", "license": "MIT", "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/yargs-parser": {"version": "20.2.9", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/depd": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/@rollup/pluginutils": {"version": "3.1.0", "license": "MIT", "dependencies": {"@types/estree": "0.0.39", "estree-walker": "^1.0.1", "picomatch": "^2.2.2"}, "engines": {"node": ">= 8.0.0"}, "peerDependencies": {"rollup": "^1.20.0||^2.0.0"}}, "node_modules/csso/node_modules/mdn-data": {"version": "2.0.14", "license": "CC0-1.0"}, "node_modules/@jest/transform": {"version": "27.5.1", "license": "MIT", "dependencies": {"@babel/core": "^7.1.0", "@jest/types": "^27.5.1", "babel-plugin-istanbul": "^6.1.1", "chalk": "^4.0.0", "convert-source-map": "^1.4.0", "fast-json-stable-stringify": "^2.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^27.5.1", "jest-regex-util": "^27.5.1", "jest-util": "^27.5.1", "micromatch": "^4.0.4", "pirates": "^4.0.4", "slash": "^3.0.0", "source-map": "^0.6.1", "write-file-atomic": "^3.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jsonpath/node_modules/esprima": {"version": "1.2.2", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=0.4.0"}}, "node_modules/istanbul-lib-source-maps": {"version": "4.0.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}, "engines": {"node": ">=10"}}, "node_modules/eslint-plugin-import": {"version": "2.31.0", "license": "MIT", "dependencies": {"semver": "^6.3.1", "doctrine": "^2.1.0", "object.values": "^1.2.0", "is-glob": "^4.0.3", "eslint-module-utils": "^2.12.0", "@rtsao/scc": "^1.1.0", "hasown": "^2.0.2", "string.prototype.trimend": "^1.0.8", "array.prototype.flat": "^1.3.2", "tsconfig-paths": "^3.15.0", "eslint-import-resolver-node": "^0.3.9", "object.groupby": "^1.0.3", "array.prototype.flatmap": "^1.3.2", "object.fromentries": "^2.0.8", "debug": "^3.2.7", "is-core-module": "^2.15.1", "minimatch": "^3.1.2", "array.prototype.findlastindex": "^1.2.5", "array-includes": "^3.1.8"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9"}}, "node_modules/unicode-canonical-property-names-ecmascript": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@types/prop-types": {"version": "15.7.14", "license": "MIT"}, "node_modules/async": {"version": "3.2.6", "license": "MIT"}, "node_modules/string.prototype.trimstart": {"version": "1.0.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/etag": {"version": "1.8.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs.realpath": {"version": "1.0.0", "license": "ISC"}, "node_modules/dotenv": {"version": "10.0.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10"}}, "node_modules/coa": {"version": "2.0.2", "license": "MIT", "dependencies": {"@types/q": "^1.5.1", "chalk": "^2.4.1", "q": "^1.1.2"}, "engines": {"node": ">= 4.0"}}, "node_modules/stack-utils": {"version": "2.0.6", "license": "MIT", "dependencies": {"escape-string-regexp": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/readdirp": {"version": "3.6.0", "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/caniuse-lite": {"version": "1.0.30001706", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/eslint": {"version": "8.57.1", "license": "MIT", "dependencies": {"ignore": "^5.2.0", "eslint-scope": "^7.2.2", "js-yaml": "^4.1.0", "natural-compare": "^1.4.0", "doctrine": "^3.0.0", "file-entry-cache": "^6.0.1", "is-glob": "^4.0.0", "lodash.merge": "^4.6.2", "eslint-visitor-keys": "^3.4.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0", "espree": "^9.6.1", "@ungap/structured-clone": "^1.2.0", "imurmurhash": "^0.1.4", "cross-spawn": "^7.0.2", "@eslint/eslintrc": "^2.1.4", "graphemer": "^1.4.0", "is-path-inside": "^3.0.3", "@nodelib/fs.walk": "^1.2.8", "@eslint-community/regexpp": "^4.6.1", "@humanwhocodes/module-importer": "^1.0.1", "@eslint-community/eslint-utils": "^4.2.0", "chalk": "^4.0.0", "debug": "^4.3.2", "ajv": "^6.12.4", "@humanwhocodes/config-array": "^0.13.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "esutils": "^2.0.2", "globals": "^13.19.0", "minimatch": "^3.1.2", "glob-parent": "^6.0.2", "fast-deep-equal": "^3.1.3", "esquery": "^1.4.2", "find-up": "^5.0.0", "optionator": "^0.9.3", "escape-string-regexp": "^4.0.0", "@eslint/js": "8.57.1"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@emotion/weak-memoize": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/@emotion/weak-memoize/-/weak-memoize-0.4.0.tgz", "integrity": "sha512-snKqtPW01tN0ui7yu9rGv69aJXr/a/Ywvl11sUjNtEcRc+ng/mQriFL0wLXMef74iHa/EkftbDzU9F8iFbH+zg==", "license": "MIT"}, "node_modules/babel-plugin-polyfill-corejs3": {"version": "0.11.1", "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.3", "core-js-compat": "^3.40.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/get-symbol-description": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/colord": {"version": "2.9.3", "license": "MIT"}, "node_modules/typed-array-byte-length": {"version": "1.0.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@emotion/babel-plugin": {"version": "11.13.5", "resolved": "https://registry.npmjs.org/@emotion/babel-plugin/-/babel-plugin-11.13.5.tgz", "integrity": "sha512-pxHCpT2ex+0q+HH91/zsdHkw/lXd468DIN2zvfvLtPKLLMo6gQj7oLObq8PhkrxOZb/gGCq03S3Z7PDhS8pduQ==", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.16.7", "@babel/runtime": "^7.18.3", "@emotion/hash": "^0.9.2", "@emotion/memoize": "^0.9.0", "@emotion/serialize": "^1.3.3", "babel-plugin-macros": "^3.1.0", "convert-source-map": "^1.5.0", "escape-string-regexp": "^4.0.0", "find-root": "^1.1.0", "source-map": "^0.5.7", "stylis": "4.2.0"}}, "node_modules/schema-utils/node_modules/ajv-keywords": {"version": "5.1.0", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "node_modules/@mswjs/interceptors/node_modules/strict-event-emitter": {"version": "0.2.8", "resolved": "https://registry.npmjs.org/strict-event-emitter/-/strict-event-emitter-0.2.8.tgz", "integrity": "sha512-KDf/ujU8Zud3YaLtMCcTI4xkZlZVIYxTLr+XIULexP+77EEVWixeXroLUXQXiVtH4XH2W7jr/3PT1v3zBuvc3A==", "dev": true, "license": "MIT", "dependencies": {"events": "^3.3.0"}}, "node_modules/@nicolo-ribaudo/eslint-scope-5-internals": {"version": "5.1.1-v1", "license": "MIT", "dependencies": {"eslint-scope": "5.1.1"}}, "node_modules/postcss-modules-values": {"version": "4.0.0", "license": "ISC", "dependencies": {"icss-utils": "^5.0.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/dotenv-expand": {"version": "5.1.0", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/tapable": {"version": "2.2.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/postcss-modules-scope/node_modules/postcss-selector-parser": {"version": "7.1.0", "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/relateurl": {"version": "0.2.7", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/@babel/plugin-transform-unicode-escapes": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/espree": {"version": "9.6.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@testing-library/react/node_modules/aria-query": {"version": "5.1.3", "license": "Apache-2.0", "dependencies": {"deep-equal": "^2.0.5"}}, "node_modules/@csstools/postcss-oklab-function": {"version": "1.1.1", "license": "CC0-1.0", "dependencies": {"@csstools/postcss-progressive-custom-properties": "^1.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.25.9", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@csstools/postcss-trigonometric-functions": {"version": "1.0.2", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/safe-push-apply": {"version": "1.0.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "isarray": "^2.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/postcss-normalize-repeat-style": {"version": "5.1.1", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/websocket-driver": {"version": "0.7.4", "license": "Apache-2.0", "dependencies": {"http-parser-js": ">=0.5.1", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.6", "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "license": "MIT"}, "node_modules/@emotion/serialize": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/@emotion/serialize/-/serialize-1.3.3.tgz", "integrity": "sha512-EISGqt7sSNWHGI76hC7x1CksiXPahbxEOrC5RjmFRJTqLyEK9/9hZvBbiYn70dw4wuwMKiEMCUlR6ZXTSWQqxA==", "license": "MIT", "dependencies": {"@emotion/hash": "^0.9.2", "@emotion/memoize": "^0.9.0", "@emotion/unitless": "^0.10.0", "@emotion/utils": "^1.4.2", "csstype": "^3.0.2"}}, "node_modules/babel-plugin-macros": {"version": "3.1.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5", "cosmiconfig": "^7.0.0", "resolve": "^1.19.0"}, "engines": {"node": ">=10", "npm": ">=6"}}, "node_modules/fork-ts-checker-webpack-plugin/node_modules/fs-extra": {"version": "9.1.0", "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/d3-chord": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/d3-chord/-/d3-chord-3.0.1.tgz", "integrity": "sha512-VE5S6TNa+j8msksl7HwjxMHDM2yNK3XCkusIlpX5kwauBfXuyLAtNg9jCp/iHH61tgI4sb6R/EIMWCqEIdjT/g==", "license": "ISC", "dependencies": {"d3-path": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/@babel/plugin-transform-regexp-modifiers": {"version": "7.26.0", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@svgr/babel-plugin-svg-em-dimensions": {"version": "5.4.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/big.js": {"version": "5.2.2", "license": "MIT", "engines": {"node": "*"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/acorn-globals/node_modules/acorn": {"version": "7.4.1", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/eslint-module-utils": {"version": "2.12.0", "license": "MIT", "dependencies": {"debug": "^3.2.7"}, "engines": {"node": ">=4"}, "peerDependenciesMeta": {"eslint": {"optional": true}}}, "node_modules/duplexer": {"version": "0.1.2", "license": "MIT"}, "node_modules/on-finished": {"version": "2.4.1", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/@types/yargs-parser": {"version": "21.0.3", "license": "MIT"}, "node_modules/rollup": {"version": "2.79.2", "license": "MIT", "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=10.0.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/v8-to-istanbul/node_modules/convert-source-map": {"version": "1.9.0", "license": "MIT"}, "node_modules/buffer": {"version": "5.7.1", "resolved": "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz", "integrity": "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/jest-diff": {"version": "27.5.1", "license": "MIT", "dependencies": {"chalk": "^4.0.0", "diff-sequences": "^27.5.1", "jest-get-type": "^27.5.1", "pretty-format": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/performance-now": {"version": "2.1.0", "license": "MIT"}, "node_modules/@csstools/postcss-ic-unit": {"version": "1.0.1", "license": "CC0-1.0", "dependencies": {"@csstools/postcss-progressive-custom-properties": "^1.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@types/d3": {"version": "7.4.3", "resolved": "https://registry.npmjs.org/@types/d3/-/d3-7.4.3.tgz", "integrity": "sha512-lZXZ9ckh5R8uiFVt8ogUNf+pIrK4EsWrx2Np75WvF/eTpJ0FMHNhjXk8CKEx/+gpHbNQyJWehbFaTvqmHWB3ww==", "license": "MIT", "dependencies": {"@types/d3-zoom": "*", "@types/d3-timer": "*", "@types/d3-chord": "*", "@types/d3-random": "*", "@types/d3-shape": "*", "@types/d3-scale": "*", "@types/d3-array": "*", "@types/d3-dsv": "*", "@types/d3-drag": "*", "@types/d3-selection": "*", "@types/d3-hierarchy": "*", "@types/d3-polygon": "*", "@types/d3-color": "*", "@types/d3-interpolate": "*", "@types/d3-axis": "*", "@types/d3-time-format": "*", "@types/d3-geo": "*", "@types/d3-time": "*", "@types/d3-fetch": "*", "@types/d3-transition": "*", "@types/d3-brush": "*", "@types/d3-format": "*", "@types/d3-quadtree": "*", "@types/d3-contour": "*", "@types/d3-scale-chromatic": "*", "@types/d3-force": "*", "@types/d3-path": "*", "@types/d3-ease": "*", "@types/d3-dispatch": "*", "@types/d3-delaunay": "*"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/@jest/source-map": {"version": "27.5.1", "license": "MIT", "dependencies": {"callsites": "^3.0.0", "graceful-fs": "^4.2.9", "source-map": "^0.6.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@emotion/react": {"version": "11.11.0", "resolved": "https://registry.npmjs.org/@emotion/react/-/react-11.11.0.tgz", "integrity": "sha512-ZSK3ZJsNkwfjT3JpDAWJZlrGD81Z3ytNDsxw1LKq1o+xkmO5pnWfr6gmCC8gHEFf3nSSX/09YrG67jybNPxSUw==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.3", "@emotion/babel-plugin": "^11.11.0", "@emotion/cache": "^11.11.0", "@emotion/serialize": "^1.1.2", "@emotion/use-insertion-effect-with-fallbacks": "^1.0.1", "@emotion/utils": "^1.2.1", "@emotion/weak-memoize": "^0.3.1", "hoist-non-react-statics": "^3.3.1"}, "peerDependencies": {"react": ">=16.8.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@typescript-eslint/utils": {"version": "5.62.0", "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@types/json-schema": "^7.0.9", "@types/semver": "^7.3.12", "@typescript-eslint/scope-manager": "5.62.0", "@typescript-eslint/types": "5.62.0", "@typescript-eslint/typescript-estree": "5.62.0", "eslint-scope": "^5.1.1", "semver": "^7.3.7"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/connect-history-api-fallback": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/jest-snapshot": {"version": "27.5.1", "license": "MIT", "dependencies": {"@babel/plugin-syntax-typescript": "^7.7.2", "@babel/core": "^7.7.2", "semver": "^7.3.2", "natural-compare": "^1.4.0", "jest-matcher-utils": "^27.5.1", "jest-diff": "^27.5.1", "jest-get-type": "^27.5.1", "jest-haste-map": "^27.5.1", "expect": "^27.5.1", "chalk": "^4.0.0", "jest-util": "^27.5.1", "jest-message-util": "^27.5.1", "@types/prettier": "^2.1.5", "@types/babel__traverse": "^7.0.4", "@babel/traverse": "^7.7.2", "@jest/transform": "^27.5.1", "pretty-format": "^27.5.1", "babel-preset-current-node-syntax": "^1.0.0", "@babel/types": "^7.0.0", "@jest/types": "^27.5.1", "graceful-fs": "^4.2.9", "@babel/generator": "^7.7.2"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/callsites": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/jest-watch-typeahead/node_modules/emittery": {"version": "0.10.2", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sindresorhus/emittery?sponsor=1"}}, "node_modules/websocket-extensions": {"version": "0.1.4", "license": "Apache-2.0", "engines": {"node": ">=0.8.0"}}, "node_modules/get-stream": {"version": "6.0.1", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/confusing-browser-globals": {"version": "1.0.11", "license": "MIT"}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/@types/semver": {"version": "7.5.8", "license": "MIT"}, "node_modules/postcss-initial": {"version": "4.0.1", "license": "MIT", "peerDependencies": {"postcss": "^8.0.0"}}, "node_modules/entities": {"version": "2.2.0", "license": "BSD-2-<PERSON><PERSON>", "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/jackspeak": {"version": "3.4.3", "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/robust-predicates": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/robust-predicates/-/robust-predicates-3.0.2.tgz", "integrity": "sha512-IXgzBWvWQwE6PrDI05OvmXUIruQTcoMDzRsOd5CDvHCVLcLHMTSYvOK5Cm46kWqlV3yAbuSpBZdJ5oP5OUoStg==", "license": "Unlicense"}, "node_modules/object-keys": {"version": "1.1.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/regexp.prototype.flags": {"version": "1.5.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/jest-pnp-resolver": {"version": "1.2.3", "license": "MIT", "engines": {"node": ">=6"}, "peerDependencies": {"jest-resolve": "*"}, "peerDependenciesMeta": {"jest-resolve": {"optional": true}}}, "node_modules/node-fetch": {"version": "2.7.0", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz", "integrity": "sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==", "dev": true, "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/eslint-import-resolver-node/node_modules/debug": {"version": "3.2.7", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/@types/mime": {"version": "1.3.5", "license": "MIT"}, "node_modules/shebang-regex": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cssdb": {"version": "7.11.2", "funding": [{"type": "opencollective", "url": "https://opencollective.com/csstools"}, {"type": "github", "url": "https://github.com/sponsors/csstools"}], "license": "CC0-1.0"}, "node_modules/glob": {"version": "7.2.3", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/jest-watch-typeahead/node_modules/react-is": {"version": "18.3.1", "license": "MIT"}, "node_modules/@babel/helper-compilation-targets/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/didyoumean": {"version": "1.2.2", "license": "Apache-2.0"}, "node_modules/@leichtgewicht/ip-codec": {"version": "2.0.5", "license": "MIT"}, "node_modules/eslint-visitor-keys": {"version": "3.4.3", "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/domutils": {"version": "2.8.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "^1.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.2.0"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "node_modules/@types/d3-contour": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/@types/d3-contour/-/d3-contour-3.0.6.tgz", "integrity": "sha512-BjzLgXGnCWjUSYGfH1cpdo41/hgdWETu4YxpezoztawmqsvCeep+8QGfiY6YbDvfgHz/DkjeIkkZVJavB4a3rg==", "license": "MIT", "dependencies": {"@types/d3-array": "*", "@types/geojson": "*"}}, "node_modules/jsdom": {"version": "16.7.0", "license": "MIT", "dependencies": {"is-potential-custom-element-name": "^1.0.1", "acorn-globals": "^6.0.0", "xml-name-validator": "^3.0.0", "data-urls": "^2.0.0", "parse5": "6.0.1", "nwsapi": "^2.2.0", "acorn": "^8.2.4", "html-encoding-sniffer": "^2.0.1", "whatwg-mimetype": "^2.3.0", "cssstyle": "^2.3.0", "symbol-tree": "^3.2.4", "w3c-hr-time": "^1.0.2", "saxes": "^5.0.1", "webidl-conversions": "^6.1.0", "whatwg-encoding": "^1.0.5", "escodegen": "^2.0.0", "https-proxy-agent": "^5.0.0", "decimal.js": "^10.2.1", "tough-cookie": "^4.0.0", "w3c-xmlserializer": "^2.0.0", "whatwg-url": "^8.5.0", "domexception": "^2.0.1", "cssom": "^0.4.4", "http-proxy-agent": "^4.0.1", "ws": "^7.4.6", "abab": "^2.0.5", "form-data": "^3.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"canvas": "^2.5.0"}, "peerDependenciesMeta": {"canvas": {"optional": true}}}, "node_modules/utils-merge": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/mimic-fn": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/commondir": {"version": "1.0.1", "license": "MIT"}, "node_modules/serve-index/node_modules/depd": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/is-bigint": {"version": "1.1.0", "license": "MIT", "dependencies": {"has-bigints": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.entries": {"version": "1.1.9", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-object-atoms": "^1.1.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/@csstools/postcss-cascade-layers": {"version": "1.1.1", "license": "CC0-1.0", "dependencies": {"@csstools/selector-specificity": "^2.0.2", "postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@csstools/postcss-normalize-display-values": {"version": "1.0.1", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/eslint-plugin-import/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/loader-runner": {"version": "4.3.0", "license": "MIT", "engines": {"node": ">=6.11.5"}}, "node_modules/@typescript-eslint/parser": {"version": "5.62.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/scope-manager": "5.62.0", "@typescript-eslint/types": "5.62.0", "@typescript-eslint/typescript-estree": "5.62.0", "debug": "^4.3.4"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/postcss-unique-selectors": {"version": "5.1.1", "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.5"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/is-finalizationregistry": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/filelist/node_modules/brace-expansion": {"version": "2.0.1", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/at-least-node": {"version": "1.0.0", "license": "ISC", "engines": {"node": ">= 4.0.0"}}, "node_modules/@babel/preset-typescript": {"version": "7.26.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-validator-option": "^7.25.9", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/plugin-transform-modules-commonjs": "^7.25.9", "@babel/plugin-transform-typescript": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/filesize": {"version": "8.0.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">= 0.4.0"}}, "node_modules/fsevents": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.2.tgz", "integrity": "sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==", "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/@types/debug": {"version": "4.1.12", "resolved": "https://registry.npmjs.org/@types/debug/-/debug-4.1.12.tgz", "integrity": "sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==", "dev": true, "license": "MIT", "dependencies": {"@types/ms": "*"}}, "node_modules/postcss-image-set-function": {"version": "4.0.7", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/schema-utils/node_modules/ajv": {"version": "8.17.1", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/postcss-custom-media": {"version": "8.0.2", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.3"}}, "node_modules/body-parser/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/domhandler": {"version": "4.3.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.2.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "node_modules/available-typed-arrays": {"version": "1.0.7", "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-symbol": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-symbols": "^1.1.0", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/jest-watch-typeahead/node_modules/ansi-styles": {"version": "5.2.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@babel/plugin-transform-runtime/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/is-boolean-object": {"version": "1.2.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/data-view-byte-length": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/inspect-js"}}, "node_modules/babel-plugin-polyfill-regenerator": {"version": "0.6.4", "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.4"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/webpack-manifest-plugin/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/sucrase/node_modules/commander": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/colorette": {"version": "2.0.20", "license": "MIT"}, "node_modules/workbox-build/node_modules/webidl-conversions": {"version": "4.0.2", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/functions-have-names": {"version": "1.2.3", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/strip-comments": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/array-buffer-byte-length": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "is-array-buffer": "^3.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tsconfig-paths/node_modules/strip-bom": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/jest-watch-typeahead/node_modules/strip-ansi/node_modules/ansi-regex": {"version": "6.1.0", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/import-local": {"version": "3.2.0", "license": "MIT", "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/d3-zoom": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/d3-zoom/-/d3-zoom-3.0.0.tgz", "integrity": "sha512-b8AmV3kfQaqWAuacbPuNbL6vahnOJflOhexLzMMNLga62+/nh0JzvJ0aO/5a5MVgUFGS7Hu1P9P03o3fJkDCyw==", "license": "ISC", "dependencies": {"d3-dispatch": "1 - 3", "d3-drag": "2 - 3", "d3-interpolate": "1 - 3", "d3-selection": "2 - 3", "d3-transition": "2 - 3"}, "engines": {"node": ">=12"}}, "node_modules/@babel/helper-skip-transparent-expression-wrappers": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/lodash.debounce": {"version": "4.0.8", "license": "MIT"}, "node_modules/fork-ts-checker-webpack-plugin": {"version": "6.5.3", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.8.3", "@types/json-schema": "^7.0.5", "chalk": "^4.1.0", "chokidar": "^3.4.2", "cosmiconfig": "^6.0.0", "deepmerge": "^4.2.2", "fs-extra": "^9.0.0", "glob": "^7.1.6", "memfs": "^3.1.2", "minimatch": "^3.0.4", "schema-utils": "2.7.0", "semver": "^7.3.2", "tapable": "^1.0.0"}, "engines": {"node": ">=10", "yarn": ">=1.0.0"}, "peerDependencies": {"eslint": ">= 6", "typescript": ">= 2.7", "vue-template-compiler": "*", "webpack": ">= 4"}, "peerDependenciesMeta": {"eslint": {"optional": true}, "vue-template-compiler": {"optional": true}}}, "node_modules/balanced-match": {"version": "1.0.2", "license": "MIT"}, "node_modules/postcss-pseudo-class-any-link": {"version": "7.1.6", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/source-map-loader": {"version": "3.0.2", "license": "MIT", "dependencies": {"abab": "^2.0.5", "iconv-lite": "^0.6.3", "source-map-js": "^1.0.1"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/y18n": {"version": "5.0.8", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/@types/d3-chord": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/@types/d3-chord/-/d3-chord-3.0.6.tgz", "integrity": "sha512-LFYWWd8nwfwEmTZG9PfQxd17HbNPksHBiJHaKuY1XeqscXacsS2tyoo6OdRsjf+NQYeB6XrNL3a25E3gH69lcg==", "license": "MIT"}, "node_modules/axios": {"version": "1.8.4", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/@sinonjs/commons": {"version": "1.8.6", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"type-detect": "4.0.8"}}, "node_modules/jsonfile": {"version": "6.1.0", "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/arg": {"version": "5.0.2", "license": "MIT"}, "node_modules/external-editor": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/external-editor/-/external-editor-3.1.0.tgz", "integrity": "sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==", "dev": true, "license": "MIT", "dependencies": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}, "engines": {"node": ">=4"}}, "node_modules/@types/retry": {"version": "0.12.0", "license": "MIT"}, "node_modules/raw-body": {"version": "2.5.2", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/postcss-value-parser": {"version": "4.2.0", "license": "MIT"}, "node_modules/array-union": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@babel/eslint-parser/node_modules/eslint-visitor-keys": {"version": "2.1.0", "license": "Apache-2.0", "engines": {"node": ">=10"}}, "node_modules/@csstools/normalize.css": {"version": "12.1.1", "license": "CC0-1.0"}, "node_modules/batch": {"version": "0.6.1", "license": "MIT"}, "node_modules/delaunator": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/delaunator/-/delaunator-5.0.1.tgz", "integrity": "sha512-8nvh+XBe96aCESrGOqMp/84b13H9cdKbG5P2ejQCh4d4sK9RL4371qou9drQjMhvnPmhWl5hnmqbEE0fXr9Xnw==", "license": "ISC", "dependencies": {"robust-predicates": "^3.0.2"}}, "node_modules/cross-spawn": {"version": "7.0.6", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/raw-body/node_modules/iconv-lite": {"version": "0.4.24", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/signal-exit": {"version": "3.0.7", "license": "ISC"}, "node_modules/jest-watch-typeahead/node_modules/@jest/console": {"version": "28.1.3", "license": "MIT", "dependencies": {"@jest/types": "^28.1.3", "@types/node": "*", "chalk": "^4.0.0", "jest-message-util": "^28.1.3", "jest-util": "^28.1.3", "slash": "^3.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/d3": {"version": "7.9.0", "resolved": "https://registry.npmjs.org/d3/-/d3-7.9.0.tgz", "integrity": "sha512-e1U46jVP+w7Iut8Jt8ri1YsPOvFpg46k+K8TpCb0P+zjCkjkPnV7WzfDJzMHy1LnA+wj5pLT1wjO901gLXeEhA==", "license": "ISC", "dependencies": {"d3-axis": "3", "d3-zoom": "3", "d3-fetch": "3", "d3-random": "3", "d3-path": "3", "d3-time": "3", "d3-force": "3", "d3-delaunay": "6", "d3-chord": "3", "d3-brush": "3", "d3-hierarchy": "3", "d3-dsv": "3", "d3-selection": "3", "d3-drag": "3", "d3-transition": "3", "d3-time-format": "4", "d3-dispatch": "3", "d3-scale-chromatic": "3", "d3-ease": "3", "d3-format": "3", "d3-contour": "4", "d3-quadtree": "3", "d3-geo": "3", "d3-color": "3", "d3-timer": "3", "d3-array": "3", "d3-shape": "3", "d3-polygon": "3", "d3-scale": "4", "d3-interpolate": "3"}, "engines": {"node": ">=12"}}, "node_modules/array.prototype.findlastindex": {"version": "1.2.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-shim-unscopables": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/json-schema-traverse": {"version": "0.4.1", "license": "MIT"}, "node_modules/react-refresh": {"version": "0.11.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/@babel/types": {"version": "7.26.10", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@types/geojson": {"version": "7946.0.16", "resolved": "https://registry.npmjs.org/@types/geojson/-/geojson-7946.0.16.tgz", "integrity": "sha512-6C8nqWur3j98U6+lXDfTUWIfgvZU+EumvpHKcYjujKH7woYyLj2sUmff0tRhrqM7BohUw7Pz3ZB1jj2gW9Fvmg==", "license": "MIT"}, "node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/static-eval/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/@babel/template": {"version": "7.26.9", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.26.2", "@babel/parser": "^7.26.9", "@babel/types": "^7.26.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.26.8", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/string.prototype.repeat": {"version": "1.0.0", "license": "MIT", "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.17.5"}}, "node_modules/read-cache": {"version": "1.0.0", "license": "MIT", "dependencies": {"pify": "^2.3.0"}}, "node_modules/is-regex": {"version": "1.2.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/dedent": {"version": "0.7.0", "license": "MIT"}, "node_modules/@babel/plugin-transform-arrow-functions": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@remix-run/router": {"version": "1.23.0", "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/jest-leak-detector": {"version": "27.5.1", "license": "MIT", "dependencies": {"jest-get-type": "^27.5.1", "pretty-format": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/is-generator-function": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/renderkid": {"version": "3.0.0", "license": "MIT", "dependencies": {"css-select": "^4.1.3", "dom-converter": "^0.2.0", "htmlparser2": "^6.1.0", "lodash": "^4.17.21", "strip-ansi": "^6.0.1"}}, "node_modules/@babel/plugin-transform-computed-properties": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/template": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/eslint-import-resolver-node": {"version": "0.3.9", "license": "MIT", "dependencies": {"debug": "^3.2.7", "is-core-module": "^2.13.0", "resolve": "^1.22.4"}}, "node_modules/find-up": {"version": "4.1.0", "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/hpack.js/node_modules/string_decoder": {"version": "1.1.1", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/rollup-plugin-terser/node_modules/serialize-javascript": {"version": "4.0.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"randombytes": "^2.1.0"}}, "node_modules/compression/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/is-callable": {"version": "1.2.7", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/coa/node_modules/escape-string-regexp": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/camelcase-css": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/nanoid": {"version": "3.3.11", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/deepmerge": {"version": "4.3.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/he": {"version": "1.2.0", "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/tailwindcss": {"version": "3.4.17", "license": "MIT", "dependencies": {"is-glob": "^4.0.3", "resolve": "^1.22.8", "arg": "^5.0.2", "@alloc/quick-lru": "^5.2.0", "jiti": "^1.21.6", "lilconfig": "^3.1.3", "normalize-path": "^3.0.0", "postcss": "^8.4.47", "postcss-import": "^15.1.0", "object-hash": "^3.0.0", "chokidar": "^3.6.0", "picocolors": "^1.1.1", "sucrase": "^3.35.0", "dlv": "^1.1.3", "postcss-load-config": "^4.0.2", "glob-parent": "^6.0.2", "fast-glob": "^3.3.2", "postcss-selector-parser": "^6.1.2", "postcss-js": "^4.0.1", "didyoumean": "^1.2.2", "postcss-nested": "^6.2.0", "micromatch": "^4.0.8"}, "bin": {"tailwind": "lib/cli.js", "tailwindcss": "lib/cli.js"}, "engines": {"node": ">=14.0.0"}}, "node_modules/jest-validate": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "camelcase": "^6.2.0", "chalk": "^4.0.0", "jest-get-type": "^27.5.1", "leven": "^3.1.0", "pretty-format": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/wcwidth": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz", "integrity": "sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==", "dev": true, "license": "MIT", "dependencies": {"defaults": "^1.0.3"}}, "node_modules/sucrase/node_modules/minimatch": {"version": "9.0.5", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/lodash.merge": {"version": "4.6.2", "license": "MIT"}, "node_modules/coa/node_modules/color-convert": {"version": "1.9.3", "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/html-escaper": {"version": "2.0.2", "license": "MIT"}, "node_modules/css-blank-pseudo": {"version": "3.0.3", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.9"}, "bin": {"css-blank-pseudo": "dist/cli.cjs"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/d3-geo": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/d3-geo/-/d3-geo-3.1.1.tgz", "integrity": "sha512-637ln3gXKXOwhalDzinUgY83KzNWZRKbYubaG+fGVuc/dxO64RRljtCTnf5ecMyE1RIdtqpkVcq0IbtU2S8j2Q==", "license": "ISC", "dependencies": {"d3-array": "2.5.0 - 3"}, "engines": {"node": ">=12"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/@trysound/sax": {"version": "0.2.0", "license": "ISC", "engines": {"node": ">=10.13.0"}}, "node_modules/error-stack-parser": {"version": "2.1.4", "license": "MIT", "dependencies": {"stackframe": "^1.3.4"}}, "node_modules/@babel/plugin-transform-function-name": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/traverse": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@types/stack-utils": {"version": "2.0.3", "license": "MIT"}, "node_modules/resolve-url-loader/node_modules/picocolors": {"version": "0.2.1", "license": "ISC"}, "node_modules/postcss-modules-extract-imports": {"version": "3.1.0", "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/react-dev-utils/node_modules/p-limit": {"version": "3.1.0", "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/babel-plugin-polyfill-corejs2": {"version": "0.4.13", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.22.6", "@babel/helper-define-polyfill-provider": "^0.6.4", "semver": "^6.3.1"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/@mui/styled-engine": {"version": "5.16.14", "resolved": "https://registry.npmjs.org/@mui/styled-engine/-/styled-engine-5.16.14.tgz", "integrity": "sha512-UAiMPZABZ7p8mUW4akDV6O7N3+4DatStpXMZwPlt+H/dA0lt67qawN021MNND+4QTpjaiMYxbhKZeQcyWCbuKw==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.23.9", "@emotion/cache": "^11.13.5", "csstype": "^3.1.3", "prop-types": "^15.8.1"}, "engines": {"node": ">=12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@emotion/react": "^11.4.1", "@emotion/styled": "^11.3.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}}}, "node_modules/is-module": {"version": "1.0.0", "license": "MIT"}, "node_modules/pirates": {"version": "4.0.6", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/side-channel": {"version": "1.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/sisteransi": {"version": "1.0.5", "license": "MIT"}, "node_modules/schema-utils/node_modules/json-schema-traverse": {"version": "1.0.0", "license": "MIT"}, "node_modules/cssstyle": {"version": "2.3.0", "license": "MIT", "dependencies": {"cssom": "~0.3.6"}, "engines": {"node": ">=8"}}, "node_modules/@babel/plugin-transform-unicode-property-regex": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/electron-to-chromium": {"version": "1.5.123", "license": "ISC"}, "node_modules/@babel/parser": {"version": "7.26.10", "license": "MIT", "dependencies": {"@babel/types": "^7.26.10"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "license": "MIT"}, "node_modules/@babel/helper-replace-supers": {"version": "7.26.5", "license": "MIT", "dependencies": {"@babel/helper-member-expression-to-functions": "^7.25.9", "@babel/helper-optimise-call-expression": "^7.25.9", "@babel/traverse": "^7.26.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/dns-packet": {"version": "5.6.1", "license": "MIT", "dependencies": {"@leichtgewicht/ip-codec": "^2.0.1"}, "engines": {"node": ">=6"}}, "node_modules/babel-plugin-polyfill-corejs2/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/playwright-core": {"version": "1.51.1", "resolved": "https://registry.npmjs.org/playwright-core/-/playwright-core-1.51.1.tgz", "integrity": "sha512-/crRMj8+j/Nq5s8QcvegseuyeZPxpQCZb6HNk3Sos3BlZyAknRjoyJPFWkpNn8v0+P3WiwqFF8P+zQo4eqiNuw==", "dev": true, "license": "Apache-2.0", "bin": {"playwright-core": "cli.js"}, "engines": {"node": ">=18"}}, "node_modules/global-prefix/node_modules/which": {"version": "1.3.1", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/sucrase/node_modules/brace-expansion": {"version": "2.0.1", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/jest-cli": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/core": "^27.5.1", "@jest/test-result": "^27.5.1", "@jest/types": "^27.5.1", "chalk": "^4.0.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "import-local": "^3.0.2", "jest-config": "^27.5.1", "jest-util": "^27.5.1", "jest-validate": "^27.5.1", "prompts": "^2.0.1", "yargs": "^16.2.0"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/@pmmmwh/react-refresh-webpack-plugin": {"version": "0.5.15", "license": "MIT", "dependencies": {"ansi-html": "^0.0.9", "core-js-pure": "^3.23.3", "error-stack-parser": "^2.0.6", "html-entities": "^2.1.0", "loader-utils": "^2.0.4", "schema-utils": "^4.2.0", "source-map": "^0.7.3"}, "engines": {"node": ">= 10.13"}, "peerDependencies": {"@types/webpack": "4.x || 5.x", "react-refresh": ">=0.10.0 <1.0.0", "sockjs-client": "^1.4.0", "type-fest": ">=0.17.0 <5.0.0", "webpack": ">=4.43.0 <6.0.0", "webpack-dev-server": "3.x || 4.x || 5.x", "webpack-hot-middleware": "2.x", "webpack-plugin-serve": "0.x || 1.x"}, "peerDependenciesMeta": {"@types/webpack": {"optional": true}, "sockjs-client": {"optional": true}, "type-fest": {"optional": true}, "webpack-dev-server": {"optional": true}, "webpack-hot-middleware": {"optional": true}, "webpack-plugin-serve": {"optional": true}}}, "node_modules/sass-loader": {"version": "12.6.0", "license": "MIT", "dependencies": {"klona": "^2.0.4", "neo-async": "^2.6.2"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"fibers": ">= 3.1.0", "node-sass": "^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0", "sass": "^1.3.0", "sass-embedded": "*", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"fibers": {"optional": true}, "node-sass": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}}}, "node_modules/builtin-modules": {"version": "3.3.0", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/estraverse": {"version": "5.3.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@babel/plugin-transform-parameters": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/fastq": {"version": "1.19.1", "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/@babel/plugin-transform-private-methods": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/jest-watch-typeahead/node_modules/jest-message-util/node_modules/slash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-watch-typeahead/node_modules/jest-message-util": {"version": "28.1.3", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "^28.1.3", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^28.1.3", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/@humanwhocodes/config-array": {"version": "0.13.0", "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^2.0.3", "debug": "^4.3.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@typescript-eslint/utils/node_modules/estraverse": {"version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/@mui/material": {"version": "5.13.0", "resolved": "https://registry.npmjs.org/@mui/material/-/material-5.13.0.tgz", "integrity": "sha512-ckS+9tCpAzpdJdaTF+btF0b6mF9wbXg/EVKtnoAWYi0UKXoXBAVvEUMNpLGA5xdpCdf+A6fPbVUEHs9TsfU+Yw==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.21.0", "@mui/base": "5.0.0-beta.0", "@mui/core-downloads-tracker": "^5.13.0", "@mui/system": "^5.12.3", "@mui/types": "^7.2.4", "@mui/utils": "^5.12.3", "@types/react-transition-group": "^4.4.6", "clsx": "^1.2.1", "csstype": "^3.1.2", "prop-types": "^15.8.1", "react-is": "^18.2.0", "react-transition-group": "^4.4.5"}, "engines": {"node": ">=12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui"}, "peerDependencies": {"@emotion/react": "^11.5.0", "@emotion/styled": "^11.3.0", "@types/react": "^17.0.0 || ^18.0.0", "react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}, "@types/react": {"optional": true}}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "5.62.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "5.62.0", "@typescript-eslint/visitor-keys": "5.62.0", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/prop-types/node_modules/react-is": {"version": "16.13.1", "license": "MIT"}, "node_modules/@babel/plugin-transform-new-target": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/destroy": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/is-array-buffer": {"version": "3.0.5", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@babel/plugin-proposal-private-property-in-object": {"version": "7.21.0-placeholder-for-preset-env.2", "license": "MIT", "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/d3-time-format": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/d3-time-format/-/d3-time-format-4.1.0.tgz", "integrity": "sha512-dJxPBlzC7NugB2PDLwo9Q8JiTR3M3e4/XANkreKSUxF8vvXKqm1Yfq4Q5dl8budlunRVlUUaDUgFt7eA8D6NLg==", "license": "ISC", "dependencies": {"d3-time": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/stringify-object": {"version": "3.3.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"get-own-enumerable-property-symbols": "^3.0.0", "is-obj": "^1.0.1", "is-regexp": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/babel-jest": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/transform": "^27.5.1", "@jest/types": "^27.5.1", "@types/babel__core": "^7.1.14", "babel-plugin-istanbul": "^6.1.1", "babel-preset-jest": "^27.5.1", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "slash": "^3.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "peerDependencies": {"@babel/core": "^7.8.0"}}, "node_modules/domexception/node_modules/webidl-conversions": {"version": "5.0.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/min-indent": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/workbox-build/node_modules/tr46": {"version": "1.0.1", "license": "MIT", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/resolve-cwd": {"version": "3.0.0", "license": "MIT", "dependencies": {"resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/regjsparser": {"version": "0.12.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"jsesc": "~3.0.2"}, "bin": {"regjsparser": "bin/parser"}}, "node_modules/debug": {"version": "4.4.0", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/jest-docblock": {"version": "27.5.1", "license": "MIT", "dependencies": {"detect-newline": "^3.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/istanbul-lib-instrument": {"version": "5.2.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0", "semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/@emotion/memoize": {"version": "0.9.0", "resolved": "https://registry.npmjs.org/@emotion/memoize/-/memoize-0.9.0.tgz", "integrity": "sha512-30FAj7/EoJ5mwVPOWhAyCX+FPfMDrVecJAM+Iw9NRoSl4BBAQeqj4cApHHUXOVvIPgLVDsCFoz/hGD+5QQD1GQ==", "license": "MIT"}, "node_modules/node-fetch/node_modules/tr46": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "integrity": "sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==", "dev": true, "license": "MIT"}, "node_modules/svgo/node_modules/css-what": {"version": "3.4.2", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">= 6"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/msw/node_modules/cliui": {"version": "8.0.1", "resolved": "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz", "integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/strip-indent": {"version": "3.0.0", "license": "MIT", "dependencies": {"min-indent": "^1.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@babel/code-frame": {"version": "7.26.2", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.25.9", "js-tokens": "^4.0.0", "picocolors": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@types/d3-geo": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/@types/d3-geo/-/d3-geo-3.1.0.tgz", "integrity": "sha512-856sckF0oP/diXtS4jNsiQw/UuK5fQG8l/a9VVLeSouf1/PPbBE1i1W852zVwKwYCBkFJJB7nCFTbk6UMEXBOQ==", "license": "MIT", "dependencies": {"@types/geojson": "*"}}, "node_modules/file-loader/node_modules/schema-utils": {"version": "3.3.0", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/@types/estree": {"version": "1.0.6", "license": "MIT"}, "node_modules/tempy/node_modules/type-fest": {"version": "0.16.0", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/selfsigned": {"version": "2.4.1", "license": "MIT", "dependencies": {"@types/node-forge": "^1.3.0", "node-forge": "^1"}, "engines": {"node": ">=10"}}, "node_modules/is-weakmap": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@rollup/plugin-node-resolve": {"version": "11.2.1", "license": "MIT", "dependencies": {"@rollup/pluginutils": "^3.1.0", "@types/resolve": "1.17.1", "builtin-modules": "^3.1.0", "deepmerge": "^4.2.2", "is-module": "^1.0.0", "resolve": "^1.19.0"}, "engines": {"node": ">= 10.0.0"}, "peerDependencies": {"rollup": "^1.20.0||^2.0.0"}}, "node_modules/@mui/types": {"version": "7.2.24", "resolved": "https://registry.npmjs.org/@mui/types/-/types-7.2.24.tgz", "integrity": "sha512-3c8tRt/CbWZ+pEg7QpSwbdxOk36EfmhbKf6AGZsD1EcLDLTSZoxxJ86FVtcjxvjuhdyBiWKSTGZFaXCnidO2kw==", "license": "MIT", "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/commander": {"version": "8.3.0", "license": "MIT", "engines": {"node": ">= 12"}}, "node_modules/postcss-dir-pseudo-class": {"version": "6.0.5", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/call-bound": {"version": "1.0.4", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@types/d3-ease": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/@types/d3-ease/-/d3-ease-3.0.2.tgz", "integrity": "sha512-NcV1JjO5oDzoK26oMzbILE6HW7uVXOHLQvHshBUW4UMdZGfiY6v5BeQwh9a9tCzv+CeefZQHJt5SRgK154RtiA==", "license": "MIT"}, "node_modules/pkg-up/node_modules/find-up": {"version": "3.0.0", "license": "MIT", "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/@babel/plugin-transform-typeof-symbol": {"version": "7.26.7", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.26.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/dlv": {"version": "1.1.3", "license": "MIT"}, "node_modules/send/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/pify": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/@popperjs/core": {"version": "2.11.8", "resolved": "https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz", "integrity": "sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}}, "node_modules/@jest/environment": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/fake-timers": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "jest-mock": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@babel/plugin-transform-block-scoped-functions": {"version": "7.26.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.26.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/helper-define-polyfill-provider": {"version": "0.6.4", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.22.6", "@babel/helper-plugin-utils": "^7.22.5", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/webpack/node_modules/eslint-scope": {"version": "5.1.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/compressible": {"version": "2.0.18", "license": "MIT", "dependencies": {"mime-db": ">= 1.43.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/psl": {"version": "1.15.0", "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "funding": {"url": "https://github.com/sponsors/lupomontero"}}, "node_modules/p-try": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@babel/plugin-syntax-bigint": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-object-rest-spread": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/plugin-transform-parameters": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/jest-watch-typeahead/node_modules/@jest/test-result": {"version": "28.1.3", "license": "MIT", "dependencies": {"@jest/console": "^28.1.3", "@jest/types": "^28.1.3", "@types/istanbul-lib-coverage": "^2.0.0", "collect-v8-coverage": "^1.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/proxy-addr/node_modules/ipaddr.js": {"version": "1.9.1", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/watchpack": {"version": "2.4.2", "license": "MIT", "dependencies": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}, "engines": {"node": ">=10.13.0"}}, "node_modules/eslint-plugin-react/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-annotate-as-pure": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/tough-cookie/node_modules/universalify": {"version": "0.2.0", "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/emojis-list": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/locate-path": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/sourcemap-codec": {"version": "1.4.8", "license": "MIT"}, "node_modules/postcss-browser-comments": {"version": "4.0.0", "license": "CC0-1.0", "engines": {"node": ">=8"}, "peerDependencies": {"browserslist": ">=4", "postcss": ">=8"}}, "node_modules/jest-watch-typeahead/node_modules/pretty-format": {"version": "28.1.3", "license": "MIT", "dependencies": {"@jest/schemas": "^28.1.3", "ansi-regex": "^5.0.1", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/msw/node_modules/yargs-parser": {"version": "21.1.1", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/mkdirp": {"version": "0.5.6", "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/svgo": {"version": "1.3.2", "license": "MIT", "dependencies": {"chalk": "^2.4.1", "coa": "^2.0.2", "css-select": "^2.0.0", "css-select-base-adapter": "^0.1.1", "css-tree": "1.0.0-alpha.37", "csso": "^4.0.2", "js-yaml": "^3.13.1", "mkdirp": "~0.5.1", "object.values": "^1.1.0", "sax": "~1.2.4", "stable": "^0.1.8", "unquote": "~1.1.1", "util.promisify": "~1.0.0"}, "bin": {"svgo": "bin/svgo"}, "engines": {"node": ">=4.0.0"}}, "node_modules/@babel/plugin-transform-logical-assignment-operators": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/select-hose": {"version": "2.0.0", "license": "MIT"}, "node_modules/node-int64": {"version": "0.4.0", "license": "MIT"}, "node_modules/@babel/plugin-transform-destructuring": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/parseurl": {"version": "1.3.3", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/parent-module": {"version": "1.0.1", "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/object-is": {"version": "1.1.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@babel/plugin-transform-literals": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/static-eval/node_modules/levn": {"version": "0.3.0", "license": "MIT", "dependencies": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/util.promisify": {"version": "1.0.1", "license": "MIT", "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.17.2", "has-symbols": "^1.0.1", "object.getownpropertydescriptors": "^2.1.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@types/bonjour": {"version": "3.5.13", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/merge-stream": {"version": "2.0.0", "license": "MIT"}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@isaacs/cliui/node_modules/ansi-regex": {"version": "6.1.0", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/@istanbuljs/schema": {"version": "0.1.3", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/postcss-merge-rules": {"version": "5.1.4", "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "caniuse-api": "^3.0.0", "cssnano-utils": "^3.1.0", "postcss-selector-parser": "^6.0.5"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/hpack.js/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/@xtuc/ieee754": {"version": "1.2.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/is-map": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/svgo/node_modules/escape-string-regexp": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/detect-port-alt": {"version": "1.1.6", "license": "MIT", "dependencies": {"address": "^1.0.1", "debug": "^2.6.0"}, "bin": {"detect": "bin/detect-port", "detect-port": "bin/detect-port"}, "engines": {"node": ">= 4.2.1"}}, "node_modules/hpack.js/node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/@types/d3-dsv": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/@types/d3-dsv/-/d3-dsv-3.0.7.tgz", "integrity": "sha512-n6QBF9/+XASqcKK6waudgL0pf/S5XHPPI8APyMLLUHd8NqouBGLsU8MgtO7NINGtPBtk9Kko/W4ea0oAspwh9g==", "license": "MIT"}, "node_modules/babel-preset-react-app": {"version": "10.1.0", "license": "MIT", "dependencies": {"@babel/plugin-transform-flow-strip-types": "^7.16.0", "@babel/core": "^7.16.0", "@babel/plugin-proposal-private-property-in-object": "^7.16.7", "@babel/preset-typescript": "^7.16.0", "babel-plugin-macros": "^3.1.0", "@babel/runtime": "^7.16.3", "@babel/plugin-transform-runtime": "^7.16.4", "@babel/plugin-proposal-class-properties": "^7.16.0", "@babel/preset-react": "^7.16.0", "@babel/preset-env": "^7.16.4", "@babel/plugin-transform-react-display-name": "^7.16.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.0", "@babel/plugin-proposal-optional-chaining": "^7.16.0", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "@babel/plugin-proposal-decorators": "^7.16.4", "@babel/plugin-proposal-private-methods": "^7.16.0", "@babel/plugin-proposal-numeric-separator": "^7.16.0"}}, "node_modules/postcss-calc": {"version": "8.2.4", "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.9", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.2.2"}}, "node_modules/loader-utils": {"version": "2.0.4", "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}, "engines": {"node": ">=8.9.0"}}, "node_modules/@types/express": {"version": "4.17.21", "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}}, "node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/upath": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=4", "yarn": "*"}}, "node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/ansi-html-community": {"version": "0.0.8", "engines": ["node >= 0.8.0"], "license": "Apache-2.0", "bin": {"ansi-html": "bin/ansi-html"}}, "node_modules/globalthis": {"version": "1.0.4", "license": "MIT", "dependencies": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.26.0", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/traverse": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/dom-accessibility-api": {"version": "0.5.16", "license": "MIT"}, "node_modules/find-cache-dir": {"version": "3.3.2", "license": "MIT", "dependencies": {"commondir": "^1.0.1", "make-dir": "^3.0.2", "pkg-dir": "^4.1.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/avajs/find-cache-dir?sponsor=1"}}, "node_modules/saxes": {"version": "5.0.1", "license": "ISC", "dependencies": {"xmlchars": "^2.2.0"}, "engines": {"node": ">=10"}}, "node_modules/react-dev-utils/node_modules/loader-utils": {"version": "3.3.1", "license": "MIT", "engines": {"node": ">= 12.13.0"}}, "node_modules/workbox-routing": {"version": "6.6.0", "license": "MIT", "dependencies": {"workbox-core": "6.6.0"}}, "node_modules/serve-static": {"version": "1.16.2", "license": "MIT", "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/@emotion/sheet": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/@emotion/sheet/-/sheet-1.4.0.tgz", "integrity": "sha512-fTBW9/8r2w3dXWYM4HCB1Rdp8NLibOw2+XELH5m5+AkWiL/KqYX6dc0kKYlaYyKjrQ6ds33MCdMPEwgs2z1rqg==", "license": "MIT"}, "node_modules/resolve.exports": {"version": "1.1.1", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/@babel/plugin-transform-runtime": {"version": "7.26.10", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.25.9", "@babel/helper-plugin-utils": "^7.26.5", "babel-plugin-polyfill-corejs2": "^0.4.10", "babel-plugin-polyfill-corejs3": "^0.11.0", "babel-plugin-polyfill-regenerator": "^0.6.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/sockjs": {"version": "0.3.24", "license": "MIT", "dependencies": {"faye-websocket": "^0.11.3", "uuid": "^8.3.2", "websocket-driver": "^0.7.4"}}, "node_modules/@babel/plugin-transform-export-namespace-from": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/workbox-window": {"version": "6.6.0", "license": "MIT", "dependencies": {"@types/trusted-types": "^2.0.2", "workbox-core": "6.6.0"}}, "node_modules/babel-plugin-istanbul": {"version": "6.1.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/npm-run-path": {"version": "4.0.1", "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/whatwg-encoding": {"version": "1.0.5", "license": "MIT", "dependencies": {"iconv-lite": "0.4.24"}}, "node_modules/default-gateway": {"version": "6.0.3", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"execa": "^5.0.0"}, "engines": {"node": ">= 10"}}, "node_modules/stylis": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/stylis/-/stylis-4.2.0.tgz", "integrity": "sha512-Orov6g6BB1sDfYgzWfTHDOxamtX1bE/zo104Dh9e6fqJ3PooipYyfJ0pUmrZO2wAvO8YbEyeFrkV91XTsGMSrw==", "license": "MIT"}, "node_modules/@types/http-errors": {"version": "2.0.4", "license": "MIT"}, "node_modules/methods": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/@csstools/postcss-unset-value": {"version": "1.0.2", "license": "CC0-1.0", "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/which-typed-array": {"version": "1.1.19", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@isaacs/cliui/node_modules/string-width": {"version": "5.1.2", "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/foreground-child/node_modules/signal-exit": {"version": "4.1.0", "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/postcss-ordered-values": {"version": "5.1.3", "license": "MIT", "dependencies": {"cssnano-utils": "^3.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/object.fromentries": {"version": "2.0.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/stable": {"version": "0.1.8", "license": "MIT"}, "node_modules/gopd": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minimalistic-assert": {"version": "1.0.1", "license": "ISC"}, "node_modules/serve-index/node_modules/http-errors": {"version": "1.6.3", "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/postcss-minify-font-values": {"version": "5.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/@eslint/eslintrc/node_modules/argparse": {"version": "2.0.1", "license": "Python-2.0"}, "node_modules/svgo/node_modules/chalk": {"version": "2.4.2", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/is-binary-path": {"version": "2.1.0", "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@rollup/plugin-babel": {"version": "5.3.1", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.10.4", "@rollup/pluginutils": "^3.1.0"}, "engines": {"node": ">= 10.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0", "@types/babel__core": "^7.1.9", "rollup": "^1.20.0||^2.0.0"}, "peerDependenciesMeta": {"@types/babel__core": {"optional": true}}}, "node_modules/detect-port-alt/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/axe-core": {"version": "4.10.3", "license": "MPL-2.0", "engines": {"node": ">=4"}}, "node_modules/@babel/core/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/eastasianwidth": {"version": "0.2.0", "license": "MIT"}, "node_modules/@types/d3-dispatch": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/@types/d3-dispatch/-/d3-dispatch-3.0.6.tgz", "integrity": "sha512-4fvZhzMeeuBJYZXRXrRIQnvUYfyXwYmLsdiN7XXmVNQKKw1cM8a5WdID0g1hVFZDqT9ZqZEY5pD44p24VS7iZQ==", "license": "MIT"}, "node_modules/postcss-colormin": {"version": "5.3.1", "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "caniuse-api": "^3.0.0", "colord": "^2.9.1", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/word-wrap": {"version": "1.2.5", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/workbox-strategies": {"version": "6.6.0", "license": "MIT", "dependencies": {"workbox-core": "6.6.0"}}, "node_modules/serve-index": {"version": "1.9.1", "license": "MIT", "dependencies": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/@types/d3-scale": {"version": "4.0.9", "resolved": "https://registry.npmjs.org/@types/d3-scale/-/d3-scale-4.0.9.tgz", "integrity": "sha512-dLmtwB8zkAeO/juAMfnV+sItKjlsw2lKdZVVy6LRr0cBmegxSABiLEpGVmSJJ8O08i4+sGR6qQtb6WtuwJdvVw==", "license": "MIT", "dependencies": {"@types/d3-time": "*"}}, "node_modules/uuid": {"version": "8.3.2", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/p-limit": {"version": "2.3.0", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@humanwhocodes/object-schema": {"version": "2.0.3", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "node_modules/@webassemblyjs/wasm-opt": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1"}}, "node_modules/acorn-walk": {"version": "7.2.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/mute-stream": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.8.tgz", "integrity": "sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==", "dev": true, "license": "ISC"}, "node_modules/@webassemblyjs/helper-numbers": {"version": "1.13.2", "license": "MIT", "dependencies": {"@webassemblyjs/floating-point-hex-parser": "1.13.2", "@webassemblyjs/helper-api-error": "1.13.2", "@xtuc/long": "4.2.2"}}, "node_modules/is-async-function": {"version": "2.1.1", "license": "MIT", "dependencies": {"async-function": "^1.0.0", "call-bound": "^1.0.3", "get-proto": "^1.0.1", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/node-releases": {"version": "2.0.19", "license": "MIT"}, "node_modules/@emotion/babel-plugin/node_modules/convert-source-map": {"version": "1.9.0", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz", "integrity": "sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==", "license": "MIT"}, "node_modules/pretty-format/node_modules/ansi-styles": {"version": "5.2.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/postcss-discard-overridden": {"version": "5.1.0", "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/is-weakref": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/wbuf": {"version": "1.7.3", "license": "MIT", "dependencies": {"minimalistic-assert": "^1.0.0"}}, "node_modules/esquery": {"version": "1.6.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@types/d3-time-format": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/@types/d3-time-format/-/d3-time-format-4.0.3.tgz", "integrity": "sha512-5xg9rC+wWL8kdDj153qZcsJ0FWiFt0J5RB6LYUNZjwSnesfblqrI/bJ1wBdJ8OQfncgbJG5+2F+qfqnqyzYxyg==", "license": "MIT"}, "node_modules/ini": {"version": "1.3.8", "license": "ISC"}, "node_modules/dunder-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/typescript": {"version": "4.9.5", "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=4.2.0"}}, "node_modules/react-dom": {"version": "18.3.1", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0", "scheduler": "^0.23.2"}, "peerDependencies": {"react": "^18.3.1"}}, "node_modules/schema-utils": {"version": "4.3.0", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/yargs": {"version": "16.2.0", "license": "MIT", "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "engines": {"node": ">=10"}}, "node_modules/css-has-pseudo": {"version": "3.0.4", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.9"}, "bin": {"css-has-pseudo": "dist/cli.cjs"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/possible-typed-array-names": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/@babel/plugin-transform-modules-commonjs": {"version": "7.26.3", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.26.0", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/range-parser": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/for-each": {"version": "0.3.5", "license": "MIT", "dependencies": {"is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/react": {"version": "18.3.1", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/d3-force": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/d3-force/-/d3-force-3.0.0.tgz", "integrity": "sha512-zxV/SsA+U4yte8051P4ECydjD/S+qeYtnaIyAs9tgHCqfguma/aAQDjo85A9Z6EKhBirHRJHXIgJUlffT4wdLg==", "license": "ISC", "dependencies": {"d3-dispatch": "1 - 3", "d3-quadtree": "1 - 3", "d3-timer": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/web-encoding": {"version": "1.1.5", "resolved": "https://registry.npmjs.org/web-encoding/-/web-encoding-1.1.5.tgz", "integrity": "sha512-HYLeVCdJ0+lBYV2FvNZmv3HJ2Nt0QYXqZojk3d9FJOLkwnuhzM9tmamh8d7HPM8QqjKH8DeHkFTx+CFlWpZZDA==", "dev": true, "license": "MIT", "dependencies": {"util": "^0.12.3"}, "optionalDependencies": {"@zxing/text-encoding": "0.9.0"}}, "node_modules/@types/babel__generator": {"version": "7.6.8", "license": "MIT", "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/cookie": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/@types/cookie/-/cookie-0.4.1.tgz", "integrity": "sha512-XW/Aa8APYr6jSVVA1y/DEIZX0/GMKLEVekNG727R8cs56ahETkRAy/3DR7+fJyh7oUgGwNQaRfXCun0+KbWY7Q==", "dev": true, "license": "MIT"}, "node_modules/@babel/plugin-bugfix-safari-class-field-initializer-scope": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-typescript": {"version": "7.26.8", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.26.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9", "@babel/plugin-syntax-typescript": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@types/express-serve-static-core": {"version": "5.0.6", "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/react-dev-utils/node_modules/p-locate": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@svgr/hast-util-to-babel-ast": {"version": "5.5.0", "license": "MIT", "dependencies": {"@babel/types": "^7.12.6"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/fresh": {"version": "0.5.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/workbox-sw": {"version": "6.6.0", "license": "MIT"}, "node_modules/workbox-build/node_modules/@apideck/better-ajv-errors": {"version": "0.3.6", "license": "MIT", "dependencies": {"json-schema": "^0.4.0", "jsonpointer": "^5.0.0", "leven": "^3.1.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"ajv": ">=8"}}, "node_modules/es-define-property": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.26.5", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.26.5", "@babel/helper-validator-option": "^7.25.9", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/string.prototype.matchall": {"version": "4.0.12", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-abstract": "^1.23.6", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.6", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "internal-slot": "^1.1.0", "regexp.prototype.flags": "^1.5.3", "set-function-name": "^2.0.2", "side-channel": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@types/serve-static": {"version": "1.15.7", "license": "MIT", "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "node_modules/strip-bom": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ajv": {"version": "6.12.6", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/postcss-normalize-whitespace": {"version": "5.1.1", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/jest-matcher-utils": {"version": "27.5.1", "license": "MIT", "dependencies": {"chalk": "^4.0.0", "jest-diff": "^27.5.1", "jest-get-type": "^27.5.1", "pretty-format": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/whatwg-mimetype": {"version": "2.3.0", "license": "MIT"}, "node_modules/tmp": {"version": "0.0.33", "resolved": "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz", "integrity": "sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==", "dev": true, "license": "MIT", "dependencies": {"os-tmpdir": "~1.0.2"}, "engines": {"node": ">=0.6.0"}}, "node_modules/@istanbuljs/load-nyc-config": {"version": "1.1.0", "license": "ISC", "dependencies": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@types/d3-quadtree": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/@types/d3-quadtree/-/d3-quadtree-3.0.6.tgz", "integrity": "sha512-oUzyO1/Zm6rsxKRHA1vH0NEDG58HrT5icx/azi9MF1TWdtttWl0UIUsjEQBBh+SIkrpd21ZjEv7ptxWys1ncsg==", "license": "MIT"}, "node_modules/@svgr/babel-plugin-svg-dynamic-title": {"version": "5.4.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/jest-util": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@surma/rollup-plugin-off-main-thread": {"version": "2.2.3", "license": "Apache-2.0", "dependencies": {"ejs": "^3.1.6", "json5": "^2.2.0", "magic-string": "^0.25.0", "string.prototype.matchall": "^4.0.6"}}, "node_modules/chrome-trace-event": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">=6.0"}}, "node_modules/eslint-scope": {"version": "7.2.2", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@bcoe/v8-coverage": {"version": "0.2.3", "license": "MIT"}, "node_modules/toidentifier": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/@types/q": {"version": "1.5.8", "license": "MIT"}, "node_modules/levn": {"version": "0.4.1", "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/glob-parent": {"version": "6.0.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/@babel/preset-react": {"version": "7.26.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-validator-option": "^7.25.9", "@babel/plugin-transform-react-display-name": "^7.25.9", "@babel/plugin-transform-react-jsx": "^7.25.9", "@babel/plugin-transform-react-jsx-development": "^7.25.9", "@babel/plugin-transform-react-pure-annotations": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/require-directory": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/postcss-env-function": {"version": "4.0.6", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9", "@babel/plugin-transform-optional-chaining": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.13.0"}}, "node_modules/@babel/plugin-syntax-import-assertions": {"version": "7.26.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/json-schema": {"version": "0.4.0", "license": "(AFL-2.1 OR BSD-3-Clause)"}, "node_modules/body-parser": {"version": "1.20.3", "license": "MIT", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.5", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.13.0", "raw-body": "2.5.2", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/coa/node_modules/chalk": {"version": "2.4.2", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/jest-watch-typeahead/node_modules/jest-watcher/node_modules/string-length": {"version": "4.0.2", "license": "MIT", "dependencies": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@emotion/is-prop-valid": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/@emotion/is-prop-valid/-/is-prop-valid-1.3.1.tgz", "integrity": "sha512-/ACwoqx7XQi9knQs/G0qKvv5teDMhD7bXYns9N/wM8ah8iNb8jZ2uNO0YOgiq2o2poIvVtJS2YALasQuMSQ7Kw==", "license": "MIT", "dependencies": {"@emotion/memoize": "^0.9.0"}}, "node_modules/@babel/preset-modules": {"version": "0.1.6-no-external-plugins", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/types": "^7.4.4", "esutils": "^2.0.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^8.0.0-0 <8.0.0"}}, "node_modules/ignore": {"version": "5.3.2", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/jest-each": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "chalk": "^4.0.0", "jest-get-type": "^27.5.1", "jest-util": "^27.5.1", "pretty-format": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/http-proxy-middleware": {"version": "2.0.7", "license": "MIT", "dependencies": {"@types/http-proxy": "^1.17.8", "http-proxy": "^1.18.1", "is-glob": "^4.0.1", "is-plain-obj": "^3.0.0", "micromatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"@types/express": "^4.17.13"}, "peerDependenciesMeta": {"@types/express": {"optional": true}}}, "node_modules/eslint-plugin-react-hooks": {"version": "4.6.2", "license": "MIT", "engines": {"node": ">=10"}, "peerDependencies": {"eslint": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0"}}, "node_modules/webpack/node_modules/estraverse": {"version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/@typescript-eslint/utils/node_modules/eslint-scope": {"version": "5.1.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/char-regex": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/css-select": {"version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.0.1", "domhandler": "^4.3.1", "domutils": "^2.8.0", "nth-check": "^2.0.1"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/http-errors": {"version": "2.0.0", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/@babel/plugin-proposal-decorators": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/plugin-syntax-decorators": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/log-symbols": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz", "integrity": "sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ajv-formats": {"version": "2.1.1", "license": "MIT", "dependencies": {"ajv": "^8.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "node_modules/source-map": {"version": "0.7.4", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">= 8"}}, "node_modules/@babel/plugin-transform-dotall-regex": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/json-buffer": {"version": "3.0.1", "license": "MIT"}, "node_modules/@testing-library/jest-dom/node_modules/chalk": {"version": "3.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=8"}}, "node_modules/is-set": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.2.1", "license": "MIT"}, "node_modules/globby": {"version": "11.1.0", "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "license": "MIT"}, "node_modules/@webassemblyjs/floating-point-hex-parser": {"version": "1.13.2", "license": "MIT"}, "node_modules/set-proto": {"version": "1.0.0", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-shared-array-buffer": {"version": "1.0.4", "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@types/babel__traverse": {"version": "7.20.6", "license": "MIT", "dependencies": {"@babel/types": "^7.20.7"}}, "node_modules/error-ex": {"version": "1.3.2", "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/source-list-map": {"version": "2.0.1", "license": "MIT"}, "node_modules/eslint-module-utils/node_modules/debug": {"version": "3.2.7", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/@jest/schemas": {"version": "28.1.3", "license": "MIT", "dependencies": {"@sinclair/typebox": "^0.24.1"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/@types/node": {"version": "16.18.126", "license": "MIT"}, "node_modules/path-scurry": {"version": "1.11.1", "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@mui/base/node_modules/clsx": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/clsx/-/clsx-1.2.1.tgz", "integrity": "sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@types/d3-format": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/@types/d3-format/-/d3-format-3.0.4.tgz", "integrity": "sha512-fALi2aI6shfg7vM5KiR1wNJnZ7r6UuggVqtDA+xiEdPZQwy/trcQaHnwShLuLdta2rTymCNpxYTiMZX/e09F4g==", "license": "MIT"}, "node_modules/get-intrinsic": {"version": "1.3.0", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/launch-editor": {"version": "2.10.0", "license": "MIT", "dependencies": {"picocolors": "^1.0.0", "shell-quote": "^1.8.1"}}, "node_modules/object.groupby": {"version": "1.0.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/cjs-module-lexer": {"version": "1.4.3", "license": "MIT"}, "node_modules/@mui/icons-material": {"version": "5.11.16", "resolved": "https://registry.npmjs.org/@mui/icons-material/-/icons-material-5.11.16.tgz", "integrity": "sha512-oKkx9z9Kwg40NtcIajF9uOXhxiyTZrrm9nmIJ4UjkU2IdHpd4QVLbCc/5hZN/y0C6qzi2Zlxyr9TGddQx2vx2A==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.21.0"}, "engines": {"node": ">=12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui"}, "peerDependencies": {"@mui/material": "^5.0.0", "@types/react": "^17.0.0 || ^18.0.0", "react": "^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/eslint/node_modules/p-locate": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/workbox-build/node_modules/fs-extra": {"version": "9.1.0", "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/postcss-focus-visible": {"version": "6.0.4", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.9"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/jest-watch-typeahead/node_modules/string-length/node_modules/char-regex": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">=12.20"}}, "node_modules/postcss-color-functional-notation": {"version": "4.2.4", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/babel-plugin-jest-hoist": {"version": "27.5.1", "license": "MIT", "dependencies": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-jasmine2": {"version": "27.5.1", "license": "MIT", "dependencies": {"jest-matcher-utils": "^27.5.1", "jest-each": "^27.5.1", "@jest/environment": "^27.5.1", "jest-snapshot": "^27.5.1", "co": "^4.6.0", "expect": "^27.5.1", "jest-runtime": "^27.5.1", "@types/node": "*", "chalk": "^4.0.0", "@jest/test-result": "^27.5.1", "throat": "^6.0.1", "jest-util": "^27.5.1", "@jest/source-map": "^27.5.1", "jest-message-util": "^27.5.1", "pretty-format": "^27.5.1", "is-generator-fn": "^2.0.0", "@jest/types": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/es-array-method-boxes-properly": {"version": "1.0.0", "license": "MIT"}, "node_modules/@babel/plugin-transform-object-super": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-replace-supers": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/harmony-reflect": {"version": "1.6.2", "license": "(Apache-2.0 OR MPL-1.1)"}, "node_modules/accepts/node_modules/negotiator": {"version": "0.6.3", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/bluebird": {"version": "3.7.2", "license": "MIT"}, "node_modules/retry": {"version": "0.13.1", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/lodash.memoize": {"version": "4.1.2", "license": "MIT"}, "node_modules/own-keys": {"version": "1.0.1", "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.6", "object-keys": "^1.1.1", "safe-push-apply": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/body-parser/node_modules/iconv-lite": {"version": "0.4.24", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/svgo/node_modules/dom-serializer": {"version": "0.2.2", "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "entities": "^2.0.0"}}, "node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"license": "MIT", "version": "^0.7.0", "engines": {"node": ">= 0.6"}}, "node_modules/@types/qs": {"version": "6.9.18", "license": "MIT"}, "node_modules/tsconfig-paths/node_modules/json5": {"version": "1.0.2", "license": "MIT", "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "node_modules/punycode": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/function.prototype.name": {"version": "1.1.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "functions-have-names": "^1.2.3", "hasown": "^2.0.2", "is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/lodash.sortby": {"version": "4.7.0", "license": "MIT"}, "node_modules/istanbul-lib-coverage": {"version": "3.2.2", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/graphql": {"version": "16.10.0", "resolved": "https://registry.npmjs.org/graphql/-/graphql-16.10.0.tgz", "integrity": "sha512-AjqGKbDGUFRKIRCP9tCKiIGHyriz2oHEbPIbEtcSLSs4YjReZOIPQQWek4+6hjw62H9QShXHyaGivGiYVLeYFQ==", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.16.0 || ^16.0.0 || >=17.0.0"}}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "5.62.0", "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.4.0", "@typescript-eslint/scope-manager": "5.62.0", "@typescript-eslint/type-utils": "5.62.0", "@typescript-eslint/utils": "5.62.0", "debug": "^4.3.4", "graphemer": "^1.4.0", "ignore": "^5.2.0", "natural-compare-lite": "^1.4.0", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^5.0.0", "eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/adjust-sourcemap-loader": {"version": "4.0.0", "license": "MIT", "dependencies": {"loader-utils": "^2.0.0", "regex-parser": "^2.2.11"}, "engines": {"node": ">=8.9"}}, "node_modules/@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/fork-ts-checker-webpack-plugin/node_modules/tapable": {"version": "1.1.3", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/resolve": {"version": "1.22.10", "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/babel-loader": {"version": "8.4.1", "license": "MIT", "dependencies": {"find-cache-dir": "^3.3.1", "loader-utils": "^2.0.4", "make-dir": "^3.1.0", "schema-utils": "^2.6.5"}, "engines": {"node": ">= 8.9"}, "peerDependencies": {"@babel/core": "^7.0.0", "webpack": ">=2"}}, "node_modules/@emotion/cache": {"version": "11.14.0", "resolved": "https://registry.npmjs.org/@emotion/cache/-/cache-11.14.0.tgz", "integrity": "sha512-L/B1lc/TViYk4DcpGxtAVbx0ZyiKM5ktoIyafGkH6zg/tj+mA+NE//aPYKG0k8kCHSHVJrpLpcAlOBEXQ3SavA==", "license": "MIT", "dependencies": {"@emotion/memoize": "^0.9.0", "@emotion/sheet": "^1.4.0", "@emotion/utils": "^1.4.2", "@emotion/weak-memoize": "^0.4.0", "stylis": "4.2.0"}}, "node_modules/fs-extra": {"version": "10.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/tough-cookie": {"version": "4.1.4", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "universalify": "^0.2.0", "url-parse": "^1.5.3"}, "engines": {"node": ">=6"}}, "node_modules/@types/set-cookie-parser": {"version": "2.4.10", "resolved": "https://registry.npmjs.org/@types/set-cookie-parser/-/set-cookie-parser-2.4.10.tgz", "integrity": "sha512-GGmQVGpQWUe5qglJozEjZV/5dyxbOOZ0LHe/lqyWssB88Y4svNfst0uqBVscdDeIKl5Jy5+aPSvy7mI9tYRguw==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/d3-axis": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/@types/d3-axis/-/d3-axis-3.0.6.tgz", "integrity": "sha512-pYeijfZuBd87T0hGn0FO1vQ/cgLk6E1ALJjfkC0oJ8cbwkZl3TpgS8bVBLZN+2jjGgg38epgxb2zmoGtSfvgMw==", "license": "MIT", "dependencies": {"@types/d3-selection": "*"}}, "node_modules/@types/prettier": {"version": "2.7.3", "license": "MIT"}, "node_modules/domexception": {"version": "2.0.1", "license": "MIT", "dependencies": {"webidl-conversions": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-watch-typeahead/node_modules/jest-watcher/node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/@csstools/postcss-color-function": {"version": "1.1.1", "license": "CC0-1.0", "dependencies": {"@csstools/postcss-progressive-custom-properties": "^1.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-normalize-url": {"version": "5.1.0", "license": "MIT", "dependencies": {"normalize-url": "^6.0.1", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/is-node-process": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/is-node-process/-/is-node-process-1.2.0.tgz", "integrity": "sha512-Vg4o6/fqPxIjtxgUH5QLJhwZ7gW5diGCVlXpuUfELC62CuxM1iHcRe51f2W1FDy04Ai4KJkagKjx3XaqyfRKXw==", "dev": true, "license": "MIT"}, "node_modules/coa/node_modules/color-name": {"version": "1.1.3", "license": "MIT"}, "node_modules/@babel/plugin-proposal-optional-chaining": {"version": "7.21.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0", "@babel/plugin-syntax-optional-chaining": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@emotion/use-insertion-effect-with-fallbacks": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@emotion/use-insertion-effect-with-fallbacks/-/use-insertion-effect-with-fallbacks-1.2.0.tgz", "integrity": "sha512-yJMtVdH59sxi/aVJBpk9FQq+OR8ll5GT8oWd57UpeaKEVGab41JWaCFA7FRLoMLloOZF/c/wsPoe+bfGmRKgDg==", "license": "MIT", "peerDependencies": {"react": ">=16.8.0"}}, "node_modules/@types/react-dom": {"version": "18.3.5", "license": "MIT", "peerDependencies": {"@types/react": "^18.0.0"}}, "node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/@babel/generator": {"version": "7.26.10", "license": "MIT", "dependencies": {"@babel/parser": "^7.26.10", "@babel/types": "^7.26.10", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/dom-helpers": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz", "integrity": "sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.8.7", "csstype": "^3.0.2"}}, "node_modules/regjsgen": {"version": "0.8.0", "license": "MIT"}, "node_modules/flat-cache": {"version": "3.2.0", "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.3", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/fs-monkey": {"version": "1.0.6", "license": "Unlicense"}, "node_modules/@babel/helpers": {"version": "7.26.10", "license": "MIT", "dependencies": {"@babel/template": "^7.26.9", "@babel/types": "^7.26.10"}, "engines": {"node": ">=6.9.0"}}, "node_modules/camelcase": {"version": "6.3.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ejs": {"version": "3.1.10", "license": "Apache-2.0", "dependencies": {"jake": "^10.8.5"}, "bin": {"ejs": "bin/cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/eslint-plugin-testing-library": {"version": "5.11.1", "license": "MIT", "dependencies": {"@typescript-eslint/utils": "^5.58.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0", "npm": ">=6"}, "peerDependencies": {"eslint": "^7.5.0 || ^8.0.0"}}, "node_modules/@babel/plugin-transform-duplicate-keys": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/workbox-background-sync": {"version": "6.6.0", "license": "MIT", "dependencies": {"idb": "^7.0.1", "workbox-core": "6.6.0"}}, "node_modules/postcss-selector-not": {"version": "6.0.1", "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/https-proxy-agent": {"version": "5.0.1", "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/jest-watch-typeahead": {"version": "1.1.0", "license": "MIT", "dependencies": {"ansi-escapes": "^4.3.1", "chalk": "^4.0.0", "jest-regex-util": "^28.0.0", "jest-watcher": "^28.0.0", "slash": "^4.0.0", "string-length": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "peerDependencies": {"jest": "^27.0.0 || ^28.0.0"}}, "node_modules/@types/yargs": {"version": "16.0.9", "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/data-view-buffer": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/bfj": {"version": "7.1.0", "license": "MIT", "dependencies": {"bluebird": "^3.7.2", "check-types": "^11.2.3", "hoopy": "^0.1.4", "jsonpath": "^1.1.1", "tryer": "^1.0.1"}, "engines": {"node": ">= 8.0.0"}}, "node_modules/obuf": {"version": "1.1.2", "license": "MIT"}, "node_modules/d3-dsv/node_modules/commander": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/commander/-/commander-7.2.0.tgz", "integrity": "sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==", "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/@babel/plugin-transform-unicode-regex": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/shell-quote": {"version": "1.8.2", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@jest/transform/node_modules/convert-source-map": {"version": "1.9.0", "license": "MIT"}, "node_modules/html-minifier-terser": {"version": "6.1.0", "license": "MIT", "dependencies": {"camel-case": "^4.1.2", "clean-css": "^5.2.2", "commander": "^8.3.0", "he": "^1.2.0", "param-case": "^3.0.4", "relateurl": "^0.2.7", "terser": "^5.10.0"}, "bin": {"html-minifier-terser": "cli.js"}, "engines": {"node": ">=12"}}, "node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/jest-changed-files": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "execa": "^5.0.0", "throat": "^6.0.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/d3-selection": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/d3-selection/-/d3-selection-3.0.0.tgz", "integrity": "sha512-fmTRWbNMmsmWq6xJV8D19U/gw/bwrHfNXxrIN+HfZgnzqTHp9jOmKMhsTUjXOJnZOdZY9Q28y4yebKzqDKlxlQ==", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/has-symbols": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/icss-utils": {"version": "5.1.0", "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-replace-overflow-wrap": {"version": "4.0.0", "license": "MIT", "peerDependencies": {"postcss": "^8.0.3"}}, "node_modules/d3-delaunay": {"version": "6.0.4", "resolved": "https://registry.npmjs.org/d3-delaunay/-/d3-delaunay-6.0.4.tgz", "integrity": "sha512-mdjtIZ1XLAM8bm/hx3WwjfHt6Sggek7qH043O8KEjDXN40xi3vx/6pYSVTwLjEgiXQTbvaouWKynLBiUZ6SK6A==", "license": "ISC", "dependencies": {"delaunator": "5"}, "engines": {"node": ">=12"}}, "node_modules/@types/d3-zoom": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/@types/d3-zoom/-/d3-zoom-3.0.8.tgz", "integrity": "sha512-iqMC4/YlFCSlO8+2Ii1GGGliCAY4XdeG748w5vQUbevlbDu0zSjH/+jojorQVBK/se0j6DUFNPBGSqD3YWYnDw==", "license": "MIT", "dependencies": {"@types/d3-interpolate": "*", "@types/d3-selection": "*"}}, "node_modules/xmlchars": {"version": "2.2.0", "license": "MIT"}, "node_modules/core-js-pure": {"version": "3.41.0", "hasInstallScript": true, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/leven": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/es-shim-unscopables": {"version": "1.1.0", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/postcss-logical": {"version": "5.0.4", "license": "CC0-1.0", "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/workbox-webpack-plugin/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/is-root": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escalade": {"version": "3.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/json-format": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/json-format/-/json-format-1.0.1.tgz", "integrity": "sha512-MoKIg/lBeQALqjYnqEanikfo3zBKRwclpXJexdF0FUniYAAN2ypEIXBEtpQb+9BkLFtDK1fyTLAsnGlyGfLGxw==", "dev": true, "license": "MIT"}, "node_modules/es-abstract": {"version": "1.23.9", "license": "MIT", "dependencies": {"gopd": "^1.2.0", "data-view-buffer": "^1.0.2", "regexp.prototype.flags": "^1.5.3", "object-keys": "^1.1.1", "has-proto": "^1.2.0", "is-array-buffer": "^3.0.5", "es-set-tostringtag": "^2.1.0", "internal-slot": "^1.1.0", "data-view-byte-length": "^1.0.2", "typed-array-buffer": "^1.0.3", "es-object-atoms": "^1.0.0", "es-to-primitive": "^1.3.0", "hasown": "^2.0.2", "get-intrinsic": "^1.2.7", "is-weakref": "^1.1.0", "call-bound": "^1.0.3", "is-callable": "^1.2.7", "string.prototype.trimend": "^1.0.9", "is-data-view": "^1.0.2", "available-typed-arrays": "^1.0.7", "is-shared-array-buffer": "^1.0.4", "safe-push-apply": "^1.0.0", "object.assign": "^4.1.7", "arraybuffer.prototype.slice": "^1.0.4", "math-intrinsics": "^1.1.0", "data-view-byte-offset": "^1.0.1", "is-regex": "^1.2.1", "is-typed-array": "^1.1.15", "es-define-property": "^1.0.1", "has-symbols": "^1.1.0", "array-buffer-byte-length": "^1.0.2", "string.prototype.trim": "^1.2.10", "get-symbol-description": "^1.1.0", "safe-regex-test": "^1.1.0", "unbox-primitive": "^1.1.0", "safe-array-concat": "^1.1.3", "set-proto": "^1.0.0", "is-string": "^1.1.1", "own-keys": "^1.0.1", "call-bind": "^1.0.8", "function.prototype.name": "^1.1.8", "typed-array-byte-length": "^1.0.3", "object-inspect": "^1.13.3", "globalthis": "^1.0.4", "typed-array-length": "^1.0.7", "which-typed-array": "^1.1.18", "get-proto": "^1.0.0", "has-property-descriptors": "^1.0.2", "typed-array-byte-offset": "^1.0.4", "string.prototype.trimstart": "^1.0.8", "es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@mswjs/interceptors": {"version": "0.17.10", "resolved": "https://registry.npmjs.org/@mswjs/interceptors/-/interceptors-0.17.10.tgz", "integrity": "sha512-N8x7eSLGcmUFNWZRxT1vsHvypzIRgQYdG0rJey/rZCy6zT/30qDt8Joj7FxzGNLSwXbeZqJOMqDurp7ra4hgbw==", "dev": true, "license": "MIT", "dependencies": {"@open-draft/until": "^1.0.3", "@types/debug": "^4.1.7", "@xmldom/xmldom": "^0.8.3", "debug": "^4.3.3", "headers-polyfill": "3.2.5", "outvariant": "^1.2.1", "strict-event-emitter": "^0.2.4", "web-encoding": "^1.1.5"}, "engines": {"node": ">=14"}}, "node_modules/walker": {"version": "1.0.8", "license": "Apache-2.0", "dependencies": {"makeerror": "1.0.12"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/minimatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/is-date-object": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/core-util-is": {"version": "1.0.3", "license": "MIT"}, "node_modules/postcss-custom-selectors": {"version": "6.0.3", "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.4"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.3"}}, "node_modules/through": {"version": "2.3.8", "resolved": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "integrity": "sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==", "dev": true, "license": "MIT"}, "node_modules/express/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/define-properties": {"version": "1.2.1", "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/jest-circus": {"version": "27.5.1", "license": "MIT", "dependencies": {"dedent": "^0.7.0", "jest-matcher-utils": "^27.5.1", "jest-each": "^27.5.1", "@jest/environment": "^27.5.1", "jest-snapshot": "^27.5.1", "co": "^4.6.0", "expect": "^27.5.1", "jest-runtime": "^27.5.1", "@types/node": "*", "chalk": "^4.0.0", "@jest/test-result": "^27.5.1", "throat": "^6.0.1", "jest-util": "^27.5.1", "slash": "^3.0.0", "jest-message-util": "^27.5.1", "pretty-format": "^27.5.1", "is-generator-fn": "^2.0.0", "@jest/types": "^27.5.1", "stack-utils": "^2.0.3"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-runtime": {"version": "27.5.1", "license": "MIT", "dependencies": {"cjs-module-lexer": "^1.0.0", "jest-mock": "^27.5.1", "@jest/environment": "^27.5.1", "jest-haste-map": "^27.5.1", "jest-snapshot": "^27.5.1", "collect-v8-coverage": "^1.0.0", "execa": "^5.0.0", "@jest/globals": "^27.5.1", "chalk": "^4.0.0", "@jest/test-result": "^27.5.1", "jest-util": "^27.5.1", "slash": "^3.0.0", "@jest/source-map": "^27.5.1", "jest-message-util": "^27.5.1", "jest-regex-util": "^27.5.1", "strip-bom": "^4.0.0", "@jest/transform": "^27.5.1", "glob": "^7.1.3", "jest-resolve": "^27.5.1", "@jest/types": "^27.5.1", "@jest/fake-timers": "^27.5.1", "graceful-fs": "^4.2.9"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/global-prefix": {"version": "3.0.0", "license": "MIT", "dependencies": {"ini": "^1.3.5", "kind-of": "^6.0.2", "which": "^1.3.1"}, "engines": {"node": ">=6"}}, "node_modules/@csstools/postcss-hwb-function": {"version": "1.0.2", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/lodash.uniq": {"version": "4.5.0", "license": "MIT"}, "node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/jest-watch-typeahead/node_modules/@jest/types": {"version": "28.1.3", "license": "MIT", "dependencies": {"@jest/schemas": "^28.1.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/address": {"version": "1.2.2", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/redent": {"version": "3.0.0", "license": "MIT", "dependencies": {"indent-string": "^4.0.0", "strip-indent": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@babel/plugin-syntax-typescript": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/package-json-from-dist": {"version": "1.0.1", "license": "BlueOak-1.0.0"}, "node_modules/@mui/material/node_modules/clsx": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/clsx/-/clsx-1.2.1.tgz", "integrity": "sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/binary-extensions": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/rimraf": {"version": "3.0.2", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/pretty-format": {"version": "27.5.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1", "ansi-styles": "^5.0.0", "react-is": "^17.0.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/d3-transition": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/d3-transition/-/d3-transition-3.0.1.tgz", "integrity": "sha512-ApKvfjsSR6tg06xrL434C0WydLr7JewBB3V+/39RMHsaXTOG0zmt/OAXeng5M5LBm0ojmxJrpomQVZ1aPvBL4w==", "license": "ISC", "dependencies": {"d3-color": "1 - 3", "d3-dispatch": "1 - 3", "d3-ease": "1 - 3", "d3-interpolate": "1 - 3", "d3-timer": "1 - 3"}, "engines": {"node": ">=12"}, "peerDependencies": {"d3-selection": "2 - 3"}}, "node_modules/postcss-color-hex-alpha": {"version": "8.0.4", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/object-hash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/@types/d3-shape": {"version": "3.1.7", "resolved": "https://registry.npmjs.org/@types/d3-shape/-/d3-shape-3.1.7.tgz", "integrity": "sha512-VLvUQ33C+3J+8p+Daf+nYSOsjB4GXp19/S/aGo60m9h1v6XaxjiT82lKVWJCfzhtuZ3yD7i/TPeC/fuKLLOSmg==", "license": "MIT", "dependencies": {"@types/d3-path": "*"}}, "node_modules/get-caller-file": {"version": "2.0.5", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/data-view-byte-offset": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@babel/plugin-transform-react-constant-elements": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/postcss-modules-local-by-default": {"version": "4.2.0", "license": "MIT", "dependencies": {"icss-utils": "^5.0.0", "postcss-selector-parser": "^7.0.0", "postcss-value-parser": "^4.1.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/@babel/plugin-transform-dynamic-import": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/cssnano-utils": {"version": "3.1.0", "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/aria-query": {"version": "5.3.0", "license": "Apache-2.0", "dependencies": {"dequal": "^2.0.3"}}, "node_modules/@svgr/babel-plugin-transform-svg-component": {"version": "5.5.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/static-eval/node_modules/optionator": {"version": "0.8.3", "license": "MIT", "dependencies": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.6", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "word-wrap": "~1.2.3"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/jsx-ast-utils": {"version": "3.3.5", "license": "MIT", "dependencies": {"array-includes": "^3.1.6", "array.prototype.flat": "^1.3.1", "object.assign": "^4.1.4", "object.values": "^1.1.6"}, "engines": {"node": ">=4.0"}}, "node_modules/side-channel-map": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/d3-drag": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/d3-drag/-/d3-drag-3.0.0.tgz", "integrity": "sha512-pWbUJLdETVA8lQNJecMxoXfH6x+mO2UQo8rSmZ+QqxcbyA3hfeprFgIT//HW2nlHChWeIIMwS2Fq+gEARkhTkg==", "license": "ISC", "dependencies": {"d3-dispatch": "1 - 3", "d3-selection": "3"}, "engines": {"node": ">=12"}}, "node_modules/minipass": {"version": "7.1.2", "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/jest-serializer": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/node": "*", "graceful-fs": "^4.2.9"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/webpack-dev-middleware": {"version": "5.3.4", "license": "MIT", "dependencies": {"colorette": "^2.0.10", "memfs": "^3.4.3", "mime-types": "^2.1.31", "range-parser": "^1.2.1", "schema-utils": "^4.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}}, "node_modules/escodegen/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/terser-webpack-plugin": {"version": "5.3.14", "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "^0.3.25", "jest-worker": "^27.4.5", "schema-utils": "^4.3.0", "serialize-javascript": "^6.0.2", "terser": "^5.31.1"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.1.0"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "esbuild": {"optional": true}, "uglify-js": {"optional": true}}}, "node_modules/@babel/plugin-transform-member-expression-literals": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/strict-event-emitter": {"version": "0.4.6", "resolved": "https://registry.npmjs.org/strict-event-emitter/-/strict-event-emitter-0.4.6.tgz", "integrity": "sha512-12KWeb+wixJohmnwNFerbyiBrAlq5qJLwIt38etRtKtmmHyDSoGlIqFE9wx+4IwG0aDjI7GV8tc8ZccjWZZtTg==", "dev": true, "license": "MIT"}, "node_modules/forwarded": {"version": "0.2.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/regenerate-unicode-properties": {"version": "10.2.0", "license": "MIT", "dependencies": {"regenerate": "^1.4.2"}, "engines": {"node": ">=4"}}, "node_modules/lz-string": {"version": "1.5.0", "license": "MIT", "bin": {"lz-string": "bin/bin.js"}}, "node_modules/react-chartjs-2": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/react-chartjs-2/-/react-chartjs-2-5.3.0.tgz", "integrity": "sha512-UfZZFnDsERI3c3CZGxzvNJd02SHjaSJ8kgW1djn65H1KK8rehwTjyrRKOG3VTMG8wtHZ5rgAO5oTHtHi9GCCmw==", "license": "MIT", "peerDependencies": {"chart.js": "^4.1.1", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/execa": {"version": "5.1.1", "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/cssnano-preset-default": {"version": "5.2.14", "license": "MIT", "dependencies": {"postcss-minify-selectors": "^5.2.1", "postcss-reduce-initial": "^5.1.2", "postcss-merge-longhand": "^5.1.7", "postcss-discard-empty": "^5.1.1", "postcss-reduce-transforms": "^5.1.0", "postcss-normalize-url": "^5.1.0", "postcss-svgo": "^5.1.0", "css-declaration-sorter": "^6.3.1", "postcss-discard-overridden": "^5.1.0", "postcss-normalize-string": "^5.1.0", "postcss-normalize-repeat-style": "^5.1.1", "postcss-ordered-values": "^5.1.3", "postcss-normalize-timing-functions": "^5.1.0", "postcss-colormin": "^5.3.1", "postcss-discard-duplicates": "^5.1.0", "postcss-minify-font-values": "^5.1.0", "postcss-minify-gradients": "^5.1.1", "postcss-merge-rules": "^5.1.4", "postcss-normalize-charset": "^5.1.0", "postcss-normalize-whitespace": "^5.1.1", "cssnano-utils": "^3.1.0", "postcss-minify-params": "^5.1.4", "postcss-normalize-positions": "^5.1.1", "postcss-normalize-display-values": "^5.1.0", "postcss-normalize-unicode": "^5.1.1", "postcss-unique-selectors": "^5.1.1", "postcss-calc": "^8.2.3", "postcss-convert-values": "^5.1.3", "postcss-discard-comments": "^5.1.2"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/lodash": {"version": "4.17.21", "license": "MIT"}, "node_modules/path-key": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@babel/plugin-transform-regenerator": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "regenerator-transform": "^0.15.2"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/postcss-normalize-string": {"version": "5.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/@emotion/unitless": {"version": "0.10.0", "resolved": "https://registry.npmjs.org/@emotion/unitless/-/unitless-0.10.0.tgz", "integrity": "sha512-dFoMUuQA20zvtVTuxZww6OHoJYgrzfKM1t52mVySDJnMSEa08ruEvdYQbhvyu6soU+NeLVd3yKfTfT0NeV6qGg==", "license": "MIT"}, "node_modules/@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/normalize-path": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/axobject-query": {"version": "4.1.0", "license": "Apache-2.0", "engines": {"node": ">= 0.4"}}, "node_modules/is-number-object": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arguments": {"version": "1.2.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/mime": {"version": "1.6.0", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/msw/node_modules/type-fest": {"version": "2.19.0", "resolved": "https://registry.npmjs.org/type-fest/-/type-fest-2.19.0.tgz", "integrity": "sha512-RAH822pAdBgcNMAfWnCBU3CFZcfZ/i1eZjwFU/dsLKumyuuP3niueg2UAukXYF0E2AAoc82ZSSf9J0WQBinzHA==", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/fraction.js": {"version": "4.3.7", "license": "MIT", "engines": {"node": "*"}, "funding": {"type": "patreon", "url": "https://github.com/sponsors/rawify"}}, "node_modules/es-module-lexer": {"version": "1.6.0", "license": "MIT"}, "node_modules/throat": {"version": "6.0.2", "license": "MIT"}, "node_modules/arraybuffer.prototype.slice": {"version": "1.0.4", "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.1", "call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "is-array-buffer": "^3.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@svgr/plugin-jsx": {"version": "5.5.0", "license": "MIT", "dependencies": {"@babel/core": "^7.12.3", "@svgr/babel-preset": "^5.5.0", "@svgr/hast-util-to-babel-ast": "^5.5.0", "svg-parser": "^2.0.2"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/serve-index/node_modules/inherits": {"version": "2.0.3", "license": "ISC"}, "node_modules/babel-preset-current-node-syntax": {"version": "1.1.0", "license": "MIT", "dependencies": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-import-attributes": "^7.24.7", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/pascal-case": {"version": "3.1.2", "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/es-errors": {"version": "1.3.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/safer-buffer": {"version": "2.1.2", "license": "MIT"}, "node_modules/@csstools/postcss-text-decoration-shorthand": {"version": "1.0.0", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/d3-hierarchy": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/d3-hierarchy/-/d3-hierarchy-3.1.2.tgz", "integrity": "sha512-FX/9frcub54beBdugHjDCdikxThEqjnR93Qt7PvQTOHxyiNCAlvMrHhclk3cD5VeAaq9fxmfRp+CnWw9rEMBuA==", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/css-tree/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/path-scurry/node_modules/lru-cache": {"version": "10.4.3", "license": "ISC"}, "node_modules/onetime": {"version": "5.1.2", "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/file-entry-cache": {"version": "6.0.1", "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/js-tokens": {"version": "4.0.0", "license": "MIT"}, "node_modules/postcss-double-position-gradients": {"version": "3.1.2", "license": "CC0-1.0", "dependencies": {"@csstools/postcss-progressive-custom-properties": "^1.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@types/serve-index": {"version": "1.9.4", "license": "MIT", "dependencies": {"@types/express": "*"}}, "node_modules/react-app-polyfill": {"version": "3.0.0", "license": "MIT", "dependencies": {"core-js": "^3.19.2", "object-assign": "^4.1.1", "promise": "^8.1.0", "raf": "^3.4.1", "regenerator-runtime": "^0.13.9", "whatwg-fetch": "^3.6.2"}, "engines": {"node": ">=14"}}, "node_modules/postcss-preset-env": {"version": "7.8.3", "license": "CC0-1.0", "dependencies": {"postcss-focus-within": "^5.0.4", "css-blank-pseudo": "^3.0.3", "@csstools/postcss-hwb-function": "^1.0.2", "@csstools/postcss-ic-unit": "^1.0.1", "postcss-pseudo-class-any-link": "^7.1.6", "@csstools/postcss-text-decoration-shorthand": "^1.0.0", "postcss-media-minmax": "^5.0.0", "postcss-selector-not": "^6.0.1", "postcss-nesting": "^10.2.0", "postcss-env-function": "^4.0.6", "@csstools/postcss-normalize-display-values": "^1.0.1", "@csstools/postcss-oklab-function": "^1.1.1", "postcss-replace-overflow-wrap": "^4.0.0", "@csstools/postcss-trigonometric-functions": "^1.0.2", "css-has-pseudo": "^3.0.4", "postcss-overflow-shorthand": "^3.0.4", "postcss-lab-function": "^4.2.1", "postcss-color-functional-notation": "^4.2.4", "@csstools/postcss-nested-calc": "^1.0.0", "postcss-font-variant": "^5.0.0", "postcss-initial": "^4.0.1", "postcss-custom-media": "^8.0.2", "postcss-value-parser": "^4.2.0", "@csstools/postcss-color-function": "^1.1.1", "@csstools/postcss-unset-value": "^1.0.2", "postcss-custom-selectors": "^6.0.3", "postcss-dir-pseudo-class": "^6.0.5", "postcss-opacity-percentage": "^1.1.2", "@csstools/postcss-progressive-custom-properties": "^1.3.0", "cssdb": "^7.1.0", "postcss-focus-visible": "^6.0.4", "@csstools/postcss-cascade-layers": "^1.1.1", "browserslist": "^4.21.4", "postcss-clamp": "^4.1.0", "postcss-attribute-case-insensitive": "^5.0.2", "postcss-place": "^7.0.5", "autoprefixer": "^10.4.13", "postcss-custom-properties": "^12.1.10", "postcss-page-break": "^3.0.4", "@csstools/postcss-is-pseudo-class": "^2.0.7", "@csstools/postcss-font-format-keywords": "^1.0.1", "postcss-logical": "^5.0.4", "css-prefers-color-scheme": "^6.0.3", "postcss-gap-properties": "^3.0.5", "postcss-image-set-function": "^4.0.7", "@csstools/postcss-stepped-value-functions": "^1.0.1", "postcss-color-rebeccapurple": "^7.1.1", "postcss-color-hex-alpha": "^8.0.4", "postcss-double-position-gradients": "^3.1.2"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@types/d3-timer": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/@types/d3-timer/-/d3-timer-3.0.2.tgz", "integrity": "sha512-Ps3T8E8dZDam6fUyNiMkekK3XUsaUEik+idO9/YjPtfj2qruF8tFBXS7XhtE4iIXBLxhmLjP3SXpLhVf21I9Lw==", "license": "MIT"}, "node_modules/safe-regex-test": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/run-async": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/run-async/-/run-async-2.4.1.tgz", "integrity": "sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/asynckit": {"version": "0.4.0", "license": "MIT"}, "node_modules/require-from-string": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/jest-config": {"version": "27.5.1", "license": "MIT", "dependencies": {"@babel/core": "^7.8.0", "jest-runner": "^27.5.1", "jest-circus": "^27.5.1", "jest-get-type": "^27.5.1", "deepmerge": "^4.2.2", "babel-jest": "^27.5.1", "parse-json": "^5.2.0", "chalk": "^4.0.0", "strip-json-comments": "^3.1.1", "jest-util": "^27.5.1", "slash": "^3.0.0", "jest-regex-util": "^27.5.1", "jest-environment-node": "^27.5.1", "jest-validate": "^27.5.1", "jest-jasmine2": "^27.5.1", "jest-environment-jsdom": "^27.5.1", "pretty-format": "^27.5.1", "glob": "^7.1.1", "jest-resolve": "^27.5.1", "@jest/types": "^27.5.1", "@jest/test-sequencer": "^27.5.1", "graceful-fs": "^4.2.9", "ci-info": "^3.2.0", "micromatch": "^4.0.4"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "peerDependencies": {"ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"ts-node": {"optional": true}}}, "node_modules/@webassemblyjs/utf8": {"version": "1.13.2", "license": "MIT"}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/@eslint/eslintrc/node_modules/globals": {"version": "13.24.0", "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-detect": {"version": "4.0.8", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/typed-array-byte-offset": {"version": "1.0.4", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.15", "reflect.getprototypeof": "^1.0.9"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/kleur": {"version": "3.0.3", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@types/json-schema": {"version": "7.0.15", "license": "MIT"}, "node_modules/postcss-normalize-timing-functions": {"version": "5.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/path-exists": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/global-modules": {"version": "2.0.0", "license": "MIT", "dependencies": {"global-prefix": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/@types/range-parser": {"version": "1.2.7", "license": "MIT"}, "node_modules/json5": {"version": "2.2.3", "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/which-boxed-primitive": {"version": "1.1.1", "license": "MIT", "dependencies": {"is-bigint": "^1.1.0", "is-boolean-object": "^1.2.1", "is-number-object": "^1.1.1", "is-string": "^1.1.1", "is-symbol": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/escape-html": {"version": "1.0.3", "license": "MIT"}, "node_modules/eventemitter3": {"version": "4.0.7", "license": "MIT"}, "node_modules/@types/html-minifier-terser": {"version": "6.1.0", "license": "MIT"}, "node_modules/human-signals": {"version": "2.1.0", "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/@babel/plugin-syntax-import-meta": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/unicode-match-property-ecmascript": {"version": "2.0.0", "license": "MIT", "dependencies": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/eslint-plugin-react/node_modules/resolve": {"version": "2.0.0-next.5", "license": "MIT", "dependencies": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/cli-spinners": {"version": "2.9.2", "resolved": "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.2.tgz", "integrity": "sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/es-get-iterator": {"version": "1.1.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "has-symbols": "^1.0.3", "is-arguments": "^1.1.1", "is-map": "^2.0.2", "is-set": "^2.0.2", "is-string": "^1.0.7", "isarray": "^2.0.5", "stop-iteration-iterator": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@mui/core-downloads-tracker": {"version": "5.17.1", "resolved": "https://registry.npmjs.org/@mui/core-downloads-tracker/-/core-downloads-tracker-5.17.1.tgz", "integrity": "sha512-OcZj+cs6EfUD39IoPBOgN61zf1XFVY+imsGoBDwXeSq2UHJZE3N59zzBOVjclck91Ne3e9gudONOeILvHCIhUA==", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}}, "node_modules/@babel/traverse": {"version": "7.26.10", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.26.2", "@babel/generator": "^7.26.10", "@babel/parser": "^7.26.10", "@babel/template": "^7.26.9", "@babel/types": "^7.26.10", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/react-app-polyfill/node_modules/regenerator-runtime": {"version": "0.13.11", "license": "MIT"}, "node_modules/whatwg-fetch": {"version": "3.6.20", "license": "MIT"}, "node_modules/querystringify": {"version": "2.2.0", "license": "MIT"}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@svgr/babel-plugin-add-jsx-attribute": {"version": "5.4.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@eslint/eslintrc/node_modules/js-yaml": {"version": "4.1.0", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/workbox-webpack-plugin/node_modules/webpack-sources": {"version": "1.4.3", "license": "MIT", "dependencies": {"source-list-map": "^2.0.0", "source-map": "~0.6.1"}}, "node_modules/handle-thing": {"version": "2.0.1", "license": "MIT"}, "node_modules/vary": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/@jest/test-sequencer": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/test-result": "^27.5.1", "graceful-fs": "^4.2.9", "jest-haste-map": "^27.5.1", "jest-runtime": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/webpack-dev-server/node_modules/ws": {"version": "8.18.1", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/setprototypeof": {"version": "1.2.0", "license": "ISC"}, "node_modules/css-declaration-sorter": {"version": "6.4.1", "license": "ISC", "engines": {"node": "^10 || ^12 || >=14"}, "peerDependencies": {"postcss": "^8.0.9"}}, "node_modules/sanitize.css": {"version": "13.0.0", "license": "CC0-1.0"}, "node_modules/recursive-readdir": {"version": "2.2.3", "license": "MIT", "dependencies": {"minimatch": "^3.0.5"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@types/body-parser": {"version": "1.19.5", "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@babel/helper-create-regexp-features-plugin": {"version": "7.26.3", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "regexpu-core": "^6.2.0", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@csstools/postcss-nested-calc": {"version": "1.0.0", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/xmlhttprequest": {"version": "1.8.0", "resolved": "https://registry.npmjs.org/xmlhttprequest/-/xmlhttprequest-1.8.0.tgz", "integrity": "sha512-58Im/U0mlVBLM38NdZjHyhuMtCqa61469k2YP/AaPbvCoV9aQGUpbJBj1QRm2ytRiVQBD/fsw7L2bJGDVQswBA==", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/csstype": {"version": "3.1.3", "license": "MIT"}, "node_modules/pretty-error": {"version": "4.0.0", "license": "MIT", "dependencies": {"lodash": "^4.17.20", "renderkid": "^3.0.0"}}, "node_modules/eslint-plugin-jsx-a11y": {"version": "6.10.2", "license": "MIT", "dependencies": {"aria-query": "^5.3.2", "array-includes": "^3.1.8", "array.prototype.flatmap": "^1.3.2", "ast-types-flow": "^0.0.8", "axe-core": "^4.10.0", "axobject-query": "^4.1.0", "damerau-levenshtein": "^1.0.8", "emoji-regex": "^9.2.2", "hasown": "^2.0.2", "jsx-ast-utils": "^3.3.5", "language-tags": "^1.0.9", "minimatch": "^3.1.2", "object.fromentries": "^2.0.8", "safe-regex-test": "^1.0.3", "string.prototype.includes": "^2.0.1"}, "engines": {"node": ">=4.0"}, "peerDependencies": {"eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9"}}, "node_modules/lru-cache": {"version": "5.1.1", "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/es-iterator-helpers": {"version": "1.2.1", "license": "MIT", "dependencies": {"gopd": "^1.2.0", "has-proto": "^1.2.0", "es-set-tostringtag": "^2.0.3", "internal-slot": "^1.1.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.6", "define-properties": "^1.2.1", "call-bound": "^1.0.3", "es-abstract": "^1.23.6", "has-symbols": "^1.1.0", "iterator.prototype": "^1.1.4", "safe-array-concat": "^1.1.3", "call-bind": "^1.0.8", "globalthis": "^1.0.4", "has-property-descriptors": "^1.0.2", "es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/@types/ms": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/@types/ms/-/ms-2.1.0.tgz", "integrity": "sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA==", "dev": true, "license": "MIT"}, "node_modules/uri-js": {"version": "4.4.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/http-proxy-agent": {"version": "4.0.1", "license": "MIT", "dependencies": {"@tootallnate/once": "1", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/unquote": {"version": "1.1.1", "license": "MIT"}, "node_modules/postcss-discard-comments": {"version": "5.1.2", "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/jest-environment-jsdom": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/environment": "^27.5.1", "@jest/fake-timers": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "jest-mock": "^27.5.1", "jest-util": "^27.5.1", "jsdom": "^16.6.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/promise": {"version": "8.3.0", "license": "MIT", "dependencies": {"asap": "~2.0.6"}}, "node_modules/fb-watchman": {"version": "2.0.2", "license": "Apache-2.0", "dependencies": {"bser": "2.1.1"}}, "node_modules/url-parse": {"version": "1.5.10", "license": "MIT", "dependencies": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}}, "node_modules/@babel/plugin-syntax-import-attributes": {"version": "7.26.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/klona": {"version": "2.0.6", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/globals": {"version": "11.12.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@tootallnate/once": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/@babel/plugin-transform-async-generator-functions": {"version": "7.26.8", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.26.5", "@babel/helper-remap-async-to-generator": "^7.25.9", "@babel/traverse": "^7.26.8"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/chokidar/node_modules/glob-parent": {"version": "5.1.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/jest-watch-typeahead/node_modules/jest-util": {"version": "28.1.3", "license": "MIT", "dependencies": {"@jest/types": "^28.1.3", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/eslint-webpack-plugin/node_modules/jest-worker": {"version": "28.1.3", "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/node-forge": {"version": "1.3.1", "license": "(BSD-3-<PERSON><PERSON> OR GPL-2.0)", "engines": {"node": ">= 6.13.0"}}, "node_modules/neo-async": {"version": "2.6.2", "license": "MIT"}, "node_modules/@babel/plugin-transform-flow-strip-types": {"version": "7.26.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.26.5", "@babel/plugin-syntax-flow": "^7.26.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/source-map-support/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/array-flatten": {"version": "1.1.1", "license": "MIT"}, "node_modules/acorn": {"version": "8.14.1", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/eslint-plugin-jest": {"version": "25.7.0", "license": "MIT", "dependencies": {"@typescript-eslint/experimental-utils": "^5.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "peerDependencies": {"@typescript-eslint/eslint-plugin": "^4.0.0 || ^5.0.0", "eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"@typescript-eslint/eslint-plugin": {"optional": true}, "jest": {"optional": true}}}, "node_modules/ts-interface-checker": {"version": "0.1.13", "license": "Apache-2.0"}, "node_modules/keyv": {"version": "4.5.4", "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/any-promise": {"version": "1.3.0", "license": "MIT"}, "node_modules/inquirer": {"version": "8.2.6", "resolved": "https://registry.npmjs.org/inquirer/-/inquirer-8.2.6.tgz", "integrity": "sha512-M1WuAmb7pn9zdFRtQYk26ZBoY043Sse0wVDdk4Bppr+JOXyQYybdtvK+l9wUibhtjdjvtoiNy8tk+EgsYIUqKg==", "dev": true, "license": "MIT", "dependencies": {"ansi-escapes": "^4.2.1", "chalk": "^4.1.1", "cli-cursor": "^3.1.0", "cli-width": "^3.0.0", "external-editor": "^3.0.3", "figures": "^3.0.0", "lodash": "^4.17.21", "mute-stream": "0.0.8", "ora": "^5.4.1", "run-async": "^2.4.0", "rxjs": "^7.5.5", "string-width": "^4.1.0", "strip-ansi": "^6.0.0", "through": "^2.3.6", "wrap-ansi": "^6.0.1"}, "engines": {"node": ">=12.0.0"}}, "node_modules/cosmiconfig": {"version": "7.1.0", "license": "MIT", "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.2.1", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.10.0"}, "engines": {"node": ">=10"}}, "node_modules/react-is": {"version": "17.0.2", "license": "MIT"}, "node_modules/regenerator-runtime": {"version": "0.14.1", "license": "MIT"}, "node_modules/array.prototype.tosorted": {"version": "1.1.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.3", "es-errors": "^1.3.0", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/internmap": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/internmap/-/internmap-2.0.3.tgz", "integrity": "sha512-5Hh7Y1wQbvY5ooGgPbDaL5iYLAPzMTUrjMulskHLH6wnv/A+1q5rgEaiuqEjB+oxGXIVZs1FF+R/KPN3ZSQYYg==", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/string-width/node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/fork-ts-checker-webpack-plugin/node_modules/schema-utils": {"version": "2.7.0", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.4", "ajv": "^6.12.2", "ajv-keywords": "^3.4.1"}, "engines": {"node": ">= 8.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/combined-stream": {"version": "1.0.8", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/thenify": {"version": "3.3.1", "license": "MIT", "dependencies": {"any-promise": "^1.0.0"}}, "node_modules/webpack": {"version": "5.98.0", "license": "MIT", "dependencies": {"chrome-trace-event": "^1.0.2", "eslint-scope": "5.1.1", "tapable": "^2.1.1", "@webassemblyjs/wasm-edit": "^1.14.1", "terser-webpack-plugin": "^5.3.11", "@types/eslint-scope": "^3.7.7", "acorn": "^8.14.0", "watchpack": "^2.4.1", "@webassemblyjs/wasm-parser": "^1.14.1", "neo-async": "^2.6.2", "enhanced-resolve": "^5.17.1", "events": "^3.2.0", "browserslist": "^4.24.0", "mime-types": "^2.1.27", "@types/estree": "^1.0.6", "loader-runner": "^4.2.0", "schema-utils": "^4.3.0", "glob-to-regexp": "^0.4.1", "@webassemblyjs/ast": "^1.14.1", "es-module-lexer": "^1.2.1", "webpack-sources": "^3.2.3", "json-parse-even-better-errors": "^2.3.1", "graceful-fs": "^4.2.11"}, "bin": {"webpack": "bin/webpack.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}}, "node_modules/@babel/plugin-transform-block-scoping": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/os-tmpdir": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "integrity": "sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/static-eval": {"version": "2.0.2", "license": "MIT", "dependencies": {"escodegen": "^1.8.1"}}, "node_modules/@types/d3-array": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/@types/d3-array/-/d3-array-3.2.1.tgz", "integrity": "sha512-Y2Jn2idRrLzUfAKV2LyRImR+y4oa2AntrgID95SHJxuMUrkNXmanDSed71sRNZysveJVt1hLLemQZIady0FpEg==", "license": "MIT"}, "node_modules/@babel/plugin-transform-async-to-generator": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-remap-async-to-generator": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/jest-watch-typeahead/node_modules/slash": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pkg-up/node_modules/path-exists": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@types/eslint": {"version": "8.56.12", "license": "MIT", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}}, "node_modules/ora": {"version": "5.4.1", "resolved": "https://registry.npmjs.org/ora/-/ora-5.4.1.tgz", "integrity": "sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==", "dev": true, "license": "MIT", "dependencies": {"bl": "^4.1.0", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-spinners": "^2.5.0", "is-interactive": "^1.0.0", "is-unicode-supported": "^0.1.0", "log-symbols": "^4.1.0", "strip-ansi": "^6.0.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/memfs": {"version": "3.5.3", "license": "Unlicense", "dependencies": {"fs-monkey": "^1.0.4"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "license": "MIT"}, "node_modules/@jest/source-map/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/@types/eslint-scope": {"version": "3.7.7", "license": "MIT", "dependencies": {"@types/eslint": "*", "@types/estree": "*"}}, "node_modules/d3-contour": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/d3-contour/-/d3-contour-4.0.2.tgz", "integrity": "sha512-4EzFTRIikzs47RGmdxbeUvLWtGedDUNkTcmzoeyg4sP/dvCexO47AaQL7VKy/gul85TOxw+IBgA8US2xwbToNA==", "license": "ISC", "dependencies": {"d3-array": "^3.2.0"}, "engines": {"node": ">=12"}}, "node_modules/define-lazy-prop": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/http-deceiver": {"version": "1.2.7", "license": "MIT"}, "node_modules/caniuse-api": {"version": "3.0.0", "license": "MIT", "dependencies": {"browserslist": "^4.0.0", "caniuse-lite": "^1.0.0", "lodash.memoize": "^4.1.2", "lodash.uniq": "^4.5.0"}}, "node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-plain-obj": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ast-types-flow": {"version": "0.0.8", "license": "MIT"}, "node_modules/semver": {"version": "7.7.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/regenerator-transform": {"version": "0.15.2", "license": "MIT", "dependencies": {"@babel/runtime": "^7.8.4"}}, "node_modules/sax": {"version": "1.2.4", "license": "ISC"}, "node_modules/@rushstack/eslint-patch": {"version": "1.11.0", "license": "MIT"}, "node_modules/svgo/node_modules/color-convert": {"version": "1.9.3", "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/whatwg-url": {"version": "8.7.0", "license": "MIT", "dependencies": {"lodash": "^4.7.0", "tr46": "^2.1.0", "webidl-conversions": "^6.1.0"}, "engines": {"node": ">=10"}}, "node_modules/object.getownpropertydescriptors": {"version": "2.1.8", "license": "MIT", "dependencies": {"array.prototype.reduce": "^1.0.6", "call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0", "gopd": "^1.0.1", "safe-array-concat": "^1.1.2"}, "engines": {"node": ">= 0.8"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/chokidar": {"version": "3.6.0", "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/pkg-dir": {"version": "4.2.0", "license": "MIT", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "5.62.0", "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.62.0", "eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/util": {"version": "0.12.5", "resolved": "https://registry.npmjs.org/util/-/util-0.12.5.tgz", "integrity": "sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "is-arguments": "^1.0.4", "is-generator-function": "^1.0.7", "is-typed-array": "^1.1.3", "which-typed-array": "^1.1.2"}}, "node_modules/react-error-overlay": {"version": "6.1.0", "license": "MIT"}, "node_modules/@csstools/postcss-stepped-value-functions": {"version": "1.0.1", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/css-minimizer-webpack-plugin": {"version": "3.4.1", "license": "MIT", "dependencies": {"cssnano": "^5.0.6", "jest-worker": "^27.0.2", "postcss": "^8.3.5", "schema-utils": "^4.0.0", "serialize-javascript": "^6.0.0", "source-map": "^0.6.1"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}, "peerDependenciesMeta": {"@parcel/css": {"optional": true}, "clean-css": {"optional": true}, "csso": {"optional": true}, "esbuild": {"optional": true}}}, "node_modules/htmlparser2": {"version": "6.1.0", "funding": ["https://github.com/fb55/htmlparser2?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.0.0", "domutils": "^2.5.2", "entities": "^2.0.0"}}, "node_modules/postcss-import": {"version": "15.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}}, "node_modules/doctrine": {"version": "3.0.0", "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/babel-preset-jest": {"version": "27.5.1", "license": "MIT", "dependencies": {"babel-plugin-jest-hoist": "^27.5.1", "babel-preset-current-node-syntax": "^1.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@open-draft/until": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@open-draft/until/-/until-1.0.3.tgz", "integrity": "sha512-Aq58f5HiWdyDlFffbbSjAlv596h/cOnt2DO1w3DOC7OJ5EHs0hd/nycJfiu9RJbT6Yk6F1knnRRXNSpxoIVZ9Q==", "dev": true, "license": "MIT"}, "node_modules/@babel/plugin-syntax-decorators": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-systemjs": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/traverse": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/eslint-plugin-react/node_modules/doctrine": {"version": "2.1.0", "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/d3-array": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/d3-array/-/d3-array-3.2.4.tgz", "integrity": "sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==", "license": "ISC", "dependencies": {"internmap": "1 - 2"}, "engines": {"node": ">=12"}}, "node_modules/@svgr/babel-plugin-remove-jsx-empty-expression": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/eslint-webpack-plugin": {"version": "3.2.0", "license": "MIT", "dependencies": {"@types/eslint": "^7.29.0 || ^8.4.1", "jest-worker": "^28.0.2", "micromatch": "^4.0.5", "normalize-path": "^3.0.0", "schema-utils": "^4.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"eslint": "^7.0.0 || ^8.0.0", "webpack": "^5.0.0"}}, "node_modules/typedarray-to-buffer": {"version": "3.1.5", "license": "MIT", "dependencies": {"is-typedarray": "^1.0.0"}}, "node_modules/@types/aria-query": {"version": "5.0.4", "license": "MIT"}, "node_modules/tempy": {"version": "0.6.0", "license": "MIT", "dependencies": {"is-stream": "^2.0.0", "temp-dir": "^2.0.0", "type-fest": "^0.16.0", "unique-string": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint/node_modules/type-fest": {"version": "0.20.2", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/web-vitals": {"version": "2.1.4", "license": "Apache-2.0"}, "node_modules/js-yaml": {"version": "3.14.1", "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/unbox-primitive": {"version": "1.1.0", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-bigints": "^1.0.2", "has-symbols": "^1.1.0", "which-boxed-primitive": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@babel/eslint-parser/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@csstools/selector-specificity": {"version": "2.2.0", "license": "CC0-1.0", "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss-selector-parser": "^6.0.10"}}, "node_modules/object-assign": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/safe-array-concat": {"version": "1.1.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "has-symbols": "^1.1.0", "isarray": "^2.0.5"}, "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/dot-case": {"version": "3.0.4", "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/@rollup/plugin-replace": {"version": "2.4.2", "license": "MIT", "dependencies": {"@rollup/pluginutils": "^3.1.0", "magic-string": "^0.25.7"}, "peerDependencies": {"rollup": "^1.20.0 || ^2.0.0"}}, "node_modules/eslint-plugin-import/node_modules/doctrine": {"version": "2.1.0", "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/figures": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/figures/-/figures-3.2.0.tgz", "integrity": "sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^1.0.5"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/path-to-regexp": {"version": "0.1.12", "license": "MIT"}, "node_modules/stackframe": {"version": "1.3.4", "license": "MIT"}, "node_modules/delayed-stream": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/diff-sequences": {"version": "27.5.1", "license": "MIT", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/set-function-length": {"version": "1.2.2", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/normalize-url": {"version": "6.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@types/istanbul-lib-report": {"version": "3.0.3", "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "*"}}, "node_modules/rw": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/rw/-/rw-1.3.3.tgz", "integrity": "sha512-PdhdWy89SiZogBLaw42zdeqtRJ//zFd2PgQavcICDUgJT5oW10QCRKbJ6bg4r0/UY2M6BWd5tkxuGFRvCkgfHQ==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/workbox-precaching": {"version": "6.6.0", "license": "MIT", "dependencies": {"workbox-core": "6.6.0", "workbox-routing": "6.6.0", "workbox-strategies": "6.6.0"}}, "node_modules/@testing-library/react": {"version": "13.4.0", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5", "@testing-library/dom": "^8.5.0", "@types/react-dom": "^18.0.0"}, "engines": {"node": ">=12"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}}, "node_modules/is-typed-array": {"version": "1.1.15", "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/tmpl": {"version": "1.0.5", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/postcss-load-config/node_modules/lilconfig": {"version": "3.1.3", "license": "MIT", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antonk52"}}, "node_modules/workbox-google-analytics": {"version": "6.6.0", "license": "MIT", "dependencies": {"workbox-background-sync": "6.6.0", "workbox-core": "6.6.0", "workbox-routing": "6.6.0", "workbox-strategies": "6.6.0"}}, "node_modules/@babel/runtime": {"version": "7.26.10", "license": "MIT", "dependencies": {"regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@kurkle/color": {"version": "0.3.4", "resolved": "https://registry.npmjs.org/@kurkle/color/-/color-0.3.4.tgz", "integrity": "sha512-M5UknZPHRu3DEDWoipU6sE8PdkZ6Z/S+v4dD+Ke8IaNlpdSQah50lz1KtcFBa2vsdOnwbbnxJwVM4wty6udA5w==", "license": "MIT"}, "node_modules/@testing-library/jest-dom": {"version": "5.17.0", "license": "MIT", "dependencies": {"@adobe/css-tools": "^4.0.1", "@babel/runtime": "^7.9.2", "@types/testing-library__jest-dom": "^5.9.1", "aria-query": "^5.0.0", "chalk": "^3.0.0", "css.escape": "^1.5.1", "dom-accessibility-api": "^0.5.6", "lodash": "^4.17.15", "redent": "^3.0.0"}, "engines": {"node": ">=8", "npm": ">=6", "yarn": ">=1"}}, "node_modules/static-eval/node_modules/estraverse": {"version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/graphemer": {"version": "1.4.0", "license": "MIT"}, "node_modules/browserslist": {"version": "4.24.4", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001688", "electron-to-chromium": "^1.5.73", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.1"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/@types/http-proxy": {"version": "1.17.16", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@babel/helper-create-regexp-features-plugin/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/loose-envify": {"version": "1.4.0", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/which-collection": {"version": "1.0.2", "license": "MIT", "dependencies": {"is-map": "^2.0.3", "is-set": "^2.0.3", "is-weakmap": "^2.0.2", "is-weakset": "^2.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@webassemblyjs/leb128": {"version": "1.13.2", "license": "Apache-2.0", "dependencies": {"@xtuc/long": "4.2.2"}}, "node_modules/postcss-page-break": {"version": "3.0.4", "license": "MIT", "peerDependencies": {"postcss": "^8"}}, "node_modules/agent-base": {"version": "6.0.2", "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/requires-port": {"version": "1.0.0", "license": "MIT"}, "node_modules/jsesc": {"version": "3.1.0", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/natural-compare": {"version": "1.4.0", "license": "MIT"}, "node_modules/call-bind": {"version": "1.0.8", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/cli-cursor": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-3.1.0.tgz", "integrity": "sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==", "dev": true, "license": "MIT", "dependencies": {"restore-cursor": "^3.1.0"}, "engines": {"node": ">=8"}}, "node_modules/workbox-navigation-preload": {"version": "6.6.0", "license": "MIT", "dependencies": {"workbox-core": "6.6.0"}}, "node_modules/faye-websocket": {"version": "0.11.4", "license": "Apache-2.0", "dependencies": {"websocket-driver": ">=0.5.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/react-router": {"version": "6.30.0", "license": "MIT", "dependencies": {"@remix-run/router": "1.23.0"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"react": ">=16.8"}}, "node_modules/@webassemblyjs/helper-api-error": {"version": "1.13.2", "license": "MIT"}, "node_modules/jsdom/node_modules/form-data": {"version": "3.0.3", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.35"}, "engines": {"node": ">= 6"}}, "node_modules/media-typer": {"version": "0.3.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/jest-worker": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/workbox-build/node_modules/source-map": {"version": "0.8.0-beta.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"whatwg-url": "^7.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/@types/d3-path": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/@types/d3-path/-/d3-path-3.1.1.tgz", "integrity": "sha512-VMZBYyQvbGmWyWVea0EHs/BwLgxc+MKi1zLDCONksozI4YJMcTt8ZEuIR4Sb1MMTE8MMW49v0IwI5+b7RmfWlg==", "license": "MIT"}, "node_modules/@babel/plugin-transform-property-literals": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/is-number": {"version": "7.0.0", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/whatwg-encoding/node_modules/iconv-lite": {"version": "0.4.24", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/@types/node-forge": {"version": "1.3.11", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/detect-port-alt/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/is-string": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/http-parser-js": {"version": "0.5.9", "license": "MIT"}, "node_modules/svgo/node_modules/has-flag": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@jest/transform/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/enhanced-resolve": {"version": "5.18.1", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}, "engines": {"node": ">=10.13.0"}}, "node_modules/@jest/fake-timers": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "@sinonjs/fake-timers": "^8.0.1", "@types/node": "*", "jest-message-util": "^27.5.1", "jest-mock": "^27.5.1", "jest-util": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/postcss-svgo/node_modules/svgo": {"version": "2.8.0", "license": "MIT", "dependencies": {"@trysound/sax": "0.2.0", "commander": "^7.2.0", "css-select": "^4.1.3", "css-tree": "^1.1.3", "csso": "^4.2.0", "picocolors": "^1.0.0", "stable": "^0.1.8"}, "bin": {"svgo": "bin/svgo"}, "engines": {"node": ">=10.13.0"}}, "node_modules/temp-dir": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-worker/node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/workbox-core": {"version": "6.6.0", "license": "MIT"}, "node_modules/magic-string": {"version": "0.25.9", "license": "MIT", "dependencies": {"sourcemap-codec": "^1.4.8"}}, "node_modules/idb": {"version": "7.1.1", "license": "ISC"}, "node_modules/@babel/plugin-transform-template-literals": {"version": "7.26.8", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.26.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/webidl-conversions": {"version": "6.1.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10.4"}}, "node_modules/@isaacs/cliui/node_modules/ansi-styles": {"version": "6.2.1", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/esprima": {"version": "4.0.1", "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/@types/graceful-fs": {"version": "4.1.9", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/statuses": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/@babel/plugin-syntax-async-generators": {"version": "7.8.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/nth-check": {"license": "BSD-2-<PERSON><PERSON>", "version": "^2.1.1", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/is-regexp": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/esrecurse": {"version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/jest-watch-typeahead/node_modules/jest-watcher": {"version": "28.1.3", "license": "MIT", "dependencies": {"@jest/test-result": "^28.1.3", "@jest/types": "^28.1.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^28.1.3", "string-length": "^4.0.1"}, "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/@babel/plugin-transform-shorthand-properties": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/has-bigints": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@types/js-levenshtein": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/@types/js-levenshtein/-/js-levenshtein-1.1.3.tgz", "integrity": "sha512-jd+Q+sD20Qfu9e2aEXogiO3vpOC1PYJOUdyN9gvs4Qrvkg4wF43L5OhqrPeokdv8TL0/mXoYfpkcoGZMNN2pkQ==", "dev": true, "license": "MIT"}, "node_modules/csso": {"version": "4.2.0", "license": "MIT", "dependencies": {"css-tree": "^1.1.2"}, "engines": {"node": ">=8.0.0"}}, "node_modules/eslint/node_modules/p-limit": {"version": "3.1.0", "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@types/babel__template": {"version": "7.4.4", "license": "MIT", "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/check-types": {"version": "11.2.3", "license": "MIT"}, "node_modules/browser-process-hrtime": {"version": "1.0.0", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/run-parallel": {"version": "1.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/@webassemblyjs/wast-printer": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@xtuc/long": "4.2.2"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "license": "MIT"}, "node_modules/set-function-name": {"version": "2.0.2", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/js-levenshtein": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/js-levenshtein/-/js-levenshtein-1.1.6.tgz", "integrity": "sha512-X2BB11YZtrRqY4EnQcLX5Rh373zbK4alC1FW7D7MBhL2gtcC17cTnr6DmfHZeS0s2rTHjUTMMHfG7gO8SSdw+g==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/indent-string": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/readable-stream": {"version": "3.6.2", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/@csstools/postcss-is-pseudo-class": {"version": "2.0.7", "license": "CC0-1.0", "dependencies": {"@csstools/selector-specificity": "^2.0.0", "postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/@csstools/postcss-progressive-custom-properties": {"version": "1.3.0", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.3"}}, "node_modules/lilconfig": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/@playwright/test": {"version": "1.51.1", "resolved": "https://registry.npmjs.org/@playwright/test/-/test-1.51.1.tgz", "integrity": "sha512-nM+kEaTSAoVlXmMPH10017vn3FSiFqr/bh4fKg9vmAdMfd9SDqRZNvPSiAHADc/itWak+qPvMPZQOPwCBW7k7Q==", "dev": true, "license": "Apache-2.0", "dependencies": {"playwright": "1.51.1"}, "bin": {"playwright": "cli.js"}, "engines": {"node": ">=18"}}, "node_modules/eslint-config-react-app": {"version": "7.0.1", "license": "MIT", "dependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@rushstack/eslint-patch": "^1.1.0", "@typescript-eslint/eslint-plugin": "^5.5.0", "@typescript-eslint/parser": "^5.5.0", "babel-preset-react-app": "^10.0.1", "confusing-browser-globals": "^1.0.11", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jest": "^25.3.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.27.1", "eslint-plugin-react-hooks": "^4.3.0", "eslint-plugin-testing-library": "^5.0.1"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"eslint": "^8.0.0"}}, "node_modules/@babel/plugin-transform-exponentiation-operator": {"version": "7.26.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/defaults": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz", "integrity": "sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==", "dev": true, "license": "MIT", "dependencies": {"clone": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cookie-signature": {"version": "1.0.6", "license": "MIT"}, "node_modules/@webassemblyjs/wasm-edit": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/helper-wasm-section": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-opt": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1", "@webassemblyjs/wast-printer": "1.14.1"}}, "node_modules/@webassemblyjs/helper-wasm-section": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/wasm-gen": "1.14.1"}}, "node_modules/mini-css-extract-plugin": {"version": "2.9.2", "license": "MIT", "dependencies": {"schema-utils": "^4.0.0", "tapable": "^2.2.1"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/@types/ws": {"version": "8.18.0", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/w3c-xmlserializer": {"version": "2.0.0", "license": "MIT", "dependencies": {"xml-name-validator": "^3.0.0"}, "engines": {"node": ">=10"}}, "node_modules/node-fetch/node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==", "dev": true, "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi": {"version": "8.1.0", "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/@types/d3-selection": {"version": "3.0.11", "resolved": "https://registry.npmjs.org/@types/d3-selection/-/d3-selection-3.0.11.tgz", "integrity": "sha512-bhAXu23DJWsrI45xafYpkQ4NtcKMwWnAC/vKrd2l+nxMFuvOT3XMYTIj2opv8vq8AO5Yh7Qac/nSeP/3zjTK0w==", "license": "MIT"}, "node_modules/decimal.js": {"version": "10.5.0", "license": "MIT"}, "node_modules/unique-string": {"version": "2.0.0", "license": "MIT", "dependencies": {"crypto-random-string": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/yocto-queue": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/workbox-range-requests": {"version": "6.6.0", "license": "MIT", "dependencies": {"workbox-core": "6.6.0"}}, "node_modules/postcss-modules-local-by-default/node_modules/postcss-selector-parser": {"version": "7.1.0", "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/@types/istanbul-reports": {"version": "3.0.4", "license": "MIT", "dependencies": {"@types/istanbul-lib-report": "*"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flatted": {"version": "3.3.3", "license": "ISC"}, "node_modules/@babel/plugin-transform-for-of": {"version": "7.26.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.26.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/accepts": {"version": "1.3.8", "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/@emotion/utils": {"version": "1.4.2", "resolved": "https://registry.npmjs.org/@emotion/utils/-/utils-1.4.2.tgz", "integrity": "sha512-3vLclRofFziIa3J2wDh9jjbkUz9qk5Vi3IZ/FSTKViB0k+ef0fPV7dYrUIugbgupYDx7v9ud/SjrtEP8Y4xLoA==", "license": "MIT"}, "node_modules/resolve-url-loader/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/cliui": {"version": "7.0.4", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/bytes": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/workbox-build/node_modules/whatwg-url": {"version": "7.1.0", "license": "MIT", "dependencies": {"lodash.sortby": "^4.7.0", "tr46": "^1.0.1", "webidl-conversions": "^4.0.2"}}, "node_modules/pretty-bytes": {"version": "5.6.0", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@types/d3-time": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/@types/d3-time/-/d3-time-3.0.4.tgz", "integrity": "sha512-yuzZug1nkAAaBlBBikKZTgzCeA+k1uy4ZFwWANOfKw5z5LRhV0gNA7gNkKm7HoK+HRN0wX3EkxGk0fpbWhmB7g==", "license": "MIT"}, "node_modules/is-generator-fn": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@types/react-transition-group": {"version": "4.4.12", "resolved": "https://registry.npmjs.org/@types/react-transition-group/-/react-transition-group-4.4.12.tgz", "integrity": "sha512-8TV6R3h2j7a91c+1DXdJi3Syo69zzIZbz7Lg5tORM5LEJG7X/E6a1V3drRyBRZq7/utz7A+c4OgYLiLcYGHG6w==", "license": "MIT", "peerDependencies": {"@types/react": "*"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/v8-to-istanbul": {"version": "8.1.1", "license": "ISC", "dependencies": {"@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^1.6.0", "source-map": "^0.7.3"}, "engines": {"node": ">=10.12.0"}}, "node_modules/spdy-transport": {"version": "3.0.0", "license": "MIT", "dependencies": {"debug": "^4.1.0", "detect-node": "^2.0.4", "hpack.js": "^2.1.6", "obuf": "^1.1.2", "readable-stream": "^3.0.6", "wbuf": "^1.7.3"}}, "node_modules/compression/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/eslint-plugin-import/node_modules/debug": {"version": "3.2.7", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/@pkgjs/parseargs": {"version": "0.11.0", "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@babel/preset-env/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/d3-color": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/d3-color/-/d3-color-3.1.0.tgz", "integrity": "sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/dom-converter": {"version": "0.2.0", "license": "MIT", "dependencies": {"utila": "~0.4"}}, "node_modules/cssesc": {"version": "3.0.0", "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/is-typedarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/@babel/plugin-transform-modules-amd": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@types/babel__core": {"version": "7.20.5", "license": "MIT", "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/supports-hyperlinks": {"version": "2.3.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@babel/plugin-syntax-jsx": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/rollup-plugin-terser": {"version": "7.0.2", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.10.4", "jest-worker": "^26.2.1", "serialize-javascript": "^4.0.0", "terser": "^5.0.0"}, "peerDependencies": {"rollup": "^2.0.0"}}, "node_modules/inflight": {"version": "1.0.6", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/terminal-link": {"version": "2.1.1", "license": "MIT", "dependencies": {"ansi-escapes": "^4.2.1", "supports-hyperlinks": "^2.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/fill-range": {"version": "7.1.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/get-package-type": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/body-parser/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/core-js-compat": {"version": "3.41.0", "license": "MIT", "dependencies": {"browserslist": "^4.24.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/tailwindcss/node_modules/lilconfig": {"version": "3.1.3", "license": "MIT", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antonk52"}}, "node_modules/postcss": {"license": "MIT", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "version": "^8.4.31", "dependencies": {"nanoid": "^3.3.8", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/eslint/node_modules/argparse": {"version": "2.0.1", "license": "Python-2.0"}, "node_modules/mz": {"version": "2.7.0", "license": "MIT", "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}}, "node_modules/@testing-library/react/node_modules/@testing-library/dom": {"version": "8.20.1", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.10.4", "@babel/runtime": "^7.12.5", "@types/aria-query": "^5.0.1", "aria-query": "5.1.3", "chalk": "^4.1.0", "dom-accessibility-api": "^0.5.9", "lz-string": "^1.5.0", "pretty-format": "^27.0.2"}, "engines": {"node": ">=12"}}, "node_modules/postcss-place": {"version": "7.0.5", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/jake": {"version": "10.9.2", "license": "Apache-2.0", "dependencies": {"async": "^3.2.3", "chalk": "^4.0.2", "filelist": "^1.0.4", "minimatch": "^3.1.2"}, "bin": {"jake": "bin/cli.js"}, "engines": {"node": ">=10"}}, "node_modules/postcss-load-config/node_modules/yaml": {"version": "2.7.0", "license": "ISC", "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14"}}, "node_modules/bonjour-service": {"version": "1.3.0", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "multicast-dns": "^7.2.5"}}, "node_modules/d3-random": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/d3-random/-/d3-random-3.0.1.tgz", "integrity": "sha512-FXMe9GfxTxqd5D6jFsQ+DJ8BJS4E/fT5mqqdjovykEB2oFbTMDVdg1MGFxfQW+FBOGoB++k8swBrgwSHT1cUXQ==", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/@typescript-eslint/scope-manager": {"version": "5.62.0", "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.62.0", "@typescript-eslint/visitor-keys": "5.62.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/preset-env": {"version": "7.26.9", "license": "MIT", "dependencies": {"@babel/plugin-syntax-import-attributes": "^7.26.0", "@babel/plugin-bugfix-firefox-class-in-computed-class-key": "^7.25.9", "@babel/plugin-syntax-unicode-sets-regex": "^7.18.6", "@babel/plugin-transform-async-generator-functions": "^7.26.8", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "^7.25.9", "@babel/plugin-transform-spread": "^7.25.9", "semver": "^6.3.1", "@babel/plugin-transform-parameters": "^7.25.9", "@babel/plugin-transform-object-super": "^7.25.9", "@babel/plugin-transform-unicode-regex": "^7.25.9", "@babel/helper-compilation-targets": "^7.26.5", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@babel/plugin-transform-dotall-regex": "^7.25.9", "@babel/plugin-transform-destructuring": "^7.25.9", "@babel/plugin-transform-duplicate-named-capturing-groups-regex": "^7.25.9", "@babel/plugin-transform-dynamic-import": "^7.25.9", "@babel/plugin-proposal-private-property-in-object": "7.21.0-placeholder-for-preset-env.2", "@babel/plugin-transform-logical-assignment-operators": "^7.25.9", "@babel/plugin-transform-unicode-escapes": "^7.25.9", "@babel/plugin-transform-computed-properties": "^7.25.9", "@babel/plugin-transform-block-scoped-functions": "^7.26.5", "@babel/plugin-transform-new-target": "^7.25.9", "@babel/plugin-transform-optional-chaining": "^7.25.9", "@babel/plugin-transform-class-properties": "^7.25.9", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "^7.25.9", "@babel/plugin-transform-private-methods": "^7.25.9", "babel-plugin-polyfill-corejs2": "^0.4.10", "@babel/plugin-transform-property-literals": "^7.25.9", "@babel/plugin-syntax-import-assertions": "^7.26.0", "@babel/plugin-transform-class-static-block": "^7.26.0", "babel-plugin-polyfill-corejs3": "^0.11.0", "@babel/plugin-transform-private-property-in-object": "^7.25.9", "@babel/plugin-transform-template-literals": "^7.26.8", "@babel/plugin-transform-object-rest-spread": "^7.25.9", "@babel/plugin-transform-named-capturing-groups-regex": "^7.25.9", "@babel/plugin-transform-sticky-regex": "^7.25.9", "@babel/plugin-transform-block-scoping": "^7.25.9", "@babel/plugin-transform-numeric-separator": "^7.25.9", "@babel/plugin-transform-shorthand-properties": "^7.25.9", "@babel/plugin-transform-exponentiation-operator": "^7.26.3", "@babel/plugin-transform-nullish-coalescing-operator": "^7.26.6", "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": "^7.25.9", "core-js-compat": "^3.40.0", "@babel/compat-data": "^7.26.8", "@babel/plugin-transform-json-strings": "^7.25.9", "@babel/plugin-transform-regexp-modifiers": "^7.26.0", "@babel/plugin-transform-classes": "^7.25.9", "@babel/preset-modules": "0.1.6-no-external-plugins", "@babel/plugin-bugfix-safari-class-field-initializer-scope": "^7.25.9", "@babel/plugin-transform-member-expression-literals": "^7.25.9", "@babel/plugin-transform-arrow-functions": "^7.25.9", "@babel/plugin-transform-function-name": "^7.25.9", "@babel/plugin-transform-duplicate-keys": "^7.25.9", "@babel/plugin-transform-regenerator": "^7.25.9", "@babel/plugin-transform-literals": "^7.25.9", "@babel/plugin-transform-modules-systemjs": "^7.25.9", "@babel/helper-plugin-utils": "^7.26.5", "@babel/plugin-transform-optional-catch-binding": "^7.25.9", "@babel/plugin-transform-modules-umd": "^7.25.9", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/plugin-transform-typeof-symbol": "^7.26.7", "@babel/plugin-transform-unicode-sets-regex": "^7.25.9", "@babel/plugin-transform-async-to-generator": "^7.25.9", "babel-plugin-polyfill-regenerator": "^0.6.1", "@babel/helper-validator-option": "^7.25.9", "@babel/plugin-transform-unicode-property-regex": "^7.25.9", "@babel/plugin-transform-for-of": "^7.26.9", "@babel/plugin-transform-modules-amd": "^7.25.9", "@babel/plugin-transform-reserved-words": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/filelist/node_modules/minimatch": {"version": "5.1.6", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/resolve-url-loader/node_modules/postcss": {"license": "MIT", "version": "^8.4.31", "engines": {"node": ">=6.0.0"}, "dependencies": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}}, "node_modules/spdy": {"version": "4.0.2", "license": "MIT", "dependencies": {"debug": "^4.1.0", "handle-thing": "^2.0.0", "http-deceiver": "^1.2.7", "select-hose": "^2.0.0", "spdy-transport": "^3.0.0"}, "engines": {"node": ">=6.0.0"}}, "node_modules/postcss-font-variant": {"version": "5.0.0", "license": "MIT", "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/detect-newline": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/postcss-reduce-initial": {"version": "5.1.2", "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "caniuse-api": "^3.0.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/eslint/node_modules/js-yaml": {"version": "4.1.0", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/events": {"version": "3.3.0", "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/@babel/helper-string-parser": {"version": "7.25.9", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/regjsparser/node_modules/jsesc": {"version": "3.0.2", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/@types/trusted-types": {"version": "2.0.7", "license": "MIT"}, "node_modules/has-proto": {"version": "1.2.0", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@jest/test-result": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/console": "^27.5.1", "@jest/types": "^27.5.1", "@types/istanbul-lib-coverage": "^2.0.0", "collect-v8-coverage": "^1.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/test-exclude": {"version": "6.0.0", "license": "ISC", "dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}, "engines": {"node": ">=8"}}, "node_modules/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/traverse": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/clone": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz", "integrity": "sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/eslint-plugin-flowtype": {"version": "8.0.3", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"lodash": "^4.17.21", "string-natural-compare": "^3.0.1"}, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"@babel/plugin-syntax-flow": "^7.14.5", "@babel/plugin-transform-react-jsx": "^7.14.9", "eslint": "^8.1.0"}}, "node_modules/buffer-from": {"version": "1.1.2", "license": "MIT"}, "node_modules/gzip-size": {"version": "6.0.0", "license": "MIT", "dependencies": {"duplexer": "^0.1.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/language-tags": {"version": "1.0.9", "license": "MIT", "dependencies": {"language-subtag-registry": "^0.3.20"}, "engines": {"node": ">=0.10"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "license": "MIT"}, "node_modules/@mui/material/node_modules/react-is": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz", "integrity": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==", "license": "MIT"}, "node_modules/@jridgewell/source-map": {"version": "0.3.6", "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "node_modules/p-locate": {"version": "4.1.0", "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/clean-css/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/universalify": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/stack-utils/node_modules/escape-string-regexp": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/d3-dispatch": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/d3-dispatch/-/d3-dispatch-3.0.1.tgz", "integrity": "sha512-rzUyPU/S7rwUflMyLc1ETDeBj0NRuHKKAcvukozwhshr6g6c5d8zh4c2gQjY2bZ0dXeGLWc1PF174P2tVvKhfg==", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/nwsapi": {"version": "2.2.19", "license": "MIT"}, "node_modules/array-includes": {"version": "3.1.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.4", "is-string": "^1.0.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/postcss-svgo/node_modules/mdn-data": {"version": "2.0.14", "license": "CC0-1.0"}, "node_modules/damerau-levenshtein": {"version": "1.0.8", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/source-map-js": {"version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/string.prototype.trim": {"version": "1.2.10", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-data-property": "^1.1.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-object-atoms": "^1.0.0", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@types/connect": {"version": "3.4.38", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/anymatch": {"version": "3.1.3", "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/slash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cli-width": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/cli-width/-/cli-width-3.0.0.tgz", "integrity": "sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==", "dev": true, "license": "ISC", "engines": {"node": ">= 10"}}, "node_modules/ee-first": {"version": "1.1.1", "license": "MIT"}, "node_modules/@babel/plugin-transform-classes": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-compilation-targets": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-replace-supers": "^7.25.9", "@babel/traverse": "^7.25.9", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/w3c-hr-time": {"version": "1.0.2", "license": "MIT", "dependencies": {"browser-process-hrtime": "^1.0.0"}}, "node_modules/@csstools/postcss-font-format-keywords": {"version": "1.0.1", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/d3-timer": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/d3-timer/-/d3-timer-3.0.1.tgz", "integrity": "sha512-ndfJ/JxxMd3nw31uyKoY2naivF+r29V+Lc0svZxe1JvvIRmi8hUsrMvdOwgS1o6uBHmiz91geQ0ylPP0aj1VUA==", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/regenerate": {"version": "1.4.2", "license": "MIT"}, "node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/@mui/system": {"version": "5.17.1", "resolved": "https://registry.npmjs.org/@mui/system/-/system-5.17.1.tgz", "integrity": "sha512-aJrmGfQpyF0U4D4xYwA6ueVtQcEMebET43CUmKMP7e7iFh3sMIF3sBR0l8Urb4pqx1CBjHAaWgB0ojpND4Q3Jg==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.23.9", "@mui/private-theming": "^5.17.1", "@mui/styled-engine": "^5.16.14", "@mui/types": "~7.2.15", "@mui/utils": "^5.17.1", "clsx": "^2.1.0", "csstype": "^3.1.3", "prop-types": "^15.8.1"}, "engines": {"node": ">=12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@emotion/react": "^11.5.0", "@emotion/styled": "^11.3.0", "@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}, "@types/react": {"optional": true}}}, "node_modules/node-fetch/node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/jest-resolve-dependencies": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "jest-regex-util": "^27.5.1", "jest-snapshot": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/cssnano": {"version": "5.1.15", "license": "MIT", "dependencies": {"cssnano-preset-default": "^5.2.14", "lilconfig": "^2.0.3", "yaml": "^1.10.2"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/cssnano"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/ansi-html": {"version": "0.0.9", "engines": ["node >= 0.8.0"], "license": "Apache-2.0", "bin": {"ansi-html": "bin/ansi-html"}}, "node_modules/stop-iteration-iterator": {"version": "1.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "internal-slot": "^1.1.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/clean-css": {"version": "5.3.3", "license": "MIT", "dependencies": {"source-map": "~0.6.0"}, "engines": {"node": ">= 10.0"}}, "node_modules/webpack-manifest-plugin": {"version": "4.1.1", "license": "MIT", "dependencies": {"tapable": "^2.0.0", "webpack-sources": "^2.2.0"}, "engines": {"node": ">=12.22.0"}, "peerDependencies": {"webpack": "^4.44.2 || ^5.47.0"}}, "node_modules/serve-index/node_modules/setprototypeof": {"version": "1.1.0", "license": "ISC"}, "node_modules/reusify": {"version": "1.1.0", "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/filelist": {"version": "1.0.4", "license": "Apache-2.0", "dependencies": {"minimatch": "^5.0.1"}}, "node_modules/express": {"version": "4.21.2", "license": "MIT", "dependencies": {"type-is": "~1.6.18", "safe-buffer": "5.2.1", "finalhandler": "1.3.1", "fresh": "0.5.2", "body-parser": "1.20.3", "content-type": "~1.0.4", "send": "0.19.0", "cookie": "0.7.1", "methods": "~1.1.2", "proxy-addr": "~2.0.7", "accepts": "~1.3.8", "range-parser": "~1.2.1", "on-finished": "2.4.1", "debug": "2.6.9", "encodeurl": "~2.0.0", "etag": "~1.8.1", "path-to-regexp": "0.1.12", "statuses": "2.0.1", "parseurl": "~1.3.3", "setprototypeof": "1.2.0", "merge-descriptors": "1.0.3", "vary": "~1.1.2", "serve-static": "1.16.2", "content-disposition": "0.5.4", "escape-html": "~1.0.3", "http-errors": "2.0.0", "cookie-signature": "1.0.6", "utils-merge": "1.0.1", "array-flatten": "1.1.1", "depd": "2.0.0", "qs": "6.13.0"}, "engines": {"node": ">= 0.10.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/workbox-build/node_modules/ajv": {"version": "8.17.1", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/identity-obj-proxy": {"version": "3.0.0", "license": "MIT", "dependencies": {"harmony-reflect": "^1.4.6"}, "engines": {"node": ">=4"}}, "node_modules/@types/d3-polygon": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/@types/d3-polygon/-/d3-polygon-3.0.2.tgz", "integrity": "sha512-ZuWOtMaHCkN9xoeEMr1ubW2nGWsp4nIql+OPQRstu4ypeZ+zk3YKqQT0CXVe/PYqrKpZAi+J9mTs05TKwjXSRA==", "license": "MIT"}, "node_modules/postcss-normalize-charset": {"version": "5.1.0", "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/@svgr/babel-preset": {"version": "5.5.0", "license": "MIT", "dependencies": {"@svgr/babel-plugin-add-jsx-attribute": "^5.4.0", "@svgr/babel-plugin-remove-jsx-attribute": "^5.4.0", "@svgr/babel-plugin-remove-jsx-empty-expression": "^5.0.1", "@svgr/babel-plugin-replace-jsx-attribute-value": "^5.0.1", "@svgr/babel-plugin-svg-dynamic-title": "^5.4.0", "@svgr/babel-plugin-svg-em-dimensions": "^5.4.0", "@svgr/babel-plugin-transform-react-native-svg": "^5.4.0", "@svgr/babel-plugin-transform-svg-component": "^5.5.0"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@babel/plugin-transform-sticky-regex": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/jiti": {"version": "1.21.7", "license": "MIT", "bin": {"jiti": "bin/jiti.js"}}, "node_modules/@types/d3-interpolate": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/@types/d3-interpolate/-/d3-interpolate-3.0.4.tgz", "integrity": "sha512-mgLPETlrpVV1YRJIglr4Ez47g7Yxjl1lj7YKsiMCb27VJH9W8NVM6Bb9d8kkpG/uAQS5AmbA48q2IAolKKo1MA==", "license": "MIT", "dependencies": {"@types/d3-color": "*"}}, "node_modules/@jest/globals": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/environment": "^27.5.1", "@jest/types": "^27.5.1", "expect": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/d3-polygon": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/d3-polygon/-/d3-polygon-3.0.1.tgz", "integrity": "sha512-3vbA7vXYwfe1SYhED++fPUQlWSYTTGmFmQiany/gdbiWgU/iEyQzyymwL9SkJjFFuCS4902BSzewVGsHHmHtXg==", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/external-editor/node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/emittery": {"version": "0.8.1", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/emittery?sponsor=1"}}, "node_modules/is-docker": {"version": "2.2.1", "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/core-js": {"version": "3.41.0", "hasInstallScript": true, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/@eslint/eslintrc": {"version": "2.1.4", "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@jest/console": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "@types/node": "*", "chalk": "^4.0.0", "jest-message-util": "^27.5.1", "jest-util": "^27.5.1", "slash": "^3.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/postcss-merge-longhand": {"version": "5.1.7", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0", "stylehacks": "^5.1.1"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/@types/jest": {"version": "27.5.2", "license": "MIT", "dependencies": {"jest-matcher-utils": "^27.0.0", "pretty-format": "^27.0.0"}}, "node_modules/@webassemblyjs/helper-buffer": {"version": "1.14.1", "license": "MIT"}, "node_modules/dequal": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@svgr/webpack": {"version": "5.5.0", "license": "MIT", "dependencies": {"@babel/core": "^7.12.3", "@babel/plugin-transform-react-constant-elements": "^7.12.1", "@babel/preset-env": "^7.12.1", "@babel/preset-react": "^7.12.5", "@svgr/core": "^5.5.0", "@svgr/plugin-jsx": "^5.5.0", "@svgr/plugin-svgo": "^5.5.0", "loader-utils": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/@babel/helper-wrap-function": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/template": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/thenify-all": {"version": "1.6.0", "license": "MIT", "dependencies": {"thenify": ">= 3.1.0 < 4"}, "engines": {"node": ">=0.8"}}, "node_modules/brace-expansion": {"version": "1.1.11", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/msw/node_modules/path-to-regexp": {"version": "6.3.0", "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-6.3.0.tgz", "integrity": "sha512-Yhpw4T9C6hPpgPeA28us07OJeqZ5EzQTkbfwuhsUg0c237RomFoETJgmp2sa3F/41gfLE6G5cqcYwznmeEeOlQ==", "dev": true, "license": "MIT"}, "node_modules/@babel/plugin-syntax-unicode-sets-regex": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-private-property-in-object": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/csso/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/postcss-normalize": {"version": "10.0.1", "license": "CC0-1.0", "dependencies": {"@csstools/normalize.css": "*", "postcss-browser-comments": "^4", "sanitize.css": "*"}, "engines": {"node": ">= 12"}, "peerDependencies": {"browserslist": ">= 4", "postcss": ">= 8"}}, "node_modules/array.prototype.flat": {"version": "1.3.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/detect-node": {"version": "2.1.0", "license": "MIT"}, "node_modules/emoji-regex": {"version": "9.2.2", "license": "MIT"}, "node_modules/encodeurl": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/@babel/plugin-transform-numeric-separator": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/hoist-non-react-statics": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "integrity": "sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.7.0"}}, "node_modules/@babel/plugin-proposal-nullish-coalescing-operator": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/css-select-base-adapter": {"version": "0.1.1", "license": "MIT"}, "node_modules/postcss-media-minmax": {"version": "5.0.0", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.25.9", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/pkg-up": {"version": "3.1.0", "license": "MIT", "dependencies": {"find-up": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@babel/plugin-transform-optional-chaining": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/ajv-formats/node_modules/json-schema-traverse": {"version": "1.0.0", "license": "MIT"}, "node_modules/@nicolo-ribaudo/eslint-scope-5-internals/node_modules/eslint-scope": {"version": "5.1.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "node_modules/cssstyle/node_modules/cssom": {"version": "0.3.8", "license": "MIT"}, "node_modules/@babel/helper-optimise-call-expression": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/compression": {"version": "1.8.0", "license": "MIT", "dependencies": {"bytes": "3.1.2", "compressible": "~2.0.18", "debug": "2.6.9", "negotiator": "~0.6.4", "on-headers": "~1.0.2", "safe-buffer": "5.2.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/msw/node_modules/yargs": {"version": "17.7.2", "resolved": "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz", "integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==", "dev": true, "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/finalhandler": {"version": "1.3.1", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/postcss-flexbugs-fixes": {"version": "5.0.2", "license": "MIT", "peerDependencies": {"postcss": "^8.1.4"}}, "node_modules/shebang-command": {"version": "2.0.0", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/regex-parser": {"version": "2.3.1", "license": "MIT"}, "node_modules/eslint/node_modules/locate-path": {"version": "6.0.0", "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/babel-loader/node_modules/schema-utils": {"version": "2.7.1", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.5", "ajv": "^6.12.4", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 8.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/on-headers": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/follow-redirects": {"version": "1.15.9", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/@babel/plugin-transform-react-jsx": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/babel-preset-react-app/node_modules/@babel/plugin-proposal-private-property-in-object": {"version": "7.21.11", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.21.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/hpack.js": {"version": "2.1.6", "license": "MIT", "dependencies": {"inherits": "^2.0.1", "obuf": "^1.0.0", "readable-stream": "^2.0.1", "wbuf": "^1.1.0"}}, "node_modules/@types/connect-history-api-fallback": {"version": "1.5.4", "license": "MIT", "dependencies": {"@types/express-serve-static-core": "*", "@types/node": "*"}}, "node_modules/domelementtype": {"version": "2.3.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/@babel/helper-remap-async-to-generator": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-wrap-function": "^7.25.9", "@babel/traverse": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/d3-quadtree": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/d3-quadtree/-/d3-quadtree-3.0.1.tgz", "integrity": "sha512-04xDrxQTDTCFwP5H6hRhsRcb9xxv2RzkcsygFzmkSIOJy3PeRJP7sNk3VRIbKXcog561P9oU0/rVH6vDROAgUw==", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/concat-map": {"version": "0.0.1", "license": "MIT"}, "node_modules/content-type": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/react-dev-utils/node_modules/locate-path": {"version": "6.0.0", "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@babel/helper-create-class-features-plugin": {"version": "7.26.9", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-member-expression-to-functions": "^7.25.9", "@babel/helper-optimise-call-expression": "^7.25.9", "@babel/helper-replace-supers": "^7.26.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9", "@babel/traverse": "^7.26.9", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/micromatch": {"version": "4.0.8", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/@svgr/core": {"version": "5.5.0", "license": "MIT", "dependencies": {"@svgr/plugin-jsx": "^5.5.0", "camelcase": "^6.2.0", "cosmiconfig": "^7.0.0"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/tsutils/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/react-dev-utils": {"version": "12.0.1", "license": "MIT", "dependencies": {"loader-utils": "^3.2.0", "gzip-size": "^6.0.0", "globby": "^11.0.4", "pkg-up": "^3.1.0", "immer": "^9.0.7", "shell-quote": "^1.7.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0", "filesize": "^8.0.6", "cross-spawn": "^7.0.3", "prompts": "^2.4.2", "recursive-readdir": "^2.2.2", "address": "^1.1.2", "is-root": "^2.1.0", "chalk": "^4.1.2", "detect-port-alt": "^1.1.6", "browserslist": "^4.18.1", "global-modules": "^2.0.0", "find-up": "^5.0.0", "open": "^8.4.0", "escape-string-regexp": "^4.0.0", "react-error-overlay": "^6.0.11", "@babel/code-frame": "^7.16.0", "fork-ts-checker-webpack-plugin": "^6.5.0"}, "engines": {"node": ">=14"}}, "node_modules/is-interactive": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-interactive/-/is-interactive-1.0.0.tgz", "integrity": "sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/dir-glob": {"version": "3.0.1", "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/content-disposition": {"version": "0.5.4", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/jest-get-type": {"version": "27.5.1", "license": "MIT", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/utila": {"version": "0.4.0", "license": "MIT"}, "node_modules/serve-index/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/randombytes": {"version": "2.1.0", "license": "MIT", "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/param-case": {"version": "3.0.4", "license": "MIT", "dependencies": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/svgo/node_modules/nth-check": {"license": "BSD-2-<PERSON><PERSON>", "version": "^2.1.1", "dependencies": {"boolbase": "~1.0.0"}}, "node_modules/type-fest": {"version": "0.21.3", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/send": {"version": "0.19.0", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/chart.js": {"version": "4.4.8", "resolved": "https://registry.npmjs.org/chart.js/-/chart.js-4.4.8.tgz", "integrity": "sha512-IkGZlVpXP+83QpMm4uxEiGqSI7jFizwVtF3+n5Pc3k7sMO+tkd0qxh2OzLhenM0K80xtmAONWGBn082EiBQSDA==", "license": "MIT", "dependencies": {"@kurkle/color": "^0.3.0"}, "engines": {"pnpm": ">=8"}}, "node_modules/unicode-match-property-value-ecmascript": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/raf": {"version": "3.4.1", "license": "MIT", "dependencies": {"performance-now": "^2.1.0"}}, "node_modules/postcss-svgo/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/d3-ease": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/d3-ease/-/d3-ease-3.0.1.tgz", "integrity": "sha512-wR/XK3D3XcLIZwpbvQwQ5fK+8Ykds1ip7A2Txe0yxncXSdq1L9skcG7blcedkOX+ZcgxGAmLX1FrRGbADwzi0w==", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "node_modules/fast-glob": {"version": "3.3.3", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/kind-of": {"version": "6.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/d3-shape": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/d3-shape/-/d3-shape-3.2.0.tgz", "integrity": "sha512-SaLBuwGm3MOViRq2ABk3eLoxwZELpH6zhl3FbAoJ7Vm1gofKx6El1Ib5z23NUEhF9AsGl7y+dzLe5Cw2AArGTA==", "license": "ISC", "dependencies": {"d3-path": "^3.1.0"}, "engines": {"node": ">=12"}}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "license": "MIT"}, "node_modules/postcss-opacity-percentage": {"version": "1.1.3", "funding": [{"type": "kofi", "url": "https://ko-fi.com/mrcgrtz"}, {"type": "liberapay", "url": "https://liberapay.com/mrcgrtz"}], "license": "MIT", "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/d3-brush": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/d3-brush/-/d3-brush-3.0.0.tgz", "integrity": "sha512-ALnjWlVYkXsVIGlOsuWH1+3udkYFI48Ljihfnh8FZPF2QS9o+PzGLBslO0PjzVoHLZ2KCVgAM8NVkXPJB2aNnQ==", "license": "ISC", "dependencies": {"d3-dispatch": "1 - 3", "d3-drag": "2 - 3", "d3-interpolate": "1 - 3", "d3-selection": "3", "d3-transition": "3"}, "engines": {"node": ">=12"}}, "node_modules/hoopy": {"version": "0.1.4", "license": "MIT", "engines": {"node": ">= 6.0.0"}}, "node_modules/serve-index/node_modules/statuses": {"version": "1.5.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/symbol-tree": {"version": "3.2.4", "license": "MIT"}, "node_modules/istanbul-lib-report": {"version": "3.0.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^4.0.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}}, "node_modules/@emotion/styled": {"version": "11.11.0", "resolved": "https://registry.npmjs.org/@emotion/styled/-/styled-11.11.0.tgz", "integrity": "sha512-hM5Nnvu9P3midq5aaXj4I+lnSfNi7Pmd4EWk1fOZ3pxookaQTNew6bp4JaCBYM4HVFZF9g7UjJmsUmC2JlxOng==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.3", "@emotion/babel-plugin": "^11.11.0", "@emotion/is-prop-valid": "^1.2.1", "@emotion/serialize": "^1.1.2", "@emotion/use-insertion-effect-with-fallbacks": "^1.0.1", "@emotion/utils": "^1.2.1"}, "peerDependencies": {"@emotion/react": "^11.0.0-rc.0", "react": ">=16.8.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/merge2": {"version": "1.4.1", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@mui/utils/node_modules/react-is": {"version": "19.0.0", "resolved": "https://registry.npmjs.org/react-is/-/react-is-19.0.0.tgz", "integrity": "sha512-H91OHcwjZsbq3ClIDHMzBShc1rotbfACdWENsmEf0IFvZ3FgGPtdHMcsv45bQ1hAbgdfiA8SnxTKfDS+x/8m2g==", "license": "MIT"}, "node_modules/npm-force-resolutions": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/npm-force-resolutions/-/npm-force-resolutions-0.0.10.tgz", "integrity": "sha512-Jscex+xIU6tw3VsyrwxM1TeT+dd9Fd3UOMAjy6J1TMpuYeEqg4LQZnATQO5vjPrsARm3und6zc6Dii/GUyRE5A==", "dev": true, "license": "MIT", "dependencies": {"json-format": "^1.0.1", "source-map-support": "^0.5.5", "xmlhttprequest": "^1.8.0"}, "bin": {"npm-force-resolutions": "index.js"}}, "node_modules/@mui/base/node_modules/react-is": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz", "integrity": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==", "license": "MIT"}, "node_modules/data-urls": {"version": "2.0.0", "license": "MIT", "dependencies": {"abab": "^2.0.3", "whatwg-mimetype": "^2.3.0", "whatwg-url": "^8.0.0"}, "engines": {"node": ">=10"}}, "node_modules/esutils": {"version": "2.0.3", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/natural-compare-lite": {"version": "1.4.0", "license": "MIT"}, "node_modules/clsx": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz", "integrity": "sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@jest/types": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "chalk": "^4.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/jest-mock": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "@types/node": "*"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@types/d3-color": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/@types/d3-color/-/d3-color-3.1.3.tgz", "integrity": "sha512-iO90scth9WAbmgv7ogoq57O9YpKmFBbmoEoCHDB2xMBY0+/KVrqAaCDyCE16dUspeOvIxFFRI+0sEtqDqy2b4A==", "license": "MIT"}, "node_modules/define-data-property": {"version": "1.1.4", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/jest-resolve": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^27.5.1", "jest-pnp-resolver": "^1.2.2", "jest-util": "^27.5.1", "jest-validate": "^27.5.1", "resolve": "^1.20.0", "resolve.exports": "^1.1.0", "slash": "^3.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/@types/d3-fetch": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/@types/d3-fetch/-/d3-fetch-3.0.7.tgz", "integrity": "sha512-fTAfNmxSb9SOWNB9IoG5c8Hg6R+AzUHDRlsXsDZsNp6sxAEOP0tkP3gKkNSO/qmHPoBFTxNrjDprVHDQDvo5aA==", "license": "MIT", "dependencies": {"@types/d3-dsv": "*"}}, "node_modules/sprintf-js": {"version": "1.0.3", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/yallist": {"version": "3.1.1", "license": "ISC"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "license": "MIT"}, "node_modules/serialize-javascript": {"version": "6.0.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"randombytes": "^2.1.0"}}, "node_modules/eslint-plugin-jsx-a11y/node_modules/aria-query": {"version": "5.3.2", "license": "Apache-2.0", "engines": {"node": ">= 0.4"}}, "node_modules/@svgr/babel-plugin-replace-jsx-attribute-value": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/camel-case": {"version": "4.1.2", "license": "MIT", "dependencies": {"pascal-case": "^3.1.2", "tslib": "^2.0.3"}}, "node_modules/jest-environment-node": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/environment": "^27.5.1", "@jest/fake-timers": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "jest-mock": "^27.5.1", "jest-util": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/deep-is": {"version": "0.1.4", "license": "MIT"}, "node_modules/process-nextick-args": {"version": "2.0.1", "license": "MIT"}, "node_modules/postcss-load-config": {"version": "4.0.2", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"lilconfig": "^3.0.0", "yaml": "^2.3.4"}, "engines": {"node": ">= 14"}, "peerDependencies": {"postcss": ">=8.0.9", "ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"postcss": {"optional": true}, "ts-node": {"optional": true}}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/postcss-nesting": {"version": "10.2.0", "license": "CC0-1.0", "dependencies": {"@csstools/selector-specificity": "^2.0.0", "postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/d3-format": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/d3-format/-/d3-format-3.1.0.tgz", "integrity": "sha512-YyUI6AEuY/Wpt8KWLgZHsIU86atmikuoOmCfommt0LYHiQSPjvX2AcFc38PX0CBpr2RCyZhjex+NS/LPOv6YqA==", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.5.1", "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@webassemblyjs/helper-wasm-bytecode": {"version": "1.13.2", "license": "MIT"}, "node_modules/@mui/private-theming": {"version": "5.17.1", "resolved": "https://registry.npmjs.org/@mui/private-theming/-/private-theming-5.17.1.tgz", "integrity": "sha512-XMxU0NTYcKqdsG8LRmSoxERPXwMbp16sIXPcLVgLGII/bVNagX0xaheWAwFv8+zDK7tI3ajllkuD3GZZE++ICQ==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.23.9", "@mui/utils": "^5.17.1", "prop-types": "^15.8.1"}, "engines": {"node": ">=12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/optionator": {"version": "0.9.4", "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/normalize-range": {"version": "0.1.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/@jest/core": {"version": "27.5.1", "license": "MIT", "dependencies": {"jest-config": "^27.5.1", "jest-runner": "^27.5.1", "emittery": "^0.8.1", "strip-ansi": "^6.0.0", "jest-haste-map": "^27.5.1", "jest-snapshot": "^27.5.1", "jest-changed-files": "^27.5.1", "@jest/reporters": "^27.5.1", "jest-runtime": "^27.5.1", "jest-watcher": "^27.5.1", "@types/node": "*", "@jest/console": "^27.5.1", "jest-resolve-dependencies": "^27.5.1", "chalk": "^4.0.0", "@jest/test-result": "^27.5.1", "jest-util": "^27.5.1", "slash": "^3.0.0", "jest-message-util": "^27.5.1", "jest-regex-util": "^27.5.1", "jest-validate": "^27.5.1", "@jest/transform": "^27.5.1", "exit": "^0.1.2", "rimraf": "^3.0.0", "jest-resolve": "^27.5.1", "@jest/types": "^27.5.1", "graceful-fs": "^4.2.9", "ansi-escapes": "^4.2.1", "micromatch": "^4.0.4"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/postcss-svgo/node_modules/commander": {"version": "7.2.0", "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/sucrase": {"version": "3.35.0", "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.2", "commander": "^4.0.0", "glob": "^10.3.10", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/no-case": {"version": "3.0.4", "license": "MIT", "dependencies": {"lower-case": "^2.0.2", "tslib": "^2.0.3"}}, "node_modules/send/node_modules/encodeurl": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/file-loader": {"version": "6.2.0", "license": "MIT", "dependencies": {"loader-utils": "^2.0.0", "schema-utils": "^3.0.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}}, "node_modules/@eslint/js": {"version": "8.57.1", "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/postcss-reduce-transforms": {"version": "5.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/async-function": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/@nicolo-ribaudo/eslint-scope-5-internals/node_modules/estraverse": {"version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/@babel/plugin-syntax-flow": {"version": "7.26.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/import-fresh": {"version": "3.3.1", "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/css-minimizer-webpack-plugin/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/eslint/node_modules/globals": {"version": "13.24.0", "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/negotiator": {"version": "0.6.4", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/eslint-webpack-plugin/node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/resolve-url-loader": {"version": "4.0.0", "license": "MIT", "dependencies": {"adjust-sourcemap-loader": "^4.0.0", "convert-source-map": "^1.7.0", "loader-utils": "^2.0.0", "postcss": "^7.0.35", "source-map": "0.6.1"}, "engines": {"node": ">=8.9"}, "peerDependencies": {"rework": "1.0.1", "rework-visit": "1.0.0"}, "peerDependenciesMeta": {"rework": {"optional": true}, "rework-visit": {"optional": true}}}, "node_modules/crypto-random-string": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ci-info": {"version": "3.9.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/type-is": {"version": "1.6.18", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.26.5", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/escape-string-regexp": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/webpack-sources": {"version": "3.2.3", "license": "MIT", "engines": {"node": ">=10.13.0"}}, "node_modules/postcss-loader": {"version": "6.2.1", "license": "MIT", "dependencies": {"cosmiconfig": "^7.0.0", "klona": "^2.0.5", "semver": "^7.3.5"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"postcss": "^7.0.0 || ^8.0.1", "webpack": "^5.0.0"}}, "node_modules/style-loader": {"version": "3.3.4", "license": "MIT", "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/workbox-expiration": {"version": "6.6.0", "license": "MIT", "dependencies": {"idb": "^7.0.1", "workbox-core": "6.6.0"}}, "node_modules/workbox-build/node_modules/json-schema-traverse": {"version": "1.0.0", "license": "MIT"}, "node_modules/jest-watch-typeahead/node_modules/@types/yargs": {"version": "17.0.33", "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@types/d3-force": {"version": "3.0.10", "resolved": "https://registry.npmjs.org/@types/d3-force/-/d3-force-3.0.10.tgz", "integrity": "sha512-ZYeSaCF3p73RdOKcjj+swRlZfnYpK1EbaDiYICEEp5Q6sUiqFaFQ9qgoshp5CzIyyb/yD09kD9o2zEltCexlgw==", "license": "MIT"}, "node_modules/@babel/plugin-transform-modules-umd": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/co": {"version": "4.6.0", "license": "MIT", "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}}, "node_modules/type-check": {"version": "0.4.0", "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/svgo/node_modules/color-name": {"version": "1.1.3", "license": "MIT"}, "node_modules/import-fresh/node_modules/resolve-from": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/hpack.js/node_modules/readable-stream": {"version": "2.3.8", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/makeerror": {"version": "1.0.12", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tmpl": "1.0.5"}}, "node_modules/typed-array-length": {"version": "1.0.7", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "is-typed-array": "^1.1.13", "possible-typed-array-names": "^1.0.0", "reflect.getprototypeof": "^1.0.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@webassemblyjs/wasm-parser": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-api-error": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "node_modules/braces": {"version": "3.0.3", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/@webassemblyjs/ast": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/helper-numbers": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2"}}, "node_modules/unicode-property-aliases-ecmascript": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/write-file-atomic": {"version": "3.0.3", "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^3.1.5"}}, "node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/@svgr/babel-plugin-transform-react-native-svg": {"version": "5.4.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/serve-index/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/is-potential-custom-element-name": {"version": "1.0.1", "license": "MIT"}, "node_modules/is-unicode-supported": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz", "integrity": "sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/d3-axis": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/d3-axis/-/d3-axis-3.0.0.tgz", "integrity": "sha512-IH5tgjV4jE/GhHkRV0HiVYPDtvfjHQlQfJHs0usq7M30XcSBvOotpmH1IgkcXsO/5gEQZD43B//fc7SRT5S+xw==", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/prop-types": {"version": "15.8.1", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/d3-interpolate": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/d3-interpolate/-/d3-interpolate-3.0.1.tgz", "integrity": "sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==", "license": "ISC", "dependencies": {"d3-color": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/is-wsl": {"version": "2.2.0", "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/q": {"version": "1.5.1", "license": "MIT", "engines": {"node": ">=0.6.0", "teleport": ">=0.2.0"}}, "node_modules/set-cookie-parser": {"version": "2.7.1", "resolved": "https://registry.npmjs.org/set-cookie-parser/-/set-cookie-parser-2.7.1.tgz", "integrity": "sha512-IOc8uWeOZgnb3ptbCURJWNjWUPcO3ZnTTdzsurqERrP6nPyv+paC55vJM0LpOlT2ne+Ix+9+CRG1MNLlyZ4GjQ==", "dev": true, "license": "MIT"}, "node_modules/es-object-atoms": {"version": "1.1.1", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/open": {"version": "8.4.2", "license": "MIT", "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@zxing/text-encoding": {"version": "0.9.0", "resolved": "https://registry.npmjs.org/@zxing/text-encoding/-/text-encoding-0.9.0.tgz", "integrity": "sha512-U/4aVJ2mxI0aDNI8Uq0wEhMgY+u4CNtEb0om3+y3+niDAsoTCOB33UF0sxpzqzdqXLqmvc+vZyAt4O8pPdfkwA==", "dev": true, "license": "(Unlicense OR Apache-2.0)", "optional": true}, "node_modules/case-sensitive-paths-webpack-plugin": {"version": "2.4.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/is-core-module": {"version": "2.16.1", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.values": {"version": "1.2.1", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/postcss-convert-values": {"version": "5.1.3", "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/static-eval/node_modules/type-check": {"version": "0.3.2", "license": "MIT", "dependencies": {"prelude-ls": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/postcss-normalize-unicode": {"version": "5.1.1", "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/@adobe/css-tools": {"version": "4.4.2", "license": "MIT"}, "node_modules/escodegen": {"version": "2.1.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esprima": "^4.0.1", "estraverse": "^5.2.0", "esutils": "^2.0.2"}, "bin": {"escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js"}, "engines": {"node": ">=6.0"}, "optionalDependencies": {"source-map": "~0.6.1"}}, "node_modules/jest-haste-map": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/types": "^27.5.1", "@types/graceful-fs": "^4.1.2", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.9", "jest-regex-util": "^27.5.1", "jest-serializer": "^27.5.1", "jest-util": "^27.5.1", "jest-worker": "^27.5.1", "micromatch": "^4.0.4", "walker": "^1.0.7"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}}, "node_modules/resolve-url-loader/node_modules/convert-source-map": {"version": "1.9.0", "license": "MIT"}, "node_modules/postcss-discard-duplicates": {"version": "5.1.0", "license": "MIT", "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/@isaacs/cliui/node_modules/strip-ansi": {"version": "7.1.0", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/@types/d3-delaunay": {"version": "6.0.4", "resolved": "https://registry.npmjs.org/@types/d3-delaunay/-/d3-delaunay-6.0.4.tgz", "integrity": "sha512-ZMaSKu4THYCU6sV64Lhg6qjf1orxBthaC161plr5KuPHo3CNm8DTHiLw/5Eq2b6TsNP0W0iJrUOFscY6Q450Hw==", "license": "MIT"}, "node_modules/@babel/plugin-transform-react-display-name": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/headers-polyfill": {"version": "3.2.5", "resolved": "https://registry.npmjs.org/headers-polyfill/-/headers-polyfill-3.2.5.tgz", "integrity": "sha512-tUCGvt191vNSQgttSyJoibR+VO+I6+iCHIUdhzEMJKE+EAL8BwCN7fUOZlY4ofOelNHsK+gEjxB/B+9N3EWtdA==", "dev": true, "license": "MIT"}, "node_modules/css-loader": {"version": "6.11.0", "license": "MIT", "dependencies": {"icss-utils": "^5.1.0", "postcss": "^8.4.33", "postcss-modules-extract-imports": "^3.1.0", "postcss-modules-local-by-default": "^4.0.5", "postcss-modules-scope": "^3.2.0", "postcss-modules-values": "^4.0.0", "postcss-value-parser": "^4.2.0", "semver": "^7.5.4"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}}, "node_modules/typed-array-buffer": {"version": "1.0.3", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}}, "node_modules/make-dir/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/path-parse": {"version": "1.0.7", "license": "MIT"}, "node_modules/@babel/plugin-transform-class-static-block": {"version": "7.26.0", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.12.0"}}, "node_modules/outvariant": {"version": "1.4.3", "resolved": "https://registry.npmjs.org/outvariant/-/outvariant-1.4.3.tgz", "integrity": "sha512-+Sl2<PERSON>rvtsoajRDKCE5/dBz4DIvHXQQnAxtQTF04OJxY0+DyZXSo5P5Bb7XYWOh81syohlYL24hbDwxedPUJCA==", "dev": true, "license": "MIT"}, "node_modules/@typescript-eslint/type-utils": {"version": "5.62.0", "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "5.62.0", "@typescript-eslint/utils": "5.62.0", "debug": "^4.3.4", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/csso/node_modules/css-tree": {"version": "1.1.3", "license": "MIT", "dependencies": {"mdn-data": "2.0.14", "source-map": "^0.6.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@types/testing-library__jest-dom": {"version": "5.14.9", "license": "MIT", "dependencies": {"@types/jest": "*"}}, "node_modules/get-own-enumerable-property-symbols": {"version": "3.0.2", "license": "ISC"}, "node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/jest-watch-typeahead/node_modules/jest-regex-util": {"version": "28.0.2", "license": "MIT", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}}, "node_modules/postcss-overflow-shorthand": {"version": "3.0.4", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-svgo": {"version": "5.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0", "svgo": "^2.7.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/queue-microtask": {"version": "1.2.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/svgo/node_modules/domutils": {"version": "1.7.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "0", "domelementtype": "1"}}, "node_modules/ipaddr.js": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/imurmurhash": {"version": "0.1.4", "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/hoist-non-react-statics/node_modules/react-is": {"version": "16.13.1", "resolved": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==", "license": "MIT"}, "node_modules/@rtsao/scc": {"version": "1.1.0", "license": "MIT"}, "node_modules/picocolors": {"version": "1.1.1", "license": "ISC"}, "node_modules/stylehacks": {"version": "5.1.1", "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "postcss-selector-parser": "^6.0.4"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/postcss-normalize-display-values": {"version": "5.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/proxy-addr": {"version": "2.0.7", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/string-width-cjs/node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/@babel/plugin-transform-react-jsx-development": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/plugin-transform-react-jsx": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/react-dev-utils/node_modules/find-up": {"version": "5.0.0", "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@typescript-eslint/types": {"version": "5.62.0", "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "node_modules/regexpu-core": {"version": "6.2.0", "license": "MIT", "dependencies": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.2.0", "regjsgen": "^0.8.0", "regjsparser": "^0.12.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "engines": {"node": ">=4"}}, "node_modules/form-data": {"version": "4.0.2", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/css-tree": {"version": "1.0.0-alpha.37", "license": "MIT", "dependencies": {"mdn-data": "2.0.4", "source-map": "^0.6.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/postcss-svgo/node_modules/css-tree": {"version": "1.1.3", "license": "MIT", "dependencies": {"mdn-data": "2.0.14", "source-map": "^0.6.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/which": {"version": "2.0.2", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/string.prototype.trimend": {"version": "1.0.9", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@xmldom/xmldom": {"version": "0.8.10", "resolved": "https://registry.npmjs.org/@xmldom/xmldom/-/xmldom-0.8.10.tgz", "integrity": "sha512-2WALfTl4xo2SkGCYRt6rDTFfk9R1czmBvUQy12gK2KuRKIpWEhcbbzy8EZXtz/jkRqHX8bFEc6FC1HjX4TUWYw==", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/is-data-view": {"version": "1.0.2", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "is-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/webpack-manifest-plugin/node_modules/webpack-sources": {"version": "2.3.1", "license": "MIT", "dependencies": {"source-list-map": "^2.0.1", "source-map": "^0.6.1"}, "engines": {"node": ">=10.13.0"}}, "node_modules/isarray": {"version": "2.0.5", "license": "MIT"}, "node_modules/object-inspect": {"version": "1.13.4", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/postcss-minify-params": {"version": "5.1.4", "license": "MIT", "dependencies": {"browserslist": "^4.21.4", "cssnano-utils": "^3.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/@types/d3-transition": {"version": "3.0.9", "resolved": "https://registry.npmjs.org/@types/d3-transition/-/d3-transition-3.0.9.tgz", "integrity": "sha512-uZS5shfxzO3rGlu0cC3bjmMFKsXv+SmZZcgp0KD22ts4uGXp5EVYGzu/0YdwZeKmddhcAccYtREJKkPfXkZuCg==", "license": "MIT", "dependencies": {"@types/d3-selection": "*"}}, "node_modules/underscore": {"version": "1.12.1", "license": "MIT"}, "node_modules/@eslint/eslintrc/node_modules/type-fest": {"version": "0.20.2", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@babel/plugin-proposal-class-properties": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/scheduler": {"version": "0.23.2", "license": "MIT", "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/immer": {"version": "9.0.21", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/immer"}}, "node_modules/@babel/core": {"version": "7.26.10", "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.26.2", "@babel/generator": "^7.26.10", "@babel/helper-compilation-targets": "^7.26.5", "@babel/helper-module-transforms": "^7.26.0", "@babel/helpers": "^7.26.10", "@babel/parser": "^7.26.10", "@babel/template": "^7.26.9", "@babel/traverse": "^7.26.10", "@babel/types": "^7.26.10", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/d3-path": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/d3-path/-/d3-path-3.1.0.tgz", "integrity": "sha512-p3KP5HCf/bvjBSSKuXid6Zqijx7wIfNW+J/maPs+iwR35at5JCbLUT0LzF1cnjbCHWhqzQTIN2Jpe8pRebIEFQ==", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/d3-fetch": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/d3-fetch/-/d3-fetch-3.0.1.tgz", "integrity": "sha512-kpkQIM20n3oLVBKGg6oHrUchHM3xODkTzjMoj7aWQFq5QEM+R6E4WkzT5+tojDY7yjez8KgCBRoj4aEr99Fdqw==", "license": "ISC", "dependencies": {"d3-dsv": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/@sinonjs/fake-timers": {"version": "8.1.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sinonjs/commons": "^1.7.0"}}, "node_modules/@jest/reporters/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/parse5": {"version": "6.0.1", "license": "MIT"}, "node_modules/collect-v8-coverage": {"version": "1.0.2", "license": "MIT"}, "node_modules/svg-parser": {"version": "2.0.4", "license": "MIT"}, "node_modules/css.escape": {"version": "1.5.1", "license": "MIT"}, "node_modules/postcss-modules-scope": {"version": "3.2.1", "license": "ISC", "dependencies": {"postcss-selector-parser": "^7.0.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/@types/json5": {"version": "0.0.29", "license": "MIT"}, "node_modules/path-type": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-watch-typeahead/node_modules/@jest/console/node_modules/slash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@mui/base": {"version": "5.0.0-beta.0", "resolved": "https://registry.npmjs.org/@mui/base/-/base-5.0.0-beta.0.tgz", "integrity": "sha512-ap+juKvt8R8n3cBqd/pGtZydQ4v2I/hgJKnvJRGjpSh3RvsvnDHO4rXov8MHQlH6VqpOekwgilFLGxMZjNTucA==", "deprecated": "This package has been replaced by @base-ui-components/react", "license": "MIT", "dependencies": {"@babel/runtime": "^7.21.0", "@emotion/is-prop-valid": "^1.2.0", "@mui/types": "^7.2.4", "@mui/utils": "^5.12.3", "@popperjs/core": "^2.11.7", "clsx": "^1.2.1", "prop-types": "^15.8.1", "react-is": "^18.2.0"}, "engines": {"node": ">=12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mui"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0", "react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@types/d3-random": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/@types/d3-random/-/d3-random-3.0.3.tgz", "integrity": "sha512-Imagg1vJ3y76Y2ea0871wpabqp613+8/r0mCLEBfdtqC7xMSfj9idOnmBYyMoULfHePJyxMAw3nWhJxzc+LFwQ==", "license": "MIT"}, "node_modules/sucrase/node_modules/glob": {"version": "10.4.5", "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@types/d3-drag": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/@types/d3-drag/-/d3-drag-3.0.7.tgz", "integrity": "sha512-HE3jVKlzU9AaMazNufooRJ5ZpWmLIoc90A37WU2JMmeq28w1FQqCZswHZ3xR+SuxYftzHq6WU6KJHvqxKzTxxQ==", "license": "MIT", "dependencies": {"@types/d3-selection": "*"}}, "node_modules/boolbase": {"version": "1.0.0", "license": "ISC"}, "node_modules/babel-plugin-transform-react-remove-prop-types": {"version": "0.4.24", "license": "MIT"}, "node_modules/@babel/helper-create-class-features-plugin/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-stream": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/svgo/node_modules/ansi-styles": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/postcss-gap-properties": {"version": "3.0.5", "license": "CC0-1.0", "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-nested": {"version": "6.2.0", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.1.1"}, "engines": {"node": ">=12.0"}, "peerDependencies": {"postcss": "^8.2.14"}}, "node_modules/postcss-minify-selectors": {"version": "5.2.1", "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.5"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/react-transition-group": {"version": "4.4.5", "resolved": "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.5.tgz", "integrity": "sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/runtime": "^7.5.5", "dom-helpers": "^5.0.1", "loose-envify": "^1.4.0", "prop-types": "^15.6.2"}, "peerDependencies": {"react": ">=16.6.0", "react-dom": ">=16.6.0"}}, "node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}, "node_modules/jest": {"version": "27.5.1", "license": "MIT", "dependencies": {"@jest/core": "^27.5.1", "import-local": "^3.0.2", "jest-cli": "^27.5.1"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/postcss-clamp": {"version": "4.1.0", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": ">=7.6.0"}, "peerDependencies": {"postcss": "^8.4.6"}}, "node_modules/@babel/plugin-transform-nullish-coalescing-operator": {"version": "7.26.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.26.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/language-subtag-registry": {"version": "0.3.23", "license": "CC0-1.0"}, "node_modules/pkg-up/node_modules/p-locate": {"version": "3.0.0", "license": "MIT", "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/postcss-custom-properties": {"version": "12.1.11", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/d3-scale-chromatic": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/d3-scale-chromatic/-/d3-scale-chromatic-3.1.0.tgz", "integrity": "sha512-A3s5PWiZ9YCXFye1o246KoscMWqf8BsD9eRiJ3He7C9OBaxKhAd5TFCdEx/7VbKtxxTsu//1mMJFrEt572cEyQ==", "license": "ISC", "dependencies": {"d3-color": "1 - 3", "d3-interpolate": "1 - 3"}, "engines": {"node": ">=12"}}, "node_modules/object.assign": {"version": "4.1.7", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/inquirer/node_modules/wrap-ansi": {"version": "6.2.0", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz", "integrity": "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@jest/reporters": {"version": "27.5.1", "license": "MIT", "dependencies": {"istanbul-lib-source-maps": "^4.0.0", "jest-haste-map": "^27.5.1", "istanbul-lib-coverage": "^3.0.0", "collect-v8-coverage": "^1.0.0", "source-map": "^0.6.0", "istanbul-lib-instrument": "^5.1.0", "string-length": "^4.0.1", "@types/node": "*", "v8-to-istanbul": "^8.1.0", "@jest/console": "^27.5.1", "chalk": "^4.0.0", "@jest/test-result": "^27.5.1", "jest-util": "^27.5.1", "slash": "^3.0.0", "istanbul-reports": "^3.1.3", "@jest/transform": "^27.5.1", "exit": "^0.1.2", "glob": "^7.1.2", "jest-resolve": "^27.5.1", "@jest/types": "^27.5.1", "terminal-link": "^2.0.0", "istanbul-lib-report": "^3.0.0", "@bcoe/v8-coverage": "^0.2.3", "graceful-fs": "^4.2.9", "jest-worker": "^27.5.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/@types/parse-json": {"version": "4.0.2", "license": "MIT"}, "node_modules/html-encoding-sniffer": {"version": "2.0.1", "license": "MIT", "dependencies": {"whatwg-encoding": "^1.0.5"}, "engines": {"node": ">=10"}}, "node_modules/msw/node_modules/cookie": {"license": "MIT", "version": "^0.7.0", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/string-length": {"version": "4.0.2", "license": "MIT", "dependencies": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@types/react": {"version": "18.3.19", "license": "MIT", "dependencies": {"@types/prop-types": "*", "csstype": "^3.0.2"}}, "node_modules/fork-ts-checker-webpack-plugin/node_modules/cosmiconfig": {"version": "6.0.0", "license": "MIT", "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.1.0", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.7.2"}, "engines": {"node": ">=8"}}, "node_modules/eslint/node_modules/find-up": {"version": "5.0.0", "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/postcss-color-rebeccapurple": {"version": "7.1.1", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/terser/node_modules/commander": {"version": "2.20.3", "license": "MIT"}, "node_modules/msw": {"version": "1.3.5", "resolved": "https://registry.npmjs.org/msw/-/msw-1.3.5.tgz", "integrity": "sha512-nG3fpmBXxFbKSIdk6miPuL3KjU6WMxgoW4tG1YgnP1M+TRG3Qn7b7R0euKAHq4vpwARHb18ZyfZljSxsTnMX2w==", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"is-node-process": "^1.2.0", "yargs": "^17.3.1", "@mswjs/interceptors": "^0.17.10", "outvariant": "^1.4.0", "cookie": "^0.4.2", "js-levenshtein": "^1.1.6", "inquirer": "^8.2.0", "graphql": "^16.8.1", "@types/cookie": "^0.4.1", "node-fetch": "^2.6.7", "chalk": "^4.1.1", "chokidar": "^3.4.2", "@open-draft/until": "^1.0.3", "path-to-regexp": "^6.3.0", "type-fest": "^2.19.0", "@mswjs/cookies": "^0.2.2", "headers-polyfill": "3.2.5", "@types/js-levenshtein": "^1.1.1", "strict-event-emitter": "^0.4.3"}, "bin": {"msw": "cli/index.js"}, "engines": {"node": ">=14"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mswjs"}, "peerDependencies": {"typescript": ">= 4.4.x"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@types/send": {"version": "0.17.4", "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@svgr/babel-plugin-remove-jsx-attribute": {"version": "5.4.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/eslint-plugin-react": {"version": "7.37.4", "license": "MIT", "dependencies": {"semver": "^6.3.1", "doctrine": "^2.1.0", "object.values": "^1.2.1", "resolve": "^2.0.0-next.5", "estraverse": "^5.3.0", "hasown": "^2.0.2", "array.prototype.findlast": "^1.2.5", "string.prototype.repeat": "^1.0.0", "array.prototype.flatmap": "^1.3.3", "object.fromentries": "^2.0.8", "array.prototype.tosorted": "^1.1.4", "es-iterator-helpers": "^1.2.1", "minimatch": "^3.1.2", "object.entries": "^1.1.8", "string.prototype.matchall": "^4.0.12", "jsx-ast-utils": "^2.4.1 || ^3.0.0", "array-includes": "^3.1.8", "prop-types": "^15.8.1"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7"}}, "node_modules/@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/iconv-lite": {"version": "0.6.3", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/jest-watch-typeahead/node_modules/strip-ansi": {"version": "7.1.0", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/find-root": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/find-root/-/find-root-1.1.0.tgz", "integrity": "sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng==", "license": "MIT"}, "node_modules/prelude-ls": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/@babel/plugin-transform-json-strings": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/array.prototype.reduce": {"version": "1.0.8", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-array-method-boxes-properly": "^1.0.0", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "is-string": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/workbox-streams": {"version": "6.6.0", "license": "MIT", "dependencies": {"workbox-core": "6.6.0", "workbox-routing": "6.6.0"}}, "node_modules/jsonpointer": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/@svgr/plugin-svgo": {"version": "5.5.0", "license": "MIT", "dependencies": {"cosmiconfig": "^7.0.0", "deepmerge": "^4.2.2", "svgo": "^1.2.2"}, "engines": {"node": ">=10"}, "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}}, "node_modules/html-entities": {"version": "2.5.2", "funding": [{"type": "github", "url": "https://github.com/sponsors/mdevils"}, {"type": "patreon", "url": "https://patreon.com/mdevils"}], "license": "MIT"}, "node_modules/ws": {"version": "7.5.10", "license": "MIT", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/workbox-cacheable-response": {"version": "6.6.0", "license": "MIT", "dependencies": {"workbox-core": "6.6.0"}}, "node_modules/text-table": {"version": "0.2.0", "license": "MIT"}, "node_modules/figures/node_modules/escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/@babel/helper-member-expression-to-functions": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-transform-react-pure-annotations": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@rollup/pluginutils/node_modules/@types/estree": {"version": "0.0.39", "license": "MIT"}, "node_modules/dom-serializer": {"version": "1.4.1", "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}, "funding": {"url": "https://github.com/cheeriojs/dom-serializer?sponsor=1"}}, "node_modules/yaml": {"version": "1.10.2", "license": "ISC", "engines": {"node": ">= 6"}}, "node_modules/prompts": {"version": "2.4.2", "license": "MIT", "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "engines": {"node": ">= 6"}}, "node_modules/coa/node_modules/ansi-styles": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/workbox-recipes": {"version": "6.6.0", "license": "MIT", "dependencies": {"workbox-cacheable-response": "6.6.0", "workbox-core": "6.6.0", "workbox-expiration": "6.6.0", "workbox-precaching": "6.6.0", "workbox-routing": "6.6.0", "workbox-strategies": "6.6.0"}}, "node_modules/bl": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/bl/-/bl-4.1.0.tgz", "integrity": "sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==", "dev": true, "license": "MIT", "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/xml-name-validator": {"version": "3.0.0", "license": "Apache-2.0"}, "node_modules/glob-to-regexp": {"version": "0.4.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/@babel/plugin-transform-reserved-words": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-spread": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@types/express/node_modules/@types/express-serve-static-core": {"version": "4.19.6", "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/mdn-data": {"version": "2.0.4", "license": "CC0-1.0"}, "node_modules/ajv-formats/node_modules/ajv": {"version": "8.17.1", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/postcss-minify-gradients": {"version": "5.1.1", "license": "MIT", "dependencies": {"colord": "^2.9.1", "cssnano-utils": "^3.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "peerDependencies": {"postcss": "^8.2.15"}}, "node_modules/qs": {"version": "6.13.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/svgo/node_modules/supports-color": {"version": "5.5.0", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/postcss-attribute-case-insensitive": {"version": "5.0.2", "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/http-proxy": {"version": "1.18.1", "license": "MIT", "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/restore-cursor": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-3.1.0.tgz", "integrity": "sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==", "dev": true, "license": "MIT", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=8"}}}}