/**
 * Webpack configuration to enhance error handling and logging
 * This can be imported and merged with the main webpack configuration
 */
const path = require('path');
const BuildErrorLoggerPlugin = require('./src/utils/error/buildErrorLogger');

/**
 * Function to merge with existing webpack configuration
 * @param {Object} existingConfig - The existing webpack configuration
 * @returns {Object} - The enhanced webpack configuration
 */
function enhanceErrorLogging(existingConfig) {
  // Ensure we have a plugins array
  if (!existingConfig.plugins) {
    existingConfig.plugins = [];
  }

  // Add our build error logger plugin
  existingConfig.plugins.push(
    new BuildErrorLoggerPlugin({
      logFilePath: path.resolve(__dirname, 'logs/build-errors.log'),
      notifyDevelopers: process.env.NODE_ENV === 'production'
    })
  );

  // Enhanced error reporting in development mode
  if (process.env.NODE_ENV !== 'production') {
    // Set devtool for better source maps
    existingConfig.devtool = 'source-map';
    
    // Add module no parse for large dependencies if needed
    if (!existingConfig.module) {
      existingConfig.module = {};
    }
    
    // Configure stats for better error reporting
    existingConfig.stats = {
      errorDetails: true,
      children: true,
      logging: 'verbose',
      ...existingConfig.stats
    };
  }
  
  // Add error handling to development server
  if (existingConfig.devServer) {
    existingConfig.devServer = {
      ...existingConfig.devServer,
      client: {
        ...existingConfig.devServer?.client,
        overlay: {
          errors: true,
          warnings: false
        },
        logging: 'error'
      },
      // Log errors to console in a more readable format
      onListening: function(devServer) {
        const originalOnListening = existingConfig.devServer?.onListening;
        if (typeof originalOnListening === 'function') {
          originalOnListening(devServer);
        }
        
        console.log('Dev server is now listening. Enhanced error logging enabled.');
      }
    };
  }

  return existingConfig;
}

// Export the enhancer function
module.exports = enhanceErrorLogging;

// Example of direct usage:
/*
const createWebpackConfig = require('react-scripts/config/webpack.config');

module.exports = (webpackEnv) => {
  const baseConfig = createWebpackConfig(webpackEnv);
  return enhanceErrorLogging(baseConfig);
};
*/ 