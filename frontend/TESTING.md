# Testing Strategy for CertRats Frontend

This document outlines how to use and extend the testing frameworks set up for the CertRats frontend application.

## Testing Stack

- **Unit and Component Testing**: React Testing Library with Jest
- **End-to-End Testing**: Playwright
- **API Mocking**: Mock Service Worker (MSW)

## Running Tests

### Component Tests (React Testing Library)

```bash
# Run all component tests
npm test

# Run tests with coverage report
npm run test:coverage

# Run a specific test file
npm test -- -t "App Component"
```

### E2E Tests (Playwright)

```bash
# Install browsers (first time only)
npx playwright install

# Run all E2E tests
npm run test:e2e

# Run with UI mode
npm run test:e2e:ui

# Run specific browser
npx playwright test --project=chromium

# Run a specific test file
npx playwright test certification-explorer.spec.ts
```

## Writing Tests

### Component Test Best Practices

1. **Test from a user's perspective**

   - Use accessibility queries (`getByRole`, `getByLabelText`) instead of implementation details
   - Test user interactions through events like click, change, etc.

2. **Use data-testid for difficult-to-query elements**

   ```jsx
   <div data-testid="certification-card">...</div>
   ```

3. **Test component behavior, not implementation details**

   - Focus on what users see and can interact with
   - Avoid testing internal state or method calls

4. **Mock API responses**

   ```jsx
   // Using MSW to mock API responses
   import { rest } from "msw";
   import { setupServer } from "msw/node";

   const server = setupServer(
     rest.get("/api/certifications", (req, res, ctx) => {
       return res(ctx.json({ certifications: [...mockData] }));
     })
   );

   beforeAll(() => server.listen());
   afterEach(() => server.resetHandlers());
   afterAll(() => server.close());
   ```

### E2E Test Best Practices

1. **Focus on critical user flows**

   - Login/logout
   - Certification search and filtering
   - Language switching
   - Form submissions

2. **Use data-testid attributes for reliable selectors**

   ```html
   <button data-testid="domain-filter-button">Network Security</button>
   ```

3. **Group related tests using test.describe**

   ```typescript
   test.describe("User Authentication", () => {
     // Login/logout tests
   });

   test.describe("Certification Explorer", () => {
     // Filter/search tests
   });
   ```

4. **Handle state setup**

   - Use beforeEach hooks to set up common state
   - Consider API calls for complex state setup instead of UI interactions

5. **Visual regression testing**
   ```typescript
   // Compare screenshot with baseline
   await expect(page).toHaveScreenshot("certification-list.png");
   ```

## CI Integration

The testing configuration is optimized for CI environments:

- Retries for flaky tests
- HTML reports for test results
- Parallelization settings
- Proper error handling

## Best Practices for Test Maintenance

1. **Keep selectors resilient to UI changes**

   - Prefer role, label, and text content over CSS selectors
   - Use data-testid for elements without semantic meaning

2. **Isolate tests from each other**

   - Clean up state between tests
   - Don't rely on test order

3. **Monitor test performance**

   - Regularly check test run times
   - Optimize slow tests

4. **Maintain test coverage**
   - Aim for 80%+ code coverage
   - Ensure all critical paths have E2E coverage
