FROM node:18-alpine

WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies with special focus on Material UI
RUN npm install --legacy-peer-deps
RUN npm install --save @mui/icons-material@5.11.16 @mui/material@5.13.0 @emotion/react@11.11.0 @emotion/styled@11.11.0

# Copy the rest of the application
COPY . .

# Expose the port
EXPOSE 3000

# Start the development server
CMD ["npm", "start"] 