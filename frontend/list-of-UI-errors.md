Compiled with problems:
×
ERROR in src/pages/CareerPath/index.tsx:258:15
TS2322: Type '{ id: number; name: string; type: "certification"; level: "beginner" | "intermediate" | "advanced" | "expert"; }[]' is not assignable to type 'NodeData[]'.
  Type '{ id: number; name: string; type: "certification"; level: "beginner" | "intermediate" | "advanced" | "expert"; }' is missing the following properties from type 'NodeData': x, y, fx, fy
    256 |
    257 |         // Create nodes for all certifications
  > 258 |         const certNodes: NodeData[] = certifications
        |               ^^^^^^^^^
    259 |             .filter(cert => {
    260 |                 // Apply filters
    261 |                 if (filterLevel !== 'all' && cert.level !== filterLevel) return false;
// FIXED: Added x, y, fx, fy properties to all certNodes

ERROR in src/pages/CareerPath/index.tsx:273:15
TS2322: Type '{ id: number; name: string; type: "role"; children: number[]; }[]' is not assignable to type 'NodeData[]'.
  Type '{ id: number; name: string; type: "role"; children: number[]; }' is missing the following properties from type 'NodeData': x, y, fx, fy
    271 |
    272 |         // Create nodes for roles (always show all roles)
  > 273 |         const roleNodes: NodeData[] = roles.map(role => ({
        |               ^^^^^^^^^
    274 |             id: role.id,
    275 |             name: role.title,
    276 |             type: 'role',
// FIXED: Added x, y, fx, fy properties to all roleNodes

ERROR in src/pages/CareerPath/index.tsx:364:56
TS2345: Argument of type '(d: NodeData) => 50 | 40' is not assignable to parameter of type 'number | ((node: SimulationNodeDatum, i: number, nodes: SimulationNodeDatum[]) => number)'.
  Type '(d: NodeData) => 50 | 40' is not assignable to type '(node: SimulationNodeDatum, i: number, nodes: SimulationNodeDatum[]) => number'.
    Types of parameters 'd' and 'node' are incompatible.
      Type 'SimulationNodeDatum' is missing the following properties from type 'NodeData': id, name, type
    362 |             .force('charge', d3.forceManyBody().strength(-200))
    363 |             .force('center', d3.forceCenter(width / 2, height / 2))
  > 364 |             .force('collide', d3.forceCollide().radius((d: NodeData) => (d.type === 'role' ? 50 : 40)))
        |                                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    365 |             .force('x', d3.forceX(width / 2).strength(0.05))
    366 |             .force('y', d3.forceY(height / 2).strength(0.05))
    367 |             .velocityDecay(0.5);
// FIXED: Used type assertion in the collide force radius function

ERROR in src/pages/Dashboard/Dashboard.tsx:13:19
TS2339: Property 'feedback' does not exist on type 'APIClient'.
    11 |     // Setup the feedback functionality
    12 |     const handleSubmitFeedback = async (data: FeedbackData) => {
  > 13 |         await api.feedback.submit(data);
       |                   ^^^^^^^^
    14 |     };
    15 |
    16 |     const { openFeedback, FeedbackPopupComponent } = useFeedback(handleSubmitFeedback);
// FIXED: Added feedback API interface and implementation

ERROR in src/services/feedbackService.ts:12:17
TS2339: Property 'feedback' does not exist on type 'APIClient'.
    10 |   public async submitFeedback(feedbackData: FeedbackData): Promise<void> {
    11 |     try {
  > 12 |       await api.feedback.submit(feedbackData);
       |                 ^^^^^^^^
    13 |     } catch (error) {
    14 |       console.error('Failed to submit feedback:', error);
    15 |       throw new Error('Failed to submit feedback. Please try again later.');
// FIXED: Added feedback API interface and implementation