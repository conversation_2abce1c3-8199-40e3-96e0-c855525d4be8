'use client';

import { useTheme } from 'next-themes';
import Link from 'next/link';
import { useEffect, useState } from 'react';

export default function MainPage() {
    const { theme } = useTheme();
    const [mounted, setMounted] = useState(false);

    useEffect(() => {
        setMounted(true);
    }, []);

    if (!mounted) {
        return <div>Loading...</div>;  // Show loading state instead of null
    }

    return (
        <div className="main-page">
            {/* Hero Section */}
            <section className="hero-section">
                <div className="hero-content">
                    <h1>Navigate Your Cybersecurity Career Path</h1>
                    <h2>Find, Plan, and Achieve the Right Certifications for Your Career Goals</h2>
                    <div className="hero-cta">
                        <Link href="/login" className="cta-button primary">
                            Start Your Journey
                        </Link>
                        <Link href="/certification-explorer" className="cta-button secondary">
                            Explore Certifications
                        </Link>
                    </div>
                </div>
                <div className="hero-visual">
                    <div className="certification-path-visual">
                        <div className="visualization-placeholder">
                            Career Path Visualization
                        </div>
                    </div>
                </div>
            </section>

            {/* Dashboard Preview Section */}
            <section className="dashboard-preview-section">
                <h2>Track Your Certification Journey</h2>
                <p>Monitor your progress, study time, and ROI with our comprehensive dashboard</p>
                <div className="dashboard-preview">
                    <div className="dashboard-preview-placeholder">
                        Dashboard Preview
                    </div>
                    <div className="dashboard-features">
                        <div className="feature">
                            <h3>Progress Tracking</h3>
                            <p>Track completion percentage across all certifications</p>
                        </div>
                        <div className="feature">
                            <h3>Study Time Management</h3>
                            <p>Optimize your study schedule and monitor hours invested</p>
                        </div>
                        <div className="feature">
                            <h3>ROI Calculator</h3>
                            <p>Calculate the return on investment for each certification</p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Career Path Navigator Section */}
            <section className="career-path-section">
                <h2>Visualize Your Career Path</h2>
                <p>Discover certification paths aligned with your career goals</p>
                <div className="career-path-preview">
                    <div className="career-path-placeholder">
                        Career Path Visualization
                    </div>
                    <div className="popular-paths">
                        <h3>Popular Career Paths</h3>
                        <ul>
                            <li>
                                <Link href="/career-path?path=security-analyst">
                                    Security Analyst
                                </Link>
                            </li>
                            <li>
                                <Link href="/career-path?path=penetration-tester">
                                    Penetration Tester
                                </Link>
                            </li>
                            <li>
                                <Link href="/career-path?path=security-architect">
                                    Security Architect
                                </Link>
                            </li>
                            <li>
                                <Link href="/career-path?path=cloud-security">
                                    Cloud Security Specialist
                                </Link>
                            </li>
                        </ul>
                    </div>
                </div>
            </section>

            {/* Certification Catalog Section */}
            <section className="certification-catalog-section">
                <h2>Explore Certifications</h2>
                <p>Browse our comprehensive catalog of security certifications</p>
                <div className="certification-filters">
                    <div className="filter-group">
                        <h4>Domain</h4>
                        <div className="filter-options">
                            <span className="filter-option active">All</span>
                            <span className="filter-option">Network</span>
                            <span className="filter-option">Cloud</span>
                            <span className="filter-option">Application</span>
                        </div>
                    </div>
                    <div className="filter-group">
                        <h4>Level</h4>
                        <div className="filter-options">
                            <span className="filter-option active">All</span>
                            <span className="filter-option">Beginner</span>
                            <span className="filter-option">Intermediate</span>
                            <span className="filter-option">Advanced</span>
                        </div>
                    </div>
                </div>
                <div className="certification-previews">
                    <div className="certification-card">
                        <h3>CompTIA Security+</h3>
                        <div className="certification-details">
                            <span className="cert-level">Beginner</span>
                            <span className="cert-cost">$370</span>
                        </div>
                        <p>Foundation-level security certification covering network security, compliance, and risk management.</p>
                    </div>
                    <div className="certification-card">
                        <h3>CISSP</h3>
                        <div className="certification-details">
                            <span className="cert-level">Advanced</span>
                            <span className="cert-cost">$749</span>
                        </div>
                        <p>Advanced certification for security professionals with extensive experience.</p>
                    </div>
                    <div className="certification-card">
                        <h3>AWS Certified Security</h3>
                        <div className="certification-details">
                            <span className="cert-level">Intermediate</span>
                            <span className="cert-cost">$300</span>
                        </div>
                        <p>Specialized certification for securing workloads and data in AWS environments.</p>
                    </div>
                </div>
                <div className="view-all-link">
                    <Link href="/certification-explorer">
                        View All Certifications
                    </Link>
                </div>
            </section>

            {/* Success Stories Section */}
            <section className="success-stories-section">
                <h2>Success Stories</h2>
                <p>Real outcomes from security professionals using our platform</p>
                <div className="stories-container">
                    <div className="success-story">
                        <div className="story-header">
                            <div className="user-avatar">
                                <div className="avatar-placeholder">JD</div>
                            </div>
                            <div className="user-info">
                                <h3>John Doe</h3>
                                <p>Security Analyst → Security Engineer</p>
                            </div>
                        </div>
                        <p className="story-content">
                            "The certification roadmap helped me identify the exact certifications I needed to advance my career.
                            Within 8 months, I earned my CISSP and landed a senior security engineer position with a 30% salary increase."
                        </p>
                        <div className="story-metrics">
                            <div className="metric">
                                <span className="metric-value">3</span>
                                <span className="metric-label">Certifications</span>
                            </div>
                            <div className="metric">
                                <span className="metric-value">30%</span>
                                <span className="metric-label">Salary Increase</span>
                            </div>
                            <div className="metric">
                                <span className="metric-value">8</span>
                                <span className="metric-label">Months</span>
                            </div>
                        </div>
                    </div>
                    <div className="success-story">
                        <div className="story-header">
                            <div className="user-avatar">
                                <div className="avatar-placeholder">JS</div>
                            </div>
                            <div className="user-info">
                                <h3>Jane Smith</h3>
                                <p>IT Support → Penetration Tester</p>
                            </div>
                        </div>
                        <p className="story-content">
                            "I wanted to transition to offensive security but didn't know where to start.
                            The platform guided me through the right certifications and study materials.
                            I'm now working as a junior penetration tester!"
                        </p>
                        <div className="story-metrics">
                            <div className="metric">
                                <span className="metric-value">4</span>
                                <span className="metric-label">Certifications</span>
                            </div>
                            <div className="metric">
                                <span className="metric-value">45%</span>
                                <span className="metric-label">Salary Increase</span>
                            </div>
                            <div className="metric">
                                <span className="metric-value">14</span>
                                <span className="metric-label">Months</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Getting Started Guide Section */}
            <section className="getting-started-section">
                <h2>How to Get Started</h2>
                <p>Begin your certification journey in three easy steps</p>
                <div className="steps-container">
                    <div className="step">
                        <div className="step-number">1</div>
                        <h3>Create Your Profile</h3>
                        <p>Tell us about your current skills, experience, and career goals</p>
                    </div>
                    <div className="step">
                        <div className="step-number">2</div>
                        <h3>Discover Your Path</h3>
                        <p>Get personalized certification recommendations based on your goals</p>
                    </div>
                    <div className="step">
                        <div className="step-number">3</div>
                        <h3>Start Learning</h3>
                        <p>Access study materials and track your progress towards certification</p>
                    </div>
                </div>
                <div className="getting-started-cta">
                    <Link href="/signup" className="cta-button primary">
                        Create Your Profile
                    </Link>
                </div>
            </section>
        </div>
    );
} 