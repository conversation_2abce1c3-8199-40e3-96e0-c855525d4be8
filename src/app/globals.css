@tailwind base;
@tailwind components;
@tailwind utilities;
 
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
 
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
 
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
 
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
 
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
 
    --radius: 0.5rem;
  }
 
  .dark {
    --background: 222 47% 4%;
    --foreground: 120 100% 86%;
 
    --card: 222 47% 3%;
    --card-foreground: 120 100% 86%;
 
    --popover: 222 47% 3%;
    --popover-foreground: 120 100% 86%;
 
    --primary: 120 100% 45%;
    --primary-foreground: 222 47% 3%;
 
    --secondary: 217 32% 12%;
    --secondary-foreground: 120 100% 86%;
 
    --muted: 217 32% 12%;
    --muted-foreground: 120 100% 70%;
 
    --accent: 217 32% 12%;
    --accent-foreground: 120 100% 86%;
 
    --destructive: 0 100% 30%;
    --destructive-foreground: 120 100% 86%;
 
    --border: 217 32% 15%;
    --input: 217 32% 15%;
    --ring: 120 100% 45%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  .terminal-glow {
    text-shadow: 0 0 10px hsl(var(--foreground) / 0.5),
                 0 0 20px hsl(var(--primary) / 0.3);
  }
  
  .terminal-border {
    box-shadow: 0 0 15px hsl(var(--primary) / 0.3),
                inset 0 0 15px hsl(var(--primary) / 0.1);
    border: 1px solid hsl(var(--primary) / 0.2);
  }

  .terminal-card {
    @apply bg-black/40 backdrop-blur-sm border border-primary/20 shadow-lg;
    box-shadow: 0 0 30px hsl(var(--primary) / 0.1);
  }

  .terminal-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--primary)) transparent;
  }

  .terminal-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .terminal-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .terminal-scrollbar::-webkit-scrollbar-thumb {
    background-color: hsl(var(--primary) / 0.3);
    border-radius: 20px;
    border: 2px solid transparent;
  }

  .terminal-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--primary) / 0.5);
  }

  .terminal-text-glow {
    text-shadow: 0 0 10px currentColor;
  }
} 