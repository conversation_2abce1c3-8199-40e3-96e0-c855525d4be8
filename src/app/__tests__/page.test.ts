import { expect, test } from '@playwright/test';

test.describe('Main Page', () => {
  test('should render the hero section', async ({ page }) => {
    await page.goto('/');
    
    // Check if the main content is visible
    await expect(page.locator('.main-page')).toBeVisible();
    
    // Check if the hero section is rendered
    await expect(page.locator('.hero-section')).toBeVisible();
    
    // Verify hero content
    await expect(page.locator('.hero-content h1')).toHaveText('Navigate Your Cybersecurity Career Path');
    await expect(page.locator('.hero-content h2')).toHaveText('Find, Plan, and Achieve the Right Certifications for Your Career Goals');
    
    // Check if CTA buttons are present
    await expect(page.locator('.hero-cta .cta-button.primary')).toBeVisible();
    await expect(page.locator('.hero-cta .cta-button.secondary')).toBeVisible();
  });

  test('should render all main sections', async ({ page }) => {
    await page.goto('/');
    
    // Check if all major sections are present
    const sections = [
      '.dashboard-preview-section',
      '.career-path-section',
      '.certification-catalog-section',
      '.success-stories-section',
      '.getting-started-section'
    ];
    
    for (const section of sections) {
      await expect(page.locator(section)).toBeVisible();
    }
  });

  test('should toggle theme', async ({ page }) => {
    await page.goto('/');
    
    // Find and click the theme toggle button
    const themeToggle = page.locator('button[aria-label="Toggle theme"]');
    await expect(themeToggle).toBeVisible();
    
    // Click the toggle and verify theme change
    await themeToggle.click();
    
    // Check if the theme has changed (you might need to adjust this based on your theme implementation)
    await expect(page.locator('html')).toHaveAttribute('class', /dark/);
  });
}); 