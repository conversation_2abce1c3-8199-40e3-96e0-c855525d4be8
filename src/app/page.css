/* Main Page Styles */
.main-page {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.main-page section {
  margin: 80px 0;
  padding: 20px 0;
}

.main-page h1 {
  font-size: 48px;
  font-weight: 800;
  margin-bottom: 16px;
  color: hsl(var(--primary));
  line-height: 1.2;
  @apply terminal-text-glow;
}

.main-page h2 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 16px;
  color: hsl(var(--foreground));
  @apply dark:terminal-glow;
}

.main-page h3 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 12px;
  color: hsl(var(--foreground));
  @apply dark:terminal-glow;
}

.main-page h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: hsl(var(--foreground));
}

.main-page p {
  font-size: 18px;
  line-height: 1.6;
  color: hsl(var(--muted-foreground));
  margin-bottom: 24px;
}

/* Hero Section */
.hero-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  min-height: 600px;
  padding: 40px 0;
}

.hero-content {
  flex: 1;
  max-width: 550px;
}

.hero-content h1 {
  color: hsl(var(--primary));
}

.hero-content h2 {
  font-size: 24px;
  font-weight: 400;
  margin-bottom: 32px;
  color: hsl(var(--muted-foreground));
}

.hero-visual {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 550px;
}

.certification-path-visual {
  width: 100%;
  height: 400px;
  @apply terminal-card terminal-border;
  display: flex;
  justify-content: center;
  align-items: center;
}

.visualization-placeholder {
  font-size: 18px;
  color: hsl(var(--muted-foreground));
  text-align: center;
  @apply dark:terminal-glow;
}

.hero-cta {
  display: flex;
  gap: 16px;
  margin-top: 32px;
}

.cta-button {
  padding: 14px 28px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
  @apply dark:terminal-border;
}

.cta-button.primary {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border: 2px solid hsl(var(--primary));
  @apply dark:terminal-glow;
}

.cta-button.primary:hover {
  background-color: hsl(var(--primary) / 0.9);
  border-color: hsl(var(--primary) / 0.9);
  transform: translateY(-2px);
}

.cta-button.secondary {
  background-color: transparent;
  color: hsl(var(--primary));
  border: 2px solid hsl(var(--primary));
  @apply dark:terminal-text-glow;
}

.cta-button.secondary:hover {
  background-color: hsl(var(--primary) / 0.1);
  transform: translateY(-2px);
}

/* Dashboard Preview Section */
.dashboard-preview-section {
  text-align: center;
}

.dashboard-preview {
  margin-top: 40px;
}

.dashboard-preview-placeholder {
  width: 100%;
  height: 300px;
  @apply terminal-card terminal-border;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40px;
}

.dashboard-features {
  display: flex;
  justify-content: space-between;
  gap: 24px;
  margin-top: 30px;
}

.feature {
  flex: 1;
  padding: 24px;
  @apply terminal-card;
  text-align: left;
  transition: transform 0.3s ease;
}

.feature:hover {
  transform: translateY(-5px);
  @apply dark:terminal-border;
}

.feature h3 {
  color: hsl(var(--primary));
  @apply dark:terminal-text-glow;
}

.feature p {
  font-size: 16px;
  color: #4a4a68;
  margin: 0;
}

/* Career Path Section */
.career-path-section {
  text-align: center;
}

.career-path-preview {
  margin-top: 40px;
  display: flex;
  gap: 40px;
  align-items: flex-start;
}

.career-path-placeholder {
  flex: 2;
  height: 400px;
  @apply terminal-card terminal-border;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 24px;
}

.ai-career-path-features {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  margin-bottom: 24px;
  width: 100%;
}

.feature-highlight {
  text-align: center;
  padding: 16px;
  @apply terminal-card;
}

.feature-highlight h3 {
  font-size: 18px;
  margin-bottom: 8px;
  color: hsl(var(--primary));
  @apply dark:terminal-text-glow;
}

.feature-highlight p {
  font-size: 14px;
  margin: 0;
  color: hsl(var(--muted-foreground));
}

.career-path-cta {
  width: 100%;
  text-align: center;
}

.cta-button.large {
  padding: 16px 32px;
  font-size: 18px;
  font-weight: 700;
}

.popular-paths {
  flex: 1;
  @apply terminal-card p-6;
}

.popular-paths h3 {
  margin-bottom: 20px;
  @apply dark:terminal-text-glow;
}

.popular-paths ul {
  list-style: none;
  padding: 0;
}

.popular-paths li {
  margin-bottom: 16px;
}

.popular-paths a {
  color: hsl(var(--primary));
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  @apply dark:terminal-text-glow;
}

.popular-paths a:hover {
  color: hsl(var(--primary) / 0.8);
  text-decoration: underline;
}

.popular-paths-cta {
  margin-top: 20px;
  text-align: center;
}

.text-link {
  color: hsl(var(--primary));
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  @apply dark:terminal-text-glow;
}

.text-link:hover {
  color: hsl(var(--primary) / 0.8);
  text-decoration: underline;
}

/* Certification Catalog Section */
.certification-catalog-section {
  text-align: center;
}

.certification-filters {
  margin: 40px 0;
  @apply terminal-card p-6;
}

.filter-group {
  margin-bottom: 24px;
}

.filter-options {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.filter-option {
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  @apply terminal-border;
  color: hsl(var(--muted-foreground));
}

.filter-option.active {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  @apply dark:terminal-glow;
}

.certification-previews {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 40px;
}

.certification-card {
  @apply terminal-card p-6;
  transition: transform 0.3s ease;
}

.certification-card:hover {
  transform: translateY(-5px);
  @apply dark:terminal-border;
}

.certification-card h3 {
  margin-bottom: 12px;
  color: #0f3460;
}

.certification-details {
  display: flex;
  justify-content: space-between;
  margin: 12px 0;
}

.cert-level, .cert-cost {
  @apply terminal-card px-3 py-1;
  font-size: 14px;
  color: hsl(var(--primary));
  @apply dark:terminal-text-glow;
}

.certification-card p {
  font-size: 14px;
  margin: 0;
}

.view-all-link {
  margin-top: 40px;
}

.view-all-link a {
  color: hsl(var(--primary));
  text-decoration: none;
  font-weight: 500;
  @apply dark:terminal-text-glow;
}

.view-all-link a:hover {
  text-decoration: underline;
}

/* Success Stories Section */
.success-stories-section {
  text-align: center;
}

.stories-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 40px;
}

.success-story {
  @apply terminal-card p-6;
  text-align: left;
}

.story-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.user-avatar {
  width: 48px;
  height: 48px;
  @apply terminal-card terminal-border;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}

.avatar-placeholder {
  color: hsl(var(--primary));
  font-weight: 600;
  @apply dark:terminal-text-glow;
}

.user-info h3 {
  margin-bottom: 4px;
}

.user-info p {
  font-size: 14px;
  color: #4a4a68;
  margin: 0;
}

.story-content {
  font-size: 16px;
  line-height: 1.6;
  color: #4a4a68;
  margin-bottom: 24px;
}

.story-metrics {
  display: flex;
  justify-content: space-around;
  margin-top: 24px;
  text-align: center;
}

.metric {
  @apply terminal-card p-4;
}

.metric-value {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: hsl(var(--primary));
  @apply dark:terminal-text-glow;
}

.metric-label {
  display: block;
  font-size: 14px;
  color: hsl(var(--muted-foreground));
  margin-top: 4px;
}

/* Getting Started Section */
.getting-started-section {
  text-align: center;
  @apply terminal-card p-8;
  margin-bottom: 80px;
}

.steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 32px;
  margin: 40px 0;
}

.step {
  text-align: left;
}

.step-number {
  width: 40px;
  height: 40px;
  @apply terminal-card terminal-border;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  margin-bottom: 16px;
  color: hsl(var(--primary));
  font-weight: 600;
  @apply dark:terminal-text-glow;
}

.getting-started-cta {
  margin-top: 40px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-section {
    flex-direction: column;
    text-align: center;
  }

  .hero-content {
    max-width: 100%;
    margin-bottom: 40px;
  }

  .hero-cta {
    justify-content: center;
  }

  .career-path-preview {
    flex-direction: column;
  }

  .popular-paths {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .main-page h1 {
    font-size: 36px;
  }

  .main-page h2 {
    font-size: 24px;
  }

  .main-page section {
    margin: 40px 0;
  }

  .certification-filters {
    overflow-x: auto;
  }

  .filter-options {
    justify-content: flex-start;
    padding-bottom: 12px;
  }

  .hero-cta {
    flex-direction: column;
  }

  .cta-button {
    width: 100%;
  }

  .getting-started-cta {
    padding: 0 20px;
  }
}

@media (max-width: 480px) {
  .main-page h1 {
    font-size: 32px;
  }

  .main-page h2 {
    font-size: 24px;
  }

  .main-page section {
    margin: 32px 0;
  }

  .hero-content h2 {
    font-size: 20px;
  }

  .certification-path-visual,
  .dashboard-preview-placeholder {
    height: 250px;
  }

  .certification-previews {
    grid-template-columns: 1fr;
  }

  .story-metrics {
    flex-direction: column;
    gap: 16px;
  }
} 