import { db } from '@/lib/db';
import { prisma } from '@/lib/prisma';
import { certifications } from '@/lib/schema';
import { and, eq, gte, ilike, lte, sql } from 'drizzle-orm';
import { NextResponse } from 'next/server';
import { z } from 'zod';

const certificationSchema = z.object({
  name: z.string().min(1),
  description: z.string().min(1),
  category: z.string().min(1),
  level: z.string().min(1),
  provider: z.string().min(1),
  aliases: z.array(z.string()),
});

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const provider = searchParams.get('provider');
    const type = searchParams.get('type');
    const level = searchParams.get('level');
    const search = searchParams.get('search');
    const minPrice = searchParams.get('min_price');
    const maxPrice = searchParams.get('max_price');

    // Build where conditions
    const whereConditions = [];

    if (provider) {
      whereConditions.push(eq(certifications.provider, provider));
    }
    if (type) {
      whereConditions.push(eq(certifications.type, type));
    }
    if (level) {
      whereConditions.push(eq(certifications.level, level));
    }
    if (search) {
      whereConditions.push(
        ilike(certifications.name, `%${search}%`)
      );
    }
    if (minPrice) {
      whereConditions.push(gte(certifications.price, parseFloat(minPrice)));
    }
    if (maxPrice) {
      whereConditions.push(lte(certifications.price, parseFloat(maxPrice)));
    }

    // Get total count
    const totalCount = await db.select({ count: sql`count(*)` })
      .from(certifications)
      .where(and(...whereConditions))
      .execute();

    // Get paginated results
    const results = await db.select()
      .from(certifications)
      .where(and(...whereConditions))
      .limit(pageSize)
      .offset((page - 1) * pageSize)
      .execute();

    return NextResponse.json({
      certifications: results,
      total: totalCount[0].count,
      page,
      pageSize
    });
  } catch (error) {
    console.error('Error fetching certifications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch certifications' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const validatedData = certificationSchema.parse(body);

    const certification = await prisma.certification.create({
      data: validatedData,
    });

    return NextResponse.json(certification, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid certification data', details: error.errors },
        { status: 400 }
      );
    }
    console.error('Failed to create certification:', error);
    return NextResponse.json(
      { error: 'Failed to create certification' },
      { status: 500 }
    );
  }
} 