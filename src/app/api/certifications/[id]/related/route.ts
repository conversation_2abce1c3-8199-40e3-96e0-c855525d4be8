import { db } from '@/lib/db';
import { certifications } from '@/lib/schema';
import { and, eq, ne } from 'drizzle-orm';
import { NextResponse } from 'next/server';

export async function GET(
    request: Request,
    { params }: { params: { id: string } }
) {
    try {
        const id = parseInt(params.id);
        if (isNaN(id)) {
            return NextResponse.json(
                { error: 'Invalid certification ID' },
                { status: 400 }
            );
        }

        // First get the current certification to match against
        const currentCert = await db.select()
            .from(certifications)
            .where(eq(certifications.id, id))
            .execute();

        if (!currentCert || currentCert.length === 0) {
            return NextResponse.json(
                { error: 'Certification not found' },
                { status: 404 }
            );
        }

        // Find related certifications based on type and level
        const relatedCerts = await db.select()
            .from(certifications)
            .where(
                and(
                    ne(certifications.id, id),
                    eq(certifications.type, currentCert[0].type),
                    eq(certifications.level, currentCert[0].level)
                )
            )
            .limit(5)
            .execute();

        return NextResponse.json({
            certifications: relatedCerts
        });
    } catch (error) {
        console.error('Error fetching related certifications:', error);
        return NextResponse.json(
            { error: 'Failed to fetch related certifications' },
            { status: 500 }
        );
    }
} 