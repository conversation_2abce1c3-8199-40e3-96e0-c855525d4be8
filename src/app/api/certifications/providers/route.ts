import { db } from '@/lib/db';
import { certifications } from '@/lib/schema';
import { sql } from 'drizzle-orm';
import { NextResponse } from 'next/server';

export async function GET() {
    try {
        // Get distinct providers
        const providers = await db.select({
            provider: sql`DISTINCT ${certifications.provider}`
        })
        .from(certifications)
        .orderBy(certifications.provider)
        .execute();

        return NextResponse.json({
            providers: providers.map(p => p.provider)
        });
    } catch (error) {
        console.error('Error fetching providers:', error);
        return NextResponse.json(
            { error: 'Failed to fetch providers' },
            { status: 500 }
        );
    }
} 