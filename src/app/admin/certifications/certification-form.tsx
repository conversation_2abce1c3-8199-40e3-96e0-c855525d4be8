'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import * as z from 'zod';

const certificationSchema = z.object({
    name: z.string().min(1, 'Name is required'),
    description: z.string().min(1, 'Description is required'),
    category: z.string().min(1, 'Category is required'),
    level: z.string().min(1, 'Level is required'),
    provider: z.string().min(1, 'Provider is required'),
    aliases: z.string().transform((val) => val.split(',').map((s) => s.trim())),
});

type CertificationFormValues = z.infer<typeof certificationSchema>;

interface CertificationFormProps {
    certification?: {
        id: string;
        name: string;
        description: string;
        category: string;
        level: string;
        provider: string;
        aliases: string[];
    } | null;
    onClose: () => void;
}

export function CertificationForm({ certification, onClose }: CertificationFormProps) {
    const queryClient = useQueryClient();
    const [isSubmitting, setIsSubmitting] = useState(false);

    const form = useForm<CertificationFormValues>({
        resolver: zodResolver(certificationSchema),
        defaultValues: {
            name: certification?.name || '',
            description: certification?.description || '',
            category: certification?.category || '',
            level: certification?.level || '',
            provider: certification?.provider || '',
            aliases: certification?.aliases.join(', ') || '',
        },
    });

    const mutation = useMutation({
        mutationFn: async (values: CertificationFormValues) => {
            const url = certification
                ? `/api/certifications/${certification.id}`
                : '/api/certifications';
            const method = certification ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(values),
            });

            if (!response.ok) {
                throw new Error('Failed to save certification');
            }

            return response.json();
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['certifications'] });
            toast.success(
                `Certification ${certification ? 'updated' : 'created'} successfully`
            );
            onClose();
        },
        onError: (error) => {
            toast.error('Failed to save certification');
            console.error(error);
        },
    });

    const onSubmit = async (values: CertificationFormValues) => {
        setIsSubmitting(true);
        try {
            await mutation.mutateAsync(values);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Dialog open onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>
                        {certification ? 'Edit Certification' : 'Add Certification'}
                    </DialogTitle>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Name</FormLabel>
                                    <FormControl>
                                        <Input placeholder="e.g., CISSP" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="description"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Description</FormLabel>
                                    <FormControl>
                                        <Input
                                            placeholder="e.g., Certified Information Systems Security Professional"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="category"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Category</FormLabel>
                                    <FormControl>
                                        <Input placeholder="e.g., Security" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="level"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Level</FormLabel>
                                    <FormControl>
                                        <Input placeholder="e.g., Advanced" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="provider"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Provider</FormLabel>
                                    <FormControl>
                                        <Input placeholder="e.g., (ISC)²" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="aliases"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Aliases (comma-separated)</FormLabel>
                                    <FormControl>
                                        <Input
                                            placeholder="e.g., Certified Information Systems Security Professional, CISSP certification"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <div className="flex justify-end space-x-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={onClose}
                                disabled={isSubmitting}
                            >
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isSubmitting}>
                                {isSubmitting ? 'Saving...' : 'Save'}
                            </Button>
                        </div>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
} 