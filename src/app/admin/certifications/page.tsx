'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Edit, Plus, Search, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { CertificationForm } from './certification-form';

interface Certification {
    id: string;
    name: string;
    description: string;
    category: string;
    level: string;
    provider: string;
    aliases: string[];
    createdAt: string;
    updatedAt: string;
}

export default function CertificationsPage() {
    const [searchQuery, setSearchQuery] = useState('');
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [editingCertification, setEditingCertification] = useState<Certification | null>(null);
    const queryClient = useQueryClient();

    // Fetch certifications
    const { data: certifications, isLoading } = useQuery({
        queryKey: ['certifications'],
        queryFn: async () => {
            const response = await fetch('/api/certifications');
            if (!response.ok) throw new Error('Failed to fetch certifications');
            return response.json();
        },
    });

    // Delete certification mutation
    const deleteMutation = useMutation({
        mutationFn: async (id: string) => {
            const response = await fetch(`/api/certifications/${id}`, {
                method: 'DELETE',
            });
            if (!response.ok) throw new Error('Failed to delete certification');
            return response.json();
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['certifications'] });
            toast.success('Certification deleted successfully');
        },
        onError: (error) => {
            toast.error('Failed to delete certification');
            console.error(error);
        },
    });

    // Filter certifications based on search query
    const filteredCertifications = certifications?.filter((cert: Certification) =>
        cert.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        cert.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        cert.category.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const handleEdit = (certification: Certification) => {
        setEditingCertification(certification);
        setIsFormOpen(true);
    };

    const handleDelete = async (id: string) => {
        if (window.confirm('Are you sure you want to delete this certification?')) {
            await deleteMutation.mutateAsync(id);
        }
    };

    return (
        <div className="container mx-auto py-10">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold">Certification Management</h1>
                <Button onClick={() => setIsFormOpen(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Certification
                </Button>
            </div>

            <div className="flex items-center space-x-2 mb-6">
                <div className="relative flex-1">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder="Search certifications..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-8"
                    />
                </div>
            </div>

            {isLoading ? (
                <div>Loading...</div>
            ) : (
                <div className="rounded-md border">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Name</TableHead>
                                <TableHead>Category</TableHead>
                                <TableHead>Level</TableHead>
                                <TableHead>Provider</TableHead>
                                <TableHead>Aliases</TableHead>
                                <TableHead>Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {filteredCertifications?.map((cert: Certification) => (
                                <TableRow key={cert.id}>
                                    <TableCell className="font-medium">{cert.name}</TableCell>
                                    <TableCell>{cert.category}</TableCell>
                                    <TableCell>{cert.level}</TableCell>
                                    <TableCell>{cert.provider}</TableCell>
                                    <TableCell>{cert.aliases.join(', ')}</TableCell>
                                    <TableCell>
                                        <div className="flex space-x-2">
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={() => handleEdit(cert)}
                                            >
                                                <Edit className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={() => handleDelete(cert.id)}
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
            )}

            {isFormOpen && (
                <CertificationForm
                    certification={editingCertification}
                    onClose={() => {
                        setIsFormOpen(false);
                        setEditingCertification(null);
                    }}
                />
            )}
        </div>
    );
} 