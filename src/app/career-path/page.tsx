'use client';

import { useState } from 'react';
import Link from 'next/link';

interface CareerPathRequest {
  completed_certifications: string[];
  interests: string[];
  years_experience: number;
  current_role: string;
  target_role: string;
  learning_style: string;
  study_hours: number;
}

interface CareerPathResponse {
  career_path: Array<{
    stage: string;
    timeline: string;
    certifications: string[];
    certification_costs: Record<string, {
      exam: number;
      materials: number;
      training: number;
    }>;
  }>;
  alignment_analysis: {
    current_role_alignment: number;
    target_role_alignment: number;
    gap_analysis: string;
  };
  total_estimated_cost: number;
  total_estimated_timeline: string;
}

export default function CareerPathPage() {
  const [formData, setFormData] = useState<CareerPathRequest>({
    completed_certifications: [],
    interests: [],
    years_experience: 0,
    current_role: '',
    target_role: '',
    learning_style: 'Mixed',
    study_hours: 10
  });
  
  const [careerPath, setCareerPath] = useState<CareerPathResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (field: keyof CareerPathRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayInputChange = (field: 'completed_certifications' | 'interests', value: string) => {
    const items = value.split(',').map(item => item.trim()).filter(item => item);
    setFormData(prev => ({
      ...prev,
      [field]: items
    }));
  };

  const generateCareerPath = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/v1/career-path', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setCareerPath(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="career-path-page">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Link href="/" className="text-blue-600 hover:text-blue-800 mb-4 inline-block">
            ← Back to Home
          </Link>
          <h1 className="text-4xl font-bold mb-4">AI-Powered Career Path Generator</h1>
          <p className="text-lg text-gray-600">
            Get personalized certification recommendations based on your career goals and experience.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Form Section */}
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-2xl font-semibold mb-6">Tell Us About Yourself</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Current Role</label>
                <input
                  type="text"
                  className="w-full p-3 border border-gray-300 rounded-md"
                  placeholder="e.g., Security Analyst"
                  value={formData.current_role}
                  onChange={(e) => handleInputChange('current_role', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Target Role</label>
                <input
                  type="text"
                  className="w-full p-3 border border-gray-300 rounded-md"
                  placeholder="e.g., Security Engineer"
                  value={formData.target_role}
                  onChange={(e) => handleInputChange('target_role', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Years of Experience</label>
                <input
                  type="number"
                  className="w-full p-3 border border-gray-300 rounded-md"
                  placeholder="e.g., 3"
                  value={formData.years_experience}
                  onChange={(e) => handleInputChange('years_experience', parseInt(e.target.value) || 0)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Completed Certifications (comma-separated)</label>
                <input
                  type="text"
                  className="w-full p-3 border border-gray-300 rounded-md"
                  placeholder="e.g., Security+, Network+"
                  onChange={(e) => handleArrayInputChange('completed_certifications', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Interests (comma-separated)</label>
                <input
                  type="text"
                  className="w-full p-3 border border-gray-300 rounded-md"
                  placeholder="e.g., Network Security, Cloud Security"
                  onChange={(e) => handleArrayInputChange('interests', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Learning Style</label>
                <select
                  className="w-full p-3 border border-gray-300 rounded-md"
                  value={formData.learning_style}
                  onChange={(e) => handleInputChange('learning_style', e.target.value)}
                >
                  <option value="Visual">Visual</option>
                  <option value="Hands-on">Hands-on</option>
                  <option value="Reading">Reading</option>
                  <option value="Mixed">Mixed</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Study Hours per Week</label>
                <input
                  type="number"
                  className="w-full p-3 border border-gray-300 rounded-md"
                  placeholder="e.g., 10"
                  value={formData.study_hours}
                  onChange={(e) => handleInputChange('study_hours', parseInt(e.target.value) || 0)}
                />
              </div>

              <button
                onClick={generateCareerPath}
                disabled={loading || !formData.current_role || !formData.target_role}
                className="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {loading ? 'Generating Career Path...' : 'Generate Career Path'}
              </button>
            </div>
          </div>

          {/* Results Section */}
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-2xl font-semibold mb-6">Your Personalized Career Path</h2>
            
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                Error: {error}
              </div>
            )}

            {loading && (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-4 text-gray-600">Generating your personalized career path...</p>
              </div>
            )}

            {careerPath && (
              <div className="space-y-6">
                {/* Summary */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-lg mb-2">Summary</h3>
                  <p><strong>Total Timeline:</strong> {careerPath.total_estimated_timeline}</p>
                  <p><strong>Total Cost:</strong> ${careerPath.total_estimated_cost.toLocaleString()}</p>
                  <p><strong>Current Role Alignment:</strong> {careerPath.alignment_analysis.current_role_alignment}%</p>
                  <p><strong>Target Role Alignment:</strong> {careerPath.alignment_analysis.target_role_alignment}%</p>
                </div>

                {/* Gap Analysis */}
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-lg mb-2">Gap Analysis</h3>
                  <p className="text-sm">{careerPath.alignment_analysis.gap_analysis}</p>
                </div>

                {/* Career Path Stages */}
                <div>
                  <h3 className="font-semibold text-lg mb-4">Career Path Stages</h3>
                  {careerPath.career_path.map((stage, index) => (
                    <div key={index} className="border-l-4 border-blue-500 pl-4 mb-6">
                      <h4 className="font-semibold text-md mb-2">
                        Stage {index + 1}: {stage.stage}
                      </h4>
                      <p className="text-sm text-gray-600 mb-2">Timeline: {stage.timeline}</p>
                      
                      <div className="mb-3">
                        <p className="font-medium text-sm mb-1">Certifications:</p>
                        <ul className="list-disc list-inside text-sm">
                          {stage.certifications.map((cert, certIndex) => (
                            <li key={certIndex}>{cert}</li>
                          ))}
                        </ul>
                      </div>

                      {Object.keys(stage.certification_costs).length > 0 && (
                        <div>
                          <p className="font-medium text-sm mb-1">Costs:</p>
                          {Object.entries(stage.certification_costs).map(([cert, costs]) => (
                            <div key={cert} className="text-xs bg-gray-50 p-2 rounded mb-1">
                              <p className="font-medium">{cert}</p>
                              <p>Exam: ${costs.exam} | Materials: ${costs.materials} | Training: ${costs.training}</p>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {!careerPath && !loading && !error && (
              <div className="text-center py-8 text-gray-500">
                <p>Fill out the form and click "Generate Career Path" to see your personalized recommendations.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
