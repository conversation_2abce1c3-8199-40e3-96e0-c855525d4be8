import { ModeToggle } from '@/components/mode-toggle';
import { ThemeProvider } from '@/components/theme-provider';
import { Inter } from 'next/font/google';
import './globals.css';
import './page.css';

const inter = Inter({ subsets: ['latin'] })

export const metadata = {
    title: 'CertRats',
    description: 'Your comprehensive certification management platform',
}

export default function RootLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <html lang="en" suppressHydrationWarning>
            <body className={`${inter.className} dark:bg-[#0a0a0a]`}>
                <ThemeProvider
                    attribute="class"
                    defaultTheme="dark"
                    enableSystem
                    disableTransitionOnChange
                >
                    <div className="min-h-screen bg-background text-foreground dark:terminal-glow">
                        <div className="fixed inset-0 bg-grid-white/[0.02] bg-[size:50px_50px] dark:bg-grid-primary/[0.02]" />
                        <div className="fixed inset-0 flex items-center justify-center bg-background [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)] dark:bg-black/[0.95]" />
                        <div className="relative">
                            <header className="border-b dark:border-primary/20 dark:terminal-border backdrop-blur-sm">
                                <div className="container mx-auto px-4 py-4 flex justify-between items-center">
                                    <h1 className="text-2xl font-bold dark:text-primary dark:terminal-text-glow">CertRats</h1>
                                    <ModeToggle />
                                </div>
                            </header>
                            <main className="container mx-auto px-4 py-8">
                                {children}
                            </main>
                        </div>
                    </div>
                </ThemeProvider>
            </body>
        </html>
    );
} 