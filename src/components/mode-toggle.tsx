'use client';

import { useTheme } from '../contexts/ThemeContext';

export function ModeToggle() {
    const { setTheme, theme } = useTheme();

    // Pick icon based on theme
    let icon = <img src="/sun.svg" alt="Sun" width={20} height={20} />;
    if (theme === 'dark') icon = <img src="/moon.svg" alt="Moon" width={20} height={20} />;
    if (theme === 'crt') icon = <img src="/monitor.svg" alt="CRT" width={20} height={20} style={{ filter: 'drop-shadow(0 0 4px #39ff14)' }} />;

    return (
        <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
            <button onClick={() => setTheme('light')} aria-label="Switch to light mode">{theme === 'light' ? icon : <img src="/sun.svg" alt="Sun" width={20} height={20} />} Light</button>
            <button onClick={() => setTheme('dark')} aria-label="Switch to dark mode">{theme === 'dark' ? icon : <img src="/moon.svg" alt="Moon" width={20} height={20} />} Dark</button>
            <button onClick={() => setTheme('crt')} aria-label="Switch to CRT mode">{theme === 'crt' ? icon : <img src="/monitor.svg" alt="CRT" width={20} height={20} style={{ filter: 'drop-shadow(0 0 4px #39ff14)' }} />} CRT Terminal</button>
        </div>
    );
} 