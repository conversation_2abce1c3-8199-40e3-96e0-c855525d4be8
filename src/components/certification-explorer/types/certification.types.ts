export enum CertificationType {
    SECURITY = "security",
    CLOUD = "cloud",
    NETWORKING = "networking",
    DEVELOPMENT = "development",
    DEVOPS = "devops",
    OTHER = "other"
}

export enum CertificationLevel {
    BEGINNER = "beginner",
    INTERMEDIATE = "intermediate",
    ADVANCED = "advanced",
    EXPERT = "expert"
}

export enum SecurityDomain {
    SECURITY_ENGINEERING = "Security Engineering",
    DEFENSIVE_SECURITY = "Defensive Security",
    SECURITY_MANAGEMENT = "Security Management",
    NETWORK_SECURITY = "Network Security",
    IDENTITY_ACCESS_MANAGEMENT = "Identity & Access Management",
    ASSET_SECURITY = "Asset Security",
    SECURITY_TESTING = "Security Testing",
    OFFENSIVE_SECURITY = "Offensive Security"
}

export interface ICertification {
    id: number;
    name: string;
    provider: string;
    description?: string;
    type: CertificationType;
    level: CertificationLevel;
    domain?: SecurityDomain;
    exam_code?: string;
    price?: number;
    duration_minutes?: number;
    passing_score?: number;
    prerequisites?: string[];
    skills_covered?: string[];
    url?: string;
    created_at: string;
    updated_at?: string;
}

export interface FilterState {
    provider: string;
    type: string;
    level: string;
    domain?: string;
    search: string;
    min_price: string;
    max_price: string;
}

export interface CertificationListResponse {
    certifications: ICertification[];
    total: number;
    page: number;
    page_size: number;
}

export interface ComparisonData {
  certifications: ICertification[];
  differences: {
    [key: string]: {
      [certId: number]: any;
    };
  };
  similarities: {
    [key: string]: any;
  };
} 