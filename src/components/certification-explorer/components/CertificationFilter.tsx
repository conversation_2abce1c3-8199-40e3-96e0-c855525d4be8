'use client';

import { FilterState } from '../types/certification.types';

interface CertificationFilterProps {
    filters: FilterState;
    onFilterChange: (name: string, value: string) => void;
    providers: string[];
    isLoading: boolean;
}

export function CertificationFilter({
    filters,
    onFilterChange,
    providers,
    isLoading
}: CertificationFilterProps) {
    return (
        <div className="filters-panel w-64 bg-white p-4 rounded-lg shadow space-y-4">
            <h2 className="text-lg font-semibold mb-4">Filters</h2>

            <div className="filter-group">
                <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
                    Search
                </label>
                <input
                    type="text"
                    id="search"
                    name="search"
                    value={filters.search}
                    onChange={(e) => onFilterChange('search', e.target.value)}
                    placeholder="Search certifications..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    disabled={isLoading}
                />
            </div>

            <div className="filter-group">
                <label htmlFor="provider" className="block text-sm font-medium text-gray-700 mb-1">
                    Provider
                </label>
                <select
                    id="provider"
                    name="provider"
                    value={filters.provider}
                    onChange={(e) => onFilterChange('provider', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    disabled={isLoading}
                >
                    <option value="">All Providers</option>
                    {providers.map(provider => (
                        <option key={provider} value={provider}>{provider}</option>
                    ))}
                </select>
            </div>

            <div className="filter-group">
                <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                    Type
                </label>
                <select
                    id="type"
                    name="type"
                    value={filters.type}
                    onChange={(e) => onFilterChange('type', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    disabled={isLoading}
                >
                    <option value="">All Types</option>
                    <option value="security">Security</option>
                    <option value="network">Network</option>
                    <option value="cloud">Cloud</option>
                    <option value="software">Software</option>
                </select>
            </div>

            <div className="filter-group">
                <label htmlFor="level" className="block text-sm font-medium text-gray-700 mb-1">
                    Level
                </label>
                <select
                    id="level"
                    name="level"
                    value={filters.level}
                    onChange={(e) => onFilterChange('level', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    disabled={isLoading}
                >
                    <option value="">All Levels</option>
                    <option value="beginner">Beginner</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                    <option value="expert">Expert</option>
                </select>
            </div>

            <div className="filter-group">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                    Price Range
                </label>
                <div className="flex gap-2">
                    <input
                        type="number"
                        name="min_price"
                        value={filters.min_price}
                        onChange={(e) => onFilterChange('min_price', e.target.value)}
                        placeholder="Min"
                        className="w-1/2 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        disabled={isLoading}
                    />
                    <input
                        type="number"
                        name="max_price"
                        value={filters.max_price}
                        onChange={(e) => onFilterChange('max_price', e.target.value)}
                        placeholder="Max"
                        className="w-1/2 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        disabled={isLoading}
                    />
                </div>
            </div>
        </div>
    );
} 