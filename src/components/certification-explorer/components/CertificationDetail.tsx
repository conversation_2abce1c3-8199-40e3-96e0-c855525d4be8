'use client';

import { ICertification } from '../types/certification.types';

interface CertificationDetailProps {
    certification: ICertification;
    relatedCertifications: ICertification[];
    onBack: () => void;
    onRelatedCertificationSelect: (certification: ICertification) => void;
    onAddToComparison: (certification: ICertification) => void;
    isInComparison: boolean;
}

export function CertificationDetail({
    certification,
    relatedCertifications,
    onBack,
    onRelatedCertificationSelect,
    onAddToComparison,
    isInComparison
}: CertificationDetailProps) {
    const formatDuration = (minutes: number | null): string => {
        if (minutes === null) return 'Not available';
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
    };

    const formatPrice = (price: number | null): string => {
        if (price === null) return 'Not available';
        return `$${price.toLocaleString()}`;
    };

    return (
        <div className="certification-detail bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center mb-6">
                <button
                    onClick={onBack}
                    className="mr-4 text-gray-600 hover:text-gray-900"
                >
                    ← Back
                </button>
                <h2 className="text-2xl font-bold flex-grow">{certification.name}</h2>
                <button
                    onClick={() => onAddToComparison(certification)}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${isInComparison
                            ? 'bg-gray-100 text-gray-600'
                            : 'bg-blue-50 text-blue-600 hover:bg-blue-100'
                        }`}
                >
                    {isInComparison ? 'Added to Compare' : 'Add to Compare'}
                </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="space-y-4">
                    <div>
                        <h3 className="text-lg font-semibold mb-2">Provider</h3>
                        <p className="text-gray-600">{certification.provider}</p>
                    </div>

                    <div>
                        <h3 className="text-lg font-semibold mb-2">Level</h3>
                        <span className={`px-3 py-1 rounded-full text-sm ${certification.level === 'beginner' ? 'bg-green-100 text-green-800' :
                                certification.level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                                    certification.level === 'advanced' ? 'bg-red-100 text-red-800' :
                                        'bg-gray-100 text-gray-800'
                            }`}>
                            {certification.level}
                        </span>
                    </div>

                    <div>
                        <h3 className="text-lg font-semibold mb-2">Type</h3>
                        <p className="text-gray-600">{certification.type}</p>
                    </div>
                </div>

                <div className="space-y-4">
                    <div>
                        <h3 className="text-lg font-semibold mb-2">Exam Details</h3>
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <p className="text-sm text-gray-500">Exam Code</p>
                                <p className="text-gray-900">{certification.exam_code || 'Not available'}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500">Price</p>
                                <p className="text-gray-900">{formatPrice(certification.price)}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500">Duration</p>
                                <p className="text-gray-900">{formatDuration(certification.duration_minutes)}</p>
                            </div>
                            <div>
                                <p className="text-sm text-gray-500">Passing Score</p>
                                <p className="text-gray-900">{certification.passing_score ? `${certification.passing_score}%` : 'Not available'}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="mb-8">
                <h3 className="text-lg font-semibold mb-4">Description</h3>
                <p className="text-gray-600">{certification.description || 'No description available.'}</p>
            </div>

            {certification.skills_covered && certification.skills_covered.length > 0 && (
                <div className="mb-8">
                    <h3 className="text-lg font-semibold mb-4">Skills Covered</h3>
                    <div className="flex flex-wrap gap-2">
                        {certification.skills_covered.map((skill, index) => (
                            <span
                                key={index}
                                className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
                            >
                                {skill}
                            </span>
                        ))}
                    </div>
                </div>
            )}

            {certification.prerequisites && certification.prerequisites.length > 0 && (
                <div className="mb-8">
                    <h3 className="text-lg font-semibold mb-4">Prerequisites</h3>
                    <ul className="list-disc list-inside space-y-2 text-gray-600">
                        {certification.prerequisites.map((prerequisite, index) => (
                            <li key={index}>{prerequisite}</li>
                        ))}
                    </ul>
                </div>
            )}

            {certification.url && (
                <div className="mb-8">
                    <a
                        href={certification.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        Visit Official Page
                    </a>
                </div>
            )}

            {relatedCertifications.length > 0 && (
                <div>
                    <h3 className="text-lg font-semibold mb-4">Related Certifications</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {relatedCertifications.map(cert => (
                            <div
                                key={cert.id}
                                className="p-4 border rounded-lg cursor-pointer hover:bg-gray-50"
                                onClick={() => onRelatedCertificationSelect(cert)}
                            >
                                <h4 className="font-medium mb-2">{cert.name}</h4>
                                <div className="text-sm text-gray-500">{cert.provider}</div>
                                <div className="mt-2">
                                    <span className={`px-2 py-1 rounded-full text-xs ${cert.level === 'beginner' ? 'bg-green-100 text-green-800' :
                                            cert.level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                                                cert.level === 'advanced' ? 'bg-red-100 text-red-800' :
                                                    'bg-gray-100 text-gray-800'
                                        }`}>
                                        {cert.level}
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
} 