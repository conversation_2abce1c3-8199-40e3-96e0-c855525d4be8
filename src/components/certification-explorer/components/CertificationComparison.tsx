'use client';

import { ComparisonData, ICertification } from '../types/certification.types';

interface CertificationComparisonProps {
    comparisonData: ComparisonData;
    onClose: () => void;
    onCertificationSelect: (certification: ICertification) => void;
}

export function CertificationComparison({
    comparisonData,
    onClose,
    onCertificationSelect
}: CertificationComparisonProps) {
    const formatValue = (value: any): string => {
        if (value === null || value === undefined) return 'Not available';
        if (typeof value === 'number') {
            if (value > 1000) return `$${value.toLocaleString()}`;
            return value.toString();
        }
        if (Array.isArray(value)) return value.join(', ');
        return value.toString();
    };

    return (
        <div className="comparison-container bg-white rounded-lg shadow-lg p-6 mb-8">
            <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold">Certification Comparison</h2>
                <button
                    onClick={onClose}
                    className="text-gray-600 hover:text-gray-900"
                >
                    × Close
                </button>
            </div>

            <div className="overflow-x-auto">
                <table className="w-full">
                    <thead>
                        <tr className="border-b">
                            <th className="py-4 px-6 text-left text-gray-500 font-medium">Feature</th>
                            {comparisonData.certifications.map(cert => (
                                <th key={cert.id} className="py-4 px-6 text-left">
                                    <div className="font-bold">{cert.name}</div>
                                    <div className="text-sm text-gray-500">{cert.provider}</div>
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody>
                        {/* Similarities */}
                        {Object.entries(comparisonData.similarities).map(([key, value]) => (
                            <tr key={key} className="border-b bg-green-50">
                                <td className="py-4 px-6 font-medium">{key}</td>
                                <td
                                    className="py-4 px-6 text-green-700"
                                    colSpan={comparisonData.certifications.length}
                                >
                                    {formatValue(value)}
                                </td>
                            </tr>
                        ))}

                        {/* Differences */}
                        {Object.entries(comparisonData.differences).map(([key, values]) => (
                            <tr key={key} className="border-b">
                                <td className="py-4 px-6 font-medium">{key}</td>
                                {comparisonData.certifications.map(cert => (
                                    <td key={cert.id} className="py-4 px-6">
                                        {formatValue(values[cert.id])}
                                    </td>
                                ))}
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            <div className="mt-6 flex justify-end space-x-4">
                {comparisonData.certifications.map(cert => (
                    <button
                        key={cert.id}
                        onClick={() => onCertificationSelect(cert)}
                        className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-800"
                    >
                        View {cert.name} Details
                    </button>
                ))}
            </div>
        </div>
    );
} 