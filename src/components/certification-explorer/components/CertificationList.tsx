'use client';

import { ICertification } from '../types/certification.types';

interface CertificationListProps {
    certifications: ICertification[];
    onSelectCertification: (certification: ICertification) => void;
    totalCertifications: number;
    page: number;
    pageSize: number;
    onPageChange: (page: number) => void;
    onPageSizeChange: (pageSize: number) => void;
    isLoading?: boolean;
    onAddToComparison: (certification: ICertification) => void;
    certificationsToCompare: ICertification[];
}

export function CertificationList({
    certifications,
    onSelectCertification,
    totalCertifications,
    page,
    pageSize,
    onPageChange,
    onPageSizeChange,
    isLoading = false,
    onAddToComparison,
    certificationsToCompare
}: CertificationListProps) {
    // Calculate total pages based on total items and page size
    const totalPages = Math.ceil(totalCertifications / pageSize);

    // Check if a certification is already in comparison
    const isInComparison = (certId: number): boolean => {
        return certificationsToCompare.some(cert => cert.id === certId);
    };

    if (isLoading) {
        return (
            <div className="loading-placeholder flex items-center justify-center p-8">
                <div className="spinner animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                <p className="ml-3">Loading certifications...</p>
            </div>
        );
    }

    if (certifications.length === 0) {
        return (
            <div className="no-results p-4 bg-yellow-50 text-yellow-800 rounded-md">
                <p>No certifications found matching your criteria.</p>
            </div>
        );
    }

    return (
        <div className="certification-list space-y-6">
            <div className="certification-count text-sm text-gray-600">
                Showing {certifications.length} of {totalCertifications} certifications
            </div>

            <div className="certification-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" data-testid="certifications-grid">
                {certifications.map(cert => (
                    <div
                        key={cert.id}
                        className="certification-card bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer"
                        onClick={() => onSelectCertification(cert)}
                    >
                        <h3 className="text-lg font-semibold mb-2">{cert.name}</h3>
                        <div className="meta-info text-sm text-gray-600 space-y-1">
                            <div>{cert.provider}</div>
                            <div className="flex items-center gap-2">
                                <span className={`px-2 py-1 rounded-full text-xs ${cert.level === 'beginner' ? 'bg-green-100 text-green-800' :
                                        cert.level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                                            cert.level === 'advanced' ? 'bg-red-100 text-red-800' :
                                                'bg-gray-100 text-gray-800'
                                    }`}>
                                    {cert.level}
                                </span>
                                <span className="text-gray-500">{cert.type}</span>
                            </div>
                            {cert.price && (
                                <div className="text-gray-900 font-medium">
                                    ${cert.price.toLocaleString()}
                                </div>
                            )}
                        </div>
                        <button
                            onClick={(e) => {
                                e.stopPropagation();
                                onAddToComparison(cert);
                            }}
                            className={`mt-3 px-3 py-1 rounded-md text-sm font-medium ${isInComparison(cert.id)
                                    ? 'bg-gray-100 text-gray-600'
                                    : 'bg-blue-50 text-blue-600 hover:bg-blue-100'
                                }`}
                        >
                            {isInComparison(cert.id) ? 'Added to Compare' : 'Add to Compare'}
                        </button>
                    </div>
                ))}
            </div>

            {/* Pagination */}
            <div className="pagination flex items-center justify-between mt-6 p-4 bg-white rounded-lg shadow">
                <button
                    onClick={() => onPageChange(page - 1)}
                    disabled={page === 1 || isLoading}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    Previous
                </button>

                <span className="text-sm text-gray-700">
                    Page {page} of {totalPages}
                </span>

                <button
                    onClick={() => onPageChange(page + 1)}
                    disabled={page === totalPages || isLoading}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    Next
                </button>
            </div>
        </div>
    );
} 