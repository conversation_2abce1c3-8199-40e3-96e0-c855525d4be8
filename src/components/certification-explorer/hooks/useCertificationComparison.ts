import { useState } from 'react';
import { ComparisonData, ICertification } from '../types/certification.types';

export function useCertificationComparison() {
  const [certificationsToCompare, setCertificationsToCompare] = useState<ICertification[]>([]);
  const [comparisonData, setComparisonData] = useState<ComparisonData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isComparing, setIsComparing] = useState<boolean>(false);

  const addCertificationToCompare = (certification: ICertification) => {
    if (certificationsToCompare.some(cert => cert.id === certification.id)) {
      removeCertificationFromCompare(certification);
    } else if (certificationsToCompare.length < 3) {
      setCertificationsToCompare(prev => [...prev, certification]);
    } else {
      setError('You can compare up to 3 certifications at a time.');
    }
  };

  const removeCertificationFromCompare = (certification: ICertification) => {
    setCertificationsToCompare(prev =>
      prev.filter(cert => cert.id !== certification.id)
    );
    if (isComparing) {
      cancelComparison();
    }
  };

  const clearComparison = () => {
    setCertificationsToCompare([]);
    setComparisonData(null);
    setIsComparing(false);
    setError(null);
  };

  const startComparison = async () => {
    if (certificationsToCompare.length < 2) {
      setError('Please select at least 2 certifications to compare.');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // TODO: Replace with actual API call
      // For now, generate mock comparison data
      const differences: { [key: string]: { [certId: number]: any } } = {
        price: {},
        duration_minutes: {},
        passing_score: {},
        level: {},
        skills_covered: {}
      };

      const similarities: { [key: string]: any } = {};

      // Compare all fields
      certificationsToCompare.forEach(cert => {
        Object.entries(cert).forEach(([key, value]) => {
          if (key === 'id' || key === 'created_at' || key === 'updated_at') return;

          const allValues = certificationsToCompare.map(c => c[key as keyof ICertification]);
          const allSame = allValues.every(v => JSON.stringify(v) === JSON.stringify(allValues[0]));

          if (allSame) {
            similarities[key] = value;
          } else {
            if (!differences[key]) differences[key] = {};
            differences[key][cert.id] = value;
          }
        });
      });

      setComparisonData({
        certifications: certificationsToCompare,
        differences,
        similarities
      });

      setIsComparing(true);
    } catch (err) {
      console.error('Error comparing certifications:', err);
      setError('Failed to compare certifications. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const cancelComparison = () => {
    setIsComparing(false);
    setComparisonData(null);
    setError(null);
  };

  return {
    certificationsToCompare,
    comparisonData,
    loading,
    error,
    isComparing,
    addCertificationToCompare,
    removeCertificationFromCompare,
    clearComparison,
    startComparison,
    cancelComparison
  };
} 