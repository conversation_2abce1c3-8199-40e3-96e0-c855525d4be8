import { useEffect, useState } from 'react';
import { FilterState, ICertification } from '../types/certification.types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api/v1';

export function useCertifications() {
  const [certifications, setCertifications] = useState<ICertification[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [totalCertifications, setTotalCertifications] = useState<number>(0);
  const [filters, setFilters] = useState<FilterState>({
    provider: '',
    type: '',
    level: '',
    search: '',
    min_price: '',
    max_price: ''
  });
  const [providers, setProviders] = useState<string[]>([]);
  const [selectedCertification, setSelectedCertification] = useState<ICertification | null>(null);
  const [relatedCertifications, setRelatedCertifications] = useState<ICertification[]>([]);

  useEffect(() => {
    const fetchCertifications = async () => {
      try {
        setLoading(true);
        setError(null);

        // Build query parameters
        const queryParams = new URLSearchParams({
          skip: ((page - 1) * pageSize).toString(),
          limit: pageSize.toString(),
          ...(filters.provider && { provider: filters.provider }),
          ...(filters.type && { type: filters.type }),
          ...(filters.level && { level: filters.level }),
          ...(filters.search && { search: filters.search }),
          ...(filters.min_price && { min_price: filters.min_price }),
          ...(filters.max_price && { max_price: filters.max_price })
        });

        const url = `${API_BASE_URL}/certifications?${queryParams}`;
        console.log('[CertExplorer] Fetching certifications:', { url, filters, page, pageSize });

        // Fetch certifications
        const response = await fetch(url);
        console.log('[CertExplorer] Response status:', response.status);
        
        if (!response.ok) {
          const errorText = await response.text();
          console.error('[CertExplorer] API Error:', { status: response.status, body: errorText });
          throw new Error(`Failed to fetch certifications: ${response.status} ${errorText}`);
        }
        
        const data = await response.json();
        console.log('[CertExplorer] Received data:', { 
          certCount: data.certifications?.length, 
          total: data.total,
          firstCert: data.certifications?.[0] 
        });
        
        setCertifications(data.certifications);
        setTotalCertifications(data.total);

        // Fetch providers if not already loaded
        if (providers.length === 0) {
          console.log('[CertExplorer] Fetching providers');
          const providersResponse = await fetch(`${API_BASE_URL}/certifications/providers`);
          
          if (!providersResponse.ok) {
            const errorText = await providersResponse.text();
            console.error('[CertExplorer] Providers API Error:', { 
              status: providersResponse.status, 
              body: errorText 
            });
            throw new Error('Failed to fetch providers');
          }
          
          const providersData = await providersResponse.json();
          console.log('[CertExplorer] Received providers:', providersData);
          setProviders(providersData);
        }
      } catch (err) {
        console.error('[CertExplorer] Error in fetchCertifications:', err);
        setError('Failed to load certifications. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchCertifications();
  }, [page, pageSize, filters]);

  const handleFilterChange = (name: string, value: string) => {
    setFilters(prev => ({ ...prev, [name]: value }));
    setPage(1); // Reset to first page when filters change
  };

  const handleCertificationSelect = async (certification: ICertification) => {
    setSelectedCertification(certification);
    try {
      // Fetch related certifications
      const response = await fetch(`${API_BASE_URL}/certifications/${certification.id}/related`);
      if (!response.ok) {
        throw new Error('Failed to fetch related certifications');
      }
      const data = await response.json();
      setRelatedCertifications(data);
    } catch (err) {
      console.error('Error fetching related certifications:', err);
      setRelatedCertifications([]);
    }
  };

  const handleBackToList = () => {
    setSelectedCertification(null);
    setRelatedCertifications([]);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(1); // Reset to first page when page size changes
  };

  return {
    certifications,
    loading,
    error,
    page,
    pageSize,
    totalCertifications,
    filters,
    providers,
    selectedCertification,
    relatedCertifications,
    handleFilterChange,
    handleCertificationSelect,
    handleBackToList,
    handlePageChange,
    handlePageSizeChange,
  };
} 