'use client';

import { CertificationComparison } from './components/CertificationComparison';
import { CertificationDetail } from './components/CertificationDetail';
import { CertificationFilter } from './components/CertificationFilter';
import { CertificationList } from './components/CertificationList';
import { useCertificationComparison } from './hooks/useCertificationComparison';
import { useCertifications } from './hooks/useCertifications';

export function CertificationExplorer() {
    // Main certifications data and logic
    const {
        certifications,
        loading,
        error,
        page,
        pageSize,
        totalCertifications,
        filters,
        providers,
        selectedCertification,
        relatedCertifications,
        handleFilterChange,
        handleCertificationSelect,
        handleBackToList,
        handlePageChange,
        handlePageSizeChange,
    } = useCertifications();

    // Comparison functionality
    const {
        certificationsToCompare,
        comparisonData,
        loading: comparisonLoading,
        error: comparisonError,
        isComparing,
        addCertificationToCompare,
        removeCertificationFromCompare,
        clearComparison,
        startComparison,
        cancelComparison
    } = useCertificationComparison();

    return (
        <div className="certification-explorer-container" data-testid="certification-explorer">
            <h1 className="text-4xl font-bold mb-8" data-testid="explorer-title">
                Certification Explorer
            </h1>
            <div className="certification-description-container mb-8">
                <p className="text-lg text-gray-600">
                    Explore and compare security certifications to find the right path for your career.
                </p>
            </div>

            {certifications.length === 0 && !loading && !error && (
                <div className="no-data-message p-4 bg-yellow-50 text-yellow-800 rounded-md">
                    <p>No certifications found. Try adjusting your filters or search criteria.</p>
                </div>
            )}

            {/* Display any errors */}
            {error && (
                <div className="error-message p-4 bg-red-50 text-red-800 rounded-md mb-4" role="alert" data-testid="error-message">
                    {error}
                </div>
            )}

            {comparisonError && (
                <div className="error-message p-4 bg-red-50 text-red-800 rounded-md mb-4" role="alert">
                    {comparisonError}
                </div>
            )}

            {/* Loading state */}
            {loading && (
                <div className="loading-state flex items-center justify-center p-8" data-testid="loading-state">
                    <div className="spinner animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                    <p className="ml-3">Loading certifications...</p>
                </div>
            )}

            {/* Comparison mode UI */}
            {isComparing && comparisonData && (
                <CertificationComparison
                    comparisonData={comparisonData}
                    onClose={cancelComparison}
                    onCertificationSelect={handleCertificationSelect}
                />
            )}

            <div className="certification-explorer-content flex gap-6">
                {/* Filters Panel */}
                <CertificationFilter
                    filters={filters}
                    onFilterChange={handleFilterChange}
                    providers={providers}
                    isLoading={loading}
                />

                {/* Main Content Area */}
                <div className="main-content flex-1">
                    {selectedCertification ? (
                        <CertificationDetail
                            certification={selectedCertification}
                            relatedCertifications={relatedCertifications}
                            onBack={handleBackToList}
                            onRelatedCertificationSelect={handleCertificationSelect}
                            onAddToComparison={addCertificationToCompare}
                            isInComparison={certificationsToCompare.some(cert => cert.id === selectedCertification.id)}
                        />
                    ) : (
                        <CertificationList
                            certifications={certifications}
                            onSelectCertification={handleCertificationSelect}
                            totalCertifications={totalCertifications}
                            page={page}
                            pageSize={pageSize}
                            onPageChange={handlePageChange}
                            onPageSizeChange={handlePageSizeChange}
                            isLoading={loading}
                            onAddToComparison={addCertificationToCompare}
                            certificationsToCompare={certificationsToCompare}
                        />
                    )}
                </div>
            </div>
        </div>
    );
} 