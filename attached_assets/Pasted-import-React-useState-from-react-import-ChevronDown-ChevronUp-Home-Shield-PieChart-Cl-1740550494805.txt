import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Home, Shield, Pie<PERSON><PERSON>, Clock, DollarSign, BookOpen, Award, Users, FileText, Settings, Search } from 'lucide-react';

const Navigation = () => {
  const [activeCategory, setActiveCategory] = useState('main');
  const [activeItem, setActiveItem] = useState('visualization');
  
  const categories = [
    {
      id: 'main',
      title: 'Main',
      icon: <Home size={20} />,
      items: [
        { id: 'visualization', label: 'Visualization' },
        { id: 'listings', label: 'Certification Listings' }
      ]
    },
    {
      id: 'tools',
      title: 'Tools',
      icon: <Settings size={20} />,
      items: [
        { id: 'study-time', label: 'Study Time Calculator' },
        { id: 'cost-calculator', label: 'Cost Calculator' },
        { id: 'career-path', label: 'Career Path Generator' }
      ]
    },
    {
      id: 'resources',
      title: 'Resources',
      icon: <BookOpen size={20} />,
      items: [
        { id: 'user-journeys', label: 'User Journeys' },
        { id: 'job-search', label: 'Job Search' },
        { id: 'faq', label: 'FAQ' }
      ]
    }
  ];
  
  return (
    <div className="w-64 h-screen bg-gray-900 text-white flex flex-col">
      {/* App Title */}
      <div className="flex items-center p-4 border-b border-gray-700">
        <Shield className="text-blue-400 mr-2" size={24} />
        <h1 className="text-xl font-bold">CertRat Explorer</h1>
      </div>
      
      {/* User Profile */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
              <span className="font-bold">JD</span>
            </div>
            <div className="ml-2">
              <div className="font-medium">John Doe</div>
              <div className="text-xs text-gray-400">Security Analyst</div>
            </div>
          </div>
          <Settings size={16} className="text-gray-400 cursor-pointer hover:text-white" />
        </div>
      </div>
      
      {/* Search Box */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center bg-gray-800 rounded-md px-3 py-2">
          <Search size={16} className="text-gray-400 mr-2" />
          <input 
            className="bg-transparent border-none focus:outline-none text-sm w-full placeholder-gray-500"
            placeholder="Search certifications..." 
          />
        </div>
      </div>
      
      {/* Navigation Categories */}
      <div className="flex-1 overflow-y-auto py-2">
        {categories.map(category => (
          <div key={category.id} className="mb-2">
            <button
              onClick={() => setActiveCategory(activeCategory === category.id ? null : category.id)}
              className="flex items-center justify-between w-full px-4 py-2 text-left hover:bg-gray-800"
            >
              <div className="flex items-center">
                {category.icon}
                <span className="ml-2 font-medium">{category.title}</span>
              </div>
              {activeCategory === category.id ? 
                <ChevronUp size={16} /> : 
                <ChevronDown size={16} />
              }
            </button>
            
            {activeCategory === category.id && (
              <div className="mt-1">
                {category.items.map(item => (
                  <button 
                    key={item.id}
                    onClick={() => setActiveItem(item.id)}
                    className={`w-full pl-10 pr-4 py-2 text-left text-sm flex items-center ${activeItem === item.id ? 'bg-blue-600' : 'hover:bg-gray-800'}`}
                  >
                    <span>{item.label}</span>
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
      
      {/* Language Selector */}
      <div className="p-4 border-t border-gray-700">
        <select className="w-full bg-gray-800 rounded p-2 text-sm">
          <option value="en">🇬🇧 English</option>
          <option value="de">🇩🇪 German</option>
          <option value="es">🇪🇸 Spanish</option>
          <option value="fr">🇫🇷 French</option>
          <option value="af">🇿🇦 Afrikaans</option>
          <option value="zu">🇿🇦 isiZulu</option>
          <option value="ro">🇷🇴 Romanian</option>
        </select>
      </div>
    </div>
  );
};

export default Navigation;