import streamlit as st
from utils.i18n import switch_language, _
from utils.auth import check_authentication_status
import logging

logger = logging.getLogger(__name__)

def create_navigation():
    """
    Creates and renders the site navigation in the sidebar.
    Returns the currently selected page identifier.
    """
    # App title and logo
    try:
        st.sidebar.image("static/images/logo.png", width=40)
    except FileNotFoundError:
        logger.warning("Logo file not found")
    
    st.sidebar.title(_("CertRat Explorer"))
    
    # User profile section
    if check_authentication_status():
        col1, col2 = st.sidebar.columns([1, 3])
        with col1:
            try:
                st.image(st.session_state.user.get("avatar_url", "static/images/default_avatar.png"), width=40)
            except FileNotFoundError:
                logger.warning("Avatar image not found")
        with col2:
            st.write(f"**{st.session_state.user.get('name', _('User'))}**")
            st.caption(f"{st.session_state.user.get('role', _('Analyst'))}")
    
    # Search box
    search_query = st.sidebar.text_input("🔍 " + _("Search certifications..."))
    if search_query:
        st.session_state.page = "search_results"
        st.session_state.search_query = search_query
    
    # Category-based navigation with expanders
    categories = {
        _("Main"): {
            "icon": "🏠",
            "items": [_("Visualization"), _("Certification Listings"), _("Domain Overview")]
        },
        _("Tools"): {
            "icon": "🔧",
            "items": [_("Study Time Calculator"), _("Cost Calculator"), 
                     _("Career Path Generator"), _("Certification Comparison")]
        },
        _("Resources"): {
            "icon": "📚",
            "items": [_("User Journeys"), _("Job Search"), 
                     _("Learning Resources"), _("FAQ")]
        },
        _("Settings"): {
            "icon": "⚙️",
            "items": [_("Profile"), _("Preferences"), _("API Settings")]
        }
    }
    
    # Create expanders for each category
    for category, details in categories.items():
        with st.sidebar.expander(f"{details['icon']} {category}"):
            for item in details['items']:
                item_id = item.lower().replace(" ", "_")
                if st.button(item, key=f"nav_{item_id}"):
                    st.session_state.page = item_id
                    # Reset any page-specific state when changing pages
                    if item_id != "search_results":
                        st.session_state.pop("search_query", None)
    
    # Language selector at bottom
    st.sidebar.write("---")
    languages = {
        "en": "English",
        "de": "Deutsch",
        "es": "Español", 
        "fr": "Français",
        "af": "Afrikaans",
        "zu": "isiZulu",
        "ro": "Română"
    }
    
    current_lang = st.session_state.get("language", "en")
    selected_language = st.sidebar.selectbox(
        "🌐 " + _("Language"), 
        options=list(languages.keys()),
        format_func=lambda x: languages[x],
        index=list(languages.keys()).index(current_lang) if current_lang in languages else 0
    )
    
    if selected_language != current_lang:
        st.session_state.language = selected_language
        switch_language(selected_language)
        st.experimental_rerun()
    
    # Return the current page to be displayed in main content area
    return st.session_state.get("page", "visualization")