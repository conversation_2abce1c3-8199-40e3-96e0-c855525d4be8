/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fworkspaces%2Fcertrats%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Fcertrats&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fworkspaces%2Fcertrats%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Fcertrats&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/workspaces/certrats/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/workspaces/certrats/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/workspaces/certrats/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPSUyRndvcmtzcGFjZXMlMkZjZXJ0cmF0cyUyRnNyYyUyRmFwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9JTJGd29ya3NwYWNlcyUyRmNlcnRyYXRzJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsYUFBYSxzQkFBc0I7QUFDaUU7QUFDckM7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDLHVCQUF1QixnSkFBMEU7QUFDakc7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0EseUJBQXlCLG9KQUE0RTtBQUNyRyxvQkFBb0IsME5BQWdGO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2VydHJhdHMvP2VkZTciXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL3dvcmtzcGFjZXMvY2VydHJhdHMvc3JjL2FwcC9wYWdlLnRzeFwiKSwgXCIvd29ya3NwYWNlcy9jZXJ0cmF0cy9zcmMvYXBwL3BhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL3dvcmtzcGFjZXMvY2VydHJhdHMvc3JjL2FwcC9sYXlvdXQudHN4XCIpLCBcIi93b3Jrc3BhY2VzL2NlcnRyYXRzL3NyYy9hcHAvbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiL3dvcmtzcGFjZXMvY2VydHJhdHMvc3JjL2FwcC9wYWdlLnRzeFwiXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiO1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCIsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fworkspaces%2Fcertrats%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Fcertrats&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fsrc%2Fapp%2Fpage.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fsrc%2Fcomponents%2Fmode-toggle.tsx%22%2C%22ids%22%3A%5B%22ModeToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fsrc%2Fapp%2Fpage.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fsrc%2Fcomponents%2Fmode-toggle.tsx%22%2C%22ids%22%3A%5B%22ModeToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/mode-toggle.tsx */ \"(ssr)/./src/components/mode-toggle.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/theme-provider.tsx */ \"(ssr)/./src/components/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRndvcmtzcGFjZXMlMkZjZXJ0cmF0cyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZ3b3Jrc3BhY2VzJTJGY2VydHJhdHMlMkZzcmMlMkZhcHAlMkZnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZ3b3Jrc3BhY2VzJTJGY2VydHJhdHMlMkZzcmMlMkZhcHAlMkZwYWdlLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZ3b3Jrc3BhY2VzJTJGY2VydHJhdHMlMkZzcmMlMkZjb21wb25lbnRzJTJGbW9kZS10b2dnbGUudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyTW9kZVRvZ2dsZSUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZ3b3Jrc3BhY2VzJTJGY2VydHJhdHMlMkZzcmMlMkZjb21wb25lbnRzJTJGdGhlbWUtcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVGhlbWVQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEtBQXdIO0FBQ3hIO0FBQ0Esa0xBQThIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2VydHJhdHMvPzQyNzAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJNb2RlVG9nZ2xlXCJdICovIFwiL3dvcmtzcGFjZXMvY2VydHJhdHMvc3JjL2NvbXBvbmVudHMvbW9kZS10b2dnbGUudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUaGVtZVByb3ZpZGVyXCJdICovIFwiL3dvcmtzcGFjZXMvY2VydHJhdHMvc3JjL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fsrc%2Fapp%2Fpage.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fsrc%2Fcomponents%2Fmode-toggle.tsx%22%2C%22ids%22%3A%5B%22ModeToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRndvcmtzcGFjZXMlMkZjZXJ0cmF0cyUyRnNyYyUyRmFwcCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBMEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jZXJ0cmF0cy8/MDk5ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi93b3Jrc3BhY2VzL2NlcnRyYXRzL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Fcertrats%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MainPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction MainPage() {\n    const { theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"/workspaces/certrats/src/app/page.tsx\",\n            lineNumber: 16,\n            columnNumber: 16\n        }, this); // Show loading state instead of null\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"main-page\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"hero-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hero-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                children: \"Navigate Your Cybersecurity Career Path\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: \"Find, Plan, and Achieve the Right Certifications for Your Career Goals\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hero-cta\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/career-path\",\n                                        className: \"cta-button primary\",\n                                        children: \"Get AI Career Path\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/certification-explorer\",\n                                        className: \"cta-button secondary\",\n                                        children: \"Explore Certifications\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hero-visual\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"certification-path-visual\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"visualization-placeholder\",\n                                children: \"Career Path Visualization\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                lineNumber: 22,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"dashboard-preview-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Track Your Certification Journey\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Monitor your progress, study time, and ROI with our comprehensive dashboard\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"dashboard-preview\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"dashboard-preview-placeholder\",\n                                children: \"Dashboard Preview\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"dashboard-features\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"feature\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"Progress Tracking\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Track completion percentage across all certifications\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"feature\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"Study Time Management\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Optimize your study schedule and monitor hours invested\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"feature\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"ROI Calculator\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Calculate the return on investment for each certification\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                lineNumber: 45,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"career-path-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"AI-Powered Career Path Generator\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Get personalized certification recommendations based on your experience, goals, and interests\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"career-path-preview\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"career-path-placeholder\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ai-career-path-features\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"feature-highlight\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        children: \"\\uD83E\\uDD16 AI-Powered Recommendations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Our AI analyzes your profile to suggest the most relevant certifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"feature-highlight\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        children: \"\\uD83D\\uDCB0 Cost & Timeline Analysis\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Get detailed cost breakdowns and realistic timelines for your career path\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"feature-highlight\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        children: \"\\uD83D\\uDCCA Gap Analysis\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Understand the skills gap between your current and target role\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"career-path-cta\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/career-path\",\n                                            className: \"cta-button primary large\",\n                                            children: \"Generate My Career Path\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"popular-paths\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"Popular Career Transitions\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"IT Support → Security Analyst\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"Security Analyst → Security Engineer\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"Network Admin → Penetration Tester\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"Security Engineer → Security Architect\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"Any Role → Cloud Security Specialist\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"popular-paths-cta\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/career-path\",\n                                            className: \"text-link\",\n                                            children: \"See how AI can plan your transition →\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                lineNumber: 70,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"certification-catalog-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Explore Certifications\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Browse our comprehensive catalog of security certifications\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"certification-filters\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"filter-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"Domain\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"filter-options\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"filter-option active\",\n                                                children: \"All\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"filter-option\",\n                                                children: \"Network\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"filter-option\",\n                                                children: \"Cloud\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"filter-option\",\n                                                children: \"Application\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"filter-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"Level\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"filter-options\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"filter-option active\",\n                                                children: \"All\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"filter-option\",\n                                                children: \"Beginner\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"filter-option\",\n                                                children: \"Intermediate\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"filter-option\",\n                                                children: \"Advanced\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"certification-previews\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"certification-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"CompTIA Security+\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"certification-details\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"cert-level\",\n                                                children: \"Beginner\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"cert-cost\",\n                                                children: \"$370\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Foundation-level security certification covering network security, compliance, and risk management.\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"certification-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"CISSP\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"certification-details\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"cert-level\",\n                                                children: \"Advanced\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"cert-cost\",\n                                                children: \"$749\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Advanced certification for security professionals with extensive experience.\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"certification-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"AWS Certified Security\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"certification-details\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"cert-level\",\n                                                children: \"Intermediate\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"cert-cost\",\n                                                children: \"$300\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Specialized certification for securing workloads and data in AWS environments.\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"view-all-link\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/certification-explorer\",\n                            children: \"View All Certifications\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                lineNumber: 114,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"success-stories-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Success Stories\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Real outcomes from security professionals using our platform\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"stories-container\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"success-story\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"story-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"user-avatar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"avatar-placeholder\",\n                                                    children: \"JD\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 33\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"user-info\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        children: \"John Doe\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Security Analyst → Security Engineer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"story-content\",\n                                        children: '\"The certification roadmap helped me identify the exact certifications I needed to advance my career. Within 8 months, I earned my CISSP and landed a senior security engineer position with a 30% salary increase.\"'\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"story-metrics\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"metric\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"metric-value\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"metric-label\",\n                                                        children: \"Certifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"metric\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"metric-value\",\n                                                        children: \"30%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"metric-label\",\n                                                        children: \"Salary Increase\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"metric\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"metric-value\",\n                                                        children: \"8\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"metric-label\",\n                                                        children: \"Months\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"success-story\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"story-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"user-avatar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"avatar-placeholder\",\n                                                    children: \"JS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 33\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"user-info\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        children: \"Jane Smith\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"IT Support → Penetration Tester\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"story-content\",\n                                        children: \"\\\"I wanted to transition to offensive security but didn't know where to start. The platform guided me through the right certifications and study materials. I'm now working as a junior penetration tester!\\\"\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"story-metrics\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"metric\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"metric-value\",\n                                                        children: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"metric-label\",\n                                                        children: \"Certifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"metric\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"metric-value\",\n                                                        children: \"45%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"metric-label\",\n                                                        children: \"Salary Increase\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"metric\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"metric-value\",\n                                                        children: \"14\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 33\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"metric-label\",\n                                                        children: \"Months\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 33\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                lineNumber: 171,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"getting-started-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"How to Get Started\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Begin your certification journey in three easy steps\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"steps-container\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"step\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step-number\",\n                                        children: \"1\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"Create Your Profile\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Tell us about your current skills, experience, and career goals\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"step\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step-number\",\n                                        children: \"2\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"Get AI Career Path\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Our AI analyzes your profile and generates a personalized certification roadmap\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"step\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"step-number\",\n                                        children: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"Start Learning\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Access study materials and track your progress towards certification\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"getting-started-cta\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/career-path\",\n                            className: \"cta-button primary\",\n                            children: \"Get Started with AI Career Path\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/certrats/src/app/page.tsx\",\n                lineNumber: 238,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/certrats/src/app/page.tsx\",\n        lineNumber: 20,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/mode-toggle.tsx":
/*!****************************************!*\
  !*** ./src/components/mode-toggle.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModeToggle: () => (/* binding */ ModeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ModeToggle auto */ \n\nfunction ModeToggle() {\n    const { setTheme, theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    // Pick icon based on theme\n    let icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: \"/sun.svg\",\n        alt: \"Sun\",\n        width: 20,\n        height: 20\n    }, void 0, false, {\n        fileName: \"/workspaces/certrats/src/components/mode-toggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 16\n    }, this);\n    if (theme === \"dark\") icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: \"/moon.svg\",\n        alt: \"Moon\",\n        width: 20,\n        height: 20\n    }, void 0, false, {\n        fileName: \"/workspaces/certrats/src/components/mode-toggle.tsx\",\n        lineNumber: 10,\n        columnNumber: 34\n    }, this);\n    if (theme === \"crt\") icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: \"/monitor.svg\",\n        alt: \"CRT\",\n        width: 20,\n        height: 20,\n        style: {\n            filter: \"drop-shadow(0 0 4px #39ff14)\"\n        }\n    }, void 0, false, {\n        fileName: \"/workspaces/certrats/src/components/mode-toggle.tsx\",\n        lineNumber: 11,\n        columnNumber: 33\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            gap: \"0.5rem\",\n            alignItems: \"center\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setTheme(\"light\"),\n                \"aria-label\": \"Switch to light mode\",\n                children: [\n                    theme === \"light\" ? icon : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/sun.svg\",\n                        alt: \"Sun\",\n                        width: 20,\n                        height: 20\n                    }, void 0, false, {\n                        fileName: \"/workspaces/certrats/src/components/mode-toggle.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 117\n                    }, this),\n                    \" Light\"\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/certrats/src/components/mode-toggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setTheme(\"dark\"),\n                \"aria-label\": \"Switch to dark mode\",\n                children: [\n                    theme === \"dark\" ? icon : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/moon.svg\",\n                        alt: \"Moon\",\n                        width: 20,\n                        height: 20\n                    }, void 0, false, {\n                        fileName: \"/workspaces/certrats/src/components/mode-toggle.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 114\n                    }, this),\n                    \" Dark\"\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/certrats/src/components/mode-toggle.tsx\",\n                lineNumber: 16,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setTheme(\"crt\"),\n                \"aria-label\": \"Switch to CRT mode\",\n                children: [\n                    theme === \"crt\" ? icon : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/monitor.svg\",\n                        alt: \"CRT\",\n                        width: 20,\n                        height: 20,\n                        style: {\n                            filter: \"drop-shadow(0 0 4px #39ff14)\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/workspaces/certrats/src/components/mode-toggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 111\n                    }, this),\n                    \" CRT Terminal\"\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/certrats/src/components/mode-toggle.tsx\",\n                lineNumber: 17,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/certrats/src/components/mode-toggle.tsx\",\n        lineNumber: 14,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/mode-toggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/theme-provider.tsx":
/*!*******************************************!*\
  !*** ./src/components/theme-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/workspaces/certrats/src/components/theme-provider.tsx\",\n        lineNumber: 7,\n        columnNumber: 12\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy90aGVtZS1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFa0U7QUFHM0QsU0FBU0EsY0FBYyxFQUFFRSxRQUFRLEVBQUUsR0FBR0MsT0FBMkI7SUFDcEUscUJBQU8sOERBQUNGLHNEQUFrQkE7UUFBRSxHQUFHRSxLQUFLO2tCQUFHRDs7Ozs7O0FBQzNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2VydHJhdHMvLi9zcmMvY29tcG9uZW50cy90aGVtZS1wcm92aWRlci50c3g/YjY5NiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSAnbmV4dC10aGVtZXMnO1xuaW1wb3J0IHsgdHlwZSBUaGVtZVByb3ZpZGVyUHJvcHMgfSBmcm9tICduZXh0LXRoZW1lcy9kaXN0L3R5cGVzJztcblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gICAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+O1xufSAiXSwibmFtZXMiOlsiVGhlbWVQcm92aWRlciIsIk5leHRUaGVtZXNQcm92aWRlciIsImNoaWxkcmVuIiwicHJvcHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/theme-provider.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5029949cf48f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2VydHJhdHMvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzI1YmUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1MDI5OTQ5Y2Y0OGZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/page.css":
/*!**************************!*\
  !*** ./src/app/page.css ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"236313428bb2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2VydHJhdHMvLi9zcmMvYXBwL3BhZ2UuY3NzPzA1MzMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyMzYzMTM0MjhiYjJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_mode_toggle__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/mode-toggle */ \"(rsc)/./src/components/mode-toggle.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./src/components/theme-provider.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _page_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./page.css */ \"(rsc)/./src/app/page.css\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"CertRats\",\n    description: \"Your comprehensive certification management platform\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className)} dark:bg-[#0a0a0a]`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"dark\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-background text-foreground dark:terminal-glow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-grid-white/[0.02] bg-[size:50px_50px] dark:bg-grid-primary/[0.02]\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/certrats/src/app/layout.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 flex items-center justify-center bg-background [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)] dark:bg-black/[0.95]\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/certrats/src/app/layout.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                    className: \"border-b dark:border-primary/20 dark:terminal-border backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"container mx-auto px-4 py-4 flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold dark:text-primary dark:terminal-text-glow\",\n                                                children: \"CertRats\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/layout.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mode_toggle__WEBPACK_IMPORTED_MODULE_1__.ModeToggle, {}, void 0, false, {\n                                                fileName: \"/workspaces/certrats/src/app/layout.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/certrats/src/app/layout.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/certrats/src/app/layout.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                    className: \"container mx-auto px-4 py-8\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/certrats/src/app/layout.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/certrats/src/app/layout.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/certrats/src/app/layout.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"/workspaces/certrats/src/app/layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/workspaces/certrats/src/app/layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"/workspaces/certrats/src/app/layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/workspaces/certrats/src/app/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/mode-toggle.tsx":
/*!****************************************!*\
  !*** ./src/components/mode-toggle.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ModeToggle: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/workspaces/certrats/src/components/mode-toggle.tsx#ModeToggle`);


/***/ }),

/***/ "(rsc)/./src/components/theme-provider.tsx":
/*!*******************************************!*\
  !*** ./src/components/theme-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/workspaces/certrats/src/components/theme-provider.tsx#ThemeProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/next-themes"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fworkspaces%2Fcertrats%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Fcertrats&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();