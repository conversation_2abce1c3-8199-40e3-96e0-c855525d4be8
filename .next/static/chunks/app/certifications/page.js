/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/certifications/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fcvr%2Fdev%2F10Baht%2Fcertrats%2Fsrc%2Fcomponents%2Fcertification-explorer%2FCertificationExplorer.tsx%22%2C%22ids%22%3A%5B%22CertificationExplorer%22%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fcvr%2Fdev%2F10Baht%2Fcertrats%2Fsrc%2Fcomponents%2Fcertification-explorer%2FCertificationExplorer.tsx%22%2C%22ids%22%3A%5B%22CertificationExplorer%22%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/certification-explorer/CertificationExplorer.tsx */ \"(app-pages-browser)/./src/components/certification-explorer/CertificationExplorer.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGaG9tZSUyRmN2ciUyRmRldiUyRjEwQmFodCUyRmNlcnRyYXRzJTJGc3JjJTJGY29tcG9uZW50cyUyRmNlcnRpZmljYXRpb24tZXhwbG9yZXIlMkZDZXJ0aWZpY2F0aW9uRXhwbG9yZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQ2VydGlmaWNhdGlvbkV4cGxvcmVyJTIyJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsNFBBQTZLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/MmIzZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkNlcnRpZmljYXRpb25FeHBsb3JlclwiXSAqLyBcIi9ob21lL2N2ci9kZXYvMTBCYWh0L2NlcnRyYXRzL3NyYy9jb21wb25lbnRzL2NlcnRpZmljYXRpb24tZXhwbG9yZXIvQ2VydGlmaWNhdGlvbkV4cGxvcmVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fcvr%2Fdev%2F10Baht%2Fcertrats%2Fsrc%2Fcomponents%2Fcertification-explorer%2FCertificationExplorer.tsx%22%2C%22ids%22%3A%5B%22CertificationExplorer%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/certification-explorer/CertificationExplorer.tsx":
/*!*************************************************************************!*\
  !*** ./src/components/certification-explorer/CertificationExplorer.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CertificationExplorer: function() { return /* binding */ CertificationExplorer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_CertificationComparison__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/CertificationComparison */ \"(app-pages-browser)/./src/components/certification-explorer/components/CertificationComparison.tsx\");\n/* harmony import */ var _components_CertificationDetail__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/CertificationDetail */ \"(app-pages-browser)/./src/components/certification-explorer/components/CertificationDetail.tsx\");\n/* harmony import */ var _components_CertificationFilter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/CertificationFilter */ \"(app-pages-browser)/./src/components/certification-explorer/components/CertificationFilter.tsx\");\n/* harmony import */ var _components_CertificationList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/CertificationList */ \"(app-pages-browser)/./src/components/certification-explorer/components/CertificationList.tsx\");\n/* harmony import */ var _hooks_useCertificationComparison__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./hooks/useCertificationComparison */ \"(app-pages-browser)/./src/components/certification-explorer/hooks/useCertificationComparison.ts\");\n/* harmony import */ var _hooks_useCertifications__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./hooks/useCertifications */ \"(app-pages-browser)/./src/components/certification-explorer/hooks/useCertifications.ts\");\n/* __next_internal_client_entry_do_not_use__ CertificationExplorer auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CertificationExplorer() {\n    _s();\n    // Main certifications data and logic\n    const { certifications, loading, error, page, pageSize, totalCertifications, filters, providers, selectedCertification, relatedCertifications, handleFilterChange, handleCertificationSelect, handleBackToList, handlePageChange, handlePageSizeChange } = (0,_hooks_useCertifications__WEBPACK_IMPORTED_MODULE_6__.useCertifications)();\n    // Comparison functionality\n    const { certificationsToCompare, comparisonData, loading: comparisonLoading, error: comparisonError, isComparing, addCertificationToCompare, removeCertificationFromCompare, clearComparison, startComparison, cancelComparison } = (0,_hooks_useCertificationComparison__WEBPACK_IMPORTED_MODULE_5__.useCertificationComparison)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"certification-explorer-container\",\n        \"data-testid\": \"certification-explorer\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold mb-8\",\n                \"data-testid\": \"explorer-title\",\n                children: \"Certification Explorer\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n                lineNumber: 46,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"certification-description-container mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg text-gray-600\",\n                    children: \"Explore and compare security certifications to find the right path for your career.\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n                lineNumber: 49,\n                columnNumber: 13\n            }, this),\n            certifications.length === 0 && !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"no-data-message p-4 bg-yellow-50 text-yellow-800 rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"No certifications found. Try adjusting your filters or search criteria.\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n                lineNumber: 56,\n                columnNumber: 17\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"error-message p-4 bg-red-50 text-red-800 rounded-md mb-4\",\n                role: \"alert\",\n                \"data-testid\": \"error-message\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n                lineNumber: 63,\n                columnNumber: 17\n            }, this),\n            comparisonError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"error-message p-4 bg-red-50 text-red-800 rounded-md mb-4\",\n                role: \"alert\",\n                children: comparisonError\n            }, void 0, false, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n                lineNumber: 69,\n                columnNumber: 17\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"loading-state flex items-center justify-center p-8\",\n                \"data-testid\": \"loading-state\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"ml-3\",\n                        children: \"Loading certifications...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n                lineNumber: 76,\n                columnNumber: 17\n            }, this),\n            isComparing && comparisonData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CertificationComparison__WEBPACK_IMPORTED_MODULE_1__.CertificationComparison, {\n                comparisonData: comparisonData,\n                onClose: cancelComparison,\n                onCertificationSelect: handleCertificationSelect\n            }, void 0, false, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n                lineNumber: 84,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"certification-explorer-content flex gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CertificationFilter__WEBPACK_IMPORTED_MODULE_3__.CertificationFilter, {\n                        filters: filters,\n                        onFilterChange: handleFilterChange,\n                        providers: providers,\n                        isLoading: loading\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"main-content flex-1\",\n                        children: selectedCertification ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CertificationDetail__WEBPACK_IMPORTED_MODULE_2__.CertificationDetail, {\n                            certification: selectedCertification,\n                            relatedCertifications: relatedCertifications,\n                            onBack: handleBackToList,\n                            onRelatedCertificationSelect: handleCertificationSelect,\n                            onAddToComparison: addCertificationToCompare,\n                            isInComparison: certificationsToCompare.some((cert)=>cert.id === selectedCertification.id)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 25\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CertificationList__WEBPACK_IMPORTED_MODULE_4__.CertificationList, {\n                            certifications: certifications,\n                            onSelectCertification: handleCertificationSelect,\n                            totalCertifications: totalCertifications,\n                            page: page,\n                            pageSize: pageSize,\n                            onPageChange: handlePageChange,\n                            onPageSizeChange: handlePageSizeChange,\n                            isLoading: loading,\n                            onAddToComparison: addCertificationToCompare,\n                            certificationsToCompare: certificationsToCompare\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n                lineNumber: 91,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/CertificationExplorer.tsx\",\n        lineNumber: 45,\n        columnNumber: 9\n    }, this);\n}\n_s(CertificationExplorer, \"nWHAvK5OGdUjKKbaIc1NDGHpdfU=\", false, function() {\n    return [\n        _hooks_useCertifications__WEBPACK_IMPORTED_MODULE_6__.useCertifications,\n        _hooks_useCertificationComparison__WEBPACK_IMPORTED_MODULE_5__.useCertificationComparison\n    ];\n});\n_c = CertificationExplorer;\nvar _c;\n$RefreshReg$(_c, \"CertificationExplorer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/certification-explorer/CertificationExplorer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/certification-explorer/components/CertificationComparison.tsx":
/*!**************************************************************************************!*\
  !*** ./src/components/certification-explorer/components/CertificationComparison.tsx ***!
  \**************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CertificationComparison: function() { return /* binding */ CertificationComparison; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ CertificationComparison auto */ \nfunction CertificationComparison(param) {\n    let { comparisonData, onClose, onCertificationSelect } = param;\n    const formatValue = (value)=>{\n        if (value === null || value === undefined) return \"Not available\";\n        if (typeof value === \"number\") {\n            if (value > 1000) return \"$\".concat(value.toLocaleString());\n            return value.toString();\n        }\n        if (Array.isArray(value)) return value.join(\", \");\n        return value.toString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"comparison-container bg-white rounded-lg shadow-lg p-6 mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold\",\n                        children: \"Certification Comparison\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"text-gray-600 hover:text-gray-900\",\n                        children: \"\\xd7 Close\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                lineNumber: 28,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"py-4 px-6 text-left text-gray-500 font-medium\",\n                                        children: \"Feature\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 29\n                                    }, this),\n                                    comparisonData.certifications.map((cert)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"py-4 px-6 text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-bold\",\n                                                    children: cert.name\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: cert.provider\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, cert.id, true, {\n                                            fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 33\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: [\n                                Object.entries(comparisonData.similarities).map((param)=>{\n                                    let [key, value] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b bg-green-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"py-4 px-6 font-medium\",\n                                                children: key\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"py-4 px-6 text-green-700\",\n                                                colSpan: comparisonData.certifications.length,\n                                                children: formatValue(value)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, key, true, {\n                                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 29\n                                    }, this);\n                                }),\n                                Object.entries(comparisonData.differences).map((param)=>{\n                                    let [key, values] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"py-4 px-6 font-medium\",\n                                                children: key\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 33\n                                            }, this),\n                                            comparisonData.certifications.map((cert)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-4 px-6\",\n                                                    children: formatValue(values[cert.id])\n                                                }, cert.id, false, {\n                                                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 37\n                                                }, this))\n                                        ]\n                                    }, key, true, {\n                                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 29\n                                    }, this);\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                lineNumber: 38,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 flex justify-end space-x-4\",\n                children: comparisonData.certifications.map((cert)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onCertificationSelect(cert),\n                        className: \"px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-800\",\n                        children: [\n                            \"View \",\n                            cert.name,\n                            \" Details\"\n                        ]\n                    }, cert.id, true, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 21\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n                lineNumber: 80,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationComparison.tsx\",\n        lineNumber: 27,\n        columnNumber: 9\n    }, this);\n}\n_c = CertificationComparison;\nvar _c;\n$RefreshReg$(_c, \"CertificationComparison\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/certification-explorer/components/CertificationComparison.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/certification-explorer/components/CertificationDetail.tsx":
/*!**********************************************************************************!*\
  !*** ./src/components/certification-explorer/components/CertificationDetail.tsx ***!
  \**********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CertificationDetail: function() { return /* binding */ CertificationDetail; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ CertificationDetail auto */ \nfunction CertificationDetail(param) {\n    let { certification, relatedCertifications, onBack, onRelatedCertificationSelect, onAddToComparison, isInComparison } = param;\n    const formatDuration = (minutes)=>{\n        if (minutes === null) return \"Not available\";\n        const hours = Math.floor(minutes / 60);\n        const mins = minutes % 60;\n        return hours > 0 ? \"\".concat(hours, \"h \").concat(mins, \"m\") : \"\".concat(mins, \"m\");\n    };\n    const formatPrice = (price)=>{\n        if (price === null) return \"Not available\";\n        return \"$\".concat(price.toLocaleString());\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"certification-detail bg-white rounded-lg shadow-lg p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        className: \"mr-4 text-gray-600 hover:text-gray-900\",\n                        children: \"← Back\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold flex-grow\",\n                        children: certification.name\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onAddToComparison(certification),\n                        className: \"px-4 py-2 rounded-md text-sm font-medium \".concat(isInComparison ? \"bg-gray-100 text-gray-600\" : \"bg-blue-50 text-blue-600 hover:bg-blue-100\"),\n                        children: isInComparison ? \"Added to Compare\" : \"Add to Compare\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                lineNumber: 36,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: \"Provider\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: certification.provider\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: \"Level\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 rounded-full text-sm \".concat(certification.level === \"beginner\" ? \"bg-green-100 text-green-800\" : certification.level === \"intermediate\" ? \"bg-yellow-100 text-yellow-800\" : certification.level === \"advanced\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                        children: certification.level\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: certification.type\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-2\",\n                                    children: \"Exam Details\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Exam Code\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-900\",\n                                                    children: certification.exam_code || \"Not available\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Price\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-900\",\n                                                    children: formatPrice(certification.price)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Duration\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-900\",\n                                                    children: formatDuration(certification.duration_minutes)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Passing Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-900\",\n                                                    children: certification.passing_score ? \"\".concat(certification.passing_score, \"%\") : \"Not available\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                lineNumber: 55,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Description\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: certification.description || \"No description available.\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                lineNumber: 104,\n                columnNumber: 13\n            }, this),\n            certification.skills_covered && certification.skills_covered.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Skills Covered\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: certification.skills_covered.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm\",\n                                children: skill\n                            }, index, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 29\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                lineNumber: 110,\n                columnNumber: 17\n            }, this),\n            certification.prerequisites && certification.prerequisites.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Prerequisites\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside space-y-2 text-gray-600\",\n                        children: certification.prerequisites.map((prerequisite, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: prerequisite\n                            }, index, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 29\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                lineNumber: 126,\n                columnNumber: 17\n            }, this),\n            certification.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: certification.url,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                    children: \"Visit Official Page\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                lineNumber: 137,\n                columnNumber: 17\n            }, this),\n            relatedCertifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Related Certifications\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: relatedCertifications.map((cert)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border rounded-lg cursor-pointer hover:bg-gray-50\",\n                                onClick: ()=>onRelatedCertificationSelect(cert),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium mb-2\",\n                                        children: cert.name\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: cert.provider\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 rounded-full text-xs \".concat(cert.level === \"beginner\" ? \"bg-green-100 text-green-800\" : cert.level === \"intermediate\" ? \"bg-yellow-100 text-yellow-800\" : cert.level === \"advanced\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                            children: cert.level\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, cert.id, true, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 29\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n                lineNumber: 150,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationDetail.tsx\",\n        lineNumber: 35,\n        columnNumber: 9\n    }, this);\n}\n_c = CertificationDetail;\nvar _c;\n$RefreshReg$(_c, \"CertificationDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NlcnRpZmljYXRpb24tZXhwbG9yZXIvY29tcG9uZW50cy9DZXJ0aWZpY2F0aW9uRGV0YWlsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFhTyxTQUFTQSxvQkFBb0IsS0FPVDtRQVBTLEVBQ2hDQyxhQUFhLEVBQ2JDLHFCQUFxQixFQUNyQkMsTUFBTSxFQUNOQyw0QkFBNEIsRUFDNUJDLGlCQUFpQixFQUNqQkMsY0FBYyxFQUNTLEdBUFM7SUFRaEMsTUFBTUMsaUJBQWlCLENBQUNDO1FBQ3BCLElBQUlBLFlBQVksTUFBTSxPQUFPO1FBQzdCLE1BQU1DLFFBQVFDLEtBQUtDLEtBQUssQ0FBQ0gsVUFBVTtRQUNuQyxNQUFNSSxPQUFPSixVQUFVO1FBQ3ZCLE9BQU9DLFFBQVEsSUFBSSxHQUFhRyxPQUFWSCxPQUFNLE1BQVMsT0FBTEcsTUFBSyxPQUFLLEdBQVEsT0FBTEEsTUFBSztJQUN0RDtJQUVBLE1BQU1DLGNBQWMsQ0FBQ0M7UUFDakIsSUFBSUEsVUFBVSxNQUFNLE9BQU87UUFDM0IsT0FBTyxJQUEyQixPQUF2QkEsTUFBTUMsY0FBYztJQUNuQztJQUVBLHFCQUNJLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDWCw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNYLDhEQUFDQzt3QkFDR0MsU0FBU2hCO3dCQUNUYyxXQUFVO2tDQUNiOzs7Ozs7a0NBR0QsOERBQUNHO3dCQUFHSCxXQUFVO2tDQUFnQ2hCLGNBQWNvQixJQUFJOzs7Ozs7a0NBQ2hFLDhEQUFDSDt3QkFDR0MsU0FBUyxJQUFNZCxrQkFBa0JKO3dCQUNqQ2dCLFdBQVcsNENBR04sT0FIa0RYLGlCQUM3Qyw4QkFDQTtrQ0FHVEEsaUJBQWlCLHFCQUFxQjs7Ozs7Ozs7Ozs7OzBCQUkvQyw4REFBQ1U7Z0JBQUlDLFdBQVU7O2tDQUNYLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ1gsOERBQUNEOztrREFDRyw4REFBQ007d0NBQUdMLFdBQVU7a0RBQTZCOzs7Ozs7a0RBQzNDLDhEQUFDTTt3Q0FBRU4sV0FBVTtrREFBaUJoQixjQUFjdUIsUUFBUTs7Ozs7Ozs7Ozs7OzBDQUd4RCw4REFBQ1I7O2tEQUNHLDhEQUFDTTt3Q0FBR0wsV0FBVTtrREFBNkI7Ozs7OztrREFDM0MsOERBQUNRO3dDQUFLUixXQUFXLGtDQUlaLE9BSjhDaEIsY0FBY3lCLEtBQUssS0FBSyxhQUFhLGdDQUNoRnpCLGNBQWN5QixLQUFLLEtBQUssaUJBQWlCLGtDQUNyQ3pCLGNBQWN5QixLQUFLLEtBQUssYUFBYSw0QkFDakM7a0RBRVh6QixjQUFjeUIsS0FBSzs7Ozs7Ozs7Ozs7OzBDQUk1Qiw4REFBQ1Y7O2tEQUNHLDhEQUFDTTt3Q0FBR0wsV0FBVTtrREFBNkI7Ozs7OztrREFDM0MsOERBQUNNO3dDQUFFTixXQUFVO2tEQUFpQmhCLGNBQWMwQixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSXhELDhEQUFDWDt3QkFBSUMsV0FBVTtrQ0FDWCw0RUFBQ0Q7OzhDQUNHLDhEQUFDTTtvQ0FBR0wsV0FBVTs4Q0FBNkI7Ozs7Ozs4Q0FDM0MsOERBQUNEO29DQUFJQyxXQUFVOztzREFDWCw4REFBQ0Q7OzhEQUNHLDhEQUFDTztvREFBRU4sV0FBVTs4REFBd0I7Ozs7Ozs4REFDckMsOERBQUNNO29EQUFFTixXQUFVOzhEQUFpQmhCLGNBQWMyQixTQUFTLElBQUk7Ozs7Ozs7Ozs7OztzREFFN0QsOERBQUNaOzs4REFDRyw4REFBQ087b0RBQUVOLFdBQVU7OERBQXdCOzs7Ozs7OERBQ3JDLDhEQUFDTTtvREFBRU4sV0FBVTs4REFBaUJKLFlBQVlaLGNBQWNhLEtBQUs7Ozs7Ozs7Ozs7OztzREFFakUsOERBQUNFOzs4REFDRyw4REFBQ087b0RBQUVOLFdBQVU7OERBQXdCOzs7Ozs7OERBQ3JDLDhEQUFDTTtvREFBRU4sV0FBVTs4REFBaUJWLGVBQWVOLGNBQWM0QixnQkFBZ0I7Ozs7Ozs7Ozs7OztzREFFL0UsOERBQUNiOzs4REFDRyw4REFBQ087b0RBQUVOLFdBQVU7OERBQXdCOzs7Ozs7OERBQ3JDLDhEQUFDTTtvREFBRU4sV0FBVTs4REFBaUJoQixjQUFjNkIsYUFBYSxHQUFHLEdBQStCLE9BQTVCN0IsY0FBYzZCLGFBQWEsRUFBQyxPQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPcEgsOERBQUNkO2dCQUFJQyxXQUFVOztrQ0FDWCw4REFBQ0s7d0JBQUdMLFdBQVU7a0NBQTZCOzs7Ozs7a0NBQzNDLDhEQUFDTTt3QkFBRU4sV0FBVTtrQ0FBaUJoQixjQUFjOEIsV0FBVyxJQUFJOzs7Ozs7Ozs7Ozs7WUFHOUQ5QixjQUFjK0IsY0FBYyxJQUFJL0IsY0FBYytCLGNBQWMsQ0FBQ0MsTUFBTSxHQUFHLG1CQUNuRSw4REFBQ2pCO2dCQUFJQyxXQUFVOztrQ0FDWCw4REFBQ0s7d0JBQUdMLFdBQVU7a0NBQTZCOzs7Ozs7a0NBQzNDLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDVmhCLGNBQWMrQixjQUFjLENBQUNFLEdBQUcsQ0FBQyxDQUFDQyxPQUFPQyxzQkFDdEMsOERBQUNYO2dDQUVHUixXQUFVOzBDQUVUa0I7K0JBSElDOzs7Ozs7Ozs7Ozs7Ozs7O1lBVXhCbkMsY0FBY29DLGFBQWEsSUFBSXBDLGNBQWNvQyxhQUFhLENBQUNKLE1BQU0sR0FBRyxtQkFDakUsOERBQUNqQjtnQkFBSUMsV0FBVTs7a0NBQ1gsOERBQUNLO3dCQUFHTCxXQUFVO2tDQUE2Qjs7Ozs7O2tDQUMzQyw4REFBQ3FCO3dCQUFHckIsV0FBVTtrQ0FDVGhCLGNBQWNvQyxhQUFhLENBQUNILEdBQUcsQ0FBQyxDQUFDSyxjQUFjSCxzQkFDNUMsOERBQUNJOzBDQUFnQkQ7K0JBQVJIOzs7Ozs7Ozs7Ozs7Ozs7O1lBTXhCbkMsY0FBY3dDLEdBQUcsa0JBQ2QsOERBQUN6QjtnQkFBSUMsV0FBVTswQkFDWCw0RUFBQ3lCO29CQUNHQyxNQUFNMUMsY0FBY3dDLEdBQUc7b0JBQ3ZCRyxRQUFPO29CQUNQQyxLQUFJO29CQUNKNUIsV0FBVTs4QkFDYjs7Ozs7Ozs7Ozs7WUFNUmYsc0JBQXNCK0IsTUFBTSxHQUFHLG1CQUM1Qiw4REFBQ2pCOztrQ0FDRyw4REFBQ007d0JBQUdMLFdBQVU7a0NBQTZCOzs7Ozs7a0NBQzNDLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDVmYsc0JBQXNCZ0MsR0FBRyxDQUFDWSxDQUFBQSxxQkFDdkIsOERBQUM5QjtnQ0FFR0MsV0FBVTtnQ0FDVkUsU0FBUyxJQUFNZiw2QkFBNkIwQzs7a0RBRTVDLDhEQUFDQzt3Q0FBRzlCLFdBQVU7a0RBQW9CNkIsS0FBS3pCLElBQUk7Ozs7OztrREFDM0MsOERBQUNMO3dDQUFJQyxXQUFVO2tEQUF5QjZCLEtBQUt0QixRQUFROzs7Ozs7a0RBQ3JELDhEQUFDUjt3Q0FBSUMsV0FBVTtrREFDWCw0RUFBQ1E7NENBQUtSLFdBQVcsa0NBSVosT0FKOEM2QixLQUFLcEIsS0FBSyxLQUFLLGFBQWEsZ0NBQ3ZFb0IsS0FBS3BCLEtBQUssS0FBSyxpQkFBaUIsa0NBQzVCb0IsS0FBS3BCLEtBQUssS0FBSyxhQUFhLDRCQUN4QjtzREFFWG9CLEtBQUtwQixLQUFLOzs7Ozs7Ozs7Ozs7K0JBWmRvQixLQUFLRSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBc0I1QztLQW5LZ0JoRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9jZXJ0aWZpY2F0aW9uLWV4cGxvcmVyL2NvbXBvbmVudHMvQ2VydGlmaWNhdGlvbkRldGFpbC50c3g/YjA1NiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IElDZXJ0aWZpY2F0aW9uIH0gZnJvbSAnLi4vdHlwZXMvY2VydGlmaWNhdGlvbi50eXBlcyc7XG5cbmludGVyZmFjZSBDZXJ0aWZpY2F0aW9uRGV0YWlsUHJvcHMge1xuICAgIGNlcnRpZmljYXRpb246IElDZXJ0aWZpY2F0aW9uO1xuICAgIHJlbGF0ZWRDZXJ0aWZpY2F0aW9uczogSUNlcnRpZmljYXRpb25bXTtcbiAgICBvbkJhY2s6ICgpID0+IHZvaWQ7XG4gICAgb25SZWxhdGVkQ2VydGlmaWNhdGlvblNlbGVjdDogKGNlcnRpZmljYXRpb246IElDZXJ0aWZpY2F0aW9uKSA9PiB2b2lkO1xuICAgIG9uQWRkVG9Db21wYXJpc29uOiAoY2VydGlmaWNhdGlvbjogSUNlcnRpZmljYXRpb24pID0+IHZvaWQ7XG4gICAgaXNJbkNvbXBhcmlzb246IGJvb2xlYW47XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBDZXJ0aWZpY2F0aW9uRGV0YWlsKHtcbiAgICBjZXJ0aWZpY2F0aW9uLFxuICAgIHJlbGF0ZWRDZXJ0aWZpY2F0aW9ucyxcbiAgICBvbkJhY2ssXG4gICAgb25SZWxhdGVkQ2VydGlmaWNhdGlvblNlbGVjdCxcbiAgICBvbkFkZFRvQ29tcGFyaXNvbixcbiAgICBpc0luQ29tcGFyaXNvblxufTogQ2VydGlmaWNhdGlvbkRldGFpbFByb3BzKSB7XG4gICAgY29uc3QgZm9ybWF0RHVyYXRpb24gPSAobWludXRlczogbnVtYmVyIHwgbnVsbCk6IHN0cmluZyA9PiB7XG4gICAgICAgIGlmIChtaW51dGVzID09PSBudWxsKSByZXR1cm4gJ05vdCBhdmFpbGFibGUnO1xuICAgICAgICBjb25zdCBob3VycyA9IE1hdGguZmxvb3IobWludXRlcyAvIDYwKTtcbiAgICAgICAgY29uc3QgbWlucyA9IG1pbnV0ZXMgJSA2MDtcbiAgICAgICAgcmV0dXJuIGhvdXJzID4gMCA/IGAke2hvdXJzfWggJHttaW5zfW1gIDogYCR7bWluc31tYDtcbiAgICB9O1xuXG4gICAgY29uc3QgZm9ybWF0UHJpY2UgPSAocHJpY2U6IG51bWJlciB8IG51bGwpOiBzdHJpbmcgPT4ge1xuICAgICAgICBpZiAocHJpY2UgPT09IG51bGwpIHJldHVybiAnTm90IGF2YWlsYWJsZSc7XG4gICAgICAgIHJldHVybiBgJCR7cHJpY2UudG9Mb2NhbGVTdHJpbmcoKX1gO1xuICAgIH07XG5cbiAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNlcnRpZmljYXRpb24tZGV0YWlsIGJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LWxnIHAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtYi02XCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtvbkJhY2t9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1yLTQgdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIOKGkCBCYWNrXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCBmbGV4LWdyb3dcIj57Y2VydGlmaWNhdGlvbi5uYW1lfTwvaDI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvbkFkZFRvQ29tcGFyaXNvbihjZXJ0aWZpY2F0aW9uKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtNCBweS0yIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bSAke2lzSW5Db21wYXJpc29uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTYwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1ibHVlLTUwIHRleHQtYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS0xMDAnXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtpc0luQ29tcGFyaXNvbiA/ICdBZGRlZCB0byBDb21wYXJlJyA6ICdBZGQgdG8gQ29tcGFyZSd9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC02IG1iLThcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi0yXCI+UHJvdmlkZXI8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPntjZXJ0aWZpY2F0aW9uLnByb3ZpZGVyfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItMlwiPkxldmVsPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHB4LTMgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC1zbSAke2NlcnRpZmljYXRpb24ubGV2ZWwgPT09ICdiZWdpbm5lcicgPyAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNlcnRpZmljYXRpb24ubGV2ZWwgPT09ICdpbnRlcm1lZGlhdGUnID8gJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjZXJ0aWZpY2F0aW9uLmxldmVsID09PSAnYWR2YW5jZWQnID8gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjZXJ0aWZpY2F0aW9uLmxldmVsfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi0yXCI+VHlwZTwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+e2NlcnRpZmljYXRpb24udHlwZX08L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItMlwiPkV4YW0gRGV0YWlsczwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5FeGFtIENvZGU8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS05MDBcIj57Y2VydGlmaWNhdGlvbi5leGFtX2NvZGUgfHwgJ05vdCBhdmFpbGFibGUnfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5QcmljZTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTkwMFwiPntmb3JtYXRQcmljZShjZXJ0aWZpY2F0aW9uLnByaWNlKX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+RHVyYXRpb248L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS05MDBcIj57Zm9ybWF0RHVyYXRpb24oY2VydGlmaWNhdGlvbi5kdXJhdGlvbl9taW51dGVzKX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+UGFzc2luZyBTY29yZTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTkwMFwiPntjZXJ0aWZpY2F0aW9uLnBhc3Npbmdfc2NvcmUgPyBgJHtjZXJ0aWZpY2F0aW9uLnBhc3Npbmdfc2NvcmV9JWAgOiAnTm90IGF2YWlsYWJsZSd9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItNFwiPkRlc2NyaXB0aW9uPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+e2NlcnRpZmljYXRpb24uZGVzY3JpcHRpb24gfHwgJ05vIGRlc2NyaXB0aW9uIGF2YWlsYWJsZS4nfTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7Y2VydGlmaWNhdGlvbi5za2lsbHNfY292ZXJlZCAmJiBjZXJ0aWZpY2F0aW9uLnNraWxsc19jb3ZlcmVkLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTRcIj5Ta2lsbHMgQ292ZXJlZDwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjZXJ0aWZpY2F0aW9uLnNraWxsc19jb3ZlcmVkLm1hcCgoc2tpbGwsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0xIGJnLWdyYXktMTAwIHRleHQtZ3JheS03MDAgcm91bmRlZC1mdWxsIHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3NraWxsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHtjZXJ0aWZpY2F0aW9uLnByZXJlcXVpc2l0ZXMgJiYgY2VydGlmaWNhdGlvbi5wcmVyZXF1aXNpdGVzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTRcIj5QcmVyZXF1aXNpdGVzPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cImxpc3QtZGlzYyBsaXN0LWluc2lkZSBzcGFjZS15LTIgdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2NlcnRpZmljYXRpb24ucHJlcmVxdWlzaXRlcy5tYXAoKHByZXJlcXVpc2l0ZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGkga2V5PXtpbmRleH0+e3ByZXJlcXVpc2l0ZX08L2xpPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7Y2VydGlmaWNhdGlvbi51cmwgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17Y2VydGlmaWNhdGlvbi51cmx9XG4gICAgICAgICAgICAgICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgICAgICAgICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIGJvcmRlciBib3JkZXItdHJhbnNwYXJlbnQgdGV4dC1zbSBmb250LW1lZGl1bSByb3VuZGVkLW1kIHNoYWRvdy1zbSB0ZXh0LXdoaXRlIGJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgVmlzaXQgT2ZmaWNpYWwgUGFnZVxuICAgICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7cmVsYXRlZENlcnRpZmljYXRpb25zLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItNFwiPlJlbGF0ZWQgQ2VydGlmaWNhdGlvbnM8L2gzPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtyZWxhdGVkQ2VydGlmaWNhdGlvbnMubWFwKGNlcnQgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtjZXJ0LmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTQgYm9yZGVyIHJvdW5kZWQtbGcgY3Vyc29yLXBvaW50ZXIgaG92ZXI6YmctZ3JheS01MFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uUmVsYXRlZENlcnRpZmljYXRpb25TZWxlY3QoY2VydCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gbWItMlwiPntjZXJ0Lm5hbWV9PC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj57Y2VydC5wcm92aWRlcn08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BweC0yIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgJHtjZXJ0LmxldmVsID09PSAnYmVnaW5uZXInID8gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjZXJ0LmxldmVsID09PSAnaW50ZXJtZWRpYXRlJyA/ICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2VydC5sZXZlbCA9PT0gJ2FkdmFuY2VkJyA/ICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2VydC5sZXZlbH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICApO1xufSAiXSwibmFtZXMiOlsiQ2VydGlmaWNhdGlvbkRldGFpbCIsImNlcnRpZmljYXRpb24iLCJyZWxhdGVkQ2VydGlmaWNhdGlvbnMiLCJvbkJhY2siLCJvblJlbGF0ZWRDZXJ0aWZpY2F0aW9uU2VsZWN0Iiwib25BZGRUb0NvbXBhcmlzb24iLCJpc0luQ29tcGFyaXNvbiIsImZvcm1hdER1cmF0aW9uIiwibWludXRlcyIsImhvdXJzIiwiTWF0aCIsImZsb29yIiwibWlucyIsImZvcm1hdFByaWNlIiwicHJpY2UiLCJ0b0xvY2FsZVN0cmluZyIsImRpdiIsImNsYXNzTmFtZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJoMiIsIm5hbWUiLCJoMyIsInAiLCJwcm92aWRlciIsInNwYW4iLCJsZXZlbCIsInR5cGUiLCJleGFtX2NvZGUiLCJkdXJhdGlvbl9taW51dGVzIiwicGFzc2luZ19zY29yZSIsImRlc2NyaXB0aW9uIiwic2tpbGxzX2NvdmVyZWQiLCJsZW5ndGgiLCJtYXAiLCJza2lsbCIsImluZGV4IiwicHJlcmVxdWlzaXRlcyIsInVsIiwicHJlcmVxdWlzaXRlIiwibGkiLCJ1cmwiLCJhIiwiaHJlZiIsInRhcmdldCIsInJlbCIsImNlcnQiLCJoNCIsImlkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/certification-explorer/components/CertificationDetail.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/certification-explorer/components/CertificationFilter.tsx":
/*!**********************************************************************************!*\
  !*** ./src/components/certification-explorer/components/CertificationFilter.tsx ***!
  \**********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CertificationFilter: function() { return /* binding */ CertificationFilter; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ CertificationFilter auto */ \nfunction CertificationFilter(param) {\n    let { filters, onFilterChange, providers, isLoading } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"filters-panel w-64 bg-white p-4 rounded-lg shadow space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: \"Filters\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                lineNumber: 20,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: \"search\",\n                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                        children: \"Search\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        id: \"search\",\n                        name: \"search\",\n                        value: filters.search,\n                        onChange: (e)=>onFilterChange(\"search\", e.target.value),\n                        placeholder: \"Search certifications...\",\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                        disabled: isLoading\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                lineNumber: 22,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: \"provider\",\n                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                        children: \"Provider\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        id: \"provider\",\n                        name: \"provider\",\n                        value: filters.provider,\n                        onChange: (e)=>onFilterChange(\"provider\", e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                        disabled: isLoading,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"All Providers\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 21\n                            }, this),\n                            providers.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: provider,\n                                    children: provider\n                                }, provider, false, {\n                                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 25\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                lineNumber: 38,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: \"type\",\n                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                        children: \"Type\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        id: \"type\",\n                        name: \"type\",\n                        value: filters.type,\n                        onChange: (e)=>onFilterChange(\"type\", e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                        disabled: isLoading,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"All Types\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"security\",\n                                children: \"Security\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"network\",\n                                children: \"Network\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"cloud\",\n                                children: \"Cloud\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"software\",\n                                children: \"Software\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                lineNumber: 57,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: \"level\",\n                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                        children: \"Level\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        id: \"level\",\n                        name: \"level\",\n                        value: filters.level,\n                        onChange: (e)=>onFilterChange(\"level\", e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                        disabled: isLoading,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"All Levels\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"beginner\",\n                                children: \"Beginner\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"intermediate\",\n                                children: \"Intermediate\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"advanced\",\n                                children: \"Advanced\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"expert\",\n                                children: \"Expert\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                lineNumber: 77,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"filter-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                        children: \"Price Range\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                name: \"min_price\",\n                                value: filters.min_price,\n                                onChange: (e)=>onFilterChange(\"min_price\", e.target.value),\n                                placeholder: \"Min\",\n                                className: \"w-1/2 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                name: \"max_price\",\n                                value: filters.max_price,\n                                onChange: (e)=>onFilterChange(\"max_price\", e.target.value),\n                                placeholder: \"Max\",\n                                className: \"w-1/2 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n                lineNumber: 97,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationFilter.tsx\",\n        lineNumber: 19,\n        columnNumber: 9\n    }, this);\n}\n_c = CertificationFilter;\nvar _c;\n$RefreshReg$(_c, \"CertificationFilter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/certification-explorer/components/CertificationFilter.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/certification-explorer/components/CertificationList.tsx":
/*!********************************************************************************!*\
  !*** ./src/components/certification-explorer/components/CertificationList.tsx ***!
  \********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CertificationList: function() { return /* binding */ CertificationList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ CertificationList auto */ \nfunction CertificationList(param) {\n    let { certifications, onSelectCertification, totalCertifications, page, pageSize, onPageChange, onPageSizeChange, isLoading = false, onAddToComparison, certificationsToCompare } = param;\n    // Calculate total pages based on total items and page size\n    const totalPages = Math.ceil(totalCertifications / pageSize);\n    // Check if a certification is already in comparison\n    const isInComparison = (certId)=>{\n        return certificationsToCompare.some((cert)=>cert.id === certId);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"loading-placeholder flex items-center justify-center p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"spinner animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"ml-3\",\n                    children: \"Loading certifications...\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n            lineNumber: 40,\n            columnNumber: 13\n        }, this);\n    }\n    if (certifications.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"no-results p-4 bg-yellow-50 text-yellow-800 rounded-md\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"No certifications found matching your criteria.\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                lineNumber: 50,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n            lineNumber: 49,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"certification-list space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"certification-count text-sm text-gray-600\",\n                children: [\n                    \"Showing \",\n                    certifications.length,\n                    \" of \",\n                    totalCertifications,\n                    \" certifications\"\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                lineNumber: 57,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"certification-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                \"data-testid\": \"certifications-grid\",\n                children: certifications.map((cert)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"certification-card bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer\",\n                        onClick: ()=>onSelectCertification(cert),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: cert.name\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"meta-info text-sm text-gray-600 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: cert.provider\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 rounded-full text-xs \".concat(cert.level === \"beginner\" ? \"bg-green-100 text-green-800\" : cert.level === \"intermediate\" ? \"bg-yellow-100 text-yellow-800\" : cert.level === \"advanced\" ? \"bg-red-100 text-red-800\" : \"bg-gray-100 text-gray-800\"),\n                                                children: cert.level\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: cert.type\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 29\n                                    }, this),\n                                    cert.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-900 font-medium\",\n                                        children: [\n                                            \"$\",\n                                            cert.price.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    onAddToComparison(cert);\n                                },\n                                className: \"mt-3 px-3 py-1 rounded-md text-sm font-medium \".concat(isInComparison(cert.id) ? \"bg-gray-100 text-gray-600\" : \"bg-blue-50 text-blue-600 hover:bg-blue-100\"),\n                                children: isInComparison(cert.id) ? \"Added to Compare\" : \"Add to Compare\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, cert.id, true, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 21\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                lineNumber: 61,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pagination flex items-center justify-between mt-6 p-4 bg-white rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onPageChange(page - 1),\n                        disabled: page === 1 || isLoading,\n                        className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: \"Previous\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm text-gray-700\",\n                        children: [\n                            \"Page \",\n                            page,\n                            \" of \",\n                            totalPages\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onPageChange(page + 1),\n                        disabled: page === totalPages || isLoading,\n                        className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: \"Next\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n                lineNumber: 104,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/dev/10Baht/certrats/src/components/certification-explorer/components/CertificationList.tsx\",\n        lineNumber: 56,\n        columnNumber: 9\n    }, this);\n}\n_c = CertificationList;\nvar _c;\n$RefreshReg$(_c, \"CertificationList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/certification-explorer/components/CertificationList.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/certification-explorer/hooks/useCertificationComparison.ts":
/*!***********************************************************************************!*\
  !*** ./src/components/certification-explorer/hooks/useCertificationComparison.ts ***!
  \***********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCertificationComparison: function() { return /* binding */ useCertificationComparison; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useCertificationComparison() {\n    const [certificationsToCompare, setCertificationsToCompare] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [comparisonData, setComparisonData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isComparing, setIsComparing] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const addCertificationToCompare = (certification)=>{\n        if (certificationsToCompare.some((cert)=>cert.id === certification.id)) {\n            removeCertificationFromCompare(certification);\n        } else if (certificationsToCompare.length < 3) {\n            setCertificationsToCompare((prev)=>[\n                    ...prev,\n                    certification\n                ]);\n        } else {\n            setError(\"You can compare up to 3 certifications at a time.\");\n        }\n    };\n    const removeCertificationFromCompare = (certification)=>{\n        setCertificationsToCompare((prev)=>prev.filter((cert)=>cert.id !== certification.id));\n        if (isComparing) {\n            cancelComparison();\n        }\n    };\n    const clearComparison = ()=>{\n        setCertificationsToCompare([]);\n        setComparisonData(null);\n        setIsComparing(false);\n        setError(null);\n    };\n    const startComparison = async ()=>{\n        if (certificationsToCompare.length < 2) {\n            setError(\"Please select at least 2 certifications to compare.\");\n            return;\n        }\n        try {\n            setLoading(true);\n            setError(null);\n            // TODO: Replace with actual API call\n            // For now, generate mock comparison data\n            const differences = {\n                price: {},\n                duration_minutes: {},\n                passing_score: {},\n                level: {},\n                skills_covered: {}\n            };\n            const similarities = {};\n            // Compare all fields\n            certificationsToCompare.forEach((cert)=>{\n                Object.entries(cert).forEach((param)=>{\n                    let [key, value] = param;\n                    if (key === \"id\" || key === \"created_at\" || key === \"updated_at\") return;\n                    const allValues = certificationsToCompare.map((c)=>c[key]);\n                    const allSame = allValues.every((v)=>JSON.stringify(v) === JSON.stringify(allValues[0]));\n                    if (allSame) {\n                        similarities[key] = value;\n                    } else {\n                        if (!differences[key]) differences[key] = {};\n                        differences[key][cert.id] = value;\n                    }\n                });\n            });\n            setComparisonData({\n                certifications: certificationsToCompare,\n                differences,\n                similarities\n            });\n            setIsComparing(true);\n        } catch (err) {\n            console.error(\"Error comparing certifications:\", err);\n            setError(\"Failed to compare certifications. Please try again later.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const cancelComparison = ()=>{\n        setIsComparing(false);\n        setComparisonData(null);\n        setError(null);\n    };\n    return {\n        certificationsToCompare,\n        comparisonData,\n        loading,\n        error,\n        isComparing,\n        addCertificationToCompare,\n        removeCertificationFromCompare,\n        clearComparison,\n        startComparison,\n        cancelComparison\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NlcnRpZmljYXRpb24tZXhwbG9yZXIvaG9va3MvdXNlQ2VydGlmaWNhdGlvbkNvbXBhcmlzb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWlDO0FBRzFCLFNBQVNDO0lBQ2QsTUFBTSxDQUFDQyx5QkFBeUJDLDJCQUEyQixHQUFHSCwrQ0FBUUEsQ0FBbUIsRUFBRTtJQUMzRixNQUFNLENBQUNJLGdCQUFnQkMsa0JBQWtCLEdBQUdMLCtDQUFRQSxDQUF3QjtJQUM1RSxNQUFNLENBQUNNLFNBQVNDLFdBQVcsR0FBR1AsK0NBQVFBLENBQVU7SUFDaEQsTUFBTSxDQUFDUSxPQUFPQyxTQUFTLEdBQUdULCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNVLGFBQWFDLGVBQWUsR0FBR1gsK0NBQVFBLENBQVU7SUFFeEQsTUFBTVksNEJBQTRCLENBQUNDO1FBQ2pDLElBQUlYLHdCQUF3QlksSUFBSSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLQyxFQUFFLEtBQUtILGNBQWNHLEVBQUUsR0FBRztZQUN0RUMsK0JBQStCSjtRQUNqQyxPQUFPLElBQUlYLHdCQUF3QmdCLE1BQU0sR0FBRyxHQUFHO1lBQzdDZiwyQkFBMkJnQixDQUFBQSxPQUFRO3VCQUFJQTtvQkFBTU47aUJBQWM7UUFDN0QsT0FBTztZQUNMSixTQUFTO1FBQ1g7SUFDRjtJQUVBLE1BQU1RLGlDQUFpQyxDQUFDSjtRQUN0Q1YsMkJBQTJCZ0IsQ0FBQUEsT0FDekJBLEtBQUtDLE1BQU0sQ0FBQ0wsQ0FBQUEsT0FBUUEsS0FBS0MsRUFBRSxLQUFLSCxjQUFjRyxFQUFFO1FBRWxELElBQUlOLGFBQWE7WUFDZlc7UUFDRjtJQUNGO0lBRUEsTUFBTUMsa0JBQWtCO1FBQ3RCbkIsMkJBQTJCLEVBQUU7UUFDN0JFLGtCQUFrQjtRQUNsQk0sZUFBZTtRQUNmRixTQUFTO0lBQ1g7SUFFQSxNQUFNYyxrQkFBa0I7UUFDdEIsSUFBSXJCLHdCQUF3QmdCLE1BQU0sR0FBRyxHQUFHO1lBQ3RDVCxTQUFTO1lBQ1Q7UUFDRjtRQUVBLElBQUk7WUFDRkYsV0FBVztZQUNYRSxTQUFTO1lBRVQscUNBQXFDO1lBQ3JDLHlDQUF5QztZQUN6QyxNQUFNZSxjQUE0RDtnQkFDaEVDLE9BQU8sQ0FBQztnQkFDUkMsa0JBQWtCLENBQUM7Z0JBQ25CQyxlQUFlLENBQUM7Z0JBQ2hCQyxPQUFPLENBQUM7Z0JBQ1JDLGdCQUFnQixDQUFDO1lBQ25CO1lBRUEsTUFBTUMsZUFBdUMsQ0FBQztZQUU5QyxxQkFBcUI7WUFDckI1Qix3QkFBd0I2QixPQUFPLENBQUNoQixDQUFBQTtnQkFDOUJpQixPQUFPQyxPQUFPLENBQUNsQixNQUFNZ0IsT0FBTyxDQUFDO3dCQUFDLENBQUNHLEtBQUtDLE1BQU07b0JBQ3hDLElBQUlELFFBQVEsUUFBUUEsUUFBUSxnQkFBZ0JBLFFBQVEsY0FBYztvQkFFbEUsTUFBTUUsWUFBWWxDLHdCQUF3Qm1DLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBS0EsQ0FBQyxDQUFDSixJQUE0QjtvQkFDakYsTUFBTUssVUFBVUgsVUFBVUksS0FBSyxDQUFDQyxDQUFBQSxJQUFLQyxLQUFLQyxTQUFTLENBQUNGLE9BQU9DLEtBQUtDLFNBQVMsQ0FBQ1AsU0FBUyxDQUFDLEVBQUU7b0JBRXRGLElBQUlHLFNBQVM7d0JBQ1hULFlBQVksQ0FBQ0ksSUFBSSxHQUFHQztvQkFDdEIsT0FBTzt3QkFDTCxJQUFJLENBQUNYLFdBQVcsQ0FBQ1UsSUFBSSxFQUFFVixXQUFXLENBQUNVLElBQUksR0FBRyxDQUFDO3dCQUMzQ1YsV0FBVyxDQUFDVSxJQUFJLENBQUNuQixLQUFLQyxFQUFFLENBQUMsR0FBR21CO29CQUM5QjtnQkFDRjtZQUNGO1lBRUE5QixrQkFBa0I7Z0JBQ2hCdUMsZ0JBQWdCMUM7Z0JBQ2hCc0I7Z0JBQ0FNO1lBQ0Y7WUFFQW5CLGVBQWU7UUFDakIsRUFBRSxPQUFPa0MsS0FBSztZQUNaQyxRQUFRdEMsS0FBSyxDQUFDLG1DQUFtQ3FDO1lBQ2pEcEMsU0FBUztRQUNYLFNBQVU7WUFDUkYsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNYyxtQkFBbUI7UUFDdkJWLGVBQWU7UUFDZk4sa0JBQWtCO1FBQ2xCSSxTQUFTO0lBQ1g7SUFFQSxPQUFPO1FBQ0xQO1FBQ0FFO1FBQ0FFO1FBQ0FFO1FBQ0FFO1FBQ0FFO1FBQ0FLO1FBQ0FLO1FBQ0FDO1FBQ0FGO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9jZXJ0aWZpY2F0aW9uLWV4cGxvcmVyL2hvb2tzL3VzZUNlcnRpZmljYXRpb25Db21wYXJpc29uLnRzPzkyMjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBDb21wYXJpc29uRGF0YSwgSUNlcnRpZmljYXRpb24gfSBmcm9tICcuLi90eXBlcy9jZXJ0aWZpY2F0aW9uLnR5cGVzJztcblxuZXhwb3J0IGZ1bmN0aW9uIHVzZUNlcnRpZmljYXRpb25Db21wYXJpc29uKCkge1xuICBjb25zdCBbY2VydGlmaWNhdGlvbnNUb0NvbXBhcmUsIHNldENlcnRpZmljYXRpb25zVG9Db21wYXJlXSA9IHVzZVN0YXRlPElDZXJ0aWZpY2F0aW9uW10+KFtdKTtcbiAgY29uc3QgW2NvbXBhcmlzb25EYXRhLCBzZXRDb21wYXJpc29uRGF0YV0gPSB1c2VTdGF0ZTxDb21wYXJpc29uRGF0YSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZTxib29sZWFuPihmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtpc0NvbXBhcmluZywgc2V0SXNDb21wYXJpbmddID0gdXNlU3RhdGU8Ym9vbGVhbj4oZmFsc2UpO1xuXG4gIGNvbnN0IGFkZENlcnRpZmljYXRpb25Ub0NvbXBhcmUgPSAoY2VydGlmaWNhdGlvbjogSUNlcnRpZmljYXRpb24pID0+IHtcbiAgICBpZiAoY2VydGlmaWNhdGlvbnNUb0NvbXBhcmUuc29tZShjZXJ0ID0+IGNlcnQuaWQgPT09IGNlcnRpZmljYXRpb24uaWQpKSB7XG4gICAgICByZW1vdmVDZXJ0aWZpY2F0aW9uRnJvbUNvbXBhcmUoY2VydGlmaWNhdGlvbik7XG4gICAgfSBlbHNlIGlmIChjZXJ0aWZpY2F0aW9uc1RvQ29tcGFyZS5sZW5ndGggPCAzKSB7XG4gICAgICBzZXRDZXJ0aWZpY2F0aW9uc1RvQ29tcGFyZShwcmV2ID0+IFsuLi5wcmV2LCBjZXJ0aWZpY2F0aW9uXSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHNldEVycm9yKCdZb3UgY2FuIGNvbXBhcmUgdXAgdG8gMyBjZXJ0aWZpY2F0aW9ucyBhdCBhIHRpbWUuJyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHJlbW92ZUNlcnRpZmljYXRpb25Gcm9tQ29tcGFyZSA9IChjZXJ0aWZpY2F0aW9uOiBJQ2VydGlmaWNhdGlvbikgPT4ge1xuICAgIHNldENlcnRpZmljYXRpb25zVG9Db21wYXJlKHByZXYgPT5cbiAgICAgIHByZXYuZmlsdGVyKGNlcnQgPT4gY2VydC5pZCAhPT0gY2VydGlmaWNhdGlvbi5pZClcbiAgICApO1xuICAgIGlmIChpc0NvbXBhcmluZykge1xuICAgICAgY2FuY2VsQ29tcGFyaXNvbigpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBjbGVhckNvbXBhcmlzb24gPSAoKSA9PiB7XG4gICAgc2V0Q2VydGlmaWNhdGlvbnNUb0NvbXBhcmUoW10pO1xuICAgIHNldENvbXBhcmlzb25EYXRhKG51bGwpO1xuICAgIHNldElzQ29tcGFyaW5nKGZhbHNlKTtcbiAgICBzZXRFcnJvcihudWxsKTtcbiAgfTtcblxuICBjb25zdCBzdGFydENvbXBhcmlzb24gPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKGNlcnRpZmljYXRpb25zVG9Db21wYXJlLmxlbmd0aCA8IDIpIHtcbiAgICAgIHNldEVycm9yKCdQbGVhc2Ugc2VsZWN0IGF0IGxlYXN0IDIgY2VydGlmaWNhdGlvbnMgdG8gY29tcGFyZS4nKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgICAvLyBUT0RPOiBSZXBsYWNlIHdpdGggYWN0dWFsIEFQSSBjYWxsXG4gICAgICAvLyBGb3Igbm93LCBnZW5lcmF0ZSBtb2NrIGNvbXBhcmlzb24gZGF0YVxuICAgICAgY29uc3QgZGlmZmVyZW5jZXM6IHsgW2tleTogc3RyaW5nXTogeyBbY2VydElkOiBudW1iZXJdOiBhbnkgfSB9ID0ge1xuICAgICAgICBwcmljZToge30sXG4gICAgICAgIGR1cmF0aW9uX21pbnV0ZXM6IHt9LFxuICAgICAgICBwYXNzaW5nX3Njb3JlOiB7fSxcbiAgICAgICAgbGV2ZWw6IHt9LFxuICAgICAgICBza2lsbHNfY292ZXJlZDoge31cbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IHNpbWlsYXJpdGllczogeyBba2V5OiBzdHJpbmddOiBhbnkgfSA9IHt9O1xuXG4gICAgICAvLyBDb21wYXJlIGFsbCBmaWVsZHNcbiAgICAgIGNlcnRpZmljYXRpb25zVG9Db21wYXJlLmZvckVhY2goY2VydCA9PiB7XG4gICAgICAgIE9iamVjdC5lbnRyaWVzKGNlcnQpLmZvckVhY2goKFtrZXksIHZhbHVlXSkgPT4ge1xuICAgICAgICAgIGlmIChrZXkgPT09ICdpZCcgfHwga2V5ID09PSAnY3JlYXRlZF9hdCcgfHwga2V5ID09PSAndXBkYXRlZF9hdCcpIHJldHVybjtcblxuICAgICAgICAgIGNvbnN0IGFsbFZhbHVlcyA9IGNlcnRpZmljYXRpb25zVG9Db21wYXJlLm1hcChjID0+IGNba2V5IGFzIGtleW9mIElDZXJ0aWZpY2F0aW9uXSk7XG4gICAgICAgICAgY29uc3QgYWxsU2FtZSA9IGFsbFZhbHVlcy5ldmVyeSh2ID0+IEpTT04uc3RyaW5naWZ5KHYpID09PSBKU09OLnN0cmluZ2lmeShhbGxWYWx1ZXNbMF0pKTtcblxuICAgICAgICAgIGlmIChhbGxTYW1lKSB7XG4gICAgICAgICAgICBzaW1pbGFyaXRpZXNba2V5XSA9IHZhbHVlO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBpZiAoIWRpZmZlcmVuY2VzW2tleV0pIGRpZmZlcmVuY2VzW2tleV0gPSB7fTtcbiAgICAgICAgICAgIGRpZmZlcmVuY2VzW2tleV1bY2VydC5pZF0gPSB2YWx1ZTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgfSk7XG5cbiAgICAgIHNldENvbXBhcmlzb25EYXRhKHtcbiAgICAgICAgY2VydGlmaWNhdGlvbnM6IGNlcnRpZmljYXRpb25zVG9Db21wYXJlLFxuICAgICAgICBkaWZmZXJlbmNlcyxcbiAgICAgICAgc2ltaWxhcml0aWVzXG4gICAgICB9KTtcblxuICAgICAgc2V0SXNDb21wYXJpbmcodHJ1ZSk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjb21wYXJpbmcgY2VydGlmaWNhdGlvbnM6JywgZXJyKTtcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gY29tcGFyZSBjZXJ0aWZpY2F0aW9ucy4gUGxlYXNlIHRyeSBhZ2FpbiBsYXRlci4nKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGNhbmNlbENvbXBhcmlzb24gPSAoKSA9PiB7XG4gICAgc2V0SXNDb21wYXJpbmcoZmFsc2UpO1xuICAgIHNldENvbXBhcmlzb25EYXRhKG51bGwpO1xuICAgIHNldEVycm9yKG51bGwpO1xuICB9O1xuXG4gIHJldHVybiB7XG4gICAgY2VydGlmaWNhdGlvbnNUb0NvbXBhcmUsXG4gICAgY29tcGFyaXNvbkRhdGEsXG4gICAgbG9hZGluZyxcbiAgICBlcnJvcixcbiAgICBpc0NvbXBhcmluZyxcbiAgICBhZGRDZXJ0aWZpY2F0aW9uVG9Db21wYXJlLFxuICAgIHJlbW92ZUNlcnRpZmljYXRpb25Gcm9tQ29tcGFyZSxcbiAgICBjbGVhckNvbXBhcmlzb24sXG4gICAgc3RhcnRDb21wYXJpc29uLFxuICAgIGNhbmNlbENvbXBhcmlzb25cbiAgfTtcbn0gIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlQ2VydGlmaWNhdGlvbkNvbXBhcmlzb24iLCJjZXJ0aWZpY2F0aW9uc1RvQ29tcGFyZSIsInNldENlcnRpZmljYXRpb25zVG9Db21wYXJlIiwiY29tcGFyaXNvbkRhdGEiLCJzZXRDb21wYXJpc29uRGF0YSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImlzQ29tcGFyaW5nIiwic2V0SXNDb21wYXJpbmciLCJhZGRDZXJ0aWZpY2F0aW9uVG9Db21wYXJlIiwiY2VydGlmaWNhdGlvbiIsInNvbWUiLCJjZXJ0IiwiaWQiLCJyZW1vdmVDZXJ0aWZpY2F0aW9uRnJvbUNvbXBhcmUiLCJsZW5ndGgiLCJwcmV2IiwiZmlsdGVyIiwiY2FuY2VsQ29tcGFyaXNvbiIsImNsZWFyQ29tcGFyaXNvbiIsInN0YXJ0Q29tcGFyaXNvbiIsImRpZmZlcmVuY2VzIiwicHJpY2UiLCJkdXJhdGlvbl9taW51dGVzIiwicGFzc2luZ19zY29yZSIsImxldmVsIiwic2tpbGxzX2NvdmVyZWQiLCJzaW1pbGFyaXRpZXMiLCJmb3JFYWNoIiwiT2JqZWN0IiwiZW50cmllcyIsImtleSIsInZhbHVlIiwiYWxsVmFsdWVzIiwibWFwIiwiYyIsImFsbFNhbWUiLCJldmVyeSIsInYiLCJKU09OIiwic3RyaW5naWZ5IiwiY2VydGlmaWNhdGlvbnMiLCJlcnIiLCJjb25zb2xlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/certification-explorer/hooks/useCertificationComparison.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/certification-explorer/hooks/useCertifications.ts":
/*!**************************************************************************!*\
  !*** ./src/components/certification-explorer/hooks/useCertifications.ts ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCertifications: function() { return /* binding */ useCertifications; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useCertifications() {\n    const [certifications, setCertifications] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(10);\n    const [totalCertifications, setTotalCertifications] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        provider: \"\",\n        type: \"\",\n        level: \"\",\n        search: \"\",\n        min_price: \"\",\n        max_price: \"\"\n    });\n    const [providers, setProviders] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [selectedCertification, setSelectedCertification] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [relatedCertifications, setRelatedCertifications] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const fetchCertifications = async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // TODO: Replace with actual API call\n                // For now, using mock data\n                const mockCertifications = [\n                    {\n                        id: 1,\n                        name: \"Certified Ethical Hacker (CEH)\",\n                        provider: \"EC-Council\",\n                        description: \"The Certified Ethical Hacker certification focuses on ethical hacking methodologies.\",\n                        type: \"security\",\n                        level: \"intermediate\",\n                        exam_code: \"312-50\",\n                        price: 1199,\n                        duration_minutes: 240,\n                        passing_score: 70,\n                        skills_covered: [\n                            \"penetration testing\",\n                            \"vulnerability assessment\",\n                            \"network security\"\n                        ],\n                        url: \"https://www.eccouncil.org/programs/certified-ethical-hacker-ceh/\",\n                        created_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString()\n                    },\n                    {\n                        id: 2,\n                        name: \"CompTIA Security+\",\n                        provider: \"CompTIA\",\n                        description: \"CompTIA Security+ is a global certification that validates the baseline skills necessary to perform core security functions.\",\n                        type: \"security\",\n                        level: \"beginner\",\n                        exam_code: \"SY0-601\",\n                        price: 349,\n                        duration_minutes: 90,\n                        passing_score: 750,\n                        skills_covered: [\n                            \"network security\",\n                            \"compliance\",\n                            \"operational security\"\n                        ],\n                        url: \"https://www.comptia.org/certifications/security\",\n                        created_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString()\n                    }\n                ];\n                setCertifications(mockCertifications);\n                setTotalCertifications(mockCertifications.length);\n                setProviders([\n                    \"EC-Council\",\n                    \"CompTIA\",\n                    \"ISC2\",\n                    \"ISACA\"\n                ]);\n            } catch (err) {\n                console.error(\"Error fetching certifications:\", err);\n                setError(\"Failed to load certifications. Please try again later.\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchCertifications();\n    }, [\n        page,\n        pageSize,\n        filters\n    ]);\n    const handleFilterChange = (name, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        setPage(1); // Reset to first page when filters change\n    };\n    const handleCertificationSelect = (certification)=>{\n        setSelectedCertification(certification);\n        // TODO: Replace with actual API call for related certifications\n        setRelatedCertifications([]);\n    };\n    const handleBackToList = ()=>{\n        setSelectedCertification(null);\n        setRelatedCertifications([]);\n    };\n    const handlePageChange = (newPage)=>{\n        setPage(newPage);\n    };\n    const handlePageSizeChange = (newPageSize)=>{\n        setPageSize(newPageSize);\n        setPage(1); // Reset to first page when page size changes\n    };\n    return {\n        certifications,\n        loading,\n        error,\n        page,\n        pageSize,\n        totalCertifications,\n        filters,\n        providers,\n        selectedCertification,\n        relatedCertifications,\n        handleFilterChange,\n        handleCertificationSelect,\n        handleBackToList,\n        handlePageChange,\n        handlePageSizeChange\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/certification-explorer/hooks/useCertifications.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider'); // TODO: Delete with enableRenderableContext\n\nvar REACT_CONSUMER_TYPE = Symbol.for('react.consumer');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\nvar enableRenderableContext = false;\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false;\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nvar REACT_CLIENT_REFERENCE$2 = Symbol.for('react.client.reference'); // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  if (typeof type === 'function') {\n    if (type.$$typeof === REACT_CLIENT_REFERENCE$2) {\n      // TODO: Create a convention for naming client references with debug info.\n      return null;\n    }\n\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    {\n      if (typeof type.tag === 'number') {\n        error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n      }\n    }\n\n    switch (type.$$typeof) {\n      case REACT_PROVIDER_TYPE:\n        {\n          var provider = type;\n          return getContextName(provider._context) + '.Provider';\n        }\n\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n\n        {\n          return getContextName(context) + '.Consumer';\n        }\n\n      case REACT_CONSUMER_TYPE:\n        {\n          return null;\n        }\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar assign = Object.assign;\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || enableRenderableContext  || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    {\n      var warnAboutAccessingRef = function () {\n        if (!specialPropRefWarningShown) {\n          specialPropRefWarningShown = true;\n\n          error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n        }\n      };\n\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, _ref, self, source, owner, props) {\n  var ref;\n\n  {\n    ref = _ref;\n  }\n\n  var element;\n\n  {\n    // In prod, `ref` is a regular property. It will be removed in a\n    // future release.\n    element = {\n      // This tag allows us to uniquely identify this as a React Element\n      $$typeof: REACT_ELEMENT_TYPE,\n      // Built-in properties that belong on the element\n      type: type,\n      key: key,\n      ref: ref,\n      props: props,\n      // Record the component responsible for creating this element.\n      _owner: owner\n    };\n  }\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // debugInfo contains Server Component debug information.\n\n    Object.defineProperty(element, '_debugInfo', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: null\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\nvar didWarnAboutKeySpread = {};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, isStaticChildren, source, self) {\n  {\n    if (!isValidElementType(type)) {\n      // This is an invalid element type.\n      //\n      // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    } else {\n      // This is a valid element type.\n      // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing\n      // errors. We don't want exception behavior to differ between dev and\n      // prod. (Rendering will throw with a helpful message and as soon as the\n      // type is fixed, the key warnings will appear.)\n      var children = config.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    } // Warn about key spread regardless of whether the type is valid.\n\n\n    if (hasOwnProperty.call(config, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(config).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      {\n        ref = config.ref;\n      }\n\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && // Skip over reserved prop names\n      propName !== 'key' && (propName !== 'ref')) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    var element = ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    }\n\n    return element;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nvar ownerHasKeyUseWarning = {};\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = getComponentNameFromType(parentType);\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  // TODO: Move this to render phase instead of at element creation.\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar jsxDEV = jsxDEV$1 ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/YzM3OCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fcvr%2Fdev%2F10Baht%2Fcertrats%2Fsrc%2Fcomponents%2Fcertification-explorer%2FCertificationExplorer.tsx%22%2C%22ids%22%3A%5B%22CertificationExplorer%22%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);