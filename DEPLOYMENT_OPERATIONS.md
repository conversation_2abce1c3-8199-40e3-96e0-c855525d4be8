# CertRats Deployment & Operations Guide

## 🚀 **PRODUCTION DEPLOYMENT CHECKLIST**

### **Pre-Deployment Requirements**
- [ ] **Infrastructure**: Kubernetes cluster (v1.28+) with 3+ nodes
- [ ] **Database**: PostgreSQL 15+ with connection pooling
- [ ] **Cache**: Redis 7+ cluster for session management
- [ ] **Monitoring**: Prometheus + Grafana + J<PERSON>ger stack
- [ ] **Security**: SSL certificates and WAF configuration
- [ ] **DNS**: Domain configuration with CDN
- [ ] **Secrets**: Environment variables and API keys secured

### **Environment Configuration**

#### **Production Environment Variables**
```bash
# Application Configuration
ENVIRONMENT=production
DEBUG=false
API_VERSION=v1
FRONTEND_URL=https://certrats.com
BACKEND_URL=https://api.certrats.com

# Database Configuration
DATABASE_URL=********************************************/certrats_prod
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30

# Redis Configuration
REDIS_URL=redis://redis-cluster:6379/0
REDIS_PASSWORD=your-secure-redis-password
REDIS_POOL_SIZE=10

# AI Integration
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_MODEL=claude-3-sonnet-20240229
AI_TIMEOUT=30
AI_MAX_RETRIES=3

# Security Configuration
JWT_SECRET_KEY=your-super-secure-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
BCRYPT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
RATE_LIMIT_BURST=20

# Monitoring & Logging
LOG_LEVEL=INFO
SENTRY_DSN=your-sentry-dsn
PROMETHEUS_METRICS=true
JAEGER_ENDPOINT=http://jaeger-collector:14268/api/traces

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password
```

### **Kubernetes Deployment**

#### **1. Namespace and Secrets**
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: certrats
  labels:
    name: certrats
    environment: production

---
# k8s/secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: certrats-secrets
  namespace: certrats
type: Opaque
data:
  database-url: <base64-encoded-database-url>
  jwt-secret: <base64-encoded-jwt-secret>
  anthropic-api-key: <base64-encoded-anthropic-key>
  redis-password: <base64-encoded-redis-password>
```

#### **2. Backend Deployment**
```yaml
# k8s/backend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: certrats
  labels:
    app: backend
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
        version: v1
    spec:
      containers:
      - name: backend
        image: certrats/backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: certrats-secrets
              key: database-url
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: certrats-secrets
              key: jwt-secret
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: certrats-secrets
              key: anthropic-api-key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
```

#### **3. Frontend Deployment**
```yaml
# k8s/frontend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
  namespace: certrats
spec:
  replicas: 3
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
      - name: frontend
        image: certrats/frontend:latest
        ports:
        - containerPort: 3000
        env:
        - name: NEXT_PUBLIC_API_URL
          value: "https://api.certrats.com"
        - name: NODE_ENV
          value: "production"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### **4. Horizontal Pod Autoscaler**
```yaml
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-hpa
  namespace: certrats
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## 📊 **MONITORING & OBSERVABILITY**

### **Prometheus Monitoring Configuration**
```yaml
# k8s/prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: certrats-monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
      - "alert_rules.yml"
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
    
    scrape_configs:
      - job_name: 'certrats-backend'
        static_configs:
          - targets: ['backend-service:8000']
        metrics_path: '/metrics'
        scrape_interval: 10s
      
      - job_name: 'certrats-frontend'
        static_configs:
          - targets: ['frontend-service:3000']
        metrics_path: '/metrics'
        scrape_interval: 10s
      
      - job_name: 'postgres'
        static_configs:
          - targets: ['postgres-exporter:9187']
      
      - job_name: 'redis'
        static_configs:
          - targets: ['redis-exporter:9121']
```

### **Grafana Dashboard Configuration**
```json
{
  "dashboard": {
    "title": "CertRats Production Dashboard",
    "panels": [
      {
        "title": "API Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "Requests/sec"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "singlestat",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m])",
            "legendFormat": "Error Rate"
          }
        ]
      }
    ]
  }
}
```

### **Alert Rules**
```yaml
# k8s/alert-rules.yaml
groups:
- name: certrats.rules
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.01
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} requests/sec"
  
  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High response time detected"
      description: "95th percentile response time is {{ $value }}s"
  
  - alert: DatabaseConnectionFailure
    expr: up{job="postgres"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Database connection failure"
      description: "PostgreSQL database is not responding"
```

## 🔄 **CI/CD PIPELINE**

### **GitHub Actions Deployment Workflow**
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: |
          make test
          make test-integration
          make test-e2e

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Login to Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Build and push backend
        uses: docker/build-push-action@v4
        with:
          context: .
          file: docker/backend/Dockerfile
          push: true
          tags: ghcr.io/certrats/backend:${{ github.sha }}
      
      - name: Build and push frontend
        uses: docker/build-push-action@v4
        with:
          context: .
          file: docker/frontend/Dockerfile
          push: true
          tags: ghcr.io/certrats/frontend:${{ github.sha }}

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    environment: production
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure kubectl
        uses: azure/k8s-set-context@v1
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG }}
      
      - name: Deploy to Kubernetes
        run: |
          # Update image tags
          sed -i 's|image: certrats/backend:latest|image: ghcr.io/certrats/backend:${{ github.sha }}|' k8s/backend-deployment.yaml
          sed -i 's|image: certrats/frontend:latest|image: ghcr.io/certrats/frontend:${{ github.sha }}|' k8s/frontend-deployment.yaml
          
          # Apply configurations
          kubectl apply -f k8s/
          
          # Wait for rollout
          kubectl rollout status deployment/backend -n certrats
          kubectl rollout status deployment/frontend -n certrats
      
      - name: Run smoke tests
        run: |
          make smoke-test-production
```

## 🔧 **OPERATIONAL PROCEDURES**

### **Daily Operations Checklist**
- [ ] **Health Checks**: Verify all services are running
- [ ] **Performance Metrics**: Check response times and error rates
- [ ] **Resource Usage**: Monitor CPU, memory, and disk usage
- [ ] **Database Health**: Check connection pool and query performance
- [ ] **AI Service**: Verify Claude API integration is working
- [ ] **Security Alerts**: Review any security notifications
- [ ] **Backup Status**: Confirm automated backups completed

### **Weekly Operations Tasks**
- [ ] **Security Updates**: Apply security patches to infrastructure
- [ ] **Performance Review**: Analyze weekly performance trends
- [ ] **Cost Analysis**: Review infrastructure costs and optimization
- [ ] **Capacity Planning**: Assess resource needs for upcoming week
- [ ] **Backup Testing**: Verify backup restoration procedures
- [ ] **Documentation Updates**: Update operational procedures

### **Monthly Operations Tasks**
- [ ] **Security Audit**: Comprehensive security review
- [ ] **Performance Optimization**: Identify and implement optimizations
- [ ] **Disaster Recovery Test**: Full DR procedure testing
- [ ] **Cost Optimization**: Review and optimize infrastructure costs
- [ ] **Capacity Planning**: Long-term resource planning
- [ ] **Team Training**: Update team on new procedures

## 🚨 **INCIDENT RESPONSE**

### **Severity Levels**
- **Critical (P0)**: Service completely down, data loss
- **High (P1)**: Major functionality impaired, significant user impact
- **Medium (P2)**: Minor functionality issues, limited user impact
- **Low (P3)**: Cosmetic issues, no user impact

### **Response Procedures**
1. **Detection**: Automated alerts or user reports
2. **Assessment**: Determine severity and impact
3. **Response**: Implement immediate fixes or workarounds
4. **Communication**: Update stakeholders and users
5. **Resolution**: Implement permanent fix
6. **Post-mortem**: Document lessons learned

### **Emergency Contacts**
- **On-Call Engineer**: +1-XXX-XXX-XXXX
- **DevOps Lead**: <EMAIL>
- **Security Team**: <EMAIL>
- **Management**: <EMAIL>

## 📈 **SCALING STRATEGIES**

### **Horizontal Scaling**
- **Auto-scaling**: HPA configured for 3-10 replicas
- **Load Balancing**: Nginx with round-robin distribution
- **Database**: Read replicas for query scaling
- **Cache**: Redis cluster for distributed caching

### **Vertical Scaling**
- **Resource Limits**: Configured for burst capacity
- **Database**: Connection pooling optimization
- **AI Service**: Request batching and caching
- **CDN**: Static asset optimization

### **Geographic Scaling**
- **Multi-Region**: Deploy to multiple AWS regions
- **CDN**: CloudFront for global content delivery
- **Database**: Cross-region replication
- **Monitoring**: Regional monitoring dashboards

This comprehensive deployment and operations guide ensures CertRats runs reliably in production with proper monitoring, scaling, and incident response procedures.
