# Default values for certrats
# This is a YAML-formatted file.

# Global configuration
global:
  imageRegistry: "ghcr.io"
  imagePullSecrets:
    - name: ghcr-secret
  storageClass: "fast-ssd"

# Application configuration
app:
  name: certrats
  version: "1.0.0"
  environment: production

# Backend configuration
backend:
  enabled: true
  image:
    repository: ghcr.io/your-org/certrats-backend
    tag: "latest"
    pullPolicy: IfNotPresent
  
  replicaCount: 3
  
  service:
    type: ClusterIP
    port: 8000
    targetPort: 8000
  
  resources:
    requests:
      memory: "256Mi"
      cpu: "250m"
    limits:
      memory: "1Gi"
      cpu: "1000m"
  
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  env:
    ENVIRONMENT: production
    LOG_LEVEL: INFO
    API_HOST: "0.0.0.0"
    API_PORT: "8000"
  
  secrets:
    DATABASE_URL: ""
    JWT_SECRET_KEY: ""
    ANTHROPIC_API_KEY: ""
    REDIS_PASSWORD: ""

# Frontend configuration
frontend:
  enabled: true
  image:
    repository: ghcr.io/your-org/certrats-frontend
    tag: "latest"
    pullPolicy: IfNotPresent
  
  replicaCount: 3
  
  service:
    type: ClusterIP
    port: 3000
    targetPort: 3000
  
  resources:
    requests:
      memory: "128Mi"
      cpu: "100m"
    limits:
      memory: "512Mi"
      cpu: "500m"
  
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 8
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  env:
    NODE_ENV: production
    NEXT_PUBLIC_API_URL: "https://api.certrats.com"
    NEXT_PUBLIC_APP_NAME: "CertRats"
    NEXT_PUBLIC_APP_VERSION: "1.0.0"

# Nginx configuration
nginx:
  enabled: true
  image:
    repository: nginx
    tag: "alpine"
    pullPolicy: IfNotPresent
  
  replicaCount: 2
  
  service:
    type: LoadBalancer
    ports:
      http: 80
      https: 443
  
  resources:
    requests:
      memory: "64Mi"
      cpu: "50m"
    limits:
      memory: "256Mi"
      cpu: "250m"

# PostgreSQL configuration
postgresql:
  enabled: true
  auth:
    postgresPassword: ""
    username: "certrats_user"
    password: ""
    database: "certrats"
  
  primary:
    persistence:
      enabled: true
      size: 20Gi
      storageClass: "fast-ssd"
    
    resources:
      requests:
        memory: "512Mi"
        cpu: "250m"
      limits:
        memory: "2Gi"
        cpu: "1000m"
  
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true

# Redis configuration
redis:
  enabled: true
  auth:
    enabled: true
    password: ""
  
  master:
    persistence:
      enabled: true
      size: 8Gi
      storageClass: "fast-ssd"
    
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "512Mi"
        cpu: "500m"
  
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true

# Ingress configuration
ingress:
  enabled: true
  className: "nginx"
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
  
  hosts:
    - host: certrats.com
      paths:
        - path: /
          pathType: Prefix
          service: nginx
    - host: www.certrats.com
      paths:
        - path: /
          pathType: Prefix
          service: nginx
    - host: api.certrats.com
      paths:
        - path: /
          pathType: Prefix
          service: backend
  
  tls:
    - secretName: certrats-tls
      hosts:
        - certrats.com
        - www.certrats.com
        - api.certrats.com

# Monitoring configuration
monitoring:
  enabled: true
  prometheus:
    enabled: true
    retention: "15d"
    storage:
      size: 50Gi
      storageClass: "fast-ssd"
    
    resources:
      requests:
        memory: "512Mi"
        cpu: "250m"
      limits:
        memory: "2Gi"
        cpu: "1000m"
  
  grafana:
    enabled: true
    adminUser: "admin"
    adminPassword: ""
    storage:
      size: 10Gi
      storageClass: "fast-ssd"
    
    resources:
      requests:
        memory: "256Mi"
        cpu: "100m"
      limits:
        memory: "1Gi"
        cpu: "500m"
  
  alertmanager:
    enabled: true
    config:
      global:
        smtp_smarthost: 'localhost:587'
        smtp_from: '<EMAIL>'
      
      route:
        group_by: ['alertname']
        group_wait: 10s
        group_interval: 10s
        repeat_interval: 1h
        receiver: 'web.hook'
      
      receivers:
      - name: 'web.hook'
        webhook_configs:
        - url: 'http://127.0.0.1:5001/'

# Security configuration
security:
  podSecurityPolicy:
    enabled: true
  
  networkPolicy:
    enabled: true
  
  serviceAccount:
    create: true
    annotations: {}
    name: ""

# Persistence configuration
persistence:
  storageClass: "fast-ssd"
  accessMode: ReadWriteOnce
  
  postgres:
    size: 20Gi
  
  redis:
    size: 8Gi
  
  prometheus:
    size: 50Gi
  
  grafana:
    size: 10Gi

# Node affinity and tolerations
nodeSelector: {}

tolerations: []

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - certrats
        topologyKey: kubernetes.io/hostname

# Pod disruption budgets
podDisruptionBudget:
  enabled: true
  minAvailable: 2
