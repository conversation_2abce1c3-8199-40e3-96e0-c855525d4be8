# CertRats Testing Strategy & Quality Assurance Guide

## 🎯 **TESTING PHILOSOPHY**

CertRats maintains **90%+ test coverage** with a comprehensive testing strategy that ensures:
- **Reliability**: All critical paths are thoroughly tested
- **Security**: Authentication and data protection are validated
- **Performance**: Response times and scalability are verified
- **User Experience**: UI/UX functionality works across devices
- **AI Quality**: AI-generated content meets quality standards

## 🧪 **TESTING PYRAMID STRUCTURE**

### **1. Unit Tests (70% of test suite)**
**Purpose**: Test individual functions and components in isolation
**Coverage Target**: 95%+ for business logic

#### **Backend Unit Tests (Python/pytest)**
```python
# tests/test_career_path_generation.py
import pytest
from unittest.mock import Mock, patch
from utils.claude_assistant import ClaudeAssistant
from utils.advanced_career_ai import AdvancedCareerAI

class TestCareerPathGeneration:
    
    @pytest.fixture
    def mock_db_session(self):
        return Mock()
    
    @pytest.fixture
    def sample_user(self):
        return {
            "id": 1,
            "current_role": "Security Analyst",
            "years_experience": 3,
            "completed_certifications": ["Security+"],
            "interests": ["Network Security", "Cloud Security"],
            "learning_style": "Mixed",
            "study_hours_per_week": 10
        }
    
    @patch('utils.claude_assistant.ClaudeAssistant.generate_career_path')
    def test_generate_personalized_path_success(self, mock_claude, mock_db_session, sample_user):
        # Arrange
        mock_claude.return_value = {
            "career_path": [
                {
                    "stage": "Foundation",
                    "timeline": "3-6 months",
                    "certifications": ["CISSP Associate"]
                }
            ]
        }
        
        ai_service = AdvancedCareerAI(mock_db_session)
        
        # Act
        result = ai_service.generate_personalized_path(
            user=sample_user,
            target_role="Security Engineer",
            timeline_months=12
        )
        
        # Assert
        assert "career_path" in result
        assert "study_schedule" in result
        assert "personalization_score" in result
        assert len(result["career_path"]) > 0
        mock_claude.assert_called_once()
    
    def test_certification_search_filters(self, mock_db_session):
        # Test search functionality with various filters
        from utils.search_service import CertificationSearchService
        
        search_service = CertificationSearchService(mock_db_session)
        
        # Mock database query results
        mock_db_session.query.return_value.filter.return_value.count.return_value = 5
        mock_db_session.query.return_value.filter.return_value.offset.return_value.limit.return_value.all.return_value = []
        
        result = search_service.search_certifications(
            query="security",
            providers=["CompTIA"],
            levels=["beginner"],
            cost_max=500.0
        )
        
        assert "certifications" in result
        assert "total" in result
        assert result["total"] == 5

# tests/test_authentication.py
import pytest
from fastapi.testclient import TestClient
from api.app import app
from api.auth.jwt_handler import JWTHandler

client = TestClient(app)

class TestAuthentication:
    
    def test_user_registration_success(self):
        response = client.post("/api/v1/auth/register", json={
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "name": "Test User"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
    
    def test_user_registration_duplicate_email(self):
        # First registration
        client.post("/api/v1/auth/register", json={
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "name": "First User"
        })
        
        # Second registration with same email
        response = client.post("/api/v1/auth/register", json={
            "email": "<EMAIL>",
            "password": "SecurePass123!",
            "name": "Second User"
        })
        
        assert response.status_code == 400
        assert "Email already registered" in response.json()["detail"]
    
    def test_jwt_token_validation(self):
        # Test JWT token creation and validation
        token_data = {"sub": "123"}
        token = JWTHandler.create_access_token(token_data)
        
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Validate token
        decoded = JWTHandler.decode_token(token)
        assert decoded["sub"] == "123"
```

#### **Frontend Unit Tests (Jest/React Testing Library)**
```typescript
// src/components/__tests__/CareerPathForm.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AuthProvider } from '@/contexts/AuthContext';
import CareerPathForm from '@/components/CareerPathForm';

const MockAuthProvider = ({ children }: { children: React.ReactNode }) => (
  <AuthProvider>{children}</AuthProvider>
);

describe('CareerPathForm', () => {
  test('renders form fields correctly', () => {
    render(
      <MockAuthProvider>
        <CareerPathForm />
      </MockAuthProvider>
    );
    
    expect(screen.getByLabelText(/current role/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/target role/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/years of experience/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /generate career path/i })).toBeInTheDocument();
  });
  
  test('validates required fields', async () => {
    render(
      <MockAuthProvider>
        <CareerPathForm />
      </MockAuthProvider>
    );
    
    const submitButton = screen.getByRole('button', { name: /generate career path/i });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/current role is required/i)).toBeInTheDocument();
      expect(screen.getByText(/target role is required/i)).toBeInTheDocument();
    });
  });
  
  test('submits form with valid data', async () => {
    const mockSubmit = jest.fn();
    
    render(
      <MockAuthProvider>
        <CareerPathForm onSubmit={mockSubmit} />
      </MockAuthProvider>
    );
    
    fireEvent.change(screen.getByLabelText(/current role/i), {
      target: { value: 'Security Analyst' }
    });
    fireEvent.change(screen.getByLabelText(/target role/i), {
      target: { value: 'Security Engineer' }
    });
    
    fireEvent.click(screen.getByRole('button', { name: /generate career path/i }));
    
    await waitFor(() => {
      expect(mockSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          current_role: 'Security Analyst',
          target_role: 'Security Engineer'
        })
      );
    });
  });
});
```

### **2. Integration Tests (20% of test suite)**
**Purpose**: Test component interactions and API integrations
**Coverage Target**: 85%+ for critical user flows

#### **API Integration Tests**
```python
# tests/integration/test_career_path_api.py
import pytest
from fastapi.testclient import TestClient
from api.app import app
from tests.conftest import test_db, test_user

client = TestClient(app)

class TestCareerPathAPI:
    
    def test_career_path_generation_flow(self, test_db, test_user):
        # Login user
        login_response = client.post("/api/v1/auth/login", json={
            "email": test_user.email,
            "password": "testpassword"
        })
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # Generate career path
        career_path_response = client.post("/api/v1/career-path", 
            headers=headers,
            json={
                "completed_certifications": ["Security+"],
                "interests": ["Network Security"],
                "years_experience": 3,
                "current_role": "Security Analyst",
                "target_role": "Security Engineer",
                "learning_style": "Mixed",
                "study_hours": 10
            }
        )
        
        assert career_path_response.status_code == 200
        data = career_path_response.json()
        
        # Validate response structure
        assert "career_path" in data
        assert "alignment_analysis" in data
        assert "user_profile" in data
        assert len(data["career_path"]) > 0
        
        # Validate career path stages
        for stage in data["career_path"]:
            assert "stage" in stage
            assert "timeline" in stage
            assert "certifications" in stage
            assert "certification_costs" in stage
    
    def test_certification_search_integration(self, test_db):
        response = client.get("/api/v1/certifications/search", params={
            "query": "security",
            "providers": "CompTIA",
            "levels": "beginner",
            "page": 0,
            "per_page": 10
        })
        
        assert response.status_code == 200
        data = response.json()
        
        assert "certifications" in data
        assert "total" in data
        assert "page" in data
        assert data["page"] == 0
```

### **3. End-to-End Tests (10% of test suite)**
**Purpose**: Test complete user journeys across the application
**Coverage Target**: 100% for critical user paths

#### **E2E Tests with Playwright**
```typescript
// tests/e2e/career-path-generation.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Career Path Generation Flow', () => {
  test('complete career path generation journey', async ({ page }) => {
    // Navigate to homepage
    await page.goto('/');
    
    // Click on "Get AI Career Path" button
    await page.click('text=Get AI Career Path');
    
    // Fill out the career path form
    await page.fill('[data-testid="current-role"]', 'Security Analyst');
    await page.fill('[data-testid="target-role"]', 'Security Engineer');
    await page.fill('[data-testid="years-experience"]', '3');
    await page.fill('[data-testid="completed-certifications"]', 'Security+, Network+');
    await page.selectOption('[data-testid="learning-style"]', 'Mixed');
    await page.fill('[data-testid="study-hours"]', '10');
    
    // Select interests
    await page.check('[data-testid="interest-network-security"]');
    await page.check('[data-testid="interest-cloud-security"]');
    
    // Submit form
    await page.click('[data-testid="generate-career-path"]');
    
    // Wait for AI generation to complete
    await page.waitForSelector('[data-testid="career-path-results"]', { timeout: 30000 });
    
    // Verify results are displayed
    await expect(page.locator('[data-testid="career-path-stages"]')).toBeVisible();
    await expect(page.locator('[data-testid="cost-analysis"]')).toBeVisible();
    await expect(page.locator('[data-testid="timeline-estimate"]')).toBeVisible();
    
    // Verify at least one certification is recommended
    const certificationCards = page.locator('[data-testid="certification-card"]');
    await expect(certificationCards).toHaveCountGreaterThan(0);
    
    // Test export functionality
    await page.click('[data-testid="export-career-path"]');
    const downloadPromise = page.waitForEvent('download');
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toContain('career-path');
  });
  
  test('certification explorer functionality', async ({ page }) => {
    await page.goto('/certifications');
    
    // Test search functionality
    await page.fill('[data-testid="certification-search"]', 'CISSP');
    await page.press('[data-testid="certification-search"]', 'Enter');
    
    // Verify search results
    await expect(page.locator('[data-testid="certification-card"]')).toHaveCountGreaterThan(0);
    
    // Test filtering
    await page.selectOption('[data-testid="provider-filter"]', 'CompTIA');
    await page.selectOption('[data-testid="level-filter"]', 'beginner');
    
    // Apply filters
    await page.click('[data-testid="apply-filters"]');
    
    // Verify filtered results
    const filteredCards = page.locator('[data-testid="certification-card"]');
    await expect(filteredCards).toHaveCountGreaterThan(0);
    
    // Test certification detail view
    await filteredCards.first().click();
    await expect(page.locator('[data-testid="certification-detail"]')).toBeVisible();
    await expect(page.locator('[data-testid="certification-description"]')).toBeVisible();
    await expect(page.locator('[data-testid="certification-cost"]')).toBeVisible();
  });
});
```

## 🔒 **SECURITY TESTING**

### **Authentication & Authorization Tests**
```python
# tests/security/test_auth_security.py
import pytest
from fastapi.testclient import TestClient
from api.app import app

client = TestClient(app)

class TestSecurityMeasures:
    
    def test_password_strength_requirements(self):
        weak_passwords = [
            "123456",
            "password",
            "abc123",
            "qwerty"
        ]
        
        for weak_password in weak_passwords:
            response = client.post("/api/v1/auth/register", json={
                "email": f"test_{weak_password}@example.com",
                "password": weak_password,
                "name": "Test User"
            })
            assert response.status_code == 400
            assert "password" in response.json()["detail"].lower()
    
    def test_sql_injection_protection(self):
        # Test SQL injection attempts
        malicious_inputs = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "admin'--",
            "' UNION SELECT * FROM users --"
        ]
        
        for malicious_input in malicious_inputs:
            response = client.post("/api/v1/auth/login", json={
                "email": malicious_input,
                "password": "password"
            })
            # Should not cause server error, should return 401
            assert response.status_code in [400, 401]
    
    def test_rate_limiting(self):
        # Test rate limiting on login attempts
        for i in range(10):
            response = client.post("/api/v1/auth/login", json={
                "email": "<EMAIL>",
                "password": "wrongpassword"
            })
        
        # After multiple failed attempts, should be rate limited
        response = client.post("/api/v1/auth/login", json={
            "email": "<EMAIL>",
            "password": "wrongpassword"
        })
        assert response.status_code == 429  # Too Many Requests
```

## 📊 **PERFORMANCE TESTING**

### **Load Testing with k6**
```javascript
// tests/performance/load-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

export let errorRate = new Rate('errors');

export let options = {
  stages: [
    { duration: '2m', target: 10 }, // Ramp up to 10 users
    { duration: '5m', target: 10 }, // Stay at 10 users
    { duration: '2m', target: 50 }, // Ramp up to 50 users
    { duration: '5m', target: 50 }, // Stay at 50 users
    { duration: '2m', target: 0 },  // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests under 2s
    http_req_failed: ['rate<0.01'],    // Error rate under 1%
    errors: ['rate<0.01'],
  },
};

export default function() {
  // Test career path generation
  let careerPathPayload = JSON.stringify({
    completed_certifications: ['Security+'],
    interests: ['Network Security', 'Cloud Security'],
    years_experience: 3,
    current_role: 'Security Analyst',
    target_role: 'Security Engineer',
    learning_style: 'Mixed',
    study_hours: 10
  });

  let params = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  let response = http.post('http://localhost:8000/api/v1/career-path', careerPathPayload, params);
  
  let success = check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 2000ms': (r) => r.timings.duration < 2000,
    'response has career_path': (r) => JSON.parse(r.body).career_path !== undefined,
  });

  errorRate.add(!success);
  
  sleep(1);
}
```

## 🎯 **AI QUALITY TESTING**

### **AI Response Validation**
```python
# tests/ai/test_ai_quality.py
import pytest
from utils.claude_assistant import ClaudeAssistant
from utils.ai_response_validator import AIResponseValidator

class TestAIQuality:
    
    def test_career_path_response_structure(self):
        claude = ClaudeAssistant()
        validator = AIResponseValidator()
        
        test_input = {
            "completed_certifications": ["Security+"],
            "interests": ["Network Security"],
            "years_experience": 3,
            "current_role": "Security Analyst",
            "target_role": "Security Engineer",
            "learning_style": "Mixed",
            "study_hours": 10
        }
        
        response = claude.generate_career_path(test_input)
        
        # Validate response structure
        assert validator.validate_career_path_structure(response)
        assert "career_path" in response
        assert "alignment_analysis" in response
        
        # Validate content quality
        assert validator.validate_certification_recommendations(response["career_path"])
        assert validator.validate_cost_estimates(response["career_path"])
        assert validator.validate_timeline_realism(response["career_path"])
    
    def test_ai_response_consistency(self):
        # Test that similar inputs produce consistent outputs
        claude = ClaudeAssistant()
        
        test_input = {
            "completed_certifications": ["Security+"],
            "current_role": "Security Analyst",
            "target_role": "Security Engineer",
            "years_experience": 3
        }
        
        responses = []
        for _ in range(3):
            response = claude.generate_career_path(test_input)
            responses.append(response)
        
        # Check consistency in recommendations
        recommended_certs = [set(r["career_path"][0]["certifications"]) for r in responses]
        
        # At least 70% overlap in recommendations
        overlap = len(recommended_certs[0] & recommended_certs[1] & recommended_certs[2])
        total_unique = len(recommended_certs[0] | recommended_certs[1] | recommended_certs[2])
        consistency_ratio = overlap / total_unique
        
        assert consistency_ratio >= 0.7
```

## 📈 **CONTINUOUS TESTING STRATEGY**

### **CI/CD Integration**
```yaml
# .github/workflows/test.yml
name: Comprehensive Testing

on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install -r requirements-test.txt
      
      - name: Run unit tests
        run: |
          pytest tests/unit/ --cov=api --cov-report=xml --cov-report=html
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpass
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      - name: Run integration tests
        run: pytest tests/integration/

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Install Playwright
        run: |
          npm ci
          npx playwright install
      
      - name: Run E2E tests
        run: npx playwright test

  security-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run security tests
        run: |
          pip install bandit safety
          bandit -r api/
          safety check
```

## 🎯 **QUALITY METRICS & MONITORING**

### **Test Coverage Goals**
- **Unit Tests**: 95%+ coverage for business logic
- **Integration Tests**: 85%+ coverage for API endpoints
- **E2E Tests**: 100% coverage for critical user journeys
- **Security Tests**: 100% coverage for authentication flows

### **Performance Benchmarks**
- **API Response Time**: <2s (95th percentile)
- **Page Load Time**: <3s (95th percentile)
- **AI Generation Time**: <30s (95th percentile)
- **Database Query Time**: <500ms (95th percentile)

### **Quality Gates**
- All tests must pass before deployment
- Code coverage must not decrease
- Performance benchmarks must be met
- Security scans must pass
- AI response quality must meet standards

This comprehensive testing strategy ensures CertRats maintains high quality, security, and performance standards while enabling rapid development and deployment.
