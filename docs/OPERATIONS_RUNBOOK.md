# CertRats Operations Runbook

## 📋 **Operations Overview**

This runbook provides comprehensive operational procedures for managing CertRats in production, including monitoring, troubleshooting, maintenance, and incident response.

## 🔍 **Daily Operations**

### **Morning Health Check (9:00 AM)**

```bash
#!/bin/bash
# Daily Health Check Script

echo "🌅 CertRats Daily Health Check - $(date)"
echo "================================================"

# 1. Check overall system health
echo "📊 System Health:"
kubectl get nodes
kubectl get pods -n certrats --field-selector=status.phase!=Running

# 2. Check application endpoints
echo "🌐 Application Health:"
curl -s https://certrats.com/health | jq .
curl -s https://api.certrats.com/api/v1/health | jq .

# 3. Check database connectivity
echo "🗄️ Database Health:"
kubectl exec -it $(kubectl get pods -l app=postgres -n certrats -o jsonpath='{.items[0].metadata.name}') -n certrats -- pg_isready

# 4. Check Redis connectivity
echo "🔄 Cache Health:"
kubectl exec -it $(kubectl get pods -l app=redis-cluster -n certrats -o jsonpath='{.items[0].metadata.name}') -n certrats -- redis-cli ping

# 5. Check resource usage
echo "💻 Resource Usage:"
kubectl top nodes
kubectl top pods -n certrats

# 6. Check recent errors
echo "🚨 Recent Errors:"
kubectl logs -l app=backend -n certrats --since=24h | grep -i error | tail -10

# 7. Check backup status
echo "💾 Backup Status:"
velero backup get | head -5

# 8. Generate summary
echo "✅ Daily health check completed at $(date)"
```

### **Performance Monitoring**

```bash
# Performance Monitoring Script
#!/bin/bash

echo "📈 Performance Monitoring - $(date)"

# API Response Times
echo "⏱️ API Response Times:"
curl -w "@curl-format.txt" -s -o /dev/null https://api.certrats.com/api/v1/health
curl -w "@curl-format.txt" -s -o /dev/null https://api.certrats.com/api/v1/certifications

# Database Performance
echo "🗄️ Database Performance:"
kubectl exec -it $(kubectl get pods -l app=postgres -n certrats -o jsonpath='{.items[0].metadata.name}') -n certrats -- psql -U certrats_user -d certrats -c "
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE schemaname = 'public' 
ORDER BY n_distinct DESC 
LIMIT 10;"

# Cache Hit Ratio
echo "🔄 Cache Performance:"
kubectl exec -it $(kubectl get pods -l app=redis-cluster -n certrats -o jsonpath='{.items[0].metadata.name}') -n certrats -- redis-cli info stats | grep hit_rate

# Resource Utilization
echo "💻 Resource Utilization:"
kubectl top pods -n certrats --sort-by=cpu
kubectl top pods -n certrats --sort-by=memory
```

## 🚨 **Incident Response Procedures**

### **Severity Levels**

| Level | Description | Response Time | Actions |
|-------|-------------|---------------|---------|
| P0 | Complete service outage | 5 minutes | All hands, immediate escalation |
| P1 | Significant service degradation | 15 minutes | On-call engineer, team lead |
| P2 | Minor service issues | 1 hour | Assigned engineer |
| P3 | Non-urgent issues | 4 hours | Next business day |

### **P0 - Complete Service Outage**

```bash
#!/bin/bash
# P0 Incident Response

echo "🚨 P0 INCIDENT RESPONSE ACTIVATED"
echo "Time: $(date)"

# 1. Immediate assessment
echo "📊 Service Status Check:"
curl -I https://certrats.com
curl -I https://api.certrats.com/api/v1/health

# 2. Check infrastructure
echo "🏗️ Infrastructure Status:"
kubectl get nodes
kubectl get pods -n certrats
kubectl get services -n certrats

# 3. Check recent deployments
echo "🚀 Recent Deployments:"
kubectl rollout history deployment/backend -n certrats
kubectl rollout history deployment/frontend -n certrats

# 4. Check logs for errors
echo "📝 Error Analysis:"
kubectl logs -l app=backend -n certrats --since=1h | grep -i error | tail -20
kubectl logs -l app=frontend -n certrats --since=1h | grep -i error | tail -20

# 5. Immediate mitigation options
echo "🔧 Mitigation Options:"
echo "1. Rollback deployment: kubectl rollout undo deployment/backend -n certrats"
echo "2. Scale up replicas: kubectl scale deployment backend --replicas=5 -n certrats"
echo "3. Restart services: kubectl rollout restart deployment/backend -n certrats"
echo "4. Enable maintenance mode: kubectl apply -f k8s/maintenance-mode.yaml"

# 6. Communication
echo "📢 Communication:"
echo "1. Update status page: https://status.certrats.com"
echo "2. Notify stakeholders via Slack: #incidents"
echo "3. Send customer communication if needed"
```

### **P1 - Service Degradation**

```bash
#!/bin/bash
# P1 Incident Response

echo "⚠️ P1 INCIDENT RESPONSE ACTIVATED"

# 1. Performance analysis
echo "📈 Performance Analysis:"
kubectl top pods -n certrats
curl -w "@curl-format.txt" -s -o /dev/null https://api.certrats.com/api/v1/health

# 2. Check for resource constraints
echo "💻 Resource Analysis:"
kubectl describe nodes | grep -A 5 "Allocated resources"
kubectl get events -n certrats --sort-by='.lastTimestamp' | tail -10

# 3. Database performance check
echo "🗄️ Database Performance:"
kubectl exec -it $(kubectl get pods -l app=postgres -n certrats -o jsonpath='{.items[0].metadata.name}') -n certrats -- psql -U certrats_user -d certrats -c "
SELECT 
    query,
    calls,
    total_time,
    mean_time
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;"

# 4. Auto-scaling check
echo "🔄 Auto-scaling Status:"
kubectl get hpa -n certrats
kubectl describe hpa backend-hpa -n certrats
```

## 🔧 **Maintenance Procedures**

### **Weekly Maintenance (Sunday 2:00 AM)**

```bash
#!/bin/bash
# Weekly Maintenance Script

echo "🔧 Weekly Maintenance - $(date)"

# 1. Update system packages
echo "📦 Updating system packages..."
kubectl apply -f k8s/maintenance-mode.yaml

# 2. Database maintenance
echo "🗄️ Database Maintenance:"
kubectl exec -it $(kubectl get pods -l app=postgres -n certrats -o jsonpath='{.items[0].metadata.name}') -n certrats -- psql -U certrats_user -d certrats -c "
VACUUM ANALYZE;
REINDEX DATABASE certrats;
"

# 3. Clear old logs
echo "📝 Log Cleanup:"
kubectl delete pods -l app=log-cleanup -n certrats

# 4. Backup verification
echo "💾 Backup Verification:"
velero backup get | head -10
aws s3 ls s3://certrats-database-backups/postgres/ | tail -5

# 5. Security updates
echo "🔒 Security Updates:"
kubectl apply -f k8s/security-updates.yaml

# 6. Performance optimization
echo "⚡ Performance Optimization:"
python scripts/performance-optimization.py --once

# 7. Remove maintenance mode
echo "✅ Removing Maintenance Mode:"
kubectl delete -f k8s/maintenance-mode.yaml

echo "✅ Weekly maintenance completed"
```

### **Monthly Maintenance (First Sunday)**

```bash
#!/bin/bash
# Monthly Maintenance Script

echo "🗓️ Monthly Maintenance - $(date)"

# 1. Certificate renewal check
echo "🔒 Certificate Status:"
kubectl get certificate -n certrats
openssl x509 -in /tmp/cert.pem -text -noout | grep "Not After"

# 2. Dependency updates
echo "📦 Dependency Updates:"
# Check for security updates
trivy image ghcr.io/certrats/backend:latest
trivy image ghcr.io/certrats/frontend:latest

# 3. Cost optimization review
echo "💰 Cost Optimization:"
./scripts/cost-optimization.sh --report-only

# 4. Security audit
echo "🔍 Security Audit:"
./scripts/security-scan.sh

# 5. Disaster recovery test
echo "🔄 DR Test:"
velero backup create monthly-dr-test --include-namespaces certrats
velero restore create monthly-dr-test-restore --from-backup monthly-dr-test

# 6. Performance baseline update
echo "📊 Performance Baseline:"
k6 run scripts/load-testing.js --out json=monthly-performance-$(date +%Y%m).json

echo "✅ Monthly maintenance completed"
```

## 📊 **Monitoring and Alerting**

### **Key Metrics to Monitor**

#### Application Metrics
```yaml
# Prometheus Queries for Key Metrics

# Response Time (95th percentile)
histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))

# Error Rate
rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) * 100

# Throughput (requests per second)
rate(http_requests_total[5m])

# Active Users
increase(user_sessions_total[1h])

# Database Connections
pg_stat_database_numbackends

# Cache Hit Ratio
redis_keyspace_hits_total / (redis_keyspace_hits_total + redis_keyspace_misses_total) * 100
```

#### Infrastructure Metrics
```yaml
# CPU Utilization
100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)

# Memory Utilization
(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100

# Disk Usage
100 - ((node_filesystem_avail_bytes * 100) / node_filesystem_size_bytes)

# Network I/O
rate(node_network_receive_bytes_total[5m])
rate(node_network_transmit_bytes_total[5m])
```

### **Alert Configurations**

```yaml
# Critical Alerts
groups:
- name: critical
  rules:
  - alert: ServiceDown
    expr: up{job="certrats-backend"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "CertRats service is down"
      
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      
  - alert: DatabaseDown
    expr: up{job="postgres"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Database is down"

# Warning Alerts
- name: warning
  rules:
  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "High response time detected"
      
  - alert: HighCPUUsage
    expr: 100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 15m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage detected"
```

## 🔄 **Backup and Recovery**

### **Backup Procedures**

```bash
#!/bin/bash
# Backup Procedures

echo "💾 Starting Backup Procedures"

# 1. Application backup with Velero
echo "📦 Application Backup:"
velero backup create daily-backup-$(date +%Y%m%d) \
  --include-namespaces certrats \
  --storage-location aws-s3 \
  --volume-snapshot-locations aws-ebs

# 2. Database backup
echo "🗄️ Database Backup:"
kubectl exec -it $(kubectl get pods -l app=postgres -n certrats -o jsonpath='{.items[0].metadata.name}') -n certrats -- pg_dump -U certrats_user certrats | gzip > backup-$(date +%Y%m%d).sql.gz

# Upload to S3
aws s3 cp backup-$(date +%Y%m%d).sql.gz s3://certrats-database-backups/postgres/

# 3. Configuration backup
echo "⚙️ Configuration Backup:"
kubectl get configmaps -n certrats -o yaml > configmaps-backup-$(date +%Y%m%d).yaml
kubectl get secrets -n certrats -o yaml > secrets-backup-$(date +%Y%m%d).yaml

# 4. Verify backups
echo "✅ Backup Verification:"
velero backup describe daily-backup-$(date +%Y%m%d)
aws s3 ls s3://certrats-database-backups/postgres/backup-$(date +%Y%m%d).sql.gz

echo "✅ Backup procedures completed"
```

### **Recovery Procedures**

```bash
#!/bin/bash
# Recovery Procedures

BACKUP_DATE=$1
if [ -z "$BACKUP_DATE" ]; then
  echo "Usage: $0 <backup_date>"
  exit 1
fi

echo "🔄 Starting Recovery Procedures for $BACKUP_DATE"

# 1. Application recovery
echo "📦 Application Recovery:"
velero restore create restore-$BACKUP_DATE \
  --from-backup daily-backup-$BACKUP_DATE \
  --wait

# 2. Database recovery
echo "🗄️ Database Recovery:"
aws s3 cp s3://certrats-database-backups/postgres/backup-$BACKUP_DATE.sql.gz .
gunzip backup-$BACKUP_DATE.sql.gz

kubectl exec -i $(kubectl get pods -l app=postgres -n certrats -o jsonpath='{.items[0].metadata.name}') -n certrats -- psql -U certrats_user certrats < backup-$BACKUP_DATE.sql

# 3. Verify recovery
echo "✅ Recovery Verification:"
kubectl get pods -n certrats
curl https://certrats.com/health
curl https://api.certrats.com/api/v1/health

echo "✅ Recovery procedures completed"
```

## 📞 **Escalation Procedures**

### **Contact Matrix**

| Role | Primary | Secondary | Phone | Email |
|------|---------|-----------|-------|-------|
| On-Call Engineer | John Doe | Jane Smith | ******-0101 | <EMAIL> |
| DevOps Lead | Jane Smith | Bob Johnson | ******-0102 | <EMAIL> |
| Security Lead | Bob Johnson | Alice Brown | ******-0103 | <EMAIL> |
| Engineering Manager | Alice Brown | John Doe | ******-0104 | <EMAIL> |
| CTO | Mike Wilson | - | ******-0105 | <EMAIL> |

### **Escalation Timeline**

```
P0 Incidents:
0-5 min:   On-Call Engineer
5-15 min:  DevOps Lead + Engineering Manager
15-30 min: CTO + Security Lead (if security-related)
30+ min:   Executive team notification

P1 Incidents:
0-15 min:  On-Call Engineer
15-60 min: DevOps Lead
1-4 hours: Engineering Manager
4+ hours:  CTO notification

P2/P3 Incidents:
Follow normal business hours escalation
```

## 📈 **Performance Optimization**

### **Regular Performance Reviews**

```bash
#!/bin/bash
# Performance Review Script

echo "📈 Performance Review - $(date)"

# 1. Response time analysis
echo "⏱️ Response Time Analysis:"
kubectl exec -it $(kubectl get pods -l app=prometheus -n certrats-monitoring -o jsonpath='{.items[0].metadata.name}') -n certrats-monitoring -- \
  promtool query instant 'histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[24h]))'

# 2. Throughput analysis
echo "🚀 Throughput Analysis:"
kubectl exec -it $(kubectl get pods -l app=prometheus -n certrats-monitoring -o jsonpath='{.items[0].metadata.name}') -n certrats-monitoring -- \
  promtool query instant 'rate(http_requests_total[24h])'

# 3. Error rate analysis
echo "🚨 Error Rate Analysis:"
kubectl exec -it $(kubectl get pods -l app=prometheus -n certrats-monitoring -o jsonpath='{.items[0].metadata.name}') -n certrats-monitoring -- \
  promtool query instant 'rate(http_requests_total{status=~"5.."}[24h]) / rate(http_requests_total[24h]) * 100'

# 4. Resource utilization
echo "💻 Resource Utilization:"
kubectl top nodes
kubectl top pods -n certrats --sort-by=cpu

# 5. Database performance
echo "🗄️ Database Performance:"
kubectl exec -it $(kubectl get pods -l app=postgres -n certrats -o jsonpath='{.items[0].metadata.name}') -n certrats -- psql -U certrats_user -d certrats -c "
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;"

echo "✅ Performance review completed"
```

## 📚 **Knowledge Base**

### **Common Issues and Solutions**

#### High Memory Usage
```bash
# Identify memory-intensive pods
kubectl top pods -n certrats --sort-by=memory

# Check for memory leaks
kubectl exec -it <pod-name> -n certrats -- ps aux --sort=-%mem | head -10

# Restart high-memory pods
kubectl delete pod <pod-name> -n certrats
```

#### Database Connection Pool Exhaustion
```bash
# Check active connections
kubectl exec -it $(kubectl get pods -l app=postgres -n certrats -o jsonpath='{.items[0].metadata.name}') -n certrats -- psql -U certrats_user -d certrats -c "SELECT count(*) FROM pg_stat_activity;"

# Check PgBouncer status
kubectl exec -it $(kubectl get pods -l app=pgbouncer -n certrats -o jsonpath='{.items[0].metadata.name}') -n certrats -- psql -h localhost -p 6432 -U pgbouncer pgbouncer -c "SHOW POOLS;"

# Restart connection pooler
kubectl rollout restart deployment pgbouncer -n certrats
```

#### SSL Certificate Issues
```bash
# Check certificate expiration
kubectl get certificate -n certrats
kubectl describe certificate certrats-tls -n certrats

# Force certificate renewal
kubectl delete certificate certrats-tls -n certrats
kubectl apply -f k8s/ssl-certificates.yaml
```

### **Useful Commands Reference**

```bash
# Quick health check
kubectl get pods -n certrats | grep -v Running

# View recent logs
kubectl logs -l app=backend -n certrats --since=1h | tail -50

# Scale application
kubectl scale deployment backend --replicas=5 -n certrats

# Rolling restart
kubectl rollout restart deployment backend -n certrats

# Check resource usage
kubectl top pods -n certrats

# Port forward for debugging
kubectl port-forward service/backend-service 8000:8000 -n certrats

# Execute commands in pods
kubectl exec -it <pod-name> -n certrats -- /bin/bash

# View events
kubectl get events -n certrats --sort-by='.lastTimestamp'
```

## 🎯 **SLA and KPI Monitoring**

### **Service Level Objectives (SLOs)**

| Metric | Target | Measurement |
|--------|--------|-------------|
| Availability | 99.9% | Uptime monitoring |
| Response Time | <2s (95th percentile) | Application metrics |
| Error Rate | <1% | Error tracking |
| Recovery Time | <15 minutes | Incident response |
| Backup Success | 100% | Backup monitoring |

### **Key Performance Indicators (KPIs)**

```bash
# Daily KPI Report
#!/bin/bash

echo "📊 Daily KPI Report - $(date)"

# Availability
echo "🟢 Availability: $(curl -s https://status.certrats.com/api/uptime)"

# Response Time
echo "⏱️ Response Time: $(curl -w '%{time_total}' -s -o /dev/null https://api.certrats.com/api/v1/health)s"

# Active Users
echo "👥 Active Users: $(kubectl logs -l app=backend -n certrats --since=24h | grep 'user_login' | wc -l)"

# API Calls
echo "🔄 API Calls: $(kubectl logs -l app=backend -n certrats --since=24h | grep 'GET\|POST\|PUT\|DELETE' | wc -l)"

# Error Count
echo "🚨 Errors: $(kubectl logs -l app=backend -n certrats --since=24h | grep -i error | wc -l)"

echo "✅ KPI report completed"
```
