/* Custom CSS for CertRats Documentation */

/* Brand colors */
:root {
    --certrats-primary: #2563eb;
    --certrats-secondary: #1e40af;
    --certrats-accent: #3b82f6;
    --certrats-success: #10b981;
    --certrats-warning: #f59e0b;
    --certrats-error: #ef4444;
    --certrats-dark: #1f2937;
    --certrats-light: #f8fafc;
}

/* Header customization */
.wy-nav-top {
    background-color: var(--certrats-primary) !important;
}

.wy-nav-top a {
    color: white !important;
}

/* Sidebar customization */
.wy-nav-side {
    background: linear-gradient(180deg, var(--certrats-primary) 0%, var(--certrats-secondary) 100%);
}

.wy-menu-vertical a {
    color: rgba(255, 255, 255, 0.9) !important;
}

.wy-menu-vertical a:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

.wy-menu-vertical li.current a {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    border-right: 3px solid var(--certrats-accent);
}

.wy-menu-vertical li.current > a {
    background-color: rgba(255, 255, 255, 0.15) !important;
}

/* Content area */
.wy-nav-content {
    background-color: var(--certrats-light);
}

/* Code blocks */
.highlight {
    background-color: #f8f9fa !important;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
}

.highlight pre {
    background-color: transparent !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Admonitions */
.admonition {
    border-radius: 6px;
    border-left: 4px solid var(--certrats-primary);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admonition.note {
    border-left-color: var(--certrats-primary);
}

.admonition.warning {
    border-left-color: var(--certrats-warning);
}

.admonition.danger {
    border-left-color: var(--certrats-error);
}

.admonition.tip {
    border-left-color: var(--certrats-success);
}

/* Tables */
.wy-table-responsive table td,
.wy-table-responsive table th {
    white-space: normal !important;
}

table.docutils {
    border-collapse: collapse;
    border-spacing: 0;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    overflow: hidden;
}

table.docutils th {
    background-color: var(--certrats-primary);
    color: white;
    font-weight: 600;
    padding: 12px;
    text-align: left;
}

table.docutils td {
    padding: 12px;
    border-top: 1px solid #e1e4e8;
}

table.docutils tr:nth-child(even) {
    background-color: #f6f8fa;
}

/* Links */
a {
    color: var(--certrats-primary) !important;
}

a:hover {
    color: var(--certrats-secondary) !important;
}

/* Buttons */
.btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 8px 16px;
    text-decoration: none !important;
    display: inline-block;
    margin: 4px;
}

.btn-primary {
    background-color: var(--certrats-primary);
    color: white;
    border: 1px solid var(--certrats-primary);
}

.btn-primary:hover {
    background-color: var(--certrats-secondary);
    border-color: var(--certrats-secondary);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    border: 1px solid #6c757d;
}

/* Custom badges */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 4px;
    margin: 2px;
}

.badge-success {
    background-color: var(--certrats-success);
    color: white;
}

.badge-warning {
    background-color: var(--certrats-warning);
    color: white;
}

.badge-error {
    background-color: var(--certrats-error);
    color: white;
}

.badge-primary {
    background-color: var(--certrats-primary);
    color: white;
}

/* Feature grid */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.feature-card {
    background: white;
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.feature-card h3 {
    color: var(--certrats-primary);
    margin-top: 0;
    margin-bottom: 1rem;
}

/* Status indicators */
.status-complete {
    color: var(--certrats-success);
    font-weight: 600;
}

.status-in-progress {
    color: var(--certrats-warning);
    font-weight: 600;
}

.status-planned {
    color: #6c757d;
    font-weight: 600;
}

/* Emoji support */
.emoji {
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Android Emoji", sans-serif;
    font-size: 1.2em;
    vertical-align: middle;
}

/* Responsive design */
@media (max-width: 768px) {
    .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .wy-nav-content {
        margin-left: 0;
    }
}

/* Print styles */
@media print {
    .wy-nav-side,
    .wy-nav-top,
    .rst-footer-buttons {
        display: none !important;
    }
    
    .wy-nav-content {
        margin-left: 0 !important;
    }
}
