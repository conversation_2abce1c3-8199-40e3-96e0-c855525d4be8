# Database Documentation

## Entity Relationship Diagram

```mermaid
erDiagram
    organizations ||--o{ certifications : issues
    certifications ||--o{ certification_versions : tracks
    certifications }|--o{ certification_prerequisites : requires
    certifications }|--o{ certification_prerequisites : required_by
    user_experiences ||--o{ learning_paths : creates
    learning_paths ||--o{ learning_path_items : contains
    learning_path_items ||--o{ study_resources : has
    learning_path_items }o--|| certifications : studies

    organizations {
        int id PK
        string name UK
        string country
        string url
        text description
        datetime created_at
        datetime updated_at
    }

    certifications {
        int id PK
        string name UK
        string category
        string domain
        string level
        int difficulty
        float cost
        text description
        text prerequisites
        int validity_period
        string exam_code
        int organization_id FK
        string url
        int custom_hours
        text study_notes
        text course_content
        int current_version
        datetime last_updated
        datetime created_at
    }

    certification_versions {
        int id PK
        int certification_id FK
        int version
        string name
        string category
        string domain
        string level
        int difficulty
        float cost
        text description
        text prerequisites
        int validity_period
        string exam_code
        int organization_id FK
        string url
        int custom_hours
        text study_notes
        text course_content
        datetime changed_at
        text change_reason
        string change_source
        string reference_url
    }

    user_experiences {
        int id PK
        string user_id
        int years_experience
        string user_role
        string desired_role
        json expertise_areas
        string preferred_learning_style
        int study_time_available
        datetime created_at
        datetime updated_at
    }

    learning_paths {
        int id PK
        int user_experience_id FK
        string name
        text description
        int estimated_duration_weeks
        string difficulty_level
        string status
        datetime created_at
        datetime updated_at
    }

    learning_path_items {
        int id PK
        int learning_path_id FK
        int certification_id FK
        int sequence
        string item_type
        string name
        text description
        int estimated_hours
        string status
        datetime created_at
        datetime updated_at
    }

    study_resources {
        int id PK
        int learning_path_item_id FK
        string resource_type
        string title
        string url
        text description
        boolean is_official
        float rating
        string access_type
        datetime created_at
        datetime updated_at
    }
```

## Schema Overview

The database schema consists of several interconnected models designed to manage security certifications, learning paths, and user experiences. Below is a detailed breakdown of each model and its relationships.

### Core Models

#### Organizations
```sql
organizations
├── id (Primary Key)
├── name (String, Unique)
├── country (String)
├── url (String, Optional)
├── description (Text, Optional)
├── created_at (DateTime)
└── updated_at (DateTime)
```

#### Certifications
```sql
certifications
├── id (Primary Key)
├── name (String, Unique)
├── category (String)
├── domain (String)
├── level (String)
├── difficulty (Integer)
├── cost (Float, Optional)
├── description (Text, Optional)
├── prerequisites (Text, Optional)
├── validity_period (Integer, Optional)
├── exam_code (String, Optional)
├── organization_id (Foreign Key)
├── url (String, Optional)
├── custom_hours (Integer, Optional)
├── study_notes (Text, Optional)
├── course_content (Text, Optional)
├── current_version (Integer)
├── last_updated (DateTime)
└── created_at (DateTime)
```

#### Certification Versions
```sql
certification_versions
├── id (Primary Key)
├── certification_id (Foreign Key)
├── version (Integer)
├── name (String)
├── category (String)
├── domain (String)
├── level (String)
├── difficulty (Integer)
├── cost (Float, Optional)
├── description (Text, Optional)
├── prerequisites (Text, Optional)
├── validity_period (Integer, Optional)
├── exam_code (String, Optional)
├── organization_id (Foreign Key)
├── url (String, Optional)
├── custom_hours (Integer, Optional)
├── study_notes (Text, Optional)
├── course_content (Text, Optional)
├── changed_at (DateTime)
├── change_reason (Text, Optional)
├── change_source (String, Optional)
└── reference_url (String, Optional)
```

### Learning Path Models

#### User Experiences
```sql
user_experiences
├── id (Primary Key)
├── user_id (String)
├── years_experience (Integer)
├── user_role (String, Optional)
├── desired_role (String, Optional)
├── expertise_areas (JSON)
├── preferred_learning_style (String, Optional)
├── study_time_available (Integer, Optional)
├── created_at (DateTime)
└── updated_at (DateTime)
```

#### Learning Paths
```sql
learning_paths
├── id (Primary Key)
├── user_experience_id (Foreign Key)
├── name (String)
├── description (Text, Optional)
├── estimated_duration_weeks (Integer, Optional)
├── difficulty_level (String)
├── status (String)
├── created_at (DateTime)
└── updated_at (DateTime)
```

#### Learning Path Items
```sql
learning_path_items
├── id (Primary Key)
├── learning_path_id (Foreign Key)
├── certification_id (Foreign Key, Optional)
├── sequence (Integer)
├── item_type (String)
├── name (String)
├── description (Text, Optional)
├── estimated_hours (Integer, Optional)
├── status (String)
├── created_at (DateTime)
└── updated_at (DateTime)
```

#### Study Resources
```sql
study_resources
├── id (Primary Key)
├── learning_path_item_id (Foreign Key)
├── resource_type (String)
├── title (String)
├── url (String)
├── description (Text, Optional)
├── is_official (Boolean)
├── rating (Float, Optional)
├── access_type (String, Optional)
├── created_at (DateTime)
└── updated_at (DateTime)
```

### Relationships

1. Organizations -> Certifications: One-to-Many
2. Certifications -> CertificationVersions: One-to-Many
3. Certifications -> Prerequisites: Many-to-Many (self-referential)
4. UserExperience -> LearningPaths: One-to-Many
5. LearningPath -> LearningPathItems: One-to-Many
6. LearningPathItem -> StudyResources: One-to-Many
7. LearningPathItem -> Certification: Many-to-One

## Database Maintenance

### 1. Database Migrations

We use Flask-Migrate for handling database migrations. Here are the key commands:

```bash
# Initialize migrations (first time only)
flask db init

# Create a new migration
flask db migrate -m "Description of changes"

# Apply migrations
flask db upgrade

# Rollback migrations
flask db downgrade
```

### 2. Backup and Restore

To backup the database:
```bash
pg_dump $DATABASE_URL > backup.sql
```

To restore from backup:
```bash
psql $DATABASE_URL < backup.sql
```

### 3. Data Seeding

Initial data seeding is handled through the `data/seed.py` script. The script:
1. Imports certification data from the security certification roadmap
2. Creates organization records
3. Creates certification records with proper relationships
4. Establishes prerequisite relationships between certifications

To seed the database:
```bash
python data/seed.py
```

### 4. Version Control

The system maintains version history for certifications:
- Each change to a certification creates a new version record
- Version records track the change reason and source
- The current_version field in certifications table tracks the latest version

### 5. Performance Optimization

1. Indexes are automatically created on:
   - Primary keys
   - Foreign keys
   - Unique constraints (e.g., certification names)

2. Query Optimization:
   - Use eager loading with `relationship(lazy='joined')` for frequently accessed relationships
   - Keep the certification versions table pruned if storage becomes an issue

### 6. Error Handling

The database connections are managed with:
- Connection pooling
- Automatic reconnection on failure
- SSL for secure connections
- Transaction management

Common errors and solutions:
1. Connection timeouts: Check `pool_recycle` setting
2. Deadlocks: Review transaction isolation levels
3. Connection pool exhaustion: Monitor `pool_size` setting

## Development Guidelines

1. Always use SQLAlchemy models for database operations
2. Create new versions when updating certifications
3. Use transactions for operations that modify multiple tables
4. Keep foreign key relationships consistent
5. Validate data before insertion
6. Handle NULL values appropriately
7. Use optimistic locking for concurrent updates

## Monitoring and Maintenance

1. Regular Tasks:
   - Monitor database size and growth
   - Check for slow queries
   - Validate data integrity
   - Update certifications when sources change

2. Health Checks:
   - Connection pool status
   - Transaction duration
   - Lock contention
   - Cache hit ratios