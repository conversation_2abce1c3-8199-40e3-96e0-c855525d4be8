# CertRats Production Deployment Guide

This guide provides comprehensive instructions for deploying CertRats to production with enterprise-grade infrastructure, monitoring, and security.

## Prerequisites

### Infrastructure Requirements
- **Kubernetes Cluster**: v1.28+ (AWS EKS, GKE, or AKS)
- **Node Requirements**: Minimum 3 nodes, 4 vCPU, 16GB RAM each
- **Storage**: SSD-backed storage class with 100GB+ available
- **Network**: Load balancer with SSL/TLS termination
- **DNS**: Domain name with SSL certificate

### Tools Required
```bash
# Install required tools
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
curl -L https://github.com/vmware-tanzu/velero/releases/download/v1.12.0/velero-v1.12.0-linux-amd64.tar.gz | tar xz
```

## Infrastructure Deployment

### Step 1: Provision Infrastructure with Terraform

```bash
# Clone the repository
git clone https://github.com/your-org/certrats.git
cd certrats

# Initialize Terraform
cd terraform
terraform init

# Review and customize variables
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your specific values

# Plan infrastructure deployment
terraform plan -var-file="terraform.tfvars"

# Deploy infrastructure
terraform apply -var-file="terraform.tfvars"

# Get cluster credentials
aws eks update-kubeconfig --region us-east-1 --name certrats-production
```

### Step 2: Verify Cluster Setup

```bash
# Verify cluster connectivity
kubectl cluster-info

# Check node status
kubectl get nodes

# Verify storage classes
kubectl get storageclass

# Check for required namespaces
kubectl get namespaces
```

## Application Deployment

### Step 3: Deploy Core Infrastructure

```bash
# Create namespaces
kubectl apply -f k8s/namespaces.yaml

# Deploy secrets (update with actual values)
kubectl create secret generic certrats-secrets \
  --from-literal=DATABASE_URL="********************************/certrats" \
  --from-literal=JWT_SECRET_KEY="your-jwt-secret" \
  --from-literal=ANTHROPIC_API_KEY="your-anthropic-key" \
  --from-literal=REDIS_PASSWORD="your-redis-password" \
  -n certrats

# Deploy PostgreSQL and Redis
kubectl apply -f k8s/postgres.yaml
kubectl apply -f k8s/performance-optimization.yaml

# Wait for databases to be ready
kubectl wait --for=condition=ready pod -l app=postgres -n certrats --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis-cluster -n certrats --timeout=300s
```

### Step 4: Deploy Application Services

```bash
# Deploy backend services
kubectl apply -f k8s/backend.yaml

# Deploy frontend services
kubectl apply -f k8s/frontend.yaml

# Wait for deployments to be ready
kubectl wait --for=condition=available deployment/backend -n certrats --timeout=600s
kubectl wait --for=condition=available deployment/frontend -n certrats --timeout=600s

# Verify pod status
kubectl get pods -n certrats
```

### Step 5: Deploy Monitoring and Observability

```bash
# Create monitoring namespace
kubectl create namespace certrats-monitoring

# Deploy monitoring stack
kubectl apply -f k8s/monitoring.yaml
kubectl apply -f k8s/advanced-monitoring.yaml

# Wait for monitoring services
kubectl wait --for=condition=available deployment/prometheus -n certrats-monitoring --timeout=300s
kubectl wait --for=condition=available deployment/grafana -n certrats-monitoring --timeout=300s

# Get Grafana admin password
kubectl get secret grafana-admin-secret -n certrats-monitoring -o jsonpath='{.data.password}' | base64 -d
```

## DNS and SSL Configuration

### Step 6: Configure DNS and SSL

```bash
# Get load balancer IP/hostname
kubectl get service nginx-service -n certrats

# Update DNS records
# A record: certrats.com -> LOAD_BALANCER_IP
# CNAME: www.certrats.com -> certrats.com
# CNAME: api.certrats.com -> certrats.com

# Install cert-manager for SSL
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml

# Deploy SSL certificates
kubectl apply -f k8s/ssl-certificates.yaml

# Verify SSL certificate
kubectl get certificate -n certrats
```

## Validation and Testing

### Step 7: Run Comprehensive Validation

```bash
# Execute go-live checklist
./scripts/go-live-checklist.sh

# Run load tests
k6 run scripts/load-testing.js

# Validate application functionality
curl https://certrats.com/health
curl https://api.certrats.com/api/v1/health

# Test API endpoints
curl -X GET https://api.certrats.com/api/v1/certifications
curl -X POST https://api.certrats.com/api/v1/career-path \
  -H "Content-Type: application/json" \
  -d '{"current_role": "IT Support", "target_role": "Security Analyst"}'
```

## Post-Deployment Tasks

### Step 8: Performance Optimization

```bash
# Run performance optimization
python scripts/performance-optimization.py --once

# Analyze performance metrics
kubectl top nodes
kubectl top pods -n certrats

# Optimize resource allocations if needed
kubectl patch deployment backend -n certrats -p '{"spec":{"template":{"spec":{"containers":[{"name":"backend","resources":{"requests":{"cpu":"500m","memory":"1Gi"}}}]}}}}'
```

### Step 9: Security Hardening

```bash
# Apply network policies
kubectl apply -f k8s/network-policies.yaml

# Apply pod security policies
kubectl apply -f k8s/pod-security-policies.yaml

# Apply RBAC configurations
kubectl apply -f k8s/rbac.yaml

# Run security validation
./scripts/security-validation.sh
```

## Monitoring Setup

### Step 10: Configure Monitoring Dashboards

```bash
# Access Grafana
kubectl port-forward service/grafana-service 3000:3000 -n certrats-monitoring

# Import dashboards (access via http://localhost:3000)
# Default credentials: admin / <password from step 5>

# Configure Prometheus data source
# URL: http://prometheus-service:9090

# Import pre-built dashboards:
# - Kubernetes Cluster Overview (ID: 7249)
# - Node Exporter Full (ID: 1860)
# - PostgreSQL Database (ID: 9628)
# - Redis Dashboard (ID: 763)
```

## Backup and Disaster Recovery

### Step 11: Setup Backup Infrastructure

```bash
# Install Velero
velero install \
  --provider aws \
  --plugins velero/velero-plugin-for-aws:v1.8.0 \
  --bucket certrats-velero-backups \
  --secret-file ./credentials-velero \
  --use-volume-snapshots=true \
  --backup-location-config region=us-east-1

# Deploy backup configurations
kubectl apply -f k8s/disaster-recovery.yaml

# Verify backup setup
velero backup-location get
velero schedule get
```

## Deployment Checklist

### Pre-Deployment
- [ ] Infrastructure provisioned and validated
- [ ] DNS records configured
- [ ] SSL certificates obtained
- [ ] Secrets and configurations prepared
- [ ] Backup storage configured
- [ ] Monitoring tools installed

### Deployment
- [ ] Core infrastructure deployed (PostgreSQL, Redis)
- [ ] Application services deployed (Backend, Frontend)
- [ ] Monitoring and observability configured
- [ ] Backup and disaster recovery setup
- [ ] Security policies applied
- [ ] Load balancer and ingress configured

### Post-Deployment
- [ ] Health checks passing
- [ ] Load tests completed successfully
- [ ] Security scans passed
- [ ] Monitoring dashboards configured
- [ ] Alerting rules tested
- [ ] Performance optimization applied
- [ ] Documentation updated
- [ ] Team training completed

## Troubleshooting

### Common Issues

#### Pod Startup Issues
```bash
# Check pod status
kubectl get pods -n certrats

# Check pod logs
kubectl logs <pod-name> -n certrats

# Describe pod for events
kubectl describe pod <pod-name> -n certrats
```

#### Database Connection Issues
```bash
# Check database pod status
kubectl get pods -l app=postgres -n certrats

# Test database connectivity
kubectl exec -it <postgres-pod> -n certrats -- psql -U certrats_user -d certrats -c "SELECT 1;"
```

#### SSL Certificate Issues
```bash
# Check certificate status
kubectl get certificate -n certrats

# Check cert-manager logs
kubectl logs -l app=cert-manager -n cert-manager
```

## Support and Escalation

### Contact Information
- **DevOps Team**: <EMAIL>
- **Security Team**: <EMAIL>
- **On-Call Engineer**: +1-XXX-XXX-XXXX
- **Emergency Escalation**: <EMAIL>

### Documentation Links
- **API Documentation**: https://api.certrats.com/docs
- **Monitoring Dashboards**: https://monitoring.certrats.com
- **Status Page**: https://status.certrats.com
- **Internal Wiki**: https://wiki.certrats.com

## Success Criteria

### Technical Metrics
- [ ] All pods running and healthy
- [ ] Response time < 2s (95th percentile)
- [ ] Error rate < 1%
- [ ] Uptime > 99.9%
- [ ] Load test passing with 100+ concurrent users

### Business Metrics
- [ ] Application accessible via production URLs
- [ ] User registration and login working
- [ ] Career path generation functional
- [ ] Certification data loading correctly

**🎉 Congratulations! CertRats is now successfully deployed to production!**
