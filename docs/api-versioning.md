# API Versioning System Documentation

## Overview

This document outlines the API versioning system used in the CertRats application. The versioning system ensures consistency between frontend and backend API routes and provides a clear path for future API evolution.

## Versioning Strategy

We use a URL-based versioning strategy with format `/api/v{n}/resource` where `n` is the API version number (e.g., `/api/v1/users/profile`).

### Benefits

- Clear distinction between API versions
- Easy to route requests to different code paths
- Frontend and backend can maintain compatibility with older versions
- Clients can explicitly choose which API version to use

## Directory Structure

```
api/
  constants.py         # API constants including versions
  route_manager.py     # Backend route management system
  app.py               # Main FastAPI application
  routes.py            # API route collection

frontend/
  src/
    constants/
      apiEndpoints.ts  # Frontend API endpoint definitions
    services/
      apiService.ts    # Centralized API access service
    hooks/
      useApi.ts        # React hook for API access
```

## Key Components

### Backend Components

#### 1. API Constants (`api/constants.py`)

Defines API version and endpoint paths. Acts as the single source of truth for API routes.

```python
API_VERSION = "v1"
API_PREFIX = f"/api/{API_VERSION}"

# Helper function to create versioned endpoints
def get_versioned_endpoint(endpoint_path, version=None):
    version = version or API_VERSION
    return f"/api/{version}/{endpoint_path.lstrip('/')}"
```

#### 2. Route Manager (`api/route_manager.py`)

Provides a centralized way to manage API routers and versioning.

```python
class RouteManager:
    def __init__(self):
        self.version = API_VERSION
        self.prefix = API_PREFIX
        self.routers = {}
        self.main_router = APIRouter()
    
    def register_router(self, router, prefix, name=None):
        # Registers routers with proper versioning
        pass
```

#### 3. Routes Configuration (`api/routes.py`)

Uses the route manager to register all API routes.

```python
# Register routers with the route manager
route_manager.register_router(auth_router, "auth", name="auth")
route_manager.register_router(dashboard_router, "", name="dashboard")
# ...
```

#### 4. Application Setup (`api/app.py`)

Includes the API routes with the proper version prefix.

```python
# Include API router with versioned prefix
app.include_router(api_router, prefix=API_PREFIX)
```

### Frontend Components

#### 1. API Endpoints (`frontend/src/constants/apiEndpoints.ts`)

Defines API endpoint constants with versioning.

```typescript
export const API_VERSION = 'v1';
export const API_PREFIX = `/api/${API_VERSION}`;

// Helper function to create versioned endpoints
export const getVersionedEndpoint = (
  endpoint: string, 
  version?: string
): string => {
  const apiVersion = version || API_VERSION;
  return `/api/${apiVersion}/${endpoint.replace(/^\/+/, '')}`;
};
```

#### 2. API Service (`frontend/src/services/apiService.ts`)

Centralized API service that handles versioning and requests.

```typescript
class ApiService {
  private apiVersion: string;
  
  public getEndpoint(path: string, version?: string): string {
    return getVersionedEndpoint(path, version || this.apiVersion);
  }
}
```

#### 3. API Hook (`frontend/src/hooks/useApi.ts`)

React hook for API access in components.

```typescript
export const useApi = (): UseApiReturn => {
  const getEndpoint = useCallback((path: string, version?: string): string => {
    return apiService.getEndpoint(path, version);
  }, []);
  
  // ...
};
```

## Version Changes

When we need to introduce a new API version:

1. Update `API_VERSION` in both `api/constants.py` and `frontend/src/constants/apiEndpoints.ts`
2. Create a new router in `api/app.py` for the new version
3. Implement the new version's routes
4. Update components to use the new API version as needed

Old versions can continue to be supported for backward compatibility.

## Example Usage

### Backend Example

```python
# Creating a versioned endpoint
endpoint = get_versioned_endpoint("users/profile", "v2")
# Result: "/api/v2/users/profile"
```

### Frontend Example

```typescript
// In a React component
const { getEndpoint } = useApi();

// Get current version endpoint
const userProfileEndpoint = getEndpoint("users/profile");
// Result: "/api/v1/users/profile"

// Get specific version endpoint
const v2UserProfileEndpoint = getEndpoint("users/profile", "v2");
// Result: "/api/v2/users/profile"
```

## Best Practices

1. Always use the versioning constants and utilities when defining or referencing endpoints
2. Keep the endpoint definitions in sync between frontend and backend
3. When introducing breaking changes, create a new API version
4. Document each API version's features and changes
5. Consider deprecation strategy for older API versions 