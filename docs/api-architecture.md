# API Architecture Documentation

## Overview

The CertRats application implements a versioned API architecture that follows RESTful principles. This document outlines the architecture, versioning strategy, and key components of the API system.

## API Versioning Strategy

All API endpoints are versioned using a URL-based versioning strategy, following the pattern `/api/v{n}/resource` where `n` is the API version number. Currently, the system uses `v1` as the base version (e.g., `/api/v1/users/profile`).

### Benefits of Versioning

- **Backward Compatibility**: Allows the API to evolve without breaking existing clients
- **Clear Contract**: Explicit versioning creates a clear contract with API consumers
- **Independent Evolution**: Enables new features in newer versions while maintaining legacy endpoints
- **Client Control**: Clients can explicitly choose which API version to use

## Architecture Diagram

```mermaid
flowchart TB
    subgraph Client ["Client Layer"]
        ReactApp[React Application]
        ApiService[API Service]
        ApiHooks[API Hooks]
    end

    subgraph Server ["Server Layer"]
        FastAPI[FastAPI Application]
        RouteManager[Route Manager]
        Versioning[API Versioning]
        
        subgraph Endpoints ["API Endpoints"]
            direction TB
            AuthEndpoints[Authentication]
            UserEndpoints[User Management]
            StudyEndpoints[Study Tools]
            CertEndpoints[Certifications]
            AdminEndpoints[Administration]
        end
        
        subgraph Database ["Database Layer"]
            PostgreSQL[(PostgreSQL)]
            SQLAlchemy[SQLAlchemy ORM]
        end
    end

    ReactApp --> ApiHooks
    ApiHooks --> ApiService
    ApiService --> FastAPI
    
    FastAPI --> RouteManager
    RouteManager --> Versioning
    Versioning --> Endpoints
    
    Endpoints --> SQLAlchemy
    SQLAlchemy --> PostgreSQL
    
    class StudyEndpoints highlight
```

## Component Details

### Client-Side Components

1. **React Application**: The frontend application built with React and TypeScript
2. **API Service**: Centralized service that handles API requests, authentication, and error handling
3. **API Hooks**: React hooks that provide components with access to API functionality

### Server-Side Components

1. **FastAPI Application**: The main application server built with FastAPI
2. **Route Manager**: Manages routing for API endpoints with versioning
3. **API Versioning**: Handles API versioning and request routing to appropriate endpoints
4. **API Endpoints**: Individual endpoint implementations organized by functionality
5. **SQLAlchemy ORM**: Object-Relational Mapping layer for database interactions
6. **PostgreSQL**: Persistent database storage

## Request Flow

```mermaid
sequenceDiagram
    participant Client as React Client
    participant ApiService as API Service
    participant FastAPI as FastAPI
    participant RouteManager as Route Manager
    participant Endpoint as API Endpoint
    participant SQLAlchemy as ORM Layer
    participant DB as PostgreSQL

    Client->>ApiService: Make API request
    ApiService->>ApiService: Add auth token & headers
    ApiService->>FastAPI: HTTP request with version
    FastAPI->>RouteManager: Route to correct version
    RouteManager->>Endpoint: Process request
    
    alt Database Operation Needed
        Endpoint->>SQLAlchemy: Query data
        SQLAlchemy->>DB: SQL query
        DB->>SQLAlchemy: Result set
        SQLAlchemy->>Endpoint: ORM objects
    end
    
    Endpoint->>RouteManager: Response data
    RouteManager->>FastAPI: Formatted response
    FastAPI->>ApiService: HTTP response
    ApiService->>ApiService: Handle errors & parse data
    ApiService->>Client: Typed response data
```

## API Endpoint Groups

### Authentication Endpoints

```mermaid
flowchart LR
    Auth[Authentication] --> Login[POST /api/v1/auth/login]
    Auth --> Logout[POST /api/v1/auth/logout]
    Auth --> Me[GET /api/v1/auth/me]
```

### User Management Endpoints

```mermaid
flowchart LR
    User[User Management] --> Profile[GET/PUT /api/v1/users/profile]
    User --> Preferences[GET/PUT /api/v1/users/preferences]
    User --> Certifications[GET/POST/DELETE /api/v1/users/certifications]
    User --> Tutorial[GET/PUT /api/v1/users/tutorial]
```

### Study Endpoints

```mermaid
flowchart LR
    Study[Study Tools] --> Estimate[POST /api/v1/study/estimate]
    Study --> Progress[GET/POST /api/v1/study/progress]
    Study --> ROI[GET /api/v1/study/roi/{id}]
```

## Study Endpoints Implementation

The study endpoints provide tools for users to plan their certification studies, track progress, and calculate potential return on investment.

### 1. Study Estimate Endpoint

**Endpoint**: `POST /api/v1/study/estimate`

**Purpose**: Estimates the time required to complete a certification based on difficulty level, user experience, and available study hours per week.

**Request Flow**:

```mermaid
flowchart TD
    A[Client Request] --> B[Validate Input]
    B --> C[Get Certification Details]
    C --> D[Calculate Total Hours]
    D --> E[Adjust for Experience Level]
    E --> F[Calculate Weeks Required]
    F --> G[Generate Weekly Schedule]
    G --> H[Create Focus Areas]
    H --> I[Add Milestones]
    I --> J[Generate Study Tips]
    J --> K[Return Response]
```

### 2. Study Progress Endpoint

**Endpoint**: `GET/POST /api/v1/study/progress`

**Purpose**: Tracks user progress through certifications, allowing updates to progress percentage, custom hours, and study notes.

**Data Model**:

```mermaid
erDiagram
    User ||--o{ UserCertification : has
    Certification ||--o{ UserCertification : contains
    
    User {
        int id
        string username
        string email
    }
    
    Certification {
        int id
        string name
        int difficulty
        int custom_hours
    }
    
    UserCertification {
        int id
        int user_id
        int certification_id
        int progress
        date target_date
        string study_notes
    }
```

### 3. ROI Calculation Endpoint

**Endpoint**: `GET /api/v1/study/roi/{certification_id}`

**Purpose**: Calculates the potential return on investment for obtaining a certification, including break-even time and 5-year value.

**Calculation Flow**:

```mermaid
flowchart TD
    A[Get Certification Details] --> B[Calculate Costs]
    B --> C[Estimate Time Investment]
    C --> D[Calculate Opportunity Cost]
    D --> E[Determine Total Investment]
    E --> F[Estimate Salary Increase]
    F --> G[Calculate ROI Percentage]
    G --> H[Calculate Break-even Months]
    H --> I[Calculate 5-year Value]
    I --> J[Return ROI Metrics]
```

## API Versioning Implementation

The API versioning system is implemented through several components:

### Server-Side Implementation

1. **Constants Module**: Defines API versions and endpoint paths
2. **Route Manager**: Centralizes route registration with versioning
3. **Application Setup**: Includes versioned routers with appropriate prefixes

### Client-Side Implementation

1. **API Constants**: Defines versioned endpoint paths
2. **API Service**: Handles version-specific requests
3. **API Hooks**: Provides components with access to versioned endpoints

## Future API Evolution

When a new API version is needed:

1. Define new version constants (e.g., `v2`)
2. Create new endpoint implementations for the new version
3. Register new version routers with the route manager
4. Update frontend to support multiple versions

This versioning strategy ensures smooth transitions between API versions without breaking existing clients. 