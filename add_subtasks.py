#!/usr/bin/env python3
import json
from datetime import datetime

# Read existing tasks
with open('tasks.json', 'r') as f:
    tasks_data = json.load(f)

# Read phase 1 subtasks
with open('T12_1_subtasks.json', 'r') as f:
    phase1_data = json.load(f)

# Read phase 2 subtasks
with open('T12_2_subtasks.json', 'r') as f:
    phase2_data = json.load(f)

# Create phase 3 subtasks
phase3_subtasks = [
    {
        "id": "T12.3.1",
        "title": "Create user-specific features (favoriting, tracking)",
        "description": "Implement user-centric features allowing users to save favorite certifications and track their progress.",
        "status": "pending"
    },
    {
        "id": "T12.3.2",
        "title": "Implement personalized recommendations",
        "description": "Add a recommendation engine that suggests certifications based on user profile, interests, and career goals.",
        "status": "pending"
    },
    {
        "id": "T12.3.3",
        "title": "Add analytics tracking",
        "description": "Implement comprehensive analytics tracking for user interactions with the certification explorer.",
        "status": "pending"
    },
    {
        "id": "T12.3.4",
        "title": "Optimize performance and accessibility",
        "description": "Enhance performance through code optimization and ensure WCAG 2.1 AA compliance for all components.",
        "status": "pending"
    },
    {
        "id": "T12.3.5",
        "title": "Complete comprehensive testing",
        "description": "Implement unit, integration, and E2E tests for all certification page functionality.",
        "status": "pending"
    }
]

# Find task T12 and its subtasks
for task in tasks_data['tasks']:
    if task['id'] == 'T12':
        # Find Phase 1 subtask
        for subtask in task['subtasks']:
            if subtask['id'] == 'T12.1':
                subtask['subtasks'] = phase1_data['subtasks']
            elif subtask['id'] == 'T12.2':
                subtask['subtasks'] = phase2_data['subtasks']
            elif subtask['id'] == 'T12.3':
                subtask['subtasks'] = phase3_subtasks

# Update the timestamp
tasks_data['updated_at'] = datetime.now().isoformat()

# Write back to tasks.json
with open('tasks.json', 'w') as f:
    json.dump(tasks_data, f, indent=2)

print('Subtasks added successfully!') 