# CertRats Technical Architecture & API Specification

## 🏗️ **SYSTEM ARCHITECTURE**

### **High-Level Architecture**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   External      │
│   (Next.js)     │    │   (FastAPI)     │    │   Services      │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • React Pages   │◄──►│ • API Routes    │◄──►│ • Claude API    │
│ • Components    │    │ • Business Logic│    │ • PostgreSQL    │
│ • State Mgmt    │    │ • Data Models   │    │ • Redis Cache   │
│ • API Calls     │    │ • Auth & Security│   │ • Monitoring    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **Detailed Component Architecture**
```
Frontend Layer (Next.js 14)
├── App Router (/src/app/)
│   ├── page.tsx (Landing page)
│   ├── career-path/page.tsx (AI career generator)
│   ├── certifications/page.tsx (Certification explorer)
│   └── api/v1/ (API proxy routes)
├── Components (/src/components/)
│   ├── UI Components (forms, cards, modals)
│   ├── Layout Components (header, footer, nav)
│   └── Business Components (career path display)
└── Libraries (/src/lib/)
    ├── API client utilities
    ├── Type definitions
    └── Helper functions

Backend Layer (FastAPI)
├── API Layer (/api/)
│   ├── app.py (FastAPI application)
│   ├── routes.py (Main route definitions)
│   ├── endpoints/ (Modular route handlers)
│   └── deps.py (Dependency injection)
├── Business Logic (/utils/)
│   ├── claude_assistant.py (AI integration)
│   ├── certification_utils.py (Cert processing)
│   ├── job_management.py (Job search logic)
│   └── user_profile_manager.py (User operations)
├── Data Layer (/models/)
│   ├── SQLAlchemy models
│   ├── Pydantic schemas
│   └── Database relationships
└── Infrastructure
    ├── Database connections
    ├── Caching layer
    └── External API clients

Data Layer
├── PostgreSQL (Production)
│   ├── Users table
│   ├── Certifications table
│   ├── Career paths table
│   └── Jobs table
├── SQLite (Development)
└── Redis (Caching)
    ├── Session storage
    ├── API response caching
    └── Rate limiting
```

## 🔌 **COMPLETE API SPECIFICATION**

### **Base Configuration**
- **Base URL**: `http://localhost:8000/api/v1` (development)
- **Production URL**: `https://api.certrats.com/api/v1`
- **Authentication**: JWT Bearer tokens (planned)
- **Content-Type**: `application/json`
- **API Version**: `v1`

### **Core Endpoints**

#### **1. Career Path Generation**
```http
POST /api/v1/career-path
Content-Type: application/json

Request Body:
{
  "completed_certifications": ["Security+", "Network+"],
  "interests": ["Network Security", "Cloud Security", "Penetration Testing"],
  "years_experience": 3,
  "current_role": "Security Analyst",
  "target_role": "Security Engineer", 
  "learning_style": "Mixed",  // "Visual" | "Hands-on" | "Reading" | "Mixed"
  "study_hours": 10
}

Response (200 OK):
{
  "generated_at": "2024-01-01T00:00:00Z",
  "request_id": "api-request",
  "api_version": "v1",
  "user_profile": {
    "years_experience": 3,
    "current_role": "Security Analyst",
    "learning_style": "Mixed",
    "study_hours": 10
  },
  "career_path": [
    {
      "stage": "Foundation",
      "timeline": "3-6 months",
      "certifications": ["CISSP Associate"],
      "certification_costs": {
        "CISSP Associate": {
          "exam": 749.0,
          "materials": 200.0,
          "training": 500.0
        }
      }
    },
    {
      "stage": "Core",
      "timeline": "6-12 months", 
      "certifications": ["CISSP", "CCSP"],
      "certification_costs": {
        "CISSP": {
          "exam": 749.0,
          "materials": 300.0,
          "training": 800.0
        }
      }
    }
  ],
  "alignment_analysis": {
    "current_role_alignment": 75,
    "target_role_alignment": 85,
    "gap_analysis": "Strong foundation in security fundamentals..."
  }
}

Error Responses:
400 Bad Request - Invalid input parameters
500 Internal Server Error - AI generation failed
503 Service Unavailable - Database or AI service unavailable
```

#### **2. Health Check**
```http
GET /api/v1/health

Response (200 OK):
{
  "status": "healthy",
  "database": "connected",
  "anthropic_client": "ready",
  "timestamp": "2024-01-01T00:00:00Z",
  "api_version": "v1"
}

Response (503 Service Unavailable):
{
  "status": "unhealthy",
  "database": "disconnected",
  "anthropic_client": "not_ready",
  "error": "Database connection failed"
}
```

#### **3. Job Search**
```http
GET /api/v1/jobs/search?term=security&domain=All&page=0&per_page=10

Query Parameters:
- term: string (search term)
- domain: string (job domain filter)
- page: integer (pagination page)
- per_page: integer (results per page)

Response (200 OK):
{
  "jobs": [
    {
      "id": 1,
      "title": "Security Engineer",
      "company": "TechCorp",
      "location": "Remote",
      "salary_range": "$80,000 - $120,000",
      "required_certifications": ["Security+", "CISSP"],
      "description": "...",
      "posted_date": "2024-01-01"
    }
  ],
  "total": 150,
  "page": 0,
  "per_page": 10,
  "api_version": "v1"
}
```

### **Extended API Endpoints (Planned)**

#### **Authentication Endpoints**
```http
POST /api/v1/auth/register
POST /api/v1/auth/login
POST /api/v1/auth/logout
GET  /api/v1/auth/me
POST /api/v1/auth/refresh
```

#### **User Management**
```http
GET  /api/v1/users/profile
PUT  /api/v1/users/profile
GET  /api/v1/users/certifications
POST /api/v1/users/certifications
DELETE /api/v1/users/certifications/{id}
```

#### **Certification Management**
```http
GET  /api/v1/certifications
GET  /api/v1/certifications/{id}
GET  /api/v1/certifications/providers
GET  /api/v1/certifications/search?q={query}
POST /api/v1/certifications/compare
```

#### **Study Planning**
```http
GET  /api/v1/study/estimate
POST /api/v1/study/progress
GET  /api/v1/study/roi
GET  /api/v1/study/schedule
```

## 🗄️ **DATABASE SCHEMA**

### **Core Tables**

#### **Users Table**
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    current_role VARCHAR(255),
    target_role VARCHAR(255),
    years_experience INTEGER DEFAULT 0,
    learning_style VARCHAR(50) DEFAULT 'Mixed',
    study_hours_per_week INTEGER DEFAULT 10,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **Certifications Table**
```sql
CREATE TABLE certifications (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    provider VARCHAR(255) NOT NULL,
    description TEXT,
    cost DECIMAL(10,2),
    level VARCHAR(50), -- 'beginner', 'intermediate', 'advanced'
    domain VARCHAR(100), -- 'network', 'cloud', 'application', etc.
    prerequisites TEXT[], -- Array of prerequisite names
    skills_covered TEXT[], -- Array of skills
    url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **Career Paths Table**
```sql
CREATE TABLE career_paths (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    stages JSONB, -- 4-stage career progression
    total_cost DECIMAL(10,2),
    total_timeline VARCHAR(100),
    alignment_score INTEGER,
    request_data JSONB -- Original request parameters
);
```

#### **User Certifications Table**
```sql
CREATE TABLE user_certifications (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    certification_id INTEGER REFERENCES certifications(id),
    status VARCHAR(50), -- 'planned', 'in_progress', 'completed'
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **Indexes and Performance**
```sql
-- Performance indexes
CREATE INDEX idx_certifications_provider ON certifications(provider);
CREATE INDEX idx_certifications_level ON certifications(level);
CREATE INDEX idx_certifications_domain ON certifications(domain);
CREATE INDEX idx_career_paths_user_id ON career_paths(user_id);
CREATE INDEX idx_user_certifications_user_id ON user_certifications(user_id);

-- Full-text search
CREATE INDEX idx_certifications_search ON certifications 
USING gin(to_tsvector('english', name || ' ' || description));
```

## 🔧 **TECHNOLOGY STACK**

### **Frontend Technologies**
- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript 5.0+
- **Styling**: Tailwind CSS 3.0
- **UI Components**: Radix UI primitives
- **State Management**: React hooks + Context API
- **Forms**: React Hook Form + Zod validation
- **HTTP Client**: Fetch API with custom wrapper
- **Testing**: Jest + React Testing Library + Playwright

### **Backend Technologies**
- **Framework**: FastAPI 0.104+
- **Language**: Python 3.11+
- **Database ORM**: SQLAlchemy 2.0
- **Migrations**: Alembic
- **Validation**: Pydantic v2
- **Authentication**: JWT (planned)
- **AI Integration**: Anthropic Claude API
- **Testing**: pytest + httpx
- **Code Quality**: Black, mypy, flake8

### **Infrastructure Technologies**
- **Containerization**: Docker + Docker Compose
- **Orchestration**: Kubernetes 1.28+
- **Database**: PostgreSQL 15 + PgBouncer
- **Caching**: Redis 7 cluster
- **Load Balancer**: Nginx
- **Monitoring**: Prometheus + Grafana + Jaeger
- **CI/CD**: GitHub Actions
- **Infrastructure as Code**: Terraform + Helm

## 🔄 **DATA FLOW**

### **Career Path Generation Flow**
```
1. User Input (Frontend)
   ├── Form validation (React Hook Form + Zod)
   ├── API call to /api/v1/career-path
   └── Loading state management

2. API Proxy (Next.js)
   ├── Receive request from frontend
   ├── Forward to FastAPI backend
   └── Return response to frontend

3. Backend Processing (FastAPI)
   ├── Request validation (Pydantic)
   ├── Database query for certifications
   ├── AI processing (Claude API)
   ├── Cost calculation and analysis
   └── Response formatting

4. AI Integration (Claude API)
   ├── Context preparation
   ├── Prompt engineering
   ├── API call to Anthropic
   └── Response parsing

5. Response (Frontend)
   ├── Data processing and state update
   ├── UI rendering with results
   └── Error handling and user feedback
```

### **Database Query Patterns**
```python
# Certification retrieval with ORM
certifications = db.query(CertificationExplorer).all()

# Parameterized queries for security
db.execute(text("SELECT 1 WHERE :param = :param"), {"param": 1})

# Job search with filtering
jobs = db.query(Job).filter(
    Job.title.ilike(f"%{search_term}%")
).offset(page * per_page).limit(per_page).all()
```

## 🔒 **SECURITY ARCHITECTURE**

### **Application Security**
- **Input Validation**: Pydantic models with strict validation
- **SQL Injection Protection**: Parameterized queries only
- **XSS Prevention**: Content Security Policy headers
- **CSRF Protection**: SameSite cookies and CSRF tokens
- **Rate Limiting**: Redis-based rate limiting
- **Authentication**: JWT with secure storage (planned)

### **Infrastructure Security**
- **Network Policies**: Kubernetes network segmentation
- **Container Security**: Non-root containers, image scanning
- **Secrets Management**: Kubernetes secrets with encryption
- **SSL/TLS**: End-to-end encryption
- **WAF Protection**: Web Application Firewall
- **Monitoring**: Security event logging and alerting

## 📊 **MONITORING & OBSERVABILITY**

### **Metrics Collection**
- **Application Metrics**: Response times, error rates, throughput
- **Infrastructure Metrics**: CPU, memory, disk, network
- **Business Metrics**: User engagement, conversion rates
- **Custom Metrics**: AI generation success rates, cost analysis

### **Logging Strategy**
- **Structured Logging**: JSON format with correlation IDs
- **Log Levels**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **Log Aggregation**: Centralized logging with ELK stack
- **Log Retention**: 30 days for application logs, 90 days for audit logs

### **Alerting Rules**
- **Critical**: Service down, database unavailable
- **Warning**: High error rate, slow response times
- **Info**: Deployment notifications, scaling events

This technical architecture provides a comprehensive foundation for understanding and extending the CertRats platform. The modular design allows for easy scaling and feature additions while maintaining security and performance standards.
