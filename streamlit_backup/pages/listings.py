"""
Listings page for detailed certification information.

This module provides functionality to display and filter security certifications
based on various criteria such as experience level, domain, and cost.
"""
from typing import Dict, List, Optional, Set
import streamlit as st
from components.visualization import display_certification_details
from models.certification import Certification
from sqlalchemy.orm import Session

def get_certifications(db: Session, filters: Optional[Dict] = None) -> List[Certification]:
    """Get certifications with optional filters.

    Args:
        db: SQLAlchemy database session
        filters: Optional dictionary containing filter criteria:
            - level: Experience level filter
            - domains: List of domain filters
            - max_cost: Maximum cost filter

    Returns:
        List of Certification objects matching the filter criteria
    """
    query = db.query(Certification)

    if filters:
        if filters.get('level'):
            query = query.filter(Certification.level == filters['level'])
        if filters.get('domains'):
            query = query.filter(Certification.domain.in_(filters['domains']))
        if filters.get('max_cost') is not None:
            query = query.filter(Certification.cost <= filters['max_cost'])

    return query.all()

def run(get_certifications: callable, get_db: callable) -> None:
    """Execute the listings page functionality.

    This function displays the certifications listing page with filtering options
    and detailed information about each certification.

    Args:
        get_certifications: Function to retrieve certifications from database
        get_db: Function to get database session
    """
    st.title("📋 Certification Listings")
    st.markdown("""
    Browse and filter security certifications based on your experience level, 
    interests, and budget. Detailed information about each certification is 
    provided below.

    🔍 **Use the filters in the sidebar to:**
    - Select your experience level 🎓
    - Choose specific domains 🎯
    - Set your budget range 💰
    """)

    # Get database session
    db = next(get_db())

    # Get unique values for filters
    all_certs = get_certifications(db)
    levels = sorted(set(cert.level for cert in all_certs))
    domains = sorted(set(cert.domain for cert in all_certs))
    costs = [cert.cost for cert in all_certs if cert.cost is not None]
    max_cost = max(costs) if costs else 5000

    # Initialize session state for selected domains
    if 'selected_domains' not in st.session_state:
        st.session_state.selected_domains: Set[str] = set()

    # Filter inputs
    st.sidebar.subheader("🔍 Filters")
    selected_level = st.sidebar.selectbox(
        "🎓 Experience Level",
        ["All"] + levels
    )

    # Domain filter buttons
    st.sidebar.subheader("🎯 Domains")
    cols = st.sidebar.columns(2)
    for idx, domain in enumerate(domains):
        col = cols[idx % 2]
        if col.button(
            domain,
            type="secondary" if domain in st.session_state.selected_domains else "primary",
            key=f"domain_{domain}"
        ):
            if domain in st.session_state.selected_domains:
                st.session_state.selected_domains.remove(domain)
            else:
                st.session_state.selected_domains.add(domain)
            st.rerun()

    # Show selected domains
    if st.session_state.selected_domains:
        st.sidebar.caption(
            "🎯 Selected domains: " + 
            ", ".join(sorted(st.session_state.selected_domains))
        )

    max_cost_filter = st.sidebar.slider(
        "💰 Maximum Cost ($)", 
        min_value=0,
        max_value=int(max_cost),
        value=int(max_cost)
    )

    # Apply filters
    filters: Dict = {}
    if selected_level != "All":
        filters['level'] = selected_level
    if st.session_state.selected_domains:
        filters['domains'] = list(st.session_state.selected_domains)
    filters['max_cost'] = max_cost_filter

    filtered_certs = get_certifications(db, filters)

    # Display results count
    st.write(f"🔍 Found {len(filtered_certs)} certifications matching your criteria")

    # Display detailed certification information
    for cert in filtered_certs:
        with st.expander(f"🔐 {cert.name} - {cert.domain} ({cert.level})"):
            display_certification_details(cert.name, {
                'level': cert.level,
                'domain': cert.domain,
                'difficulty': cert.difficulty,
                'cost': cert.cost,
                'description': cert.description,
                'prerequisites': cert.prerequisites,
                'issuing_organization': cert.organization.name if cert.organization else "Unknown",
                'url': cert.url
            })

if __name__ == "__main__":
    from database import get_db
    run(get_certifications, get_db)