"""
Visualization page for certification paths.

This module provides interactive visualization functionality for certification paths,
including skill radar charts and relationship graphs.
"""
from typing import List, Set, Callable, Dict, Any
import streamlit as st
from sqlalchemy.orm import Session
from components.visualization import display_certification_visualizations
from components.skill_radar import display_skill_radar
from components.study_progress_sunburst import display_progress_sunburst
from models.certification import Certification
import logging
from sqlalchemy.exc import SQLAlchemyError

# Configure logging
logger = logging.getLogger(__name__)

def run(
    get_certifications: Callable[[Session, Dict[str, Any]], List[Certification]], 
    get_db: Callable[[], Session]
) -> None:
    """Execute the visualization page functionality."""
    try:
        st.title("📊 Certification Path Visualization")
        st.markdown("""
        Explore certification paths and relationships through interactive 
        visualizations. Track your progress and see how your skills develop across 
        different security domains.

        🎯 **Tips:**
        - Drag nodes to arrange the certification graph
        - Use mouse wheel to zoom in/out
        - Click and drag the background to pan
        - Hover over nodes for details
        """)

        # Get database session with error handling
        try:
            db = next(get_db())
            logger.info("Successfully connected to database")
        except Exception as e:
            logger.error(f"Failed to connect to database: {str(e)}")
            st.error("Unable to connect to the database. Please try again later.")
            return

        try:
            # Get certifications with explicit error handling
            logger.info("Fetching certifications from database")
            all_certs = get_certifications(db, {})

            if not all_certs:
                st.info("No certification data available. Please check back later.")
                return

            # Get unique domains for filtering
            domains = sorted(set(cert.domain for cert in all_certs))
            logger.info(f"Found {len(domains)} unique domains")

            # Initialize session state
            if 'selected_domains' not in st.session_state:
                st.session_state.selected_domains = set()
            if 'completed_certs' not in st.session_state:
                st.session_state.completed_certs = set()

            # Domain selection
            st.sidebar.subheader("🎯 Select Domain")
            for domain in domains:
                if st.sidebar.button(
                    domain,
                    type="secondary" if domain in st.session_state.selected_domains else "primary",
                    key=f"domain_{domain}"
                ):
                    if domain in st.session_state.selected_domains:
                        st.session_state.selected_domains.remove(domain)
                    else:
                        st.session_state.selected_domains = {domain}
                    st.rerun()

            # Apply domain filter
            filters = {}
            if st.session_state.selected_domains:
                filters['domains'] = list(st.session_state.selected_domains)

            filtered_certs = get_certifications(db, filters)

            if filtered_certs:
                # Show skill radar chart
                st.subheader("🎯 Skills Overview")
                try:
                    display_skill_radar(filtered_certs, {
                        cert.id for cert in filtered_certs 
                        if cert.name in st.session_state.completed_certs
                    })
                except Exception as e:
                    logger.error(f"Error displaying skill radar: {str(e)}")
                    st.warning("Unable to display skills overview")

                # Show certification paths
                st.subheader("🔗 Certification Paths")
                selected_domain = (
                    list(st.session_state.selected_domains)[0] 
                    if st.session_state.selected_domains 
                    else None
                )

                try:
                    cert_data = {
                        cert.name: {
                            'level': cert.level,
                            'domain': cert.domain,
                            'difficulty': cert.difficulty,
                            'cost': cert.cost,
                            'description': cert.description,
                            'prerequisites': cert.prerequisites,
                            'issuing_organization': (
                                cert.organization.name if cert.organization else "Unknown"
                            ),
                            'url': cert.url,
                            'related_certs': {}
                        } for cert in filtered_certs
                    }

                    display_certification_visualizations(cert_data, selected_domain)
                except Exception as e:
                    logger.error(f"Error displaying certification paths: {str(e)}")
                    st.error("Unable to display certification paths")

            else:
                st.info("🎯 Select a domain to view available certifications")

        except SQLAlchemyError as e:
            logger.error(f"Database error: {str(e)}")
            st.error("Failed to fetch certification data. Please try again later.")
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            st.error("An unexpected error occurred. Please try again later.")
        finally:
            db.close()

    except Exception as e:
        logger.error(f"Critical error in visualization page: {str(e)}")
        st.error("We encountered an issue with the visualization. Please try again later.")