"""OAuth callback handler for Streamlit application.

This module handles the OAuth callback process for various authentication providers,
managing user sessions and authentication state in a secure manner.
"""
from typing import Optional, Dict, Any
import streamlit as st
from utils.oauth import OAuthHandler
import logging
import traceback
import json
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

def validate_state(state: str, provider: str) -> bool:
    """Validate the OAuth state parameter.

    Args:
        state: The state parameter from the OAuth callback
        provider: The authentication provider identifier

    Returns:
        bool: True if state is valid, False otherwise
    """
    try:
        if not hasattr(st.session_state, 'oauth_states'):
            logger.error("No OAuth states found in session")
            return False

        states = st.session_state.oauth_states
        if not isinstance(states, dict):
            logger.error(f"Invalid oauth_states format: {type(states)}")
            return False

        provider_state = states.get(provider)
        if not provider_state:
            logger.error(f"No state found for provider {provider}")
            return False

        stored_state = provider_state.get('state')
        expires_at = provider_state.get('expires_at')

        if not stored_state or not expires_at:
            logger.error("Missing state or expiration data")
            return False

        # Check if state matches and hasn't expired
        is_valid = (
            stored_state == state and 
            datetime.fromtimestamp(expires_at) > datetime.now()
        )

        if not is_valid:
            logger.error(
                f"State validation failed: "
                f"Stored={stored_state}, "
                f"Received={state}, "
                f"Expires={datetime.fromtimestamp(expires_at)}"
            )

        return is_valid

    except Exception as e:
        logger.error(f"State validation error: {str(e)}")
        return False

def handle_auth_callback(
    code: Optional[str],
    state: Optional[str],
    provider: str
) -> Dict[str, Any]:
    """Process OAuth callback and retrieve user information.

    Args:
        code: OAuth authorization code
        state: OAuth state parameter for security validation
        provider: Authentication provider identifier

    Returns:
        Dictionary containing user information or error details
    """
    try:
        if not code or not state:
            logger.error("Missing required parameters")
            return {"error": "Missing authentication parameters"}

        # Validate state parameter
        if not validate_state(state, provider):
            logger.error(f"Invalid state parameter: {state}")
            return {"error": "Invalid or expired authentication session"}

        user_info = OAuthHandler.get_user_info(provider, code, state)
        if not user_info:
            logger.error("Failed to get user information")
            return {"error": "Failed to get user information"}

        # Clear the used state after successful authentication
        if hasattr(st.session_state, 'oauth_states'):
            provider_states = st.session_state.oauth_states
            if provider in provider_states:
                del provider_states[provider]

        return {"success": True, "user_info": user_info}

    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        logger.error(f"Stacktrace: {traceback.format_exc()}")
        return {"error": f"Authentication failed: {str(e)}"}

def main() -> None:
    """Handle OAuth callback and manage authentication state.

    This function processes the OAuth callback, updates the session state,
    and manages the user interface for the authentication process.
    """
    st.set_page_config(
        page_title="Authentication Callback",
        page_icon="🔐",
        layout="centered"
    )

    # Hide the callback page from navigation
    st.markdown("""
        <style>
            [data-testid="stSidebar"] {display: none}
        </style>
    """, unsafe_allow_html=True)

    # Get query parameters
    code = st.query_params.get('code')
    state = st.query_params.get('state')
    provider = st.query_params.get('provider', 'github')

    try:
        # Log initial state of the callback
        logger.info("=== OAuth Callback Started ===")
        logger.info(f"Provider: {provider}")
        logger.info(f"State received: {state}")
        logger.info(f"Code received: {code is not None}")
        logger.info(f"Current session state: {json.dumps(dict(st.session_state))}")

        result = handle_auth_callback(code, state, provider)

        if "error" not in result:
            st.session_state.user = result["user_info"]
            logger.info("Successfully authenticated user")
            logger.info(f"Final session state: {json.dumps(dict(st.session_state))}")

            st.success('Successfully authenticated!')
            st.markdown("""
                <meta http-equiv="refresh" content="2;url=/" />
                """, unsafe_allow_html=True)
            st.write("Redirecting back to homepage...")
        else:
            st.error(result["error"])
            st.button("Try Again", on_click=lambda: st.query_params.clear())

    except Exception as e:
        logger.error("=== OAuth Callback Error ===")
        logger.error(f"Error: {str(e)}")
        logger.error(f"Stacktrace: {traceback.format_exc()}")
        logger.error(f"Final session state: {json.dumps(dict(st.session_state))}")

        st.error('Authentication failed. Please try again.')
        st.button("Try Again", on_click=lambda: st.query_params.clear())

if __name__ == "__main__":
    main()