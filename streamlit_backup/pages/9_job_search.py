"""
Job search page with fast title search and domain information
"""
import streamlit as st
from typing import List, <PERSON><PERSON>
from database import get_db
from models.job import <PERSON>Job
from sqlalchemy.orm import Session
from sqlalchemy import func

# Set page configuration
st.set_page_config(
    page_title="Job Search",
    page_icon="🔍",
    layout="wide"
)

@st.cache_data(ttl=3600)  # Cache for 1 hour
def load_job_titles(_db: Session) -> List[dict]:
    """Load all job titles and their domains from the database"""
    try:
        # Query to get unique titles with aggregated domains
        results = (_db.query(
                SecurityJob.title,
                func.string_agg(SecurityJob.domain, '; ').label('domains')
            )
            .filter(SecurityJob.is_deleted.is_(False))
            .group_by(SecurityJob.title)
            .all())

        return [{"title": r.title, "domains": r.domains.split('; ')} for r in results]
    except Exception as e:
        st.error(f"Error loading job titles: {str(e)}")
        return []

def format_title_with_domains(title: str, domains: List[str]) -> str:
    """Format job title with domains if multiple exist"""
    if len(domains) > 1:
        return f"{title} ({', '.join(domains)})"
    return title

def main():
    st.title("🔍 Security Job Search")

    # Get database session
    db = next(get_db())

    # Load job titles for search
    job_data = load_job_titles(db)

    # Create a search box with autocomplete
    search_query = st.text_input(
        "Search for a security job title",
        key="job_search",
        help="Start typing to see matching job titles"
    ).lower()

    if search_query:
        # Filter job titles based on search query
        matching_jobs = [
            job for job in job_data
            if search_query in job["title"].lower()
        ]

        if matching_jobs:
            # Create radio buttons for selection with formatted titles
            formatted_titles = {
                format_title_with_domains(job["title"], job["domains"]): job
                for job in matching_jobs
            }

            selected_title = st.radio(
                "Select a job title:",
                options=list(formatted_titles.keys()),
                key="job_selection"
            )

            # Get the original job data for the selected formatted title
            selected_job = formatted_titles[selected_title]

            # Display domains
            if len(selected_job["domains"]) == 1:
                st.info(f"**Domain:** {selected_job['domains'][0]}")
            else:
                st.info("**Domains:**\n" + "\n".join(f"- {domain}" for domain in selected_job["domains"]))
        else:
            st.warning("No matching job titles found.")

if __name__ == "__main__":
    main()