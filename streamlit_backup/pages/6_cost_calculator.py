"""
Cost calculator for certification paths with currency conversion and retake analysis
"""
import streamlit as st
from database import get_db
from models.certification import Certification
import plotly.graph_objects as go
from components.icons import get_icon, icon_header
from components.cost_analysis import (
    get_exchange_rates,
    calculate_retake_costs,
    format_currency
)
from translations import _, translate_page

@translate_page
def run():
    """Run the cost calculator page"""
    st.title(f"{get_icon('money')} {_('Certification Cost Calculator')}")
    st.markdown(
        f'<div style="text-align: right; font-size: 0.8em; color: #666;">'
        f"{_('Home')} > {_('Cost Calculator')}</div>",
        unsafe_allow_html=True
    )

    st.markdown(_("""
    Calculate the total investment needed for your certification journey, including:
    - Exam fees and study materials
    - Potential retake costs
    - Currency conversion
    - Regional price variations
    """))

    # Get database session
    db = next(get_db())

    # Get all certifications
    certifications = db.query(Certification).all()
    cert_names = [cert.name for cert in certifications]

    # Create columns for the layout
    col1, col2 = st.columns([2, 1])

    with col1:
        # Select certifications
        selected_certs = st.multiselect(
            _("Select Certifications"),
            options=cert_names,
            help=_("Choose the certifications you want to include in the cost calculation")
        )

        # Additional costs
        study_materials = st.number_input(
            _("Study Materials Cost ($)"),
            min_value=0,
            value=100,
            step=50,
            help=_("Estimated cost of books, online courses, practice exams, etc.")
        )

        # Get available currencies
        exchange_rates = get_exchange_rates()
        available_currencies = list(exchange_rates["rates"].keys())

        # Currency selection with more options
        currency = st.selectbox(
            _("Select Currency"),
            available_currencies,
            help=_("Select your preferred currency")
        )

        # Retake analysis section
        st.subheader(_("📚 Exam Retake Analysis"))

        retake_policy = {
            "waiting_period": st.slider(
                _("Minimum Days Between Attempts"),
                min_value=7,
                max_value=90,
                value=14,
                help=_("Typical waiting period required between exam attempts")
            ),
            "discount": st.slider(
                _("Retake Discount (%)"),
                min_value=0,
                max_value=50,
                value=0,
                help=_("Some certifications offer discounts on retakes")
            ) / 100,
            "max_attempts": st.number_input(
                _("Maximum Attempts Considered"),
                min_value=1,
                max_value=5,
                value=2,
                help=_("Number of potential attempts to include in cost analysis")
            )
        }

        # Calculate costs
        cert_costs = []
        total_retake_costs = []
        for cert_name in selected_certs:
            cert = next((c for c in certifications if c.name == cert_name), None)
            if cert and cert.cost:
                # Calculate base and retake costs
                retake_analysis = calculate_retake_costs(
                    cert.cost,
                    retake_policy,
                    retake_policy["max_attempts"]
                )
                cert_costs.append(cert.cost)
                total_retake_costs.extend(retake_analysis["retake_fees"])

        base_cert_cost = sum(cert_costs)
        total_retake_cost = sum(total_retake_costs)
        total_cost = base_cert_cost + study_materials + total_retake_cost

        # Convert to selected currency
        rate = exchange_rates["rates"][currency]
        converted_total = total_cost * rate
        converted_base = base_cert_cost * rate
        converted_materials = study_materials * rate
        converted_retakes = total_retake_cost * rate

        # Display totals
        st.markdown("---")
        icon_header(_("Cost Breakdown"), "money")

        # Cost metrics in columns
        col1a, col1b, col1c = st.columns(3)
        with col1a:
            st.metric(
                _("Base Exam Fees"),
                format_currency(converted_base, currency)
            )
        with col1b:
            st.metric(
                _("Study Materials"),
                format_currency(converted_materials, currency)
            )
        with col1c:
            st.metric(
                _("Potential Retake Costs"),
                format_currency(converted_retakes, currency)
            )

        st.metric(
            _("Total Investment"),
            format_currency(converted_total, currency),
            help=_("Total cost including all certifications, materials, and potential retakes")
        )

    with col2:
        if selected_certs:
            # Show cost breakdown chart
            st.markdown(_("### 📊 Cost Distribution"))

            # Prepare data for pie chart
            costs = [converted_base, converted_materials, converted_retakes]
            labels = [
                _("Base Exam Fees"),
                _("Study Materials"),
                _("Potential Retakes")
            ]

            # Remove zero values
            costs_labels = [(c, l) for c, l in zip(costs, labels) if c > 0]
            if costs_labels:
                costs, labels = zip(*costs_labels)
                fig = create_cost_breakdown_chart(costs, labels)
                st.plotly_chart(fig, use_container_width=True)

            # Certification details
            st.markdown(_("### 📋 Selected Certifications"))
            for cert_name in selected_certs:
                cert = next((c for c in certifications if c.name == cert_name), None)
                if cert:
                    with st.expander(cert_name):
                        # Basic info
                        st.write(f"**{_('Base Cost')}:** {format_currency(cert.cost * rate, currency)}")
                        st.write(f"**{_('Level')}:** {cert.level}")
                        st.write(f"**{_('Domain')}:** {cert.domain}")

                        # Retake analysis
                        retake_costs = calculate_retake_costs(
                            cert.cost,
                            retake_policy,
                            retake_policy["max_attempts"]
                        )

                        st.write("#### " + _("Retake Cost Analysis"))
                        for i, retake_cost in enumerate(retake_costs["retake_fees"], 1):
                            converted_retake = retake_cost * rate
                            st.write(
                                f"- {_('Attempt')} {i+1}: "
                                f"{format_currency(converted_retake, currency)}"
                            )
        else:
            st.info(_("Select certifications to see cost breakdown"))

def create_cost_breakdown_chart(costs, labels):
    """Create a pie chart showing cost breakdown"""
    fig = go.Figure(data=[go.Pie(
        labels=labels,
        values=costs,
        hole=.3,
        marker_colors=['#7792E3', '#1e88e5', '#1565c0']
    )])

    fig.update_layout(
        showlegend=True,
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)',
        font=dict(color='white'),
        margin=dict(t=0, b=0, l=0, r=0),
        height=300
    )

    return fig

if __name__ == "__main__":
    run()