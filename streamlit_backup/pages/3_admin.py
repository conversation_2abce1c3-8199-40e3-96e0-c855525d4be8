"""
Admin interface for managing certification data and viewing feedback
"""
import streamlit as st
import os
from database import get_db
from models.certification import Certification, CertificationVersion, Organization
from models.feedback import Feedback
from models.job import SecurityJob
from data.seed import import_certifications
from components.icons import get_icon
from datetime import datetime
import subprocess
from utils.enrichment import (
    fetch_certification_data, 
    compare_certification_data, 
    format_changes, 
    extract_pdf_content,
    find_matching_certifications
)
from utils.admin import (
    require_admin, 
    admin_logout, 
    is_admin_authenticated,
    get_claude_usage_stats
)
from scripts.populate_security_jobs import SECURITY_DOMAINS, is_valid_job_title

# Set page configuration to use full width
st.set_page_config(
    page_title="Admin Dashboard",
    page_icon="🔧",
    layout="wide"
)

@require_admin
def run():
    """Main admin interface function"""
    st.title(f"{get_icon('admin')} Admin Dashboard")

    # Add logout button in top right
    col1, col2 = st.columns([10, 1])
    with col2:
        if st.button("🚪 Logout"):
            admin_logout()
            st.rerun()

    st.markdown(
        '<div style="text-align: right; font-size: 0.8em; color: #666;">Home > Admin</div>',
        unsafe_allow_html=True
    )

    # Get admin password from environment variable first, then fallback to secrets
    admin_password = os.environ.get("ADMIN_PASSWORD")
    if not admin_password:
        try:
            admin_password = st.secrets["ADMIN_PASSWORD"]
        except Exception:
            admin_password = "admin"  # Default for development

    if not st.session_state.get("authenticated"):
        with st.form("login"):
            password = st.text_input("Enter admin password", type="password")
            if st.form_submit_button(f"{get_icon('security')} Login"):
                if password == admin_password:
                    st.session_state.authenticated = True
                    st.rerun()
                else:
                    st.error(f"{get_icon('error')} Invalid password")
        return

    # Tabs for different admin sections
    tab1, tab2, tab3, tab4, tab5, tab6, tab7 = st.tabs([
        "📋 Certification Management",
        "🏢 Organizations",
        "💬 Feedback Management",
        "🌐 Translations",
        "🔄 Data Enrichment",
        "📊 API Usage",
        "👨‍💼 Job Management"
    ])

    # Get database session
    db = next(get_db())

    with tab1:
        st.markdown(f"""
        ### {get_icon('management')} Manage Certification Data
        Add, edit, or remove certification information from the database.
        Fields marked with * are required.
        """)

        # Import certifications button
        if st.button(f"{get_icon('add')} Import Certifications from Roadmap", use_container_width=True):
            try:
                success, errors = import_certifications(db)
                if success > 0:
                    st.success(f"{get_icon('success')} Successfully imported {success} certifications")
                if errors > 0:
                    st.warning(f"{get_icon('warning')} {errors} certifications failed to import")
                if success == 0 and errors == 0:
                    st.info(f"{get_icon('info')} No new certifications to import")
                st.rerun()
            except Exception as e:
                st.error(f"{get_icon('error')} Error importing certifications: {str(e)}")

        # Get organizations for dropdown
        organizations = db.query(Organization).order_by(Organization.name).all()
        org_options = [""] + [org.name for org in organizations]

        # Add new certification form with full width
        with st.expander(f"{get_icon('add')} Add New Certification", expanded=False):
            with st.form("add_certification", clear_on_submit=True):
                cols = st.columns([1, 1, 1])
                with cols[0]:
                    name = st.text_input("Certification Name *")
                    level = st.selectbox(
                        "Level *",
                        ["Entry Level", "Intermediate", "Advanced", "Expert"]
                    )
                    cost = st.number_input("Cost ($)", min_value=0, value=0)

                with cols[1]:
                    domain = st.selectbox(
                        "Domain *",
                        ["Network Security", "IAM", "Security Engineering", 
                         "Asset Security", "Security Management", "Security Testing",
                         "Software Security", "Security Operations"]
                    )
                    difficulty = st.selectbox(
                        "Difficulty *",
                        ["Low", "Medium", "High"]
                    )
                    organization_name = st.selectbox("Issuing Organization", org_options)

                with cols[2]:
                    description = st.text_area("Description", height=100)
                    prerequisites = st.text_area("Prerequisites", height=100)
                    url = st.text_input("URL")

                if st.form_submit_button(f"{get_icon('add')} Add Certification", use_container_width=True):
                    if not all([name, domain, level, difficulty]):
                        st.error(f"{get_icon('error')} Please fill in all required fields")
                    else:
                        try:
                            difficulty_map = {"Low": 1, "Medium": 2, "High": 3}

                            # Get organization if selected
                            organization_id = None
                            if organization_name:
                                org = db.query(Organization).filter_by(name=organization_name).first()
                                if org:
                                    organization_id = org.id

                            new_cert = Certification(
                                name=name,
                                domain=domain,
                                level=level,
                                difficulty=difficulty_map[difficulty],
                                cost=cost,
                                description=description,
                                prerequisites=prerequisites,
                                organization_id=organization_id,
                                url=url,
                                category="Added via Admin Interface"
                            )
                            db.add(new_cert)
                            db.commit()
                            st.success(f"{get_icon('success')} Certification added successfully!")
                            st.rerun()
                        except Exception as e:
                            st.error(f"{get_icon('error')} Error adding certification: {str(e)}")
                            db.rollback()

        # List existing certifications
        st.subheader(f"{get_icon('certification')} Existing Certifications")
        certifications = db.query(Certification).order_by(Certification.name).all()

        # Add search box for certifications
        search_term = st.text_input(
            "🔍 Search Certifications",
            help="Filter certifications by name, domain, or organization"
        )

        # Filter certifications if search term is provided
        if search_term:
            filtered_certs = [
                cert for cert in certifications 
                if search_term.lower() in cert.name.lower() 
                or search_term.lower() in cert.domain.lower() 
                or (cert.organization and search_term.lower() in cert.organization.name.lower())
            ]
        else:
            filtered_certs = certifications

        # Display certification count
        st.write(f"Showing {len(filtered_certs)} certification(s)")

        # Display certifications in expandable sections
        for cert in filtered_certs:
            with st.expander(f"{get_icon('certification')} {cert.name}", expanded=False):
                col1, col2 = st.columns([3, 1])
                with col1:
                    st.write(f"**Domain:** {cert.domain}")
                    st.write(f"**Level:** {cert.level}")
                    st.write(f"**Difficulty:** {cert.difficulty}")
                    st.write(f"**Cost:** ${cert.cost if cert.cost else 'N/A'}")
                    if cert.organization:
                        st.write(f"**Organization:** {cert.organization.name}")
                    if cert.description:
                        st.write(f"**Description:** {cert.description}")
                    if cert.prerequisites:
                        st.write(f"**Prerequisites:** {cert.prerequisites}")
                    if cert.url:
                        st.write(f"**URL:** [{cert.url}]({cert.url})")
                with col2:
                    if st.button(f"{get_icon('edit')} Edit", key=f"edit_cert_{cert.id}"):
                        # Store the certification ID in session state for editing
                        st.session_state.editing_cert_id = cert.id
                        st.rerun()
                    if st.button(f"{get_icon('delete')} Delete", key=f"delete_cert_{cert.id}"):
                        try:
                            # Check for dependencies before deletion
                            if cert.versions:
                                st.error(f"{get_icon('error')} Cannot delete certification with version history")
                            else:
                                db.delete(cert)
                                db.commit()
                                st.success(f"{get_icon('success')} Certification deleted!")
                                st.rerun()
                        except Exception as e:
                            st.error(f"{get_icon('error')} Error deleting certification: {str(e)}")
                            db.rollback()

    with tab2:
        st.markdown(f"""
        ### {get_icon('organization')} Organization Management
        Add and manage certification issuing organizations.
        """)

        # Add new organization form
        with st.expander(f"{get_icon('add')} Add New Organization", expanded=False):
            with st.form("add_organization"):
                org_name = st.text_input("Organization Name *")
                country = st.text_input("Country *")
                org_url = st.text_input("Website URL")
                org_description = st.text_area("Description")

                if st.form_submit_button(f"{get_icon('add')} Add Organization"):
                    if not all([org_name, country]):
                        st.error(f"{get_icon('error')} Please fill in all required fields")
                    else:
                        try:
                            new_org = Organization(
                                name=org_name,
                                country=country,
                                url=org_url,
                                description=org_description
                            )
                            db.add(new_org)
                            db.commit()
                            st.success(f"{get_icon('success')} Organization added successfully!")
                            st.rerun()
                        except Exception as e:
                            st.error(f"{get_icon('error')} Error adding organization: {str(e)}")
                            db.rollback()

        # List existing organizations
        st.subheader(f"{get_icon('organization')} Existing Organizations")
        organizations = db.query(Organization).order_by(Organization.name).all()
        for org in organizations:
            with st.expander(f"{get_icon('organization')} {org.name}", expanded=False):
                col1, col2 = st.columns([3, 1])
                with col1:
                    st.write(f"**Country:** {org.country}")
                    if org.url:
                        st.write(f"**Website:** [{org.url}]({org.url})")
                    if org.description:
                        st.write(f"**Description:** {org.description}")
                    cert_count = db.query(Certification).filter_by(organization_id=org.id).count()
                    st.write(f"**Associated Certifications:** {cert_count}")
                with col2:
                    if st.button(f"{get_icon('delete')} Delete", key=f"delete_org_{org.id}"):
                        try:
                            # Check if organization has certifications
                            if cert_count > 0:
                                st.error(f"{get_icon('error')} Cannot delete organization with associated certifications")
                            else:
                                db.delete(org)
                                db.commit()
                                st.success(f"{get_icon('success')} Organization deleted!")
                                st.rerun()
                        except Exception as e:
                            st.error(f"{get_icon('error')} Error deleting organization: {str(e)}")
                            db.rollback()

    with tab3:
        st.markdown(f"""
        ### {get_icon('info')} Feedback Management
        View and manage user feedback submissions.
        """)

        # Get feedback from database
        feedback_items = db.query(Feedback).order_by(Feedback.created_at.desc()).all()

        # Display feedback statistics
        total_feedback = len(feedback_items)
        if total_feedback > 0:
            avg_rating = sum(f.rating for f in feedback_items) / total_feedback
            st.metric("Total Feedback", total_feedback)
            st.metric("Average Rating", f"{avg_rating:.1f}/5.0")

            # Filter options
            feedback_types = ["All"] + list(set(f.feedback_type for f in feedback_items))
            selected_type = st.selectbox("Filter by Type", feedback_types)

            # Display feedback items
            for feedback in feedback_items:
                if selected_type == "All" or feedback.feedback_type == selected_type:
                    with st.expander(
                        f"{get_icon('info')} {feedback.feedback_type} - Rating: {feedback.rating}/5 "
                        f"({feedback.created_at.strftime('%Y-%m-%d %H:%M')})", 
                        expanded=False
                    ):
                        st.write(feedback.feedback_text)
                        if st.button(f"{get_icon('delete')} Delete", key=f"delete_feedback_{feedback.id}"):
                            try:
                                db.delete(feedback)
                                db.commit()
                                st.success("Feedback deleted successfully!")
                                st.rerun()
                            except Exception as e:
                                st.error(f"Error deleting feedback: {str(e)}")
                                db.rollback()
        else:
            st.info("No feedback submissions yet.")

    with tab4:
        st.markdown(f"""
        ### {get_icon('language')} Translation Management
        Manage and update translations for the application.
        """)

        # Display translation statistics
        locale_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'translations')
        supported_langs = ['en', 'de', 'es', 'fr', 'af', 'zu', 'ro']
        lang_names = {
            'en': '🇬🇧 English',
            'de': '🇩🇪 German',
            'es': '🇪🇸 Spanish',
            'fr': '🇫🇷 French',
            'af': '🇿🇦 Afrikaans',
            'zu': '🇿🇦 isiZulu',
            'ro': '🇷🇴 Romanian'
        }

        st.subheader("Translation Status")
        for lang in supported_langs:
            mo_path = os.path.join(locale_dir, lang, 'LC_MESSAGES', 'messages.mo')
            po_path = os.path.join(locale_dir, lang, 'LC_MESSAGES', 'messages.po')

            col1, col2, col3 = st.columns([2, 1, 1])
            with col1:
                st.write(f"**{lang_names.get(lang, lang)}**")
            with col2:
                if os.path.exists(mo_path):
                    st.success("✓ Active")
                else:
                    st.warning("⚠ Not Compiled")
            with col3:
                if os.path.exists(po_path):
                    st.info("PO File Present")
                else:
                    st.error("Missing PO File")

        # Add "Translate All" button at the top
        if st.button(f"{get_icon('language')} Translate All Pages"):
            success, message = translate_page()
            if success:
                st.success(f"✅ {message}")
                st.rerun()
            else:
                st.error(message)

        st.markdown("""
        #### Fully Translated:
        - ✅ **Main Interface** (main.py)
        - ✅ **FAQ** (pages/4_faq.py)
        - ✅ **Admin** (pages/3_admin.py)
        - ✅ **Visualization** (pages/1_visualization.py)
        """)

        st.markdown("#### In Progress:")

        # Create columns for each page that needs translation
        pages_to_translate = [
            ("Listings", "pages/2_listings.py"),
            ("Study Time", "pages/5_study_time.py"),
            ("Cost Calculator", "pages/6_cost_calculator.py"),
            ("AI Career Path", "pages/7_ai_career_path.py")
        ]

        for page_name, page_path in pages_to_translate:
            col1, col2 = st.columns([3, 1])
            with col1:
                st.markdown(f"- 🔄 **{page_name}** ({page_path}) - Strings extracted, needs translations")
            with col2:
                if st.button(f"{get_icon('language')} Translate", key=f"translate_{page_path}"):
                    success, message = translate_page(page_path)
                    if success:
                        st.success(f"✅ {page_name}: {message}")
                        st.rerun()
                    else:
                        st.error(f"❌ {page_name}: {message}")

        st.markdown("""
        ### Supported Languages:
        - 🇬🇧 English (en)
        - 🇩🇪 German (de)
        - 🇪🇸 Spanish (es)
        - 🇫🇷 French (fr)
        - 🇿🇦 Afrikaans (af)
        - 🇿🇦 isiZulu (zu)
        - 🇷🇴 Romanian (ro)

        To translate a page:
        1. Wrap all text strings with `_()` function
        2. Use the translation decorator `@translate_page`
        3. Click the translate button to update string catalogs
        """)

    with tab5:
        st.markdown(f"""
        ### {get_icon('sync')} Certification Data Enrichment
        Automatically check online sources for updates to certification information.
        You can also upload PDF files or provide URLs to extract course content.
        Changes will be reviewed before being applied, and all versions are preserved.
        """)

        # Add file upload for PDF enrichment
        uploaded_file = st.file_uploader("Upload PDF with course content", type=['pdf'])

        if uploaded_file:
            pdf_content = extract_pdf_content(uploaded_file)
            if pdf_content:
                st.success("PDF content extracted successfully!")
                st.markdown("### Extracted Content Preview")
                st.text_area("Content", value=pdf_content[:1000] + "...", height=200, disabled=True)

                # Get all certifications for matching
                certifications = db.query(Certification).order_by(Certification.name).all()

                # Find potential matches
                matches = find_matching_certifications(pdf_content, certifications)

                if matches:
                    st.markdown("### 🎯 Suggested Matches")
                    st.info("The following certifications most closely match the uploaded content:")

                    # Create radio options for matches
                    options = [(cert.id, f"{cert.name} (Confidence: {score:.0%})") 
                              for cert, score in matches]
                    options.append((-1, "Choose a different certification"))

                    selected_id = st.radio(
                        "Select matching certification:",
                        options=options,
                        format_func=lambda x: x[1]
                    )[0]

                    if selected_id != -1:
                        selected_cert = next(cert for cert in certifications if cert.id == selected_id)
                    else:
                        # Show dropdown with all certifications if user wants to choose different one
                        selected_cert = st.selectbox(
                            "Select certification to enrich",
                            options=certifications,
                            format_func=lambda x: x.name
                        )
                else:
                    # If no matches found, just show the full dropdown
                    selected_cert = st.selectbox(
                        "Select certification to enrich",
                        options=certifications,
                        format_func=lambda x: x.name
                    )

                if st.button("Apply Content to Certification"):
                    try:
                        # Create new version record
                        version = selected_cert.create_version(
                            reason="PDF content enrichment",
                            source="pdf_upload",
                            reference_url=None
                        )
                        db.add(version)

                        # Update certification with new content
                        selected_cert.course_content = pdf_content
                        selected_cert.last_updated = datetime.now()

                        db.commit()
                        st.success(f"{get_icon('success')} Content applied successfully!")
                        st.rerun()
                    except Exception as e:
                        st.error(f"{get_icon('error')} Error applying content: {str(e)}")
                        db.rollback()

        # List certifications for enrichment
        certifications = db.query(Certification).all()
        for cert in certifications:
            with st.expander(f"{get_icon('certification')} {cert.name}", expanded=False):
                col1, col2 = st.columns([3, 1])
                with col1:
                    st.write(f"**Current Version:** {cert.current_version}")
                    st.write(f"**Last Updated:** {cert.last_updated.strftime('%Y-%m-%d %H:%M')}")
                    st.write(f"**URL:** {cert.url or 'Not specified'}")

                    if cert.course_content:
                        with st.expander("View Current Course Content"):
                            st.text_area("Content", value=cert.course_content[:1000] + "...", 
                                       height=200, disabled=True)

                with col2:
                    if cert.url:  # Only show enrich button if URL is available
                        if st.button(f"{get_icon('sync')} Enrich", key=f"enrich_{cert.id}"):
                            # Fetch new data
                            new_data = fetch_certification_data(cert.url)

                            if new_data:
                                # Compare with current data
                                changes, significant = compare_certification_data(
                                    cert.to_dict(),
                                    new_data
                                )

                                if changes:
                                    st.markdown("### Detected Changes")
                                    st.code(format_changes(changes))

                                    if st.button(f"{get_icon('save')} Apply Changes"):
                                        try:
                                            # Create new version record
                                            version = cert.create_version(
                                                reason="Automatic data enrichment",
                                                source="auto_enrichment",
                                                reference_url=cert.url
                                            )
                                            db.add(version)

                                            # Update certification with new data
                                            for key, value in new_data.items():
                                                if hasattr(cert, key):
                                                    setattr(cert, key, value)

                                            db.commit()
                                            st.success(f"{get_icon('success')} Changes applied successfully!")
                                            st.rerun()
                                        except Exception as e:
                                            st.error(f"{get_icon('error')} Error applying changes: {str(e)}")
                                            db.rollback()
                                else:
                                    st.info(f"{get_icon('info')} No changes detected")
                            else:
                                st.error(f"{get_icon('error')} Failed to fetch updated data")
                    else:
                        st.warning(f"{get_icon('warning')} No URL specified")

                # Show version history
                if cert.versions:
                    st.markdown("#### Version History")
                    for version in cert.versions:
                        with st.expander(
                            f"Version {version.version} - "
                            f"{version.changed_at.strftime('%Y-%m-%d %H:%M')}",
                            expanded=False
                        ):
                            st.write(f"**Source:** {version.change_source}")
                            st.write(f"**Reason:** {version.change_reason}")
                            if version.reference_url:
                                st.write(f"**Reference URL:** [{version.reference_url}]({version.reference_url})")
                            st.write("**Changes:**")
                            for key, value in version.to_dict().items():
                                if key not in ['version', 'change_source', 'change_reason', 
                                             'changed_at', 'reference_url']:
                                    st.write(f"- {key}: {value}")

    with tab6:
        st.markdown(f"""
        ### {get_icon('sync')} API Usage Statistics
        Monitor Claude API usage and quotas.
        """)

        # Get current usage stats
        usage_stats = get_claude_usage_stats()

        if "error" in usage_stats:
            st.error(f"{get_icon('error')} {usage_stats['error']}")
        else:
            # API Status
            status_color = "green" if usage_stats["status"] == "active" else "red"
            st.markdown(f"""
            #### API Status
            <span style='color: {status_color};'>●</span> {usage_stats["status"].title()}
            """, unsafe_allow_html=True)

            # Create columns for metrics
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric(
                    "Total Requests",
                    usage_stats["total_requests"]
                )

            with col2:
                st.metric(
                    "Requests Today",
                    usage_stats["requests_today"]
                )

            with col3:
                st.metric(
                    "Total Tokens Used",
                    f"{usage_stats['total_tokens']:,}"
                )

            # Last request information
            if usage_stats["last_request"]:
                st.markdown("#### Last Request")
                st.info(
                    f"Last API call: "
                    f"{usage_stats['last_request'].strftime('%Y-%m-%d %H:%M:%S')}"
                )

            # Auto-refresh button
            if st.button(f"{get_icon('sync')} Refresh Stats"):
                st.rerun()

            # Display usage chart if we have historical data
            if "usage_history" in st.session_state:
                st.markdown("#### Usage History")
                # Add chart implementation here if needed

            st.markdown("""
            #### Usage Notes:
            - Token usage is tracked per request
            - Daily counters reset at midnight
            - Usage stats are stored in session state
            """)

    with tab7:
        st.markdown(f"""
        ### {get_icon('user')} Job Title Management
        Add, edit, or remove security job titles and their domains.
        Fields marked with * are required.
        """)

        # Cache query results
        @st.cache_data(ttl=60)
        def get_filtered_jobs(_db, _search, _domain, _page, _per_page):
            """Get filtered and paginated jobs using parameterized queries"""
            from sqlalchemy import or_, text

            query = _db.query(SecurityJob)
            if _search:
                # Use parameterized LIKE patterns
                search_pattern = f"%{_search}%"
                query = query.filter(
                    or_(
                        SecurityJob.title.ilike(search_pattern),
                        SecurityJob.domain.ilike(search_pattern)
                    )
                )
            if _domain != "All":
                query = query.filter(SecurityJob.domain == _domain)

            # Get total count first
            total = query.count()

            # Then get paginated results
            jobs = (query.order_by(SecurityJob.title)
                   .offset(_page * _per_page)
                   .limit(_per_page)
                   .all())
            return jobs, total

        # Add new job form
        with st.expander(f"{get_icon('add')} Add New Job Title", expanded=False):
            with st.form("add_job"):
                # Form fields remain the same
                title = st.text_input("Job Title *")
                domain = st.selectbox(
                    "Domain *",
                    options=SECURITY_DOMAINS
                )
                description = st.text_area(
                    "Description",
                    help="Brief description of the job role and responsibilities"
                )
                experience_level = st.selectbox(
                    "Experience Level",
                    options=["Entry Level", "Mid Level", "Senior", "Lead", "Principal"]
                )
                salary_range_min = st.number_input("Minimum Salary (USD)", min_value=0, value=0)
                salary_range_max = st.number_input("Maximum Salary (USD)", min_value=0, value=0)
                skills = st.text_area(
                    "Required Skills",
                    help="Enter comma-separated list of required skills"
                )

                if st.form_submit_button(f"{get_icon('add')} Add Job Title"):
                    if not all([title, domain]):
                        st.error(f"{get_icon('error')} Please fill in all required fields")
                    elif not is_valid_job_title(title):
                        st.error(f"{get_icon('error')} Invalid job title format")
                    else:
                        try:
                            # Use parameterized query to check existing job
                            existing = db.query(SecurityJob).filter(
                                SecurityJob.title == title,
                                SecurityJob.domain == domain
                            ).first()

                            if existing:
                                st.error(f"{get_icon('error')} Job title already exists")
                            else:
                                new_job = SecurityJob(
                                    title=title,
                                    domain=domain,
                                    description=description,
                                    experience_level=experience_level,
                                    salary_range_min=salary_range_min,
                                    salary_range_max=salary_range_max,
                                    skills_required=skills
                                )
                                db.add(new_job)
                                db.commit()
                                st.success(f"{get_icon('success')} Job title added successfully!")
                                st.rerun()
                        except Exception as e:
                            st.error(f"{get_icon('error')} Error adding job title: {str(e)}")
                            db.rollback()

        # List and manage existing jobs
        st.subheader(f"{get_icon('list')} Existing Job Titles")

        # Add search and filter controls in columns for better space usage
        col1, col2 = st.columns(2)
        with col1:
            search_term = st.text_input(
                "🔍 Search Jobs",
                help="Filter jobs by title or domain"
            )
        with col2:
            selected_domain = st.selectbox(
                "Filter by Domain",
                options=["All"] + SECURITY_DOMAINS
            )

        # Pagination controls
        items_per_page = 10
        if 'job_page_number' not in st.session_state:
            st.session_state.job_page_number = 0

        # Get paginated and filtered results
        jobs, total_jobs = get_filtered_jobs(
            db, 
            search_term, 
            selected_domain,
            st.session_state.job_page_number,
            items_per_page
        )

        # Pagination UI
        total_pages = -(-total_jobs // items_per_page)  # Ceiling division
        if total_pages > 1:
            col1, col2, col3 = st.columns([1, 2, 1])
            with col1:
                if st.button("Previous", disabled=st.session_state.job_page_number == 0):
                    st.session_state.job_page_number -= 1
                    st.rerun()
            with col2:
                st.write(f"Page {st.session_state.job_page_number + 1} of {total_pages}")
            with col3:
                if st.button("Next", disabled=st.session_state.job_page_number >= total_pages - 1):
                    st.session_state.job_page_number += 1
                    st.rerun()

        # Display job count
        st.write(f"Showing {len(jobs)} of {total_jobs} job title(s)")

        # Display jobs in a more compact format
        for job in jobs:
            with st.expander(f"{get_icon('job')} {job.title} ({job.domain})", expanded=False):
                if "editing_job_" + str(job.id) not in st.session_state:
                    st.session_state["editing_job_" + str(job.id)] = False

                if st.session_state["editing_job_" + str(job.id)]:
                    # Edit form
                    with st.form(f"edit_job_{job.id}"):
                        col1, col2 = st.columns(2)
                        with col1:
                            edited_title = st.text_input("Job Title *", value=job.title)
                            edited_domain = st.selectbox(
                                "Domain *",
                                options=SECURITY_DOMAINS,
                                index=SECURITY_DOMAINS.index(job.domain)
                            )
                            edited_experience = st.selectbox(
                                "Experience Level",
                                options=["Entry Level", "Mid Level", "Senior", "Lead", "Principal"],
                                index=["Entry Level", "Mid Level", "Senior", "Lead", "Principal"].index(
                                    job.experience_level if job.experience_level else "Entry Level"
                                )
                            )
                        with col2:
                            edited_min_salary = st.number_input(
                                "Minimum Salary (USD)",
                                value=job.salary_range_min or 0                            )
                            edited_max_salary =st.number_input(
                                "Maximum Salary (USD)",
                                value=job.salary_range_max or 0
                            )

                        edited_description = st.text_area(
                            "Description",
                            value=job.description or "",
                            help="Enter the job description"
                        )
                        edited_skills = st.text_area(
                            "Required Skills",
                            value=job.skills_required or "",
                            help="Enter comma-separated list of required skills"
                        )

                        col1, col2 = st.columns(2)
                        with col1:
                            if st.form_submit_button(f"{get_icon('save')} Save Changes"):
                                if not all([edited_title, edited_domain]):
                                    st.error(f"{get_icon('error')} Please fill in all required fields")
                                elif not is_valid_job_title(edited_title):
                                    st.error(f"{get_icon('error')} Invalid job title format")
                                else:
                                    try:
                                        # Use parameterized query for update
                                        db.query(SecurityJob).filter(
                                            SecurityJob.id == job.id
                                        ).update({
                                            'title': edited_title,
                                            'domain': edited_domain,
                                            'description': edited_description,
                                            'experience_level': edited_experience,
                                            'salary_range_min': edited_min_salary,
                                            'salary_range_max': edited_max_salary,
                                            'skills_required': edited_skills,
                                            'updated_at': datetime.utcnow()
                                        })
                                        db.commit()
                                        st.success(f"{get_icon('success')} Changes saved!")
                                        st.session_state["editing_job_" + str(job.id)] = False
                                        st.rerun()
                                    except Exception as e:
                                        st.error(f"{get_icon('error')} Error: {str(e)}")
                                        db.rollback()
                        with col2:
                            if st.form_submit_button(f"{get_icon('cancel')} Cancel"):
                                st.session_state["editing_job_" + str(job.id)] = False
                                st.rerun()
                else:
                    # Display job details in a more compact layout
                    col1, col2, col3 = st.columns([2, 2, 1])
                    with col1:
                        st.write(f"**Domain:** {job.domain}")
                        st.write(f"**Experience:** {job.experience_level or 'Not specified'}")
                        if job.salary_range_min and job.salary_range_max:
                            st.write(f"**Salary:** ${job.salary_range_min:,} - ${job.salary_range_max:,}")
                    with col2:
                        if job.description:
                            st.write(f"**Description:** {job.description}")
                        if job.skills_required:
                            skills = [s.strip() for s in job.skills_required.split(',')]
                            st.write("**Skills:** " + ", ".join(skills))
                    with col3:
                        st.button(f"{get_icon('edit')} Edit", key=f"edit_job_{job.id}", 
                                on_click=lambda id=job.id: setattr(st.session_state, f"editing_job_{id}", True))
                        delete_key = f"delete_job_{job.id}"
                        if st.button(f"{get_icon('delete')} Delete", key=delete_key):
                            try:
                                # Use parameterized query for delete
                                db.query(SecurityJob).filter(
                                    SecurityJob.id == job.id
                                ).delete()
                                db.commit()
                                st.success(f"{get_icon('success')} Deleted!")
                                st.rerun()
                            except Exception as e:
                                st.error(f"{get_icon('error')} Error: {str(e)}")
                                db.rollback()

if __name__ == "__main__":
    run()