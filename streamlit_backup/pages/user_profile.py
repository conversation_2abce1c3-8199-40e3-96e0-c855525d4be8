"""User profile management page for Security Certification Explorer"""
import streamlit as st
from typing import List, Dict
from components.icons import get_icon, icon_header
from utils.user_profile_manager import UserProfileManager
import logging

# Set up logging
logger = logging.getLogger(__name__)

def get_available_roles() -> List[str]:
    """Get list of predefined security roles"""
    return [
        "Security Analyst",
        "Penetration Tester",
        "Security Engineer",
        "Security Architect",
        "Security Consultant",
        "Security Manager",
        "Incident Responder",
        "Threat Hunter",
        "Application Security Engineer",
        "Cloud Security Engineer"
    ]

def get_expertise_areas() -> List[str]:
    """Get list of security expertise areas"""
    return [
        "Network Security",
        "Application Security",
        "Cloud Security",
        "Incident Response",
        "Threat Intelligence",
        "Risk Management",
        "Cryptography",
        "Identity and Access Management",
        "Security Operations",
        "Compliance and Governance"
    ]

def run() -> None:
    """Execute the user profile page functionality"""
    st.title(f"{get_icon('user')} User Profile")

    # For testing, automatically set a test user if not logged in
    if not st.session_state.get('user'):
        st.session_state['user'] = {
            'id': 'test_user_1',
            'name': 'Test User',
            'email': '<EMAIL>'
        }
        st.info("Using test user account for development. In production, login will be required.")

    # Initialize user profile state
    UserProfileManager.initialize_session()
    profile = UserProfileManager.get_profile()

    logger.info(f"Current profile state: {profile}")

    with st.form("user_profile_form", clear_on_submit=False):
        st.subheader("Professional Information")

        # Basic information
        years_exp = st.number_input(
            "Years of Experience",
            min_value=0,
            max_value=50,
            value=profile.get('years_experience', 0) if profile else 0
        )

        current_role = st.selectbox(
            "Current Role",
            options=get_available_roles(),
            index=get_available_roles().index(profile.get('user_role')) if profile and profile.get('user_role') else 0
        )

        desired_role = st.selectbox(
            "Desired Role",
            options=get_available_roles(),
            index=get_available_roles().index(profile.get('desired_role')) if profile and profile.get('desired_role') else 0
        )

        # Expertise areas
        st.subheader("Areas of Expertise")
        expertise = st.multiselect(
            "Select your areas of expertise",
            options=get_expertise_areas(),
            default=profile.get('expertise_areas', []) if profile else []
        )

        # Learning preferences
        st.subheader("Learning Preferences")
        learning_style = st.selectbox(
            "Preferred Learning Style",
            options=["Visual", "Reading/Writing", "Auditory", "Hands-on"],
            index=["Visual", "Reading/Writing", "Auditory", "Hands-on"].index(profile.get('preferred_learning_style')) if profile and profile.get('preferred_learning_style') else 0
        )

        study_time = st.number_input(
            "Available Study Hours per Week",
            min_value=1,
            max_value=168,
            value=profile.get('study_time_available', 10) if profile else 10,
            help="How many hours can you dedicate to studying each week?"
        )

        # Save button
        if st.form_submit_button("Save Profile"):
            user_data = {
                'years_experience': years_exp,
                'user_role': current_role,
                'desired_role': desired_role,
                'expertise_areas': expertise,
                'preferred_learning_style': learning_style,
                'study_time_available': study_time
            }

            logger.info(f"Attempting to save profile data: {user_data}")

            if UserProfileManager.save_profile(user_data):
                st.success("Profile saved successfully!")
                st.rerun()  # Refresh to show updated data
            else:
                st.error("Failed to save profile. Please try again.")

    # Display learning paths section
    if profile:
        st.markdown("---")
        icon_header("Your Learning Paths", "certification")

        try:
            from database import get_db
            from models.learning_path import UserExperience

            db = next(get_db())
            user_exp = db.query(UserExperience).filter_by(
                user_id=st.session_state['user']['id']
            ).first()

            if user_exp and user_exp.learning_paths:
                for path in user_exp.learning_paths:
                    with st.expander(f"📚 {path.name}", expanded=False):
                        st.write(f"**Description:** {path.description}")
                        st.write(f"**Duration:** {path.estimated_duration_weeks} weeks")
                        st.write(f"**Status:** {path.status}")

                        # Show progress
                        completed = len([item for item in path.path_items if item.status == 'completed'])
                        total = len(path.path_items)
                        if total > 0:
                            progress = completed / total * 100
                            st.progress(progress)
                            st.write(f"Progress: {progress:.1f}% ({completed}/{total} items completed)")
            else:
                st.info("No learning paths created yet. Visit the CertRat CareerPath page to create your first learning path!")

        except Exception as e:
            logger.error(f"Error displaying learning paths: {str(e)}")
            st.error("Unable to load learning paths. Please try refreshing the page.")

if __name__ == "__main__":
    run()