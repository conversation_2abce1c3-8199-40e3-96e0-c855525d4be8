"""FAQ page for Security Certification Explorer.

This module provides the FAQ (Frequently Asked Questions) page functionality,
displaying information about certification-related topics such as CPE
(Continuing Professional Education) requirements and best practices.
"""
from typing import NoReturn
import streamlit as st
from components.icons import icon_header, get_icon

def run() -> NoReturn:
    """Display the FAQ page content.

    This function renders the FAQ page with sections covering various topics
    related to security certifications, particularly focusing on CPE requirements
    and certification maintenance.

    Returns:
        NoReturn: This function modifies the Streamlit UI and doesn't return a value
    """
    st.title(f"{get_icon('info')} Frequently Asked Questions")
    st.markdown(
        '<div style="text-align: right; font-size: 0.8em; color: #666;">'
        'Home > FAQ</div>',
        unsafe_allow_html=True
    )

    # CPE Section
    icon_header("What is CPE?", "certification")

    st.markdown("""
    ### Continuing Professional Education (CPE)

    CPE, or Continuing Professional Education, is a requirement for maintaining many
    security certifications. It ensures that certified professionals stay current
    with the latest developments, technologies, and best practices in their field.

    #### Key Points About CPE:

    1. **Purpose**
       - Keep knowledge and skills current
       - Stay updated with industry trends
       - Maintain certification validity

    2. **Common CPE Activities**
       - Attending conferences or seminars
       - Completing training courses
       - Publishing articles or research
       - Contributing to the security community
       - Teaching or presenting at events
       - Reading professional literature

    3. **Typical Requirements**
       - Most certifications require 20-40 CPE credits annually
       - Credits must be relevant to your certification domain
       - Activities must be documented and sometimes pre-approved
       - Some credits must be from specific categories

    4. **Example CPE Requirements**
       - CISSP: 40 CPE credits per year (120 over 3 years)
       - Security+: 50 CPE credits over 3 years
       - CISA: 20 CPE credits per year (120 over 3 years)
       - CISM: 20 CPE credits per year (120 over 3 years)

    5. **Best Practices**
       - Track CPE credits regularly
       - Maintain documentation of all activities
       - Plan activities in advance
       - Focus on quality and relevance
       - Balance different types of activities

    #### CPE Tracking
    Most certification organizations provide online portals where you can:
    - Log and track your CPE credits
    - Submit documentation
    - View your certification status
    - Get notifications about CPE deadlines
    """)

if __name__ == "__main__":
    run()