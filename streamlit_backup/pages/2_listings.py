"""
Certification Listings Page with optimized loading and search
"""
from typing import List, Dict, Optional, Tuple, Set
import streamlit as st
from database import get_db
from models.certification import Certification
from components.visualization import display_certification_details
from sqlalchemy import func
import logging

# Configure logging
logger = logging.getLogger(__name__)

def get_basic_certification_info(db) -> List[Dict]:
    """Get basic certification information"""
    try:
        certs = db.query(Certification).with_entities(
            Certification.id,
            Certification.name,
            Certification.domain,
            Certification.level,
            Certification.cost
        ).all()
        return [
            {
                'id': cert.id,
                'name': cert.name,
                'domain': cert.domain,
                'level': cert.level,
                'cost': cert.cost
            }
            for cert in certs
        ]
    except Exception as e:
        logger.error(f"Error fetching basic certification info: {str(e)}")
        return []

def get_certification_details(db, cert_id: int) -> Optional[Dict]:
    """Get detailed certification information"""
    try:
        cert = db.query(Certification).filter(Certification.id == cert_id).first()
        if cert:
            return {
                'level': cert.level,
                'domain': cert.domain,
                'difficulty': cert.difficulty,
                'cost': cert.cost,
                'description': cert.description,
                'prerequisites': cert.prerequisites,
                'issuing_organization': (
                    cert.organization.name if cert.organization else "Unknown"
                ),
                'url': cert.url
            }
    except Exception as e:
        logger.error(f"Error fetching certification details: {str(e)}")
    return None

def main() -> None:
    """Display the certification listings page with optimized loading."""
    st.title("Certification Listings")
    st.markdown("""
    Browse and filter security certifications based on your experience level, 
    interests, and budget. Select certifications to view detailed information.
    """)

    try:
        # Get database session
        db = next(get_db())

        # Get basic certification info
        basic_certs = get_basic_certification_info(db)
        if not basic_certs:
            st.error("Unable to load certifications. Please try again.")
            return

        # Initialize session state
        if 'selected_domains' not in st.session_state:
            st.session_state.selected_domains: Set[str] = set()
        if 'cert_details_cache' not in st.session_state:
            st.session_state.cert_details_cache: Dict[int, Dict] = {}
        if 'selected_certs' not in st.session_state:
            st.session_state.selected_certs: Set[str] = set()

        # Get unique values for filters from basic info
        levels = sorted(set(cert['level'] for cert in basic_certs))
        domains = sorted(set(cert['domain'] for cert in basic_certs))
        max_cost = max((cert['cost'] for cert in basic_certs if cert['cost'] is not None), default=5000)

        # Main content area
        col1, col2 = st.columns([2, 1])

        with col1:
            # Searchable certification dropdown
            selected_certs = st.multiselect(
                "Search Certifications",
                options=[cert['name'] for cert in basic_certs],
                default=list(st.session_state.selected_certs),
                help="Search and select certifications to view details"
            )
            st.session_state.selected_certs = set(selected_certs)

        with col2:
            # Filter inputs in sidebar
            st.sidebar.subheader("📊 Filters")
            selected_level = st.sidebar.selectbox(
                "Experience Level",
                ["All"] + levels
            )

            # Domain filter buttons with responsive layout
            st.sidebar.subheader("Domains")
            cols = st.sidebar.columns(2)
            for idx, domain in enumerate(domains):
                col = cols[idx % 2]
                if col.button(
                    domain,
                    type="secondary" if domain in st.session_state.selected_domains else "primary",
                    key=f"domain_{domain}"
                ):
                    if domain in st.session_state.selected_domains:
                        st.session_state.selected_domains.remove(domain)
                    else:
                        st.session_state.selected_domains.add(domain)
                    st.rerun()

            # Show selected domains
            if st.session_state.selected_domains:
                st.sidebar.caption(
                    "Selected domains: " + 
                    ", ".join(sorted(st.session_state.selected_domains))
                )

            max_cost_filter = st.sidebar.slider(
                "Maximum Cost ($)", 
                min_value=0,
                max_value=int(max_cost),
                value=int(max_cost)
            )

        # Apply filters to basic cert info
        filtered_certs = [
            cert for cert in basic_certs
            if (selected_level == "All" or cert['level'] == selected_level) and
               (not st.session_state.selected_domains or 
                cert['domain'] in st.session_state.selected_domains) and
               (cert['cost'] is None or cert['cost'] <= max_cost_filter) and
               (not st.session_state.selected_certs or
                cert['name'] in st.session_state.selected_certs)
        ]

        # Display results count
        st.write(f"Found {len(filtered_certs)} certifications matching your criteria")

        # Display certifications with lazy loading of details
        for cert in filtered_certs:
            with st.expander(
                f"{cert['name']} - {cert['domain']} ({cert['level']})",
                expanded=cert['name'] in st.session_state.selected_certs
            ):
                try:
                    # Check if we have cached details
                    if cert['id'] not in st.session_state.cert_details_cache:
                        st.session_state.cert_details_cache[cert['id']] = get_certification_details(db, cert['id'])

                    # Display details
                    if cert['id'] in st.session_state.cert_details_cache and st.session_state.cert_details_cache[cert['id']]:
                        display_certification_details(
                            cert['name'],
                            st.session_state.cert_details_cache[cert['id']]
                        )
                    else:
                        st.info("Loading certification details...")
                except Exception as e:
                    logger.error(f"Error displaying certification {cert['name']}: {str(e)}")
                    st.warning("Unable to load certification details. Please try again.")

    except Exception as e:
        logger.error(f"Error in listings page: {str(e)}", exc_info=True)
        st.error(
            "An error occurred while loading certifications. Please try again later."
        )

if __name__ == "__main__":
    main()