"""Study time estimation page for Security Certification Explorer.

This module provides functionality to estimate study time required for various
security certifications based on their difficulty levels and user preferences.
"""
from typing import Tuple, Optional, Dict, Any
import streamlit as st
from datetime import datetime, timedelta
from components.icons import get_icon, icon_header
from database import get_db
from models.certification import Certification
from sqlalchemy.exc import SQLAlchemyError
from utils.growth_analyzer import StudyPlanner, calculate_certification_roi
import logging

# Set up logging
logger = logging.getLogger(__name__)

def calculate_study_weeks(difficulty: int, hours_per_week: float) -> Tuple[float, int]:
    """Calculate estimated weeks to complete certification.

    Args:
        difficulty: Integer from 1-4 representing certification difficulty
        hours_per_week: Number of study hours available per week

    Returns:
        Tuple containing:
            - Estimated weeks to complete (float)
            - Total study hours required (int)
    """
    base_hours: Dict[int, int] = {
        1: 60,   # Entry level (~60 hours total)
        2: 120,  # Intermediate (~120 hours total)
        3: 240,  # Advanced (~240 hours total)
        4: 400   # Expert (~400 hours total)
    }

    total_hours = base_hours.get(difficulty, 120)
    weeks = round(total_hours / hours_per_week, 1)
    return weeks, total_hours

def display_study_recommendations(
    hours_per_week: float,
    selected_cert: Certification,
    total_hours: int
) -> None:
    """Display study recommendations and tips.

    Args:
        hours_per_week: Available study hours per week
        selected_cert: Selected certification object
        total_hours: Total estimated study hours
    """
    st.markdown(f"""
    ### Weekly Schedule
    With {hours_per_week} hours per week:
    - {round(hours_per_week/5, 1)} hours per weekday, or
    - {round(hours_per_week/2, 1)} hours per weekend day

    ### Prerequisites
    {selected_cert.prerequisites if selected_cert.prerequisites else 
    "No prerequisites required"}

    ### Tips for Success
    1. Break down study sessions into 25-minute focused intervals
    2. Review material regularly to reinforce learning
    3. Take practice exams in the final weeks
    4. Join study groups or online communities
    5. Create a dedicated study environment
    """)

def display_progress_tracking(weeks: float) -> None:
    """Display and handle progress tracking interface.

    Args:
        weeks: Total number of weeks estimated for completion
    """
    progress: int = st.slider(
        "Update your progress",
        0, 100, 0,
        help="Move the slider to track your study progress"
    )

    if progress > 0:
        remaining_weeks = weeks * (100 - progress) / 100
        new_target = datetime.now() + timedelta(weeks=remaining_weeks)
        st.success(
            f"You're {progress}% complete! "
            f"Expected completion: {new_target.strftime('%B %Y')}"
        )

def handle_custom_estimate(db: Any, cert_names: list) -> None:
    """Handle the custom time estimate form.

    Args:
        db: Database session
        cert_names: List of certification names
    """
    with st.expander("➕ Add Custom Time Estimate", expanded=False):
        st.markdown("""
        Add your own time estimate for a certification based on personal experience 
        or specific study materials.
        """)

        with st.form("add_custom_estimate"):
            cert_name: str = st.selectbox(
                "Select Certification",
                options=cert_names,
                key="custom_cert"
            )

            estimated_hours: int = st.number_input(
                "Total Study Hours Required",
                min_value=1,
                max_value=1000,
                value=120,
                help="Enter your estimate of total hours needed"
            )

            study_materials: Optional[str] = st.text_area(
                "Study Materials/Notes",
                help="Optional: Add notes about study materials or approach"
            )

            if st.form_submit_button("Add Estimate"):
                try:
                    cert = db.query(Certification).filter_by(name=cert_name).first()
                    if cert:
                        cert.custom_hours = estimated_hours
                        cert.study_notes = study_materials
                        db.commit()
                        st.success("Custom estimate added successfully!")
                        st.rerun()
                except SQLAlchemyError as e:
                    logger.error(f"Database error adding custom estimate: {str(e)}")
                    st.error(f"Error adding estimate: {str(e)}")
                    db.rollback()

def run() -> None:
    """Execute the study time estimation page functionality."""
    st.title(f"{get_icon('certification')} Study Time Estimator")
    st.markdown(
        '<div style="text-align: right; font-size: 0.8em; color: #666;">'
        'Home > Study Time</div>',
        unsafe_allow_html=True
    )

    st.markdown("""
    Plan your certification journey by estimating the time needed based on your schedule.
    This tool provides personalized study time estimates and recommendations.
    """)

    try:
        # Get database session
        db = next(get_db())
        planner = StudyPlanner(db)

        # Get all certifications
        certifications = db.query(Certification).all()
        cert_names = [cert.name for cert in certifications]

        # Main estimation interface
        selected_cert_name: str = st.selectbox(
            "Select Certification",
            options=cert_names,
            help="Choose the certification you want to pursue"
        )

        hours_per_week: float = st.slider(
            "Available Study Hours per Week",
            min_value=1,
            max_value=40,
            value=10,
            help="How many hours can you dedicate to studying each week?"
        )

        # Optional target date
        use_target_date = st.checkbox("Set Target Completion Date")
        target_date = None
        if use_target_date:
            target_date = st.date_input(
                "Target Completion Date",
                min_value=datetime.now().date(),
                value=datetime.now().date() + timedelta(weeks=12)
            )

        if selected_cert_name and hours_per_week:
            selected_cert = db.query(Certification).filter_by(
                name=selected_cert_name
            ).first()

            if selected_cert:
                # Generate detailed study schedule
                schedule = planner.generate_study_schedule(
                    certification_id=selected_cert.id,
                    target_date=datetime.combine(target_date, datetime.min.time()) if target_date else None,
                    hours_per_week=hours_per_week
                )

                if schedule["status"] == "warning":
                    st.warning(schedule["message"])
                    st.info(f"Suggested target date: {schedule['suggested_target']}")
                elif schedule["status"] == "success":
                    # Display results
                    st.markdown("---")
                    icon_header("Study Schedule", "certification")

                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric(
                            "Total Study Hours",
                            f"{schedule['total_hours_required']} hours"
                        )
                        st.metric(
                            "Completion Time",
                            f"{schedule['weeks_required']} weeks"
                        )

                    with col2:
                        target_date = datetime.now() + timedelta(
                            weeks=schedule['weeks_required']
                        )
                        st.metric(
                            "Target Completion",
                            target_date.strftime("%B %Y")
                        )
                        if selected_cert.cost:
                            st.metric(
                                "Certification Cost",
                                f"${selected_cert.cost:,.2f}"
                            )

                    # Show ROI calculation if user enters current salary
                    show_roi = st.checkbox("Calculate Return on Investment (ROI)")
                    if show_roi:
                        current_salary = st.number_input(
                            "Current Annual Salary ($)",
                            min_value=0,
                            value=75000,
                            step=5000
                        )

                        roi_metrics = calculate_certification_roi(
                            certification_id=selected_cert.id,
                            current_salary=current_salary,
                            db_session=db
                        )

                        if roi_metrics["status"] == "success":
                            st.markdown("#### 📈 ROI Analysis")
                            roi_cols = st.columns(3)

                            with roi_cols[0]:
                                st.metric(
                                    "Expected ROI",
                                    f"{roi_metrics['percent_roi']}%"
                                )

                            with roi_cols[1]:
                                st.metric(
                                    "Break-even Time",
                                    f"{roi_metrics['break_even_months']} months"
                                )

                            with roi_cols[2]:
                                st.metric(
                                    "5-Year Value",
                                    f"${roi_metrics['five_year_value']:,.2f}"
                                )

                    # Display weekly schedule
                    st.markdown("### 📅 Weekly Study Plan")
                    for week in schedule['weekly_schedule']:
                        with st.expander(
                            f"Week {week['week']} - {week['hours_planned']} hours"
                        ):
                            # Focus Areas
                            st.markdown("#### 🎯 Focus Areas")
                            for focus in week['focus_areas']:
                                st.markdown(f"""
                                - **{focus['topic']}** ({focus['hours_suggested']} hours)
                                  - Resources: {', '.join(focus['resources'])}
                                """)

                            # Milestones
                            st.markdown("#### 🏆 Milestones")
                            for milestone in week['milestones']:
                                st.markdown(f"""
                                - {milestone['description']}
                                  - Type: {milestone['type']}
                                  - Target: {milestone['target_date']}
                                """)

                    # Study tips
                    st.markdown("### 📚 Study Tips")
                    for tip in schedule['study_tips']:
                        st.markdown(f"- {tip}")

    except SQLAlchemyError as e:
        logger.error(f"Database error: {str(e)}")
        st.error(
            "An error occurred while fetching certification data. "
            "Please try again later."
        )
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        st.error("An unexpected error occurred. Please try again later.")

if __name__ == "__main__":
    run()