"""
User Journey Maps page with Mermaid.js visualization
"""
import streamlit as st
from typing import Dict, List
from translations import _, init_localization
from components.icons import get_icon
from components.mermaid_diagram import render_mermaid_diagram, generate_journey_diagram

# Initialize translations
init_localization()

def get_lifelong_learner_journey() -> List[Dict[str, str]]:
    """Define the lifelong learner journey stages based on documentation"""
    return [
        {
            'name': 'Discovery',
            'task': 'Discover Platform & Filter Certifications',
            'score': 3,
            'details': 'Initial platform exploration and certification discovery'
        },
        {
            'name': 'Research',
            'task': 'Review Detailed Certification Info',
            'score': 4,
            'details': 'Deep dive into certification requirements and paths'
        },
        {
            'name': 'Planning',
            'task': 'Get AI Career Guidance',
            'score': 5,
            'details': 'Personalized certification recommendations'
        },
        {
            'name': 'Preparation',
            'task': 'Calculate Costs & Plan Study Time',
            'score': 4,
            'details': 'Resource planning and timeline setting'
        },
        {
            'name': 'Execution',
            'task': 'Begin Certification Journey',
            'score': 5,
            'details': 'Start active learning and preparation'
        },
        {
            'name': 'Progress',
            'task': 'Track Study Progress',
            'score': 4,
            'details': 'Monitor achievements and update goals'
        },
        {
            'name': 'Enhancement',
            'task': 'Access Study Resources',
            'score': 5,
            'details': 'Utilize platform resources and community support'
        }
    ]

def run():
    """Main function to run the User Journeys page"""
    st.title(f"{get_icon('map')} {_('User Journey Maps')}")

    st.markdown("""
    ## Lifelong Learner Journey Map

    This visualization shows the journey of a continuous learner using our platform
    to advance their career through certifications. Each stage includes satisfaction
    scores and key activities.

    **Satisfaction Levels:**
    - 1: Very Dissatisfied
    - 2: Dissatisfied
    - 3: Neutral
    - 4: Satisfied
    - 5: Very Satisfied
    """)

    # Generate and render the journey diagram
    journey_stages = get_lifelong_learner_journey()
    diagram_code = generate_journey_diagram(journey_stages)
    render_mermaid_diagram(diagram_code, height=500)

    # Show detailed journey information
    st.header("Journey Stage Details")
    st.markdown("""
    Explore each stage of the certification journey to understand the user's
    experience and satisfaction levels at different points.
    """)

    # Create columns for the stages
    cols = st.columns(2)
    for idx, stage in enumerate(journey_stages):
        with cols[idx % 2]:
            with st.expander(f"🔹 {stage['name']} Stage", expanded=False):
                st.markdown(f"""
                **Task:** {stage['task']}

                **Satisfaction:** {'⭐' * int(stage['score'])}

                **Details:** {stage['details']}
                """)

    # Add feedback section
    st.markdown("---")
    st.header("Journey Feedback")
    st.markdown("""
    Help us improve the certification journey by sharing your experience
    at different stages of the process.
    """)

    feedback_stage = st.selectbox(
        "Select Journey Stage",
        options=[stage['name'] for stage in journey_stages]
    )

    feedback_text = st.text_area(
        "Share Your Experience",
        placeholder="Tell us about your experience at this stage..."
    )

    satisfaction = st.slider(
        "Rate Your Satisfaction",
        min_value=1,
        max_value=5,
        value=3,
        help="1: Very Dissatisfied, 5: Very Satisfied"
    )

    if st.button("Submit Feedback"):
        st.success("Thank you for your feedback! Your input helps us improve the platform.")

if __name__ == "__main__":
    run()