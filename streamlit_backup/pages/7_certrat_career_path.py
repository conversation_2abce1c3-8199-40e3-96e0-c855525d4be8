"""
CertRat CareerPath visualization page
"""
import streamlit as st
import plotly.graph_objects as go
import logging
from typing import Dict, Any, List, Optional, TypedDict, Union
from translations import _, init_localization
from components.icons import get_icon
from database import init_db, get_db
from models.certification import Certification
from utils.claude_assistant import generate_career_path, analyze_certification_alignment
from utils.pdf_report import generate_career_path_report
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import text
from datetime import datetime
from utils.user_profile_manager import UserProfileManager

# Initialize translations
init_localization()

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

class CareerPathStage(TypedDict):
    stage: str
    timeline: str
    certifications: List[str]

class CareerPathResponse(TypedDict):
    career_path: List[CareerPathStage]
    status: str
    message: Optional[str]

def create_career_path_chart(path_data: Dict[str, Any]) -> go.Figure:
    """Create a timeline visualization of the career path"""
    try:
        logger.info("Creating career path chart")
        stages: List[CareerPathStage] = path_data.get('career_path', [])

        # Prepare data for timeline
        y_positions = list(range(len(stages)))
        x_positions = list(range(len(stages)))

        # Create the timeline
        fig = go.Figure()

        # Add connecting lines with timeline labels
        for i in range(len(stages)-1):
            fig.add_trace(go.Scatter(
                x=x_positions[i:i+2],
                y=y_positions[i:i+2],
                mode='lines',
                line=dict(color='#1e88e5', width=2),
                showlegend=False
            ))

            fig.add_annotation(
                x=(x_positions[i] + x_positions[i+1])/2,
                y=(y_positions[i] + y_positions[i+1])/2,
                text=f"{get_icon('timeline')} {stages[i]['timeline']}",
                showarrow=False,
                yshift=20,
                font=dict(color='#666666', size=10)
            )

        # Add stages as points with certification details
        for i, stage in enumerate(stages):
            cert_text = f"<b>{get_icon('education')} Stage {i+1}</b><br>{stage['stage']}<br>"
            certs = stage['certifications']
            cert_text += "<br>".join([f"{get_icon('certification')} {cert}" for cert in certs])

            fig.add_trace(go.Scatter(
                x=[x_positions[i]],
                y=[y_positions[i]],
                mode='markers+text',
                name=f'Stage {i+1}',
                marker=dict(
                    size=30,
                    color='#7792E3',
                    line=dict(color='#1e88e5', width=2)
                ),
                text=[cert_text],
                textposition="top center",
                textfont=dict(size=12),
                hoverinfo='text',
                hoverlabel=dict(bgcolor='white')
            ))

        # Update layout for better visibility
        fig.update_layout(
            showlegend=False,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            xaxis=dict(showgrid=False, showticklabels=False, zeroline=False),
            yaxis=dict(showgrid=False, showticklabels=False, zeroline=False),
            margin=dict(l=50, r=50, t=100, b=50),
            height=400,
            hovermode='closest'
        )

        return fig
    except Exception as e:
        logger.error(f"Error creating career path chart: {str(e)}")
        raise

def init_database():
    """Initialize database connection with retry logic"""
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            init_db()
            # Test the connection
            db = next(get_db())
            db.execute(text("SELECT 1"))
            logger.info("Database connection successful")
            return True
        except Exception as e:
            retry_count += 1
            logger.error(f"Database connection attempt {retry_count} failed: {str(e)}")
            if retry_count == max_retries:
                return False
            import time
            time.sleep(1)  # Add delay between retries

def run():
    """Main function to run the CertRat CareerPath page"""
    try:
        if not init_database():
            st.error(_("Unable to connect to the database. Please try refreshing the page."))
            return

        st.title(f"{get_icon('cybersecurity')} {_('CertRat CareerPath')}")

        # Add description with better styling
        st.markdown(_("""
        <div style='background-color: #1E1E1E; padding: 20px; border-radius: 10px; margin-bottom: 25px;'>
        <h4 style='color: #FFFFFF;'>{} Welcome to CertRat CareerPath!</h4>
        <p style='color: #E0E0E0;'>{} Whether you're just starting in cybersecurity or advancing your career, 
        we'll help you choose the right certifications in the right order.</p>
        </div>
        """).format(get_icon('path'), get_icon('goals')), unsafe_allow_html=True)

        # Get user profile data
        user_profile = UserProfileManager.get_profile()

        try:
            # Database session
            db = next(get_db())
            db.execute(text("SELECT 1"))

            certifications = db.query(Certification).all()
            if not certifications:
                st.error(_("No certification data available. Please try again later."))
                return

            cert_data = [cert.to_dict() for cert in certifications]

            # Create main form container with custom styling
            with st.container():
                st.subheader(f"{get_icon('user')} {_('Your Profile')}")

                # Get domains and certification names
                domains = sorted(set(cert.domain for cert in certifications))
                cert_names = sorted(set(cert.name for cert in certifications))

                # Completed Certifications with pill-style selection
                completed_certs = st.multiselect(
                    f"{get_icon('education')} {_('Completed Certifications')}",
                    options=cert_names,
                    default=user_profile.get('completed_certifications', []) if user_profile else None,
                    help=_("Select any security certifications you've already completed"),
                    key="completed_certs"
                )

                # Domains of Interest
                interests = st.multiselect(
                    f"{get_icon('skills')} {_('Security Domains of Interest')}",
                    options=domains,
                    default=user_profile.get('expertise_areas', []) if user_profile else None,
                    help=_("Select the security domains you're most interested in"),
                    key="interests"
                )

                # Two columns for experience and roles
                col1, col2 = st.columns(2)

                with col1:
                    years_experience = st.number_input(
                        f"{get_icon('experience')} {_('Years of Experience')}",
                        min_value=0,
                        max_value=50,
                        value=user_profile.get('years_experience', 0) if user_profile else 0,
                        help=_("Enter your years of experience in security or related fields"),
                        key="years_exp"
                    )

                    current_role = st.text_input(
                        f"{get_icon('user')} {_('Current Role')}",
                        value=user_profile.get('user_role', '') if user_profile else '',
                        help=_("What is your current job role?"),
                        key="current_role"
                    )

                with col2:
                    target_role = st.text_input(
                        f"{get_icon('goals')} {_('Target Role')}",
                        value=user_profile.get('desired_role', '') if user_profile else '',
                        help=_("What role do you want to achieve? (e.g., Security Engineer, CISO)"),
                        key="target_role"
                    )

                    learning_style = st.selectbox(
                        f"{get_icon('study')} {_('Preferred Learning Style')}",
                        options=["Visual", "Reading", "Hands-on", "Mixed"],
                        index=["Visual", "Reading", "Hands-on", "Mixed"].index(
                            user_profile.get('preferred_learning_style', 'Mixed')
                        ) if user_profile and user_profile.get('preferred_learning_style') else 3,
                        help=_("How do you learn best?"),
                        key="learning_style"
                    )

                # Study hours with min/max validation
                study_time = st.number_input(
                    f"{get_icon('duration')} {_('Available Study Hours per Week')}",
                    min_value=1,
                    max_value=168,
                    value=user_profile.get('study_time_available', 10) if user_profile else 10,
                    help=_("How many hours can you dedicate to studying each week?"),
                    key="study_hours"
                )

                # Action buttons with proper styling
                st.markdown("<div style='padding: 20px 0px;'>", unsafe_allow_html=True)
                col1, col2 = st.columns([1, 2])

                with col1:
                    if st.button(f"{get_icon('path')} {_('Generate Career Path')}", type="primary", key="generate_btn"):
                        if not (interests and target_role):
                            st.error(_("Please fill in all required fields"))
                            return

                        with st.spinner(_("Analyzing your profile and generating recommendations...")):
                            try:
                                recommendations: CareerPathResponse = generate_career_path(
                                    interests=interests,
                                    experience_level="Entry Level" if years_experience < 2 else 
                                                 "Mid Level" if years_experience < 5 else 
                                                 "Senior Level" if years_experience < 10 else "Expert",
                                    certifications=cert_data,
                                    target_role=target_role,
                                    context_id="default"
                                )

                                if recommendations.get("status") == "error":
                                    st.error(_(recommendations["message"]))
                                    return

                                st.session_state.recommendations = recommendations
                                st.session_state.user_exp = {
                                    'years_experience': years_experience,
                                    'current_role': current_role,
                                    'learning_style': learning_style,
                                    'study_time': study_time
                                }
                                st.session_state.learning_path = {
                                    'interests': interests,
                                    'target_role': target_role
                                }

                                st.success(_("Career path generated successfully!"))

                                if completed_certs:
                                    with st.expander(f"{get_icon('analysis')} {_('Current Certification Analysis')}", expanded=True):
                                        alignment = analyze_certification_alignment(
                                            current_certs=completed_certs,
                                            target_role=target_role,
                                            cert_data=cert_data,
                                            context_id="default"
                                        )

                                        if "error" not in alignment:
                                            st.metric(
                                                _("Alignment Score"), 
                                                f"{alignment['alignment_score']*100:.0f}%"
                                            )

                                            col1, col2 = st.columns(2)
                                            with col1:
                                                st.write(f"{get_icon('success')} {_('Strengths:')}")
                                                for strength in alignment['strengths']:
                                                    st.markdown(f"✅ {strength}")

                                            with col2:
                                                st.write(f"{get_icon('warning')} {_('Gaps to Address:')}")
                                                for gap in alignment['gaps']:
                                                    st.markdown(f"❗ {gap}")

                                st.subheader(f"{get_icon('timeline')} {_('Your Career Path Timeline')}")
                                try:
                                    fig = create_career_path_chart(recommendations)
                                    st.plotly_chart(fig, use_container_width=True)
                                except Exception as e:
                                    st.warning(_("Unable to display visualization"))

                                st.subheader(f"{get_icon('path')} {_('Detailed Career Path')}")
                                for i, stage in enumerate(recommendations["career_path"], 1):
                                    with st.expander(f"{get_icon('education')} Stage {i}: {stage['stage']}", expanded=True):
                                        st.info(f"{get_icon('timeline')} Timeline: {stage['timeline']}")
                                        st.write(f"{get_icon('certification')} Certifications:")
                                        for cert in stage["certifications"]:
                                            st.markdown(f"- {cert}")

                            except Exception as e:
                                st.error(_("Failed to generate career path. Please try again."))

                with col2:
                    if hasattr(st.session_state, 'recommendations'):
                        if st.button(f"{get_icon('download')} {_('Download PDF Report')}", key="download_btn"):
                            try:
                                with st.spinner(_("Generating PDF report...")):
                                    cert_costs = {}
                                    for cert_name in [cert for stage in st.session_state.recommendations['career_path'] 
                                                    for cert in stage['certifications']]:
                                        cert_data = next((cert for cert in certifications 
                                                        if cert.name == cert_name), None)
                                        if cert_data:
                                            cert_costs[cert_name] = {
                                                'exam': float(cert_data.cost or 0),
                                                'materials': float(cert_data.custom_hours or 0) * 50,
                                                'training': float(cert_data.custom_hours or 0) * 100
                                            }

                                    recommendations = st.session_state.recommendations.copy()
                                    for stage in recommendations['career_path']:
                                        stage['certification_costs'] = {
                                            cert: cert_costs.get(cert, {
                                                'exam': 0,
                                                'materials': 0,
                                                'training': 0
                                            }) for cert in stage['certifications']
                                        }

                                    pdf_buffer = generate_career_path_report(
                                        user_exp=st.session_state.user_exp,
                                        learning_path=st.session_state.learning_path,
                                        recommendations=recommendations
                                    )

                                    st.download_button(
                                        label=f"{get_icon('download')} {_('Download Career Path PDF')}",
                                        data=pdf_buffer,
                                        file_name="career_path.pdf",
                                        mime="application/pdf"
                                    )
                                    st.success(_("PDF generated successfully!"))
                            except Exception as e:
                                st.error(_("Failed to generate PDF. Please try again."))

        except SQLAlchemyError as e:
            st.error(_("Database connection error. Please try refreshing the page."))
            return

    except Exception as e:
        st.error(_("Service temporarily unavailable"))

if __name__ == "__main__":
    run()