"""
Certification Path Visualization Page
"""
import streamlit as st
from database import get_db
from models.certification import Certification
from components.visualization import display_certification_visualizations
from translations import _, translate_page
from typing import Dict, Any, Sequence, Optional
from utils.job_domain_mapper import get_certification_job_matches

def get_certifications(db, filters: Optional[Dict[str, Any]] = None) -> Sequence[Dict[str, Any]]:
    """Get certifications with optional filters"""
    query = db.query(Certification)

    if filters:
        if filters.get('domains'):
            query = query.filter(Certification.domain.in_(filters['domains']))

    return [cert.to_dict() for cert in query.all()]

@translate_page
def main():
    st.title(_("Certification Path Visualization"))
    st.markdown(_("""
    Explore certification paths and relationships through an interactive visualization.
    The graph shows certification progression paths and domain relationships.
    """))

    # Sidebar filters
    st.sidebar.title(_("Visualization Filters"))

    # Get database session
    db = next(get_db())

    # Get unique domains for filtering
    all_certs = get_certifications(db)
    domains = sorted(set(cert['domain'] for cert in all_certs))

    # Initialize session state for selected domains
    if 'selected_domains' not in st.session_state:
        st.session_state.selected_domains = set()

    # Domain filter buttons
    st.sidebar.subheader(_("Focus Domain"))
    cols = st.sidebar.columns(2)
    for idx, domain in enumerate(domains):
        col = cols[idx % 2]
        if col.button(
            _(domain),
            type="secondary" if domain in st.session_state.selected_domains else "primary",
            key=f"domain_{domain}"
        ):
            if domain in st.session_state.selected_domains:
                st.session_state.selected_domains.remove(domain)
            else:
                st.session_state.selected_domains = {domain}  # Only allow one domain
            st.rerun()

    # Apply domain filter
    filters = {}
    if st.session_state.selected_domains:
        filters['domains'] = list(st.session_state.selected_domains)

    filtered_certs = get_certifications(db, filters)

    # Display visualization
    if filtered_certs:
        selected_domain = list(st.session_state.selected_domains)[0] if st.session_state.selected_domains else None

        # Get job roles for selected domain
        if selected_domain:
            domain_info = get_certification_job_matches(selected_domain, db)

            # Display job roles section
            st.markdown("### 💼 Related Job Roles")
            for job in domain_info['jobs']:
                with st.expander(f"🎯 {job['title']}"):
                    st.write(f"**Description:** {job['description']}")
                    st.write("**Key Responsibilities:**")
                    for resp in job['key_responsibilities']:
                        st.write(f"- {resp}")

            st.markdown("---")

        # Display certification visualization
        display_certification_visualizations(
            {cert['name']: {
                'level': cert['level'],
                'domain': cert['domain'],
                'difficulty': cert['difficulty'],
                'cost': cert['cost'],
                'description': cert['description'],
                'prerequisites': cert['prerequisites'],
                'issuing_organization': cert.get('organization', {}).get('name', 'Unknown'),
                'url': cert['url'],
                'related_certs': {}  # This will be populated from roadmap data
            } for cert in filtered_certs},
            selected_domain
        )
    else:
        st.info(_("Select a domain to visualize its certification paths."))

if __name__ == "__main__":
    main()