"""
Mermaid diagram component for Streamlit
"""
import streamlit as st
import streamlit.components.v1 as components
from typing import List, Dict, Optional, Any, Union

def render_mermaid_diagram(diagram_definition: str, height: int = 400) -> None:
    """
    Render a Mermaid diagram in Streamlit using HTML component.

    Args:
        diagram_definition: The Mermaid diagram code
        height: Height of the diagram container in pixels
    """
    # Clean up whitespace and normalize line endings
    diagram_definition = diagram_definition.strip().replace('\r\n', '\n')

    # Add CSS for better diagram display
    st.markdown("""
        <style>
        .mermaid {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        </style>
    """, unsafe_allow_html=True)

    # Render the diagram with proper initialization
    components.html(
        f"""
        <!DOCTYPE html>
        <html>
        <body>
            <pre class="mermaid">
                {diagram_definition}
            </pre>
            <script src="https://cdn.jsdelivr.net/npm/mermaid@9.4.3/dist/mermaid.min.js"></script>
            <script>
                mermaid.initialize({{
                    startOnLoad: true,
                    theme: 'default',
                    logLevel: 'error',
                    securityLevel: 'loose',
                    journey: {{
                        diagramMarginX: 50,
                        diagramMarginY: 20,
                        taskMargin: 50,
                        height: 40,
                        width: 150,
                        boxMargin: 10,
                        boxTextMargin: 5,
                        topMargin: 50,
                        rightMargin: 25,
                        leftMargin: 25,
                        bottomMargin: 50
                    }}
                }});
            </script>
        </body>
        </html>
        """,
        height=height
    )

def generate_journey_diagram(stages: List[Dict[str, Union[str, int]]]) -> str:
    """
    Generate a Mermaid journey diagram from a list of stages.

    Args:
        stages: List of dictionaries containing stage information.
               Each dict should have 'name', 'task', and 'score' keys.

    Returns:
        A string containing the Mermaid journey diagram definition.
    """
    # Start with basic journey setup
    diagram_lines = [
        "journey",
        "title Certification Journey Map",
        "",
        "section Journey Progression"
    ]

    # Add each stage with proper formatting
    for stage in stages:
        # Add the task with its satisfaction score
        diagram_lines.append(f"  {stage['task']}: {stage['score']}")

    # Join all lines with newlines
    return "\n".join(diagram_lines)

def journey_diagram(
    title: str,
    actors: List[str],
    stages: List[Dict[str, Any]],
    height: Optional[int] = None
) -> None:
    """
    Create and render a user journey diagram.

    Args:
        title: Title of the journey
        actors: List of actors/users in the journey
        stages: List of dictionaries containing stage information
               Each dict should have: 'name', 'tasks', and 'scores' keys
    """
    try:
        diagram_code = generate_journey_diagram(stages)
        render_mermaid_diagram(diagram_code, height or 400)
    except Exception as e:
        st.error(f"Error rendering diagram: {str(e)}")
        st.code(diagram_code, language="mermaid")