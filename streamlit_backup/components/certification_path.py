"""
D3.js-powered certification path visualization component
"""
import streamlit as st
import streamlit.components.v1 as components
import json

def create_cert_path_html(certifications, selected_domain=None):
    """
    Create HTML/JavaScript for D3.js certification path visualization
    """
    # Filter certifications by domain if specified
    certs_data = {
        name: data for name, data in certifications.items()
        if not selected_domain or data.get('domain') == selected_domain
    }

    # Create nodes and links for D3.js
    nodes = []
    links = []
    domain_groups = {}

    # Convert difficulty to numeric values
    difficulty_map = {
        'Low': 1,
        'Medium': 2,
        'High': 3
    }

    # Create nodes and group by domain
    for name, data in certs_data.items():
        domain = data.get('domain', 'General')
        if domain not in domain_groups:
            domain_groups[domain] = []

        # Convert text difficulty to numeric
        difficulty = difficulty_map.get(data.get('difficulty', 'Low'), 1)

        node = {
            'id': name,
            'level': data.get('level', 'Entry Level'),
            'domain': domain,
            'difficulty': difficulty,  # Now using numeric difficulty
            'difficulty_text': data.get('difficulty', 'Low')  # Keep text version for tooltip
        }
        nodes.append(node)
        domain_groups[domain].append(node)

        # Create links from relationships
        relationships = data.get('related_certs', {})
        for prereq in relationships.get('prerequisites', []):
            if prereq in certs_data:
                links.append({
                    'source': prereq,
                    'target': name,
                    'type': 'prerequisite'
                })

        for leads_to in relationships.get('leads_to', []):
            if leads_to in certs_data:
                links.append({
                    'source': name,
                    'target': leads_to,
                    'type': 'progression'
                })

    # Add domain grouping links
    for domain, group in domain_groups.items():
        for i, node1 in enumerate(group):
            if i + 1 < len(group):
                links.append({
                    'source': node1['id'],
                    'target': group[i + 1]['id'],
                    'type': 'domain'
                })

    # Convert data to JSON for embedding in JavaScript
    json_data = json.dumps({'nodes': nodes, 'links': links})

    html = '''
    <div id="cert-path-container">
        <div id="loading-bar" style="
            width: 100%;
            height: 4px;
            background: #f0f0f0;
            position: relative;
            margin-bottom: 10px;
            display: block;
        ">
            <div id="progress" style="
                width: 0%;
                height: 100%;
                background: #4CAF50;
                position: absolute;
                transition: width 0.1s ease;
            "></div>
        </div>
        <div id="cert-path-viz" style="width: 100%; height: 800px;"></div>
    </div>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script>
    try {
        // Initialize data from Python
        const data = ''' + json_data + ''';
        console.log("Loaded data:", data);

        // Loading progress elements
        const loadingBar = document.getElementById('loading-bar');
        const progressBar = document.getElementById('progress');

        // Set up the SVG with responsive sizing
        const container = document.getElementById('cert-path-viz');
        const width = container.clientWidth;
        const height = container.clientHeight;

        console.log("Container dimensions:", width, height);

        // Create SVG with viewBox for responsiveness
        const svg = d3.select('#cert-path-viz')
            .append('svg')
            .attr('viewBox', `0 0 ${width} ${height}`)
            .attr('preserveAspectRatio', 'xMidYMid meet')
            .style('width', '100%')
            .style('height', '100%');

        // Define arrow marker
        svg.append('defs').append('marker')
            .attr('id', 'arrowhead')
            .attr('viewBox', '-10 -10 20 20')
            .attr('refX', 20)
            .attr('refY', 0)
            .attr('markerWidth', 6)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .append('path')
            .attr('d', 'M-6,-6 L 0,0 L -6,6')
            .attr('fill', '#999');

        // Define vertical level positions
        const levelPositions = {
            'Entry Level': height * 0.2,
            'Intermediate': height * 0.4,
            'Advanced': height * 0.6,
            'Expert': height * 0.8
        };

        // Define domain colors
        const domainColors = {
            'Offensive Security': '#ff7676',
            'Defensive Security': '#76b0ff',
            'Security Engineering': '#76ff7a',
            'Security Management': '#ffd976',
            'Network Security': '#ff76eb',
            'Security Testing': '#76ffd9',
            'Identity & Access Management': '#d976ff',
            'Asset Security': '#fff176'
        };

        // Performance optimization: Cache node positions
        const nodePositions = new Map(data.nodes.map(node => [
            node.id,
            {
                x: width / 2 + (Math.random() - 0.5) * 200,
                y: levelPositions[node.level] || height * 0.5
            }
        ]));

        console.log("Initial node positions calculated");

        // Set up forces with optimized parameters
        const simulation = d3.forceSimulation(data.nodes)
            .force('link', d3.forceLink(data.links)
                .id(d => d.id)
                .distance(d => d.type === 'domain' ? 150 : 200)
                .strength(d => d.type === 'domain' ? 0.1 : 0.3))
            .force('charge', d3.forceManyBody()
                .strength(d => -300 - d.difficulty * 100)
                .distanceMax(300))
            .force('center', d3.forceCenter(width / 2, height / 2))
            .force('y', d3.forceY().strength(0.3).y(d => levelPositions[d.level] || height * 0.5))
            .force('collide', d3.forceCollide().radius(d => 25 + d.difficulty * 5).strength(0.7))
            .velocityDecay(0.4);  // Add decay to stabilize faster

        console.log("Force simulation initialized");

        // Create links with different styles for different types
        const link = svg.append('g')
            .selectAll('line')
            .data(data.links)
            .join('line')
            .attr('stroke', d => d.type === 'domain' ? '#ddd' : '#999')
            .attr('stroke-opacity', d => d.type === 'domain' ? 0.3 : 0.6)
            .attr('stroke-width', d => {
                if (d.type === 'prerequisite') { return 2; }
                if (d.type === 'domain') { return 1; }
                return 1.5;
            })
            .attr('stroke-dasharray', d => d.type === 'domain' ? '3,3' : 'none')
            .attr('marker-end', d => d.type !== 'domain' ? 'url(#arrowhead)' : '');

        // Create nodes with cached positions
        const node = svg.append('g')
            .selectAll('g')
            .data(data.nodes)
            .join('g')
            .attr('transform', d => {
                const pos = nodePositions.get(d.id);
                return `translate(${pos.x},${pos.y})`;
            });

        // Map difficulty levels to numeric values
        const baseSize = 25;  // Base size for difficulty level 1
        const sizeIncrement = 15;  // Size increment per difficulty level

        // Add circles to nodes with improved size scaling
        node.append('circle')
            .attr('r', d => baseSize + (d.difficulty * sizeIncrement))  // Now using numeric difficulty
            .attr('fill', d => domainColors[d.domain] || '#999')
            .attr('stroke', '#fff')
            .attr('stroke-width', 2);

        // Adjust label positioning based on new node sizes
        node.append('text')
            .text(d => d.id)
            .attr('x', d => baseSize + (d.difficulty * sizeIncrement) + 10)  // Position label outside circle
            .attr('y', 5)
            .style('font-size', '12px')
            .style('fill', '#333');

        // Add tooltips
        node.append('title')
            .text(d => `${d.id}\nLevel: ${d.level}\nDomain: ${d.domain}\nDifficulty: ${d.difficulty_text}`);

        // Add zoom behavior
        const zoom = d3.zoom()
            .scaleExtent([0.5, 2])
            .on('zoom', (event) => {
                svg.selectAll('g').attr('transform', event.transform);
            });

        svg.call(zoom);

        // Add drag behavior with improved error handling
        node.call(d3.drag()
            .on('start', function(event) {
                if (!event.active) simulation.alphaTarget(0.3).restart();
                const d = event.subject;
                d.fx = d.x;
                d.fy = d.y;
            })
            .on('drag', function(event) {
                const d = event.subject;
                d.fx = event.x;
                d.fy = event.y;
            })
            .on('end', function(event) {
                if (!event.active) simulation.alphaTarget(0);
                const d = event.subject;
                d.fx = null;
                d.fy = null;
            }));

        // Performance optimization: Limit simulation ticks
        let tickCount = 0;
        const maxTicks = 300;
        let isSimulationRunning = true;

        // Update positions on each tick with limited iterations
        simulation.on('tick', () => {
            if (!isSimulationRunning) return;

            tickCount++;
            if (tickCount > maxTicks) {
                simulation.stop();
                isSimulationRunning = false;
                // Hide loading bar when simulation completes
                loadingBar.style.display = 'none';
                return;
            }

            // Update progress bar
            const progress = (tickCount / maxTicks) * 100;
            progressBar.style.width = `${progress}%`;

            // Safely update positions
            try {
                link
                    .attr('x1', d => d.source.x)
                    .attr('y1', d => d.source.y)
                    .attr('x2', d => d.target.x)
                    .attr('y2', d => d.target.y);

                node.attr('transform', d => `translate(${d.x},${d.y})`);
            } catch (error) {
                console.error('Error updating positions:', error);
                isSimulationRunning = false;
                simulation.stop();
            }
        });


        console.log("Graph initialization complete");
    } catch (error) {
        console.error("Error initializing graph:", error);
        document.getElementById('cert-path-viz').innerHTML = 
            `<div style="color: red; padding: 20px;">Error loading visualization: ${error.message}</div>`;
    }
    </script>
    '''

    return html

def display_certification_path(certifications, selected_domain=None):
    """
    Display the D3.js certification path visualization
    """
    st.write("### Certification Progression Path")

    # Create container with increased height
    container = st.container()
    with container:
        # Create and display the visualization
        html = create_cert_path_html(certifications, selected_domain)
        components.html(html, height=850)

    # Add legend
    st.write("#### Legend")
    cols = st.columns(4)

    domain_colors = {
        'Offensive Security': '#ff7676',
        'Defensive Security': '#76b0ff',
        'Security Engineering': '#76ff7a',
        'Security Management': '#ffd976',
        'Network Security': '#ff76eb',
        'Security Testing': '#76ffd9',
        'Identity & Access Management': '#d976ff',
        'Asset Security': '#fff176'
    }

    for i, (domain, color) in enumerate(domain_colors.items()):
        col = cols[i % 4]
        col.markdown(
            f'<div style="display: flex; align-items: center;">'
            f'<div style="width: 20px; height: 20px; background-color: {color}; '
            f'border-radius: 50%; margin-right: 8px;"></div>'
            f'{domain}</div>',
            unsafe_allow_html=True
        )