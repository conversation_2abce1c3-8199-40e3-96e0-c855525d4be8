"""
Visualization components for certification paths
"""
import streamlit as st
import plotly.graph_objects as go
from components.estimates import display_estimates
from components.certification_path import display_certification_path

def display_certification_details(cert_name, cert_data):
    """
    Display detailed information about a certification
    """
    st.subheader(cert_name)

    col1, col2 = st.columns(2)

    with col1:
        st.write("**Level:** ", cert_data['level'])
        st.write("**Domain:** ", cert_data['domain'])
        st.write("**Difficulty:** ", cert_data['difficulty'])

    with col2:
        st.write("**Cost:** $", cert_data['cost'])
        st.write("**Prerequisites:** ", cert_data['prerequisites'] or "None")
        st.write("**Issuing Organization:** ", cert_data['issuing_organization'])

    st.write("**Description:** ", cert_data['description'])
    if cert_data.get('exam_code'):
        st.write("**Exam Code:** ", cert_data['exam_code'])
    if cert_data.get('validity_period'):
        st.write("**Validity Period:** ", f"{cert_data['validity_period']} months")

    # Add cost and time estimates
    st.markdown("---")
    display_estimates(cert_name, cert_data)

def display_certification_visualizations(certifications, selected_domain=None):
    """
    Display all visualization components for certifications
    """
    # Show the interactive certification path
    display_certification_path(certifications, selected_domain)

    # Add any additional visualization components here
    pass