"""
Streamlit components for certification filters
"""
import streamlit as st
from data.certifications import DOMAINS, LEVELS, DIFFICULTIES

def render_filters():
    """
    Render filter components and return selected values
    """
    st.sidebar.header("Filter Certifications")
    
    # Experience Level filter
    level = st.sidebar.selectbox(
        "Experience Level",
        ["All"] + <PERSON>E<PERSON><PERSON>
    )
    
    # Domain filter
    domain = st.sidebar.selectbox(
        "Domain",
        ["All"] + DOMAINS
    )
    
    # Difficulty filter
    difficulty = st.sidebar.selectbox(
        "Difficulty",
        ["All"] + DIFFICULTIES
    )
    
    # Cost filter
    max_cost = st.sidebar.slider(
        "Maximum Cost ($)",
        0, 2000, 2000
    )
    
    return {
        "level": None if level == "All" else level,
        "domain": None if domain == "All" else domain,
        "difficulty": None if difficulty == "All" else difficulty,
        "max_cost": max_cost
    }
