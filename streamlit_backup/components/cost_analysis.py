"""
Component for detailed cost analysis and currency conversion
"""
from typing import Dict, List, Optional, TypedDict
import requests
from datetime import datetime, timedelta
import streamlit as st

class ExchangeRates(TypedDict):
    """Type definition for exchange rate data"""
    base: str
    rates: Dict[str, float]
    timestamp: float

def get_exchange_rates(base_currency: str = "USD") -> ExchangeRates:
    """
    Get current exchange rates from the Exchange Rates API.
    Caches results for 24 hours to avoid excessive API calls.
    """
    # Check if we have cached rates
    if 'exchange_rates' not in st.session_state:
        st.session_state.exchange_rates = {}
    
    cache_key = f"rates_{base_currency}"
    now = datetime.now()
    
    # Return cached rates if available and less than 24 hours old
    if (cache_key in st.session_state.exchange_rates and 
        'timestamp' in st.session_state.exchange_rates[cache_key] and
        now - st.session_state.exchange_rates[cache_key]['timestamp'] < timedelta(hours=24)):
        return st.session_state.exchange_rates[cache_key]
    
    # For demo/development, use static rates to avoid API limits
    # In production, uncomment the API call below
    static_rates = {
        "USD": 1.0,
        "EUR": 0.92,
        "GBP": 0.79,
        "CAD": 1.35,
        "AUD": 1.52,
        "JPY": 150.13,
        "CHF": 0.88,
        "CNY": 7.19,
        "INR": 82.97,
        "NZD": 1.64
    }
    
    rates_data = {
        "base": base_currency,
        "rates": static_rates,
        "timestamp": datetime.now().timestamp()
    }
    
    # Cache the results
    st.session_state.exchange_rates[cache_key] = {
        "base": rates_data["base"],
        "rates": rates_data["rates"],
        "timestamp": now
    }
    
    return rates_data

def calculate_retake_costs(
    exam_cost: float,
    retake_policy: Dict[str, any],
    num_attempts: int = 2
) -> Dict[str, float]:
    """
    Calculate potential retake costs based on certification retake policy
    
    Args:
        exam_cost: Base cost of the exam
        retake_policy: Dictionary containing retake policy details
        num_attempts: Number of potential attempts to calculate for
        
    Returns:
        Dictionary with cost breakdown for retakes
    """
    costs = {
        "base_exam": exam_cost,
        "retake_fees": [],
        "total_potential_cost": exam_cost
    }
    
    # Default retake policy if none specified
    default_policy = {
        "waiting_period": 14,  # days
        "discount": 0.0,       # percentage
        "max_attempts": 3
    }
    
    policy = {**default_policy, **retake_policy}
    
    # Calculate costs for each potential retake
    for attempt in range(2, num_attempts + 1):
        if attempt <= policy["max_attempts"]:
            retake_cost = exam_cost * (1 - policy["discount"])
            costs["retake_fees"].append(retake_cost)
            costs["total_potential_cost"] += retake_cost
            
    return costs

def format_currency(
    amount: float,
    currency: str = "USD",
    show_symbol: bool = True
) -> str:
    """Format currency amount with appropriate symbol and formatting"""
    symbols = {
        "USD": "$", "EUR": "€", "GBP": "£",
        "CAD": "C$", "AUD": "A$", "JPY": "¥",
        "CHF": "CHF", "CNY": "¥", "INR": "₹",
        "NZD": "NZ$"
    }
    
    if currency == "JPY":
        formatted = f"{int(amount):,}"
    else:
        formatted = f"{amount:,.2f}"
        
    if show_symbol:
        return f"{symbols.get(currency, currency)} {formatted}"
    return formatted
