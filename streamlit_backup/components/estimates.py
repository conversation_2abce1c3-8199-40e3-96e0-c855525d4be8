"""
Component for certification cost and time estimates
"""
import streamlit as st

def calculate_total_cost(cert_data):
    """
    Calculate total estimated cost including exam fee, study materials, and training
    """
    exam_fee = cert_data['cost']
    
    # Estimate study materials cost based on difficulty
    materials_cost = {
        'Low': 100,
        'Medium': 250,
        'High': 500
    }.get(cert_data['difficulty'], 200)
    
    # Estimate training cost based on difficulty
    training_cost = {
        'Low': 0,
        'Medium': 1000,
        'High': 2500
    }.get(cert_data['difficulty'], 1000)
    
    return {
        'Exam Fee': exam_fee,
        'Study Materials': materials_cost,
        'Training (Optional)': training_cost,
        'Total (with training)': exam_fee + materials_cost + training_cost,
        'Total (self-study)': exam_fee + materials_cost
    }

def estimate_study_time(cert_data):
    """
    Estimate study time based on difficulty and prerequisites
    """
    # Base study hours by difficulty
    base_hours = {
        'Low': 40,
        'Medium': 80,
        'High': 160
    }.get(cert_data['difficulty'], 80)
    
    # Adjust based on prerequisites
    if cert_data['prerequisites'] and 'experience' in cert_data['prerequisites'].lower():
        base_hours *= 0.8  # Reduce time if person has experience
    
    return {
        'Hours per Week': 10,
        'Total Study Hours': base_hours,
        'Estimated Weeks': round(base_hours / 10),
        'Recommended Timeline': f"{round(base_hours / 10)} weeks at 10 hours/week"
    }

def display_estimates(cert_name, cert_data):
    """
    Display cost and time estimates for a certification
    """
    st.write("### Cost Estimates 💰")
    
    # Calculate and display costs
    costs = calculate_total_cost(cert_data)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Breakdown:**")
        for item, cost in costs.items():
            if item != 'Total (with training)' and item != 'Total (self-study)':
                st.write(f"- {item}: ${cost:,}")
    
    with col2:
        st.write("**Total Cost Options:**")
        st.write(f"- Self-study path: ${costs['Total (self-study)']:,}")
        st.write(f"- With training: ${costs['Total (with training)']:,}")
    
    st.write("### Time Estimates ⏱️")
    
    # Calculate and display time estimates
    time_est = estimate_study_time(cert_data)
    
    col3, col4 = st.columns(2)
    
    with col3:
        st.write("**Study Plan:**")
        st.write(f"- Recommended hours per week: {time_est['Hours per Week']}")
        st.write(f"- Total study hours: {time_est['Total Study Hours']}")
    
    with col4:
        st.write("**Timeline:**")
        st.write(f"- Estimated completion time: {time_est['Recommended Timeline']}")
    
    # Add validity period if available
    st.write("### Certification Validity ✔️")
    st.write("- Most security certifications require renewal every 3 years")
    st.write("- Renewal typically requires continuing education credits or re-examination")
    
    # Add study tips based on difficulty
    st.write("### Study Tips 📚")
    if cert_data['difficulty'] == 'Low':
        st.info("This certification is suitable for self-study with online resources.")
    elif cert_data['difficulty'] == 'Medium':
        st.warning("Consider combining self-study with online training courses.")
    else:
        st.error("Recommended to enroll in formal training or bootcamp programs.")
