"""Onboarding tutorial component for new users"""
import streamlit as st
from typing import Dict, Any, Optional
from components.icons import get_icon
from utils.user_profile_manager import UserProfileManager

class OnboardingTutorial:
    """Interactive onboarding tutorial for new users"""
    
    TUTORIAL_STEPS = {
        'welcome': {
            'title': 'Welcome to Security Certification Explorer!',
            'content': """
            👋 Welcome! This platform helps you plan your cybersecurity certification journey.
            
            Let's take a quick tour to help you get started.
            """,
            'icon': 'education'
        },
        'profile': {
            'title': 'Set Up Your Profile',
            'content': """
            Your profile helps us personalize recommendations:
            - Current experience level
            - Areas of interest
            - Study preferences
            """,
            'icon': 'user'
        },
        'visualization': {
            'title': 'Certification Path Visualization',
            'content': """
            Explore certification paths visually:
            - See relationships between certifications
            - Track your progress
            - Understand prerequisites
            """,
            'icon': 'path'
        },
        'career': {
            'title': 'Career Path Planning',
            'content': """
            Plan your career progression:
            - Set career goals
            - Get personalized certification recommendations
            - Track ROI and learning milestones
            """,
            'icon': 'goals'
        },
        'study': {
            'title': 'Study Planning',
            'content': """
            Optimize your study approach:
            - Calculate study time requirements
            - Access study resources
            - Track progress
            """,
            'icon': 'study'
        }
    }

    @staticmethod
    def show() -> None:
        """Display the onboarding tutorial"""
        # Initialize session state for tutorial
        if 'tutorial_step' not in st.session_state:
            st.session_state.tutorial_step = 'welcome'
        
        # Get user profile
        profile = UserProfileManager.get_profile()
        
        # Skip if tutorial completed
        if profile.get('tutorial_completed'):
            return

        # Create tutorial container
        with st.container():
            st.markdown(
                f"""
                <div style='
                    padding: 20px;
                    border-radius: 10px;
                    background-color: #1E1E1E;
                    margin-bottom: 20px;
                '>
                """,
                unsafe_allow_html=True
            )
            
            current_step = OnboardingTutorial.TUTORIAL_STEPS[st.session_state.tutorial_step]
            
            # Display step content
            st.title(f"{get_icon(current_step['icon'])} {current_step['title']}")
            st.markdown(current_step['content'])
            
            # Navigation buttons
            cols = st.columns([1, 1, 1])
            
            with cols[0]:
                if st.session_state.tutorial_step != 'welcome':
                    if st.button('⬅️ Previous'):
                        OnboardingTutorial._go_to_previous_step()
            
            with cols[2]:
                if st.session_state.tutorial_step != 'study':
                    if st.button('Next ➡️'):
                        OnboardingTutorial._go_to_next_step()
                else:
                    if st.button('Finish 🎉'):
                        OnboardingTutorial._complete_tutorial()
            
            # Progress indicator
            total_steps = len(OnboardingTutorial.TUTORIAL_STEPS)
            current_step_num = list(OnboardingTutorial.TUTORIAL_STEPS.keys()).index(
                st.session_state.tutorial_step
            ) + 1
            
            st.progress(current_step_num / total_steps)
            st.markdown(
                f"Step {current_step_num} of {total_steps}",
                help="Your progress through the tutorial"
            )

            st.markdown("</div>", unsafe_allow_html=True)

    @staticmethod
    def _go_to_next_step() -> None:
        """Move to the next tutorial step"""
        steps = list(OnboardingTutorial.TUTORIAL_STEPS.keys())
        current_index = steps.index(st.session_state.tutorial_step)
        if current_index < len(steps) - 1:
            st.session_state.tutorial_step = steps[current_index + 1]

    @staticmethod
    def _go_to_previous_step() -> None:
        """Move to the previous tutorial step"""
        steps = list(OnboardingTutorial.TUTORIAL_STEPS.keys())
        current_index = steps.index(st.session_state.tutorial_step)
        if current_index > 0:
            st.session_state.tutorial_step = steps[current_index - 1]

    @staticmethod
    def _complete_tutorial() -> None:
        """Mark the tutorial as completed"""
        profile = UserProfileManager.get_profile()
        profile['tutorial_completed'] = True
        UserProfileManager.save_profile(profile)
        st.session_state.pop('tutorial_step', None)
        st.rerun()
