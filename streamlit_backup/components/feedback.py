"""
Feedback component for collecting user feedback
"""
import streamlit as st
from database import get_db
from models.feedback import Feedback
from components.icons import get_icon

def feedback_popup():
    """Display a persistent feedback button and popup form"""
    # Custom CSS for the feedback button and popup
    st.markdown("""
    <style>
    .feedback-button {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 999;
        border-radius: 20px;
        padding: 10px 20px;
        background-color: #1e88e5;
        color: white;
        border: none;
        cursor: pointer;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .feedback-button:hover {
        background-color: #1565c0;
    }
    </style>
    """, unsafe_allow_html=True)

    # Initialize session state for feedback popup
    if 'feedback_open' not in st.session_state:
        st.session_state.feedback_open = False

    # Feedback button
    st.markdown(
        f"""
        <button 
            class="feedback-button"
            onclick="document.dispatchEvent(new CustomEvent('streamlit:toggleFeedback'))">
            {get_icon('info')} Feedback
        </button>
        """,
        unsafe_allow_html=True
    )

    # Custom JavaScript to handle button click
    st.markdown("""
        <script>
        document.addEventListener('streamlit:toggleFeedback', function() {
            window.parent.postMessage({
                type: 'streamlit:setComponentValue',
                value: true
            }, '*');
        });
        </script>
        """, unsafe_allow_html=True)

    # Feedback modal
    if st.session_state.feedback_open:
        # Create a modal dialog using streamlit's built-in modal
        with st.form("feedback_form", clear_on_submit=True):
            st.subheader("Share Your Feedback")

            feedback_type = st.selectbox(
                "Type of Feedback",
                ["General", "Bug Report", "Feature Request", "Question"]
            )

            feedback_text = st.text_area(
                "Your Feedback",
                placeholder="Tell us what you think..."
            )

            col1, col2, col3 = st.columns([1, 2, 1])
            with col1:
                rating = st.select_slider(
                    "Rating",
                    options=[1, 2, 3, 4, 5],
                    value=5,
                    format_func=lambda x: "⭐" * x
                )

            submit_col, cancel_col = st.columns([1, 1])
            with submit_col:
                submitted = st.form_submit_button("Submit", type="primary")
            with cancel_col:
                if st.form_submit_button("Cancel"):
                    st.session_state.feedback_open = False
                    st.rerun()

            if submitted:
                if feedback_text:
                    try:
                        # Get database session
                        db = next(get_db())

                        # Create new feedback record
                        new_feedback = Feedback(
                            feedback_type=feedback_type,
                            feedback_text=feedback_text,
                            rating=rating
                        )

                        # Add and commit to database
                        db.add(new_feedback)
                        db.commit()

                        st.success("Thank you for your feedback!")
                        st.session_state.feedback_open = False
                        st.rerun()

                    except Exception as e:
                        st.error(f"Error saving feedback: {str(e)}")
                        db.rollback()
                    finally:
                        db.close()
                else:
                    st.error("Please enter your feedback before submitting.")