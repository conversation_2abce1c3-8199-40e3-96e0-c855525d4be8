"""
Component for certification comparison functionality
"""
import streamlit as st
import pandas as pd

def compare_certifications(certifications, selected_certs):
    """
    Create a side-by-side comparison view of selected certifications
    """
    if not selected_certs:
        st.warning("Please select certifications to compare")
        return

    # Create comparison dataframe
    comparison_data = {}
    attributes = ['level', 'domain', 'difficulty', 'cost', 'prerequisites', 'description']
    
    for cert_name in selected_certs:
        cert_data = certifications[cert_name]
        comparison_data[cert_name] = {
            'Level': cert_data['level'],
            'Domain': cert_data['domain'],
            'Difficulty': cert_data['difficulty'],
            'Cost': f"${cert_data['cost']:,}",
            'Prerequisites': cert_data['prerequisites'],
            'Description': cert_data['description']
        }
    
    # Convert to DataFrame for easier display
    df = pd.DataFrame(comparison_data)
    
    # Style the comparison view
    st.subheader("Certification Comparison")
    
    # Use custom CSS to improve table readability
    st.markdown("""
        <style>
        .comparison-table {
            font-size: 0.9rem;
            margin: 1rem 0;
        }
        </style>
    """, unsafe_allow_html=True)
    
    # Display the comparison table
    st.table(df)
    
    # Add highlights for key differences
    st.subheader("Key Differences")
    
    # Compare costs
    costs = [certifications[cert]['cost'] for cert in selected_certs]
    cost_diff = max(costs) - min(costs)
    if cost_diff > 0:
        st.info(f"Cost difference: ${cost_diff:,}")
    
    # Compare difficulty levels
    difficulties = [certifications[cert]['difficulty'] for cert in selected_certs]
    if len(set(difficulties)) > 1:
        st.info("These certifications have different difficulty levels")
    
    # Compare domains
    domains = [certifications[cert]['domain'] for cert in selected_certs]
    if len(set(domains)) > 1:
        st.info("These certifications cover different domains")
