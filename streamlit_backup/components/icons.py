"""
Icon management component for consistent icon usage across pages
"""
import streamlit as st

# Dictionary mapping icon names to emoji fallbacks
# These will be replaced with Flaticon assets once available
ICONS = {
    'security': '🔒',
    'network': '🌐',
    'certification': '🎓',
    'management': '⚙️',
    'testing': '🔍',
    'development': '💻',
    'operations': '🛠️',
    'add': '➕',
    'delete': '🗑️',
    'edit': '✏️',
    'filter': '🔍',
    'admin': '👤',
    'settings': '⚙️',
    'success': '✅',
    'error': '❌',
    'info': 'ℹ️',
    'warning': '⚠️',
    # New icons for CertRat
    'cybersecurity': '🛡️',
    'career': '📈',
    'path': '🗺️',
    'study': '📚',
    'skills': '🎯',
    'experience': '⭐',
    'education': '🎓',
    'goals': '🎯',
    'timeline': '📅',
    'progress': '📊',
    'language': '🌍',
    'user': '👤',
    'difficulty': '📊',
    'duration': '⏱️',
    'cost': '💰',
    'prerequisites': '📋',
    'resources': '📚'
}

def get_icon(name: str, fallback: str = '▫️') -> str:
    """Get icon by name with fallback"""
    return ICONS.get(name, fallback)

def icon_button(label: str, icon_name: str = None) -> bool:
    """Create a button with an icon"""
    icon = get_icon(icon_name) if icon_name else ''
    return st.button(f"{icon} {label}")

def icon_header(text: str, icon_name: str = None) -> None:
    """Create a header with an icon"""
    icon = get_icon(icon_name) if icon_name else ''
    st.markdown(f"### {icon} {text}")