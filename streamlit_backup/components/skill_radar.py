"""
Skill progression radar chart component
"""
import streamlit as st
import plotly.graph_objects as go
from typing import Dict, List

def create_radar_chart(skills: Dict[str, float], domains: List[str] = None) -> go.Figure:
    """
    Create an animated radar chart showing skill progression
    
    Args:
        skills: Dictionary mapping skill names to proficiency levels (0-100)
        domains: Optional list of security domains to include
    """
    if domains is None:
        domains = [
            "Network Security", "Security Engineering", "Security Testing",
            "Identity & Access Management", "Asset Security", 
            "Security Management", "Defensive Security", "Offensive Security"
        ]
    
    # Filter skills to include only specified domains
    domain_scores = [skills.get(domain, 0) for domain in domains]
    
    # Create radar chart
    fig = go.Figure()
    
    # Add radar chart trace
    fig.add_trace(go.Scatterpolar(
        r=domain_scores,
        theta=domains,
        fill='toself',
        name='Current Skills',
        line_color='#7792E3',
        fillcolor='rgba(119, 146, 227, 0.3)'
    ))
    
    # Update layout for dark theme compatibility
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 100],
                tickfont=dict(color='white'),
                gridcolor='rgba(255, 255, 255, 0.1)',
                linecolor='rgba(255, 255, 255, 0.1)'
            ),
            angularaxis=dict(
                tickfont=dict(color='white'),
                gridcolor='rgba(255, 255, 255, 0.1)',
                linecolor='rgba(255, 255, 255, 0.1)'
            ),
            bgcolor='rgba(0,0,0,0)'
        ),
        showlegend=False,
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)',
        margin=dict(l=80, r=80, t=40, b=40),
        height=500
    )
    
    return fig

def animate_skill_progression(
    initial_skills: Dict[str, float],
    target_skills: Dict[str, float],
    domains: List[str] = None
) -> None:
    """
    Create an animated radar chart showing skill progression over time
    
    Args:
        initial_skills: Dictionary of starting skill levels
        target_skills: Dictionary of target skill levels
        domains: Optional list of security domains to include
    """
    if domains is None:
        domains = list(set(list(initial_skills.keys()) + list(target_skills.keys())))
    
    # Create placeholder for chart
    chart_placeholder = st.empty()
    
    # Calculate step sizes for animation
    steps = 30
    skill_steps = {
        domain: (target_skills.get(domain, 0) - initial_skills.get(domain, 0)) / steps
        for domain in domains
    }
    
    # Create animation frames
    current_skills = initial_skills.copy()
    chart = create_radar_chart(current_skills, domains)
    chart_placeholder.plotly_chart(chart, use_container_width=True)
    
    # Animate progression
    for _ in range(steps):
        # Update skills
        for domain in domains:
            current_skills[domain] = current_skills.get(domain, 0) + skill_steps[domain]
        
        # Update chart
        chart = create_radar_chart(current_skills, domains)
        chart_placeholder.plotly_chart(chart, use_container_width=True)
        st.experimental_rerun()  # Trigger rerun for smooth animation

def display_skill_radar(certifications, completed_certs=None):
    """
    Display the skill radar chart based on completed certifications
    
    Args:
        certifications: List of all available certifications
        completed_certs: List of completed certification IDs
    """
    if completed_certs is None:
        completed_certs = []
    
    # Calculate skill levels by domain
    domains = {
        "Network Security": 0,
        "Security Engineering": 0,
        "Security Testing": 0,
        "Identity & Access Management": 0,
        "Asset Security": 0,
        "Security Management": 0,
        "Defensive Security": 0,
        "Offensive Security": 0
    }
    
    # Calculate current skills based on completed certifications
    for cert in certifications:
        if cert.id in completed_certs:
            domain = cert.domain
            if domain in domains:
                # Weight by certification difficulty (1-4)
                domains[domain] += cert.difficulty * 25  # Scale to 0-100
    
    # Normalize scores to 0-100 range
    max_possible = 4 * 25  # Maximum difficulty * scale factor
    for domain in domains:
        domains[domain] = min(100, (domains[domain] / max_possible) * 100)
    
    # Display radar chart
    st.markdown("### 📊 Skill Progression")
    fig = create_radar_chart(domains)
    st.plotly_chart(fig, use_container_width=True)
    
    # Display skill levels
    st.markdown("### 🎯 Domain Proficiency")
    cols = st.columns(2)
    for idx, (domain, level) in enumerate(domains.items()):
        col = cols[idx % 2]
        col.metric(
            domain,
            f"{level:.1f}%",
            help=f"Based on completed certifications in {domain}"
        )
