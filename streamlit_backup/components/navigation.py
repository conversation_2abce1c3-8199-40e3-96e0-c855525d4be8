"""Navigation component for the application"""
import streamlit as st
from translations import _, LANGUAGE_DISPLAY
import logging
import os  # Added back for path operations

logger = logging.getLogger(__name__)

def create_navigation():
    """
    Creates and renders the site navigation in the sidebar.
    Returns the currently selected page identifier.
    """
    # App title and logo with fallback handling
    logo_found = False
    logo_paths = ["static/images/logo.png", "static/logo.png", "logo.png"]

    for path in logo_paths:
        if os.path.exists(path):
            try:
                st.sidebar.image(path, width=40)
                logo_found = True
                break
            except Exception as e:
                logger.warning(f"Could not load logo from {path}: {str(e)}")
                continue

    if not logo_found:
        st.sidebar.markdown("🛡️")  # Always show shield emoji as fallback

    st.sidebar.title(_("CertRat Explorer"))  # Use title for consistent styling

    # User profile section with error handling
    if 'user' in st.session_state and st.session_state.user:
        col1, col2 = st.sidebar.columns([1, 3])
        with col1:
            st.markdown("👤")  # User icon as fallback
        with col2:
            st.write(f"**{st.session_state.user.get('name', _('User'))}**")
            st.caption(f"{st.session_state.user.get('role', _('Security Professional'))}")

    # Search box
    search_query = st.sidebar.text_input(
        "🔍 " + _("Search certifications..."),
        key="search_input"
    )
    if search_query:
        st.session_state.page = "search_results"
        st.session_state.search_query = search_query

    # Category-based navigation with expanders
    categories = {
        _("Main"): {
            "icon": "🏠",
            "items": [
                {"id": "visualization", "label": _("Visualization")},
                {"id": "listings", "label": _("Certification Listings")}
            ]
        },
        _("Tools"): {
            "icon": "🔧",
            "items": [
                {"id": "study-time", "label": _("Study Time Calculator")},
                {"id": "cost-calculator", "label": _("Cost Calculator")},
                {"id": "career-path", "label": _("Career Path Generator")}
            ]
        },
        _("Resources"): {
            "icon": "📚",
            "items": [
                {"id": "user-journeys", "label": _("User Journeys")},
                {"id": "job-search", "label": _("Job Search")},
                {"id": "faq", "label": _("FAQ")}
            ]
        }
    }

    # Initialize session state for active category if not present
    if 'active_category' not in st.session_state:
        st.session_state.active_category = None

    selected_page = None

    # Create expanders for each category
    for category, details in categories.items():
        with st.sidebar.expander(
            f"{details['icon']} {category}",
            expanded=category == st.session_state.active_category
        ):
            for item in details['items']:
                if st.button(
                    item['label'],
                    key=f"nav_{category}_{item['id']}",
                    use_container_width=True
                ):
                    st.session_state.active_category = category
                    selected_page = item['id']

    # Language selector with enhanced error handling
    st.sidebar.markdown("---")

    # Use existing LANGUAGE_DISPLAY for consistency
    current_lang = st.session_state.get("language", "en")
    selected_language = st.sidebar.selectbox(
        "🌐 " + _("Language"),
        options=list(LANGUAGE_DISPLAY.keys()),
        format_func=lambda x: LANGUAGE_DISPLAY[x],
        index=list(LANGUAGE_DISPLAY.keys()).index(current_lang) if current_lang in LANGUAGE_DISPLAY else 0,
        key="language_selector"
    )

    # Handle language change
    if selected_language != current_lang:
        st.session_state.language = selected_language
        st.rerun()

    # Return the selected page or default to visualization
    return selected_page if selected_page else "visualization"