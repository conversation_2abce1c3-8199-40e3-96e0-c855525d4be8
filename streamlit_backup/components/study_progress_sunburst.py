"""
Animated sunburst chart for visualizing study progress
"""
import streamlit as st
import plotly.graph_objects as go
from typing import Dict, List, Optional
import json
from models.learning_path import LearningPath, LearningPathItem

def prepare_sunburst_data(learning_path: LearningPath) -> tuple[List[str], List[str], List[int], List[float]]:
    """
    Prepare data for the sunburst chart from a learning path
    """
    labels = []
    parents = []
    values = []
    completion_rates = []
    
    # Add root
    labels.append(learning_path.name)
    parents.append("")
    values.append(100)
    completion_rates.append(0)
    
    # Process path items
    for item in learning_path.path_items:
        labels.append(item.name)
        parents.append(learning_path.name)
        values.append(item.estimated_hours or 10)  # Default 10 hours if not specified
        
        # Calculate completion rate
        if item.status == "completed":
            rate = 1.0
        elif item.status == "in_progress":
            rate = 0.5
        else:
            rate = 0.0
        completion_rates.append(rate)
        
        # Add study resources as children
        for resource in item.study_resources:
            labels.append(resource.title)
            parents.append(item.name)
            values.append(5)  # Default 5 hours for resources
            completion_rates.append(rate)
    
    return labels, parents, values, completion_rates

def create_sunburst_chart(learning_path: LearningPath) -> go.Figure:
    """
    Create an animated sunburst chart for study progress
    """
    labels, parents, values, completion_rates = prepare_sunburst_data(learning_path)
    
    # Create base figure
    fig = go.Figure()
    
    # Add sunburst trace
    fig.add_trace(go.Sunburst(
        ids=labels,
        labels=labels,
        parents=parents,
        values=values,
        branchvalues="total",
        hovertemplate="<b>%{label}</b><br>" +
                     "Time allocated: %{value} hours<br>" +
                     "Completion: %{customdata:.0%}<br>" +
                     "<extra></extra>",
        customdata=completion_rates
    ))
    
    # Update layout
    fig.update_layout(
        width=800,
        height=800,
        title={
            'text': "Study Progress Visualization",
            'y':0.95,
            'x':0.5,
            'xanchor': 'center',
            'yanchor': 'top'
        },
        sunburstcolorway=["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A"],
        margin=dict(t=60, l=0, r=0, b=0)
    )
    
    # Add animation frames
    frames = []
    for i in range(11):  # 0% to 100% in 10 steps
        progress = i / 10
        frame_data = go.Sunburst(
            ids=labels,
            labels=labels,
            parents=parents,
            values=values,
            branchvalues="total",
            customdata=[min(progress, rate) for rate in completion_rates]
        )
        frames.append(go.Frame(data=[frame_data], name=f"frame{i}"))
    
    fig.frames = frames
    
    # Add animation settings
    fig.update_layout(
        updatemenus=[{
            'type': 'buttons',
            'showactive': False,
            'buttons': [{
                'label': 'Play',
                'method': 'animate',
                'args': [None, {
                    'frame': {'duration': 500, 'redraw': True},
                    'fromcurrent': True,
                    'transition': {'duration': 300, 'easing': 'quadratic-in-out'}
                }]
            }]
        }]
    )
    
    return fig

def display_progress_sunburst(learning_path: Optional[LearningPath] = None):
    """
    Display the animated sunburst chart in Streamlit
    """
    st.write("### Study Progress Visualization")
    
    if not learning_path:
        st.warning("No learning path selected. Please select or create a learning path to view progress.")
        return
    
    # Create and display chart
    fig = create_sunburst_chart(learning_path)
    st.plotly_chart(fig, use_container_width=True)
    
    # Add legend/explanation
    st.write("#### How to read this chart:")
    cols = st.columns(3)
    with cols[0]:
        st.write("🔵 Not Started")
    with cols[1]:
        st.write("🟡 In Progress")
    with cols[2]:
        st.write("🟢 Completed")
    
    st.write("""
    - The size of each segment represents the estimated time commitment
    - Click segments to zoom in
    - Use the play button to animate progress over time
    - Hover over segments for detailed information
    """)
