"""
Main application entry point
"""
import streamlit as st
from typing import Optional, Dict, Any, List, Sequence
from database import init_db, get_db
from components.visualization import display_certification_details, display_certification_visualizations
from components.icons import get_icon, icon_header
from components.feedback import feedback_popup
from components.navigation import create_navigation
from components.onboarding_tutorial import OnboardingTutorial # Added import
from pages import visualization, listings
from translations import _, init_localization, switch_language, translate_page
from utils.oauth import OAuthHandler, init_auth_state, login_button
from utils.admin import is_admin_authenticated, admin_logout
from utils.user_profile_manager import UserProfileManager
from utils.certification_utils import get_certifications
import uvicorn
from api.app import app as fastapi_app
from api.config import settings
import threading
import logging
import traceback

# Configure logging
logger = logging.getLogger(__name__)

# Set page config as the first Streamlit command
st.set_page_config(
    page_title="Security Certification Explorer",
    page_icon=get_icon('security'),
    layout="wide"
)

def run_fastapi():
    """Run the FastAPI server"""
    try:
        uvicorn.run(fastapi_app, host=settings.HOST, port=settings.PORT)
    except OSError as e:
        logger.error(f"Failed to start FastAPI server: {e}")
    except Exception as e:
        logger.error(f"Unexpected error starting FastAPI server: {str(e)}")
        logger.error(traceback.format_exc())

# Initialize admin session state if not already set
if "admin_authenticated" not in st.session_state:
    st.session_state.admin_authenticated = False

@translate_page
def main():
    """Main function to run the Streamlit application"""
    try:
        # Initialize database, localization, auth state and user profile
        logger.info("Initializing application services...")
        init_db()
        init_localization()
        init_auth_state()
        UserProfileManager.initialize_session()

        # Show onboarding tutorial for new users
        OnboardingTutorial.show() #Added this line

        # Default to visualization if no page is selected
        if 'active_page' not in st.session_state:
            st.session_state.active_page = "visualization"
            logger.info("Setting default active page to visualization")

        try:
            # Navigation is created once here, and the selected page is stored
            page = create_navigation()
            logger.info(f"Selected navigation page: {page}")

            # Use page from navigation or fallback to default visualization
            if not page:
                page = "visualization"
                logger.info("No page selected, defaulting to visualization")

            # Routing logic uses the selected page from navigation
            try:
                if page == "visualization":
                    visualization.run(get_certifications=get_certifications, get_db=get_db)
                elif page == "listings":
                    listings.run(get_certifications=get_certifications, get_db=get_db)
                elif page == "user-profile":
                    st.switch_page("pages/user_profile.py")
                elif page == "admin":
                    st.switch_page("pages/3_admin.py")
                elif page == "faq":
                    st.switch_page("pages/4_faq.py")
                elif page == "study-time":
                    st.switch_page("pages/5_study_time.py")
                elif page == "cost-calculator":
                    st.switch_page("pages/6_cost_calculator.py")
                elif page == "career-path":
                    st.switch_page("pages/7_certrat_career_path.py")
                elif page == "user-journeys":
                    st.switch_page("pages/8_user_journeys.py")
                elif page == "job-search":
                    st.switch_page("pages/job_search.py")
                else:
                    # Log unknown page but default to visualization
                    logger.warning(f"Unknown page: {page}, defaulting to visualization")
                    visualization.run(get_certifications=get_certifications, get_db=get_db)
            except Exception as e:
                logger.error(f"Error rendering page {page}: {str(e)}")
                logger.error(traceback.format_exc())
                raise

            # Add feedback popup to every page
            feedback_popup()

        except Exception as e:
            logger.error(f"Error in navigation: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    except Exception as e:
        logger.error(f"Critical application error: {str(e)}")
        logger.error(traceback.format_exc())
        st.error("Please try refreshing the page. If the error persists, contact support.")

if __name__ == "__main__":
    # Start FastAPI in a separate thread
    api_thread = threading.Thread(target=run_fastapi, daemon=True)
    api_thread.start()

    # Run Streamlit main app
    main()