#!/usr/bin/env python3
"""
Manual script to seed the certification_explorer table with major providers and types
"""
import psycopg2
from psycopg2.extras import Json

# Database connection string
DB_URL = "***************************************************"

# Define certification providers
PROVIDERS = [
    "CompTIA",
    "Amazon Web Services",
    "Microsoft",
    "Cisco Systems",
    "EC-Council",
    "ISC²",
    "ISACA",
    "Google",
    "Oracle",
    "Linux Foundation"
]

# Define certification types
TYPES = [
    "SECURITY",
    "CLOUD",
    "NETWORKING",
    "DEVELOPMENT",
    "DEVOPS"
]

# Sample certifications with proper data
SAMPLE_CERTIFICATIONS = [
    # CompTIA
    {
        "name": "CompTIA Security+",
        "provider": "CompTIA",
        "description": "CompTIA Security+ is a global certification that validates the baseline skills necessary to perform core security functions and pursue an IT security career.",
        "type": "SECURITY",
        "level": "BEGINNER",
        "domain": "SECURITY_MANAGEMENT",
        "price": 370.0,
        "prerequisites": ["Network+"],
        "url": "https://www.comptia.org/certifications/security"
    },
    {
        "name": "CompTIA Network+",
        "provider": "CompTIA",
        "description": "CompTIA Network+ certifies the essential skills needed to confidently design, configure, manage and troubleshoot wired and wireless networks.",
        "type": "NETWORKING",
        "level": "BEGINNER",
        "domain": "NETWORK_SECURITY",
        "price": 329.0,
        "prerequisites": ["A+"],
        "url": "https://www.comptia.org/certifications/network"
    },
    
    # AWS
    {
        "name": "AWS Certified Solutions Architect - Associate",
        "provider": "Amazon Web Services",
        "description": "This certification validates the ability to design and implement distributed systems on the AWS platform.",
        "type": "CLOUD",
        "level": "INTERMEDIATE",
        "domain": "SECURITY_ENGINEERING",
        "price": 150.0,
        "prerequisites": [],
        "url": "https://aws.amazon.com/certification/certified-solutions-architect-associate/"
    },
    {
        "name": "AWS Certified Security - Specialty",
        "provider": "Amazon Web Services",
        "description": "This certification validates expertise in security on the AWS platform.",
        "type": "CLOUD",
        "level": "ADVANCED",
        "domain": "SECURITY_ENGINEERING",
        "price": 300.0,
        "prerequisites": ["AWS Certified Solutions Architect - Associate"],
        "url": "https://aws.amazon.com/certification/certified-security-specialty/"
    },
    
    # Microsoft
    {
        "name": "Microsoft Certified: Azure Security Engineer Associate",
        "provider": "Microsoft",
        "description": "This certification validates the skills needed to implement security controls and threat protection.",
        "type": "CLOUD",
        "level": "INTERMEDIATE",
        "domain": "SECURITY_ENGINEERING",
        "price": 165.0,
        "prerequisites": [],
        "url": "https://learn.microsoft.com/en-us/certifications/azure-security-engineer/"
    },
    
    # Cisco
    {
        "name": "Cisco CCNA",
        "provider": "Cisco Systems",
        "description": "CCNA certification validates knowledge of networking fundamentals, including network access, IP connectivity, security fundamentals, and automation.",
        "type": "NETWORKING",
        "level": "BEGINNER",
        "domain": "NETWORK_SECURITY",
        "price": 300.0,
        "prerequisites": [],
        "url": "https://www.cisco.com/c/en/us/training-events/training-certifications/certifications/associate/ccna.html"
    },
    {
        "name": "Cisco CCNP Security",
        "provider": "Cisco Systems",
        "description": "CCNP Security certification validates knowledge of security infrastructure, security of networked devices, and security of virtual environments.",
        "type": "SECURITY",
        "level": "ADVANCED",
        "domain": "NETWORK_SECURITY",
        "price": 1200.0,
        "prerequisites": ["Cisco CCNA"],
        "url": "https://www.cisco.com/c/en/us/training-events/training-certifications/certifications/professional/ccnp-security.html"
    },
    
    # EC-Council
    {
        "name": "Certified Ethical Hacker (CEH)",
        "provider": "EC-Council",
        "description": "The Certified Ethical Hacker (CEH) is a core certification that establishes the concepts and principles of ethical hacking.",
        "type": "SECURITY",
        "level": "INTERMEDIATE",
        "domain": "OFFENSIVE_SECURITY",
        "price": 950.0,
        "prerequisites": [],
        "url": "https://www.eccouncil.org/programs/certified-ethical-hacker-ceh/"
    },
    
    # ISC²
    {
        "name": "CISSP",
        "provider": "ISC²",
        "description": "The CISSP certification is considered the gold standard in the field of information security.",
        "type": "SECURITY",
        "level": "ADVANCED",
        "domain": "SECURITY_MANAGEMENT",
        "price": 749.0,
        "prerequisites": [],
        "url": "https://www.isc2.org/Certifications/CISSP"
    },
    
    # ISACA
    {
        "name": "CISA",
        "provider": "ISACA",
        "description": "The Certified Information Systems Auditor (CISA) certification is for IT professionals who audit, control, monitor and assess information technology and business systems.",
        "type": "SECURITY",
        "level": "ADVANCED",
        "domain": "SECURITY_TESTING",
        "price": 575.0,
        "prerequisites": [],
        "url": "https://www.isaca.org/credentialing/cisa"
    }
]

def ensure_enum_types(conn):
    """Ensure enum types exist"""
    cursor = conn.cursor()
    
    # Create enum types if they don't exist
    cursor.execute("""
        DO $$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'certificationtypeenum') THEN
                CREATE TYPE certificationtypeenum AS ENUM (
                    'SECURITY', 'CLOUD', 'NETWORKING', 'DEVELOPMENT', 'DEVOPS', 'OTHER'
                );
            END IF;
            
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'certificationlevelenum') THEN
                CREATE TYPE certificationlevelenum AS ENUM (
                    'BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'
                );
            END IF;
            
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'securitydomainenum') THEN
                CREATE TYPE securitydomainenum AS ENUM (
                    'SECURITY_ENGINEERING', 'DEFENSIVE_SECURITY', 'SECURITY_MANAGEMENT',
                    'NETWORK_SECURITY', 'IDENTITY_ACCESS_MANAGEMENT', 'ASSET_SECURITY',
                    'SECURITY_TESTING', 'OFFENSIVE_SECURITY'
                );
            END IF;
        END
        $$;
    """)
    conn.commit()

def ensure_table(conn):
    """Ensure the certification_explorer table exists"""
    cursor = conn.cursor()
    
    # Check if the table exists
    cursor.execute("""
        SELECT EXISTS(
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'certification_explorer'
        )
    """)
    table_exists = cursor.fetchone()[0]
    
    if not table_exists:
        # Create certification_explorer table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS certification_explorer (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                provider VARCHAR(100) NOT NULL,
                description TEXT,
                type certificationtypeenum NOT NULL,
                level certificationlevelenum NOT NULL,
                domain securitydomainenum,
                exam_code VARCHAR(50),
                price FLOAT,
                duration_minutes INTEGER,
                passing_score INTEGER,
                prerequisites_list JSONB,
                skills_covered JSONB,
                url VARCHAR(255),
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            );
        """)
        
        # Create indexes
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS ix_certification_explorer_name ON certification_explorer (name);
            CREATE INDEX IF NOT EXISTS ix_certification_explorer_provider ON certification_explorer (provider);
        """)
        
        # Create prerequisites junction table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS certification_explorer_prerequisites (
                certification_id INTEGER REFERENCES certification_explorer(id),
                prerequisite_id INTEGER REFERENCES certification_explorer(id),
                PRIMARY KEY (certification_id, prerequisite_id)
            );
        """)
    
    conn.commit()

def seed_sample_certifications():
    """Seed the certification_explorer table with sample certifications"""
    try:
        # Connect to the database
        conn = psycopg2.connect(DB_URL)
        
        # Ensure enums and table exist
        ensure_enum_types(conn)
        ensure_table(conn)
        
        cursor = conn.cursor()
        
        # Clear existing data
        print("Clearing existing certification data...")
        cursor.execute("TRUNCATE certification_explorer CASCADE")
        conn.commit()
        
        # Insert sample certifications
        print("Inserting sample certifications...")
        for cert in SAMPLE_CERTIFICATIONS:
            cursor.execute("""
                INSERT INTO certification_explorer
                (name, provider, description, type, level, domain, price, prerequisites_list, url)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                cert["name"],
                cert["provider"],
                cert["description"],
                cert["type"],
                cert["level"],
                cert["domain"],
                cert["price"],
                Json(cert["prerequisites"]),
                cert["url"]
            ))
        
        conn.commit()
        
        # Verify count
        cursor.execute("SELECT COUNT(*) FROM certification_explorer")
        count = cursor.fetchone()[0]
        print(f"Successfully added {count} sample certifications")
        
        # Verify providers
        cursor.execute("SELECT DISTINCT provider FROM certification_explorer")
        providers = [row[0] for row in cursor.fetchall()]
        print(f"Providers in database: {', '.join(providers)}")
        
        # Verify types
        cursor.execute("SELECT DISTINCT type FROM certification_explorer")
        types = [row[0] for row in cursor.fetchall()]
        print(f"Certification types in database: {', '.join(types)}")
        
    except Exception as e:
        print(f"Error seeding sample certifications: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'conn' in locals():
            cursor.close()
            conn.close()

if __name__ == "__main__":
    seed_sample_certifications() 