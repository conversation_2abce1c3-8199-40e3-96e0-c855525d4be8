"""
FastAPI server entry point
"""
import uvicorn
import logging
import sys
import time
import socket
from api.app import app
from api.config import settings
import os

# Configure logging with more detailed format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def is_port_in_use(port: int, host: str = '0.0.0.0') -> bool:
    """Check if a port is already in use"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind((host, port))
            s.close()
            return False
        except OSError as e:
            logger.error(f"Port {port} is already in use: {e}")
            return True

def find_available_port(start_port: int = 8000, max_port: int = 8010) -> int:
    """Find first available port in range"""
    for port in range(start_port, max_port + 1):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.bind(('0.0.0.0', port))
                s.close()
                return port
            except OSError:
                continue
    raise RuntimeError(f"No available ports found between {start_port} and {max_port}")

def run_server():
    """Run the FastAPI server with improved error handling"""
    try:
        host = '0.0.0.0'

        # Find available port
        try:
            port = find_available_port(start_port=8001, max_port=8020)  # Changed port range
            logger.info(f"Found available port: {port}")
        except RuntimeError as e:
            logger.error(str(e))
            sys.exit(1)

        logger.info(f"Starting FastAPI server on {host}:{port}")

        # Configure uvicorn with explicit settings
        config = uvicorn.Config(
            app=app,
            host=host,
            port=port,
            log_level="info",
            access_log=True,
            timeout_keep_alive=30,
            workers=1,
            reload=False  # Disable reload to prevent duplicate processes
        )

        server = uvicorn.Server(config)
        server.run()

    except Exception as e:
        logger.error(f"Critical error starting server: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    logger.info("Starting FastAPI server process")
    run_server()