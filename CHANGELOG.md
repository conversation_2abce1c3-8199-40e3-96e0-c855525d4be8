# Changelog

All notable changes to the CertRats project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Completed full implementation of main page interface PRD:
  - Created DashboardPreview component with Chart.js visualizations
  - Built CareerPathNavigator with D3.js interactive graph visualization
  - Developed CertificationCatalogPreview with filtering functionality
  - Implemented SuccessStories component with testimonial carousel
  - Created GettingStartedGuide with interactive step navigation
  - Added analytics tracking and performance optimizations
- Implemented language selector component in application header
- Created address book-style flip animations for main page sections
- Added horizontal swipe gestures for mobile navigation between sections
- Created PRD for certifications page implementation
- Created comprehensive PRD for main page interface
- Added feedback API endpoints and client implementation
- Enhanced documentation with updated roadmap and main page plans

### Changed
- Refined dark mode color scheme for improved readability and reduced eye strain 
- Updated color variables to use more semantic naming
- Improved responsive design for mobile devices
- Enhanced language context with better fallback and error handling

### Fixed
- Corrected contrast issues in dark mode UI across multiple components
- Improved accessibility for dark mode text and background colors
- Resolved TypeScript errors in UI components:
  - Fixed SVG Icon component type assertions
  - Updated D3.js visualization typing in CareerPath component
  - Added missing API interfaces and implementations
  - Fixed TutorialStep interface in OnboardingTutorial component
  - Updated translation function to accept multiple parameter types
  - Fixed authentication context interfaces

## [1.1.0] - 2023-03-21

### Added
- Implemented API versioning system for all endpoints (api/v1/...)
- Created centralized route management for API endpoints
- Implemented backend logic for all study-related endpoints:
  - `/api/v1/study/estimate` - Study time estimation
  - `/api/v1/study/progress` - Study progress tracking
  - `/api/v1/study/roi` - Return on investment calculations
- Added comprehensive test suite for study endpoints with 100% coverage
- Created API documentation for versioning system

### Changed
- Updated API client structure to use versioned endpoints
- Improved error handling in API endpoints
- Reorganized route management to use the new centralized system

## [1.0.0] - 2023-03-10

### Added
- Complete migration from Streamlit to React
- New React components for all major features
- TypeScript integration for type safety
- Comprehensive test suite for React components
- End-to-end tests with Playwright

### Changed
- Frontend architecture completely redesigned
- API client refactored for React integration
- Authentication flow improved
- UI modernized with responsive design

### Removed
- Streamlit dependencies and components
- Legacy code and unused packages 