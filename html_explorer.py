#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to explore the HTML structure of the Security Certification Roadmap
"""
import os
from bs4 import BeautifulSoup

def explore_html():
    """Explore the HTML structure of the roadmap file"""
    # Define the path to the HTML file
    html_path = '/app/submodules/SecCertRoadmapHTML/Security-Certification-Roadmap9.html'
    
    # Read the HTML file
    with open(html_path, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # Parse the HTML
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Find all div elements
    divs = soup.find_all('div')
    print(f"Total div elements: {len(divs)}")
    
    # Print distribution of classes
    class_counts = {}
    for div in divs:
        classes = div.get('class')
        if classes:
            for cls in classes:
                if cls not in class_counts:
                    class_counts[cls] = 0
                class_counts[cls] += 1
    
    print("\nClass distribution:")
    for cls, count in sorted(class_counts.items(), key=lambda x: x[1], reverse=True)[:20]:
        print(f"  {cls}: {count} divs")
    
    # Look for certificates based on typical patterns in security cert roadmaps
    cert_keywords = [
        'cert', 'certification', 'security', 'cissp', 'comptia', 'aws', 
        'microsoft', 'cisco', 'ccna', 'ceh', 'level', 'beginner', 'intermediate', 
        'advanced', 'expert'
    ]
    
    # Try different approaches to find certificates
    # Approach 1: Look for divs with title attribute
    titled_divs = [div for div in divs if div.get('title')]
    print(f"\nFound {len(titled_divs)} divs with title attribute")
    
    # Approach 2: Look for divs with specific classes
    cert_classes = []
    for cls, count in class_counts.items():
        if any(keyword in cls.lower() for keyword in cert_keywords):
            cert_classes.append(cls)
    
    print(f"\nFound {len(cert_classes)} potentially certification-related classes:")
    for cls in cert_classes:
        print(f"  {cls}")
    
    # Approach 3: Look for divs with data-x and data-y attributes
    positioned_divs = [div for div in divs if div.get('data-x') and div.get('data-y')]
    print(f"\nFound {len(positioned_divs)} positioned divs (with data-x and data-y attributes)")
    
    if positioned_divs:
        print("\nSample positioned divs:")
        for i, div in enumerate(positioned_divs[:5]):
            print(f"Div {i+1}:")
            print(f"  Text: {div.text.strip()[:30]}...")
            print(f"  Class: {div.get('class')}")
            print(f"  Title: {div.get('title')}")
            print(f"  Position: data-x={div.get('data-x')}, data-y={div.get('data-y')}")
            print()
    
    # Try to find actual certificates based on combined approaches
    potential_certs = []
    for div in positioned_divs:
        if div.get('title') and div.text.strip():
            potential_certs.append(div)
    
    print(f"\nFound {len(potential_certs)} potential certificates")
    
    if potential_certs:
        print("\nSample certifications:")
        for i, div in enumerate(potential_certs[:15]):
            print(f"Cert {i+1}:")
            print(f"  Title: {div.get('title')}")
            print(f"  Text: {div.text.strip()}")
            print(f"  Class: {div.get('class')}")
            print()

if __name__ == "__main__":
    explore_html() 