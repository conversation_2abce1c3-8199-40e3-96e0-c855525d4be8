# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.pytest_cache/
.coverage
htmlcov/
.venv/
venv/
env/
ENV/

# JavaScript/Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Frontend build artifacts
/frontend/build/
/frontend/coverage/

# IDEs and editors
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store

# Logs
logs/
*.log

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.sqlite
*.sqlite3
*.db

# Docker
.docker/

# Temporary files
*.tmp
**/tmp/
**/temp/

# Test artifacts
*.zip
test-results/
playwright-results/
*.png
*.jpg
*.jpeg
*.gif
*.webp
*.bmp
*.tiff
screenshots/

# OS
Thumbs.db 