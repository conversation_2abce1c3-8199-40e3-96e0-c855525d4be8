version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: certrats-postgres-dev
    environment:
      POSTGRES_DB: certrats_dev
      POSTGRES_USER: certrats_user
      POSTGRES_PASSWORD: certrats_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - certrats-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U certrats_user -d certrats_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: certrats-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    networks:
      - certrats-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI Backend
  backend:
    build:
      context: ..
      dockerfile: docker/Dockerfile.backend
    container_name: certrats-backend-dev
    environment:
      - DATABASE_URL=**********************************************************/certrats_dev
      - REDIS_URL=redis://redis:6379/0
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
    ports:
      - "8000:8000"
    volumes:
      - ../api:/app/api
      - ../models:/app/models
      - ../utils:/app/utils
      - ../migrations:/app/migrations
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - certrats-network
    command: uvicorn api.app:app --host 0.0.0.0 --port 8000 --reload
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Next.js Frontend
  frontend:
    build:
      context: ..
      dockerfile: docker/Dockerfile.frontend
    container_name: certrats-frontend-dev
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    ports:
      - "3000:3000"
    volumes:
      - ../src:/app/src
      - ../public:/app/public
      - ../package.json:/app/package.json
      - ../package-lock.json:/app/package-lock.json
      - ../next.config.js:/app/next.config.js
      - ../tailwind.config.js:/app/tailwind.config.js
      - ../tsconfig.json:/app/tsconfig.json
    depends_on:
      - backend
    networks:
      - certrats-network
    command: npm run dev

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: certrats-nginx-dev
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.dev.conf:/etc/nginx/nginx.conf
    depends_on:
      - frontend
      - backend
    networks:
      - certrats-network

volumes:
  postgres_data_dev:
  redis_data_dev:

networks:
  certrats-network:
    driver: bridge
