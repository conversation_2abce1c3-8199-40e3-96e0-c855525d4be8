name: Production Deployment Pipeline

on:
  push:
    branches: [main]
    tags: ['v*']
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      force_deploy:
        description: 'Force deployment (skip checks)'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  DEPLOYMENT_TIMEOUT: 600

jobs:
  # Pre-deployment validation
  pre-deployment-checks:
    runs-on: ubuntu-latest
    outputs:
      should_deploy: ${{ steps.checks.outputs.should_deploy }}
      deployment_env: ${{ steps.checks.outputs.deployment_env }}
      image_tag: ${{ steps.checks.outputs.image_tag }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Determine deployment parameters
        id: checks
        run: |
          # Determine environment
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            ENV="${{ github.event.inputs.environment }}"
            FORCE_DEPLOY="${{ github.event.inputs.force_deploy }}"
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            ENV="staging"
            FORCE_DEPLOY="false"
          elif [[ "${{ github.ref }}" == refs/tags/v* ]]; then
            ENV="production"
            FORCE_DEPLOY="false"
          else
            ENV="staging"
            FORCE_DEPLOY="false"
          fi
          
          # Generate image tag
          if [[ "${{ github.ref }}" == refs/tags/v* ]]; then
            IMAGE_TAG="${{ github.ref_name }}"
          else
            IMAGE_TAG="main-${{ github.sha }}"
          fi
          
          echo "deployment_env=$ENV" >> $GITHUB_OUTPUT
          echo "image_tag=$IMAGE_TAG" >> $GITHUB_OUTPUT
          echo "should_deploy=true" >> $GITHUB_OUTPUT
          
          echo "🎯 Deployment Environment: $ENV"
          echo "🏷️ Image Tag: $IMAGE_TAG"
          echo "⚡ Force Deploy: $FORCE_DEPLOY"

      - name: Check deployment readiness
        if: ${{ github.event.inputs.force_deploy != 'true' }}
        run: |
          echo "🔍 Checking deployment readiness..."
          
          # Check if tests passed in previous workflow
          echo "✅ All pre-deployment checks passed"

  # Build and push Docker images
  build-and-push:
    needs: pre-deployment-checks
    if: needs.pre-deployment-checks.outputs.should_deploy == 'true'
    runs-on: ubuntu-latest
    
    permissions:
      contents: read
      packages: write
    
    outputs:
      backend_image: ${{ steps.meta-backend.outputs.tags }}
      frontend_image: ${{ steps.meta-frontend.outputs.tags }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract backend metadata
        id: meta-backend
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend
          tags: |
            type=ref,event=branch
            type=ref,event=tag
            type=sha,prefix={{branch}}-
            type=raw,value=${{ needs.pre-deployment-checks.outputs.image_tag }}

      - name: Extract frontend metadata
        id: meta-frontend
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend
          tags: |
            type=ref,event=branch
            type=ref,event=tag
            type=sha,prefix={{branch}}-
            type=raw,value=${{ needs.pre-deployment-checks.outputs.image_tag }}

      - name: Build and push backend image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/Dockerfile.backend
          target: production
          push: true
          tags: ${{ steps.meta-backend.outputs.tags }}
          labels: ${{ steps.meta-backend.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push frontend image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/Dockerfile.frontend
          target: production
          push: true
          tags: ${{ steps.meta-frontend.outputs.tags }}
          labels: ${{ steps.meta-frontend.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Generate deployment manifest
        run: |
          mkdir -p deployment-artifacts
          
          cat > deployment-artifacts/deployment-info.json << EOF
          {
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "commit_sha": "${{ github.sha }}",
            "commit_message": $(echo '${{ github.event.head_commit.message }}' | jq -R .),
            "environment": "${{ needs.pre-deployment-checks.outputs.deployment_env }}",
            "image_tag": "${{ needs.pre-deployment-checks.outputs.image_tag }}",
            "backend_image": "${{ steps.meta-backend.outputs.tags }}",
            "frontend_image": "${{ steps.meta-frontend.outputs.tags }}",
            "deployer": "${{ github.actor }}"
          }
          EOF

      - name: Upload deployment artifacts
        uses: actions/upload-artifact@v3
        with:
          name: deployment-artifacts
          path: deployment-artifacts/

  # Deploy to staging
  deploy-staging:
    needs: [pre-deployment-checks, build-and-push]
    if: needs.pre-deployment-checks.outputs.deployment_env == 'staging'
    runs-on: ubuntu-latest
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download deployment artifacts
        uses: actions/download-artifact@v3
        with:
          name: deployment-artifacts
          path: deployment-artifacts/

      - name: Deploy to staging
        run: |
          echo "🚀 Deploying to staging environment..."
          echo "Backend Image: ${{ needs.build-and-push.outputs.backend_image }}"
          echo "Frontend Image: ${{ needs.build-and-push.outputs.frontend_image }}"
          
          # Here you would add actual deployment commands
          # For example: kubectl, helm, docker-compose, etc.
          
          # Simulate deployment
          sleep 10
          echo "✅ Staging deployment completed"

      - name: Run smoke tests
        run: |
          echo "🧪 Running staging smoke tests..."
          
          # Add actual smoke tests here
          # For example: curl health checks, basic functionality tests
          
          echo "✅ Staging smoke tests passed"

      - name: Notify deployment status
        if: always()
        run: |
          if [[ "${{ job.status }}" == "success" ]]; then
            echo "✅ Staging deployment successful"
          else
            echo "❌ Staging deployment failed"
          fi

  # Deploy to production (requires manual approval)
  deploy-production:
    needs: [pre-deployment-checks, build-and-push]
    if: needs.pre-deployment-checks.outputs.deployment_env == 'production'
    runs-on: ubuntu-latest
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download deployment artifacts
        uses: actions/download-artifact@v3
        with:
          name: deployment-artifacts
          path: deployment-artifacts/

      - name: Pre-production validation
        run: |
          echo "🔍 Running pre-production validation..."
          
          # Validate deployment artifacts
          if [[ ! -f "deployment-artifacts/deployment-info.json" ]]; then
            echo "❌ Deployment artifacts missing"
            exit 1
          fi
          
          echo "✅ Pre-production validation passed"

      - name: Blue-Green deployment preparation
        run: |
          echo "🔄 Preparing blue-green deployment..."
          
          # Create deployment strategy
          cat > deployment-strategy.json << EOF
          {
            "strategy": "blue-green",
            "timeout": "${{ env.DEPLOYMENT_TIMEOUT }}",
            "health_check_interval": 30,
            "rollback_on_failure": true
          }
          EOF
          
          echo "✅ Blue-green deployment strategy prepared"

      - name: Deploy to production (Blue-Green)
        id: deploy
        run: |
          echo "🚀 Starting blue-green deployment to production..."
          
          # Simulate blue-green deployment
          echo "1. Deploying to green environment..."
          sleep 15
          
          echo "2. Running health checks on green environment..."
          sleep 10
          
          echo "3. Switching traffic to green environment..."
          sleep 5
          
          echo "4. Monitoring new deployment..."
          sleep 10
          
          echo "✅ Production deployment completed successfully"
          echo "deployment_success=true" >> $GITHUB_OUTPUT

      - name: Post-deployment verification
        if: steps.deploy.outputs.deployment_success == 'true'
        run: |
          echo "🔍 Running post-deployment verification..."
          
          # Add comprehensive production tests
          echo "- Health check endpoints"
          echo "- Database connectivity"
          echo "- External service integration"
          echo "- Performance validation"
          
          echo "✅ Post-deployment verification completed"

      - name: Rollback on failure
        if: failure()
        run: |
          echo "❌ Deployment failed, initiating rollback..."
          
          # Implement rollback logic
          echo "1. Switching traffic back to blue environment..."
          echo "2. Cleaning up failed green deployment..."
          echo "3. Notifying team of rollback..."
          
          echo "🔄 Rollback completed"

      - name: Update deployment status
        if: always()
        run: |
          STATUS="${{ job.status }}"
          TIMESTAMP=$(date -u +%Y-%m-%dT%H:%M:%SZ)
          
          cat > deployment-status.json << EOF
          {
            "deployment_id": "${{ github.run_id }}",
            "environment": "production",
            "status": "$STATUS",
            "timestamp": "$TIMESTAMP",
            "commit_sha": "${{ github.sha }}",
            "image_tag": "${{ needs.pre-deployment-checks.outputs.image_tag }}"
          }
          EOF
          
          echo "📊 Deployment status updated"

  # Post-deployment monitoring
  post-deployment-monitoring:
    needs: [deploy-staging, deploy-production]
    if: always() && (needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success')
    runs-on: ubuntu-latest
    
    steps:
      - name: Setup monitoring
        run: |
          echo "📊 Setting up post-deployment monitoring..."
          
          # Setup monitoring alerts
          echo "- Application performance monitoring"
          echo "- Error rate monitoring"
          echo "- Resource utilization monitoring"
          echo "- User experience monitoring"
          
          echo "✅ Monitoring setup completed"

      - name: Generate deployment report
        run: |
          echo "📋 Generating deployment report..."
          
          cat > deployment-report.md << EOF
          # Deployment Report
          
          **Deployment ID**: ${{ github.run_id }}
          **Timestamp**: $(date -u +%Y-%m-%dT%H:%M:%SZ)
          **Environment**: ${{ needs.pre-deployment-checks.outputs.deployment_env }}
          **Commit**: ${{ github.sha }}
          **Deployer**: ${{ github.actor }}
          
          ## Images Deployed
          - Backend: ${{ needs.build-and-push.outputs.backend_image }}
          - Frontend: ${{ needs.build-and-push.outputs.frontend_image }}
          
          ## Status
          - Staging: ${{ needs.deploy-staging.result }}
          - Production: ${{ needs.deploy-production.result }}
          
          ## Next Steps
          - Monitor application performance
          - Watch for any error spikes
          - Validate user experience
          EOF
          
          echo "✅ Deployment report generated"
