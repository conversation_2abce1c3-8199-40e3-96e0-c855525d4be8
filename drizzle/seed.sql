INSERT INTO certifications (
    name,
    provider,
    description,
    type,
    level,
    exam_code,
    price,
    duration_minutes,
    passing_score,
    skills_covered,
    url
) VALUES
(
    'Certified Ethical Hacker (CEH)',
    'EC-Council',
    'The Certified Ethical Hacker certification focuses on ethical hacking methodologies and tools used by cybersecurity professionals and ethical hackers.',
    'security',
    'intermediate',
    '312-50',
    1199,
    240,
    70,
    ARRAY['penetration testing', 'vulnerability assessment', 'network security', 'system hacking', 'malware threats'],
    'https://www.eccouncil.org/programs/certified-ethical-hacker-ceh/'
),
(
    'CompTIA Security+',
    'CompTIA',
    'CompTIA Security+ is a global certification that validates the baseline skills necessary to perform core security functions and pursue an IT security career.',
    'security',
    'beginner',
    'SY0-601',
    349,
    90,
    750,
    ARRAY['network security', 'compliance', 'operational security', 'threats and vulnerabilities', 'access control'],
    'https://www.comptia.org/certifications/security'
),
(
    'CISSP',
    'ISC2',
    'The CISSP certification is ideal for experienced security practitioners, managers and executives interested in proving their knowledge across a wide array of security practices and principles.',
    'security',
    'advanced',
    'CISSP',
    749,
    180,
    700,
    ARRAY['security management', 'asset security', 'security architecture', 'network security', 'identity management'],
    'https://www.isc2.org/Certifications/CISSP'
),
(
    'AWS Certified Security - Specialty',
    'Amazon Web Services',
    'The AWS Certified Security - Specialty certification demonstrates your ability to secure the AWS platform.',
    'cloud',
    'advanced',
    'AWS-CSS',
    300,
    170,
    750,
    ARRAY['cloud security', 'infrastructure security', 'data protection', 'incident response', 'access management'],
    'https://aws.amazon.com/certification/certified-security-specialty/'
),
(
    'CCNA Security',
    'Cisco',
    'The Cisco CCNA Security certification validates your skills in security network infrastructure, threats, and vulnerabilities.',
    'network',
    'intermediate',
    '210-260',
    299,
    90,
    825,
    ARRAY['network security', 'VPN encryption', 'firewall configuration', 'threat prevention', 'secure routing'],
    'https://www.cisco.com/c/en/us/training-events/training-certifications/certifications/associate/ccna.html'
); 