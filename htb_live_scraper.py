#!/usr/bin/env python3
"""
HackTheBox Machine Scraper - Live Version
Scrapes machine data from the actual HackTheBox website with appropriate delays
No login required - only scrapes publicly available data
"""
import requests
from bs4 import BeautifulSoup
import re
import json
import time
import random
import psycopg2
from psycopg2.extras import <PERSON><PERSON>
from datetime import datetime, timedelta
import os
import logging
from rich.console import Console
from rich.progress import Progress, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn, SpinnerColumn
from rich.panel import Panel
from rich.table import Table
from rich import print as rprint

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("htb_scraper")

# Create Rich console
console = Console()

# Database connection string
DB_URL = "***************************************************"

class HackTheBoxScraper:
    def __init__(self, base_url="https://www.hackthebox.com"):
        self.base_url = base_url
        self.session = requests.Session()
        
        # Set up a proper user agent string
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Referer': 'https://www.hackthebox.com/',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
        })
        
        # Check if robots.txt allows crawling
        self._check_robots_txt()
    
    def _check_robots_txt(self):
        """Check if robots.txt allows crawling the machines page"""
        try:
            robots_url = f"{self.base_url}/robots.txt"
            response = self.session.get(robots_url)
            if response.status_code == 200:
                # Very basic check - look for "Disallow: /machines" or similar
                if "Disallow: /machines" in response.text or "Disallow: /" in response.text:
                    console.log("[yellow]Warning:[/yellow] robots.txt disallows crawling the machines page. Proceed with caution.")
        except Exception as e:
            console.log(f"[yellow]Warning:[/yellow] Could not check robots.txt: {e}")
    
    def sleep_with_delay(self, progress_task=None):
        """Sleep with 30s base delay + random up to 15s to avoid rate limiting"""
        base_delay = 30
        jitter = random.uniform(0, 15)
        sleep_time = base_delay + jitter
        
        if progress_task:
            with console.status(f"[cyan]Waiting {sleep_time:.2f} seconds before next request...[/cyan]"):
                time.sleep(sleep_time)
        else:
            console.log(f"Waiting for {sleep_time:.2f} seconds...")
            time.sleep(sleep_time)
    
    def get_machines_list(self):
        """Get list of all publicly visible machines"""
        machines_url = f"{self.base_url}/machines"
        console.log(f"Fetching machines list from [link={machines_url}]{machines_url}[/link]")
        
        response = self.session.get(machines_url)
        if response.status_code != 200:
            console.log(f"[red]Failed to fetch machines, status code: {response.status_code}[/red]")
            return []
            
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Save the page content for debugging
        with open('debug_machines_page.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        console.log("Saved page content to debug_machines_page.html for inspection")
        
        machines = []
        
        # Try multiple approaches to find machine cards
        all_approaches = [
            # Approach 1: Standard machine cards
            soup.find_all('div', class_=lambda c: c and ('machine-card' in c or 'machineCard' in c)),
            # Approach 2: Table rows for machines
            soup.find_all('tr', class_=lambda c: c and ('machine' in c or 'htb-machine' in c)),
            # Approach 3: Generic card approach
            soup.find_all('div', class_=lambda c: c and ('card' in c))
        ]
        
        machine_cards = []
        for approach_cards in all_approaches:
            if approach_cards:
                machine_cards = approach_cards
                break
        
        if not machine_cards:
            # Last resort - try to find list items with machine names
            machine_cards = soup.find_all('li', class_=lambda c: c and ('machine' in c or 'htb-machine' in c))
            
        if not machine_cards:
            console.log("[yellow]No machine cards found using standard selectors. Page structure may have changed.[/yellow]")
            # Try looking for links to machine pages
            machine_links = soup.find_all('a', href=lambda h: h and '/machines/' in h)
            if machine_links:
                console.log(f"Found {len(machine_links)} machine links, will use these instead")
                for link in machine_links:
                    machine_id = re.search(r'/machines/([^/]+)', link['href'])
                    if machine_id:
                        machine_info = {
                            'id': machine_id.group(1),
                            'name': link.text.strip() if link.text.strip() else f"Machine {machine_id.group(1)}"
                        }
                        machines.append(machine_info)
        else:
            console.log(f"Found {len(machine_cards)} machine cards")
            
            for card in machine_cards:
                machine_info = {}
                
                # Machine name - look for common name elements
                name_elem = (
                    card.find('h4', class_=lambda c: c and 'machine-name' in c) or
                    card.find('div', class_=lambda c: c and 'name' in c) or
                    card.find('h3') or
                    card.find('h4') or
                    card.find('h5') or
                    card.find('span', class_=lambda c: c and 'name' in c)
                )
                
                if name_elem:
                    machine_info['name'] = name_elem.text.strip()
                else:
                    # Try to extract name from alt text of an image
                    img = card.find('img', alt=True)
                    if img:
                        machine_info['name'] = img['alt'].strip()
                
                # If no name found, try to get any text
                if 'name' not in machine_info:
                    text = card.text.strip()
                    if text:
                        # Try to extract a meaningful name
                        lines = [line.strip() for line in text.split('\n') if line.strip()]
                        if lines:
                            machine_info['name'] = lines[0]
                
                # If still no name found, skip this card
                if 'name' not in machine_info:
                    console.log("[yellow]Skipping card - could not find machine name[/yellow]")
                    continue
                    
                console.log(f"Processing machine: [bold]{machine_info['name']}[/bold]")
                
                # Machine logo
                logo_elem = card.find('img', class_=lambda c: c and ('machine-logo' in c or 'logo' in c))
                if logo_elem and 'src' in logo_elem.attrs:
                    machine_info['logo_url'] = logo_elem['src']
                    if machine_info['logo_url'].startswith('/'):
                        machine_info['logo_url'] = self.base_url + machine_info['logo_url']
                
                # Difficulty
                difficulty_elem = card.find('div', class_=lambda c: c and ('difficulty' in c))
                if difficulty_elem:
                    machine_info['difficulty'] = difficulty_elem.text.strip()
                else:
                    # Try different approaches to find difficulty
                    for elem in card.find_all(['div', 'span']):
                        text = elem.text.strip().lower()
                        if text in ['easy', 'medium', 'hard', 'insane']:
                            machine_info['difficulty'] = text.capitalize()
                            break
                
                # Operating System
                os_elem = card.find('img', class_=lambda c: c and ('os' in c or 'operating-system' in c))
                if os_elem and 'alt' in os_elem.attrs:
                    machine_info['os'] = os_elem['alt']
                elif os_elem and 'title' in os_elem.attrs:
                    machine_info['os'] = os_elem['title']
                
                # Machine ID for further details
                # Try different ways to get machine ID
                for attr in ['data-id', 'id', 'data-machine-id']:
                    machine_id = card.get(attr)
                    if machine_id:
                        machine_info['id'] = machine_id
                        break
                        
                # If no ID found, try to extract from URLs
                if 'id' not in machine_info:
                    link = card.find('a', href=True)
                    if link:
                        href = link['href']
                        # Extract ID from URL pattern like /machines/123 or similar
                        match = re.search(r'/machines/([^/]+)', href)
                        if match:
                            machine_info['id'] = match.group(1)
                
                machines.append(machine_info)
        
        return machines
    
    def get_machine_details(self, machine_id, machine_name="Unknown"):
        """Get detailed information about a specific machine"""
        machine_url = f"{self.base_url}/machines/{machine_id}"
        
        response = self.session.get(machine_url)
        if response.status_code != 200:
            console.log(f"[red]Failed to fetch machine details for [bold]{machine_name}[/bold], status code: {response.status_code}[/red]")
            return {}
            
        # Save page content for debugging
        with open(f'debug_machine_{machine_id}.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        details = {}
        
        # User owns
        user_owns_elem = soup.find('div', class_=lambda c: c and ('user-owns' in c or 'userOwns' in c))
        if user_owns_elem:
            owns_text = user_owns_elem.text.strip()
            owns_count = re.search(r'\d+', owns_text)
            if owns_count:
                details['user_owns'] = int(owns_count.group())
        
        # System owns
        system_owns_elem = soup.find('div', class_=lambda c: c and ('system-owns' in c or 'systemOwns' in c))
        if system_owns_elem:
            owns_text = system_owns_elem.text.strip()
            owns_count = re.search(r'\d+', owns_text)
            if owns_count:
                details['system_owns'] = int(owns_count.group())
        
        # Release date
        release_elem = soup.find(['div', 'span'], class_=lambda c: c and ('release' in c or 'date' in c))
        if release_elem:
            date_text = release_elem.text.strip()
            # Try different date formats
            date_patterns = [
                r'\d{2}/\d{2}/\d{4}',  # DD/MM/YYYY
                r'\d{2}-\d{2}-\d{4}',  # DD-MM-YYYY
                r'\d{4}-\d{2}-\d{2}',  # YYYY-MM-DD
                r'\d{2}\.\d{2}\.\d{4}'  # DD.MM.YYYY
            ]
            
            for pattern in date_patterns:
                date_match = re.search(pattern, date_text)
                if date_match:
                    details['release_date'] = date_match.group()
                    break
        
        # Machine matrix (5 axis ratings)
        matrix_elem = soup.find('div', class_=lambda c: c and ('matrix' in c or 'ratings' in c))
        if matrix_elem:
            axis_ratings = {}
            axes = matrix_elem.find_all('div', class_=lambda c: c and ('axis' in c or 'rating' in c))
            
            for axis in axes:
                # Try different ways to get axis name and value
                axis_name = None
                axis_value = None
                
                # Method 1: Dedicated spans for name and value
                name_span = axis.find('span', class_=lambda c: c and ('name' in c or 'label' in c))
                value_span = axis.find('span', class_=lambda c: c and ('value' in c or 'score' in c))
                
                if name_span and value_span:
                    axis_name = name_span.text.strip()
                    value_text = value_span.text.strip()
                    value_match = re.search(r'[\d.]+', value_text)
                    if value_match:
                        axis_value = float(value_match.group())
                
                # Method 2: Text contains both name and value
                if not (axis_name and axis_value):
                    text = axis.text.strip()
                    parts = text.split(':')
                    if len(parts) == 2:
                        axis_name = parts[0].strip()
                        value_match = re.search(r'[\d.]+', parts[1])
                        if value_match:
                            axis_value = float(value_match.group())
                
                if axis_name and axis_value:
                    axis_ratings[axis_name] = axis_value
            
            if axis_ratings:
                details['matrix'] = axis_ratings
        
        # Machine rating
        rating_elem = soup.find(['div', 'span'], class_=lambda c: c and ('rating' in c or 'score' in c))
        if rating_elem:
            rating_text = rating_elem.text.strip()
            rating_match = re.search(r'[\d.]+', rating_text)
            if rating_match:
                details['rating'] = float(rating_match.group())
        
        return details
    
    def scrape_all_machines(self, limit=None):
        """Scrape all machines with their details
        Args:
            limit: Optional limit on number of machines to scrape
        """
        machines = self.get_machines_list()
        if not machines:
            console.log("[red]No machines found to scrape[/red]")
            return []
            
        console.log(f"Retrieved list of [bold green]{len(machines)}[/bold green] machines")
        
        if limit:
            console.log(f"Limiting to [bold yellow]{limit}[/bold yellow] machines as requested")
            machines = machines[:limit]
            
        detailed_machines = []
        total = len(machines)
        
        # Calculate estimated time
        est_time_per_machine = 45  # seconds (30s base delay + avg 15s processing)
        total_est_seconds = total * est_time_per_machine
        est_completion_time = datetime.now() + timedelta(seconds=total_est_seconds)
        
        console.print(Panel(
            f"[bold]Starting scraper for {total} machines[/bold]\n"
            f"Estimated time: {timedelta(seconds=total_est_seconds)}\n"
            f"Expected completion: {est_completion_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"Average time per machine: {est_time_per_machine} seconds",
            title="[bold cyan]HackTheBox Scraper[/bold cyan]",
            border_style="green"
        ))
        
        # Create progress bar
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TextColumn("•"),
            TimeElapsedColumn(),
            TextColumn("•"),
            TimeRemainingColumn(),
        ) as progress:
            task = progress.add_task("[cyan]Scraping machines...", total=total)
            
            for i, machine in enumerate(machines):
                if 'id' not in machine:
                    progress.log(f"[yellow]Skipping machine {machine.get('name', 'Unknown')} - no ID found[/yellow]")
                    progress.update(task, advance=1)
                    continue
                
                name = machine.get('name', 'Unknown')
                progress.update(task, description=f"[cyan]Scraping machine {i+1}/{total}: [bold]{name}[/bold]")
                
                # Use the conservative delay between requests
                self.sleep_with_delay()
                
                details = self.get_machine_details(machine['id'], name)
                if details:
                    machine.update(details)
                    detailed_machines.append(machine)
                    
                    # Show a summary of the scraped machine
                    difficulty = machine.get('difficulty', 'Unknown')
                    difficulty_color = {
                        'Easy': 'green',
                        'Medium': 'yellow',
                        'Hard': 'red',
                        'Insane': 'magenta'
                    }.get(difficulty, 'blue')
                    
                    os_type = machine.get('os', 'Unknown')
                    progress.log(f"✓ [{difficulty_color}]{name}[/{difficulty_color}] ({os_type}) - " + 
                               f"Rating: {machine.get('rating', 'N/A')}, " + 
                               f"User/System Owns: {machine.get('user_owns', 'N/A')}/{machine.get('system_owns', 'N/A')}")
                    
                else:
                    progress.log(f"[red]Failed to retrieve details for {name}[/red]")
                
                progress.update(task, advance=1)
        
        return detailed_machines

def store_in_database(machines):
    """Store scraped machine data in the database"""
    conn = None
    try:
        # Connect to database
        conn = psycopg2.connect(DB_URL)
        cursor = conn.cursor()
        
        # Create table if not exists
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS hackthebox_machines (
                id SERIAL PRIMARY KEY,
                machine_id VARCHAR(50) UNIQUE,
                name VARCHAR(100) NOT NULL,
                logo_url TEXT,
                difficulty VARCHAR(20),
                operating_system VARCHAR(50),
                user_owns INTEGER,
                system_owns INTEGER,
                release_date DATE,
                rating FLOAT,
                matrix JSONB,
                provider VARCHAR(50) DEFAULT 'HackTheBox',
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        """)
        
        # Create indexes if not exists
        cursor.execute("""
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM pg_indexes 
                    WHERE indexname = 'idx_htb_machine_name'
                ) THEN
                    CREATE INDEX idx_htb_machine_name ON hackthebox_machines(name);
                END IF;
                
                IF NOT EXISTS (
                    SELECT 1 FROM pg_indexes 
                    WHERE indexname = 'idx_htb_difficulty'
                ) THEN
                    CREATE INDEX idx_htb_difficulty ON hackthebox_machines(difficulty);
                END IF;
                
                IF NOT EXISTS (
                    SELECT 1 FROM pg_indexes 
                    WHERE indexname = 'idx_htb_os'
                ) THEN
                    CREATE INDEX idx_htb_os ON hackthebox_machines(operating_system);
                END IF;
            END
            $$;
        """)
        
        # Progress for database operations
        with console.status("[bold green]Storing data in database...[/bold green]"):
            # Insert data
            for machine in machines:
                try:
                    # Parse date with various formats
                    release_date = None
                    if 'release_date' in machine and machine['release_date']:
                        date_str = machine['release_date']
                        date_formats = [
                            '%d/%m/%Y',  # DD/MM/YYYY
                            '%d-%m-%Y',  # DD-MM-YYYY
                            '%Y-%m-%d',  # YYYY-MM-DD
                            '%d.%m.%Y'   # DD.MM.YYYY
                        ]
                        
                        for fmt in date_formats:
                            try:
                                release_date = datetime.strptime(date_str, fmt).date()
                                break
                            except ValueError:
                                continue
                    
                    cursor.execute("""
                        INSERT INTO hackthebox_machines 
                        (machine_id, name, logo_url, difficulty, operating_system, 
                        user_owns, system_owns, release_date, rating, matrix)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (machine_id) DO UPDATE SET
                        name = EXCLUDED.name,
                        logo_url = EXCLUDED.logo_url,
                        difficulty = EXCLUDED.difficulty,
                        operating_system = EXCLUDED.operating_system,
                        user_owns = EXCLUDED.user_owns,
                        system_owns = EXCLUDED.system_owns,
                        release_date = EXCLUDED.release_date,
                        rating = EXCLUDED.rating,
                        matrix = EXCLUDED.matrix,
                        updated_at = NOW()
                    """, (
                        machine.get('id'),
                        machine.get('name'),
                        machine.get('logo_url'),
                        machine.get('difficulty'),
                        machine.get('os'),
                        machine.get('user_owns'),
                        machine.get('system_owns'),
                        release_date,
                        machine.get('rating'),
                        Json(machine.get('matrix', {}))
                    ))
                except Exception as e:
                    console.log(f"[red]Error storing machine {machine.get('name')}: {str(e)}[/red]")
            
            conn.commit()
        
        console.print(f"[bold green]Successfully stored {len(machines)} machines in the database[/bold green]")
        
        # Query and display some data
        cursor.execute("SELECT COUNT(*) FROM hackthebox_machines")
        total_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT difficulty, COUNT(*) FROM hackthebox_machines GROUP BY difficulty")
        difficulty_counts = cursor.fetchall()
        
        cursor.execute("SELECT operating_system, COUNT(*) FROM hackthebox_machines GROUP BY operating_system")
        os_counts = cursor.fetchall()
        
        cursor.execute("SELECT AVG(rating) FROM hackthebox_machines")
        avg_rating = cursor.fetchone()[0]
        
        # Create a summary table
        table = Table(title="Database Summary", show_header=True, header_style="bold cyan")
        table.add_column("Category", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Total machines", str(total_count))
        table.add_row("Average rating", f"{avg_rating:.2f}")
        
        console.print(table)
        
        # Difficulty distribution table
        diff_table = Table(title="Difficulty Distribution", show_header=True, header_style="bold cyan")
        diff_table.add_column("Difficulty", style="cyan")
        diff_table.add_column("Count", style="green")
        diff_table.add_column("Percentage", style="yellow")
        
        for diff, count in difficulty_counts:
            percent = (count / total_count) * 100
            difficulty_color = {
                'Easy': 'green',
                'Medium': 'yellow',
                'Hard': 'red',
                'Insane': 'magenta'
            }.get(diff, 'cyan') if diff else 'cyan'
            
            diff_table.add_row(
                f"[{difficulty_color}]{diff or 'Unknown'}[/{difficulty_color}]", 
                str(count), 
                f"{percent:.1f}%"
            )
        
        console.print(diff_table)
        
        # OS distribution table
        os_table = Table(title="Operating System Distribution", show_header=True, header_style="bold cyan")
        os_table.add_column("Operating System", style="cyan")
        os_table.add_column("Count", style="green")
        os_table.add_column("Percentage", style="yellow")
        
        for os_name, count in os_counts:
            percent = (count / total_count) * 100
            os_table.add_row(
                os_name or "Unknown", 
                str(count), 
                f"{percent:.1f}%"
            )
        
        console.print(os_table)
        
    except Exception as e:
        console.print(f"[bold red]Database error: {str(e)}[/bold red]")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

def main():
    console.print(Panel(
        "[bold green]HackTheBox Machine Scraper - Live Version[/bold green]\n"
        "[bold cyan]No login required - scrapes publicly available data[/bold cyan]",
        border_style="green"
    ))
    
    # No machine limit by default - scrape all machines
    machine_limit = int(os.environ.get('HTB_MACHINE_LIMIT', 0))
    if machine_limit > 0:
        console.print(f"[yellow]Note: Limiting to {machine_limit} machines as requested via HTB_MACHINE_LIMIT env var[/yellow]")
    
    # Initialize scraper
    scraper = HackTheBoxScraper()
    
    console.print("[cyan]Starting scraping as a non-logged-in user...[/cyan]")
    console.print("[yellow]Using conservative rate limits (30s base + up to 15s random delay)[/yellow]")
    
    start_time = datetime.now()
    
    # Scrape machines
    machines = scraper.scrape_all_machines(limit=machine_limit)
    
    end_time = datetime.now()
    elapsed_time = end_time - start_time
    
    if not machines:
        console.print("[bold red]No machines were successfully scraped[/bold red]")
        return
        
    # Store data in database
    console.print(f"[cyan]Storing {len(machines)} machines in database...[/cyan]")
    store_in_database(machines)
    
    # Also save as JSON for backup
    output_file = 'htb_machines_live.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(machines, f, indent=2, ensure_ascii=False)
    
    console.print(f"[green]Scraped data saved to {output_file}[/green]")
    
    # Print final summary
    console.print(Panel(
        f"[bold green]Process complete![/bold green]\n"
        f"Scraped [bold]{len(machines)}[/bold] machines in [bold]{elapsed_time}[/bold]\n"
        f"Average time per machine: [bold]{elapsed_time.total_seconds() / len(machines):.2f}[/bold] seconds",
        title="[bold cyan]Summary[/bold cyan]",
        border_style="green"
    ))

if __name__ == "__main__":
    main() 