<div align="center">

# 🔐 Security Certification Explorer

[![React](https://img.shields.io/badge/React-18.x-61DAFB?style=for-the-badge&logo=react&logoColor=white)](https://reactjs.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.95.x-009688?style=for-the-badge&logo=fastapi&logoColor=white)](https://fastapi.tiangolo.com/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x-3178C6?style=for-the-badge&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15.x-4169E1?style=for-the-badge&logo=postgresql&logoColor=white)](https://www.postgresql.org/)
[![Docker](https://img.shields.io/badge/Docker-Compose-2496ED?style=for-the-badge&logo=docker&logoColor=white)](https://www.docker.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg?style=for-the-badge)](LICENSE)

<img src="https://raw.githubusercontent.com/forkrul/replit-CertPathFinder/master/frontend/public/logo192.png" alt="Security Certification Explorer Logo" width="150"/>

**Navigate the complex world of cybersecurity certifications with confidence**

[Features](#features) • [Setup](#setup-and-installation) • [Development](#development) • [Testing](#testing-strategy) • [Contributing](#contributing)

</div>

## 🚀 Project Overview

A comprehensive React-powered platform for exploring security certifications, providing interactive guidance and advanced data management for cybersecurity professionals and enthusiasts. The application helps users discover relevant certifications based on their interests, experience level, and budget constraints.

### 🎯 The Challenge

- **Overwhelming Options**: Cybersecurity has a vast array of certifications
- **Unclear Pathways**: It's hard to know which certifications are most relevant for specific job roles
- **Industry Confusion**: Creates uncertainty for both job seekers and employers

### 🏆 The Goal

Create an intelligent system that maps cybersecurity certifications to:
- Specific job roles (e.g., Security Analyst, Penetration Tester)
- Career levels (Entry-Level, Mid-Level, Senior)
- Skill domains and competency areas

### 🛠️ Our Approach

1. **Analyze the Certification Landscape**
   - Leverage the PaulJerimy Security Certification Roadmap
   - Map key certification bodies like (ISC)², CompTIA, GIAC, and Offensive Security
   - Categorize certification levels (Foundational, Core, Advanced)

2. **Define Roles and Career Progression**
   - Map common cybersecurity job roles and responsibilities
   - Establish standardized career levels
   - Align with the NICE Cybersecurity Workforce Framework

3. **Develop Intelligent Mapping**
   - Analyze industry surveys and job postings
   - Incorporate certification body guidance
   - Create a comprehensive certification-to-role matrix

4. **Build Interactive Tools**
   - Interactive career path visualization
   - Detailed certification explorer
   - Personalized assessment and recommendations

### 💼 Benefits

- **For Job Seekers**: Clear certification pathways aligned with career goals
- **For Employers**: Better candidate evaluation and skill gap analysis
- **For the Industry**: Standardized framework for cybersecurity competencies

## ✨ Features

<details open>
<summary><h3>🌟 Current Features</h3></summary>

<table>
  <tr>
    <td width="50%">
      <h4>🖥️ Interactive Main Page</h4>
      <ul>
        <li>Engaging hero section with platform value proposition</li>
        <li>Dashboard preview with key metrics visualization</li>
        <li>Career path navigator with interactive visualization</li>
        <li>Certification catalog with filtering capabilities</li>
        <li>Success stories from platform users</li>
        <li>Step-by-step getting started guide</li>
      </ul>
    </td>
    <td width="50%">
      <h4>🔍 Interactive Filtering System</h4>
      <ul>
        <li>Multi-select domain filter buttons for precise domain selection</li>
        <li>Experience level filtering (Entry Level to Expert)</li>
        <li>Cost-based filtering with dynamic slider</li>
        <li>Real-time results updating</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td>
      <h4>📊 Comprehensive Data Display</h4>
      <ul>
        <li>Detailed certification information</li>
        <li>Cost and prerequisites details</li>
        <li>Organization and domain categorization</li>
        <li>Direct links to certification pages</li>
      </ul>
    </td>
    <td>
      <h4>🌐 Multilingual Support</h4>
      <ul>
        <li>Full internationalization (i18n) support</li>
        <li>Available languages:</li>
        <ul>
          <li>🇬🇧 English (en)</li>
          <li>🇩🇪 German (de)</li>
          <li>🇪🇸 Spanish (es)</li>
          <li>🇫🇷 French (fr)</li>
          <li>🇿🇦 Afrikaans (af)</li>
          <li>🇿🇦 isiZulu (zu)</li>
          <li>🇷🇴 Romanian (ro)</li>
        </ul>
        <li>Dynamic language switching</li>
        <li>Localized content and UI elements</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td>
      <h4>💾 Database Integration</h4>
      <ul>
        <li>PostgreSQL database for robust data management</li>
        <li>Automatic data extraction from Security Certification Roadmap</li>
        <li>Structured certification data model</li>
      </ul>
    </td>
    <td>
      <h4>⚛️ Modern React UI</h4>
      <ul>
        <li>Component-based architecture</li>
        <li>TypeScript for type safety</li>
        <li>Responsive design for all devices</li>
        <li>Improved user experience</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td colspan="2">
      <h4>🔄 API Versioning System</h4>
      <ul>
        <li>Centralized API route management with versioning (api/v1/...)</li>
        <li>Consistent endpoint definitions between frontend and backend</li>
        <li>React hooks for accessing versioned API endpoints</li>
        <li>Future-proof architecture for API evolution</li>
      </ul>
    </td>
  </tr>
</table>
</details>

<details>
<summary><h3>🔮 Upcoming Features</h3></summary>

<table>
  <tr>
    <td width="50%">
      <h4>📈 Advanced Visualization</h4>
      <ul>
        <li>D3.js-powered certification progression paths</li>
        <li>Domain-specific certification hierarchies</li>
        <li>Interactive certification relationship mapping</li>
        <li>Visual prerequisite chains</li>
        <li>Improved node sizing based on certification difficulty</li>
      </ul>
    </td>
    <td width="50%">
      <h4>📊 Enhanced Data Analysis</h4>
      <ul>
        <li>Certification comparison tools</li>
        <li>Cost-benefit analysis</li>
        <li>Career path optimization</li>
        <li>Prerequisites tracking</li>
        <li>Node size representation refinement</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td>
      <h4>⏱️ Certification Study Timer</h4>
      <ul>
        <li>Track study time for each certification</li>
        <li>Set study goals and receive progress notifications</li>
        <li>Generate study schedules based on exam dates</li>
        <li>Study session analytics</li>
      </ul>
    </td>
    <td>
      <h4>💰 Advanced Cost Calculator</h4>
      <ul>
        <li>Currency conversion support</li>
        <li>Exam retake costs calculation</li>
        <li>Provider cost comparison</li>
        <li>Bundle pricing analysis</li>
        <li>Regional price variations</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td>
      <h4>🤖 AI-Powered Study Guide</h4>
      <ul>
        <li>Custom study guides generation</li>
        <li>AI-generated practice questions</li>
        <li>Personalized learning paths</li>
        <li>Adaptive content difficulty</li>
      </ul>
    </td>
    <td>
      <h4>👥 Community Features</h4>
      <ul>
        <li>User certification reviews</li>
        <li>Study group formation</li>
        <li>Study material sharing</li>
        <li>Success stories and testimonials</li>
        <li>Mentor matching</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td>
      <h4>📱 Mobile-Friendly Features</h4>
      <ul>
        <li>Study progress notifications</li>
        <li>Exam date reminders</li>
        <li>Quick certification lookup</li>
        <li>Mobile-optimized study materials</li>
      </ul>
    </td>
    <td>
      <h4>📊 Progress Tracking Dashboard</h4>
      <ul>
        <li>Visual progress tracking</li>
        <li>Certification completion timeline</li>
        <li>Study milestone achievements</li>
        <li>Skill gap analysis</li>
        <li>Performance metrics</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td colspan="2">
      <h4>🔄 Certification Data Enrichment (In Progress)</h4>
      <ul>
        <li>Automated online data verification</li>
        <li>Historical version tracking</li>
        <li>Change detection and review</li>
        <li>Data quality monitoring</li>
        <li>Automatic updates with admin approval</li>
      </ul>
    </td>
  </tr>
</table>
</details>

## 🏗️ Technical Architecture

<details>
<summary><h3>📊 Data Flow</h3></summary>

```mermaid
sequenceDiagram
    participant User
    participant UI as React Interface
    participant API as FastAPI Backend
    participant Filter as Filter System
    participant DB as PostgreSQL Database
    participant Viz as D3.js Visualization

    User->>UI: Select Filters
    UI->>API: Request Data
    API->>Filter: Apply Criteria
    Filter->>DB: Query Certifications
    DB-->>Filter: Return Matches
    Filter-->>API: Process Results
    API-->>UI: Return Data
    UI->>Viz: Generate Path
    Viz-->>UI: Display Graph
```
</details>

<details>
<summary><h3>🧩 Components</h3></summary>

<table>
  <tr>
    <th>Component</th>
    <th>Technology</th>
    <th>Description</th>
  </tr>
  <tr>
    <td>Frontend</td>
    <td>React with TypeScript</td>
    <td>Modern component-based UI with type safety</td>
  </tr>
  <tr>
    <td>Backend</td>
    <td>FastAPI</td>
    <td>High-performance Python API framework</td>
  </tr>
  <tr>
    <td>Database</td>
    <td>PostgreSQL</td>
    <td>Robust relational database for certification data</td>
  </tr>
  <tr>
    <td>Data Processing</td>
    <td>Python with SQLAlchemy</td>
    <td>Efficient ORM for database operations</td>
  </tr>
  <tr>
    <td>Visualization</td>
    <td>D3.js</td>
    <td>Advanced data visualization library</td>
  </tr>
  <tr>
    <td>Deployment</td>
    <td>Docker Compose with Nginx</td>
    <td>Containerized deployment with reverse proxy</td>
  </tr>
</table>
</details>

## 🚀 Setup and Installation

<div align="center">

[![Docker Required](https://img.shields.io/badge/Docker-Required-2496ED?style=for-the-badge&logo=docker&logoColor=white)](https://docs.docker.com/get-docker/)
[![Docker Compose Required](https://img.shields.io/badge/Docker_Compose-Required-2496ED?style=for-the-badge&logo=docker&logoColor=white)](https://docs.docker.com/compose/install/)

</div>

```bash
# 1. Clone the repository
git clone https://github.com/forkrul/replit-CertPathFinder.git
cd replit-CertPathFinder

# 2. Start the application
./.dockerwrapper/run.sh up -d

# 3. Access the application
# Frontend: http://localhost:8080/
# API Docs: http://localhost:8080/api/docs
```

## 👨‍💻 Development

<details open>
<summary><h3>📋 Current Status</h3></summary>

<table>
  <tr>
    <td width="50%">
      <h4>✅ Completed</h4>
      <ul>
        <li>Main page interface with all planned features</li>
        <li>Interactive dashboard with Chart.js visualizations</li>
        <li>Career path navigator with D3.js visualization</li>
        <li>Certification catalog with filtering capabilities</li>
        <li>Success stories component with testimonial carousel</li>
        <li>Getting started guide with interactive navigation</li>
        <li>Performance optimizations and analytics tracking</li>
        <li>Basic filtering system implementation</li>
        <li>Database schema establishment</li>
        <li>Data extraction pipeline</li>
        <li>Multi-select domain filters</li>
        <li>Full internationalization (i18n) support</li>
        <li>Multiple language translations</li>
      </ul>
    </td>
    <td width="50%">
      <h4>⚠️ Known Issues</h4>
      <ul>
        <li>Node sizing based on certification difficulty needs improvement</li>
        <li>Graph interaction stability needs enhancement</li>
      </ul>
      <h4>🔄 In Progress</h4>
      <ul>
        <li>Certification data enrichment system</li>
        <li>Certification page implementation</li>
        <li>Graph interaction stability improvements</li>
      </ul>
    </td>
  </tr>
</table>
</details>

<details>
<summary><h3>🗺️ Roadmap</h3></summary>

<table>
  <tr>
    <th>Priority</th>
    <th>Task</th>
    <th>Status</th>
  </tr>
  <tr>
    <td>1</td>
    <td>Implement certification page based on PRD</td>
    <td>🔄 In Progress</td>
  </tr>
  <tr>
    <td>2</td>
    <td>Implement certification data enrichment system</td>
    <td>🔄 In Progress</td>
  </tr>
  <tr>
    <td>3</td>
    <td>Enhance graph interaction stability</td>
    <td>🔄 In Progress</td>
  </tr>
  <tr>
    <td>4</td>
    <td>Implement certification progression mapping</td>
    <td>📅 Planned</td>
  </tr>
  <tr>
    <td>5</td>
    <td>Enhance data extraction for prerequisites</td>
    <td>📅 Planned</td>
  </tr>
  <tr>
    <td>6</td>
    <td>Develop comparison features</td>
    <td>📅 Planned</td>
  </tr>
  <tr>
    <td>7</td>
    <td>Complete translations for all pages</td>
    <td>📅 Planned</td>
  </tr>
  <tr>
    <td>8</td>
    <td>Implement comprehensive UI testing with Playwright</td>
    <td>📅 Planned</td>
  </tr>
  <tr>
    <td>9</td>
    <td>Enhance component testing with React Testing Library</td>
    <td>📅 Planned</td>
  </tr>
</table>
</details>

<details>
<summary><h3>👨‍💻 Developer Guidance</h3></summary>

#### Code Standards

<table>
  <tr>
    <th colspan="2">Python Standards (PEP Guidelines)</th>
  </tr>
  <tr>
    <td width="33%"><strong>PEP-8: Style Guide</strong></td>
    <td>
      <ul>
        <li>4 spaces for indentation</li>
        <li>79 character line length</li>
        <li>snake_case for functions/variables</li>
        <li>PascalCase for classes</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td><strong>PEP-257: Docstrings</strong></td>
    <td>
      <ul>
        <li>All modules, functions, classes need docstrings</li>
        <li>Triple quotes for all docstrings</li>
        <li>Summary line followed by blank line</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td><strong>PEP-484: Type Hints</strong></td>
    <td>
      <ul>
        <li>Type hints for all function arguments and returns</li>
        <li>Use Optional[] for nullable values</li>
        <li>Use typing module (List[], Dict[], etc.)</li>
      </ul>
    </td>
  </tr>
  <tr>
    <th colspan="2">TypeScript/React Standards</th>
  </tr>
  <tr>
    <td><strong>Component Structure</strong></td>
    <td>
      <ul>
        <li>Functional components with hooks</li>
        <li>Props interfaces for all components</li>
        <li>Component files named same as component</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td><strong>State Management</strong></td>
    <td>
      <ul>
        <li>React Context for global state</li>
        <li>useState for component-level state</li>
        <li>Custom hooks for reusable logic</li>
      </ul>
    </td>
  </tr>
</table>

#### Refactoring Plan

<div align="center">

```mermaid
gantt
    title Refactoring Timeline
    dateFormat  YYYY-MM-DD
    section Code Quality
    Code Style & Documentation    :a1, 2023-10-01, 14d
    Code Organization             :a2, after a1, 14d
    section Testing
    Testing Infrastructure        :b1, after a2, 14d
    section Optimization
    Performance Optimization      :c1, after b1, 7d
    Security Hardening            :c2, after c1, 7d
```

</div>

#### Implementation Priorities

1. ⚠️ Core functionality must remain stable during refactoring
2. 🔄 Changes must be backward compatible
3. 🧪 Each phase must include thorough testing
4. 📝 Documentation must be updated with changes
5. 📊 Performance impact must be monitored
</details>

## 🧪 Testing Strategy

<details>
<summary><h3>Component Testing</h3></summary>

We use **React Testing Library** with these principles:

<table>
  <tr>
    <td width="50%">
      <h4>🧠 Philosophy</h4>
      <ul>
        <li><strong>Test behavior, not implementation</strong></li>
        <li><strong>Accessibility-first</strong> query approach</li>
        <li><strong>Realistic user interactions</strong></li>
        <li><strong>Mocked API layer</strong> with MSW</li>
      </ul>
    </td>
    <td width="50%">
      <h4>📋 Coverage Goals</h4>
      <ul>
        <li><strong>Unit/Component:</strong> 80%+ coverage</li>
        <li><strong>Integration:</strong> Key user flows</li>
        <li><strong>E2E:</strong> Critical paths</li>
      </ul>
    </td>
  </tr>
</table>
</details>

<details>
<summary><h3>E2E Testing</h3></summary>

We use **Playwright** for end-to-end testing with these goals:

<table>
  <tr>
    <td width="50%">
      <h4>🌐 Cross-Browser Testing</h4>
      <ul>
        <li>Chrome, Firefox, and Safari verification</li>
        <li>Visual regression testing</li>
        <li>Complete user journey testing</li>
      </ul>
    </td>
    <td width="50%">
      <h4>📱 Responsive Testing</h4>
      <ul>
        <li>Mobile viewport testing</li>
        <li>Tablet viewport testing</li>
        <li>Desktop viewport testing</li>
      </ul>
    </td>
  </tr>
</table>

#### Implementation Timeline

1. **Phase 1 (2 weeks)**: React Testing Library setup and initial component tests
2. **Phase 2 (2 weeks)**: Playwright configuration and first E2E tests
3. **Phase 3 (ongoing)**: Expand test coverage and CI pipeline integration
</details>

## 🤝 Contributing

<div align="center">

[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=for-the-badge)](CONTRIBUTING.md)
[![Code of Conduct](https://img.shields.io/badge/Code%20of%20Conduct-v1.0-ff69b4.svg?style=for-the-badge)](CODE_OF_CONDUCT.md)

</div>

Please read our [contributing guidelines](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License

<div align="center">

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg?style=for-the-badge)](LICENSE)

</div>

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔄 Migration from Streamlit to React

<div align="center">

✅ **Migration Complete!**

</div>

The frontend has been successfully migrated from Streamlit to React while maintaining FastAPI as the backend.

<details>
<summary><h3>Migration Highlights</h3></summary>

<table>
  <tr>
    <td width="50%">
      <h4>✅ Migrated Features</h4>
      <ul>
        <li>Dashboard</li>
        <li>User Management</li>
        <li>Certification Explorer</li>
        <li>Reports</li>
        <li>Admin Interface</li>
      </ul>
    </td>
    <td width="50%">
      <h4>🎁 Benefits</h4>
      <ul>
        <li>Improved user experience with modern UI</li>
        <li>Enhanced maintainability with component architecture</li>
        <li>Better performance with client-side rendering</li>
        <li>Type safety with TypeScript</li>
        <li>Better separation of concerns</li>
      </ul>
    </td>
  </tr>
</table>

For more details, see the [Migration Guide](MIGRATION.md) and [Migration Milestones](.claude/milestones.md).
</details>

<details>
<summary><h3>🐳 Docker Environment</h3></summary>

All Docker-related files are located in the `.dockerwrapper` directory. The setup supports both multi-container and single-container deployments.

```bash
# Start the application
./.dockerwrapper/run.sh up -d
```

This will start the following services:

- FastAPI backend
- React frontend
- PostgreSQL database
- Nginx reverse proxy

Access points:
- React UI: http://localhost:8080/
- FastAPI Swagger UI: http://localhost:8080/api/docs
</details>

## 🆕 Recent Updates

<details>
<summary><h3>Latest Changes</h3></summary>

<table>
  <tr>
    <th>Update</th>
    <th>Description</th>
  </tr>
  <tr>
    <td><strong>TypeScript Error Remediation</strong></td>
    <td>
      <ul>
        <li>Fixed SVG Icon component type assertions</li>
        <li>Improved D3.js visualization typing</li>
        <li>Enhanced API client interfaces</li>
        <li>Updated component interfaces for better type safety</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td><strong>Main Page Interface Design</strong></td>
    <td>
      <ul>
        <li>Created comprehensive PRD for main page implementation</li>
        <li>Designed modular component architecture</li>
        <li>Planned implementation in phases for iterative deployment</li>
        <li>Added success metrics for feature validation</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td><strong>README Enhancement</strong></td>
    <td>
      <ul>
        <li>Improved GitHub presentation with modern styling</li>
        <li>Added badges, collapsible sections, and tables</li>
        <li>Enhanced visual organization of project information</li>
        <li>Updated with emoji icons for better readability</li>
      </ul>
    </td>
  </tr>
</table>
</details>

---

<div align="center">
<p>Made with ❤️ by the Security Certification Explorer Team</p>
</div>
