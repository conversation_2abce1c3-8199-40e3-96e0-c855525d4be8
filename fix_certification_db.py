#!/usr/bin/env python3
"""
Script to reset and properly seed the certification_explorer table
"""

def main():
    """Main function to seed the database"""
    print("=== Certification Database Fix Script ===")
    print("This script will reset and reseed the certification database")
    
    try:
        # Import modules inside the function to handle potential import errors
        from database import get_db
        from data.seed import load_certification_data
        import json
        from models.certification_explorer import CertificationExplorer
        from sqlalchemy import text

        # Get database session
        print("Connecting to database...")
        db = next(get_db())
        
        # Check current certification count
        current_count = db.query(CertificationExplorer).count()
        print(f"Current certification count: {current_count}")
        
        # Truncate the table if there are certifications
        if current_count > 0:
            print("Clearing existing certifications table...")
            db.execute(text("TRUNCATE TABLE certification_explorer CASCADE"))
            db.commit()
            print("Table cleared successfully")
        
        # Load certification data
        print("Loading certification data...")
        certifications_data = load_certification_data()
        print(f"Loaded {len(certifications_data)} certifications from data file")
        
        # Import certifications
        success_count = 0
        error_count = 0
        
        # Helper functions to map certification data
        def map_certification_type(category):
            category_lower = category.lower() if category else ""
            if "security" in category_lower or "pentest" in category_lower or "forensic" in category_lower:
                return "SECURITY"
            if "cloud" in category_lower:
                return "CLOUD"
            if "network" in category_lower:
                return "NETWORKING"
            if "dev" in category_lower or "program" in category_lower:
                return "DEVELOPMENT"
            if "devops" in category_lower or "ops" in category_lower:
                return "DEVOPS"
            return "SECURITY"  # Default
            
        def map_certification_level(level, difficulty=None):
            if not level:
                level = ""
            level_lower = level.lower()
            
            if difficulty and isinstance(difficulty, (int, float)):
                if difficulty <= 2:
                    return "BEGINNER"
                if difficulty <= 3:
                    return "INTERMEDIATE"
                return "ADVANCED"
                
            if "beginner" in level_lower or "foundation" in level_lower or "fundamental" in level_lower:
                return "BEGINNER"
            if "intermediate" in level_lower or "associate" in level_lower:
                return "INTERMEDIATE"
            if "advanced" in level_lower or "expert" in level_lower or "professional" in level_lower:
                return "ADVANCED"
            return "INTERMEDIATE"  # Default
            
        def map_security_domain(domain):
            if not domain:
                return "GENERAL_SECURITY"
            domain_lower = domain.lower()
            
            domain_map = {
                "application": "APPLICATION_SECURITY",
                "app": "APPLICATION_SECURITY",
                "network": "NETWORK_SECURITY",
                "cloud": "CLOUD_SECURITY",
                "offensive": "OFFENSIVE_SECURITY",
                "pentest": "OFFENSIVE_SECURITY",
                "defensive": "DEFENSIVE_SECURITY",
                "forensic": "FORENSICS",
                "governance": "GOVERNANCE",
                "risk": "RISK_MANAGEMENT",
                "compliance": "COMPLIANCE",
                "audit": "AUDIT",
                "identity": "IDENTITY_MANAGEMENT",
                "access": "IDENTITY_MANAGEMENT",
                "management": "SECURITY_MANAGEMENT"
            }
            
            for key, value in domain_map.items():
                if key in domain_lower:
                    return value
            
            return "GENERAL_SECURITY"  # Default
        
        print("Starting import process...")
        
        # Process each certification
        for name, cert_data in certifications_data.items():
            try:
                # Create certification explorer record
                cert = CertificationExplorer(
                    name=name,
                    provider=cert_data['organization']['name'] if 'organization' in cert_data else cert_data.get('provider', 'Unknown'),
                    description=cert_data.get('description', ''),
                    type=map_certification_type(cert_data.get('category', '')),
                    level=map_certification_level(cert_data.get('level', ''), cert_data.get('difficulty')),
                    domain=map_security_domain(cert_data.get('domain', '')),
                    price=cert_data.get('cost') if cert_data.get('cost') else None,
                    prerequisites_list=cert_data.get('prerequisites', []),
                    skills_covered=[],
                    url=cert_data.get('url', '')
                )
                db.add(cert)
                success_count += 1
                
                # Commit every 100 records
                if success_count % 100 == 0:
                    db.commit()
                    print(f"Added {success_count} certifications so far...")
                
            except Exception as e:
                print(f"Error adding certification {name}: {e}")
                error_count += 1
        
        # Final commit
        db.commit()
        print(f"Successfully added {success_count} certifications with {error_count} errors")
        
        # Verify
        final_count = db.query(CertificationExplorer).count()
        print(f"Total certifications in explorer table: {final_count}")
        
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    main() 