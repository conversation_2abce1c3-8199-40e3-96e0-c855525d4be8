# Performance Optimization Components
# CDN, Caching, Database Optimization, and Performance Monitoring

# Redis Cluster for High-Performance Caching
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-cluster
  namespace: certrats
  labels:
    app: redis-cluster
    component: cache
spec:
  serviceName: redis-cluster-headless
  replicas: 6
  selector:
    matchLabels:
      app: redis-cluster
      component: cache
  template:
    metadata:
      labels:
        app: redis-cluster
        component: cache
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: client
        - containerPort: 16379
          name: gossip
        command:
        - redis-server
        - /etc/redis/redis.conf
        - --cluster-enabled
        - "yes"
        - --cluster-config-file
        - /data/nodes.conf
        - --cluster-node-timeout
        - "5000"
        - --appendonly
        - "yes"
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: redis-config
        configMap:
          name: redis-cluster-config
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi

---
apiVersion: v1
kind: Service
metadata:
  name: redis-cluster-headless
  namespace: certrats
  labels:
    app: redis-cluster
    component: cache
spec:
  clusterIP: None
  ports:
  - port: 6379
    targetPort: 6379
    name: client
  - port: 16379
    targetPort: 16379
    name: gossip
  selector:
    app: redis-cluster
    component: cache

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-cluster-config
  namespace: certrats
  labels:
    app: redis-cluster
    component: cache
data:
  redis.conf: |
    # Redis Cluster Configuration for High Performance
    
    # Network
    bind 0.0.0.0
    port 6379
    timeout 300
    tcp-keepalive 300
    
    # Memory Management
    maxmemory 512mb
    maxmemory-policy allkeys-lru
    
    # Persistence
    save 900 1
    save 300 10
    save 60 10000
    
    # Cluster
    cluster-enabled yes
    cluster-config-file nodes.conf
    cluster-node-timeout 5000
    cluster-announce-ip $(POD_IP)
    
    # Performance
    tcp-backlog 511
    databases 1
    
    # Logging
    loglevel notice
    logfile ""

---
# Database Connection Pooler (PgBouncer)
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pgbouncer
  namespace: certrats
  labels:
    app: pgbouncer
    component: database-proxy
spec:
  replicas: 2
  selector:
    matchLabels:
      app: pgbouncer
      component: database-proxy
  template:
    metadata:
      labels:
        app: pgbouncer
        component: database-proxy
    spec:
      containers:
      - name: pgbouncer
        image: pgbouncer/pgbouncer:latest
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: DATABASES_HOST
          value: "postgres-service"
        - name: DATABASES_PORT
          value: "5432"
        - name: DATABASES_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secrets
              key: POSTGRES_USER
        - name: DATABASES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secrets
              key: POSTGRES_PASSWORD
        - name: DATABASES_DBNAME
          valueFrom:
            secretKeyRef:
              name: postgres-secrets
              key: POSTGRES_DB
        - name: POOL_MODE
          value: "transaction"
        - name: MAX_CLIENT_CONN
          value: "1000"
        - name: DEFAULT_POOL_SIZE
          value: "25"
        - name: MIN_POOL_SIZE
          value: "5"
        - name: RESERVE_POOL_SIZE
          value: "5"
        - name: SERVER_LIFETIME
          value: "3600"
        - name: SERVER_IDLE_TIMEOUT
          value: "600"
        volumeMounts:
        - name: pgbouncer-config
          mountPath: /etc/pgbouncer
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          tcpSocket:
            port: 5432
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          tcpSocket:
            port: 5432
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: pgbouncer-config
        configMap:
          name: pgbouncer-config

---
apiVersion: v1
kind: Service
metadata:
  name: pgbouncer-service
  namespace: certrats
  labels:
    app: pgbouncer
    component: database-proxy
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
    name: postgres
  selector:
    app: pgbouncer
    component: database-proxy

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: pgbouncer-config
  namespace: certrats
  labels:
    app: pgbouncer
    component: database-proxy
data:
  pgbouncer.ini: |
    [databases]
    certrats = host=postgres-service port=5432 dbname=certrats
    
    [pgbouncer]
    listen_addr = 0.0.0.0
    listen_port = 5432
    auth_type = md5
    auth_file = /etc/pgbouncer/userlist.txt
    
    # Pool settings
    pool_mode = transaction
    max_client_conn = 1000
    default_pool_size = 25
    min_pool_size = 5
    reserve_pool_size = 5
    
    # Connection limits
    server_lifetime = 3600
    server_idle_timeout = 600
    server_connect_timeout = 15
    server_login_retry = 15
    
    # Logging
    log_connections = 1
    log_disconnections = 1
    log_pooler_errors = 1
    
    # Performance
    tcp_keepalive = 1
    tcp_keepcnt = 3
    tcp_keepidle = 600
    tcp_keepintvl = 30
  
  userlist.txt: |
    "certrats_user" "md5placeholder"

---
# Nginx with Advanced Caching
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-performance-config
  namespace: certrats
  labels:
    app: certrats
    component: nginx-performance
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    worker_rlimit_nofile 65535;
    
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;
    
    events {
        worker_connections 4096;
        use epoll;
        multi_accept on;
    }
    
    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;
        
        # Logging
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for" '
                        'rt=$request_time uct="$upstream_connect_time" '
                        'uht="$upstream_header_time" urt="$upstream_response_time"';
        
        access_log /var/log/nginx/access.log main buffer=16k flush=2m;
        
        # Performance settings
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        keepalive_requests 1000;
        types_hash_max_size 2048;
        server_tokens off;
        
        # Buffer settings
        client_body_buffer_size 128k;
        client_max_body_size 10m;
        client_header_buffer_size 1k;
        large_client_header_buffers 4 4k;
        output_buffers 1 32k;
        postpone_output 1460;
        
        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;
        
        # Brotli compression (if available)
        # brotli on;
        # brotli_comp_level 6;
        # brotli_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
        
        # Caching
        proxy_cache_path /var/cache/nginx/api levels=1:2 keys_zone=api_cache:10m max_size=1g inactive=60m use_temp_path=off;
        proxy_cache_path /var/cache/nginx/static levels=1:2 keys_zone=static_cache:10m max_size=2g inactive=24h use_temp_path=off;
        
        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
        limit_req_zone $binary_remote_addr zone=career_path:10m rate=2r/s;
        
        # Upstream servers
        upstream backend {
            least_conn;
            server backend-service:8000 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }
        
        upstream frontend {
            least_conn;
            server frontend-service:3000 max_fails=3 fail_timeout=30s;
            keepalive 32;
        }
        
        # Main server block
        server {
            listen 80;
            server_name _;
            
            # Security headers
            add_header X-Frame-Options "SAMEORIGIN" always;
            add_header X-Content-Type-Options "nosniff" always;
            add_header X-XSS-Protection "1; mode=block" always;
            add_header Referrer-Policy "strict-origin-when-cross-origin" always;
            add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
            
            # Health check endpoint
            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }
            
            # API routes with caching
            location /api/v1/certifications {
                limit_req zone=api burst=20 nodelay;
                
                proxy_cache api_cache;
                proxy_cache_valid 200 302 10m;
                proxy_cache_valid 404 1m;
                proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
                proxy_cache_lock on;
                proxy_cache_lock_timeout 5s;
                
                proxy_pass http://backend;
                proxy_http_version 1.1;
                proxy_set_header Connection "";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # Cache headers
                add_header X-Cache-Status $upstream_cache_status;
            }
            
            # Career path API with special rate limiting
            location /api/v1/career-path {
                limit_req zone=career_path burst=5 nodelay;
                
                proxy_pass http://backend;
                proxy_http_version 1.1;
                proxy_set_header Connection "";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # Longer timeout for AI processing
                proxy_connect_timeout 60s;
                proxy_send_timeout 60s;
                proxy_read_timeout 60s;
            }
            
            # Other API routes
            location /api/ {
                limit_req zone=api burst=20 nodelay;
                
                proxy_pass http://backend;
                proxy_http_version 1.1;
                proxy_set_header Connection "";
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # Static files with aggressive caching
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)$ {
                proxy_cache static_cache;
                proxy_cache_valid 200 302 24h;
                proxy_cache_valid 404 1m;
                proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
                
                expires 1y;
                add_header Cache-Control "public, immutable";
                add_header X-Cache-Status $upstream_cache_status;
                
                proxy_pass http://frontend;
                proxy_http_version 1.1;
                proxy_set_header Connection "";
            }
            
            # Frontend routes
            location / {
                proxy_pass http://frontend;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_cache_bypass $http_upgrade;
            }
        }
    }
