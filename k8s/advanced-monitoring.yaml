# Advanced Monitoring and Observability
# Real User Monitoring, Synthetic Monitoring, and Advanced Metrics

# Jaeger for Distributed Tracing
apiVersion: apps/v1
kind: Deployment
metadata:
  name: jaeger-all-in-one
  namespace: certrats-monitoring
  labels:
    app: jaeger
    component: tracing
spec:
  replicas: 1
  selector:
    matchLabels:
      app: jaeger
      component: tracing
  template:
    metadata:
      labels:
        app: jaeger
        component: tracing
    spec:
      containers:
      - name: jaeger
        image: jaegertracing/all-in-one:latest
        ports:
        - containerPort: 16686
          name: ui
        - containerPort: 14268
          name: collector
        - containerPort: 6831
          name: agent-compact
          protocol: UDP
        - containerPort: 6832
          name: agent-binary
          protocol: UDP
        env:
        - name: COLLECTOR_ZIPKIN_HTTP_PORT
          value: "9411"
        - name: SPAN_STORAGE_TYPE
          value: "memory"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /
            port: 16686
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 16686
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: jaeger-service
  namespace: certrats-monitoring
  labels:
    app: jaeger
    component: tracing
spec:
  type: ClusterIP
  ports:
  - port: 16686
    targetPort: 16686
    name: ui
  - port: 14268
    targetPort: 14268
    name: collector
  - port: 6831
    targetPort: 6831
    protocol: UDP
    name: agent-compact
  - port: 6832
    targetPort: 6832
    protocol: UDP
    name: agent-binary
  selector:
    app: jaeger
    component: tracing

---
# Synthetic Monitoring with Blackbox Exporter
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blackbox-exporter
  namespace: certrats-monitoring
  labels:
    app: blackbox-exporter
    component: synthetic-monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: blackbox-exporter
      component: synthetic-monitoring
  template:
    metadata:
      labels:
        app: blackbox-exporter
        component: synthetic-monitoring
    spec:
      containers:
      - name: blackbox-exporter
        image: prom/blackbox-exporter:latest
        ports:
        - containerPort: 9115
          name: http
        args:
        - --config.file=/etc/blackbox_exporter/config.yml
        volumeMounts:
        - name: blackbox-config
          mountPath: /etc/blackbox_exporter
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 9115
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 9115
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: blackbox-config
        configMap:
          name: blackbox-config

---
apiVersion: v1
kind: Service
metadata:
  name: blackbox-exporter-service
  namespace: certrats-monitoring
  labels:
    app: blackbox-exporter
    component: synthetic-monitoring
spec:
  type: ClusterIP
  ports:
  - port: 9115
    targetPort: 9115
    name: http
  selector:
    app: blackbox-exporter
    component: synthetic-monitoring

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: blackbox-config
  namespace: certrats-monitoring
  labels:
    app: blackbox-exporter
    component: synthetic-monitoring
data:
  config.yml: |
    modules:
      http_2xx:
        prober: http
        timeout: 5s
        http:
          valid_http_versions: ["HTTP/1.1", "HTTP/2.0"]
          valid_status_codes: []
          method: GET
          headers:
            Host: certrats.com
            Accept-Language: en-US
          no_follow_redirects: false
          fail_if_ssl: false
          fail_if_not_ssl: false
          tls_config:
            insecure_skip_verify: false
          preferred_ip_protocol: "ip4"
      
      http_post_2xx:
        prober: http
        timeout: 5s
        http:
          method: POST
          headers:
            Content-Type: application/json
          body: '{"test": true}'
      
      tcp_connect:
        prober: tcp
        timeout: 5s
      
      dns:
        prober: dns
        timeout: 5s
        dns:
          query_name: "certrats.com"
          query_type: "A"
      
      icmp:
        prober: icmp
        timeout: 5s

---
# Node Exporter for System Metrics
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: node-exporter
  namespace: certrats-monitoring
  labels:
    app: node-exporter
    component: system-monitoring
spec:
  selector:
    matchLabels:
      app: node-exporter
      component: system-monitoring
  template:
    metadata:
      labels:
        app: node-exporter
        component: system-monitoring
    spec:
      hostNetwork: true
      hostPID: true
      containers:
      - name: node-exporter
        image: prom/node-exporter:latest
        ports:
        - containerPort: 9100
          name: metrics
        args:
        - --path.procfs=/host/proc
        - --path.sysfs=/host/sys
        - --path.rootfs=/host/root
        - --collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)
        volumeMounts:
        - name: proc
          mountPath: /host/proc
          readOnly: true
        - name: sys
          mountPath: /host/sys
          readOnly: true
        - name: root
          mountPath: /host/root
          mountPropagation: HostToContainer
          readOnly: true
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 65534
      volumes:
      - name: proc
        hostPath:
          path: /proc
      - name: sys
        hostPath:
          path: /sys
      - name: root
        hostPath:
          path: /
      tolerations:
      - operator: Exists

---
apiVersion: v1
kind: Service
metadata:
  name: node-exporter-service
  namespace: certrats-monitoring
  labels:
    app: node-exporter
    component: system-monitoring
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 9100
    targetPort: 9100
    name: metrics
  selector:
    app: node-exporter
    component: system-monitoring

---
# Kube State Metrics
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kube-state-metrics
  namespace: certrats-monitoring
  labels:
    app: kube-state-metrics
    component: k8s-monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kube-state-metrics
      component: k8s-monitoring
  template:
    metadata:
      labels:
        app: kube-state-metrics
        component: k8s-monitoring
    spec:
      serviceAccountName: kube-state-metrics
      containers:
      - name: kube-state-metrics
        image: k8s.gcr.io/kube-state-metrics/kube-state-metrics:v2.10.0
        ports:
        - containerPort: 8080
          name: http-metrics
        - containerPort: 8081
          name: telemetry
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8080
          initialDelaySeconds: 5
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /
            port: 8081
          initialDelaySeconds: 5
          timeoutSeconds: 5
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kube-state-metrics
  namespace: certrats-monitoring
  labels:
    app: kube-state-metrics
    component: k8s-monitoring

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kube-state-metrics
  labels:
    app: kube-state-metrics
    component: k8s-monitoring
rules:
- apiGroups: [""]
  resources:
  - configmaps
  - secrets
  - nodes
  - pods
  - services
  - resourcequotas
  - replicationcontrollers
  - limitranges
  - persistentvolumeclaims
  - persistentvolumes
  - namespaces
  - endpoints
  verbs: ["list", "watch"]
- apiGroups: ["apps"]
  resources:
  - statefulsets
  - daemonsets
  - deployments
  - replicasets
  verbs: ["list", "watch"]
- apiGroups: ["batch"]
  resources:
  - cronjobs
  - jobs
  verbs: ["list", "watch"]
- apiGroups: ["autoscaling"]
  resources:
  - horizontalpodautoscalers
  verbs: ["list", "watch"]
- apiGroups: ["authentication.k8s.io"]
  resources:
  - tokenreviews
  verbs: ["create"]
- apiGroups: ["authorization.k8s.io"]
  resources:
  - subjectaccessreviews
  verbs: ["create"]
- apiGroups: ["policy"]
  resources:
  - poddisruptionbudgets
  verbs: ["list", "watch"]
- apiGroups: ["certificates.k8s.io"]
  resources:
  - certificatesigningrequests
  verbs: ["list", "watch"]
- apiGroups: ["storage.k8s.io"]
  resources:
  - storageclasses
  - volumeattachments
  verbs: ["list", "watch"]
- apiGroups: ["admissionregistration.k8s.io"]
  resources:
  - mutatingwebhookconfigurations
  - validatingwebhookconfigurations
  verbs: ["list", "watch"]
- apiGroups: ["networking.k8s.io"]
  resources:
  - networkpolicies
  - ingresses
  verbs: ["list", "watch"]
- apiGroups: ["coordination.k8s.io"]
  resources:
  - leases
  verbs: ["list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kube-state-metrics
  labels:
    app: kube-state-metrics
    component: k8s-monitoring
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kube-state-metrics
subjects:
- kind: ServiceAccount
  name: kube-state-metrics
  namespace: certrats-monitoring

---
apiVersion: v1
kind: Service
metadata:
  name: kube-state-metrics-service
  namespace: certrats-monitoring
  labels:
    app: kube-state-metrics
    component: k8s-monitoring
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 8080
    targetPort: 8080
    name: http-metrics
  - port: 8081
    targetPort: 8081
    name: telemetry
  selector:
    app: kube-state-metrics
    component: k8s-monitoring
