apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: certrats
  labels:
    app: certrats
    component: backend
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: certrats
      component: backend
  template:
    metadata:
      labels:
        app: certrats
        component: backend
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      imagePullSecrets:
      - name: ghcr-secret
      containers:
      - name: backend
        image: ghcr.io/your-org/certrats-backend:latest
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        env:
        # Configuration from ConfigMap
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: certrats-config
              key: ENVIRONMENT
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: certrats-config
              key: LOG_LEVEL
        - name: API_HOST
          valueFrom:
            configMapKeyRef:
              name: certrats-config
              key: API_HOST
        - name: API_PORT
          valueFrom:
            configMapKeyRef:
              name: certrats-config
              key: API_PORT
        - name: CORS_ORIGINS
          valueFrom:
            configMapKeyRef:
              name: certrats-config
              key: CORS_ORIGINS
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: certrats-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: certrats-config
              key: REDIS_PORT
        - name: REDIS_DB
          valueFrom:
            configMapKeyRef:
              name: certrats-config
              key: REDIS_DB
        
        # Secrets
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: certrats-secrets
              key: DATABASE_URL
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: certrats-secrets
              key: JWT_SECRET_KEY
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: certrats-secrets
              key: ANTHROPIC_API_KEY
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: certrats-secrets
              key: REDIS_PASSWORD
              optional: true
        
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        
        startupProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 30
        
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: tmp
          mountPath: /tmp
      
      volumes:
      - name: logs
        emptyDir: {}
      - name: tmp
        emptyDir: {}
      
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: component
                  operator: In
                  values:
                  - backend
              topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: certrats
  labels:
    app: certrats
    component: backend
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: certrats
    component: backend

---
# Horizontal Pod Autoscaler for backend
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-hpa
  namespace: certrats
  labels:
    app: certrats
    component: backend
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60

---
# Pod Disruption Budget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: backend-pdb
  namespace: certrats
  labels:
    app: certrats
    component: backend
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: certrats
      component: backend

---
# Network Policy for backend
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: backend-network-policy
  namespace: certrats
  labels:
    app: certrats
    component: backend
spec:
  podSelector:
    matchLabels:
      app: certrats
      component: backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: certrats
          component: nginx
    - podSelector:
        matchLabels:
          app: certrats
          component: frontend
    ports:
    - protocol: TCP
      port: 8000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
          component: database
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          app: redis
          component: cache
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 443  # HTTPS for external APIs
    - protocol: TCP
      port: 53   # DNS
    - protocol: UDP
      port: 53   # DNS
