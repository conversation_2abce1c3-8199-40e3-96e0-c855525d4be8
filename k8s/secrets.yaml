# Kubernetes Secrets Template
# NOTE: This file contains template secrets. In production, use proper secret management.
# Replace placeholder values with actual secrets using tools like:
# - <PERSON><PERSON><PERSON><PERSON> create secret
# - Helm with values files
# - External secret management (Vault, AWS Secrets Manager, etc.)

apiVersion: v1
kind: Secret
metadata:
  name: certrats-secrets
  namespace: certrats
  labels:
    app: certrats
    component: secrets
type: Opaque
stringData:
  # Database credentials
  DATABASE_URL: "*****************************************************************************/certrats"
  DATABASE_PASSWORD: "REPLACE_WITH_ACTUAL_PASSWORD"
  
  # JWT Secret (generate with: openssl rand -hex 32)
  JWT_SECRET_KEY: "REPLACE_WITH_ACTUAL_JWT_SECRET"
  
  # Anthropic API Key
  ANTHROPIC_API_KEY: "REPLACE_WITH_ACTUAL_ANTHROPIC_KEY"
  
  # Redis Password (if using authentication)
  REDIS_PASSWORD: "REPLACE_WITH_ACTUAL_REDIS_PASSWORD"
  
  # Email Configuration
  SMTP_USERNAME: "REPLACE_WITH_ACTUAL_SMTP_USERNAME"
  SMTP_PASSWORD: "REPLACE_WITH_ACTUAL_SMTP_PASSWORD"
  
  # AWS Credentials (if using S3)
  AWS_ACCESS_KEY_ID: "REPLACE_WITH_ACTUAL_AWS_ACCESS_KEY"
  AWS_SECRET_ACCESS_KEY: "REPLACE_WITH_ACTUAL_AWS_SECRET_KEY"

---
apiVersion: v1
kind: Secret
metadata:
  name: certrats-secrets-staging
  namespace: certrats-staging
  labels:
    app: certrats
    component: secrets
    environment: staging
type: Opaque
stringData:
  # Database credentials
  DATABASE_URL: "******************************************************************************/certrats_staging"
  DATABASE_PASSWORD: "REPLACE_WITH_STAGING_PASSWORD"
  
  # JWT Secret
  JWT_SECRET_KEY: "REPLACE_WITH_STAGING_JWT_SECRET"
  
  # Anthropic API Key (can use same as production or separate)
  ANTHROPIC_API_KEY: "REPLACE_WITH_STAGING_ANTHROPIC_KEY"
  
  # Redis Password
  REDIS_PASSWORD: "REPLACE_WITH_STAGING_REDIS_PASSWORD"
  
  # Email Configuration
  SMTP_USERNAME: "REPLACE_WITH_STAGING_SMTP_USERNAME"
  SMTP_PASSWORD: "REPLACE_WITH_STAGING_SMTP_PASSWORD"

---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secrets
  namespace: certrats
  labels:
    app: postgres
    component: database
type: Opaque
stringData:
  POSTGRES_DB: "certrats"
  POSTGRES_USER: "certrats_user"
  POSTGRES_PASSWORD: "REPLACE_WITH_ACTUAL_POSTGRES_PASSWORD"
  POSTGRES_REPLICATION_USER: "replicator"
  POSTGRES_REPLICATION_PASSWORD: "REPLACE_WITH_REPLICATION_PASSWORD"

---
# TLS Certificate Secret (for HTTPS)
# This should be created using cert-manager or manually with actual certificates
apiVersion: v1
kind: Secret
metadata:
  name: certrats-tls
  namespace: certrats
  labels:
    app: certrats
    component: tls
type: kubernetes.io/tls
data:
  # Base64 encoded certificate and key
  # Replace with actual certificate data
  tls.crt: LS0tLS1CRUdJTi... # REPLACE_WITH_ACTUAL_CERTIFICATE
  tls.key: LS0tLS1CRUdJTi... # REPLACE_WITH_ACTUAL_PRIVATE_KEY

---
# Docker registry secret for pulling private images
apiVersion: v1
kind: Secret
metadata:
  name: ghcr-secret
  namespace: certrats
  labels:
    app: certrats
    component: registry
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ********************************************************************************************************************************************************************************************************************************************************

---
# Monitoring secrets
apiVersion: v1
kind: Secret
metadata:
  name: monitoring-secrets
  namespace: certrats-monitoring
  labels:
    app: monitoring
    component: secrets
type: Opaque
stringData:
  # Grafana admin credentials
  GRAFANA_ADMIN_USER: "admin"
  GRAFANA_ADMIN_PASSWORD: "REPLACE_WITH_GRAFANA_PASSWORD"
  
  # Prometheus basic auth (if enabled)
  PROMETHEUS_USERNAME: "prometheus"
  PROMETHEUS_PASSWORD: "REPLACE_WITH_PROMETHEUS_PASSWORD"
  
  # AlertManager webhook URLs
  SLACK_WEBHOOK_URL: "REPLACE_WITH_SLACK_WEBHOOK"
  PAGERDUTY_INTEGRATION_KEY: "REPLACE_WITH_PAGERDUTY_KEY"
