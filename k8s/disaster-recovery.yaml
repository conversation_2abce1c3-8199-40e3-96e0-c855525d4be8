# Disaster Recovery and Backup Infrastructure
# Automated backups, cross-region replication, and recovery procedures

# Velero for Kubernetes Backup
apiVersion: v1
kind: Namespace
metadata:
  name: velero
  labels:
    name: velero
    component: backup

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: velero
  namespace: velero
  labels:
    component: backup

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: velero
  labels:
    component: backup
subjects:
- kind: ServiceAccount
  namespace: velero
  name: velero
roleRef:
  kind: ClusterRole
  name: cluster-admin
  apiGroup: rbac.authorization.k8s.io

---
# Backup Storage Location
apiVersion: velero.io/v1
kind: BackupStorageLocation
metadata:
  name: aws-s3
  namespace: velero
  labels:
    component: backup
spec:
  provider: aws
  objectStorage:
    bucket: certrats-velero-backups
    prefix: kubernetes-backups
  config:
    region: us-east-1
    s3ForcePathStyle: "false"

---
# Volume Snapshot Location
apiVersion: velero.io/v1
kind: VolumeSnapshotLocation
metadata:
  name: aws-ebs
  namespace: velero
  labels:
    component: backup
spec:
  provider: aws
  config:
    region: us-east-1

---
# Daily Backup Schedule
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: daily-backup
  namespace: velero
  labels:
    component: backup
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  template:
    includedNamespaces:
    - certrats
    - certrats-staging
    - certrats-monitoring
    excludedResources:
    - events
    - events.events.k8s.io
    storageLocation: aws-s3
    volumeSnapshotLocations:
    - aws-ebs
    ttl: 720h  # 30 days
    metadata:
      labels:
        backup-type: daily

---
# Weekly Full Backup Schedule
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: weekly-backup
  namespace: velero
  labels:
    component: backup
spec:
  schedule: "0 1 * * 0"  # Weekly on Sunday at 1 AM
  template:
    includedNamespaces:
    - certrats
    - certrats-staging
    - certrats-monitoring
    - kube-system
    - velero
    storageLocation: aws-s3
    volumeSnapshotLocations:
    - aws-ebs
    ttl: 2160h  # 90 days
    metadata:
      labels:
        backup-type: weekly

---
# Database Backup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgres-backup
  namespace: certrats
  labels:
    app: postgres-backup
    component: database-backup
spec:
  schedule: "0 3 * * *"  # Daily at 3 AM
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: postgres-backup
            component: database-backup
        spec:
          restartPolicy: OnFailure
          containers:
          - name: postgres-backup
            image: postgres:15-alpine
            command:
            - /bin/bash
            - -c
            - |
              set -e
              
              # Create backup filename with timestamp
              BACKUP_FILE="certrats_backup_$(date +%Y%m%d_%H%M%S).sql"
              
              echo "Starting database backup: $BACKUP_FILE"
              
              # Create database dump
              pg_dump "$DATABASE_URL" > "/backup/$BACKUP_FILE"
              
              # Compress backup
              gzip "/backup/$BACKUP_FILE"
              
              # Upload to S3
              aws s3 cp "/backup/${BACKUP_FILE}.gz" "s3://certrats-database-backups/postgres/${BACKUP_FILE}.gz"
              
              # Clean up local file
              rm "/backup/${BACKUP_FILE}.gz"
              
              # Clean up old backups (keep last 30 days)
              aws s3 ls "s3://certrats-database-backups/postgres/" | \
                awk '{print $4}' | \
                sort | \
                head -n -30 | \
                xargs -I {} aws s3 rm "s3://certrats-database-backups/postgres/{}"
              
              echo "Database backup completed successfully"
            env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: certrats-secrets
                  key: DATABASE_URL
            - name: AWS_DEFAULT_REGION
              value: "us-east-1"
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
            resources:
              requests:
                memory: "256Mi"
                cpu: "100m"
              limits:
                memory: "1Gi"
                cpu: "500m"
          volumes:
          - name: backup-storage
            emptyDir: {}

---
# Redis Backup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: redis-backup
  namespace: certrats
  labels:
    app: redis-backup
    component: cache-backup
spec:
  schedule: "0 4 * * *"  # Daily at 4 AM
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: redis-backup
            component: cache-backup
        spec:
          restartPolicy: OnFailure
          containers:
          - name: redis-backup
            image: redis:7-alpine
            command:
            - /bin/sh
            - -c
            - |
              set -e
              
              # Create backup filename with timestamp
              BACKUP_FILE="redis_backup_$(date +%Y%m%d_%H%M%S).rdb"
              
              echo "Starting Redis backup: $BACKUP_FILE"
              
              # Create Redis dump
              redis-cli -h redis-service -p 6379 --rdb "/backup/$BACKUP_FILE"
              
              # Compress backup
              gzip "/backup/$BACKUP_FILE"
              
              # Upload to S3
              aws s3 cp "/backup/${BACKUP_FILE}.gz" "s3://certrats-database-backups/redis/${BACKUP_FILE}.gz"
              
              # Clean up local file
              rm "/backup/${BACKUP_FILE}.gz"
              
              # Clean up old backups (keep last 7 days for Redis)
              aws s3 ls "s3://certrats-database-backups/redis/" | \
                awk '{print $4}' | \
                sort | \
                head -n -7 | \
                xargs -I {} aws s3 rm "s3://certrats-database-backups/redis/{}"
              
              echo "Redis backup completed successfully"
            env:
            - name: AWS_DEFAULT_REGION
              value: "us-east-1"
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
            resources:
              requests:
                memory: "128Mi"
                cpu: "50m"
              limits:
                memory: "512Mi"
                cpu: "200m"
          volumes:
          - name: backup-storage
            emptyDir: {}

---
# Disaster Recovery Runbook ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: disaster-recovery-runbook
  namespace: certrats
  labels:
    app: certrats
    component: disaster-recovery
data:
  runbook.md: |
    # CertRats Disaster Recovery Runbook
    
    ## Overview
    This runbook provides step-by-step procedures for disaster recovery scenarios.
    
    ## Backup Verification
    
    ### Check Backup Status
    ```bash
    # Check Velero backups
    velero backup get
    
    # Check database backups
    aws s3 ls s3://certrats-database-backups/postgres/
    
    # Check Redis backups
    aws s3 ls s3://certrats-database-backups/redis/
    ```
    
    ## Recovery Procedures
    
    ### 1. Complete Cluster Recovery
    
    #### Prerequisites
    - New Kubernetes cluster provisioned
    - Velero installed and configured
    - Access to backup storage
    
    #### Steps
    ```bash
    # 1. Install Velero in new cluster
    velero install --provider aws --bucket certrats-velero-backups
    
    # 2. Restore from latest backup
    velero restore create --from-backup daily-backup-YYYYMMDD-HHMMSS
    
    # 3. Verify restoration
    kubectl get pods -A
    kubectl get pvc -A
    
    # 4. Update DNS records to point to new cluster
    # 5. Verify application functionality
    ```
    
    ### 2. Database Recovery
    
    #### From PostgreSQL Backup
    ```bash
    # 1. Download latest backup
    aws s3 cp s3://certrats-database-backups/postgres/latest.sql.gz /tmp/
    
    # 2. Decompress
    gunzip /tmp/latest.sql.gz
    
    # 3. Restore to database
    psql $DATABASE_URL < /tmp/latest.sql
    
    # 4. Verify data integrity
    psql $DATABASE_URL -c "SELECT COUNT(*) FROM certification_explorer;"
    ```
    
    #### From Redis Backup
    ```bash
    # 1. Download latest backup
    aws s3 cp s3://certrats-database-backups/redis/latest.rdb.gz /tmp/
    
    # 2. Decompress
    gunzip /tmp/latest.rdb.gz
    
    # 3. Stop Redis service
    kubectl scale deployment redis --replicas=0
    
    # 4. Copy backup to Redis pod
    kubectl cp /tmp/latest.rdb redis-pod:/data/dump.rdb
    
    # 5. Start Redis service
    kubectl scale deployment redis --replicas=1
    ```
    
    ### 3. Application Recovery
    
    #### Rolling Back Deployment
    ```bash
    # 1. Check deployment history
    kubectl rollout history deployment/backend
    kubectl rollout history deployment/frontend
    
    # 2. Rollback to previous version
    kubectl rollout undo deployment/backend
    kubectl rollout undo deployment/frontend
    
    # 3. Verify rollback
    kubectl rollout status deployment/backend
    kubectl rollout status deployment/frontend
    ```
    
    #### Blue-Green Rollback
    ```bash
    # 1. Switch traffic back to blue environment
    kubectl patch service backend-service -p '{"spec":{"selector":{"color":"blue"}}}'
    kubectl patch service frontend-service -p '{"spec":{"selector":{"color":"blue"}}}'
    
    # 2. Verify traffic switch
    curl -H "Host: certrats.com" http://LOAD_BALANCER_IP/health
    
    # 3. Clean up green environment
    helm uninstall certrats-green
    ```
    
    ## Recovery Time Objectives (RTO)
    
    - **Application Recovery**: 15 minutes
    - **Database Recovery**: 30 minutes
    - **Complete Cluster Recovery**: 2 hours
    - **Cross-Region Failover**: 4 hours
    
    ## Recovery Point Objectives (RPO)
    
    - **Application Data**: 24 hours (daily backups)
    - **Database Data**: 1 hour (continuous replication)
    - **Configuration**: 24 hours (daily backups)
    
    ## Emergency Contacts
    
    - **On-Call Engineer**: +1-XXX-XXX-XXXX
    - **DevOps Team**: <EMAIL>
    - **AWS Support**: Enterprise Support Case
    
    ## Post-Recovery Checklist
    
    - [ ] Verify all services are running
    - [ ] Check application functionality
    - [ ] Validate data integrity
    - [ ] Update monitoring dashboards
    - [ ] Notify stakeholders
    - [ ] Document lessons learned
    - [ ] Update runbook if needed

---
# Backup Monitoring
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: backup-monitoring
  namespace: certrats-monitoring
  labels:
    app: backup-monitoring
    component: monitoring
spec:
  selector:
    matchLabels:
      app: velero
  endpoints:
  - port: monitoring
    interval: 30s
    path: /metrics

---
# Backup Alerts
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: backup-alerts
  namespace: certrats-monitoring
  labels:
    app: backup-alerts
    component: monitoring
spec:
  groups:
  - name: backup.rules
    rules:
    - alert: BackupFailed
      expr: increase(velero_backup_failure_total[1h]) > 0
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "Velero backup failed"
        description: "Velero backup has failed in the last hour"
    
    - alert: BackupMissing
      expr: time() - velero_backup_last_successful_timestamp > 86400
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "No recent backup found"
        description: "No successful backup in the last 24 hours"
    
    - alert: DatabaseBackupFailed
      expr: kube_job_status_failed{job_name=~"postgres-backup.*"} > 0
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "Database backup failed"
        description: "PostgreSQL backup job has failed"
