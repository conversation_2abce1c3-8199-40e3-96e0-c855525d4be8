apiVersion: v1
kind: ConfigMap
metadata:
  name: certrats-config
  namespace: certrats
  labels:
    app: certrats
    component: config
data:
  # Application Configuration
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  LOG_FORMAT: "json"
  
  # API Configuration
  API_HOST: "0.0.0.0"
  API_PORT: "8000"
  API_VERSION: "v1"
  
  # CORS Configuration
  CORS_ORIGINS: '["https://certrats.com", "https://www.certrats.com", "https://api.certrats.com"]'
  
  # Database Configuration
  DATABASE_HOST: "postgres-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "certrats"
  
  # Redis Configuration
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "0"
  
  # Health Check Configuration
  HEALTH_CHECK_TIMEOUT: "30"
  HEALTH_CHECK_INTERVAL: "60"
  
  # Rate Limiting
  RATE_LIMIT_PER_MINUTE: "60"
  RATE_LIMIT_BURST: "10"
  
  # Feature Flags
  ENABLE_AI_CAREER_PATH: "true"
  ENABLE_USER_REGISTRATION: "true"
  ENABLE_ADMIN_INTERFACE: "true"
  ENABLE_ANALYTICS: "true"
  
  # Frontend Configuration
  NEXT_PUBLIC_API_URL: "https://api.certrats.com"
  NEXT_PUBLIC_APP_NAME: "CertRats"
  NEXT_PUBLIC_APP_VERSION: "1.0.0"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: certrats-config-staging
  namespace: certrats-staging
  labels:
    app: certrats
    component: config
    environment: staging
data:
  # Application Configuration
  ENVIRONMENT: "staging"
  LOG_LEVEL: "DEBUG"
  LOG_FORMAT: "json"
  
  # API Configuration
  API_HOST: "0.0.0.0"
  API_PORT: "8000"
  API_VERSION: "v1"
  
  # CORS Configuration
  CORS_ORIGINS: '["https://staging.certrats.com", "https://staging-api.certrats.com"]'
  
  # Database Configuration
  DATABASE_HOST: "postgres-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "certrats_staging"
  
  # Redis Configuration
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "1"
  
  # Health Check Configuration
  HEALTH_CHECK_TIMEOUT: "30"
  HEALTH_CHECK_INTERVAL: "60"
  
  # Rate Limiting
  RATE_LIMIT_PER_MINUTE: "120"
  RATE_LIMIT_BURST: "20"
  
  # Feature Flags
  ENABLE_AI_CAREER_PATH: "true"
  ENABLE_USER_REGISTRATION: "true"
  ENABLE_ADMIN_INTERFACE: "true"
  ENABLE_ANALYTICS: "false"
  
  # Frontend Configuration
  NEXT_PUBLIC_API_URL: "https://staging-api.certrats.com"
  NEXT_PUBLIC_APP_NAME: "CertRats (Staging)"
  NEXT_PUBLIC_APP_VERSION: "staging"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: certrats
  labels:
    app: certrats
    component: nginx
data:
  nginx.conf: |
    events {
        worker_connections 1024;
    }
    
    http {
        include       /etc/nginx/mime.types;
        default_type  application/octet-stream;
        
        # Logging
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for" '
                        'rt=$request_time uct="$upstream_connect_time" '
                        'uht="$upstream_header_time" urt="$upstream_response_time"';
        
        access_log /var/log/nginx/access.log main;
        error_log /var/log/nginx/error.log warn;
        
        # Basic settings
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        client_max_body_size 10M;
        
        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;
        
        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
        
        # Upstream servers
        upstream backend {
            server backend-service:8000;
            keepalive 32;
        }
        
        upstream frontend {
            server frontend-service:3000;
            keepalive 32;
        }
        
        # Main server block
        server {
            listen 80;
            server_name _;
            
            # Security headers
            add_header X-Frame-Options "SAMEORIGIN" always;
            add_header X-Content-Type-Options "nosniff" always;
            add_header X-XSS-Protection "1; mode=block" always;
            add_header Referrer-Policy "strict-origin-when-cross-origin" always;
            add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;" always;
            
            # Health check endpoint
            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }
            
            # API routes
            location /api/ {
                limit_req zone=api burst=20 nodelay;
                
                proxy_pass http://backend;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_cache_bypass $http_upgrade;
                
                # Timeouts
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }
            
            # Static files caching
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
                proxy_pass http://frontend;
            }
            
            # Frontend routes
            location / {
                proxy_pass http://frontend;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_cache_bypass $http_upgrade;
                
                # Timeouts
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }
        }
    }
