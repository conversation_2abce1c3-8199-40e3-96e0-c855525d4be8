#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check for Python files that import Streamlit.
This helps identify files that need to be modified or removed during the migration.
"""

import os
import re
import sys

def find_streamlit_imports(directory='.'):
    """Find all Python files that import streamlit."""
    streamlit_files = []
    
    for root, dirs, files in os.walk(directory):
        # Skip hidden directories and virtual environments
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != 'venv' and d != 'streamlit_backup']
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # Check for streamlit imports
                        if re.search(r'import\s+streamlit|from\s+streamlit\s+import', content):
                            streamlit_files.append(file_path)
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    
    return streamlit_files

def main():
    directory = '.'
    if len(sys.argv) > 1:
        directory = sys.argv[1]
    
    streamlit_files = find_streamlit_imports(directory)
    
    if streamlit_files:
        print(f"Found {len(streamlit_files)} files with streamlit imports:")
        for file in streamlit_files:
            print(f"  - {file}")
    else:
        print("No files with streamlit imports found.")

if __name__ == "__main__":
    main() 