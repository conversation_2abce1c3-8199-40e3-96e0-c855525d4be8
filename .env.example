# Environment Configuration Template
# Copy this file to .env.local for development

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
ENVIRONMENT=development
NODE_ENV=development
DEBUG=true

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Development (SQLite)
DATABASE_URL=sqlite:///./certrats.db

# Production (PostgreSQL)
# DATABASE_URL=postgresql://username:password@localhost:5432/certrats_prod

# Test Database
# DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test

# =============================================================================
# API CONFIGURATION
# =============================================================================
API_HOST=0.0.0.0
API_PORT=8000
API_VERSION=v1

# CORS Settings
CORS_ORIGINS=["http://localhost:3000", "http://localhost:3001", "http://127.0.0.1:3000"]

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Anthropic AI API
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
# JWT Secret (generate with: openssl rand -hex 32)
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Password hashing
BCRYPT_ROUNDS=12

# =============================================================================
# REDIS CONFIGURATION (for caching and sessions)
# =============================================================================
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=logs/app.log

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
# Application Performance Monitoring
APM_SERVICE_NAME=certrats
APM_ENVIRONMENT=development

# Health Check Settings
HEALTH_CHECK_TIMEOUT=30
HEALTH_CHECK_INTERVAL=60

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME=CertRats
NEXT_PUBLIC_APP_VERSION=0.1.0

# =============================================================================
# EMAIL CONFIGURATION (for notifications)
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>

# =============================================================================
# CLOUD STORAGE (for file uploads)
# =============================================================================
# AWS S3
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=certrats-uploads

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_AI_CAREER_PATH=true
ENABLE_USER_REGISTRATION=true
ENABLE_ADMIN_INTERFACE=true
ENABLE_ANALYTICS=false

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
# Database seeding
SEED_DATABASE=true
SEED_SAMPLE_DATA=true

# Debug settings
ENABLE_SQL_LOGGING=true
ENABLE_REQUEST_LOGGING=true
