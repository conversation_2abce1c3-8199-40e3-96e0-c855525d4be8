# Migration Guide: Streamlit to React

This document outlines the process for migrating the CertRats application from Streamlit to React while maintaining FastAPI as the backend.

## Migration Strategy

We are following a progressive migration approach:

1. **API First**: Ensure all functionality has proper API endpoints
2. **Test Second**: Implement comprehensive tests for API endpoints
3. **UI Last**: Migrate UI components from Streamlit to React

## Docker Environment

All Docker-related files are located in the `.dockerwrapper` directory. The Docker setup supports both multi-container and single-container deployments.

For more information, see the [Docker README](.dockerwrapper/README.md).

## Migration Phases

### Phase 1: API Development & Enhancement

- Document all existing API endpoints
- Identify gaps in the API
- Implement missing endpoints
- Ensure proper validation and error handling
- Add comprehensive API documentation

### Phase 2: Test Suite Development

- Set up testing framework
- Implement unit tests for API endpoints
- Create integration tests for key workflows
- Set up CI/CD pipeline for automated testing

### Phase 3: React Foundation

- Set up React project structure
- Implement core components
- Create API client services
- Set up routing and state management
- Implement authentication

### Phase 4: Progressive Feature Migration

Features will be migrated one by one from Streamlit to React:

1. **Dashboard**: Initial view with basic statistics
2. **User Management**: User profile and settings
3. **Certification Explorer**: Main certification browsing interface
4. **Reports**: Data visualization and reporting
5. **Admin Interface**: Administrative functions

As each feature is migrated, the Nginx configuration will be updated to route traffic to the React implementation using the `.dockerwrapper/update-routes.sh` script.

### Phase 5: Streamlit Removal

Streamlit has been successfully removed from the application:

1. ✓ Nginx configuration updated to route all traffic to React
2. ✓ Streamlit dependencies removed
3. ✓ Streamlit-specific code cleaned up
4. ✓ Documentation updated

## Migration Tracking

| Feature | API Status | Test Status | UI Status | Migration Date |
|---------|------------|-------------|-----------|----------------|
| Dashboard | Complete | Complete | Complete | 2023-03-10 |
| User Management | Complete | Complete | Complete | 2023-03-10 |
| Certification Explorer | Complete | Complete | Complete | 2023-03-10 |
| Reports | Complete | Complete | Complete | 2023-03-10 |
| Admin Interface | Complete | Complete | Complete | 2023-03-10 |

## Development Workflow

1. **API Development**:
   - Implement API endpoints in FastAPI
   - Document using OpenAPI/Swagger
   - Test with Postman/curl

2. **Test Development**:
   - Write unit tests for API endpoints
   - Implement integration tests
   - Ensure high test coverage

3. **UI Development**:
   - Create React components
   - Connect to API endpoints
   - Implement proper error handling and loading states
   - Style components according to design system

4. **Feature Migration**:
   - Update Nginx routes to point to React implementation
   - Test thoroughly
   - Gather user feedback
   - Refine as needed

## Migration Helper Script

The `.dockerwrapper/migrate-feature.sh` script helps automate the migration process:

```bash
# Start API development for a feature
./.dockerwrapper/migrate-feature.sh --start-api dashboard

# Mark API development as complete
./.dockerwrapper/migrate-feature.sh --complete-api dashboard

# Start test development for a feature
./.dockerwrapper/migrate-feature.sh --start-test dashboard

# Mark test development as complete
./.dockerwrapper/migrate-feature.sh --complete-test dashboard

# Start UI development for a feature
./.dockerwrapper/migrate-feature.sh --start-ui dashboard

# Mark UI development as complete (also updates Nginx routes)
./.dockerwrapper/migrate-feature.sh --complete-ui dashboard

# Show migration status
./.dockerwrapper/migrate-feature.sh --status
```

The script will:
- Update the migration status in this document
- Create boilerplate files for API, tests, and UI components
- Update Nginx routes when a feature is fully migrated

## Getting Started

To start working on the migration:

1. Set up the Docker environment:
   ```bash
   ./.dockerwrapper/run.sh up -d
   ```

2. Access the different services:
   - Streamlit UI: http://localhost/
   - React UI: http://localhost/new/
   - FastAPI Swagger: http://localhost/api/docs

3. Start migrating features following the API-first, test-second, UI-last approach. 