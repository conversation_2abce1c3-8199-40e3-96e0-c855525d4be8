"""FastAPI application configuration"""
import os
import logging
from pathlib import Path
from typing import Dict, Any

# Configure logging with more detailed format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Settings:
    """Application settings"""
    API_V1_PREFIX: str = "/api/v1"
    PROJECT_NAME: str = "Security Certification Path Creator API"
    VERSION: str = "1.0.0"
    HOST: str = "0.0.0.0"  # Required for Replit
    PORT: int = 3001       # Using port 3001 to avoid conflicts
    CORS_ORIGINS: list = ["*"]  # In production, replace with specific origins
    DATABASE_URL: str = os.getenv("DATABASE_URL", "")  # Added default empty string

    @property
    def fastapi_kwargs(self) -> Dict[str, Any]:
        """FastAPI initialization arguments"""
        return {
            "title": self.PROJECT_NAME,
            "version": self.VERSION,
            "docs_url": "/docs",
            "openapi_url": "/openapi.json",
        }

settings = Settings()