"""
Authentication API endpoints
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OA<PERSON><PERSON>P<PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from typing import Dict, Optional
from models.user import User, User<PERSON><PERSON>, UserStatus
from database import get_db
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import bcrypt
import jwt
from jwt.exceptions import PyJWTError
from pydantic import BaseModel
from api.constants import AUTH
import os
import logging
from api.config import settings

# Configure logging
logger = logging.getLogger(__name__)

# Create router with prefix from constants
router = APIRouter(
    # Use empty prefix because the full prefix is included in the main router
    tags=["authentication"],
    responses={401: {"description": "Authentication failed"}},
)

# JWT settings - get from environment variables or use secure defaults
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secure-jwt-key")  # In production, this should be set in env variables
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

# OAuth2 scheme - use the right tokenUrl from constants
# Strip the API prefix as it's already included when the router is mounted
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=AUTH["LOGIN"].split("/")[-1])  # Just "login"

# Create token response model
class TokenResponse(BaseModel):
    """Authentication token response"""
    access_token: str
    token_type: str
    expires_in: int
    user_id: int
    username: str
    role: str

class UserMeResponse(BaseModel):
    """Current user information response"""
    id: int
    username: str
    email: str
    full_name: Optional[str] = None
    role: str
    status: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    
    class Config:
        """Pydantic model configuration"""
        orm_mode = True

# Helper functions
def get_password_hash(password: str) -> str:
    """Hash a password for storing using bcrypt"""
    salt = bcrypt.gensalt()
    hashed_password = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed_password.decode('utf-8')

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a stored password against a provided password using bcrypt"""
    try:
        return bcrypt.checkpw(plain_password.encode('utf-8'), hashed_password.encode('utf-8'))
    except Exception as e:
        logger.error(f"Password verification error: {str(e)}")
        return False

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT token with proper expiration"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    
    to_encode.update({"exp": expire})
    try:
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    except Exception as e:
        logger.error(f"Token creation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not create authentication token"
        )

async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    """Get the current user from the token with enhanced error handling"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            logger.warning("Token missing 'sub' claim")
            raise credentials_exception
    except PyJWTError as e:
        logger.warning(f"JWT validation error: {str(e)}")
        raise credentials_exception
    except Exception as e:
        logger.error(f"Unexpected token error: {str(e)}")
        raise credentials_exception
    
    # Get user from database
    try:
        user = db.query(User).filter(User.username == username).first()
        if user is None:
            logger.warning(f"User not found: {username}")
            raise credentials_exception
        return user
    except Exception as e:
        logger.error(f"Database error when retrieving user: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

async def get_current_active_user(current_user = Depends(get_current_user)):
    """Check if the current user is active"""
    if current_user.status != UserStatus.ACTIVE:
        logger.warning(f"Inactive user attempted access: {current_user.username}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Account is inactive"
        )
    return current_user

# Authentication endpoints - use paths relative to the router prefix
@router.post("/login", response_model=TokenResponse)
async def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    """
    Login endpoint to get JWT token
    
    This endpoint authenticates a user and returns a JWT token for use in subsequent requests.
    """
    logger.info(f"Login attempt for user: {form_data.username}")
    
    try:
        # Find user by username
        user = db.query(User).filter(User.username == form_data.username).first()
        if not user or not verify_password(form_data.password, user.password):
            logger.warning(f"Failed login attempt for user: {form_data.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Check if user is active
        if user.status != UserStatus.ACTIVE:
            logger.warning(f"Login attempt for inactive user: {form_data.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User account is not active",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Create access token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username, "role": user.role.value}, 
            expires_delta=access_token_expires
        )
        
        # Update last login time
        user.last_login = datetime.utcnow()
        db.commit()
        
        # Log user activity
        try:
            from models.user import UserActivity
            activity = UserActivity(
                user_id=user.id,
                action="login",
                details={"ip": "127.0.0.1"}  # In production, get real IP
            )
            db.add(activity)
            db.commit()
        except Exception as e:
            # If user activity logging fails, log error but continue
            logger.error(f"Failed to log user activity: {str(e)}")
        
        logger.info(f"Successful login for user: {form_data.username}")
        return TokenResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # Convert to seconds
            user_id=user.id,
            username=user.username,
            role=user.role.value
        )
    except HTTPException:
        # Re-raise HTTP exceptions to preserve status code and details
        raise
    except Exception as e:
        # Handle unexpected errors
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred during login"
        )

@router.post("/logout")
async def logout(current_user = Depends(get_current_active_user), db: Session = Depends(get_db)):
    """
    Logout endpoint
    
    In JWT authentication, tokens cannot be invalidated server-side, but we log the logout event.
    The client should discard the token.
    """
    logger.info(f"Logout for user: {current_user.username}")
    
    try:
        # Log the logout event
        from models.user import UserActivity
        activity = UserActivity(
            user_id=current_user.id,
            action="logout",
            details={"ip": "127.0.0.1"}  # In production, get real IP
        )
        db.add(activity)
        db.commit()
        
        return {"message": "Successfully logged out"}
    except Exception as e:
        logger.error(f"Logout activity logging error: {str(e)}")
        # Still return success since the client will discard the token anyway
        return {"message": "Successfully logged out"}

@router.get("/me", response_model=UserMeResponse)
async def get_me(current_user = Depends(get_current_active_user)):
    """
    Get current authenticated user information
    
    Returns the profile of the currently authenticated user.
    """
    logger.info(f"Profile request for user: {current_user.username}")
    
    try:
        # Return user data directly - the response model will handle the conversion
        return current_user
    except Exception as e:
        logger.error(f"Error retrieving user profile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving user profile"
        ) 