"""
API endpoints for reports feature
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status, BackgroundTasks
from typing import List, Optional, Dict, Any
from models.reports import (
    Report, ReportModel, ReportCreate, ReportUpdate, ReportListResponse,
    ReportFilterParams, ReportResult, ReportType, ReportFormat, ReportFrequency
)
from models.user import User
from database import get_db
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_
from datetime import datetime, timedelta
import json
import os
import uuid
from fastapi.responses import FileResponse
from api.endpoints.user_management import get_current_active_user, get_admin_user

router = APIRouter(
    prefix="/reports",
    tags=["reports"],
    responses={404: {"description": "Report not found"}},
)

# Helper functions for report generation
async def generate_report_background(
    report_id: int,
    db: Session,
    background_tasks: BackgroundTasks
):
    """
    Background task to generate a report
    """
    # Get the report
    report = db.query(Report).filter(Report.id == report_id).first()
    if not report:
        return
    
    # Update last run time
    report.last_run_at = datetime.utcnow()
    
    # Schedule next run if needed
    if report.is_scheduled and report.frequency:
        if report.frequency == "daily":
            report.next_run_date = datetime.utcnow() + timedelta(days=1)
        elif report.frequency == "weekly":
            report.next_run_date = datetime.utcnow() + timedelta(weeks=1)
        elif report.frequency == "monthly":
            report.next_run_date = datetime.utcnow() + timedelta(days=30)
        elif report.frequency == "quarterly":
            report.next_run_date = datetime.utcnow() + timedelta(days=90)
        elif report.frequency == "yearly":
            report.next_run_date = datetime.utcnow() + timedelta(days=365)
        elif report.frequency == "once":
            report.is_scheduled = False
            report.next_run_date = None
    
    db.commit()
    
    # Generate report based on type
    result_data = {}
    
    if report.type.value == "certification_progress":
        result_data = await generate_certification_progress_report(report, db)
    elif report.type.value == "user_activity":
        result_data = await generate_user_activity_report(report, db)
    elif report.type.value == "certification_popularity":
        result_data = await generate_certification_popularity_report(report, db)
    elif report.type.value == "skill_gaps":
        result_data = await generate_skill_gaps_report(report, db)
    elif report.type.value == "custom":
        result_data = await generate_custom_report(report, db)
    
    # Save report result
    file_path = None
    if report.format.value == "pdf":
        file_path = await save_report_as_pdf(report, result_data)
    elif report.format.value == "csv":
        file_path = await save_report_as_csv(report, result_data)
    elif report.format.value == "json":
        file_path = await save_report_as_json(report, result_data)
    elif report.format.value == "html":
        file_path = await save_report_as_html(report, result_data)
    
    # Store the file path in the report parameters
    if file_path:
        if not report.parameters:
            report.parameters = {}
        report.parameters["latest_file_path"] = file_path
        db.commit()

async def generate_certification_progress_report(report: Report, db: Session) -> Dict[str, Any]:
    """Generate certification progress report"""
    # This would contain actual logic to query certification progress data
    # For now, we'll return mock data
    return {
        "total_certifications": 150,
        "completed_certifications": 75,
        "in_progress_certifications": 45,
        "not_started_certifications": 30,
        "completion_rate": 0.5,
        "user_progress": [
            {"user_id": 1, "username": "user1", "completed": 5, "in_progress": 2},
            {"user_id": 2, "username": "user2", "completed": 3, "in_progress": 1},
        ]
    }

async def generate_user_activity_report(report: Report, db: Session) -> Dict[str, Any]:
    """Generate user activity report"""
    # This would contain actual logic to query user activity data
    return {
        "active_users": 120,
        "inactive_users": 30,
        "new_users_last_30_days": 25,
        "login_activity": [
            {"date": "2023-01-01", "count": 45},
            {"date": "2023-01-02", "count": 52},
            {"date": "2023-01-03", "count": 48}
        ],
        "most_active_users": [
            {"user_id": 1, "username": "user1", "login_count": 45},
            {"user_id": 2, "username": "user2", "login_count": 42}
        ]
    }

async def generate_certification_popularity_report(report: Report, db: Session) -> Dict[str, Any]:
    """Generate certification popularity report"""
    return {
        "most_popular_certifications": [
            {"id": 1, "name": "AWS Certified Solutions Architect", "count": 78},
            {"id": 2, "name": "CompTIA Security+", "count": 65},
            {"id": 3, "name": "Certified Ethical Hacker", "count": 52}
        ],
        "popularity_by_type": {
            "cloud": 145,
            "security": 120,
            "networking": 85,
            "development": 65,
            "devops": 55
        },
        "popularity_by_level": {
            "beginner": 95,
            "intermediate": 125,
            "advanced": 85,
            "expert": 45
        }
    }

async def generate_skill_gaps_report(report: Report, db: Session) -> Dict[str, Any]:
    """Generate skill gaps report"""
    return {
        "skill_gaps": [
            {"skill": "Cloud Security", "demand": 85, "supply": 45, "gap": 40},
            {"skill": "Penetration Testing", "demand": 75, "supply": 40, "gap": 35},
            {"skill": "Kubernetes", "demand": 70, "supply": 30, "gap": 40}
        ],
        "recommended_certifications": [
            {"id": 1, "name": "AWS Certified Security Specialty", "relevance": 0.95},
            {"id": 2, "name": "Certified Kubernetes Administrator", "relevance": 0.9},
            {"id": 3, "name": "OSCP", "relevance": 0.85}
        ]
    }

async def generate_custom_report(report: Report, db: Session) -> Dict[str, Any]:
    """Generate custom report based on parameters"""
    # Custom reports would use the parameters field to determine what to include
    if not report.parameters:
        return {"error": "No parameters specified for custom report"}
    
    result = {"custom_data": {}}
    
    # Example: if parameters include user_ids, include user data
    if "user_ids" in report.parameters:
        user_ids = report.parameters["user_ids"]
        users = db.query(User).filter(User.id.in_(user_ids)).all()
        result["custom_data"]["users"] = [
            {"id": user.id, "username": user.username, "email": user.email}
            for user in users
        ]
    
    # Example: if parameters include date range, filter by date
    if "start_date" in report.parameters and "end_date" in report.parameters:
        result["custom_data"]["date_range"] = {
            "start": report.parameters["start_date"],
            "end": report.parameters["end_date"]
        }
    
    return result

async def save_report_as_pdf(report: Report, data: Dict[str, Any]) -> str:
    """Save report data as PDF file"""
    # In a real implementation, this would use a PDF generation library
    # For now, we'll just create a mock file path
    file_name = f"report_{report.id}_{uuid.uuid4()}.pdf"
    file_path = os.path.join("reports", file_name)
    
    # Ensure directory exists
    os.makedirs("reports", exist_ok=True)
    
    # Write a placeholder file
    with open(file_path, "w") as f:
        f.write(f"PDF Report: {report.title}\n")
        f.write(f"Generated at: {datetime.utcnow().isoformat()}\n")
        f.write(f"Data: {json.dumps(data, indent=2)}")
    
    return file_path

async def save_report_as_csv(report: Report, data: Dict[str, Any]) -> str:
    """Save report data as CSV file"""
    file_name = f"report_{report.id}_{uuid.uuid4()}.csv"
    file_path = os.path.join("reports", file_name)
    
    # Ensure directory exists
    os.makedirs("reports", exist_ok=True)
    
    # Write a placeholder file
    with open(file_path, "w") as f:
        f.write(f"CSV Report: {report.title}\n")
        f.write(f"Generated at: {datetime.utcnow().isoformat()}\n")
        
        # In a real implementation, this would format the data as proper CSV
        for key, value in data.items():
            f.write(f"{key},{value}\n")
    
    return file_path

async def save_report_as_json(report: Report, data: Dict[str, Any]) -> str:
    """Save report data as JSON file"""
    file_name = f"report_{report.id}_{uuid.uuid4()}.json"
    file_path = os.path.join("reports", file_name)
    
    # Ensure directory exists
    os.makedirs("reports", exist_ok=True)
    
    # Write JSON file
    with open(file_path, "w") as f:
        json.dump({
            "report_id": report.id,
            "title": report.title,
            "generated_at": datetime.utcnow().isoformat(),
            "data": data
        }, f, indent=2)
    
    return file_path

async def save_report_as_html(report: Report, data: Dict[str, Any]) -> str:
    """Save report data as HTML file"""
    file_name = f"report_{report.id}_{uuid.uuid4()}.html"
    file_path = os.path.join("reports", file_name)
    
    # Ensure directory exists
    os.makedirs("reports", exist_ok=True)
    
    # Write a placeholder HTML file
    with open(file_path, "w") as f:
        f.write(f"<html><head><title>{report.title}</title></head><body>")
        f.write(f"<h1>{report.title}</h1>")
        f.write(f"<p>Generated at: {datetime.utcnow().isoformat()}</p>")
        f.write("<pre>" + json.dumps(data, indent=2) + "</pre>")
        f.write("</body></html>")
    
    return file_path

@router.get("/", response_model=ReportListResponse)
async def get_reports(
    skip: int = Query(0, ge=0, description="Number of reports to skip"),
    limit: int = Query(10, ge=1, le=100, description="Number of reports to return"),
    type: Optional[ReportType] = Query(None, description="Filter by report type"),
    is_scheduled: Optional[bool] = Query(None, description="Filter by scheduled status"),
    frequency: Optional[ReportFrequency] = Query(None, description="Filter by frequency"),
    search: Optional[str] = Query(None, description="Search in title and description"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get all reports with filtering options
    """
    query = db.query(Report)
    
    # Regular users can only see their own reports
    if current_user.role.value != "admin":
        query = query.filter(Report.user_id == current_user.id)
    
    # Apply filters
    if type:
        query = query.filter(Report.type == type)
    if is_scheduled is not None:
        query = query.filter(Report.is_scheduled == is_scheduled)
    if frequency:
        query = query.filter(Report.frequency == frequency)
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                Report.title.ilike(search_term),
                Report.description.ilike(search_term)
            )
        )
    
    # Get total count for pagination
    total = query.count()
    
    # Apply pagination
    reports = query.offset(skip).limit(limit).all()
    
    return ReportListResponse(
        reports=reports,
        total=total,
        page=(skip // limit) + 1 if limit > 0 else 1,
        page_size=limit
    )

@router.get("/types", response_model=List[str])
async def get_report_types(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a list of all report types
    """
    return [t.value for t in ReportType]

@router.get("/formats", response_model=List[str])
async def get_report_formats(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a list of all report formats
    """
    return [f.value for f in ReportFormat]

@router.get("/frequencies", response_model=List[str])
async def get_report_frequencies(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a list of all report frequencies
    """
    return [f.value for f in ReportFrequency]

@router.get("/{report_id}", response_model=ReportModel)
async def get_report(
    report_id: int = Path(..., gt=0, description="The ID of the report to get"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific report by ID
    """
    report = db.query(Report).filter(Report.id == report_id).first()
    if report is None:
        raise HTTPException(status_code=404, detail="Report not found")
    
    # Check if user has access to this report
    if current_user.role.value != "admin" and report.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this report")
    
    return report

@router.post("/", response_model=ReportModel, status_code=status.HTTP_201_CREATED)
async def create_report(
    report: ReportCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Create a new report
    """
    # Create new report
    db_report = Report(
        title=report.title,
        description=report.description,
        type=report.type,
        parameters=report.parameters,
        format=report.format,
        is_scheduled=report.is_scheduled,
        frequency=report.frequency,
        next_run_date=report.next_run_date,
        user_id=current_user.id if report.user_id is None else report.user_id
    )
    
    # Only admins can create reports for other users
    if report.user_id is not None and report.user_id != current_user.id and current_user.role.value != "admin":
        raise HTTPException(status_code=403, detail="Not authorized to create reports for other users")
    
    db.add(db_report)
    db.commit()
    db.refresh(db_report)
    
    return db_report

@router.put("/{report_id}", response_model=ReportModel)
async def update_report(
    report_id: int = Path(..., gt=0, description="The ID of the report to update"),
    report: ReportUpdate = ...,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update a report
    """
    db_report = db.query(Report).filter(Report.id == report_id).first()
    if db_report is None:
        raise HTTPException(status_code=404, detail="Report not found")
    
    # Check if user has access to update this report
    if current_user.role.value != "admin" and db_report.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to update this report")
    
    # Update fields if provided
    update_data = report.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_report, key, value)
    
    db.commit()
    db.refresh(db_report)
    
    return db_report

@router.delete("/{report_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_report(
    report_id: int = Path(..., gt=0, description="The ID of the report to delete"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Delete a report
    """
    db_report = db.query(Report).filter(Report.id == report_id).first()
    if db_report is None:
        raise HTTPException(status_code=404, detail="Report not found")
    
    # Check if user has access to delete this report
    if current_user.role.value != "admin" and db_report.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this report")
    
    db.delete(db_report)
    db.commit()
    
    return None

@router.post("/{report_id}/generate", response_model=Dict[str, str])
async def generate_report(
    report_id: int = Path(..., gt=0, description="The ID of the report to generate"),
    background_tasks: BackgroundTasks = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Generate a report
    """
    db_report = db.query(Report).filter(Report.id == report_id).first()
    if db_report is None:
        raise HTTPException(status_code=404, detail="Report not found")
    
    # Check if user has access to generate this report
    if current_user.role.value != "admin" and db_report.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to generate this report")
    
    # Start background task to generate report
    if background_tasks:
        background_tasks.add_task(generate_report_background, report_id, db, background_tasks)
        return {"status": "Report generation started in background"}
    else:
        # For testing or when background tasks are not available
        await generate_report_background(report_id, db, None)
        return {"status": "Report generated"}

@router.get("/{report_id}/download")
async def download_report(
    report_id: int = Path(..., gt=0, description="The ID of the report to download"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Download a generated report
    """
    db_report = db.query(Report).filter(Report.id == report_id).first()
    if db_report is None:
        raise HTTPException(status_code=404, detail="Report not found")
    
    # Check if user has access to download this report
    if current_user.role.value != "admin" and db_report.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to download this report")
    
    # Check if report has been generated
    if not db_report.parameters or "latest_file_path" not in db_report.parameters:
        raise HTTPException(status_code=404, detail="Report has not been generated yet")
    
    file_path = db_report.parameters["latest_file_path"]
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Report file not found")
    
    return FileResponse(
        path=file_path,
        filename=os.path.basename(file_path),
        media_type="application/octet-stream"
    )

@router.get("/scheduled/run", response_model=Dict[str, Any])
async def run_scheduled_reports(
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """
    Run all scheduled reports that are due
    Admin only endpoint
    """
    now = datetime.utcnow()
    scheduled_reports = db.query(Report).filter(
        and_(
            Report.is_scheduled == True,
            Report.next_run_date <= now
        )
    ).all()
    
    for report in scheduled_reports:
        background_tasks.add_task(generate_report_background, report.id, db, background_tasks)
    
    return {
        "status": "Scheduled reports generation started",
        "count": len(scheduled_reports),
        "reports": [{"id": r.id, "title": r.title} for r in scheduled_reports]
    }
