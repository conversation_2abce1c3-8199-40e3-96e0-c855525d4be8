"""
Study API endpoints for certification study time estimation

This module provides endpoints for:
- Estimating study time required for certifications
- Tracking study progress
- Calculating return on investment (ROI)
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from pydantic import BaseModel, Field, validator
from models.user import User
from models.certification import Certification, UserCertification
from database import get_db
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from api.constants import STUDY
from api.endpoints.auth import get_current_active_user

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    tags=["study"],
    responses={401: {"description": "Not authenticated"}}
)

# Request and response models
class StudyEstimateRequest(BaseModel):
    """Request model for study time estimation"""
    certification_id: int
    hours_per_week: int = Field(..., gt=0, le=168, description="Available study hours per week")
    target_date: Optional[str] = Field(None, description="Optional target completion date (YYYY-MM-DD)")
    experience_level: Optional[int] = Field(None, ge=1, le=5, description="Experience level from 1-5")
    
    @validator('target_date')
    def validate_target_date(cls, v):
        """Validate that target date is in the future"""
        if v:
            try:
                date = datetime.strptime(v, "%Y-%m-%d").date()
                if date <= datetime.now().date():
                    raise ValueError("Target date must be in the future")
            except ValueError as e:
                raise ValueError(f"Invalid date format: {str(e)}")
        return v

class FocusArea(BaseModel):
    """Study focus area for a specific week"""
    topic: str
    hours_suggested: int
    resources: List[str]

class Milestone(BaseModel):
    """Study milestone"""
    description: str
    type: str  # "Learning", "Practice", "Exam"
    target_date: str

class WeeklySchedule(BaseModel):
    """Weekly study schedule"""
    week: int
    hours_planned: int
    focus_areas: List[FocusArea]
    milestones: List[Milestone] = []

class StudyEstimateResponse(BaseModel):
    """Response model for study time estimation"""
    status: str = "success"
    total_hours_required: int
    weeks_required: float
    weekly_schedule: List[WeeklySchedule]
    study_tips: List[str] = []
    target_completion_date: Optional[str] = None

# Additional request and response models for progress tracking
class ProgressUpdateRequest(BaseModel):
    """Request model for updating study progress"""
    certification_id: int
    progress: Optional[int] = Field(None, ge=0, le=100, description="Progress percentage (0-100)")
    custom_hours: Optional[int] = Field(None, ge=1, description="Custom study hours estimate")
    study_notes: Optional[str] = None

class ProgressEntry(BaseModel):
    """Study progress entry"""
    certification_id: int
    certification_name: str
    progress: int
    total_hours_required: int
    hours_completed: float
    last_updated: str
    target_completion_date: Optional[str] = None
    study_notes: Optional[str] = None

class ProgressResponse(BaseModel):
    """Response model for study progress"""
    status: str = "success"
    progress_entries: List[ProgressEntry]

# Additional models for ROI calculations
class ROIResponse(BaseModel):
    """Response model for ROI calculations"""
    status: str = "success"
    percent_roi: float
    break_even_months: int
    five_year_value: float
    salary_potential: Dict[str, float] = {}
    investment_details: Dict[str, float] = {}

# Study endpoints
@router.post("/estimate", response_model=StudyEstimateResponse)
async def estimate_study_time(
    request: StudyEstimateRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Estimate study time required for a certification.
    
    This endpoint calculates:
    - Total study hours required
    - Estimated weeks to complete
    - Weekly study schedule
    - Study tips and recommendations
    """
    logger.info(f"Study estimate request for certification ID: {request.certification_id} by user ID: {current_user.id}")
    
    try:
        # Get certification details
        certification = db.query(Certification).filter(Certification.id == request.certification_id).first()
        
        if not certification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Certification with ID {request.certification_id} not found"
            )
        
        # Calculate total study hours based on difficulty level
        difficulty_hours = {
            1: 60,   # Entry level (~60 hours total)
            2: 120,  # Intermediate (~120 hours total)
            3: 240,  # Advanced (~240 hours total)
            4: 400   # Expert (~400 hours total)
        }
        
        # Use custom hours if defined, otherwise use default based on difficulty
        total_hours = certification.custom_hours or difficulty_hours.get(certification.difficulty, 120)
        
        # Adjust hours based on user experience if provided
        if request.experience_level:
            # Reduce hours for experienced users
            experience_factor = max(0.6, 1 - (request.experience_level - 1) * 0.1)  # 1=1.0, 2=0.9, 3=0.8, 4=0.7, 5=0.6
            total_hours = int(total_hours * experience_factor)
        
        # Calculate weeks required
        weeks_required = round(total_hours / request.hours_per_week, 1)
        
        # Calculate target completion date
        target_completion_date = None
        if request.target_date:
            target_date = datetime.strptime(request.target_date, "%Y-%m-%d").date()
            target_completion_date = request.target_date
        else:
            target_date = datetime.now().date() + timedelta(weeks=weeks_required)
            target_completion_date = target_date.strftime("%Y-%m-%d")
        
        # Generate weekly schedule
        weekly_schedule = []
        
        # Define main topics for the certification based on name/domain
        main_topics = generate_topics_for_certification(certification)
        
        # Create schedule for each week
        for week in range(1, int(weeks_required) + 1):
            # For the last week, adjust hours for partial weeks
            hours_this_week = request.hours_per_week
            if week == int(weeks_required) and weeks_required % 1 > 0:
                hours_this_week = int(request.hours_per_week * (weeks_required % 1))
            
            # Get topics for this week based on progression
            week_topics = get_topics_for_week(main_topics, week, int(weeks_required))
            
            # Create focus areas for this week
            focus_areas = []
            for topic in week_topics:
                # Allocate hours proportionally
                topic_hours = int(hours_this_week * (topic["weight"] / 100))
                if topic_hours < 1:  # Ensure at least 1 hour
                    topic_hours = 1
                
                focus_areas.append(FocusArea(
                    topic=topic["name"],
                    hours_suggested=topic_hours,
                    resources=topic["resources"]
                ))
            
            # Add milestones based on week
            milestones = []
            if week == 1:
                # First week milestone
                milestone_date = (datetime.now().date() + timedelta(weeks=1)).strftime("%Y-%m-%d")
                milestones.append(Milestone(
                    description=f"Complete introduction to {certification.name}",
                    type="Learning",
                    target_date=milestone_date
                ))
            elif week == int(weeks_required) // 2:
                # Mid-point milestone
                milestone_date = (datetime.now().date() + timedelta(weeks=week)).strftime("%Y-%m-%d")
                milestones.append(Milestone(
                    description="Complete practice exams",
                    type="Practice",
                    target_date=milestone_date
                ))
            elif week == int(weeks_required):
                # Final milestone
                milestones.append(Milestone(
                    description="Take certification exam",
                    type="Exam",
                    target_date=target_completion_date
                ))
            
            weekly_schedule.append(WeeklySchedule(
                week=week,
                hours_planned=hours_this_week,
                focus_areas=focus_areas,
                milestones=milestones
            ))
        
        # Generate study tips based on certification
        study_tips = generate_study_tips_for_certification(certification)
        
        # Log user activity
        try:
            from models.user import UserActivity
            activity = UserActivity(
                user_id=current_user.id,
                action="study_estimate",
                details={
                    "certification_id": certification.id,
                    "certification_name": certification.name,
                    "total_hours": total_hours,
                    "weeks_required": weeks_required
                }
            )
            db.add(activity)
            db.commit()
        except Exception as e:
            logger.warning(f"Failed to log study estimate activity: {str(e)}")
            # Continue even if logging fails
        
        # Return response
        return StudyEstimateResponse(
            status="success",
            total_hours_required=total_hours,
            weeks_required=weeks_required,
            weekly_schedule=weekly_schedule,
            study_tips=study_tips,
            target_completion_date=target_completion_date
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error estimating study time: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while estimating study time"
        )

# Helper functions
def generate_topics_for_certification(certification: Certification) -> List[Dict[str, Any]]:
    """Generate relevant topics based on certification details"""
    # Default topics
    topics = [
        {
            "name": f"Introduction to {certification.name}",
            "weight": 15,
            "resources": ["Official Documentation", "Online Videos"],
            "week_range": [1, 1]  # First week
        },
        {
            "name": f"Core Concepts",
            "weight": 25,
            "resources": ["Course Chapter 1-3", "Study Guide"],
            "week_range": [1, 3]  # First 3 weeks
        },
        {
            "name": f"Advanced Topics",
            "weight": 30,
            "resources": ["Course Chapter 4-6", "Practice Labs"],
            "week_range": [3, 7]  # Middle weeks
        },
        {
            "name": f"Practical Applications",
            "weight": 20,
            "resources": ["Hands-on Labs", "Practice Tests"],
            "week_range": [5, 9]  # Later weeks
        },
        {
            "name": f"Exam Preparation",
            "weight": 10,
            "resources": ["Practice Exams", "Review Materials"],
            "week_range": [8, 10]  # Final weeks
        }
    ]
    
    # Adjust based on certification type/domain
    if "security" in certification.name.lower():
        topics.append({
            "name": "Security Fundamentals",
            "weight": 15,
            "resources": ["Security+ Study Guide", "Online Security Labs"],
            "week_range": [2, 4]
        })
    
    if "network" in certification.name.lower():
        topics.append({
            "name": "Network Protocols & Architecture",
            "weight": 15,
            "resources": ["Network+ Study Guide", "Protocol Analyzer Tools"],
            "week_range": [2, 5]
        })
    
    if "cloud" in certification.name.lower():
        topics.append({
            "name": "Cloud Services & Deployment Models",
            "weight": 15,
            "resources": ["Cloud Provider Documentation", "Cloud Labs"],
            "week_range": [3, 6]
        })
    
    return topics

def get_topics_for_week(topics: List[Dict[str, Any]], current_week: int, total_weeks: int) -> List[Dict[str, Any]]:
    """Filter topics relevant for the current week"""
    # Scale the week ranges to the total weeks
    scaled_topics = []
    for topic in topics:
        # Scale the week range based on total weeks
        if total_weeks <= 4:
            # For short courses, compress all topics
            scaled_range = [1, total_weeks]
        else:
            scale_factor = total_weeks / 10  # Assuming original range is for a 10-week course
            start_week = max(1, int(topic["week_range"][0] * scale_factor))
            end_week = min(total_weeks, int(topic["week_range"][1] * scale_factor))
            scaled_range = [start_week, end_week]
        
        # Create a copy with scaled range
        scaled_topic = topic.copy()
        scaled_topic["week_range"] = scaled_range
        scaled_topics.append(scaled_topic)
    
    # Filter topics for current week
    week_topics = [t for t in scaled_topics if t["week_range"][0] <= current_week <= t["week_range"][1]]
    
    # If no topics found, add a default topic
    if not week_topics:
        week_topics = [{
            "name": f"Week {current_week} Study Materials",
            "weight": 100,
            "resources": ["Course Materials", "Practice Questions"]
        }]
    
    # Adjust weights to sum up to 100%
    total_weight = sum(t["weight"] for t in week_topics)
    if total_weight > 0:
        for topic in week_topics:
            topic["weight"] = (topic["weight"] / total_weight) * 100
    
    return week_topics

def generate_study_tips_for_certification(certification: Certification) -> List[str]:
    """Generate study tips based on certification details"""
    # General tips
    tips = [
        "Create a consistent study schedule and stick to it",
        "Take regular breaks using the Pomodoro technique (25 minutes study, 5 minutes break)",
        "Join study groups or forums to connect with others preparing for the same certification",
        "Use varied learning materials including videos, books, and hands-on labs"
    ]
    
    # Add specific tips based on certification difficulty
    if certification.difficulty == 1:  # Entry level
        tips.extend([
            "Focus on understanding fundamental concepts",
            "Use flashcards for key terms and definitions",
            "Take plenty of practice quizzes to reinforce learning"
        ])
    elif certification.difficulty == 2:  # Intermediate
        tips.extend([
            "Supplement official study materials with real-world examples",
            "Create mind maps to connect related concepts",
            "Practice explaining complex topics to reinforce understanding"
        ])
    elif certification.difficulty == 3:  # Advanced
        tips.extend([
            "Deep dive into advanced topics with hands-on labs",
            "Create a personal lab environment for practical experience",
            "Focus on performance-based questions and scenarios"
        ])
    elif certification.difficulty == 4:  # Expert
        tips.extend([
            "Develop comprehensive case studies to apply concepts",
            "Focus on troubleshooting and problem-solving scenarios",
            "Connect with experienced professionals for mentorship"
        ])
    
    # Add domain-specific tips
    if "security" in certification.name.lower():
        tips.append("Practice identifying security vulnerabilities in sample scenarios")
    if "network" in certification.name.lower():
        tips.append("Set up a virtual network to practice configuration and troubleshooting")
    if "cloud" in certification.name.lower():
        tips.append("Create a free tier account with cloud providers for hands-on practice")
    
    return tips

# Study progress endpoints
@router.get("/progress", response_model=ProgressResponse)
async def get_study_progress(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get study progress for the current user's certifications.
    
    This endpoint returns:
    - List of certifications with progress information
    - Hours completed and remaining
    - Target completion dates
    """
    logger.info(f"Study progress request for user ID: {current_user.id}")
    
    try:
        # Get user certifications with progress
        progress_entries = []
        
        # Join UserCertification with Certification to get details
        user_certs = (
            db.query(UserCertification, Certification)
            .join(Certification, UserCertification.certification_id == Certification.id)
            .filter(UserCertification.user_id == current_user.id)
            .all()
        )
        
        # Process each certification
        for user_cert, cert in user_certs:
            # Calculate total hours based on difficulty or custom hours
            difficulty_hours = {
                1: 60,   # Entry level
                2: 120,  # Intermediate
                3: 240,  # Advanced
                4: 400   # Expert
            }
            
            # Determine total hours (custom or difficulty-based)
            total_hours = cert.custom_hours or difficulty_hours.get(cert.difficulty, 120)
            
            # Calculate hours completed based on progress
            hours_completed = (user_cert.progress / 100) * total_hours
            
            # Format dates
            last_updated = user_cert.updated_at.strftime("%Y-%m-%d")
            target_date = None
            if user_cert.target_date:
                target_date = user_cert.target_date.strftime("%Y-%m-%d")
            
            # Create progress entry
            progress_entries.append(ProgressEntry(
                certification_id=cert.id,
                certification_name=cert.name,
                progress=user_cert.progress,
                total_hours_required=total_hours,
                hours_completed=round(hours_completed, 1),
                last_updated=last_updated,
                target_completion_date=target_date,
                study_notes=user_cert.study_notes
            ))
            
        return ProgressResponse(
            status="success",
            progress_entries=progress_entries
        )
        
    except Exception as e:
        logger.error(f"Error retrieving study progress: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving study progress"
        )

@router.post("/progress", response_model=dict)
async def update_study_progress(
    request: ProgressUpdateRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update study progress for a certification.
    
    This endpoint allows:
    - Updating progress percentage
    - Setting custom study hours
    - Adding study notes
    """
    logger.info(f"Study progress update request for certification ID: {request.certification_id} by user ID: {current_user.id}")
    
    try:
        # Verify the certification exists
        certification = db.query(Certification).filter(Certification.id == request.certification_id).first()
        if not certification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Certification with ID {request.certification_id} not found"
            )
        
        # Check if user already has a progress entry for this certification
        user_cert = (
            db.query(UserCertification)
            .filter(
                UserCertification.user_id == current_user.id,
                UserCertification.certification_id == request.certification_id
            )
            .first()
        )
        
        # If no entry exists, create one
        if not user_cert:
            user_cert = UserCertification(
                user_id=current_user.id,
                certification_id=request.certification_id,
                progress=0,
                study_notes=""
            )
            db.add(user_cert)
        
        # Update fields if provided
        if request.progress is not None:
            user_cert.progress = request.progress
        
        if request.custom_hours is not None:
            # Update custom hours on the certification itself
            certification.custom_hours = request.custom_hours
        
        if request.study_notes is not None:
            user_cert.study_notes = request.study_notes
        
        # Update timestamps
        user_cert.updated_at = datetime.utcnow()
        
        # Save changes
        db.commit()
        
        # Log user activity
        try:
            from models.user import UserActivity
            activity_details = {
                "certification_id": certification.id,
                "certification_name": certification.name
            }
            
            if request.progress is not None:
                activity_details["progress"] = request.progress
            
            if request.custom_hours is not None:
                activity_details["custom_hours"] = request.custom_hours
            
            activity = UserActivity(
                user_id=current_user.id,
                action="study_progress_update",
                details=activity_details
            )
            db.add(activity)
            db.commit()
        except Exception as e:
            logger.warning(f"Failed to log study progress update activity: {str(e)}")
            # Continue even if logging fails
        
        return {
            "status": "success",
            "message": "Study progress updated successfully"
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error updating study progress: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating study progress"
        )

@router.get("/roi/{certification_id}", response_model=ROIResponse)
async def calculate_roi(
    certification_id: int = Path(..., description="ID of the certification"),
    current_salary: float = Query(75000, description="Current annual salary"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Calculate return on investment (ROI) for a certification.
    
    This endpoint calculates:
    - Percentage ROI
    - Break-even time in months
    - 5-year financial value
    - Salary potential
    """
    logger.info(f"ROI calculation request for certification ID: {certification_id} by user ID: {current_user.id}")
    
    try:
        # Get certification details
        certification = db.query(Certification).filter(Certification.id == certification_id).first()
        
        if not certification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Certification with ID {certification_id} not found"
            )
        
        # Get certification costs
        exam_fee = certification.cost or 300  # Default to $300 if not specified
        materials_cost = 200  # Estimated cost for study materials
        training_cost = 1000  # Estimated cost for formal training (optional)
        
        # Get time investment
        difficulty_hours = {
            1: 60,   # Entry level
            2: 120,  # Intermediate
            3: 240,  # Advanced
            4: 400   # Expert
        }
        
        # Calculate total study hours
        study_hours = certification.custom_hours or difficulty_hours.get(certification.difficulty, 120)
        
        # Assume average hourly wage based on current salary
        hourly_wage = current_salary / 2080  # 40 hours/week, 52 weeks/year
        
        # Calculate opportunity cost of study time
        opportunity_cost = study_hours * hourly_wage
        
        # Calculate total investment
        total_investment = exam_fee + materials_cost + opportunity_cost
        total_with_training = total_investment + training_cost
        
        # Estimate salary increase based on certification level
        salary_increase_factors = {
            1: 0.05,  # Entry level: 5% increase
            2: 0.08,  # Intermediate: 8% increase
            3: 0.12,  # Advanced: 12% increase
            4: 0.18   # Expert: 18% increase
        }
        
        # Get appropriate factor or default to 8%
        salary_increase_factor = salary_increase_factors.get(certification.difficulty, 0.08)
        
        # Calculate new salary
        new_salary = current_salary * (1 + salary_increase_factor)
        annual_benefit = new_salary - current_salary
        
        # Calculate ROI percentage (over 3 years, typical certification validity)
        three_year_benefit = annual_benefit * 3
        percent_roi = (three_year_benefit / total_investment - 1) * 100
        
        # Calculate months to break even
        if annual_benefit > 0:
            break_even_months = round((total_investment / annual_benefit) * 12)
        else:
            break_even_months = float('inf')  # No break-even if no salary increase
        
        # Calculate 5-year value
        five_year_value = (annual_benefit * 5) - total_investment
        
        # Log user activity
        try:
            from models.user import UserActivity
            activity = UserActivity(
                user_id=current_user.id,
                action="roi_calculation",
                details={
                    "certification_id": certification.id,
                    "certification_name": certification.name,
                    "current_salary": current_salary,
                    "percent_roi": round(percent_roi, 2),
                    "break_even_months": break_even_months
                }
            )
            db.add(activity)
            db.commit()
        except Exception as e:
            logger.warning(f"Failed to log ROI calculation activity: {str(e)}")
            # Continue even if logging fails
        
        # Return response
        return ROIResponse(
            status="success",
            percent_roi=round(percent_roi, 2),
            break_even_months=break_even_months,
            five_year_value=round(five_year_value, 2),
            salary_potential={
                "current_salary": current_salary,
                "estimated_new_salary": round(new_salary, 2),
                "annual_increase": round(annual_benefit, 2),
                "percent_increase": round(salary_increase_factor * 100, 2)
            },
            investment_details={
                "exam_fee": exam_fee,
                "materials_cost": materials_cost,
                "training_cost": training_cost,
                "opportunity_cost": round(opportunity_cost, 2),
                "total_investment": round(total_investment, 2),
                "total_with_training": round(total_with_training, 2)
            }
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error calculating ROI: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while calculating ROI"
        ) 