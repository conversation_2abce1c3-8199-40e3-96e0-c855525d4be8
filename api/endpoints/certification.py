"""
API endpoints for certification feature using the Certification model
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from typing import List, Optional
from models.certification import (
    Certification, Organization, Domain
)
from pydantic import BaseModel
from database import get_db
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_
import logging
from datetime import datetime
from typing import List, Optional, Any, Dict
from enum import Enum

logger = logging.getLogger(__name__)

# Define Pydantic models for request/response
class CertificationLevel(str, Enum):
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"

class CertificationType(str, Enum):
    VENDOR_NEUTRAL = "vendor-neutral"
    VENDOR_SPECIFIC = "vendor-specific"

class CertificationModel(BaseModel):
    id: int
    name: str
    organization_id: Optional[int] = None
    organization_name: Optional[str] = None
    category: Optional[str] = None
    domain: Optional[str] = None
    level: Optional[str] = None
    difficulty: Optional[float] = None
    cost: Optional[float] = None
    exam_code: Optional[str] = None
    description: Optional[str] = None
    url: Optional[str] = None
    
    class Config:
        orm_mode = True

class CertificationCreate(BaseModel):
    name: str
    organization_name: str
    category: Optional[str] = None
    domain: Optional[str] = None
    level: Optional[str] = None
    difficulty: Optional[float] = None
    cost: Optional[float] = None
    exam_code: Optional[str] = None
    description: Optional[str] = None
    url: Optional[str] = None

class CertificationUpdate(BaseModel):
    name: Optional[str] = None
    organization_name: Optional[str] = None
    category: Optional[str] = None
    domain: Optional[str] = None
    level: Optional[str] = None
    difficulty: Optional[float] = None
    cost: Optional[float] = None
    exam_code: Optional[str] = None
    description: Optional[str] = None
    url: Optional[str] = None

class CertificationListResponse(BaseModel):
    certifications: List[CertificationModel]
    total: int
    page: int
    page_size: int

router = APIRouter(
    tags=["certifications"],
    responses={404: {"description": "Certification not found"}},
)

@router.get("/", response_model=CertificationListResponse)
async def get_certifications(
    skip: int = Query(0, ge=0, description="Number of certifications to skip"),
    limit: int = Query(10, ge=1, le=100, description="Number of certifications to return"),
    organization: Optional[str] = Query(None, description="Filter by certification organization"),
    level: Optional[str] = Query(None, description="Filter by certification level"),
    domain: Optional[str] = Query(None, description="Filter by security domain"),
    search: Optional[str] = Query(None, description="Search in name and description"),
    min_cost: Optional[float] = Query(None, ge=0, description="Minimum cost"),
    max_cost: Optional[float] = Query(None, ge=0, description="Maximum cost"),
    db: Session = Depends(get_db)
):
    """
    Get all certifications with filtering options
    """
    logger.info(f"Fetching certifications with filters: skip={skip}, limit={limit}, organization={organization}, level={level}, domain={domain}, search={search}, min_cost={min_cost}, max_cost={max_cost}")
    try:
        query = db.query(Certification).filter(Certification.deleted_at == None)
        
        # Apply filters
        if organization:
            org = db.query(Organization).filter(Organization.name == organization).first()
            if org:
                query = query.filter(Certification.organization_id == org.id)
                logger.info(f"Applied organization filter: {organization}")
        if level:
            query = query.filter(Certification.level == level)
            logger.info(f"Applied level filter: {level}")
        if domain:
            # Join with domains table
            domain_obj = db.query(Domain).filter(Domain.name == domain).first()
            if domain_obj:
                query = query.filter(Certification.domains.any(id=domain_obj.id))
                logger.info(f"Applied domain filter: {domain}")
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    Certification.name.ilike(search_term),
                    Certification.description.ilike(search_term)
                )
            )
            logger.info(f"Applied search filter: {search}")
        if min_cost is not None:
            query = query.filter(Certification.cost >= min_cost)
            logger.info(f"Applied min_cost filter: {min_cost}")
        if max_cost is not None:
            query = query.filter(Certification.cost <= max_cost)
            logger.info(f"Applied max_cost filter: {max_cost}")
        
        # Get total count for pagination
        total = query.count()
        logger.info(f"Total certifications found matching filters: {total}")
        
        # Apply pagination
        certifications = query.offset(skip).limit(limit).all()
        logger.info(f"Returning {len(certifications)} certifications for page {skip // limit + 1} (size {limit})")
        
        # Transform to response model
        certification_models = []
        for cert in certifications:
            org_name = cert.organization.name if cert.organization else None
            certification_models.append(
                CertificationModel(
                    id=cert.id,
                    name=cert.name,
                    organization_id=cert.organization_id,
                    organization_name=org_name,
                    category=cert.category,
                    domain=cert.domain,
                    level=cert.level,
                    difficulty=cert.difficulty,
                    cost=cert.cost,
                    exam_code=cert.exam_code,
                    description=cert.description,
                    url=cert.url
                )
            )
            
        response = CertificationListResponse(
            certifications=certification_models,
            total=total,
            page=(skip // limit) + 1 if limit > 0 else 1,
            page_size=limit
        )
        logger.info("Successfully created response object")
        return response
    except Exception as e:
        logger.error(f"Error fetching certifications: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching certifications."
        )

@router.get("/organizations", response_model=List[str])
async def get_certification_organizations(db: Session = Depends(get_db)):
    """
    Get a list of all certification organizations
    """
    organizations = db.query(Organization.name).distinct().all()
    return [org[0] for org in organizations]

@router.get("/{certification_id}", response_model=CertificationModel)
async def get_certification(
    certification_id: int = Path(..., gt=0, description="The ID of the certification to get"),
    db: Session = Depends(get_db)
):
    """
    Get a specific certification by ID
    """
    certification = db.query(Certification).filter(
        Certification.id == certification_id,
        Certification.deleted_at == None
    ).first()
    
    if certification is None:
        raise HTTPException(status_code=404, detail="Certification not found")
    
    org_name = certification.organization.name if certification.organization else None
    
    return CertificationModel(
        id=certification.id,
        name=certification.name,
        organization_id=certification.organization_id,
        organization_name=org_name,
        category=certification.category,
        domain=certification.domain,
        level=certification.level,
        difficulty=certification.difficulty,
        cost=certification.cost,
        exam_code=certification.exam_code,
        description=certification.description,
        url=certification.url
    )

@router.post("/", response_model=CertificationModel, status_code=status.HTTP_201_CREATED)
async def create_certification(
    certification: CertificationCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new certification
    """
    # Check if certification with same name already exists
    existing = db.query(Certification).filter(
        and_(
            Certification.name == certification.name,
            Certification.deleted_at == None
        )
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=400,
            detail=f"Certification '{certification.name}' already exists"
        )
    
    # Get or create organization
    organization = None
    if certification.organization_name:
        organization = db.query(Organization).filter(Organization.name == certification.organization_name).first()
        if not organization:
            organization = Organization(name=certification.organization_name)
            db.add(organization)
            db.flush()  # Get ID without committing
    
    # Create new certification
    db_certification = Certification(
        name=certification.name,
        organization_id=organization.id if organization else None,
        category=certification.category,
        domain=certification.domain,
        level=certification.level,
        difficulty=certification.difficulty,
        cost=certification.cost,
        exam_code=certification.exam_code,
        description=certification.description,
        url=certification.url
    )
    
    db.add(db_certification)
    db.commit()
    db.refresh(db_certification)
    
    return CertificationModel(
        id=db_certification.id,
        name=db_certification.name,
        organization_id=db_certification.organization_id,
        organization_name=organization.name if organization else None,
        category=db_certification.category,
        domain=db_certification.domain,
        level=db_certification.level,
        difficulty=db_certification.difficulty,
        cost=db_certification.cost,
        exam_code=db_certification.exam_code,
        description=db_certification.description,
        url=db_certification.url
    )

@router.put("/{certification_id}", response_model=CertificationModel)
async def update_certification(
    certification_id: int = Path(..., gt=0, description="The ID of the certification to update"),
    certification: CertificationUpdate = ...,
    db: Session = Depends(get_db)
):
    """
    Update a certification
    """
    db_certification = db.query(Certification).filter(
        Certification.id == certification_id,
        Certification.deleted_at == None
    ).first()
    
    if db_certification is None:
        raise HTTPException(status_code=404, detail="Certification not found")
    
    # Handle organization update if provided
    if certification.organization_name is not None:
        organization = db.query(Organization).filter(Organization.name == certification.organization_name).first()
        if not organization:
            organization = Organization(name=certification.organization_name)
            db.add(organization)
            db.flush()
        db_certification.organization_id = organization.id
    
    # Update other fields if provided
    update_data = certification.dict(exclude_unset=True, exclude={"organization_name"})
    for key, value in update_data.items():
        setattr(db_certification, key, value)
    
    db.commit()
    db.refresh(db_certification)
    
    org_name = db_certification.organization.name if db_certification.organization else None
    
    return CertificationModel(
        id=db_certification.id,
        name=db_certification.name,
        organization_id=db_certification.organization_id,
        organization_name=org_name,
        category=db_certification.category,
        domain=db_certification.domain,
        level=db_certification.level,
        difficulty=db_certification.difficulty,
        cost=db_certification.cost,
        exam_code=db_certification.exam_code,
        description=db_certification.description,
        url=db_certification.url
    )

@router.delete("/{certification_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_certification(
    certification_id: int = Path(..., gt=0, description="The ID of the certification to delete"),
    db: Session = Depends(get_db)
):
    """
    Soft delete a certification
    """
    db_certification = db.query(Certification).filter(
        Certification.id == certification_id,
        Certification.deleted_at == None
    ).first()
    
    if db_certification is None:
        raise HTTPException(status_code=404, detail="Certification not found")
    
    # Soft delete
    db_certification.deleted_at = datetime.utcnow()
    db.commit()
    
    return None

@router.get("/related/{certification_id}", response_model=List[CertificationModel])
async def get_related_certifications(
    certification_id: int = Path(..., gt=0, description="The ID of the certification to find related ones for"),
    limit: int = Query(5, ge=1, le=20, description="Number of related certifications to return"),
    db: Session = Depends(get_db)
):
    """
    Get related certifications based on organization, category, and level
    """
    # Get the target certification
    certification = db.query(Certification).filter(
        Certification.id == certification_id,
        Certification.deleted_at == None
    ).first()
    
    if certification is None:
        raise HTTPException(status_code=404, detail="Certification not found")
    
    # Find related certifications
    related_query = db.query(Certification).filter(
        and_(
            Certification.id != certification_id,
            Certification.deleted_at == None,
            or_(
                Certification.organization_id == certification.organization_id,
                Certification.category == certification.category,
                Certification.level == certification.level
            )
        )
    ).limit(limit)
    
    related = related_query.all()
    
    # Transform to response models
    related_models = []
    for cert in related:
        org_name = cert.organization.name if cert.organization else None
        related_models.append(
            CertificationModel(
                id=cert.id,
                name=cert.name,
                organization_id=cert.organization_id,
                organization_name=org_name,
                category=cert.category,
                domain=cert.domain,
                level=cert.level,
                difficulty=cert.difficulty,
                cost=cert.cost,
                exam_code=cert.exam_code,
                description=cert.description,
                url=cert.url
            )
        )
    
    return related_models 