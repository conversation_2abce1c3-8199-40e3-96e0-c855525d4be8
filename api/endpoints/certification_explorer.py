"""
API endpoints for certification explorer feature
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from typing import List, Optional
from models.certification_explorer import (
    CertificationExplorer, CertificationModel, CertificationCreate, 
    CertificationUpdate, CertificationFilterParams, CertificationListResponse,
    CertificationType, CertificationLevel
)
from database import get_db
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_
import logging

logger = logging.getLogger(__name__)

router = APIRouter(
    tags=["certifications"],
    responses={404: {"description": "Certification not found"}},
)

@router.get("/", response_model=CertificationListResponse)
async def get_certifications(
    skip: int = Query(0, ge=0, description="Number of certifications to skip"),
    limit: int = Query(10, ge=1, le=100, description="Number of certifications to return"),
    provider: Optional[str] = Query(None, description="Filter by certification provider"),
    type: Optional[CertificationType] = Query(None, description="Filter by certification type"),
    level: Optional[CertificationLevel] = Query(None, description="Filter by certification level"),
    domain: Optional[str] = Query(None, description="Filter by security domain"),
    search: Optional[str] = Query(None, description="Search in name and description"),
    min_price: Optional[float] = Query(None, ge=0, description="Minimum price"),
    max_price: Optional[float] = Query(None, ge=0, description="Maximum price"),
    db: Session = Depends(get_db)
):
    """
    Get all certifications with filtering options
    """
    logger.info(f"Fetching certifications with filters: skip={skip}, limit={limit}, provider={provider}, type={type}, level={level}, domain={domain}, search={search}, min_price={min_price}, max_price={max_price}")
    try:
        query = db.query(CertificationExplorer)
        
        # Apply filters
        if provider:
            query = query.filter(CertificationExplorer.provider == provider)
            logger.info(f"Applied provider filter: {provider}")
        if type:
            query = query.filter(CertificationExplorer.type == type)
            logger.info(f"Applied type filter: {type}")
        if level:
            query = query.filter(CertificationExplorer.level == level)
            logger.info(f"Applied level filter: {level}")
        if domain:
            query = query.filter(CertificationExplorer.domain == domain)
            logger.info(f"Applied domain filter: {domain}")
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    CertificationExplorer.name.ilike(search_term),
                    CertificationExplorer.description.ilike(search_term)
                )
            )
            logger.info(f"Applied search filter: {search}")
        if min_price is not None:
            query = query.filter(CertificationExplorer.price >= min_price)
            logger.info(f"Applied min_price filter: {min_price}")
        if max_price is not None:
            query = query.filter(CertificationExplorer.price <= max_price)
            logger.info(f"Applied max_price filter: {max_price}")
        
        # Get total count for pagination
        total = query.count()
        logger.info(f"Total certifications found matching filters: {total}")
        
        # Apply pagination
        certifications = query.offset(skip).limit(limit).all()
        logger.info(f"Returning {len(certifications)} certifications for page {skip // limit + 1} (size {limit})")
        
        # Log the first certification if available
        if certifications:
            logger.debug(f"First certification details: {certifications[0].__dict__}")
            
        response = CertificationListResponse(
            certifications=certifications,
            total=total,
            page=(skip // limit) + 1 if limit > 0 else 1,
            page_size=limit
        )
        logger.info("Successfully created response object")
        return response
    except Exception as e:
        logger.error(f"Error fetching certifications: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching certifications."
        )

@router.get("/providers", response_model=List[str])
async def get_certification_providers(db: Session = Depends(get_db)):
    """
    Get a list of all certification providers
    """
    providers = db.query(CertificationExplorer.provider).distinct().all()
    return [provider[0] for provider in providers]

@router.get("/{certification_id}", response_model=CertificationModel)
async def get_certification(
    certification_id: int = Path(..., gt=0, description="The ID of the certification to get"),
    db: Session = Depends(get_db)
):
    """
    Get a specific certification by ID
    """
    certification = db.query(CertificationExplorer).filter(CertificationExplorer.id == certification_id).first()
    if certification is None:
        raise HTTPException(status_code=404, detail="Certification not found")
    return certification

@router.post("/", response_model=CertificationModel, status_code=status.HTTP_201_CREATED)
async def create_certification(
    certification: CertificationCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new certification
    """
    # Check if certification with same name and provider already exists
    existing = db.query(CertificationExplorer).filter(
        and_(
            CertificationExplorer.name == certification.name,
            CertificationExplorer.provider == certification.provider
        )
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=400,
            detail=f"Certification '{certification.name}' by {certification.provider} already exists"
        )
    
    # Create new certification
    db_certification = CertificationExplorer(
        name=certification.name,
        provider=certification.provider,
        description=certification.description,
        type=certification.type,
        level=certification.level,
        exam_code=certification.exam_code,
        price=certification.price,
        duration_minutes=certification.duration_minutes,
        passing_score=certification.passing_score,
        skills_covered=certification.skills_covered,
        url=certification.url
    )
    
    db.add(db_certification)
    db.commit()
    db.refresh(db_certification)
    
    return db_certification

@router.put("/{certification_id}", response_model=CertificationModel)
async def update_certification(
    certification_id: int = Path(..., gt=0, description="The ID of the certification to update"),
    certification: CertificationUpdate = ...,
    db: Session = Depends(get_db)
):
    """
    Update a certification
    """
    db_certification = db.query(CertificationExplorer).filter(CertificationExplorer.id == certification_id).first()
    if db_certification is None:
        raise HTTPException(status_code=404, detail="Certification not found")
    
    # Update fields if provided
    update_data = certification.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_certification, key, value)
    
    db.commit()
    db.refresh(db_certification)
    
    return db_certification

@router.delete("/{certification_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_certification(
    certification_id: int = Path(..., gt=0, description="The ID of the certification to delete"),
    db: Session = Depends(get_db)
):
    """
    Delete a certification
    """
    db_certification = db.query(CertificationExplorer).filter(CertificationExplorer.id == certification_id).first()
    if db_certification is None:
        raise HTTPException(status_code=404, detail="Certification not found")
    
    db.delete(db_certification)
    db.commit()
    
    return None

@router.get("/related/{certification_id}", response_model=List[CertificationModel])
async def get_related_certifications(
    certification_id: int = Path(..., gt=0, description="The ID of the certification to find related ones for"),
    limit: int = Query(5, ge=1, le=20, description="Number of related certifications to return"),
    db: Session = Depends(get_db)
):
    """
    Get related certifications based on type, provider, and level
    """
    # Get the target certification
    certification = db.query(CertificationExplorer).filter(CertificationExplorer.id == certification_id).first()
    if certification is None:
        raise HTTPException(status_code=404, detail="Certification not found")
    
    # Find related certifications
    related = db.query(CertificationExplorer).filter(
        and_(
            CertificationExplorer.id != certification_id,
            or_(
                CertificationExplorer.provider == certification.provider,
                CertificationExplorer.type == certification.type,
                CertificationExplorer.level == certification.level
            )
        )
    ).limit(limit).all()
    
    return related
