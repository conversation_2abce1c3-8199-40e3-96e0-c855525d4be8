"""
API endpoints for dashboard feature
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional, Dict
from models.dashboard import (
    DashboardData, 
    DashboardStats, 
    CertificationSummary, 
    OrganizationStat, 
    DomainStat,
    DashboardFilterParams
)
from database import get_db
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, select
from models.certification import Certification, Organization, Domain, certification_domains

router = APIRouter(
    prefix="/dashboard",
    tags=["dashboard"],
    responses={404: {"description": "Dashboard not found"}},
)

@router.get("/", response_model=DashboardData)
async def get_dashboard_data(
    organization_id: Optional[int] = None,
    domain: Optional[str] = None,
    level: Optional[str] = None,
    cost_min: Optional[float] = None,
    cost_max: Optional[float] = None,
    db: Session = Depends(get_db)
):
    """
    Get dashboard data with optional filters
    """
    try:
        # Base query for certifications with filters
        query = db.query(Certification).filter(Certification.is_deleted == False)
        
        if organization_id:
            query = query.filter(Certification.organization_id == organization_id)
        
        if domain:
            query = query.join(certification_domains).join(Domain).filter(Domain.name == domain)
        
        if level:
            query = query.filter(Certification.level == level)
        
        if cost_min is not None:
            query = query.filter(Certification.cost >= cost_min)
        
        if cost_max is not None:
            query = query.filter(Certification.cost <= cost_max)
        
        # Get statistics
        total_certifications = query.count()
        total_organizations = db.query(Organization).filter(Organization.is_deleted == False).count()
        total_domains = db.query(Domain).filter(Domain.is_deleted == False).count()
        
        # Get certifications by level
        level_counts = db.query(
            Certification.level, 
            func.count(Certification.id)
        ).filter(
            Certification.is_deleted == False
        ).group_by(
            Certification.level
        ).all()
        
        certifications_by_level = {level: count for level, count in level_counts}
        
        # Calculate average cost
        avg_cost_result = db.query(
            func.avg(Certification.cost)
        ).filter(
            Certification.is_deleted == False,
            Certification.cost.isnot(None)
        ).scalar()
        
        average_cost = float(avg_cost_result) if avg_cost_result else None
        
        # Create stats object
        stats = DashboardStats(
            total_certifications=total_certifications,
            total_organizations=total_organizations,
            total_domains=total_domains,
            certifications_by_level=certifications_by_level,
            average_cost=average_cost
        )
        
        # Get recent certifications
        recent_certifications = []
        recent_certs_query = db.query(Certification).filter(
            Certification.is_deleted == False
        ).order_by(
            desc(Certification.created_at)
        ).limit(5).all()
        
        for cert in recent_certs_query:
            org = db.query(Organization).filter(Organization.id == cert.organization_id).first()
            domains = db.query(Domain).join(certification_domains).filter(
                certification_domains.c.certification_id == cert.id
            ).all()
            
            domain_name = domains[0].name if domains else "General"
            
            recent_certifications.append(
                CertificationSummary(
                    id=cert.id,
                    name=cert.name,
                    organization=org.name if org else "Unknown",
                    domain=domain_name,
                    level=cert.level,
                    cost=cert.cost,
                    popularity=cert.popularity if hasattr(cert, 'popularity') else None
                )
            )
        
        # Get popular certifications
        popular_certifications = []
        popular_certs_query = db.query(Certification).filter(
            Certification.is_deleted == False
        ).order_by(
            desc(Certification.popularity) if hasattr(Certification, 'popularity') else desc(Certification.id)
        ).limit(5).all()
        
        for cert in popular_certs_query:
            org = db.query(Organization).filter(Organization.id == cert.organization_id).first()
            domains = db.query(Domain).join(certification_domains).filter(
                certification_domains.c.certification_id == cert.id
            ).all()
            
            domain_name = domains[0].name if domains else "General"
            
            popular_certifications.append(
                CertificationSummary(
                    id=cert.id,
                    name=cert.name,
                    organization=org.name if org else "Unknown",
                    domain=domain_name,
                    level=cert.level,
                    cost=cert.cost,
                    popularity=cert.popularity if hasattr(cert, 'popularity') else None
                )
            )
        
        # Get organization stats
        organizations = []
        org_stats = db.query(
            Organization.name,
            func.count(Certification.id).label('cert_count')
        ).join(
            Certification, Organization.id == Certification.organization_id
        ).filter(
            Organization.is_deleted == False,
            Certification.is_deleted == False
        ).group_by(
            Organization.name
        ).order_by(
            desc('cert_count')
        ).limit(10).all()
        
        for org_name, cert_count in org_stats:
            organizations.append(
                OrganizationStat(
                    name=org_name,
                    certification_count=cert_count,
                    logo_url=None  # Add logo URL if available
                )
            )
        
        # Get domain stats
        domains = []
        domain_stats = db.query(
            Domain.name,
            func.count(certification_domains.c.certification_id).label('cert_count')
        ).join(
            certification_domains, Domain.id == certification_domains.c.domain_id
        ).filter(
            Domain.is_deleted == False
        ).group_by(
            Domain.name
        ).order_by(
            desc('cert_count')
        ).limit(10).all()
        
        for domain_name, cert_count in domain_stats:
            domains.append(
                DomainStat(
                    name=domain_name,
                    certification_count=cert_count,
                    description=None  # Add description if available
                )
            )
        
        # Create and return dashboard data
        return DashboardData(
            stats=stats,
            recent_certifications=recent_certifications,
            popular_certifications=popular_certifications,
            organizations=organizations,
            domains=domains
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving dashboard data: {str(e)}"
        )

@router.get("/stats", response_model=DashboardStats)
async def get_dashboard_stats(
    db: Session = Depends(get_db)
):
    """
    Get dashboard statistics only
    """
    try:
        # Get statistics
        total_certifications = db.query(Certification).filter(Certification.is_deleted == False).count()
        total_organizations = db.query(Organization).filter(Organization.is_deleted == False).count()
        total_domains = db.query(Domain).filter(Domain.is_deleted == False).count()
        
        # Get certifications by level
        level_counts = db.query(
            Certification.level, 
            func.count(Certification.id)
        ).filter(
            Certification.is_deleted == False
        ).group_by(
            Certification.level
        ).all()
        
        certifications_by_level = {level: count for level, count in level_counts}
        
        # Calculate average cost
        avg_cost_result = db.query(
            func.avg(Certification.cost)
        ).filter(
            Certification.is_deleted == False,
            Certification.cost.isnot(None)
        ).scalar()
        
        average_cost = float(avg_cost_result) if avg_cost_result else None
        
        # Create and return stats object
        return DashboardStats(
            total_certifications=total_certifications,
            total_organizations=total_organizations,
            total_domains=total_domains,
            certifications_by_level=certifications_by_level,
            average_cost=average_cost
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving dashboard statistics: {str(e)}"
        )
