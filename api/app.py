"""FastAPI application for certification path creator functionality"""
import sys
from pathlib import Path
import logging
from fastapi import FastAPI, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
from pydantic import BaseModel, Field
from datetime import datetime
from sqlalchemy import text
import traceback

# Add parent directory to path if needed
parent_dir = str(Path(__file__).parent.parent)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from api.config import settings, logger
from api.constants import API_VERSION, API_PREFIX, HEALTH
from database import init_db, get_db
from api.routes import misc_router
from api.endpoints.dashboard import router as dashboard_router
from api.endpoints.user_management import router as user_router
from api.endpoints.certification_explorer import router as certification_explorer_router
from api.endpoints.certification import router as certification_router
from api.endpoints.reports import router as reports_router
from api.endpoints.admin_interface import router as admin_router
from api.endpoints.auth import router as auth_router
from api.endpoints.study import router as study_router
from utils.claude_assistant import init_anthropic_client

# Define models first
class HealthResponse(BaseModel):
    """Health check response model"""
    status: str
    database: str
    anthropic_client: str
    api_version: str = API_VERSION
    timestamp: datetime = Field(default_factory=datetime.utcnow)

# Create FastAPI app with settings
app = FastAPI(**settings.fastapi_kwargs)

# Add CORS middleware with settings
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory=Path(__file__).parent / "static"), name="static")

# Custom Swagger UI with dark mode
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=f"{app.title} - Swagger UI",
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
        swagger_ui_parameters=app.swagger_ui_parameters,
        extra_css=["static/css/swagger-ui-dark.css"],
    )

@app.get("/docs/oauth2-redirect", include_in_schema=False)
async def oauth2_redirect():
    from fastapi.openapi.docs import get_swagger_ui_oauth2_redirect_html
    return get_swagger_ui_oauth2_redirect_html()

# Include all routers directly with the API_PREFIX
app.include_router(auth_router, prefix=f"{API_PREFIX}/auth", tags=["Authentication"])
app.include_router(dashboard_router, prefix=f"{API_PREFIX}/dashboard", tags=["Dashboard"])
app.include_router(user_router, prefix=f"{API_PREFIX}/users", tags=["Users"])
app.include_router(certification_explorer_router, prefix=f"{API_PREFIX}/certification-explorer", tags=["Certification Explorer"])
app.include_router(certification_router, prefix=f"{API_PREFIX}/certifications", tags=["Certifications"])
app.include_router(reports_router, prefix=f"{API_PREFIX}/reports", tags=["Reports"])
app.include_router(admin_router, prefix=f"{API_PREFIX}/admin", tags=["Admin"])
app.include_router(study_router, prefix=f"{API_PREFIX}/study", tags=["Study"])
app.include_router(misc_router, prefix=API_PREFIX, tags=["Miscellaneous"]) # For /career-path, /jobs/search, /health

# Add a simple test endpoint under the API prefix
@app.get(f"{API_PREFIX}/test", tags=["Test"])
async def test_endpoint():
    return {"message": "API v1 test endpoint reached!"}

# Add a versioned health endpoint
@app.get(f"{API_PREFIX}/health", response_model=HealthResponse, tags=["Health"])
async def versioned_health_check():
    """
    Health check endpoint to verify API status (accessible with /api/v1 prefix)
    """
    try:
        # Check database connection
        db = next(get_db())
        db_status = "connected"
        # Execute simple query to verify connectivity
        result = db.execute(text("SELECT 1")).scalar()
        if result != 1:
            db_status = "error: unexpected result"
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        db_status = f"error: {str(e)}"

    # Check anthropic client status
    try:
        client = init_anthropic_client()
        anthropic_status = "available" if client else "unavailable"
    except Exception:
        anthropic_status = "unavailable"

    return HealthResponse(
        status="healthy",
        database=db_status,
        anthropic_client=anthropic_status
    )

# Add a root endpoint (outside the /api/v1 prefix)
@app.get("/", tags=["Root"])
async def root():
    """Root endpoint providing basic API info."""
    return {
        "message": f"Welcome to the Security Certification Path Creator API {API_VERSION}",
        "documentation": "/docs",
        "api_version": API_VERSION
    }

# Health check endpoint at the root level
@app.get("/health", response_model=HealthResponse, tags=["Health"])
async def root_health_check():
    """
    Health check endpoint to verify API status (accessible without /api/v1 prefix)
    """
    try:
        # Check database connection
        db = next(get_db())
        db_status = "connected"
        # Execute simple query to verify connectivity
        result = db.execute(text("SELECT 1")).scalar()
        if result != 1:
            db_status = "error: unexpected result"
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        db_status = f"error: {str(e)}"

    # Check anthropic client status
    try:
        client = init_anthropic_client()
        anthropic_status = "available" if client else "unavailable"
    except Exception:
        anthropic_status = "unavailable"

    return HealthResponse(
        status="healthy",
        database=db_status,
        anthropic_client=anthropic_status
    )

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup with enhanced error handling"""
    logger.info("Starting FastAPI application")
    logger.info(f"API will be available at: http://{settings.HOST}:{settings.PORT}")
    logger.info(f"API Version: {API_VERSION}")
    logger.info(f"API Prefix: {API_PREFIX}")

    if not settings.DATABASE_URL:
        logger.error("DATABASE_URL environment variable is not set")
        raise RuntimeError("Database configuration missing")

    logger.info(f"Using database at {settings.DATABASE_URL.split('@')[-1]}")

    # Initialize services with proper error handling
    services_status = {
        'database': False,
        'anthropic': False
    }

    try:
        init_db()
        services_status['database'] = True
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        logger.error(traceback.format_exc())

    try:
        init_anthropic_client()
        services_status['anthropic'] = True
        logger.info("Anthropic client initialized successfully")
    except Exception as e:
        logger.error(f"Anthropic client initialization failed: {e}")
