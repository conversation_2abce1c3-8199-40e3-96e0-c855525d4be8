"""FastAPI application for certification path creator functionality"""
import sys
from pathlib import Path
import logging
from fastapi import FastAPI, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from datetime import datetime
from sqlalchemy import text
import traceback

# Add parent directory to path if needed
parent_dir = str(Path(__file__).parent.parent)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from api.config import settings, logger
from api.constants import API_VERSION, API_PREFIX, HEALTH
from database import init_db, get_db
from api.route_manager import route_manager
from api.routes import router as api_router
from utils.claude_assistant import init_anthropic_client

# Define models first
class HealthResponse(BaseModel):
    """Health check response model"""
    status: str
    database: str
    anthropic_client: str
    api_version: str = API_VERSION
    timestamp: datetime = Field(default_factory=datetime.utcnow)

# Create FastAPI app with settings
app = FastAPI(**settings.fastapi_kwargs)

# Add CORS middleware with settings
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router with prefix from constants
# Note: Individual routers will have their own prefixes
app.include_router(api_router, prefix=API_PREFIX)

# Function to create a versioned API that could be used for future versions
def create_versioned_api(version: str):
    """
    Create a versioned API with its own router
    
    Args:
        version: API version string (e.g., 'v2')
    
    Returns:
        Configured router for the specified version
    """
    # This would be where we'd set up a completely different API version
    # with potentially different routes and implementations
    router = APIRouter()
    
    @router.get("/", tags=["Root"])
    async def versioned_root():
        return {
            "message": f"API {version} is active",
            "documentation": "/docs", 
            "api_version": version
        }
    
    return router

# For future versions, we could include them like this:
# v2_router = create_versioned_api("v2") 
# app.include_router(v2_router, prefix=f"/api/v2")

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup with enhanced error handling"""
    logger.info("Starting FastAPI application")
    logger.info(f"API will be available at: http://{settings.HOST}:{settings.PORT}")
    logger.info(f"API Version: {API_VERSION}")
    logger.info(f"API Prefix: {API_PREFIX}")

    if not settings.DATABASE_URL:
        logger.error("DATABASE_URL environment variable is not set")
        raise RuntimeError("Database configuration missing")

    logger.info(f"Using database at {settings.DATABASE_URL.split('@')[-1]}")

    # Initialize services with proper error handling
    services_status = {
        'database': False,
        'anthropic': False
    }

    try:
        # Initialize database
        logger.info("Initializing database...")
        init_db()
        logger.info("Successfully initialized database")

        # Test database connection with parameterized query
        logger.info("Testing database connection...")
        db = next(get_db())
        try:
            db.execute(text("SELECT 1 WHERE :param = :param"), {"param": 1})
            logger.info("Database connection verified")
            services_status['database'] = True
        except Exception as e:
            logger.error(f"Database connection test failed: {str(e)}\n{traceback.format_exc()}")
        finally:
            db.close()

        # Initialize Anthropic client
        logger.info("Initializing Anthropic client...")
        try:
            client = init_anthropic_client()
            if client:
                logger.info("Successfully initialized Anthropic client")
                services_status['anthropic'] = True
            else:
                logger.warning("Failed to initialize Anthropic client - some features may be limited")
        except Exception as e:
            logger.error(f"Anthropic client initialization failed: {str(e)}\n{traceback.format_exc()}")
            # Non-critical error, continue startup

    except Exception as e:
        logger.error(f"Error during startup: {str(e)}\n{traceback.format_exc()}")
        if not services_status['database']:
            # Database is critical, raise error if it failed
            raise RuntimeError(f"Critical startup error: {str(e)}")

    # Log final startup status
    logger.info("Startup completed with status: " + 
                ", ".join(f"{k}: {'ready' if v else 'failed'}" 
                         for k, v in services_status.items()))

@app.get("/", tags=["Root"])
async def root():
    """Root endpoint that redirects to documentation"""
    return {
        "message": f"Welcome to the Security Certification Path Creator API {API_VERSION}", 
        "documentation": "/docs",
        "api_version": API_VERSION,
        "api_prefix": API_PREFIX
    }

@app.get(HEALTH.replace(API_PREFIX, ""), response_model=HealthResponse)
async def health_check():
    """Health check endpoint to verify service status"""
    logger.info("Performing health check")
    try:
        # Check database connection with parameterized query
        db = next(get_db())
        try:
            db.execute(text("SELECT 1 WHERE :param = :param"), {"param": 1})
            db_status = "connected"
            logger.info("Database check successful")
        except Exception as e:
            logger.error(f"Database health check failed: {str(e)}")
            db_status = "error"
        finally:
            db.close()

        # Check Anthropic client
        anthropic_ready = init_anthropic_client() is not None
        client_status = "ready" if anthropic_ready else "not_ready"
        logger.info(f"Anthropic client status: {client_status}")

        return HealthResponse(
            status="healthy",
            database=db_status,
            anthropic_client=client_status
        )

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service unhealthy: {str(e)}"
        )