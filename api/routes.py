"""API routes for the certification path creator functionality"""
import logging
from fastapi import APIRouter, HTTPException, status
from typing import Dict, Any, Optional, List
from datetime import datetime
from utils.claude_assistant import generate_career_path, analyze_certification_alignment, init_anthropic_client
from database import get_db
from models.certification import Certification
from sqlalchemy.exc import SQLAlchemyError
from pydantic import BaseModel, Field
from sqlalchemy import text, select
from utils.job_management import get_filtered_jobs
from api.constants import API_VERSION, API_PREFIX
from api.route_manager import route_manager

# Import the endpoint routers
from api.endpoints.dashboard import router as dashboard_router
from api.endpoints.user_management import router as user_router
from api.endpoints.certification_explorer import router as certification_router
from api.endpoints.reports import router as reports_router
from api.endpoints.admin_interface import router as admin_router
from api.endpoints.auth import router as auth_router
from api.endpoints.study import router as study_router

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Register routers with the route manager
route_manager.register_router(auth_router, "auth", name="auth")
route_manager.register_router(dashboard_router, "", name="dashboard")
route_manager.register_router(user_router, "", name="user")
route_manager.register_router(certification_router, "", name="certification")
route_manager.register_router(reports_router, "", name="reports")
route_manager.register_router(admin_router, "", name="admin")
route_manager.register_router(study_router, "study", name="study")

# Get the main router to export
router = route_manager.get_router()

# Create a misc_router for miscellaneous endpoints
misc_router = APIRouter()


class CareerPathRequest(BaseModel):
    """Request model for career path generation"""
    completed_certifications: Optional[List[str]] = Field(
        default=[], description="List of completed certification names")
    interests: List[str] = Field(..., min_items=1,
                                 description="List of security domains of interest")
    years_experience: int = Field(..., ge=0, le=50,
                                  description="Years of experience in security")
    current_role: str = Field(..., min_length=1,
                              description="Current job role")
    target_role: str = Field(..., min_length=1,
                             description="Target career role")
    learning_style: str = Field(
        ...,
        description="Preferred learning style (Visual, Reading, Hands-on, Mixed)"
    )
    study_hours: int = Field(
        ...,
        ge=1,
        le=168,
        description="Available study hours per week"
    )

    class Config:
        schema_extra = {
            "example": {
                "completed_certifications": ["A+", "Network+"],
                "interests": ["Network Security", "Cloud Security"],
                "years_experience": 5,
                "current_role": "Security Analyst",
                "target_role": "Security Engineer",
                "learning_style": "Mixed",
                "study_hours": 10
            }
        }


@misc_router.post("/career-path", tags=["Career Path"], response_model=Dict[str, Any])
async def generate_career_path_endpoint(request: CareerPathRequest):
    """Generate a personalized certification career path based on user profile."""
    logger.info(f"Received career path request: {request.dict()}")

    try:
        # Get certifications from database using SQLAlchemy ORM (automatically parameterized)
        db = next(get_db())
        try:
            # Use parameterized query for health check
            db.execute(text("SELECT 1 WHERE :param = :param"), {"param": 1})

            # Use SQLAlchemy ORM for safe querying (automatically parameterized)
            certifications = db.query(Certification).all()
            if not certifications:
                logger.error("No certifications found in database")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="No certification data available"
                )

            cert_data = [cert.to_dict() for cert in certifications]
            logger.info(f"Retrieved {len(cert_data)} certifications")

        except SQLAlchemyError as e:
            logger.error(f"Database error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Database error occurred"
            )

        # Determine experience level
        experience_level = (
            "Entry Level" if request.years_experience < 2 else
            "Mid Level" if request.years_experience < 5 else
            "Senior Level" if request.years_experience < 10 else "Expert"
        )

        # Generate career path
        try:
            recommendations = generate_career_path(
                interests=request.interests,
                experience_level=experience_level,
                certifications=cert_data,
                target_role=request.target_role,
                context_id="api-request"
            )

            if "error" in recommendations:
                logger.error(
                    f"Error generating path: {recommendations['error']}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=recommendations["error"]
                )

            # If there are completed certifications, analyze alignment
            alignment = None
            if request.completed_certifications:
                alignment = analyze_certification_alignment(
                    current_certs=request.completed_certifications,
                    target_role=request.target_role,
                    cert_data=cert_data,
                    context_id="api-request"
                )

            # Add certification costs using ORM for safe querying
            for stage in recommendations['career_path']:
                cert_costs = {}
                for cert_name in stage['certifications']:
                    # Use SQLAlchemy ORM for safe querying (automatically parameterized)
                    cert_data = next(
                        (cert for cert in certifications if cert.name == cert_name), None)
                    if cert_data:
                        cert_costs[cert_name] = {
                            'exam': float(cert_data.cost or 0),
                            # Estimated material cost
                            'materials': float(cert_data.custom_hours or 0) * 50,
                            # Estimated training cost
                            'training': float(cert_data.custom_hours or 0) * 100
                        }
                stage['certification_costs'] = cert_costs

            # Add metadata to response
            response = {
                "generated_at": datetime.utcnow().isoformat(),
                "request_id": "api-request",
                "api_version": API_VERSION,
                "user_profile": {
                    "years_experience": request.years_experience,
                    "current_role": request.current_role,
                    "learning_style": request.learning_style,
                    "study_hours": request.study_hours
                },
                "career_path": recommendations["career_path"],
                "alignment_analysis": alignment if alignment and "error" not in alignment else None
            }

            logger.info(
                f"Successfully generated path with {len(recommendations['career_path'])} stages")
            return response

        except Exception as e:
            logger.error(
                f"Error in career path generation: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error generating career path"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )


@misc_router.get("/health", tags=["Health"])
async def health_check():
    """Simple health check endpoint."""
    try:
        # Check database connection using parameterized query
        db = next(get_db())
        db.execute(text("SELECT 1 WHERE :param = :param"), {"param": 1})

        # Check Anthropic client
        anthropic_ready = init_anthropic_client() is not None

        return {
            "status": "healthy",
            "database": "connected",
            "anthropic_client": "ready" if anthropic_ready else "not_ready",
            "timestamp": datetime.utcnow().isoformat(),
            "api_version": API_VERSION
        }

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=str(e)
        )


@misc_router.get("/jobs/search")
async def search_jobs(
    term: str = "",
    domain: str = "All",
    page: int = 0,
    per_page: int = 10
) -> Dict[str, Any]:
    """
    Search for jobs using parameterized queries
    All database interactions use prepared statements
    """
    try:
        db = next(get_db())
        jobs, total = get_filtered_jobs(
            db=db,
            search_term=term,
            domain=domain,
            page=page,
            per_page=per_page
        )

        return {
            "jobs": [job.to_dict() for job in jobs],
            "total": total,
            "page": page,
            "per_page": per_page,
            "api_version": API_VERSION
        }

    except Exception as e:
        logger.error(f"Error searching jobs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error searching jobs"
        )

# The misc_router is used for endpoints that don't fit into other categories
# We'll keep it empty for now to avoid duplicate endpoints
