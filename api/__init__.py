"""API package initialization"""
import sys
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add parent directory to Python path for imports
parent_dir = str(Path(__file__).parent.parent)
logger.info(f"Adding parent directory to Python path: {parent_dir}")
if parent_dir not in sys.path:
    sys.path.append(parent_dir)
    logger.info("Parent directory added to Python path")