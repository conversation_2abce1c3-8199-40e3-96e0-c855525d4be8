"""
API constants and endpoint definitions
This file serves as a single source of truth for API endpoints
and can be imported both by backend and frontend code
"""

# API version
API_VERSION = "v1"
API_PREFIX = f"/api/{API_VERSION}"

# Utility function to get a versioned endpoint
def get_versioned_endpoint(endpoint_path, version=None):
    """
    Create a versioned endpoint path
    If version is None, uses the default API_VERSION
    """
    version = version or API_VERSION
    return f"/api/{version}/{endpoint_path.lstrip('/')}"

# Authentication endpoints
AUTH = {
    "PREFIX": f"{API_PREFIX}/auth",
    "LOGIN": f"{API_PREFIX}/auth/login",
    "LOGOUT": f"{API_PREFIX}/auth/logout",
    "ME": f"{API_PREFIX}/auth/me",
}

# User endpoints
USERS = {
    "PREFIX": f"{API_PREFIX}/users",
    "PROFILE": f"{API_PREFIX}/users/profile",
    "PREFERENCES": f"{API_PREFIX}/users/preferences",
    "CERTIFICATIONS": f"{API_PREFIX}/users/certifications",
    "TUTORIAL": f"{API_PREFIX}/users/tutorial",
}

# Study endpoints
STUDY = {
    "PREFIX": f"{API_PREFIX}/study",
    "ESTIMATE": f"{API_PREFIX}/study/estimate",
    "PROGRESS": f"{API_PREFIX}/study/progress", 
    "ROI": f"{API_PREFIX}/study/roi",
}

# Certification endpoints
CERTIFICATIONS = {
    "PREFIX": f"{API_PREFIX}/certifications",
    "LIST": f"{API_PREFIX}/certifications",
    "DETAIL": f"{API_PREFIX}/certifications/{{id}}",  # use with .format(id=id)
    "RELATIONSHIPS": f"{API_PREFIX}/certifications/relationships",
}

# Career path endpoints
CAREER = {
    "PREFIX": f"{API_PREFIX}/career",
    "PATHS": f"{API_PREFIX}/career/paths",
    "RECOMMENDATIONS": f"{API_PREFIX}/career/recommendations",
    "ROLES": f"{API_PREFIX}/career/roles",
}

# Job endpoints
JOBS = {
    "PREFIX": f"{API_PREFIX}/jobs",
    "SEARCH": f"{API_PREFIX}/jobs/search",
    "DETAIL": f"{API_PREFIX}/jobs/{{id}}",  # use with .format(id=id)
    "MARKET": f"{API_PREFIX}/jobs/market",
    "CERTIFICATIONS": f"{API_PREFIX}/jobs/{{id}}/certifications",  # use with .format(id=id)
}

# FAQ endpoints
FAQ = {
    "PREFIX": f"{API_PREFIX}/faq",
    "LIST": f"{API_PREFIX}/faq",
}

# Admin endpoints
ADMIN = {
    "PREFIX": f"{API_PREFIX}/admin",
    "CERTIFICATIONS": f"{API_PREFIX}/admin/certifications",
    "ORGANIZATIONS": f"{API_PREFIX}/admin/organizations",
    "FEEDBACK": f"{API_PREFIX}/admin/feedback",
    "TRANSLATIONS": f"{API_PREFIX}/admin/translations",
    "ENRICHMENT": f"{API_PREFIX}/admin/enrichment",
    "STATS": f"{API_PREFIX}/admin/stats",
    "JOBS": f"{API_PREFIX}/admin/jobs",
}

# Currency endpoints
CURRENCY = {
    "PREFIX": f"{API_PREFIX}/currency",
    "RATES": f"{API_PREFIX}/currency/rates",
    "CONVERT": f"{API_PREFIX}/currency/convert",
}

# Feedback endpoints
FEEDBACK = {
    "PREFIX": f"{API_PREFIX}/feedback",
    "SUBMIT": f"{API_PREFIX}/feedback",
}

# Journey endpoints
JOURNEYS = {
    "PREFIX": f"{API_PREFIX}/journeys",
    "LIST": f"{API_PREFIX}/journeys",
    "DETAIL": f"{API_PREFIX}/journeys/{{id}}",  # use with .format(id=id)
    "MILESTONES": f"{API_PREFIX}/journeys/{{id}}/milestones",  # use with .format(id=id)
    "COMMUNITY": f"{API_PREFIX}/journeys/community",
}

# Health check
HEALTH = f"{API_PREFIX}/health" 