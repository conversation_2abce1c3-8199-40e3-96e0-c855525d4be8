"""
Error reporting API endpoints for the frontend
Provides endpoints for logging errors from the UI
"""
import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Union

from fastapi import APIRouter, HTTPException, Body, Depends, Request, status
from pydantic import BaseModel, Field

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("api_errors.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("error_reporting")

# Models
class ErrorContext(BaseModel):
    """Additional context about an error"""
    category: Optional[str] = None
    module_path: Optional[str] = None
    component_name: Optional[str] = None
    file: Optional[str] = None
    build_time: Optional[str] = None
    level: Optional[str] = "error"
    additional_info: Optional[Dict[str, Any]] = None

class ErrorDetails(BaseModel):
    """Details about the actual error"""
    name: Optional[str] = None
    message: str
    stack: Optional[str] = None

class BrowserInfo(BaseModel):
    """Browser information from the client"""
    user_agent: str
    language: Optional[str] = None
    platform: Optional[str] = None

class ErrorReport(BaseModel):
    """Complete error report from the client"""
    timestamp: str = Field(..., description="ISO timestamp when the error occurred")
    error: Union[ErrorDetails, str]
    component_name: Optional[str] = None
    browser_info: Optional[BrowserInfo] = None
    additional_info: Optional[Dict[str, Any]] = None
    url: Optional[str] = None

# Create router
router = APIRouter(
    prefix="/error-logs",
    tags=["error-reporting"],
    responses={404: {"description": "Not found"}},
)

ERROR_LOG_DIR = os.environ.get("ERROR_LOG_DIR", "logs")

# Make sure the error log directory exists
os.makedirs(ERROR_LOG_DIR, exist_ok=True)

@router.post("/")
async def log_error(error_report: ErrorReport):
    """
    Log an error from the client
    
    This endpoint receives error reports from the frontend and logs them
    for debugging and monitoring purposes.
    """
    logger.error(f"Frontend error: {error_report.error}")
    
    try:
        # Save to a structured log file
        log_file = os.path.join(ERROR_LOG_DIR, "frontend_errors.jsonl")
        
        with open(log_file, "a") as f:
            f.write(json.dumps(error_report.dict()) + "\n")
        
        # For critical errors, we might want to send notifications
        error_level = (
            error_report.additional_info.get("level", "error") 
            if error_report.additional_info 
            else "error"
        )
        
        if error_level == "critical":
            # In a real implementation, we would send notifications
            # to the dev team via email, Slack, etc.
            logger.critical(f"CRITICAL ERROR: {error_report.error}")
        
        return {"status": "success", "message": "Error logged successfully"}
    
    except Exception as e:
        logger.exception("Failed to log error")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to log error: {str(e)}"
        )

@router.post("/build-notifications")
async def log_build_error(
    errors: List[Dict[str, Any]] = Body(...),
    build_time: str = Body(...),
    environment: str = Body(...)
):
    """
    Log build-time errors
    
    This endpoint receives build error reports from the build process
    and logs them for debugging.
    """
    logger.error(f"Build errors in {environment}: {errors}")
    
    try:
        # Save to a structured log file
        log_file = os.path.join(ERROR_LOG_DIR, "build_errors.jsonl")
        
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "build_time": build_time,
            "environment": environment,
            "errors": errors
        }
        
        with open(log_file, "a") as f:
            f.write(json.dumps(log_entry) + "\n")
        
        # In production, we would notify developers about build failures
        if environment == "production":
            # Send alerts, etc.
            logger.critical(f"Production build failed: {errors}")
        
        return {"status": "success", "message": "Build errors logged successfully"}
    
    except Exception as e:
        logger.exception("Failed to log build errors")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to log build errors: {str(e)}"
        )

# If using FastAPI as the main framework
def configure_routes(app):
    """Configure the error reporting routes for the main app"""
    app.include_router(router) 