"""
Centralized API Route Manager

This module provides a centralized way to manage API routes,
versioning, and endpoints to ensure consistency between
backend routes and frontend API client calls.
"""

from typing import Dict, List, Optional, Union, Callable
from fastapi import APIRouter
from api.constants import API_VERSION, API_PREFIX, get_versioned_endpoint

class RouteManager:
    """
    Manages API routes and versioning.
    Provides utilities to register routes with appropriate versioning.
    """
    
    def __init__(self):
        """Initialize the route manager with default version"""
        self.version = API_VERSION
        self.prefix = API_PREFIX
        self.routers: Dict[str, APIRouter] = {}
        self.main_router = APIRouter()
        
    def register_router(self, router: APIRouter, prefix: str, name: str = None):
        """
        Register a router with the specified prefix
        
        Args:
            router: The FastAPI router to register
            prefix: The router prefix (without /api/v1)
            name: Optional name to identify this router
        """
        if name:
            self.routers[name] = router
            
        # Make sure prefix doesn't have leading slash
        cleaned_prefix = prefix.lstrip('/')
        full_prefix = f"/{cleaned_prefix}" if cleaned_prefix else ""
        
        # Include the router with its cleaned prefix
        self.main_router.include_router(router, prefix=full_prefix)
        
    def get_endpoint(self, path: str, version: str = None) -> str:
        """
        Get a versioned endpoint path
        
        Args:
            path: The endpoint path without version prefix
            version: Optional version override
        
        Returns:
            The full endpoint path with version
        """
        return get_versioned_endpoint(path, version)
    
    def get_router(self) -> APIRouter:
        """Get the main router with all included routers"""
        return self.main_router

# Create a singleton instance for use throughout the app
route_manager = RouteManager() 