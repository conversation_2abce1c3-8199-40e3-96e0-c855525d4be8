"""
Route manager for the API
Handles registration and management of API routes
"""
from fastapi import APIRouter
from typing import Dict, Optional
from api.constants import API_VERSION, API_PREFIX

class RouteManager:
    """
    Manages API routes and their registration
    """
    def __init__(self):
        self.main_router = APIRouter()
        self.routers: Dict[str, APIRouter] = {}
        self.version = API_VERSION
        self.prefix = f"/api/{API_VERSION}"  # Fixed to match the expected format

    def register_router(self, router: APIRouter, prefix: str = "", name: Optional[str] = None):
        """
        Register a router with the route manager

        Args:
            router: The router to register
            prefix: Optional prefix for the router
            name: Optional name for the router
        """
        if name:
            self.routers[name] = router

        # Ensure prefix starts with / if it's not empty
        if prefix and not prefix.startswith("/"):
            prefix = f"/{prefix}"

        # Include the router in the main router
        self.main_router.include_router(router, prefix=prefix)

    def get_router(self) -> APIRouter:
        """
        Get the main router with all registered routers

        Returns:
            The main router
        """
        return self.main_router
        
    def get_endpoint(self, path: str, version: Optional[str] = None) -> str:
        """
        Get a versioned endpoint path
        
        Args:
            path: The endpoint path
            version: Optional API version, defaults to self.version
            
        Returns:
            The versioned endpoint path
        """
        # Remove leading slashes
        path = path.lstrip('/')
        
        # Use provided version or default
        ver = version or self.version
        
        return f"/api/{ver}/{path}"

# Create a singleton instance
route_manager = RouteManager()
