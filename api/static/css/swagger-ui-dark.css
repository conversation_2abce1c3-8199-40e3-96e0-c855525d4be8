/* Swagger UI Dark Mode Theme */
:root {
    --background-color: #1e1e1e;
    --text-color: #f0f0f0;
    --header-bg: #2d2d2d;
    --header-color: #ffffff;
    --link-color: #4990e2;
    --border-color: #444444;
    --input-bg: #333333;
    --input-color: #f0f0f0;
    --button-bg: #4990e2;
    --button-color: #ffffff;
    --method-get: #61affe;
    --method-post: #49cc90;
    --method-put: #fca130;
    --method-delete: #f93e3e;
    --method-patch: #50e3c2;
    --method-options: #0d5aa7;
    --method-head: #9012fe;
    --code-bg: #2d2d2d;
    --code-color: #f0f0f0;
    --model-bg: #333333;
    --model-color: #f0f0f0;
    --model-toggle-bg: #555555;
    --model-toggle-color: #f0f0f0;
}

/* Base styles */
body {
    background-color: var(--background-color);
    color: var(--text-color);
}

.swagger-ui {
    color: var(--text-color);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* Header */
.swagger-ui .topbar {
    background-color: var(--header-bg);
}

.swagger-ui .info {
    background-color: var(--background-color);
    color: var(--text-color);
}

.swagger-ui .info .title,
.swagger-ui .info h1,
.swagger-ui .info h2,
.swagger-ui .info h3,
.swagger-ui .info h4,
.swagger-ui .info h5,
.swagger-ui .info h6 {
    color: var(--text-color);
}

.swagger-ui .info a {
    color: var(--link-color);
}

/* Operations */
.swagger-ui .opblock {
    background-color: var(--model-bg);
    border-color: var(--border-color);
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
}

.swagger-ui .opblock .opblock-summary {
    border-color: var(--border-color);
}

.swagger-ui .opblock .opblock-summary-method {
    color: #fff;
}

.swagger-ui .opblock.opblock-get {
    border-color: var(--method-get);
}

.swagger-ui .opblock.opblock-post {
    border-color: var(--method-post);
}

.swagger-ui .opblock.opblock-put {
    border-color: var(--method-put);
}

.swagger-ui .opblock.opblock-delete {
    border-color: var(--method-delete);
}

.swagger-ui .opblock.opblock-patch {
    border-color: var(--method-patch);
}

.swagger-ui .opblock.opblock-options {
    border-color: var(--method-options);
}

.swagger-ui .opblock.opblock-head {
    border-color: var(--method-head);
}

.swagger-ui .opblock-tag {
    border-color: var(--border-color);
    color: var(--text-color);
}

.swagger-ui .opblock-tag:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.swagger-ui .opblock .opblock-section-header {
    background-color: rgba(0, 0, 0, 0.2);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.swagger-ui .opblock .opblock-section-header h4 {
    color: var(--text-color);
}

.swagger-ui .opblock .opblock-summary-description {
    color: var(--text-color);
}

.swagger-ui .opblock-description-wrapper p,
.swagger-ui .opblock-external-docs-wrapper p,
.swagger-ui .opblock-title_normal p {
    color: var(--text-color);
}

/* Models */
.swagger-ui section.models {
    border-color: var(--border-color);
}

.swagger-ui section.models .model-container {
    background-color: var(--model-bg);
    border-color: var(--border-color);
}

.swagger-ui section.models h4 {
    color: var(--text-color);
}

.swagger-ui .model-title {
    color: var(--text-color);
}

.swagger-ui .model {
    color: var(--model-color);
}

.swagger-ui .model-toggle {
    background-color: var(--model-toggle-bg);
    color: var(--model-toggle-color);
}

/* Schema */
.swagger-ui .prop-type {
    color: #9cdcfe;
}

.swagger-ui .prop-format {
    color: #ce9178;
}

.swagger-ui .prop-name {
    color: #9cdcfe;
}

.swagger-ui table.model tr.property-row td {
    color: var(--text-color);
}

.swagger-ui table.model {
    color: var(--text-color);
}

/* Tables */
.swagger-ui .table-container {
    background-color: var(--background-color);
}

.swagger-ui table thead tr th {
    color: var(--text-color);
    border-color: var(--border-color);
}

.swagger-ui table tbody tr td {
    color: var(--text-color);
    border-color: var(--border-color);
}

/* Inputs */
.swagger-ui .btn {
    background-color: var(--button-bg);
    color: var(--button-color);
    border-color: var(--border-color);
}

.swagger-ui select {
    background-color: var(--input-bg);
    color: var(--input-color);
    border-color: var(--border-color);
}

.swagger-ui input[type=text],
.swagger-ui input[type=password],
.swagger-ui input[type=search],
.swagger-ui input[type=email],
.swagger-ui input[type=number],
.swagger-ui textarea {
    background-color: var(--input-bg);
    color: var(--input-color);
    border-color: var(--border-color);
}

/* Code */
.swagger-ui .highlight-code {
    background-color: var(--code-bg);
}

.swagger-ui .highlight-code pre {
    color: var(--code-color);
}

.swagger-ui .microlight {
    background-color: var(--code-bg);
    color: var(--code-color);
}

/* Authorize button */
.swagger-ui .btn.authorize {
    border-color: var(--method-get);
    color: var(--method-get);
}

.swagger-ui .btn.authorize svg {
    fill: var(--method-get);
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: var(--background-color);
}

::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* Additional styles for better readability */
.swagger-ui .markdown p, 
.swagger-ui .markdown pre, 
.swagger-ui .renderedMarkdown p, 
.swagger-ui .renderedMarkdown pre {
    color: var(--text-color);
}

.swagger-ui .response-col_status {
    color: var(--text-color);
}

.swagger-ui .response-col_description__inner div.markdown, 
.swagger-ui .response-col_description__inner div.renderedMarkdown {
    color: var(--text-color);
}

.swagger-ui .responses-table {
    background-color: var(--background-color);
}

.swagger-ui .parameter__name,
.swagger-ui .parameter__type {
    color: var(--text-color);
}

.swagger-ui .parameter__in {
    color: #999;
}

/* Syntax highlighting for code blocks */
.swagger-ui .microlight .pln {
    color: #f8f8f2;
}

.swagger-ui .microlight .str {
    color: #a6e22e;
}

.swagger-ui .microlight .kwd {
    color: #66d9ef;
}

.swagger-ui .microlight .com {
    color: #75715e;
}

.swagger-ui .microlight .typ {
    color: #66d9ef;
}

.swagger-ui .microlight .lit {
    color: #ae81ff;
}

.swagger-ui .microlight .pun,
.swagger-ui .microlight .opn,
.swagger-ui .microlight .clo {
    color: #f8f8f2;
}

.swagger-ui .microlight .tag {
    color: #f92672;
}

.swagger-ui .microlight .atn {
    color: #a6e22e;
}

.swagger-ui .microlight .atv {
    color: #e6db74;
}

.swagger-ui .microlight .dec,
.swagger-ui .microlight .var {
    color: #a6e22e;
}

.swagger-ui .microlight .fun {
    color: #66d9ef;
}
