"""
Tests for User API Endpoints

These tests verify the functionality of the user-related endpoints:
- /api/v1/users/profile
- /api/v1/users/preferences
- /api/v1/users/certifications
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta, date
import jwt
import json

from api.app import app
from shared.api_endpoints import API_VERSION, API_PREFIX, API_ENDPOINTS
from models.user import User, UserR<PERSON>, UserStatus, UserPreference
from models.certification import Certification, UserCertification

client = TestClient(app)

# Constants for testing
PROFILE_URL = API_ENDPOINTS["USERS"]["PROFILE"]
PREFERENCES_URL = API_ENDPOINTS["USERS"]["PREFERENCES"]
CERTIFICATIONS_URL = API_ENDPOINTS["USERS"]["CERTIFICATIONS"]
TEST_SECRET_KEY = "test-secret-key"

# Test data - User
test_user = {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "hashed_password",
    "full_name": "Test User",
    "role": UserRole.USER,
    "status": UserStatus.ACTIVE,
    "created_at": datetime.utcnow(),
    "updated_at": datetime.utcnow(),
    "last_login": None
}

# Test data - User preferences
test_preferences = {
    "id": 1,
    "user_id": 1,
    "theme": "dark",
    "language": "en",
    "notifications_enabled": True,
    "email_notifications": True,
    "created_at": datetime.utcnow(),
    "updated_at": datetime.utcnow(),
}

# Test data - Certifications
test_certification = {
    "id": 1,
    "name": "Test Certification",
    "code": "TEST-CERT-001",
    "organization": "Test Org",
    "domain": "Security Testing",
    "level": "Intermediate",
    "description": "A certification for testing",
    "cost": 200.0,
    "url": "https://example.com/cert",
    "created_at": datetime.utcnow(),
    "updated_at": datetime.utcnow(),
}

test_user_certification = {
    "id": 1,
    "user_id": 1,
    "certification_id": 1,
    "date_earned": date.today() - timedelta(days=90),
    "expiration_date": date.today() + timedelta(days=275),
    "verification_url": "https://verify.example.com/cert/12345",
    "certificate_image_url": "https://example.com/certificates/12345.pdf",
    "status": "active",
    "created_at": datetime.utcnow(),
    "updated_at": datetime.utcnow(),
}

# Helper functions
def create_test_token(user_data, expiration_minutes=30):
    """Create a test JWT token for the given user data"""
    payload = {
        "sub": user_data["username"],
        "role": user_data["role"].value,
        "exp": datetime.utcnow() + timedelta(minutes=expiration_minutes)
    }
    return jwt.encode(payload, TEST_SECRET_KEY, algorithm="HS256")

def create_mock_user(user_data):
    """Create a mock User object from dictionary data"""
    user = MagicMock(spec=User)
    for key, value in user_data.items():
        setattr(user, key, value)
    return user

def create_mock_preferences(prefs_data):
    """Create a mock UserPreference object from dictionary data"""
    prefs = MagicMock(spec=UserPreference)
    for key, value in prefs_data.items():
        setattr(prefs, key, value)
    return prefs

def create_mock_certification(cert_data):
    """Create a mock Certification object from dictionary data"""
    cert = MagicMock(spec=Certification)
    for key, value in cert_data.items():
        setattr(cert, key, value)
    return cert

def create_mock_user_certification(user_cert_data):
    """Create a mock UserCertification object from dictionary data"""
    user_cert = MagicMock(spec=UserCertification)
    for key, value in user_cert_data.items():
        setattr(user_cert, key, value)
    return user_cert

# User Profile Tests
@patch("api.endpoints.user_management.get_current_active_user")
def test_get_profile(mock_current_user):
    """Test getting user profile"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    response = client.get(PROFILE_URL, headers=headers)
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_user["id"]
    assert data["username"] == test_user["username"]
    assert data["email"] == test_user["email"]
    assert data["full_name"] == test_user["full_name"]
    assert data["role"] == test_user["role"].value
    assert "password" not in data  # Password should not be in response

@patch("api.endpoints.user_management.get_current_active_user")
@patch("api.endpoints.user_management.get_db")
def test_update_profile_success(mock_get_db, mock_current_user):
    """Test updating user profile successfully"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    # No existing user with the new username/email (avoid conflicts)
    mock_session.query.return_value.filter.return_value.first.return_value = None
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    # Update data
    update_data = {
        "username": "updated_username",
        "email": "<EMAIL>",
        "full_name": "Updated User"
    }
    
    response = client.put(PROFILE_URL, headers=headers, json=update_data)
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == update_data["username"]
    assert data["email"] == update_data["email"]
    assert data["full_name"] == update_data["full_name"]

@patch("api.endpoints.user_management.get_current_active_user")
@patch("api.endpoints.user_management.get_db")
def test_update_profile_username_taken(mock_get_db, mock_current_user):
    """Test updating user profile with a username that's already taken"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    # Mock that username is already taken by a different user
    existing_user = create_mock_user({**test_user, "id": 999})  # Different user ID
    mock_session.query.return_value.filter.return_value.first.return_value = existing_user
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    # Update data
    update_data = {
        "username": "taken_username"
    }
    
    response = client.put(PROFILE_URL, headers=headers, json=update_data)
    
    # Assert response
    assert response.status_code == 400
    data = response.json()
    assert "detail" in data
    assert "Username already taken" in data["detail"]

@patch("api.endpoints.user_management.get_current_active_user")
@patch("api.endpoints.user_management.get_db")
def test_update_profile_with_password(mock_get_db, mock_current_user):
    """Test updating user profile with a new password"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    # No existing user with the new username/email (avoid conflicts)
    mock_session.query.return_value.filter.return_value.first.return_value = None
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    # Update data with password
    update_data = {
        "password": "newpassword123"
    }
    
    # Mock the get_password_hash function
    with patch("api.endpoints.auth.get_password_hash", return_value="new_hashed_password"):
        response = client.put(PROFILE_URL, headers=headers, json=update_data)
        
        # Assert response
        assert response.status_code == 200
        data = response.json()
        assert "password" not in data  # Password should not be in response

# User Preferences Tests
@patch("api.endpoints.user_management.get_current_active_user")
@patch("api.endpoints.user_management.get_db")
def test_get_preferences_existing(mock_get_db, mock_current_user):
    """Test getting existing user preferences"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    mock_prefs = create_mock_preferences(test_preferences)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_prefs
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    response = client.get(PREFERENCES_URL, headers=headers)
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["theme"] == test_preferences["theme"]
    assert data["language"] == test_preferences["language"]
    assert data["notifications_enabled"] == test_preferences["notifications_enabled"]
    assert data["email_notifications"] == test_preferences["email_notifications"]

@patch("api.endpoints.user_management.get_current_active_user")
@patch("api.endpoints.user_management.get_db")
def test_get_preferences_create_default(mock_get_db, mock_current_user):
    """Test getting user preferences when none exist (should create defaults)"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    # No existing preferences
    mock_session.query.return_value.filter.return_value.first.return_value = None
    # Mock preference creation
    default_prefs = create_mock_preferences({
        "user_id": test_user["id"],
        "theme": "light",
        "language": "en",
        "notifications_enabled": True,
        "email_notifications": True
    })
    # Simulate the add & refresh actions
    mock_session.add.side_effect = lambda x: None
    mock_session.commit.side_effect = lambda: None
    mock_session.refresh.side_effect = lambda x: setattr(x, "id", 1)
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    # After mocking, we need to patch the query again to return the new default preferences
    with patch("api.endpoints.user_management.UserPreferenceModel") as mock_pref_model:
        mock_pref_model.return_value = default_prefs
        response = client.get(PREFERENCES_URL, headers=headers)
        
        # Assert response
        assert response.status_code == 200
        data = response.json()
        assert data["theme"] == "light"  # Default theme
        assert data["language"] == "en"   # Default language
        assert data["notifications_enabled"] == True
        assert data["email_notifications"] == True

@patch("api.endpoints.user_management.get_current_active_user")
@patch("api.endpoints.user_management.get_db")
def test_update_preferences(mock_get_db, mock_current_user):
    """Test updating user preferences"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    mock_prefs = create_mock_preferences(test_preferences)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_prefs
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    # Update data
    update_data = {
        "theme": "light",
        "language": "fr",
        "notifications_enabled": False,
        "email_notifications": True
    }
    
    response = client.put(PREFERENCES_URL, headers=headers, json=update_data)
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["theme"] == update_data["theme"]
    assert data["language"] == update_data["language"]
    assert data["notifications_enabled"] == update_data["notifications_enabled"]
    assert data["email_notifications"] == update_data["email_notifications"]

# User Certifications Tests
@patch("api.endpoints.user_management.get_current_active_user")
@patch("api.endpoints.user_management.get_db")
def test_get_certifications(mock_get_db, mock_current_user):
    """Test getting user certifications"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    # Mock a query result with a tuple of (UserCertification, name, code, organization)
    mock_uc = create_mock_user_certification(test_user_certification)
    mock_result = [(mock_uc, test_certification["name"], test_certification["code"], test_certification["organization"])]
    mock_session.query.return_value.join.return_value.filter.return_value.all.return_value = mock_result
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    response = client.get(CERTIFICATIONS_URL, headers=headers)
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) == 1
    assert data[0]["id"] == test_user_certification["id"]
    assert data[0]["certification_id"] == test_user_certification["certification_id"]
    assert data[0]["name"] == test_certification["name"]
    assert data[0]["code"] == test_certification["code"]
    assert data[0]["organization"] == test_certification["organization"]

@patch("api.endpoints.user_management.get_current_active_user")
@patch("api.endpoints.user_management.get_db")
def test_add_certification(mock_get_db, mock_current_user):
    """Test adding a certification to user"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    # Mock certification exists
    mock_cert = create_mock_certification(test_certification)
    mock_session.query.return_value.filter.return_value.first.side_effect = [
        mock_cert,  # First call checks if certification exists
        None        # Second call checks if user already has it
    ]
    # Mock add & refresh
    mock_session.add.side_effect = lambda x: None
    mock_session.commit.side_effect = lambda: None
    mock_session.refresh.side_effect = lambda x: setattr(x, "id", 1)
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    # Certification data
    cert_data = {
        "certification_id": 1,
        "date_earned": str(date.today() - timedelta(days=90)),
        "expiration_date": str(date.today() + timedelta(days=275)),
        "verification_url": "https://verify.example.com/cert/12345",
        "certificate_image_url": "https://example.com/certificates/12345.pdf"
    }
    
    # After mocking, we need to patch to return the new user certification
    with patch("api.endpoints.user_management.UserCertification") as mock_uc_model:
        mock_uc = create_mock_user_certification(test_user_certification)
        mock_uc_model.return_value = mock_uc
        response = client.post(CERTIFICATIONS_URL, headers=headers, json=cert_data)
        
        # Assert response
        assert response.status_code == 201
        data = response.json()
        assert data["certification_id"] == cert_data["certification_id"]
        assert data["name"] == test_certification["name"]
        assert data["verification_url"] == cert_data["verification_url"]

@patch("api.endpoints.user_management.get_current_active_user")
@patch("api.endpoints.user_management.get_db")
def test_add_certification_already_exists(mock_get_db, mock_current_user):
    """Test adding a certification that user already has"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    # Mock certification exists and user already has it
    mock_cert = create_mock_certification(test_certification)
    mock_uc = create_mock_user_certification(test_user_certification)
    mock_session.query.return_value.filter.return_value.first.side_effect = [
        mock_cert,  # First call checks if certification exists
        mock_uc     # Second call checks if user already has it (returns True)
    ]
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    # Certification data
    cert_data = {
        "certification_id": 1
    }
    
    response = client.post(CERTIFICATIONS_URL, headers=headers, json=cert_data)
    
    # Assert response
    assert response.status_code == 400
    data = response.json()
    assert "detail" in data
    assert "User already has this certification" in data["detail"]

@patch("api.endpoints.user_management.get_current_active_user")
@patch("api.endpoints.user_management.get_db")
def test_delete_certification(mock_get_db, mock_current_user):
    """Test deleting a user certification"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    # Mock user certification exists
    mock_uc = create_mock_user_certification(test_user_certification)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_uc
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    response = client.delete(f"{CERTIFICATIONS_URL}/1", headers=headers)
    
    # Assert response
    assert response.status_code == 204
    assert not response.content  # No content

@patch("api.endpoints.user_management.get_current_active_user")
@patch("api.endpoints.user_management.get_db")
def test_delete_nonexistent_certification(mock_get_db, mock_current_user):
    """Test deleting a certification that user doesn't have"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    # Mock certification doesn't exist
    mock_session.query.return_value.filter.return_value.first.return_value = None
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    response = client.delete(f"{CERTIFICATIONS_URL}/999", headers=headers)
    
    # Assert response
    assert response.status_code == 404
    data = response.json()
    assert "detail" in data
    assert "User certification not found" in data["detail"] 