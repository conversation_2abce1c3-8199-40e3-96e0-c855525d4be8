"""
Tests for Authentication API Endpoints

These tests verify the functionality of the authentication endpoints:
- /api/v1/auth/login
- /api/v1/auth/logout
- /api/v1/auth/me
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta
import jwt
import bcrypt
import json

from api.app import app
from shared.api_endpoints import API_VERSION, API_PREFIX, API_ENDPOINTS
from models.user import User, UserRole, UserStatus

client = TestClient(app)

# Constants for testing
AUTH_LOGIN_URL = API_ENDPOINTS["AUTH"]["LOGIN"]
AUTH_LOGOUT_URL = API_ENDPOINTS["AUTH"]["LOGOUT"]
AUTH_ME_URL = API_ENDPOINTS["AUTH"]["ME"]
TEST_SECRET_KEY = "test-secret-key"

# Test data
test_user = {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "password": bcrypt.hashpw("password123".encode(), bcrypt.gensalt()).decode(),
    "full_name": "Test User",
    "role": UserRole.USER,
    "status": UserStatus.ACTIVE,
    "created_at": datetime.utcnow(),
    "updated_at": datetime.utcnow(),
    "last_login": None
}

test_admin = {
    "id": 2,
    "username": "adminuser",
    "email": "<EMAIL>",
    "password": bcrypt.hashpw("admin123".encode(), bcrypt.gensalt()).decode(),
    "full_name": "Admin User",
    "role": UserRole.ADMIN,
    "status": UserStatus.ACTIVE,
    "created_at": datetime.utcnow(),
    "updated_at": datetime.utcnow(),
    "last_login": None
}

test_inactive_user = {
    "id": 3,
    "username": "inactiveuser",
    "email": "<EMAIL>",
    "password": bcrypt.hashpw("inactive123".encode(), bcrypt.gensalt()).decode(),
    "full_name": "Inactive User",
    "role": UserRole.USER,
    "status": UserStatus.INACTIVE,
    "created_at": datetime.utcnow(),
    "updated_at": datetime.utcnow(),
    "last_login": None
}

# Helper functions
def create_test_token(user_data, expiration_minutes=30):
    """Create a test JWT token for the given user data"""
    payload = {
        "sub": user_data["username"],
        "role": user_data["role"].value,
        "exp": datetime.utcnow() + timedelta(minutes=expiration_minutes)
    }
    return jwt.encode(payload, TEST_SECRET_KEY, algorithm="HS256")

def create_mock_user(user_data):
    """Create a mock User object from dictionary data"""
    user = MagicMock()
    
    # Set all attributes from the user_data dictionary
    for key, value in user_data.items():
        setattr(user, key, value)
    
    # Mock special methods to ensure enum comparisons work correctly
    user.__eq__ = lambda other: False  # Default to inequality
    user.status.__eq__ = lambda other: user_data["status"] == other
    user.status.__ne__ = lambda other: user_data["status"] != other
    
    return user

# Mock patching for authentication tests
@pytest.fixture
def mock_auth_dependencies(monkeypatch):
    """Patch common auth dependencies"""
    # Mock the SECRET_KEY
    monkeypatch.setattr("api.endpoints.auth.SECRET_KEY", TEST_SECRET_KEY)
    
    # Mock the password verification
    def mock_verify_password(plain_password, hashed_password):
        # For testing, just compare against the stored passwords
        if plain_password == "password123" and user_data["username"] == "testuser":
            return True
        if plain_password == "admin123" and user_data["username"] == "adminuser":
            return True
        if plain_password == "inactive123" and user_data["username"] == "inactiveuser":
            return True
        return False
    
    monkeypatch.setattr("api.endpoints.auth.verify_password", mock_verify_password)

# Authentication Tests
@patch("api.endpoints.auth.create_access_token", return_value="test_token")
@patch("api.endpoints.auth.verify_password", return_value=True)
@patch("api.endpoints.auth.get_db")
def test_login_success(mock_get_db, mock_verify_password, mock_create_token):
    """Test successful login with valid credentials"""
    # Mock DB session and user
    mock_session = MagicMock()
    mock_user = MagicMock()
    
    # Set specific attributes we need
    mock_user.id = 1
    mock_user.username = "testuser"
    mock_user.password = "hashed_password"
    mock_user.status = UserStatus.ACTIVE
    mock_user.role = UserRole.USER
    
    # Configure status enum to pass equality tests
    mock_user.status == UserStatus.ACTIVE  # Should return True
    
    # Configure the mock session to return our user
    mock_session.query.return_value.filter.return_value.first.return_value = mock_user
    mock_get_db.return_value = mock_session
    
    print("DEBUG: Test setup complete")
    
    # Test the login endpoint
    response = client.post(
        AUTH_LOGIN_URL,
        data={"username": "testuser", "password": "password123"}
    )
    
    print(f"DEBUG: Response status: {response.status_code}")
    print(f"DEBUG: Response body: {response.json() if response.status_code != 500 else 'Internal Server Error'}")
    print(f"DEBUG: verify_password called: {mock_verify_password.called}")
    print(f"DEBUG: create_access_token called: {mock_create_token.called}")
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"
    assert data["user_id"] == mock_user.id
    assert data["username"] == mock_user.username

@patch("api.endpoints.auth.get_db")
def test_login_invalid_credentials(mock_get_db):
    """Test login with invalid credentials"""
    # Mock DB session and user
    mock_session = MagicMock()
    mock_user = create_mock_user(test_user)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_user
    mock_get_db.return_value = mock_session
    
    # Mock password verification to fail
    with patch("api.endpoints.auth.verify_password", return_value=False):
        response = client.post(
            AUTH_LOGIN_URL,
            data={"username": "testuser", "password": "wrongpassword"}
        )
        
        # Assert response
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
        assert "Incorrect username or password" in data["detail"]

@patch("api.endpoints.auth.get_db")
def test_login_nonexistent_user(mock_get_db):
    """Test login with a username that doesn't exist"""
    # Mock DB session to return no user
    mock_session = MagicMock()
    mock_session.query.return_value.filter.return_value.first.return_value = None
    mock_get_db.return_value = mock_session
    
    response = client.post(
        AUTH_LOGIN_URL,
        data={"username": "nonexistentuser", "password": "anypassword"}
    )
    
    # Assert response
    assert response.status_code == 401
    data = response.json()
    assert "detail" in data
    assert "Incorrect username or password" in data["detail"]

@patch("api.endpoints.auth.get_db")
def test_login_inactive_user(mock_get_db):
    """Test login with an inactive user account"""
    # Mock DB session and inactive user
    mock_session = MagicMock()
    mock_user = create_mock_user(test_inactive_user)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_user
    mock_get_db.return_value = mock_session
    
    # Mock password verification to succeed
    with patch("api.endpoints.auth.verify_password", return_value=True):
        response = client.post(
            AUTH_LOGIN_URL,
            data={"username": "inactiveuser", "password": "inactive123"}
        )
        
        # Assert response
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
        assert "User account is not active" in data["detail"]

@patch("api.endpoints.auth.get_current_active_user")
@patch("api.endpoints.auth.get_db")
def test_logout_success(mock_get_db, mock_current_user):
    """Test successful logout"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    response = client.post(AUTH_LOGOUT_URL, headers=headers)
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "Successfully logged out" in data["message"]

@patch("api.endpoints.auth.get_current_active_user")
def test_get_me(mock_current_user):
    """Test getting current user profile"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    response = client.get(AUTH_ME_URL, headers=headers)
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_user["id"]
    assert data["username"] == test_user["username"]
    assert data["email"] == test_user["email"]
    assert data["role"] == test_user["role"].value
    assert "password" not in data  # Password should not be in response

def test_get_me_no_token():
    """Test getting current user without authentication token"""
    response = client.get(AUTH_ME_URL)
    
    # Assert response
    assert response.status_code == 401
    data = response.json()
    assert "detail" in data
    assert "Not authenticated" in data["detail"]

def test_auth_expired_token():
    """Test using an expired token"""
    # Create an expired token
    expired_token = create_test_token(test_user, expiration_minutes=-30)  # negative minutes = expired
    headers = {"Authorization": f"Bearer {expired_token}"}
    
    # Try to access a protected endpoint
    with patch("api.endpoints.auth.jwt.decode", side_effect=jwt.ExpiredSignatureError("Token expired")):
        response = client.get(AUTH_ME_URL, headers=headers)
        
        # Assert response
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
        assert "Could not validate credentials" in data["detail"]

def test_auth_invalid_token():
    """Test using an invalid token"""
    # Use an invalid token format
    headers = {"Authorization": "Bearer invalid-token-format"}
    
    # Try to access a protected endpoint
    with patch("api.endpoints.auth.jwt.decode", side_effect=jwt.InvalidTokenError("Invalid token")):
        response = client.get(AUTH_ME_URL, headers=headers)
        
        # Assert response
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
        assert "Could not validate credentials" in data["detail"] 