"""
Direct test for authentication endpoints
"""
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, MagicMock
import pytest
import os
import sys

# Add the parent directory to sys.path to ensure imports work
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from api.app import app
from api.endpoints.auth import login
from shared.api_endpoints import API_PREFIX

# Create test client
client = TestClient(app)

# Test the login endpoint directly
def test_login_endpoint():
    # Test login URL
    login_url = f"{API_PREFIX}/auth/login"
    print(f"Login URL: {login_url}")
    
    # Try to login
    response = client.post(
        login_url,
        data={"username": "admin", "password": "admin"}
    )
    
    # Output the response for debugging
    print(f"Response status code: {response.status_code}")
    try:
        print(f"Response JSON: {response.json()}")
    except:
        print(f"Response content: {response.content}")
    
    # The test might fail, but we want to see the diagnostics
    assert True 