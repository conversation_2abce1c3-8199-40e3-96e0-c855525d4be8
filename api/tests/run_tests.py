#!/usr/bin/env python3
"""
Test runner script for API endpoint tests

This script runs all the API endpoint tests using pytest.
"""

import os
import sys
import pytest
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run all API endpoint tests"""
    logger.info("Starting API endpoint tests")
    
    # Get the directory containing this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Print test discovery information
    logger.info(f"Looking for tests in: {script_dir}")
    test_files = [f for f in os.listdir(script_dir) if f.startswith('test_') and f.endswith('.py')]
    logger.info(f"Found test files: {', '.join(test_files)}")
    
    # Run tests with pytest
    logger.info("Running tests...")
    args = [
        script_dir,  # Run tests from this directory
        "-v",        # Verbose output
        "--no-header",  # Don't show header
        "--no-summary"  # Don't show summary
    ]
    
    # Add any command line args
    args.extend(sys.argv[1:])
    
    # Run tests and get exit code
    exit_code = pytest.main(args)
    
    # Log result
    if exit_code == 0:
        logger.info("🎉 All tests passed!")
    else:
        logger.error(f"❌ Tests failed with exit code: {exit_code}")
    
    return exit_code

if __name__ == "__main__":
    sys.exit(main()) 