"""
Tests to verify the shared API endpoints configuration is working properly
"""
import os
import sys
import pytest

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.api_endpoints import API_VERSION, API_PREFIX, API_ENDPOINTS
from api.constants import API_VERSION as ORIGINAL_API_VERSION, API_PREFIX as ORIGINAL_API_PREFIX

def test_shared_api_endpoints_match_original():
    """Test that our shared API endpoints match the original ones"""
    assert API_VERSION == ORIGINAL_API_VERSION, "API versions should match"
    assert API_PREFIX == ORIGINAL_API_PREFIX, "API prefixes should match"
    
    # Print debug information
    print(f"Shared API version: {API_VERSION}")
    print(f"Original API version: {ORIGINAL_API_VERSION}")
    
    print(f"Shared API prefix: {API_PREFIX}")
    print(f"Original API prefix: {ORIGINAL_API_PREFIX}")
    
    # Print a sample of endpoints
    print(f"Sample auth endpoint: {API_ENDPOINTS['AUTH']['LOGIN']}")
    
    # These should be equal in value, not necessarily equal objects
    assert API_VERSION == ORIGINAL_API_VERSION
    assert API_PREFIX == ORIGINAL_API_PREFIX
    
    # Also verify that our endpoint constants contain critical endpoints
    assert 'AUTH' in API_ENDPOINTS, "AUTH section should be in API_ENDPOINTS"
    assert 'USERS' in API_ENDPOINTS, "USERS section should be in API_ENDPOINTS"
    assert 'CERTIFICATIONS' in API_ENDPOINTS, "CERTIFICATIONS section should be in API_ENDPOINTS"
    
    # Verify specific path patterns
    assert API_ENDPOINTS['AUTH']['LOGIN'].startswith(API_PREFIX)
    assert API_ENDPOINTS['AUTH']['LOGIN'].endswith('/auth/login') 