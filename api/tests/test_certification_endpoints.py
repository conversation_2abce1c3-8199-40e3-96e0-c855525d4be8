"""
Tests for certification explorer API endpoints
"""
import json
import pytest
from fastapi.testclient import TestClient
from unittest.mock import MagicMock, patch
from sqlalchemy.orm import Session
import sys
import os
from pathlib import Path

# Add necessary paths to Python path
app_dir = str(Path(__file__).parent.parent.parent)
if app_dir not in sys.path:
    sys.path.append(app_dir)

# Import app and constants
from api.app import app
from api.constants import API_PREFIX

# Import models
from models.certification_explorer import (
    CertificationExplorer, 
    CertificationType, 
    CertificationLevel,
    SecurityDomain
)

# Initialize the FastAPI test client
client = TestClient(app)

# Constants for testing
CERTIFICATIONS_URL = f"{API_PREFIX}/certifications"
RELATIONSHIPS_URL = f"{API_PREFIX}/certifications/relationships"

# Test data
test_certification = {
    "id": 1,
    "name": "Security+ Certification",
    "provider": "CompTIA",
    "description": "A foundational security certification",
    "type": CertificationType.SECURITY if hasattr(CertificationType, "SECURITY") else "security",
    "level": CertificationLevel.BEGINNER if hasattr(CertificationLevel, "BEGINNER") else "beginner",
    "domain": SecurityDomain.SECURITY_MANAGEMENT if hasattr(SecurityDomain, "SECURITY_MANAGEMENT") else "security_management",
    "exam_code": "SY0-601",
    "price": 349.0,
    "duration_minutes": 90,
    "passing_score": 75,
    "prerequisites": ["Network+"],
    "skills_covered": ["Network Security", "Compliance", "Cryptography"],
    "url": "https://example.com/securityplus",
    "created_at": "2023-03-25T00:00:00"
}

test_certifications = [
    test_certification,
    {
        "id": 2,
        "name": "Certified Ethical Hacker",
        "provider": "EC-Council",
        "description": "Advanced ethical hacking certification",
        "type": CertificationType.SECURITY if hasattr(CertificationType, "SECURITY") else "security",
        "level": CertificationLevel.INTERMEDIATE if hasattr(CertificationLevel, "INTERMEDIATE") else "intermediate",
        "domain": SecurityDomain.OFFENSIVE_SECURITY if hasattr(SecurityDomain, "OFFENSIVE_SECURITY") else "offensive_security",
        "exam_code": "312-50",
        "price": 950.0,
        "duration_minutes": 240,
        "passing_score": 70,
        "prerequisites": ["Security+"],
        "skills_covered": ["Ethical Hacking", "Penetration Testing", "Vulnerability Assessment"],
        "url": "https://example.com/ceh",
        "created_at": "2023-03-25T00:00:00"
    },
    {
        "id": 3,
        "name": "AWS Certified Solutions Architect",
        "provider": "Amazon",
        "description": "Cloud architecture certification",
        "type": CertificationType.CLOUD if hasattr(CertificationType, "CLOUD") else "cloud",
        "level": CertificationLevel.INTERMEDIATE if hasattr(CertificationLevel, "INTERMEDIATE") else "intermediate",
        "domain": SecurityDomain.SECURITY_ENGINEERING if hasattr(SecurityDomain, "SECURITY_ENGINEERING") else "security_engineering",
        "exam_code": "SAA-C03",
        "price": 150.0,
        "duration_minutes": 130,
        "passing_score": 72,
        "prerequisites": [],
        "skills_covered": ["Cloud Architecture", "AWS Services", "Security"],
        "url": "https://example.com/aws-sa",
        "created_at": "2023-03-25T00:00:00"
    }
]

def create_mock_certification(cert_data):
    """Helper function to create a mock certification object"""
    cert = MagicMock(spec=CertificationExplorer)
    for key, value in cert_data.items():
        setattr(cert, key, value)
    return cert


@patch("api.endpoints.certification_explorer.get_db")
def test_get_certifications(mock_get_db):
    """Test getting all certifications"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query  # For filtering
    mock_query.offset.return_value = mock_query  # For pagination
    mock_query.limit.return_value = mock_query   # For pagination
    
    # Set up mock certifications
    mock_certs = [create_mock_certification(cert) for cert in test_certifications]
    mock_query.all.return_value = mock_certs
    mock_query.count.return_value = len(mock_certs)
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API
    response = client.get(CERTIFICATIONS_URL)
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert "certifications" in data
    assert "total" in data
    assert "page" in data
    assert "page_size" in data
    assert data["total"] == len(test_certifications)
    assert len(data["certifications"]) == len(test_certifications)


@patch("api.endpoints.certification_explorer.get_db")
def test_get_certification_by_id(mock_get_db):
    """Test getting a certification by ID"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_cert = create_mock_certification(test_certification)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_cert
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API
    response = client.get(f"{CERTIFICATIONS_URL}/1")
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_certification["id"]
    assert data["name"] == test_certification["name"]
    assert data["provider"] == test_certification["provider"]
    assert data["type"] == test_certification["type"]
    assert data["level"] == test_certification["level"]


@patch("api.endpoints.certification_explorer.get_db")
def test_get_certification_not_found(mock_get_db):
    """Test getting a non-existent certification"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_session.query.return_value.filter.return_value.first.return_value = None
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API
    response = client.get(f"{CERTIFICATIONS_URL}/999")
    
    # Assert response
    assert response.status_code == 404
    assert "detail" in response.json()
    assert "not found" in response.json()["detail"].lower()


@patch("api.endpoints.certification_explorer.get_db")
def test_filter_certifications_by_provider(mock_get_db):
    """Test filtering certifications by provider"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query  # For filtering
    mock_query.offset.return_value = mock_query  # For pagination
    mock_query.limit.return_value = mock_query   # For pagination
    
    # Set up filtered result (only CompTIA certifications)
    filtered_certs = [create_mock_certification(test_certification)]  # Only CompTIA certification
    mock_query.all.return_value = filtered_certs
    mock_query.count.return_value = len(filtered_certs)
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API with provider filter
    response = client.get(f"{CERTIFICATIONS_URL}?provider=CompTIA")
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 1
    assert len(data["certifications"]) == 1
    assert data["certifications"][0]["provider"] == "CompTIA"


@patch("api.endpoints.certification_explorer.get_db")
def test_filter_certifications_by_level(mock_get_db):
    """Test filtering certifications by level"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query  # For filtering
    mock_query.offset.return_value = mock_query  # For pagination
    mock_query.limit.return_value = mock_query   # For pagination
    
    # Set up filtered result (only intermediate certifications)
    filtered_certs = [create_mock_certification(test_certifications[1])]  # Only intermediate
    mock_query.all.return_value = filtered_certs
    mock_query.count.return_value = len(filtered_certs)
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API with level filter
    response = client.get(f"{CERTIFICATIONS_URL}?level=intermediate")
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 1
    assert len(data["certifications"]) == 1
    assert data["certifications"][0]["level"] == "intermediate"


@patch("api.endpoints.certification_explorer.get_db")
def test_filter_certifications_by_type(mock_get_db):
    """Test filtering certifications by type"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query  # For filtering
    mock_query.offset.return_value = mock_query  # For pagination
    mock_query.limit.return_value = mock_query   # For pagination
    
    # Set up filtered result (only security certifications)
    filtered_certs = [create_mock_certification(cert) for cert in test_certifications]  # All are security in our test data
    mock_query.all.return_value = filtered_certs
    mock_query.count.return_value = len(filtered_certs)
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API with type filter
    response = client.get(f"{CERTIFICATIONS_URL}?type=security")
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 2
    assert len(data["certifications"]) == 2
    assert all(cert["type"] == "security" for cert in data["certifications"])


@patch("api.endpoints.certification_explorer.get_db")
def test_search_certifications(mock_get_db):
    """Test searching certifications"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query  # For filtering
    mock_query.offset.return_value = mock_query  # For pagination
    mock_query.limit.return_value = mock_query   # For pagination
    
    # Set up search result (only Security+ certification)
    search_results = [create_mock_certification(test_certification)]
    mock_query.all.return_value = search_results
    mock_query.count.return_value = len(search_results)
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API with search query
    response = client.get(f"{CERTIFICATIONS_URL}?search=Security%2B")
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 1
    assert len(data["certifications"]) == 1
    assert "Security+" in data["certifications"][0]["name"]


@patch("api.endpoints.certification_explorer.get_db")
def test_pagination(mock_get_db):
    """Test certification pagination"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query  # For filtering
    mock_query.offset.return_value = mock_query  # For pagination
    mock_query.limit.return_value = mock_query   # For pagination
    
    # Set up pagination result (only the first certification)
    page_result = [create_mock_certification(test_certification)]
    mock_query.all.return_value = page_result
    mock_query.count.return_value = len(test_certifications)  # Total count is still 2
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API with pagination
    response = client.get(f"{CERTIFICATIONS_URL}?skip=0&limit=1")
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 2  # Total is 2
    assert len(data["certifications"]) == 1  # But we only get 1 per page
    assert data["certifications"][0]["id"] == test_certification["id"]
    assert data["page"] == 1
    assert data["page_size"] == 1


@patch("api.endpoints.certification_explorer.get_db")
def test_create_certification(mock_get_db):
    """Test creating a new certification"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_session.query.return_value.filter.return_value.first.return_value = None  # No existing cert
    mock_db_cert = create_mock_certification(test_certification)
    
    # Mock add, commit and refresh
    def mock_add(obj):
        for key, value in test_certification.items():
            setattr(obj, key, value)
    
    mock_session.add.side_effect = mock_add
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API
    new_cert_data = {
        "name": "Security+ Certification",
        "provider": "CompTIA",
        "description": "A foundational security certification",
        "type": "security",
        "level": "beginner",
        "domain": "security_management",
        "exam_code": "SY0-601",
        "price": 349.0,
        "duration_minutes": 90,
        "passing_score": 75,
        "prerequisites": ["Network+"],
        "skills_covered": ["Network Security", "Compliance", "Cryptography"],
        "url": "https://example.com/securityplus"
    }
    
    response = client.post(
        CERTIFICATIONS_URL,
        json=new_cert_data
    )
    
    # Assert response
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == new_cert_data["name"]
    assert data["provider"] == new_cert_data["provider"]
    assert data["type"] == new_cert_data["type"]
    assert data["level"] == new_cert_data["level"]
    
    # Verify DB operations
    mock_session.add.assert_called_once()
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()


@patch("api.endpoints.certification_explorer.get_db")
def test_create_certification_duplicate(mock_get_db):
    """Test creating a certification that already exists"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_existing = create_mock_certification(test_certification)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_existing  # Existing cert
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API
    new_cert_data = {
        "name": "Security+ Certification",
        "provider": "CompTIA",
        "description": "A foundational security certification",
        "type": "security",
        "level": "beginner",
        "domain": "security_management",
        "exam_code": "SY0-601",
        "price": 349.0,
        "duration_minutes": 90,
        "passing_score": 75,
        "prerequisites": ["Network+"],
        "skills_covered": ["Network Security", "Compliance", "Cryptography"],
        "url": "https://example.com/securityplus"
    }
    
    response = client.post(
        CERTIFICATIONS_URL,
        json=new_cert_data
    )
    
    # Assert response
    assert response.status_code == 400
    assert "detail" in response.json()
    assert "already exists" in response.json()["detail"].lower()
    
    # Verify no DB operations occurred
    mock_session.add.assert_not_called()
    mock_session.commit.assert_not_called()


@patch("api.endpoints.certification_explorer.get_db")
def test_delete_certification(mock_get_db):
    """Test deleting a certification"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_cert = create_mock_certification(test_certification)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_cert
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API
    response = client.delete(f"{CERTIFICATIONS_URL}/1")
    
    # Assert response
    assert response.status_code == 204
    assert not response.content  # No content
    
    # Verify DB operations
    mock_session.delete.assert_called_once_with(mock_cert)
    mock_session.commit.assert_called_once()


@patch("api.endpoints.certification_explorer.get_db")
def test_delete_certification_not_found(mock_get_db):
    """Test deleting a non-existent certification"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_session.query.return_value.filter.return_value.first.return_value = None
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API
    response = client.delete(f"{CERTIFICATIONS_URL}/999")
    
    # Assert response
    assert response.status_code == 404
    assert "detail" in response.json()
    assert "not found" in response.json()["detail"].lower()
    
    # Verify no DB operations occurred
    mock_session.delete.assert_not_called()
    mock_session.commit.assert_not_called()


@patch("api.endpoints.certification_explorer.get_db")
def test_get_certification_providers(mock_get_db):
    """Test getting all certification providers"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_query = mock_session.query.return_value
    mock_query.distinct.return_value = mock_query
    mock_query.all.return_value = [("CompTIA",), ("EC-Council",), ("Amazon",)]
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API
    response = client.get(f"{CERTIFICATIONS_URL}/providers")
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) == 3
    assert "CompTIA" in data
    assert "EC-Council" in data
    assert "Amazon" in data


@patch("api.endpoints.certification_explorer.get_db")
def test_update_certification(mock_get_db):
    """Test updating a certification"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_cert = create_mock_certification(test_certification)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_cert
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test data
    update_data = {
        "name": "Updated Security+",
        "price": 399.0,
        "description": "Updated description"
    }
    
    # Test API
    response = client.put(f"{CERTIFICATIONS_URL}/1", json=update_data)
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == update_data["name"]
    assert data["price"] == update_data["price"]
    assert data["description"] == update_data["description"]


@patch("api.endpoints.certification_explorer.get_db")
def test_update_certification_not_found(mock_get_db):
    """Test updating a non-existent certification"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_session.query.return_value.filter.return_value.first.return_value = None
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test data
    update_data = {
        "name": "Updated Certification",
        "price": 399.0
    }
    
    # Test API
    response = client.put(f"{CERTIFICATIONS_URL}/999", json=update_data)
    
    # Assert response
    assert response.status_code == 404
    assert "not found" in response.json()["detail"].lower()


@patch("api.endpoints.certification_explorer.get_db")
def test_get_related_certifications(mock_get_db):
    """Test getting related certifications"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_cert = create_mock_certification(test_certification)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_cert
    
    # Set up related certifications
    related_certs = [
        create_mock_certification(test_certifications[1]),  # Same type (security)
        create_mock_certification(test_certifications[2])   # Different type
    ]
    mock_session.query.return_value.filter.return_value.limit.return_value.all.return_value = related_certs
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API
    response = client.get(f"{CERTIFICATIONS_URL}/related/1")
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) == 2


@patch("api.endpoints.certification_explorer.get_db")
def test_get_related_certifications_not_found(mock_get_db):
    """Test getting related certifications for non-existent certification"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_session.query.return_value.filter.return_value.first.return_value = None
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API
    response = client.get(f"{CERTIFICATIONS_URL}/related/999")
    
    # Assert response
    assert response.status_code == 404
    assert "not found" in response.json()["detail"].lower()


@patch("api.endpoints.certification_explorer.get_db")
def test_filter_certifications_by_price(mock_get_db):
    """Test filtering certifications by price range"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.offset.return_value = mock_query
    mock_query.limit.return_value = mock_query
    
    # Set up filtered result (only certifications within price range)
    filtered_certs = [create_mock_certification(test_certification)]  # Price: 349.0
    mock_query.all.return_value = filtered_certs
    mock_query.count.return_value = len(filtered_certs)
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API with price filters
    response = client.get(f"{CERTIFICATIONS_URL}?min_price=300&max_price=400")
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 1
    assert len(data["certifications"]) == 1
    assert data["certifications"][0]["price"] == 349.0


@patch("api.endpoints.certification_explorer.get_db")
def test_filter_certifications_by_domain(mock_get_db):
    """Test filtering certifications by security domain"""
    # Set up mock
    mock_session = MagicMock(spec=Session)
    mock_query = mock_session.query.return_value
    mock_query.filter.return_value = mock_query
    mock_query.offset.return_value = mock_query
    mock_query.limit.return_value = mock_query
    
    # Set up filtered result (only certifications in security_management domain)
    filtered_certs = [create_mock_certification(test_certification)]  # Domain: security_management
    mock_query.all.return_value = filtered_certs
    mock_query.count.return_value = len(filtered_certs)
    
    # Apply mock
    mock_get_db.return_value = mock_session
    
    # Test API with domain filter
    response = client.get(f"{CERTIFICATIONS_URL}?domain=security_management")
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 1
    assert len(data["certifications"]) == 1
    assert data["certifications"][0]["domain"] == "security_management" 