"""
Tests for API versioning functionality
"""

import unittest
from fastapi.testclient import Test<PERSON>lient
from api.app import app
from api.constants import API_VERSION, get_versioned_endpoint
from api.route_manager import RouteManager

class VersioningTest(unittest.TestCase):
    """Test API versioning functionality"""

    def setUp(self):
        """Set up test client"""
        self.client = TestClient(app)

    def test_get_versioned_endpoint(self):
        """Test get_versioned_endpoint utility function"""
        # Test with default version
        endpoint = get_versioned_endpoint("users/profile")
        self.assertEqual(endpoint, f"/api/{API_VERSION}/users/profile")

        # Test with explicit version
        endpoint = get_versioned_endpoint("users/profile", "v2")
        self.assertEqual(endpoint, "/api/v2/users/profile")

        # Test with leading slash
        endpoint = get_versioned_endpoint("/users/profile")
        self.assertEqual(endpoint, f"/api/{API_VERSION}/users/profile")

        # Test with multiple leading slashes
        endpoint = get_versioned_endpoint("///users/profile")
        self.assertEqual(endpoint, f"/api/{API_VERSION}/users/profile")

    def test_route_manager(self):
        """Test RouteManager functionality"""
        # Create a test route manager
        manager = RouteManager()
        
        # Test version is set correctly
        self.assertEqual(manager.version, API_VERSION)
        
        # Test prefix is set correctly
        self.assertEqual(manager.prefix, f"/api/{API_VERSION}")
        
        # Test get_endpoint method
        endpoint = manager.get_endpoint("users/profile")
        self.assertEqual(endpoint, f"/api/{API_VERSION}/users/profile")
        
        # Test get_endpoint with specific version
        endpoint = manager.get_endpoint("users/profile", "v2")
        self.assertEqual(endpoint, "/api/v2/users/profile")

    def test_root_endpoint(self):
        """Test root endpoint returns correct API version"""
        response = self.client.get("/")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["api_version"], API_VERSION)

    def test_health_endpoint(self):
        """Test health endpoint is accessible with versioning"""
        response = self.client.get(f"/api/{API_VERSION}/health")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["api_version"], API_VERSION)


if __name__ == "__main__":
    unittest.main() 