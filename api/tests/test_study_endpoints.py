"""
Tests for Study API Endpoints

These tests verify the functionality of the study-related endpoints:
- /api/v1/study/estimate
- /api/v1/study/progress
- /api/v1/study/roi
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta, date
import json

from api.app import app
from shared.api_endpoints import API_VERSION, API_PREFIX, API_ENDPOINTS
from models.user import User, UserRole, UserStatus
from models.certification import Certification, UserCertification

client = TestClient(app)

# Constants for testing
STUDY_ESTIMATE_URL = API_ENDPOINTS["STUDY"]["ESTIMATE"]
STUDY_PROGRESS_URL = API_ENDPOINTS["STUDY"]["PROGRESS"]
STUDY_ROI_URL = API_ENDPOINTS["STUDY"]["ROI"]
TEST_SECRET_KEY = "test-secret-key"

# Test data
test_user = {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "hashed_password",
    "full_name": "Test User",
    "role": UserRole.USER,
    "status": UserStatus.ACTIVE,
    "created_at": datetime.utcnow(),
    "updated_at": datetime.utcnow(),
    "last_login": None
}

test_certification = {
    "id": 1,
    "name": "Certified Security Tester",
    "code": "CST-001",
    "organization": "Test Org",
    "domain": "Security Testing",
    "level": "Intermediate",
    "description": "A certification for testing",
    "difficulty": 2,  # Intermediate
    "cost": 300.0,
    "custom_hours": None,
    "url": "https://example.com/cert",
    "created_at": datetime.utcnow(),
    "updated_at": datetime.utcnow(),
}

test_user_certification = {
    "id": 1,
    "user_id": 1,
    "certification_id": 1,
    "progress": 30,  # 30% progress
    "date_earned": None,
    "expiration_date": None,
    "target_date": date.today() + timedelta(days=90),
    "verification_url": None,
    "certificate_image_url": None,
    "study_notes": "Making good progress",
    "status": "in_progress",
    "created_at": datetime.utcnow(),
    "updated_at": datetime.utcnow(),
}

# Helper functions
def create_test_token(user_data, expiration_minutes=30):
    """Create a test JWT token for the given user data"""
    import jwt
    payload = {
        "sub": user_data["username"],
        "role": user_data["role"].value,
        "exp": datetime.utcnow() + timedelta(minutes=expiration_minutes)
    }
    return jwt.encode(payload, TEST_SECRET_KEY, algorithm="HS256")

def create_mock_user(user_data):
    """Create a mock User object from dictionary data"""
    user = MagicMock(spec=User)
    for key, value in user_data.items():
        setattr(user, key, value)
    return user

def create_mock_certification(cert_data):
    """Create a mock Certification object from dictionary data"""
    cert = MagicMock(spec=Certification)
    for key, value in cert_data.items():
        setattr(cert, key, value)
    return cert

def create_mock_user_certification(user_cert_data):
    """Create a mock UserCertification object from dictionary data"""
    user_cert = MagicMock(spec=UserCertification)
    for key, value in user_cert_data.items():
        setattr(user_cert, key, value)
    return user_cert

# Tests for study/estimate endpoint
@patch("api.endpoints.study.get_current_active_user")
@patch("api.endpoints.study.get_db")
def test_estimate_study_time_success(mock_get_db, mock_current_user):
    """Test successful study time estimation"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    mock_cert = create_mock_certification(test_certification)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_cert
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    # Request data
    request_data = {
        "certification_id": 1,
        "hours_per_week": 10,
        "experience_level": 2
    }
    
    response = client.post(STUDY_ESTIMATE_URL, headers=headers, json=request_data)
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "total_hours_required" in data
    assert "weeks_required" in data
    assert "weekly_schedule" in data
    assert "study_tips" in data
    
    # Check total hours and weeks calculation
    assert data["total_hours_required"] == 108  # 120 * 0.9 (experience factor for level 2)
    assert data["weeks_required"] == 10.8  # 108 / 10

@patch("api.endpoints.study.get_current_active_user")
@patch("api.endpoints.study.get_db")
def test_estimate_study_time_certification_not_found(mock_get_db, mock_current_user):
    """Test study time estimation with non-existent certification"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session to return no certification
    mock_session = MagicMock()
    mock_session.query.return_value.filter.return_value.first.return_value = None
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    # Request data
    request_data = {
        "certification_id": 999,
        "hours_per_week": 10
    }
    
    response = client.post(STUDY_ESTIMATE_URL, headers=headers, json=request_data)
    
    # Assert response
    assert response.status_code == 404
    data = response.json()
    assert "detail" in data
    assert "not found" in data["detail"]

@patch("api.endpoints.study.get_current_active_user")
@patch("api.endpoints.study.get_db")
def test_estimate_study_time_with_target_date(mock_get_db, mock_current_user):
    """Test study time estimation with target date"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    mock_cert = create_mock_certification(test_certification)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_cert
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    # Request data
    target_date = (datetime.now() + timedelta(days=90)).strftime("%Y-%m-%d")
    request_data = {
        "certification_id": 1,
        "hours_per_week": 10,
        "target_date": target_date
    }
    
    response = client.post(STUDY_ESTIMATE_URL, headers=headers, json=request_data)
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert data["target_completion_date"] == target_date

# Tests for study/progress endpoint
@patch("api.endpoints.study.get_current_active_user")
@patch("api.endpoints.study.get_db")
def test_get_study_progress(mock_get_db, mock_current_user):
    """Test getting study progress"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    mock_cert = create_mock_certification(test_certification)
    mock_user_cert = create_mock_user_certification(test_user_certification)
    # Mock joined query result
    mock_session.query.return_value.join.return_value.filter.return_value.all.return_value = [
        (mock_user_cert, mock_cert)
    ]
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    response = client.get(STUDY_PROGRESS_URL, headers=headers)
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "progress_entries" in data
    assert len(data["progress_entries"]) == 1
    
    # Check entry details
    entry = data["progress_entries"][0]
    assert entry["certification_id"] == test_certification["id"]
    assert entry["certification_name"] == test_certification["name"]
    assert entry["progress"] == test_user_certification["progress"]
    assert entry["study_notes"] == test_user_certification["study_notes"]

@patch("api.endpoints.study.get_current_active_user")
@patch("api.endpoints.study.get_db")
def test_update_study_progress(mock_get_db, mock_current_user):
    """Test updating study progress"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    mock_cert = create_mock_certification(test_certification)
    mock_user_cert = create_mock_user_certification(test_user_certification)
    mock_session.query.return_value.filter.return_value.first.side_effect = [
        mock_cert,  # First call gets the certification
        mock_user_cert  # Second call gets the user certification
    ]
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    # Update data
    update_data = {
        "certification_id": 1,
        "progress": 45,
        "study_notes": "Updated progress notes"
    }
    
    response = client.post(STUDY_PROGRESS_URL, headers=headers, json=update_data)
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "message" in data
    assert "updated successfully" in data["message"]
    
    # Check that mocks were called correctly
    assert mock_user_cert.progress == 45
    assert mock_user_cert.study_notes == "Updated progress notes"
    assert mock_session.commit.called

@patch("api.endpoints.study.get_current_active_user")
@patch("api.endpoints.study.get_db")
def test_update_study_progress_new_entry(mock_get_db, mock_current_user):
    """Test updating study progress when no entry exists (should create one)"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    mock_cert = create_mock_certification(test_certification)
    mock_session.query.return_value.filter.return_value.first.side_effect = [
        mock_cert,  # First call gets the certification
        None  # Second call finds no user certification
    ]
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    # Update data
    update_data = {
        "certification_id": 1,
        "progress": 10,
        "study_notes": "Just started"
    }
    
    response = client.post(STUDY_PROGRESS_URL, headers=headers, json=update_data)
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    
    # Check that a new entry was added
    assert mock_session.add.called

# Tests for study/roi endpoint
@patch("api.endpoints.study.get_current_active_user")
@patch("api.endpoints.study.get_db")
def test_calculate_roi(mock_get_db, mock_current_user):
    """Test ROI calculation"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session
    mock_session = MagicMock()
    mock_cert = create_mock_certification(test_certification)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_cert
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    response = client.get(f"{STUDY_ROI_URL}/1?current_salary=80000", headers=headers)
    
    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "percent_roi" in data
    assert "break_even_months" in data
    assert "five_year_value" in data
    assert "salary_potential" in data
    assert "investment_details" in data
    
    # Check salary calculations
    assert data["salary_potential"]["current_salary"] == 80000
    assert data["salary_potential"]["estimated_new_salary"] > 80000
    
    # Check investment details
    assert data["investment_details"]["exam_fee"] == test_certification["cost"]

@patch("api.endpoints.study.get_current_active_user")
@patch("api.endpoints.study.get_db")
def test_calculate_roi_certification_not_found(mock_get_db, mock_current_user):
    """Test ROI calculation with non-existent certification"""
    # Mock current user
    mock_user = create_mock_user(test_user)
    mock_current_user.return_value = mock_user
    
    # Mock DB session to return no certification
    mock_session = MagicMock()
    mock_session.query.return_value.filter.return_value.first.return_value = None
    mock_get_db.return_value = mock_session
    
    # Create auth header with token
    token = create_test_token(test_user)
    headers = {"Authorization": f"Bearer {token}"}
    
    response = client.get(f"{STUDY_ROI_URL}/999", headers=headers)
    
    # Assert response
    assert response.status_code == 404
    data = response.json()
    assert "detail" in data
    assert "not found" in data["detail"] 