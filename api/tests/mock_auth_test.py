"""
Mocked authentication tests
"""
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, MagicMock
import pytest
from datetime import datetime, timedelta
import jwt
import os
import sys

# Add the parent directory to sys.path to ensure imports work
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from api.app import app
from shared.api_endpoints import API_PREFIX, API_ENDPOINTS
from models.user import UserRole, UserStatus
from api.endpoints.auth import TokenResponse

# Client for testing
client = TestClient(app)

# Test secret key
TEST_SECRET_KEY = "test-secret-key"

# Test data
test_user = {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",  # Raw password would be hashed in real app
    "full_name": "Test User",
    "role": UserRole.USER,
    "status": UserStatus.ACTIVE,
    "created_at": datetime.utcnow(),
    "updated_at": datetime.utcnow(),
    "last_login": None
}

# URLs for testing
AUTH_LOGIN_URL = API_ENDPOINTS["AUTH"]["LOGIN"]

# Mocks the entire login function to return a known response
@patch("api.endpoints.auth.login")
def test_login_with_mocked_function(mock_login):
    # Create a mocked TokenResponse
    token_response = TokenResponse(
        access_token="test_token",
        token_type="bearer",
        expires_in=3600,
        user_id=test_user["id"],
        username=test_user["username"],
        role=test_user["role"].value
    )
    
    # Configure the mock to return our token response
    mock_login.return_value = token_response
    
    # Make the request
    response = client.post(
        AUTH_LOGIN_URL,
        data={"username": "testuser", "password": "password123"}
    )
    
    # Print debug info
    print(f"DEBUG: Login URL: {AUTH_LOGIN_URL}")
    print(f"DEBUG: Response status: {response.status_code}")
    print(f"DEBUG: Response body: {response.json() if response.status_code != 500 else 'Internal Server Error'}")
    
    # Assertions
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"
    assert data["user_id"] == test_user["id"]
    assert data["username"] == test_user["username"]
    assert data["role"] == test_user["role"].value 