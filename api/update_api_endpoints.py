#!/usr/bin/env python3
"""
Update API Endpoints

This script updates the API endpoint references in backend Python files to use
the shared API endpoints configuration. It looks for import references to the
old api/constants.py file and replaces them with imports from shared.api_endpoints.

Usage:
    python api/update_api_endpoints.py
"""
import os
import re
import sys

# Add parent directory to path to import from shared
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(parent_dir)

# Files to scan and update
API_DIRS = [
    "api",
    "api/endpoints",
    "api/tests"
]

# Patterns to look for and replace
PATTERNS = [
    (
        r"from api\.constants import API_VERSION, API_PREFIX",
        "from shared.api_endpoints import API_VERSION, API_PREFIX"
    ),
    (
        r"from api\.constants import\s*([\w, ]+API_ENDPOINTS[\w, ]*)",
        r"from shared.api_endpoints import \1"
    ),
    (
        r"import api\.constants",
        "import shared.api_endpoints as api_constants"
    ),
    (
        r"from api\.constants import\s*([\w, ]+)",
        r"from shared.api_endpoints import \1"
    )
]


def update_file(file_path):
    """Update imports in a single file"""
    print(f"Processing {file_path}...")
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check if the file already uses shared.api_endpoints
        if "from shared.api_endpoints import" in content:
            print(f"  Already updated, skipping")
            return False
        
        # Keep the original content for comparison
        original_content = content
        
        # Apply all patterns
        for pattern, replacement in PATTERNS:
            content = re.sub(pattern, replacement, content)
        
        # Write back if changed
        if content != original_content:
            with open(file_path, 'w') as f:
                f.write(content)
            print(f"  ✅ Updated imports")
            return True
        else:
            print(f"  No changes needed")
            return False
    
    except Exception as e:
        print(f"  ❌ Error processing file: {str(e)}")
        return False


def scan_directory(directory):
    """Scan a directory for Python files and update them"""
    abs_directory = os.path.join(parent_dir, directory)
    updated_files = 0
    
    if not os.path.exists(abs_directory):
        print(f"Directory {abs_directory} does not exist, skipping")
        return updated_files
    
    for filename in os.listdir(abs_directory):
        if filename.endswith(".py"):
            file_path = os.path.join(abs_directory, filename)
            if update_file(file_path):
                updated_files += 1
    
    return updated_files


def main():
    """Main function"""
    print("Updating API endpoint imports...")
    
    total_updated = 0
    for directory in API_DIRS:
        print(f"\nScanning directory: {directory}")
        updated = scan_directory(directory)
        total_updated += updated
    
    print(f"\n✅ Done! Updated {total_updated} files.")
    
    # Remind the user to run tests
    if total_updated > 0:
        print("\nPlease run tests to ensure all changes work correctly:")
        print("  pytest api/tests/")


if __name__ == "__main__":
    main() 