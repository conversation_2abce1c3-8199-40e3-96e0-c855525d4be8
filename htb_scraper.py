#!/usr/bin/env python3
"""
HackTheBox Machine Scraper
Test version with mock data for Docker testing
"""
import json
import time
import random
import psycopg2
from psycopg2.extras import Json
from datetime import datetime, timedelta
import os

# Database connection string
DB_URL = "***************************************************"

def generate_mock_data(count=20):
    """
    Generate mock HackTheBox machine data for testing
    """
    difficulties = ["Easy", "Medium", "Hard", "Insane"]
    os_types = ["Linux", "Windows", "FreeBSD", "OpenBSD", "Solaris"]
    matrix_axes = ["Enumeration", "Real-life", "Custom Applications", "CVE", "Skills"]
    
    machines = []
    
    for i in range(1, count + 1):
        machine_id = f"machine_{i}"
        name = f"Test Machine {i}"
        difficulty = random.choice(difficulties)
        os_type = random.choice(os_types)
        
        # Generate a date in the past 5 years
        days_ago = random.randint(1, 365 * 5)
        release_date = (datetime.now() - timedelta(days=days_ago)).strftime("%d/%m/%Y")
        
        # Generate matrix ratings
        matrix = {}
        for axis in matrix_axes:
            matrix[axis] = round(random.uniform(1, 10), 1)
            
        machine = {
            "id": machine_id,
            "name": name,
            "logo_url": f"https://www.hackthebox.com/images/machines/{machine_id}.png",
            "difficulty": difficulty,
            "os": os_type,
            "user_owns": random.randint(100, 10000),
            "system_owns": random.randint(50, 5000),
            "release_date": release_date,
            "rating": round(random.uniform(3, 5), 1),
            "matrix": matrix
        }
        
        machines.append(machine)
    
    return machines

def store_in_database(machines):
    """Store machine data in the database"""
    conn = None
    try:
        # Connect to database
        conn = psycopg2.connect(DB_URL)
        cursor = conn.cursor()
        
        # Create table if not exists
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS hackthebox_machines (
                id SERIAL PRIMARY KEY,
                machine_id VARCHAR(50) UNIQUE,
                name VARCHAR(100) NOT NULL,
                logo_url TEXT,
                difficulty VARCHAR(20),
                operating_system VARCHAR(50),
                user_owns INTEGER,
                system_owns INTEGER,
                release_date DATE,
                rating FLOAT,
                matrix JSONB,
                provider VARCHAR(50) DEFAULT 'HackTheBox',
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        """)
        
        # Create indexes if not exists
        cursor.execute("""
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM pg_indexes 
                    WHERE indexname = 'idx_htb_machine_name'
                ) THEN
                    CREATE INDEX idx_htb_machine_name ON hackthebox_machines(name);
                END IF;
                
                IF NOT EXISTS (
                    SELECT 1 FROM pg_indexes 
                    WHERE indexname = 'idx_htb_difficulty'
                ) THEN
                    CREATE INDEX idx_htb_difficulty ON hackthebox_machines(difficulty);
                END IF;
                
                IF NOT EXISTS (
                    SELECT 1 FROM pg_indexes 
                    WHERE indexname = 'idx_htb_os'
                ) THEN
                    CREATE INDEX idx_htb_os ON hackthebox_machines(operating_system);
                END IF;
            END
            $$;
        """)
        
        # Insert data
        for machine in machines:
            cursor.execute("""
                INSERT INTO hackthebox_machines 
                (machine_id, name, logo_url, difficulty, operating_system, 
                user_owns, system_owns, release_date, rating, matrix)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (machine_id) DO UPDATE SET
                name = EXCLUDED.name,
                logo_url = EXCLUDED.logo_url,
                difficulty = EXCLUDED.difficulty,
                operating_system = EXCLUDED.operating_system,
                user_owns = EXCLUDED.user_owns,
                system_owns = EXCLUDED.system_owns,
                release_date = EXCLUDED.release_date,
                rating = EXCLUDED.rating,
                matrix = EXCLUDED.matrix,
                updated_at = NOW()
            """, (
                machine.get('id'),
                machine.get('name'),
                machine.get('logo_url'),
                machine.get('difficulty'),
                machine.get('os'),
                machine.get('user_owns'),
                machine.get('system_owns'),
                datetime.strptime(machine.get('release_date'), '%d/%m/%Y').date() if machine.get('release_date') else None,
                machine.get('rating'),
                Json(machine.get('matrix', {}))
            ))
        
        conn.commit()
        print(f"Successfully stored {len(machines)} machines in the database")
        
        # Query and display some data
        cursor.execute("SELECT COUNT(*) FROM hackthebox_machines")
        total_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT difficulty, COUNT(*) FROM hackthebox_machines GROUP BY difficulty")
        difficulty_counts = cursor.fetchall()
        
        cursor.execute("SELECT operating_system, COUNT(*) FROM hackthebox_machines GROUP BY operating_system")
        os_counts = cursor.fetchall()
        
        cursor.execute("SELECT AVG(rating) FROM hackthebox_machines")
        avg_rating = cursor.fetchone()[0]
        
        print("\nDatabase Summary:")
        print(f"Total machines: {total_count}")
        print("\nDifficulty distribution:")
        for diff, count in difficulty_counts:
            print(f"  {diff}: {count}")
        
        print("\nOperating system distribution:")
        for os, count in os_counts:
            print(f"  {os}: {count}")
            
        print(f"\nAverage rating: {avg_rating:.2f}")
        
        # Sample of data
        cursor.execute("""
            SELECT name, difficulty, operating_system, user_owns, system_owns, rating, 
                   matrix->'Enumeration' as enum_score
            FROM hackthebox_machines
            ORDER BY rating DESC
            LIMIT 5
        """)
        top_machines = cursor.fetchall()
        
        print("\nTop rated machines:")
        for i, (name, diff, os, user_owns, sys_owns, rating, enum) in enumerate(top_machines):
            print(f"  {i+1}. {name} ({diff} {os}) - Rating: {rating}, User/System Owns: {user_owns}/{sys_owns}")
            print(f"     Enumeration score: {enum}")
        
    except Exception as e:
        print(f"Error storing data in database: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

def main():
    print("HackTheBox Machine Scraper (Mock Data Version)")
    print("----------------------------------------------")
    
    # Number of mock machines to generate
    num_machines = int(os.environ.get('HTB_MACHINE_COUNT', 20))
    
    print(f"Generating {num_machines} mock HackTheBox machines...")
    machines = generate_mock_data(num_machines)
    
    # Store data in database
    print("\nStoring machine data in database...")
    store_in_database(machines)
    
    # Also save as JSON
    output_file = 'htb_machines.json'
    with open(output_file, 'w') as f:
        json.dump(machines, f, indent=2)
    
    print(f"\nMock data saved to: {output_file}")
    print("\nProcess complete!")

if __name__ == "__main__":
    main() 