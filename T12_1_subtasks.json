{"id": "T12.1", "subtasks": [{"id": "T12.1.1", "title": "Create basic page layout with responsive design", "description": "Implement the basic layout structure for the certification page with proper responsive design for all device sizes.", "status": "pending"}, {"id": "T12.1.2", "title": "Implement certification listing with pagination", "description": "Create a component to display certifications in a list/grid view with pagination controls.", "status": "pending"}, {"id": "T12.1.3", "title": "Add search and filtering capabilities", "description": "Implement search functionality and filtering options by certification type, level, domain, and price range.", "status": "pending"}, {"id": "T12.1.4", "title": "Connect to existing API endpoints", "description": "Integrate with backend API endpoints to fetch certification data, including list, detail, and relationship endpoints.", "status": "pending"}, {"id": "T12.1.5", "title": "Implement basic error handling", "description": "Add error handling for API requests, data loading states, and edge cases like empty results.", "status": "pending"}]}