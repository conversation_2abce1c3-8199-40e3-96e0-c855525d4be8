#!/usr/bin/env python3
"""
Direct script to seed the certification_explorer table with data
"""
import sys
import os
import json
from typing import Dict, Any, List, Optional
import psycopg2
from psycopg2.extras import Json
from utils.roadmap_parser import parse_roadmap_html, extract_provider, extract_cert_type

# Database connection string
DB_URL = "***************************************************"

def map_certification_type(category: str) -> str:
    """Map category to certification type enum"""
    category = category.lower() if category else ""
    if "security" in category or "cyber" in category:
        return "SECURITY"
    elif "cloud" in category or "aws" in category or "azure" in category:
        return "CLOUD"
    elif "network" in category:
        return "NETWORKING"
    elif "development" in category or "developer" in category:
        return "DEVELOPMENT"
    elif "devops" in category:
        return "DEVOPS"
    else:
        return "OTHER"

def map_certification_level(level: Optional[str], difficulty: Optional[int]) -> str:
    """Map level string and difficulty to certification level enum"""
    # If we have a level string, use it
    if level:
        level = level.lower()
        if "beginner" in level or "entry" in level:
            return "BEGINNER"
        elif "intermediate" in level:
            return "INTERMEDIATE"
        elif "advanced" in level:
            return "ADVANCED"
        elif "expert" in level:
            return "EXPERT"
    
    # If no level string, use difficulty
    if difficulty is not None:
        if difficulty == 1:
            return "BEGINNER"
        elif difficulty == 2:
            return "INTERMEDIATE"
        elif difficulty == 3:
            return "ADVANCED"
        elif difficulty >= 4:
            return "EXPERT"
    
    # Default to beginner
    return "BEGINNER"

def map_security_domain(domain: Optional[str]) -> str:
    """Map domain to security domain enum"""
    if not domain:
        return "SECURITY_MANAGEMENT"
        
    domain = domain.lower()
    if "engineering" in domain:
        return "SECURITY_ENGINEERING"
    elif "defensive" in domain:
        return "DEFENSIVE_SECURITY"
    elif "management" in domain:
        return "SECURITY_MANAGEMENT"
    elif "network" in domain:
        return "NETWORK_SECURITY"
    elif "identity" in domain or "access" in domain:
        return "IDENTITY_ACCESS_MANAGEMENT"
    elif "asset" in domain:
        return "ASSET_SECURITY"
    elif "testing" in domain:
        return "SECURITY_TESTING"
    elif "offensive" in domain or "penetration" in domain:
        return "OFFENSIVE_SECURITY"
    else:
        return "SECURITY_MANAGEMENT"

def load_certification_data() -> List[Dict[str, Any]]:
    """Load certification data from JSON file"""
    print("Loading certification data from JSON file...")
    json_file = os.path.join('/app', 'data', 'normalized_certifications.json')
    
    if not os.path.exists(json_file):
        print(f"Error: JSON file not found at {json_file}")
        return []

    with open(json_file, 'r', encoding='utf-8') as f:
        certifications_list = json.load(f)

    print(f"Found {len(certifications_list)} total certifications to load")
    
    # Also parse the roadmap HTML to get additional data
    print("Parsing roadmap HTML data...")
    roadmap_certs = parse_roadmap_html()
    print(f"Found {len(roadmap_certs)} certifications in roadmap data")
    
    # Transform data to our format
    result = []
    for cert in certifications_list:
        name = cert['name']
        
        difficulty = cert.get('difficulty', 1)
        level = cert.get('level', '')
        domain = cert.get('domain', 'General Security')

        # Extract organization from name or description
        org_name = 'Unknown'
        if 'AWS' in name:
            org_name = 'Amazon Web Services'
        elif 'CompTIA' in name:
            org_name = 'CompTIA'
        elif 'Microsoft' in name or 'Azure' in name:
            org_name = 'Microsoft'
        elif 'Cisco' in name:
            org_name = 'Cisco Systems'
        elif 'EC-Council' in name:
            org_name = 'EC-Council'
        elif 'GIAC' in name:
            org_name = 'Global Information Assurance Certification'
        elif 'Oracle' in name:
            org_name = 'Oracle Corporation'
        elif 'ISC2' in name:
            org_name = 'International Information System Security Certification Consortium'
        elif 'ISACA' in name:
            org_name = 'Information Systems Audit and Control Association'
        
        # Try to get better provider detection from roadmap parser
        if org_name == 'Unknown':
            org_name = extract_provider(name)
        
        # Get certification type
        cert_type = cert.get('category', 'General Security')
        
        # Check if we have additional data from roadmap
        roadmap_cert = roadmap_certs.get(name)
        prerequisites = cert.get('prerequisites', [])
        
        if roadmap_cert:
            # Update with roadmap data if available
            if org_name == 'Unknown' and roadmap_cert.get('provider') != 'Unknown':
                org_name = roadmap_cert['provider']
            
            if not prerequisites and roadmap_cert.get('relationships', {}).get('prerequisites'):
                prerequisites = roadmap_cert['relationships']['prerequisites']

        result.append({
            'name': name,
            'provider': org_name,
            'description': cert.get('description', ''),
            'level': level,
            'difficulty': difficulty,
            'domain': domain,
            'category': cert.get('category', 'General Security'),
            'prerequisites': prerequisites,
            'price': cert.get('cost'),
            'url': cert.get('url')
        })

    print(f"Transformed {len(result)} certifications")
    return result

def create_tables(conn):
    """Create tables if they don't exist"""
    cursor = conn.cursor()
    
    # Check if the table exists
    cursor.execute("""
        SELECT EXISTS(
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = 'certification_explorer'
        )
    """)
    table_exists = cursor.fetchone()[0]
    
    if table_exists:
        print("Table certification_explorer already exists.")
        return
    
    # Create enum types
    cursor.execute("""
        DO $$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'certificationtypeenum') THEN
                CREATE TYPE certificationtypeenum AS ENUM (
                    'SECURITY', 'CLOUD', 'NETWORKING', 'DEVELOPMENT', 'DEVOPS', 'OTHER'
                );
            END IF;
            
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'certificationlevelenum') THEN
                CREATE TYPE certificationlevelenum AS ENUM (
                    'BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'
                );
            END IF;
            
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'securitydomainenum') THEN
                CREATE TYPE securitydomainenum AS ENUM (
                    'SECURITY_ENGINEERING', 'DEFENSIVE_SECURITY', 'SECURITY_MANAGEMENT',
                    'NETWORK_SECURITY', 'IDENTITY_ACCESS_MANAGEMENT', 'ASSET_SECURITY',
                    'SECURITY_TESTING', 'OFFENSIVE_SECURITY'
                );
            END IF;
        END
        $$;
    """)
    
    # Create certification_explorer table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS certification_explorer (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            provider VARCHAR(100) NOT NULL,
            description TEXT,
            type certificationtypeenum NOT NULL,
            level certificationlevelenum NOT NULL,
            domain securitydomainenum,
            exam_code VARCHAR(50),
            price FLOAT,
            duration_minutes INTEGER,
            passing_score INTEGER,
            prerequisites_list JSONB,
            skills_covered JSONB,
            url VARCHAR(255),
            created_at TIMESTAMP DEFAULT NOW(),
            updated_at TIMESTAMP DEFAULT NOW()
        );
    """)
    
    # Create indexes
    cursor.execute("""
        CREATE INDEX IF NOT EXISTS ix_certification_explorer_name ON certification_explorer (name);
        CREATE INDEX IF NOT EXISTS ix_certification_explorer_provider ON certification_explorer (provider);
    """)
    
    # Create prerequisites junction table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS certification_explorer_prerequisites (
            certification_id INTEGER REFERENCES certification_explorer(id),
            prerequisite_id INTEGER REFERENCES certification_explorer(id),
            PRIMARY KEY (certification_id, prerequisite_id)
        );
    """)
    
    conn.commit()
    print("Tables created successfully.")

def seed_certifications():
    """Seed certifications into the certification_explorer table"""
    try:
        # Connect to the database
        conn = psycopg2.connect(DB_URL)
        
        # Create tables if they don't exist
        create_tables(conn)
        
        cursor = conn.cursor()
        
        # Check if we already have data in the table
        cursor.execute("SELECT COUNT(*) FROM certification_explorer")
        count = cursor.fetchone()[0]
        if count > 0:
            print(f"Found {count} existing certifications. Clearing table for fresh data...")
            cursor.execute("TRUNCATE certification_explorer CASCADE")
            conn.commit()
        
        # Load certification data
        certifications_data = load_certification_data()
        success_count = 0
        error_count = 0
        
        # Insert data
        for cert_data in certifications_data:
            try:
                cursor.execute("""
                    INSERT INTO certification_explorer
                    (name, provider, description, type, level, domain, price, prerequisites_list, skills_covered, url)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                """, (
                    cert_data['name'],
                    cert_data['provider'],
                    cert_data['description'],
                    map_certification_type(cert_data['category']),
                    map_certification_level(cert_data['level'], cert_data['difficulty']),
                    map_security_domain(cert_data['domain']),
                    cert_data['price'],
                    Json(cert_data['prerequisites']) if cert_data['prerequisites'] else Json([]),
                    Json([]),
                    cert_data['url']
                ))
                
                success_count += 1
                
                # Commit every 100 records
                if success_count % 100 == 0:
                    conn.commit()
                    print(f"Added {success_count} certifications so far...")
                
            except Exception as e:
                print(f"Error adding certification {cert_data['name']}: {e}")
                error_count += 1
        
        # Final commit
        conn.commit()
        print(f"Successfully added {success_count} certifications with {error_count} errors")
        
        # Verify count
        cursor.execute("SELECT COUNT(*) FROM certification_explorer")
        final_count = cursor.fetchone()[0]
        print(f"Total certifications in explorer table: {final_count}")
        
    except Exception as e:
        print(f"Error seeding certifications: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    seed_certifications() 