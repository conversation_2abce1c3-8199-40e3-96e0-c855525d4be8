#!/usr/bin/env python3
"""
Legacy entry point that redirects users to the new React application.
This file is maintained for backward compatibility.
"""

import os
import sys
import webbrowser
from http.server import HTTPServer, SimpleHTTPRequestHandler
import http.server

class RedirectHandler(SimpleHTTPRequestHandler):
    def do_GET(self):
        # Redirect all traffic to the new React app
        self.send_response(302)
        self.send_header('Location', '/new')
        self.end_headers()

def main():
    """
    Display a message about the migration to React and redirect users
    to the new application.
    """
    print("=" * 80)
    print("MIGRATION NOTICE")
    print("=" * 80)
    print("The application has been migrated from Streamlit to React.")
    print("Opening the new React application in your default browser...")
    print("If the browser doesn't open automatically, please visit: http://localhost:3000/new")
    print("=" * 80)
    
    # Try to open the browser to the new React app
    try:
        webbrowser.open('http://localhost:3000/new')
    except Exception as e:
        print(f"Failed to open browser: {e}")
    
    # Start a simple redirect server on the old Streamlit port
    try:
        port = int(os.environ.get('PORT', 8501))  # Default Streamlit port
        server = HTTPServer(('', port), RedirectHandler)
        print(f"Starting redirect server on port {port}...")
        server.serve_forever()
    except KeyboardInterrupt:
        print("Server stopped by user.")
    except Exception as e:
        print(f"Failed to start redirect server: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())