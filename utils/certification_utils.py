"""Utility functions for certification management"""
from typing import List, Dict, Any, Sequence
from sqlalchemy.orm import Session
from database import get_db
from models.certification import Certification

def get_certifications(*args, **kwargs) -> Sequence[Dict[str, Any]]:
    """
    Get all active certifications from the database
    Returns them in a format suitable for visualization

    Returns:
        A sequence of certification dictionaries containing certification details
    """
    db = next(get_db())
    try:
        certifications = (db.query(Certification)
                        .filter(Certification.is_deleted == False)
                        .all())

        return [cert.to_dict() for cert in certifications]
    finally:
        db.close()