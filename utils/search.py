"""
Search utilities for certification filtering
"""
def filter_certifications(certifications, level=None, domain=None, difficulty=None, max_cost=None):
    """
    Filter certifications based on given criteria
    """
    filtered = certifications.copy()

    if level:
        filtered = {k: v for k, v in filtered.items() if v['level'] == level}

    if domain:
        filtered = {k: v for k, v in filtered.items() if v['domain'] == domain}

    if difficulty:
        filtered = {k: v for k, v in filtered.items() if v['difficulty'] == difficulty}

    if max_cost:
        filtered = {k: v for k, v in filtered.items() if v['cost'] <= max_cost}

    return filtered

def get_certification_path(cert_name, certifications):
    """
    Get suggested certification path based on domain and level progression
    """
    if cert_name not in certifications:
        return []

    current_cert = certifications[cert_name]
    current_domain = current_cert['domain']
    current_level = current_cert['level']

    # Define level progression
    level_order = {
        'Entry': 0,
        'Beginner': 0,
        'Intermediate': 1,
        'Advanced': 2
    }

    # Find certifications in the same domain with progressive levels
    same_domain_certs = [
        name for name, cert in certifications.items()
        if cert['domain'] == current_domain
    ]

    # Sort by level progression
    path = sorted(
        same_domain_certs,
        key=lambda x: level_order.get(certifications[x]['level'], 99)
    )

    return path