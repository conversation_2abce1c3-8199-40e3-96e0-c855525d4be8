"""Session management for OAuth state"""
import time
from typing import Dict, Optional, Any
import streamlit as st
import logging
import json

logger = logging.getLogger(__name__)

class OAuthSessionManager:
    """Manages OAuth session state with expiration"""
    
    def __init__(self, expiration_time: int = 600):  # 10 minutes default
        self.expiration_time = expiration_time
        if 'oauth_states' not in st.session_state:
            st.session_state.oauth_states = {}
            
    def store_state(self, provider: str, state: str) -> None:
        """Store OAuth state with expiration timestamp"""
        try:
            state_data = {
                'state': state,
                'expires_at': time.time() + self.expiration_time
            }
            st.session_state.oauth_states[provider] = state_data
            # Also store in URL params for cross-page persistence
            st.query_params[f'oauth_state_{provider}'] = state
            logger.info(f"Stored OAuth state for {provider}: {json.dumps(state_data)}")
        except Exception as e:
            logger.error(f"Error storing OAuth state: {str(e)}")
            raise

    def verify_state(self, provider: str, state: str) -> bool:
        """Verify if the provided state matches and hasn't expired"""
        try:
            stored_data = st.session_state.oauth_states.get(provider)
            url_state = st.query_params.get(f'oauth_state_{provider}')
            
            logger.info(f"Verifying state for {provider}")
            logger.info(f"Received state: {state}")
            logger.info(f"Stored data: {stored_data}")
            logger.info(f"URL state: {url_state}")
            
            if not stored_data and not url_state:
                logger.error("No stored state found")
                return False
                
            if stored_data:
                # Check expiration
                if time.time() > stored_data['expires_at']:
                    logger.error("Stored state has expired")
                    self.clear_state(provider)
                    return False
                    
                # Check state match
                if stored_data['state'] != state:
                    logger.error("State mismatch with session storage")
                    return False
                    
            # If we have a URL state, check that too
            if url_state and url_state != state:
                logger.error("State mismatch with URL parameters")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Error verifying OAuth state: {str(e)}")
            return False

    def clear_state(self, provider: str) -> None:
        """Clear OAuth state for the given provider"""
        try:
            if provider in st.session_state.oauth_states:
                del st.session_state.oauth_states[provider]
            if f'oauth_state_{provider}' in st.query_params:
                del st.query_params[f'oauth_state_{provider}']
            logger.info(f"Cleared OAuth state for {provider}")
        except Exception as e:
            logger.error(f"Error clearing OAuth state: {str(e)}")
            raise

    def cleanup_expired_states(self) -> None:
        """Remove all expired states"""
        try:
            current_time = time.time()
            expired_providers = [
                provider for provider, data in st.session_state.oauth_states.items()
                if current_time > data['expires_at']
            ]
            
            for provider in expired_providers:
                self.clear_state(provider)
                
            if expired_providers:
                logger.info(f"Cleaned up expired states for providers: {expired_providers}")
        except Exception as e:
            logger.error(f"Error cleaning up expired states: {str(e)}")
            raise
