"""
Utility for mapping security domains to job roles and related certifications.
"""
import logging
from typing import Dict, List, Any, Optional
from sqlalchemy.orm import Session
from models.certification import Certification
from models.job_role import JobRole

logger = logging.getLogger(__name__)

def get_domain_jobs(domain: str, db_session: Session) -> List[Dict[str, Any]]:
    """
    Get job roles for a specific security domain from the database.

    Args:
        domain: Security domain name
        db_session: Database session

    Returns:
        List of job role dictionaries with title, description, and responsibilities
    """
    try:
        jobs = (db_session.query(JobRole)
                .filter(JobRole.domain == domain)
                .all())
        return [job.to_dict() for job in jobs]
    except Exception as e:
        logger.error(f"Error fetching jobs for domain {domain}: {str(e)}")
        return []

def get_certification_job_matches(cert_domain: str, db_session: Session) -> Dict[str, Any]:
    """
    Get matching job roles and certifications for a security domain.

    Args:
        cert_domain: Security domain to match
        db_session: Database session

    Returns:
        Dictionary containing matched jobs and relevant certifications
    """
    try:
        # Get relevant certifications
        certs = (db_session.query(Certification)
                .filter(Certification.domain == cert_domain)
                .filter(Certification.is_active == True)
                .all())

        # Get matching jobs from database
        jobs = get_domain_jobs(cert_domain, db_session)

        return {
            "domain": cert_domain,
            "jobs": jobs,
            "certifications": [cert.to_dict() for cert in certs],
            "job_count": len(jobs),
            "cert_count": len(certs)
        }
    except Exception as e:
        logger.error(f"Error matching jobs to certifications for domain {cert_domain}: {str(e)}")
        return {
            "domain": cert_domain,
            "jobs": [],
            "certifications": [],
            "job_count": 0,
            "cert_count": 0
        }