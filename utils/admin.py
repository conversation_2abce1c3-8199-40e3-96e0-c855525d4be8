"""
Admin authentication utilities
"""
import os
import streamlit as st
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from anthropic import Anthropic
import logging

# Configure logging
logger = logging.getLogger(__name__)

def get_admin_password() -> str:
    """Get admin password from environment or secrets"""
    admin_password = os.environ.get("ADMIN_PASSWORD")
    if not admin_password:
        try:
            admin_password = st.secrets["ADMIN_PASSWORD"]
        except Exception:
            admin_password = "admin"  # Default for development
    return admin_password

def is_admin_authenticated() -> bool:
    """Check if admin is authenticated"""
    return bool(st.session_state.get("admin_authenticated"))

def admin_login(password: str) -> bool:
    """
    Authenticate admin and set session state
    Returns True if login successful
    """
    if password == get_admin_password():
        st.session_state.admin_authenticated = True
        return True
    return False

def admin_logout():
    """Log out admin by clearing session state"""
    if "admin_authenticated" in st.session_state:
        del st.session_state.admin_authenticated

def require_admin(func):
    """Decorator to require admin authentication"""
    def wrapper(*args, **kwargs):
        if not is_admin_authenticated():
            st.error("🔒 Admin access required. Please log in.")
            with st.form("login"):
                password = st.text_input("Enter admin password", type="password")
                if st.form_submit_button("🔑 Login"):
                    if admin_login(password):
                        st.rerun()
                    else:
                        st.error("❌ Invalid password")
            return
        return func(*args, **kwargs)
    return wrapper

def get_claude_usage_stats() -> Dict[str, Any]:
    """
    Get Claude API usage statistics.
    Returns a dictionary containing usage information.
    """
    try:
        api_key = os.environ.get("ANTHROPIC_API_KEY")
        if not api_key:
            try:
                api_key = st.secrets["ANTHROPIC_API_KEY"]
            except Exception:
                logger.error("Anthropic API key not found")
                return {
                    "error": "API key not configured",
                    "last_checked": datetime.now()
                }

        client = Anthropic(api_key=api_key)

        # Get current usage information
        # Note: Anthropic doesn't have a direct usage API yet, so we'll estimate
        # based on session state tracking
        if 'claude_usage' not in st.session_state:
            st.session_state.claude_usage = {
                'total_requests': 0,
                'total_tokens': 0,
                'last_request': None,
                'requests_today': 0,
                'first_request_today': None
            }

        usage = st.session_state.claude_usage
        current_time = datetime.now()

        # Reset daily counter if last request was yesterday
        if (usage['first_request_today'] and 
            usage['first_request_today'].date() < current_time.date()):
            usage['requests_today'] = 0
            usage['first_request_today'] = None

        return {
            "total_requests": usage['total_requests'],
            "total_tokens": usage['total_tokens'],
            "requests_today": usage['requests_today'],
            "last_request": usage['last_request'],
            "first_request_today": usage['first_request_today'],
            "last_checked": current_time,
            "status": "active" if client else "inactive"
        }

    except Exception as e:
        logger.error(f"Error fetching Claude usage stats: {str(e)}")
        return {
            "error": str(e),
            "last_checked": datetime.now()
        }

def update_claude_usage_stats(tokens_used: int = 0):
    """
    Update Claude usage statistics after each API call.
    """
    if 'claude_usage' not in st.session_state:
        st.session_state.claude_usage = {
            'total_requests': 0,
            'total_tokens': 0,
            'last_request': None,
            'requests_today': 0,
            'first_request_today': None
        }

    current_time = datetime.now()
    usage = st.session_state.claude_usage

    # Update counters
    usage['total_requests'] += 1
    usage['total_tokens'] += tokens_used
    usage['last_request'] = current_time

    # Update daily stats
    if not usage['first_request_today']:
        usage['first_request_today'] = current_time
    usage['requests_today'] += 1