"""
PDF report generation with Tufte-inspired design
"""
import os
import logging
from io import BytesIO
from typing import Dict, List, Optional, Union, Any
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import ParagraphStyle
from reportlab.lib import colors
from reportlab.platypus import Paragraph, SimpleDocTemplate, Spacer, Image, Flowable
from reportlab.lib.units import inch
from translations import _

logger = logging.getLogger(__name__)

def create_tufte_style():
    """Create Tufte-inspired paragraph styles for the PDF report."""
    styles = {}

    # Main heading style
    styles['heading'] = ParagraphStyle(
        'heading',
        fontSize=14,
        leading=16,
        spaceBefore=24,
        spaceAfter=12,
        textColor=colors.black,
        fontName='Helvetica-Bold'
    )

    # Subheading style
    styles['subheading'] = ParagraphStyle(
        'subheading',
        fontSize=12,
        leading=14,
        spaceBefore=12,
        spaceAfter=6,
        textColor=colors.black,
        fontName='Helvetica-Bold'
    )

    # Body text style
    styles['body'] = ParagraphStyle(
        'body',
        fontSize=10,
        leading=12,
        spaceBefore=6,
        spaceAfter=6,
        textColor=colors.black,
        fontName='Helvetica'
    )

    # Sidebar note style
    styles['sidebar'] = ParagraphStyle(
        'sidebar',
        fontSize=9,
        leading=11,
        leftIndent=36,
        spaceBefore=3,
        spaceAfter=3,
        textColor=colors.darkgrey,
        fontName='Helvetica-Oblique'
    )

    return styles

class CareerPathTimeline(Flowable):
    """A custom flowable for displaying the career path timeline."""
    def __init__(self, career_path):
        Flowable.__init__(self)
        self.career_path = career_path
        self.width = 400
        self.height = 100

    def draw(self):
        """Draw the timeline visualization."""
        self.canv.setStrokeColor(colors.grey)
        self.canv.setFillColor(colors.black)

        # Draw timeline
        y = self.height/2
        self.canv.line(50, y, self.width-50, y)

        # Draw stages
        num_stages = len(self.career_path)
        if num_stages > 0:
            spacing = (self.width-100)/(num_stages-1) if num_stages > 1 else 0
            for i, stage in enumerate(self.career_path):
                x = 50 + (i * spacing)
                # Draw circle
                self.canv.circle(x, y, 5, fill=1)
                # Draw stage name
                self.canv.setFont('Helvetica', 8)
                self.canv.drawCentredString(x, y+15, f"Stage {i+1}")
                stage_name = stage.get('stage', '')
                if stage_name:
                    # Truncate long stage names
                    if len(stage_name) > 20:
                        stage_name = stage_name[:17] + '...'
                    self.canv.drawCentredString(x, y-20, stage_name)

def generate_career_path_report(
    user_exp: Dict[str, Any],
    learning_path: Dict[str, Any],
    recommendations: Dict[str, Any],
    chart_fig: Optional[Any] = None
) -> BytesIO:
    """
    Generate a Tufte-style PDF report for the career path.
    """
    try:
        logger.info("Starting PDF report generation")

        buffer = BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=letter,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )

        styles = create_tufte_style()
        story = []

        # Add career path timeline visualization if available
        career_path = recommendations.get('career_path', [])
        total_cost = 0
        if career_path:
            story.append(Paragraph("Career Path Timeline", styles['subheading']))
            story.append(CareerPathTimeline(career_path))
            story.append(Spacer(1, 0.3*inch))

            # Career Stages
            story.append(Paragraph("Career Progression Stages", styles['heading']))
            for i, stage in enumerate(career_path, 1):
                # Stage header
                stage_name = stage.get('stage', f'Stage {i}')
                story.append(Paragraph(
                    f"Stage {i}: {stage_name}",
                    styles['subheading']
                ))

                # Timeline as sidebar note
                timeline = stage.get('timeline', 'Timeline not specified')
                story.append(Paragraph(
                    f"Timeline: {timeline}",
                    styles['sidebar']
                ))

                # Basic certification list without costs
                certs = stage.get('certifications', [])
                if certs:
                    cert_text = "Recommended Certifications:<br/>" + "<br/>".join(
                        f"• {cert}" for cert in certs
                    )
                    story.append(Paragraph(cert_text, styles['body']))

                # Skills
                skills = stage.get('skills_gained', [])
                if skills:
                    skills_text = "Skills to be Gained:<br/>" + "<br/>".join(
                        f"• {skill}" for skill in skills
                    )
                    story.append(Paragraph(skills_text, styles['body']))

                # Next steps as sidebar note
                next_steps = stage.get('next_steps', 'Continue to next stage')
                story.append(Paragraph(
                    f"Next Steps: {next_steps}",
                    styles['sidebar']
                ))
                story.append(Spacer(1, 0.2*inch))

            # Add detailed cost breakdown section
            story.append(Paragraph(_("Certification Cost Breakdown"), styles['heading']))
            story.append(Spacer(1, 0.2*inch))

            for i, stage in enumerate(career_path, 1):
                stage_name = stage.get('stage', f'Stage {i}')
                stage_total = 0

                story.append(Paragraph(
                    f"{_('Stage')} {i}: {stage_name}",
                    styles['subheading']
                ))

                certs = stage.get('certifications', [])
                cert_costs = stage.get('certification_costs', {})

                if certs:
                    for cert in certs:
                        cost_data = cert_costs.get(cert, {})
                        if isinstance(cost_data, (int, float)):
                            # Handle legacy format where cost is just a number
                            exam_cost = cost_data
                            material_cost = 0
                            training_cost = 0
                        else:
                            # New format with cost breakdown
                            exam_cost = cost_data.get('exam', 0)
                            material_cost = cost_data.get('materials', 0)
                            training_cost = cost_data.get('training', 0)

                        cert_total = exam_cost + material_cost + training_cost
                        stage_total += cert_total

                        story.append(Paragraph(
                            f"<b>{cert}</b>",
                            styles['body']
                        ))
                        story.append(Paragraph(
                            f"• {_('Exam Cost')}: ${exam_cost:,.2f}<br/>"
                            f"• {_('Training Cost')}: ${training_cost:,.2f}<br/>"
                            f"• {_('Study Materials')}: ${material_cost:,.2f}<br/>"
                            f"<b>{_('Certificate Total')}: ${cert_total:,.2f}</b>",
                            styles['sidebar']
                        ))

                total_cost += stage_total
                story.append(Paragraph(
                    f"<b>{_('Stage')} {i} {_('Total')}: ${stage_total:,.2f}</b>",
                    styles['subheading']
                ))
                story.append(Spacer(1, 0.3*inch))

            # Add final total cost summary
            story.append(Paragraph(_("Total Investment Summary"), styles['heading']))
            story.append(Paragraph(
                f"{_('Total Investment Required')}: ${total_cost:,.2f}",
                styles['subheading']
            ))
            story.append(Spacer(1, 0.3*inch))

        doc.build(story)
        buffer.seek(0)
        logger.info("PDF generation completed successfully")
        return buffer

    except Exception as e:
        logger.error(f"Error generating PDF report: {str(e)}", exc_info=True)
        raise  # Re-raise the exception after logging