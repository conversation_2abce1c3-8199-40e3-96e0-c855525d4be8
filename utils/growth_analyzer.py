"""
Analyze certification progression and growth trajectory based on user profile data.
Includes tools for ROI calculation and study planning.
"""
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
import logging
from sqlalchemy.orm import Session
from models.user_experience import UserExperience
from models.certification import Certification

logger = logging.getLogger(__name__)

class GrowthTrajectoryAnalyzer:
    """Analyze certification progression and growth trajectory based on user profile data."""
    
    def __init__(self, user_id: str, db_session: Session):
        self.user_id = user_id
        self.db = db_session
        self.profile = self._load_profile()
        
    def _load_profile(self) -> Optional[Dict[str, Any]]:
        """Load user profile with appropriate error handling."""
        try:
            user_exp = self.db.query(UserExperience).filter_by(user_id=self.user_id).first()
            return user_exp.to_dict() if user_exp else None
        except Exception as e:
            logger.error(f"Failed to load profile for user {self.user_id}: {str(e)}")
            return None
            
    def analyze_trajectory(self) -> Dict[str, Any]:
        """Calculate growth trajectory based on completed and planned certifications."""
        if not self.profile:
            return {
                "status": "error",
                "message": "User profile not found"
            }
            
        try:
            completed_certs = self.db.query(Certification).filter(
                Certification.id.in_(self.profile.get('completed_certifications', []))
            ).all()
            
            planned_certs = self.db.query(Certification).filter(
                Certification.id.in_(self.profile.get('planned_certifications', []))
            ).all()
            
            trajectory = {
                "status": "success",
                "current_level": self._calculate_current_level(completed_certs),
                "skill_gaps": self._identify_skill_gaps(completed_certs, planned_certs),
                "recommended_next_steps": self._get_recommendations(completed_certs),
                "completion_timeline": self._estimate_completion_timeline(planned_certs)
            }
            
            return trajectory
            
        except Exception as e:
            logger.error(f"Failed to analyze trajectory: {str(e)}")
            return {
                "status": "error",
                "message": "Failed to analyze growth trajectory"
            }
            
    def _calculate_current_level(self, completed_certs: List[Certification]) -> str:
        """Calculate current expertise level based on completed certifications."""
        if not completed_certs:
            return "Entry Level"
            
        difficulty_scores = [cert.difficulty for cert in completed_certs]
        avg_difficulty = sum(difficulty_scores) / len(difficulty_scores)
        
        if avg_difficulty >= 3.5:
            return "Expert"
        elif avg_difficulty >= 2.5:
            return "Advanced"
        elif avg_difficulty >= 1.5:
            return "Intermediate"
        return "Entry Level"
        
    def _identify_skill_gaps(
        self,
        completed_certs: List[Certification],
        planned_certs: List[Certification]
    ) -> List[Dict[str, Any]]:
        """Identify skill gaps based on certifications."""
        completed_domains = {cert.domain for cert in completed_certs}
        gaps = []
        
        for cert in planned_certs:
            if cert.domain not in completed_domains:
                gaps.append({
                    "domain": cert.domain,
                    "recommended_cert": cert.name,
                    "difficulty": cert.difficulty
                })
                
        return gaps
        
    def _get_recommendations(self, completed_certs: List[Certification]) -> List[Dict[str, Any]]:
        """Get personalized certification recommendations."""
        current_domains = {cert.domain for cert in completed_certs}
        recommendations = []
        
        try:
            # Query certifications in related domains
            related_certs = self.db.query(Certification).filter(
                Certification.domain.in_(current_domains)
            ).all()
            
            for cert in related_certs:
                if cert not in completed_certs:
                    recommendations.append({
                        "name": cert.name,
                        "domain": cert.domain,
                        "difficulty": cert.difficulty,
                        "rationale": f"Builds upon your expertise in {cert.domain}"
                    })
            
            return sorted(recommendations, key=lambda x: x['difficulty'])[:3]
            
        except Exception as e:
            logger.error(f"Failed to get recommendations: {str(e)}")
            return []
            
    def _estimate_completion_timeline(self, planned_certs: List[Certification]) -> List[Dict[str, Any]]:
        """Estimate completion timeline for planned certifications."""
        if not self.profile or not planned_certs:
            return []
            
        study_hours_per_week = self.profile.get('study_time_available', 10)
        timeline = []
        
        cumulative_weeks = 0
        for cert in planned_certs:
            # Estimate weeks based on difficulty and available study time
            estimated_total_hours = cert.difficulty * 60  # Base hours per difficulty level
            weeks_needed = round(estimated_total_hours / study_hours_per_week, 1)
            
            timeline.append({
                "certification": cert.name,
                "start_week": cumulative_weeks,
                "duration_weeks": weeks_needed,
                "estimated_completion_date": (
                    datetime.now() + timedelta(weeks=cumulative_weeks + weeks_needed)
                ).strftime("%Y-%m-%d")
            })
            
            cumulative_weeks += weeks_needed
            
        return timeline

def calculate_certification_roi(
    certification_id: int,
    current_salary: float,
    region: str = "US",
    timeframe_years: int = 3,
    db_session: Session = None
) -> Dict[str, Union[float, str]]:
    """
    Calculate potential ROI for a certification investment.
    
    Args:
        certification_id: Database ID of certification
        current_salary: User's current annual salary
        region: Geographic region for salary data
        timeframe_years: ROI calculation timeframe
        db_session: Database session
        
    Returns:
        Dictionary containing ROI metrics
    """
    try:
        if not db_session:
            return {
                "status": "error",
                "message": "Database session required"
            }
            
        cert = db_session.query(Certification).get(certification_id)
        if not cert:
            return {
                "status": "error",
                "message": "Certification not found"
            }
            
        # Calculate costs
        certification_cost = float(cert.cost or 0)
        study_materials_cost = 500  # Estimated cost for study materials
        total_investment = certification_cost + study_materials_cost
        
        # Estimate salary impact based on certification level
        salary_increase_factors = {
            "Entry Level": 0.05,
            "Intermediate": 0.10,
            "Advanced": 0.15,
            "Expert": 0.20
        }
        
        annual_increase = current_salary * salary_increase_factors.get(cert.level, 0.05)
        total_return = annual_increase * timeframe_years
        
        # Calculate ROI metrics
        percent_roi = ((total_return - total_investment) / total_investment) * 100
        break_even_months = (total_investment / annual_increase) * 12
        five_year_value = annual_increase * 5
        
        return {
            "status": "success",
            "certification_name": cert.name,
            "total_investment": total_investment,
            "annual_salary_impact": annual_increase,
            "percent_roi": round(percent_roi, 2),
            "break_even_months": round(break_even_months, 1),
            "five_year_value": round(five_year_value, 2)
        }
        
    except Exception as e:
        logger.error(f"Failed to calculate ROI: {str(e)}")
        return {
            "status": "error",
            "message": "Failed to calculate certification ROI"
        }

class StudyPlanner:
    """Interactive study planner with scheduling and progress tracking."""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        
    def generate_study_schedule(
        self,
        certification_id: int,
        target_date: Optional[datetime] = None,
        hours_per_week: int = 10,
        preferred_times: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """Generate personalized study schedule based on certification requirements."""
        try:
            cert = self.db.query(Certification).get(certification_id)
            if not cert:
                return {
                    "status": "error",
                    "message": "Certification not found"
                }
                
            # Calculate total study hours needed
            base_hours = cert.difficulty * 60  # Base hours per difficulty level
            
            # If target date specified, adjust study intensity
            if target_date:
                weeks_available = (target_date - datetime.now()).days / 7
                required_weekly_hours = base_hours / weeks_available
                if required_weekly_hours > hours_per_week:
                    return {
                        "status": "warning",
                        "message": f"Need {required_weekly_hours:.1f} hours/week to meet target date",
                        "suggested_target": (
                            datetime.now() + timedelta(weeks=base_hours/hours_per_week)
                        ).strftime("%Y-%m-%d")
                    }
            
            # Generate weekly schedule
            schedule = []
            remaining_hours = base_hours
            week_counter = 1
            
            while remaining_hours > 0:
                weekly_plan = {
                    "week": week_counter,
                    "hours_planned": min(hours_per_week, remaining_hours),
                    "focus_areas": self._get_weekly_focus_areas(cert, week_counter),
                    "milestones": self._get_weekly_milestones(cert, week_counter)
                }
                
                schedule.append(weekly_plan)
                remaining_hours -= hours_per_week
                week_counter += 1
            
            return {
                "status": "success",
                "certification_name": cert.name,
                "total_hours_required": base_hours,
                "weeks_required": len(schedule),
                "weekly_schedule": schedule,
                "study_tips": self._generate_study_tips(cert)
            }
            
        except Exception as e:
            logger.error(f"Failed to generate study schedule: {str(e)}")
            return {
                "status": "error",
                "message": "Failed to generate study schedule"
            }
            
    def _get_weekly_focus_areas(
        self,
        cert: Certification,
        week: int
    ) -> List[Dict[str, Any]]:
        """Generate focus areas for each week based on certification content."""
        # This would ideally be based on detailed certification curriculum data
        # For now, return placeholder data
        return [{
            "topic": f"Module {week}",
            "hours_suggested": 4,
            "resources": ["Official documentation", "Practice labs"]
        }]
        
    def _get_weekly_milestones(
        self,
        cert: Certification,
        week: int
    ) -> List[Dict[str, Any]]:
        """Generate weekly milestones and progress checkpoints."""
        return [{
            "description": f"Complete Module {week} assessment",
            "type": "Quiz",
            "target_date": (
                datetime.now() + timedelta(weeks=week)
            ).strftime("%Y-%m-%d")
        }]
        
    def _generate_study_tips(self, cert: Certification) -> List[str]:
        """Generate certification-specific study tips."""
        return [
            "Break study sessions into 25-minute focused intervals",
            "Review material regularly to reinforce learning",
            "Take practice exams in the final weeks",
            "Join study groups or online communities",
            f"Focus on hands-on experience for {cert.domain} domain"
        ]
