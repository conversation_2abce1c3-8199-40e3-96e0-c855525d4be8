"""Utility functions for job management with secure database queries"""
from sqlalchemy import or_, text
from models.job import <PERSON><PERSON><PERSON>
from typing import <PERSON>ple, List
from sqlalchemy.orm import Session

def get_filtered_jobs(
    db: Session,
    search_term: str = "",
    domain: str = "All",
    page: int = 0,
    per_page: int = 10
) -> <PERSON><PERSON>[List[SecurityJob], int]:
    """
    Get filtered and paginated jobs using parameterized queries

    All database interactions use SQLAlchemy's query API with proper parameter binding
    to prevent SQL injection. String operations use bind parameters instead of 
    string concatenation.

    Args:
        db: Database session
        search_term: Search term for job title/domain (automatically escaped)
        domain: Domain filter (parameterized)
        page: Page number (0-based)
        per_page: Items per page

    Returns:
        Tuple of (jobs list, total count)
    """
    # Start with a parameterized base query (automatically handles escaping)
    query = db.query(SecurityJob).filter(SecurityJob.deleted_at.is_(None))

    if search_term:
        # Use bind parameters for LIKE patterns
        pattern = f"%{search_term}%"  # Pattern is bound as a parameter
        query = query.filter(
            or_(
                SecurityJob.title.ilike(text(":pattern")),
                SecurityJob.domain.ilike(text(":pattern"))
            )
        ).params(pattern=pattern)

    if domain != "All":
        # Domain filtering uses automatic parameter binding
        query = query.filter(SecurityJob.domain == domain)

    # Get total count with the same filters
    total = query.count()

    # Pagination uses parameter binding for offset/limit
    jobs = (query.order_by(SecurityJob.title)
            .offset(page * per_page)
            .limit(per_page)
            .all())

    return jobs, total