"""
Parser for the Security Certification Roadmap HTML data
"""
import os
from bs4 import BeautifulSoup, Tag
import re
import logging
from typing import Dict, Any, Optional, List, Union

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_provider(cert_name: str) -> str:
    """Extract provider from certification name"""
    # Common certification providers
    providers_map = {
        'CompTIA': ['CompTIA', 'Security+', 'Network+', 'A+', 'CySA+', 'PenTest+', 'CASP+'],
        'Cisco Systems': ['Cisco', 'CCNA', 'CCNP', 'CCIE'],
        'Microsoft': ['Microsoft', 'Azure', 'MCSE', 'MCSA', 'MS-', 'AZ-'],
        'Amazon Web Services': ['AWS', 'Amazon'],
        'EC-Council': ['EC-Council', 'CEH', 'ECSA', 'CHFI'],
        'ISC²': ['ISC2', 'CISSP', 'SSCP', 'CAP', 'CSSLP'],
        'ISACA': ['ISACA', 'CISA', 'CISM', 'CRISC', 'CGEIT'],
        'Google': ['Google', 'GCP'],
        'Oracle': ['Oracle', 'OCA', 'OCP'],
        'Linux Foundation': ['Linux', 'LFCS', 'LFCE']
    }
    
    for provider, keywords in providers_map.items():
        for keyword in keywords:
            if keyword in cert_name:
                return provider
                
    return 'Unknown'

def extract_cert_type(cert_name: str, domain: str) -> str:
    """Extract certification type based on name and domain"""
    cert_name_lower = cert_name.lower()
    domain_lower = domain.lower() if domain else ""
    
    # Type detection
    if any(keyword in cert_name_lower or keyword in domain_lower 
           for keyword in ['security', 'cyber', 'hack', 'defense', 'protection', 'pentest']):
        return 'SECURITY'
    elif any(keyword in cert_name_lower or keyword in domain_lower
             for keyword in ['cloud', 'aws', 'azure', 'gcp']):
        return 'CLOUD'
    elif any(keyword in cert_name_lower or keyword in domain_lower
             for keyword in ['network', 'cisco', 'ccna', 'routing']):
        return 'NETWORKING'
    elif any(keyword in cert_name_lower or keyword in domain_lower
             for keyword in ['develop', 'programming', 'code']):
        return 'DEVELOPMENT'
    elif any(keyword in cert_name_lower or keyword in domain_lower
             for keyword in ['devops', 'pipelines', 'continuous']):
        return 'DEVOPS'
    else:
        return 'OTHER'

def parse_roadmap_html() -> Dict[str, Any]:
    """
    Parse the Security Certification Roadmap HTML to extract certification relationships
    and additional metadata
    """
    roadmap_path = os.path.join('submodules', 'SecCertRoadmapHTML', 'Security-Certification-Roadmap9.html')

    try:
        with open(roadmap_path, 'r', encoding='utf-8') as f:
            soup = BeautifulSoup(f.read(), 'html.parser')
    except FileNotFoundError:
        logger.error(f"Roadmap file not found at {roadmap_path}")
        return {}
    except Exception as e:
        logger.error(f"Error reading roadmap file: {e}")
        return {}

    certifications: Dict[str, Any] = {}

    # Find all certification elements
    cert_patterns = [
        'redopsitem', 'networkitem', 'mgmtitem', 'engineeritem',
        'blueopsitem', 'testitem', 'iamitem', 'assetitem'
    ]

    for pattern in cert_patterns:
        elements = soup.find_all('div', class_=lambda x: x and isinstance(x, str) and pattern in x.lower())

        for element in elements:
            # Extract basic information
            title = element.get('title', '')
            if not title:
                continue

            # Clean up the certification name
            name = re.sub(r'\s+', ' ', title).strip()

            # Extract difficulty level from class
            classes = element.get('class', [])
            difficulty: Optional[int] = None
            domain: Optional[str] = None
            level: Optional[str] = None

            for cls in classes:
                if isinstance(cls, str):
                    if cls.endswith(('1', '2', '3', '4')):
                        difficulty = int(cls[-1])
                    elif any(pattern in cls.lower() for pattern in cert_patterns):
                        domain_raw = cls.replace('item', '').replace('1', '').replace('2', '').replace('3', '').replace('4', '')
                        # Map raw domain to proper domain name
                        domain_mapping = {
                            'redops': 'Offensive Security',
                            'network': 'Network Security',
                            'mgmt': 'Security Management',
                            'engineer': 'Security Engineering',
                            'blueops': 'Defensive Security',
                            'test': 'Security Testing',
                            'iam': 'Identity & Access Management',
                            'asset': 'Asset Security'
                        }
                        domain = domain_mapping.get(domain_raw.lower(), domain_raw)

            # Map difficulty to level
            level_map = {
                1: 'Entry Level',
                2: 'Intermediate', 
                3: 'Advanced',
                4: 'Expert'
            }
            level = level_map.get(difficulty) if difficulty else 'Entry Level'

            # Extract relationships from positioning and connections
            relationships: Dict[str, List[str]] = {
                'prerequisites': [],
                'leads_to': [],
                'related': []
            }

            # Look for vertical connections (progression paths)
            y_pos = float(element.get('data-y', '0'))
            connected_elements = soup.find_all('div', 
                class_=lambda x: x and isinstance(x, str) and any(p in x.lower() for p in cert_patterns))

            for conn in connected_elements:
                conn_y = float(conn.get('data-y', '0'))
                if conn_y > y_pos:  # Element is below current cert
                    related = conn.get('title', '').strip()
                    if related:
                        relationships['leads_to'].append(related)
                elif conn_y < y_pos:  # Element is above current cert
                    related = conn.get('title', '').strip()
                    if related:
                        relationships['prerequisites'].append(related)

            # Look for horizontal connections (related certs)
            x_pos = float(element.get('data-x', '0'))
            for conn in connected_elements:
                conn_x = float(conn.get('data-x', '0'))
                if abs(conn_x - x_pos) < 100 and abs(float(conn.get('data-y', '0')) - y_pos) < 50:
                    related = conn.get('title', '').strip()
                    if related and related != name:
                        relationships['related'].append(related)

            # Remove empty relationships and deduplicate
            relationships = {k: list(set(v)) for k, v in relationships.items() if v}
            
            # Detect provider from cert name
            provider = extract_provider(name)
            
            # Detect certification type
            cert_type = extract_cert_type(name, domain)

            certifications[name] = {
                'difficulty': difficulty,
                'domain': domain,
                'level': level,
                'provider': provider,
                'type': cert_type,
                'relationships': relationships,
                'position': {'x': x_pos, 'y': y_pos}
            }

    logger.info(f"Parsed {len(certifications)} certifications with relationships")
    return certifications

def merge_roadmap_data(base_certs: Dict[str, Any], roadmap_certs: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge the roadmap data with our existing certification data

    Args:
        base_certs: Original certification data
        roadmap_certs: Additional data from roadmap parsing

    Returns:
        Dictionary containing merged certification data
    """
    merged = {}

    for name, cert_data in base_certs.items():
        merged[name] = cert_data.copy()

        # If we have additional data from the roadmap, merge it
        if name in roadmap_certs:
            roadmap_data = roadmap_certs[name]

            # Update relationships
            merged[name]['related_certs'] = roadmap_data['relationships']

            # Add positioning data for visualization
            merged[name]['position'] = roadmap_data['position']

            # Update level and domain if not already set
            if 'level' not in merged[name] and roadmap_data['level']:
                merged[name]['level'] = roadmap_data['level']

            if 'domain' not in merged[name] and roadmap_data['domain']:
                merged[name]['domain'] = roadmap_data['domain']
                
            # Update provider and type if needed
            if merged[name]['provider'] == 'Unknown' and roadmap_data['provider'] != 'Unknown':
                merged[name]['provider'] = roadmap_data['provider']
                
            if 'type' not in merged[name] and 'type' in roadmap_data:
                merged[name]['type'] = roadmap_data['type']

    logger.info(f"Merged data for {len(merged)} certifications")
    return merged