"""
Utility functions for gathering and managing study resources
"""
import requests
from bs4 import BeautifulSoup
from typing import List, Dict, Optional
import logging
from datetime import datetime
import trafilatura
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

def validate_resource_url(url: str) -> bool:
    """
    Validate if a URL is accessible and returns valid content
    """
    try:
        response = requests.head(url, timeout=5, allow_redirects=True)
        return response.status_code == 200
    except Exception as e:
        logger.error(f"Error validating URL {url}: {str(e)}")
        return False

def get_resource_recommendations(cert_name: str, user_learning_style: str) -> List[Dict]:
    """
    Get personalized resource recommendations based on certification and learning style
    Returns a curated list of study resources
    """
    # Default high-quality resources for common certifications
    default_resources = {
        "CompTIA Security+": [
            {
                "type": "documentation",
                "title": "Official CompTIA Security+ Study Guide",
                "url": "https://www.comptia.org/training/books/security-sy0-601-study-guide",
                "description": "CompTIA's comprehensive study guide covering all exam objectives",
                "is_official": True,
                "access_type": "paid"
            },
            {
                "type": "video",
                "title": "Professor Messer Security+ Training",
                "url": "https://www.professormesser.com/security-plus/sy0-601/sy0-601-video/sy0-601-comptia-security-plus/",
                "description": "Free comprehensive video course covering all Security+ topics",
                "is_official": False,
                "access_type": "free"
            },
            {
                "type": "practice_exam",
                "title": "Official CompTIA Security+ Practice Tests",
                "url": "https://www.comptia.org/training/certmaster-practice/security",
                "description": "Practice questions and performance-based exercises",
                "is_official": True,
                "access_type": "paid"
            }
        ],
        "CISSP": [
            {
                "type": "documentation",
                "title": "Official (ISC)² CISSP Study Guide",
                "url": "https://www.isc2.org/Training/Self-Study-Resources",
                "description": "Comprehensive guide covering all 8 CISSP domains",
                "is_official": True,
                "access_type": "paid"
            },
            {
                "type": "video",
                "title": "Cybrary CISSP Course",
                "url": "https://www.cybrary.it/course/cissp",
                "description": "In-depth video training covering CISSP domains",
                "is_official": False,
                "access_type": "free"
            }
        ],
        "CEH": [
            {
                "type": "documentation",
                "title": "Official CEH Training",
                "url": "https://www.eccouncil.org/programs/certified-ethical-hacker-ceh/",
                "description": "EC-Council's official study materials",
                "is_official": True,
                "access_type": "paid"
            },
            {
                "type": "practice_exam",
                "title": "CEH Practice Range",
                "url": "https://www.eccouncil.org/programs/certified-ethical-hacker-ceh/ceh-assessment/",
                "description": "Hands-on practice environment",
                "is_official": True,
                "access_type": "subscription"
            }
        ]
    }

    # Check if we have default resources for this certification
    resources = default_resources.get(cert_name, [])

    # If no default resources found, try to find online resources
    if not resources:
        resources = find_certification_resources(cert_name)

    # Filter and sort resources based on learning style
    if user_learning_style.lower() == 'visual':
        return sorted(resources, key=lambda x: 1 if x['type'] == 'video' else 0, reverse=True)
    elif user_learning_style.lower() == 'reading':
        return sorted(resources, key=lambda x: 1 if x['type'] in ['documentation', 'tutorial'] else 0, reverse=True)
    elif user_learning_style.lower() == 'hands-on':
        return sorted(resources, key=lambda x: 1 if x['type'] in ['practice_exam', 'tutorial'] else 0, reverse=True)
    else:
        return resources

def find_certification_resources(cert_name: str) -> List[Dict]:
    """Search for study resources related to a specific certification"""
    resources = []
    try:
        search_url = f"https://www.google.com/search?q={cert_name}+certification+study+guide+official"
        response = requests.get(search_url, headers={'User-Agent': 'Mozilla/5.0'})
        soup = BeautifulSoup(response.text, 'html.parser')

        for result in soup.find_all('a'):
            href = result.get('href', '')
            if href.startswith('http'):
                metadata = extract_resource_metadata(href)
                if metadata:
                    resources.append(metadata)

    except Exception as e:
        logger.error(f"Error finding resources for {cert_name}: {str(e)}")

    return resources

def extract_resource_metadata(url: str) -> Optional[Dict]:
    """Extract metadata from a study resource URL"""
    try:
        downloaded = trafilatura.fetch_url(url)
        if not downloaded:
            return None

        metadata = {
            'type': 'documentation',
            'title': url,
            'url': url,
            'description': None,
            'is_official': False,
            'access_type': 'unknown'
        }

        soup = BeautifulSoup(downloaded, 'html.parser')

        # Get title
        title_tag = soup.find('title')
        if title_tag:
            metadata['title'] = title_tag.text.strip()

        # Get description
        desc_tag = soup.find('meta', attrs={'name': 'description'}) or \
                  soup.find('meta', attrs={'property': 'og:description'})
        if desc_tag:
            metadata['description'] = desc_tag.get('content', '').strip()

        # Determine resource type and other metadata
        domain = urlparse(url).netloc.lower()
        metadata.update({
            'type': 'video' if any(s in domain for s in ['youtube.com', 'vimeo.com']) else
                    'practice_exam' if any(s in url.lower() for s in ['practice', 'exam', 'test']) else
                    'documentation',
            'is_official': any(org in domain for org in [
                'comptia.org', 'cisco.com', 'microsoft.com', 'aws.amazon.com', 
                'isc2.org', 'isaca.org', 'eccouncil.org'
            ]),
            'access_type': 'paid' if any(s in url.lower() for s in ['/premium/', '/pro/', 'subscription']) else 'free'
        })

        return metadata

    except Exception as e:
        logger.error(f"Error extracting metadata from {url}: {str(e)}")
        return None