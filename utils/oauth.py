"""OAuth authentication module for Streamlit application."""
import os
from authlib.integrations.requests_client import OAuth2Session
import streamlit as st
from typing import Optional, Dict, Any
from components.icons import get_icon
import logging
import traceback
import json
from utils.session_manager import OAuthSessionManager

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class OAuthHandler:
    """Handles OAuth authentication for multiple providers."""

    CONFIGS = {
        'github': {
            'client_id': os.getenv('GITHUB_CLIENT_ID'),
            'client_secret': os.getenv('GITHUB_CLIENT_SECRET'),
            'authorize_url': 'https://github.com/login/oauth/authorize',
            'token_url': 'https://github.com/login/oauth/access_token',
            'userinfo_url': 'https://api.github.com/user',
            'scope': 'read:user user:email',
            'headers': {'Accept': 'application/json'}
        },
        'google': {
            'client_id': os.getenv('GOOGLE_CLIENT_ID'),
            'client_secret': os.getenv('GOOGLE_CLIENT_SECRET'),
            'authorize_url': 'https://accounts.google.com/o/oauth2/v2/auth',
            'token_url': 'https://oauth2.googleapis.com/token',
            'userinfo_url': 'https://www.googleapis.com/oauth2/v3/userinfo',
            'scope': 'openid email profile'
        },
        'stackoverflow': {
            'client_id': os.getenv('STACKOVERFLOW_CLIENT_ID'),
            'client_secret': os.getenv('STACKOVERFLOW_CLIENT_SECRET'),
            'authorize_url': 'https://stackoverflow.com/oauth',
            'token_url': 'https://stackoverflow.com/oauth/access_token',
            'userinfo_url': 'https://api.stackoverflow.com/2.3/me',
            'scope': 'read_user'
        }
    }

    @staticmethod
    def get_callback_uri(provider: str = None) -> str:
        """Get the callback URI based on the environment and configuration."""
        base_uri = os.getenv('OAUTH_REDIRECT_URI')
        if not base_uri:
            raise ValueError("OAUTH_REDIRECT_URI environment variable must be set")

        base_uri = base_uri.rstrip('/')
        if provider:
            return f"{base_uri}?provider={provider}"
        return base_uri

    @staticmethod
    def initialize_oauth_session(provider: str) -> Optional[OAuth2Session]:
        """Initialize an OAuth session for the specified provider."""
        config = OAuthHandler.CONFIGS.get(provider)
        if not config:
            return None

        return OAuth2Session(
            client_id=config['client_id'],
            client_secret=config['client_secret'],
            scope=config['scope']
        )

    @staticmethod
    def get_authorization_url(provider: str) -> tuple[str, str]:
        """Get the authorization URL for the specified provider."""
        try:
            logger.info(f"Starting OAuth flow for provider: {provider}")

            config = OAuthHandler.CONFIGS.get(provider)
            if not config:
                raise ValueError(f"Unsupported provider: {provider}")

            if not config['client_id'] or not config['client_secret']:
                raise ValueError(f"Missing OAuth credentials for {provider}")

            oauth = OAuthHandler.initialize_oauth_session(provider)
            if not oauth:
                raise ValueError(f"Failed to initialize OAuth session for {provider}")

            callback_uri = OAuthHandler.get_callback_uri(provider)
            logger.info(f"Using callback URI: {callback_uri}")

            auth_url, state = oauth.create_authorization_url(
                config['authorize_url'],
                redirect_uri=callback_uri
            )

            # Use session manager to store state
            session_manager = OAuthSessionManager()
            session_manager.store_state(provider, state)
            logger.info(f"Stored OAuth state for {provider}")

            return auth_url, state

        except Exception as e:
            logger.error(f"Error in get_authorization_url: {str(e)}")
            logger.error(f"Stacktrace: {traceback.format_exc()}")
            raise

    @staticmethod
    def get_user_info(provider: str, code: str, state: str) -> Dict[str, Any]:
        """Get user information after successful OAuth authentication."""
        try:
            logger.info(f"Starting user info retrieval - Provider: {provider}")

            config = OAuthHandler.CONFIGS.get(provider)
            if not config:
                raise ValueError(f"Unsupported provider: {provider}")

            # Verify state using session manager
            session_manager = OAuthSessionManager()
            if not session_manager.verify_state(provider, state):
                raise ValueError("Invalid or expired state parameter")

            oauth = OAuthHandler.initialize_oauth_session(provider)
            if not oauth:
                raise ValueError(f"Failed to initialize OAuth session for {provider}")

            callback_uri = OAuthHandler.get_callback_uri(provider)
            logger.info(f"Using callback URI for token exchange: {callback_uri}")

            try:
                # Exchange code for token
                token_response = oauth.fetch_token(
                    config['token_url'],
                    code=code,
                    redirect_uri=callback_uri,
                    headers=config.get('headers', {})
                )
                logger.info("Successfully obtained access token")

                # Set up headers for user info request
                user_headers = config.get('headers', {}).copy()
                user_headers['Authorization'] = f"Bearer {token_response['access_token']}"

                # Get user info
                resp = oauth.get(config['userinfo_url'], headers=user_headers)
                logger.info(f"User info response status: {resp.status_code}")
                logger.info(f"User info response content: {resp.text}")

                if resp.status_code != 200:
                    raise Exception(f"Failed to get user info: {resp.text}")

                user_info = resp.json()

                # Handle GitHub-specific email fetching
                if provider == 'github' and (not user_info.get('email') or user_info['email'] is None):
                    email_resp = oauth.get('https://api.github.com/user/emails', headers=user_headers)
                    if email_resp.status_code == 200:
                        emails = email_resp.json()
                        primary_email = next((email for email in emails if email.get('primary')), None)
                        if primary_email:
                            user_info['email'] = primary_email['email']

                # Clear state after successful authentication
                session_manager.clear_state(provider)

                return user_info

            except Exception as e:
                logger.error(f"Error during token exchange or user info fetch: {str(e)}")
                logger.error(f"Stacktrace: {traceback.format_exc()}")
                raise

        except Exception as e:
            logger.error(f"Error in get_user_info: {str(e)}")
            logger.error(f"Stacktrace: {traceback.format_exc()}")
            raise

def init_auth_state():
    """Initialize authentication state in Streamlit session."""
    if 'user' not in st.session_state:
        st.session_state.user = None
    # Initialize session manager and clean up expired states
    session_manager = OAuthSessionManager()
    session_manager.cleanup_expired_states()
    logger.info("Auth state initialized and expired states cleaned up")

def login_button(provider: str):
    """Display a login button for the specified provider."""
    if st.button(f"{get_icon(provider)} {provider.title()}"):
        try:
            auth_url, state = OAuthHandler.get_authorization_url(provider)
            st.markdown(f'<a href="{auth_url}" target="_self">Click here to login with {provider.title()}</a>', unsafe_allow_html=True)
        except Exception as e:
            logger.error(f"Failed to initialize {provider} login: {str(e)}")
            logger.error(f"Stacktrace: {traceback.format_exc()}")
            st.error(f"Failed to initialize {provider} login: {str(e)}")