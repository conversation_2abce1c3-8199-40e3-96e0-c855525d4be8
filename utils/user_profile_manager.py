"""User profile management utility for consistent state handling across pages"""
import streamlit as st
from typing import Dict, Optional
import logging
from database import get_db
from models.learning_path import UserExperience
from datetime import datetime

logger = logging.getLogger(__name__)

class UserProfileManager:
    """Manages user profile state and database synchronization"""

    @staticmethod
    def initialize_session():
        """Initialize user profile in session state if not present"""
        if 'user_profile' not in st.session_state:
            logger.info("Initializing user profile session state")
            UserProfileManager.load_profile()

    @staticmethod
    def get_default_profile(user_id: str = 'test_user_1') -> Dict:
        """Get a default profile structure"""
        return {
            'user_id': user_id,
            'years_experience': 0,
            'user_role': '',
            'desired_role': '',
            'expertise_areas': [],
            'preferred_learning_style': 'Mixed',
            'study_time_available': 10,
            'tutorial_completed': False,  # Added tutorial tracking
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }

    @staticmethod
    def load_profile() -> Dict:
        """Load user profile from database into session state"""
        db = None
        try:
            # Get user_id safely
            logger.debug("Current session state keys: %s", list(st.session_state.keys()))

            # Safely get user data, defaulting to empty dict if None
            user_data = st.session_state.get('user')
            logger.debug("User data from session: %s", user_data)

            # If user_data is None or empty, use default
            if not user_data or not isinstance(user_data, dict):
                logger.info("No valid user data in session state, using default test user")
                user_id = 'test_user_1'
            else:
                user_id = user_data.get('id', 'test_user_1')

            logger.info(f"Loading profile for user {user_id}")

            # Get database connection
            db = next(get_db())
            profile = db.query(UserExperience).filter_by(user_id=user_id).first()

            if profile:
                profile_dict = profile.to_dict()
                # Ensure tutorial_completed field exists
                if 'tutorial_completed' not in profile_dict:
                    profile_dict['tutorial_completed'] = False
                st.session_state['user_profile'] = profile_dict
                logger.info(f"Loaded profile into session state: {profile_dict}")
                return profile_dict
            else:
                # Create default profile
                default_profile = UserProfileManager.get_default_profile(user_id)
                st.session_state['user_profile'] = default_profile
                logger.info(f"Created default profile for new user: {default_profile}")
                return default_profile

        except Exception as e:
            logger.error(f"Error loading profile: {str(e)}", exc_info=True)
            # Get a default profile on error
            default_profile = UserProfileManager.get_default_profile()
            st.session_state['user_profile'] = default_profile
            return default_profile
        finally:
            if db:
                db.close()

    @staticmethod
    def save_profile(user_data: Dict) -> bool:
        """Save profile to database and update session state"""
        db = None
        try:
            db = next(get_db())
            # Safely get user_id
            user_session = st.session_state.get('user')
            user_id = user_session.get('id') if isinstance(user_session, dict) else 'test_user_1'
            logger.info(f"Saving profile for user {user_id}")

            profile = db.query(UserExperience).filter_by(user_id=user_id).first()

            if profile:
                logger.info("Updating existing profile")
                for key, value in user_data.items():
                    setattr(profile, key, value)
                profile.updated_at = datetime.utcnow()
            else:
                logger.info("Creating new profile")
                profile = UserExperience(
                    user_id=user_id,
                    **user_data,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                db.add(profile)

            db.commit()

            # Update session state
            profile_dict = profile.to_dict()
            st.session_state['user_profile'] = profile_dict
            logger.info(f"Profile saved and session updated: {profile_dict}")
            return True

        except Exception as e:
            logger.error(f"Error saving profile: {str(e)}", exc_info=True)
            if db:
                db.rollback()
            return False
        finally:
            if db:
                db.close()

    @staticmethod
    def get_profile() -> Dict:
        """Get current profile from session state or load from database"""
        # First check if we have a profile in session state
        if 'user_profile' not in st.session_state:
            logger.debug("No profile in session state, loading from database")
            return UserProfileManager.load_profile()

        # Get profile from session state, defaulting to empty dict if None
        profile = st.session_state.get('user_profile')
        if not profile or not isinstance(profile, dict):
            logger.warning("Invalid profile in session state, loading fresh profile")
            return UserProfileManager.load_profile()

        # Ensure tutorial_completed field exists
        if 'tutorial_completed' not in profile:
            profile['tutorial_completed'] = False
            st.session_state['user_profile'] = profile

        return profile

    @staticmethod
    def clear_profile():
        """Clear profile from session state"""
        if 'user_profile' in st.session_state:
            del st.session_state['user_profile']
            logger.info("Cleared profile from session state")