"""
Utility functions for certification data enrichment.

This module provides functionality for analyzing and enriching certification data 
through web scraping, content analysis, and machine learning-based matching.
"""
from __future__ import annotations
import requests
from bs4 import BeautifulSoup, Tag
from typing import Dict, Tuple, Optional, List, TypedDict, Union, Any
import logging
from datetime import datetime
import PyPDF2
import io
import magic
import trafilatura
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

logger = logging.getLogger(__name__)

class CertificationMatch(TypedDict):
    """Type definition for certification matching results."""
    name: str
    confidence: float

class CertificationContent(TypedDict):
    """Type definition for certification content data."""
    course_content: str
    content_type: str

class CertificationData(TypedDict, total=False):
    """Type definition for certification data with optional fields."""
    name: Optional[str]
    description: Optional[str]
    cost: Optional[float]
    prerequisites: Optional[str]
    validity_period: Optional[str]
    exam_code: Optional[str]
    course_content: Optional[str]
    content_type: str

def find_matching_certifications(
    content: str,
    certifications: List[Any],
    top_n: int = 3
) -> List[Tuple[Any, float]]:
    """
    Find the best matching certifications for given content using TF-IDF and cosine similarity.

    Args:
        content: Text content to match against certifications
        certifications: List of certification objects to match against
        top_n: Number of top matches to return

    Returns:
        List of tuples containing (certification, confidence_score)
    """
    try:
        # Prepare texts for comparison
        cert_texts = []
        for cert in certifications:
            # Combine various text fields for better matching
            text_parts = [
                cert.name,
                cert.description or "",
                cert.prerequisites or "",
                cert.course_content or ""
            ]
            cert_texts.append(" ".join(text_parts))

        # Add the new content as the last document
        all_texts = cert_texts + [content]

        # Create TF-IDF vectors
        vectorizer = TfidfVectorizer(
            stop_words='english',
            max_features=5000,
            ngram_range=(1, 2)
        )
        tfidf_matrix = vectorizer.fit_transform(all_texts)

        # Calculate similarity between new content and each certification
        content_vector = tfidf_matrix[-1:]  # Get vector for new content
        similarities = cosine_similarity(content_vector, tfidf_matrix[:-1])[0]  # Compare with all certs

        # Get top N matches with scores
        top_indices = np.argsort(similarities)[-top_n:][::-1]
        matches = [
            (certifications[idx], float(similarities[idx]))
            for idx in top_indices
            if similarities[idx] > 0.1  # Only include matches with some similarity
        ]

        return matches
    except Exception as e:
        logger.error(f"Error finding matching certifications: {str(e)}")
        return []

def extract_pdf_content(pdf_file: io.BytesIO) -> Optional[str]:
    """
    Extract content from a PDF file.

    Args:
        pdf_file: BytesIO object containing PDF data

    Returns:
        Extracted text content or None if extraction fails
    """
    try:
        # Create PDF reader object
        pdf_reader = PyPDF2.PdfReader(pdf_file)

        # Extract text from all pages
        content = []
        for page in pdf_reader.pages:
            content.append(page.extract_text())

        return "\n".join(content)
    except Exception as e:
        logger.error(f"Error extracting PDF content: {str(e)}")
        return None

def fetch_certification_data(url: str) -> Optional[CertificationData]:
    """
    Fetch certification data from the provided URL.

    Args:
        url: URL of the certification page

    Returns:
        Dictionary containing certification data or None if fetch fails

    Raises:
        requests.RequestException: If the HTTP request fails
    """
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()

        # Check if it's a PDF file
        content_type = response.headers.get('content-type', '')
        if 'application/pdf' in content_type.lower():
            pdf_content = extract_pdf_content(io.BytesIO(response.content))
            if pdf_content:
                return CertificationData(
                    course_content=pdf_content,
                    content_type='pdf'
                )

        # Use trafilatura for better HTML content extraction
        downloaded = trafilatura.fetch_url(url)
        if downloaded:
            extracted_text = trafilatura.extract(downloaded)
            if extracted_text:
                return CertificationData(
                    course_content=extracted_text,
                    content_type='html'
                )

        # Fallback to BeautifulSoup if trafilatura fails
        soup = BeautifulSoup(response.text, 'html.parser')

        # Extract data based on common certification page patterns
        data: CertificationData = {
            'content_type': 'html'
        }

        # Look for content in common locations
        content_sections = soup.find_all(
            ['div', 'section'],
            class_=lambda x: isinstance(x, str) and any(
                term in x.lower() for term in ['syllabus', 'curriculum', 'content', 'topics']
            )
        )

        if content_sections:
            data['course_content'] = "\n".join(
                section.get_text(strip=True) for section in content_sections
            )

        # Extract other certification information
        title = soup.find('h1')
        if title:
            data['name'] = title.text.strip()

        # Look for cost information
        cost_keywords = ['price', 'fee', 'cost']
        for keyword in cost_keywords:
            cost_elem = soup.find(
                lambda tag: tag.name in ['p', 'div', 'span'] and 
                isinstance(tag, Tag) and
                keyword.lower() in tag.text.lower()
            )
            if cost_elem:
                import re
                numbers = re.findall(r'\$?\d+(?:,\d+)?(?:\.\d+)?', cost_elem.text)
                if numbers:
                    try:
                        cost = float(numbers[0].replace('$', '').replace(',', ''))
                        data['cost'] = cost
                        break
                    except ValueError:
                        continue

        # Look for description
        desc = soup.find(
            ['div', 'p'],
            class_=lambda x: isinstance(x, str) and 'description' in x.lower()
        )
        if desc:
            data['description'] = desc.text.strip()

        return data
    except requests.RequestException as e:
        logger.error(f"HTTP Request Error fetching certification data: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error fetching certification data: {str(e)}")
        return None

def compare_certification_data(
    current: CertificationData,
    new: CertificationData
) -> Tuple[Dict[str, Dict[str, Any]], bool]:
    """
    Compare current certification data with newly fetched data.

    Args:
        current: Current certification data
        new: New certification data to compare against

    Returns:
        Tuple containing:
            - Dictionary of changes with old and new values
            - Boolean indicating if significant changes were found
    """
    changes = {}
    significant_fields = {'name', 'cost', 'validity_period', 'exam_code', 'course_content'}
    has_significant_changes = False

    for key in new:
        if key in current and new[key] != current[key]:
            changes[key] = {
                'old': current[key],
                'new': new[key]
            }
            if key in significant_fields:
                has_significant_changes = True

    return changes, has_significant_changes

def format_changes(changes: Dict[str, Dict[str, Any]]) -> str:
    """
    Format changes for display.

    Args:
        changes: Dictionary of changes to format

    Returns:
        Formatted string representation of changes
    """
    if not changes:
        return "No changes detected"

    formatted = []
    for field, values in changes.items():
        if field == 'course_content':
            # Truncate course content display
            old_content = values['old'][:500] + "..." if values['old'] and len(values['old']) > 500 else values['old']
            new_content = values['new'][:500] + "..." if values['new'] and len(values['new']) > 500 else values['new']
            formatted.append(f"{field.title()}:\n"
                           f"  - Old: {old_content}\n"
                           f"  - New: {new_content}")
        else:
            formatted.append(f"{field.title()}:\n"
                           f"  - Old: {values['old']}\n"
                           f"  - New: {values['new']}")

    return "\n".join(formatted)