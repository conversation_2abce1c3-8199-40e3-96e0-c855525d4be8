"""
Process HTML data from SecCertRoadmapHTML submodule
"""
import os
from bs4 import BeautifulSoup
import json
import logging
import re

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_prerequisites(tooltip_text: str) -> list:
    """Extract prerequisite certifications from tooltip text"""
    prerequisites = []

    # Common prerequisite patterns
    patterns = [
        r"requires? (the )?([A-Za-z0-9\+\-\(\)]+(?:\s*[A-Za-z0-9\+\-\(\)]+)*)",
        r"prerequisite:?\s*([A-Za-z0-9\+\-\(\)]+(?:\s*[A-Za-z0-9\+\-\(\)]+)*)",
        r"after completing ([A-Za-z0-9\+\-\(\)]+(?:\s*[A-Za-z0-9\+\-\(\)]+)*)",
        r"must have ([A-Za-z0-9\+\-\(\)]+(?:\s*[A-Za-z0-9\+\-\(\)]+)*) certification"
    ]

    for pattern in patterns:
        matches = re.finditer(pattern, tooltip_text, re.IGNORECASE)
        for match in matches:
            # Get the certification name from the match
            cert_name = match.group(1) if len(match.groups()) == 1 else match.group(2)
            cert_name = cert_name.strip()
            if cert_name and len(cert_name) > 2:  # Avoid short acronyms or false matches
                prerequisites.append(cert_name)

    return list(set(prerequisites))  # Remove duplicates

def extract_validity_period(description: str) -> int:
    """Extract validity period in months from certification description"""
    # Common patterns for validity periods
    patterns = [
        r'valid for (\d+)\s*years?',
        r'validity( period)? of (\d+)\s*years?',
        r'renew every (\d+)\s*years?',
        r'certification expires? after (\d+)\s*years?',
        r'recertification required every (\d+)\s*years?',
        r'(\d+)-year certification',
        # Also check for month patterns
        r'valid for (\d+)\s*months?',
        r'validity( period)? of (\d+)\s*months?',
        r'renew every (\d+)\s*months?'
    ]

    for pattern in patterns:
        match = re.search(pattern, description, re.IGNORECASE)
        if match:
            # Get the number from the matched group
            value = int(match.group(1)) if 'month' in pattern.lower() else int(match.group(1)) * 12
            # Validate reasonable range (6 months to 10 years)
            if 6 <= value <= 120:
                return value

    return 36  # Default to 3 years if no valid period found

def process_certification_html():
    """Process the Security Certification Roadmap HTML file and extract certification data"""
    html_file = os.path.join('submodules', 'SecCertRoadmapHTML', 'Security-Certification-Roadmap9.html')

    if not os.path.exists(html_file):
        logger.error(f"HTML file not found at {html_file}")
        return []

    logger.info(f"Processing HTML file: {html_file}")
    with open(html_file, 'r', encoding='utf-8') as f:
        soup = BeautifulSoup(f.read(), 'html.parser')

    # Find all certification elements by looking for specific class patterns
    cert_patterns = [
        'redopsitem', 'networkitem', 'mgmtitem', 'engineeritem',
        'blueopsitem', 'testitem', 'iamitem', 'assetitem'
    ]

    cert_elements = []
    for pattern in cert_patterns:
        elements = soup.find_all("a", class_=lambda x: x and pattern in x.lower())
        cert_elements.extend(elements)

    logger.info(f"Found {len(cert_elements)} certification elements")

    # Initialize categories dictionary
    categories = {
        'Entry Level Security Certifications': [],
        'Mid-Level Security Certifications': [],
        'Advanced Security Certifications': [],
        'Expert Security Certifications': [],
        'Specialized Security Certifications': [],
        'Management Security Certifications': []
    }

    # Process certifications
    processed_certs = set()  # Track processed certifications to avoid duplicates
    for cert_element in cert_elements:
        # Extract name from text content
        name = cert_element.get_text().strip()
        if not name or name in processed_certs:
            continue

        processed_certs.add(name)

        # Extract URL
        url = cert_element.get('href')

        # Extract description from tooltip attributes
        description = cert_element.get('tooltiptext') or cert_element.get('tooltipleft') or cert_element.get('tooltipright', '')

        # Extract prerequisites
        prerequisites = extract_prerequisites(description)
        logger.debug(f"Found prerequisites for {name}: {prerequisites}")

        # Extract validity period
        validity_period = extract_validity_period(description)
        logger.debug(f"Found validity period for {name}: {validity_period} months")

        # Extract cost from description if available
        cost = None
        cost_match = re.search(r'\$(\d+(?:,\d+)?(?:\.\d+)?)', description)
        if cost_match:
            cost = float(cost_match.group(1).replace(',', ''))

        # Determine difficulty and category based on class
        classes = cert_element.get('class', [])
        difficulty = None
        focus = None

        for cls in classes:
            if any(pattern in cls.lower() for pattern in cert_patterns):
                focus = cls.replace('item', '').replace('1', '').replace('2', '').replace('3', '').replace('4', '').title()
                # Extract numeric difficulty from class (if present)
                diff_match = re.search(r'\d+', cls)
                if diff_match:
                    difficulty = int(diff_match.group())

        # Map focus areas to domains
        domain_mapping = {
            'Redops': 'Offensive Security',
            'Network': 'Network Security',
            'Mgmt': 'Security Management',
            'Engineer': 'Security Engineering',
            'Blueops': 'Defensive Security',
            'Test': 'Security Testing',
            'Iam': 'Identity & Access Management',
            'Asset': 'Asset Security'
        }

        domain = next((domain_mapping[key] for key in domain_mapping if key.lower() in focus.lower()), 'General Security')

        # Determine category based on difficulty and other factors
        if difficulty == 1 or 'entry' in description.lower():
            category = 'Entry Level Security Certifications'
        elif difficulty == 2 or 'intermediate' in description.lower():
            category = 'Mid-Level Security Certifications'
        elif difficulty == 3 or 'advanced' in description.lower():
            category = 'Advanced Security Certifications'
        elif difficulty == 4 or 'expert' in description.lower():
            category = 'Expert Security Certifications'
        elif 'management' in description.lower():
            category = 'Management Security Certifications'
        else:
            category = 'Specialized Security Certifications'

        # Create certification entry
        cert_data = {
            "name": name,
            "url": url,
            "description": description,
            "cost": cost,
            "difficulty": difficulty,
            "focus": focus,
            "domain": domain,
            "prerequisites": prerequisites,
            "validity_period": validity_period  # Add validity period to cert data
        }

        # Add to appropriate category
        categories[category].append(cert_data)

    # Convert categories dict to list format
    formatted_categories = [
        {
            "category": category,
            "certifications": sorted(certs, key=lambda x: x['name'])
        }
        for category, certs in categories.items()
        if certs  # Only include categories with certifications
    ]

    # Save to JSON file
    output_file = os.path.join('attached_assets', 'certifications.json')
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(formatted_categories, f, indent=2, ensure_ascii=False)

    # Log results
    total_certs = len(processed_certs)
    logger.info(f"Processed {total_certs} unique certifications across {len(formatted_categories)} categories")

    # Print detailed category breakdown
    for category in formatted_categories:
        logger.info(f"{category['category']}: {len(category['certifications'])} certifications")

    return formatted_categories

if __name__ == "__main__":
    process_certification_html()