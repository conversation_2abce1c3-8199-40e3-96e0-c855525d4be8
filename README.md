# Security Certification Explorer

A comprehensive React-powered platform for exploring security certifications, providing interactive guidance and advanced data management for cybersecurity professionals and enthusiasts. The application helps users discover relevant certifications based on their interests, experience level, and budget constraints.

## Features

### Current Features

- **Interactive Main Page**
  - Engaging hero section with platform value proposition
  - Dashboard preview with key metrics visualization
  - Career path navigator with interactive visualization
  - Certification catalog with filtering capabilities
  - Success stories from platform users
  - Step-by-step getting started guide

- **Interactive Filtering System**

  - Multi-select domain filter buttons for precise domain selection
  - Experience level filtering (Entry Level to Expert)
  - Cost-based filtering with dynamic slider
  - Real-time results updating

- **Comprehensive Data Display**

  - Detailed certification information
  - Cost and prerequisites details
  - Organization and domain categorization
  - Direct links to certification pages

- **Multilingual Support**

  - Full internationalization (i18n) support
  - Available languages:
    - 🇬🇧 English (en)
    - 🇩🇪 German (de)
    - 🇪🇸 Spanish (es)
    - 🇫🇷 French (fr)
    - 🇿🇦 Afrikaans (af)
    - 🇿🇦 isiZulu (zu)
    - 🇷🇴 Romanian (ro)
  - Dynamic language switching
  - Localized content and UI elements
  - Extended support for African languages

- **Database Integration**

  - PostgreSQL database for robust data management
  - Automatic data extraction from Security Certification Roadmap
  - Structured certification data model

- **Modern React UI**
  - Component-based architecture
  - TypeScript for type safety
  - Responsive design for all devices
  - Improved user experience

- **API Versioning System**
  - Centralized API route management with versioning (api/v1/...)
  - Consistent endpoint definitions between frontend and backend
  - React hooks for accessing versioned API endpoints
  - Future-proof architecture for API evolution

### Upcoming Features

- **Advanced Visualization**

  - D3.js-powered certification progression paths
  - Domain-specific certification hierarchies
  - Interactive certification relationship mapping
  - Visual prerequisite chains
  - Improved node sizing based on certification difficulty
  - Enhanced graph interaction stability

- **Enhanced Data Analysis**

  - Certification comparison tools
  - Cost-benefit analysis
  - Career path optimization
  - Prerequisites tracking
  - Node size representation refinement for better visualization

- **Certification Study Timer**

  - Track study time for each certification
  - Set study goals and receive progress notifications
  - Generate study schedules based on exam dates
  - Study session analytics

- **Advanced Cost Calculator**

  - Currency conversion support
  - Exam retake costs calculation
  - Provider cost comparison
  - Bundle pricing analysis
  - Regional price variations

- **AI-Powered Study Guide**

  - Custom study guides generation
  - AI-generated practice questions
  - Personalized learning paths
  - Adaptive content difficulty

- **Community Features**

  - User certification reviews
  - Study group formation
  - Study material sharing
  - Success stories and testimonials
  - Mentor matching

- **Progress Tracking Dashboard**

  - Visual progress tracking
  - Certification completion timeline
  - Study milestone achievements
  - Skill gap analysis
  - Performance metrics

- **Mobile-Friendly Features**

  - Study progress notifications
  - Exam date reminders
  - Quick certification lookup
  - Mobile-optimized study materials

- **Certification Data Enrichment** (In Progress)
  - Automated online data verification
  - Historical version tracking
  - Change detection and review
  - Data quality monitoring
  - Automatic updates with admin approval

## Technical Architecture

### Data Flow

```mermaid
sequenceDiagram
    participant User
    participant UI as React Interface
    participant API as FastAPI Backend
    participant Filter as Filter System
    participant DB as PostgreSQL Database
    participant Viz as D3.js Visualization

    User->>UI: Select Filters
    UI->>API: Request Data
    API->>Filter: Apply Criteria
    Filter->>DB: Query Certifications
    DB-->>Filter: Return Matches
    Filter-->>API: Process Results
    API-->>UI: Return Data
    UI->>Viz: Generate Path
    Viz-->>UI: Display Graph
```

### Components

- **Frontend**: React with TypeScript
- **Backend**: FastAPI
- **Database**: PostgreSQL
- **Data Processing**: Python with SQLAlchemy
- **Visualization**: D3.js
- **Deployment**: Docker Compose with Nginx

## Setup and Installation

1. Ensure Docker and Docker Compose are installed
2. Clone the repository
3. Start the application:
   ```bash
   ./.dockerwrapper/run.sh up -d
   ```
4. Access the application at http://localhost:8080/

## Development

### Current Status

- Main page interface completely implemented with all planned features
- Interactive dashboard preview with Chart.js visualizations
- Career path navigator with D3.js visualization 
- Certification catalog preview with filtering capabilities
- Success stories component with testimonial carousel
- Getting started guide with interactive navigation
- Performance optimizations and analytics tracking implemented
- Basic filtering system implemented
- Database schema established
- Data extraction pipeline working
- Multi-select domain filters added
- Full internationalization support added
- Multiple language translations available

### Next Steps

1. Implement certification page based on PRD
2. Implement certification data enrichment system
3. Enhance graph interaction stability
4. Implement certification progression mapping
5. Enhance data extraction for prerequisites
6. Develop comparison features
7. Complete translations for all pages
8. Implement comprehensive UI testing with Playwright
9. Enhance component testing with React Testing Library

### Known Issues

1. Node sizing based on certification difficulty needs improvement
2. Graph interaction stability needs enhancement

### Developer Guidance

#### Code Standards

We follow these Python Enhancement Proposals (PEPs) for code quality:

1. **PEP-8: Style Guide**

   - Use 4 spaces for indentation
   - Maximum line length of 79 characters
   - Two blank lines between top-level functions and classes
   - One blank line between methods within classes
   - Use snake_case for functions and variables
   - Use PascalCase for classes

2. **PEP-257: Docstring Conventions**

   - All modules, functions, classes, and methods must have docstrings
   - Use triple quotes for all docstrings
   - First line should be a summary
   - Multi-line docstrings must have summary line followed by blank line

3. **PEP-484: Type Hints**
   - Use type hints for all function arguments and return values
   - Use Optional[] for values that could be None
   - Use List[], Dict[], etc. from typing module
   - Include type hints in stub files for third-party modules

#### Refactoring Plan

1. **Phase 1: Code Style and Documentation** (2 weeks)

   - Add type hints to all functions
   - Update docstrings to follow PEP-257
   - Apply PEP-8 style guidelines
   - Create stub files for third-party modules

2. **Phase 2: Code Organization** (2 weeks)

   - Reorganize project structure
   - Split large modules into smaller ones
   - Create proper package hierarchy
   - Implement proper error handling

3. **Phase 3: Testing Infrastructure** (2 weeks)

   - Add unit tests for core functionality
   - Implement integration tests
   - Add test coverage reporting
   - Create testing documentation

4. **Phase 4: Performance Optimization** (1 week)

   - Profile code for bottlenecks
   - Optimize database queries
   - Implement caching where appropriate
   - Reduce unnecessary API calls

5. **Phase 5: Security Hardening** (1 week)
   - Security audit of dependencies
   - Implement input validation
   - Add rate limiting
   - Enhance error handling

#### Implementation Priorities

1. Core functionality must remain stable during refactoring
2. Changes must be backward compatible
3. Each phase must include thorough testing
4. Documentation must be updated with changes
5. Performance impact must be monitored

## Testing Strategy

### Component Testing with React Testing Library

We use React Testing Library for component-level testing with the following principles:

1. **Test Behavior, Not Implementation**: Focus on how components behave from a user perspective
2. **Accessibility-First**: Use accessible queries to ensure our components work well with assistive technologies
3. **Realistic User Interactions**: Test components by simulating realistic user interactions
4. **Mocked API Layer**: Use mock service worker or similar tools to mock API responses
5. **Integration Tests**: Test how components work together in typical user flows

### E2E Testing with Playwright

Playwright provides end-to-end testing across multiple browsers with these goals:

1. **Cross-Browser Testing**: Verify functionality works in Chrome, Firefox, and Safari
2. **Visual Regression**: Catch unexpected visual changes with screenshot comparisons
3. **User Flows**: Test complete user journeys such as filtering certifications, language switching, etc.
4. **Performance Metrics**: Collect performance data during UI tests
5. **Mobile Viewport Testing**: Ensure responsive design works correctly across device sizes

### Test Coverage Goals

- Unit/Component Tests: 80%+ coverage
- Integration Tests: Key user flows
- E2E Tests: Critical paths and core functionality

### Implementation Timeline

1. **Phase 1 (2 weeks)**: Set up React Testing Library and initial component tests
2. **Phase 2 (2 weeks)**: Configure Playwright and implement first E2E tests
3. **Phase 3 (ongoing)**: Expand test coverage and integrate with CI pipeline

## Contributing

Please read our contributing guidelines for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Migration from Streamlit to React

✅ **Migration Complete!** The frontend has been successfully migrated from Streamlit to React while maintaining FastAPI as the backend.

### Migration Highlights

- **All features migrated to React:**

  - Dashboard
  - User Management
  - Certification Explorer
  - Reports
  - Admin Interface

- **Benefits:**
  - Improved user experience with modern UI
  - Enhanced maintainability with component-based architecture
  - Better performance with client-side rendering
  - Type safety with TypeScript
  - Better separation of concerns between frontend and backend

For more details, see the [Migration Guide](MIGRATION.md) and [Migration Milestones](.claude/milestones.md).

### Docker Environment

All Docker-related files are located in the `.dockerwrapper` directory. The Docker setup supports both multi-container and single-container deployments.

To start the application:

```bash
./.dockerwrapper/run.sh up -d
```

This will start the following services:

- FastAPI backend
- React frontend
- PostgreSQL database
- Nginx reverse proxy

You can access the application at:

- React UI: http://localhost:8080/
- FastAPI Swagger: http://localhost:8080/api/docs

## Recent Updates

- **TypeScript Error Remediation**
  - Fixed SVG Icon component type assertions
  - Improved D3.js visualization typing
  - Enhanced API client interfaces
  - Updated component interfaces for better type safety

- **Main Page Interface Design**
  - Created comprehensive PRD for main page implementation
  - Designed modular component architecture
  - Planned implementation in phases for iterative deployment
  - Added success metrics for feature validation
