# Task 23: Fix Playwright certification tests

Status: done
Priority: high

## Description

Investigate and fix issues with Playwright certification tests not being picked up correctly.

## Implementation Details

There are already existing Playwright tests for the certification explorer functionality (certification-explorer.spec.ts). The new certifications.spec.ts file in the frontend/tests directory is not being picked up correctly. Investigate the issue and ensure the tests run properly.

## Test Strategy

Verify that all certification tests can be run successfully with the Playwright test runner.

## Subtasks

- [x] 23.1: Examine current test configuration
- [x] 23.2: Run existing certification explorer tests
- [x] 23.3: Fix test discovery issues
