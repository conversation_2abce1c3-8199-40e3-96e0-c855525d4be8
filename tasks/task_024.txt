# Task 24: Fix URL Path Discrepancy in Certification Tests

Status: done
Priority: high

## Description

Resolve the discrepancy between the URL paths used in tests (/certifications) versus the actual application URL (/certification-explorer).

## Dependencies

- Task 23

## Implementation Details

During testing, we discovered that the certification tests expect UI components at /certifications URL, but the actual application serves these components at /certification-explorer. We need to either update the tests to use the correct path or implement additional routing to support both paths.

## Test Strategy

Use the existing Playwright tests to verify the fixed URL paths. All tests in certifications.spec.ts should pass after the URLs are aligned.

## Subtasks

- [x] 24.1: Analyze test vs. implementation URL differences
- [x] 24.2: Update test files or implement dual routing
- [x] 24.3: Add missing data-testid attributes
- [x] 24.4: Verify test updates with Docker container
