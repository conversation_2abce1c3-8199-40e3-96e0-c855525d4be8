# Security Certification Explorer

A comprehensive Streamlit-powered platform for exploring security certifications, providing interactive guidance and advanced data management for cybersecurity professionals and enthusiasts. The application helps users discover relevant certifications based on their interests, experience level, and budget constraints.

## Features

### Current Features
- **Interactive Filtering System**
  - Multi-select domain filter buttons for precise domain selection
  - Experience level filtering (Entry Level to Expert)
  - Cost-based filtering with dynamic slider
  - Real-time results updating

- **Comprehensive Data Display**
  - Detailed certification information
  - Cost and prerequisites details
  - Organization and domain categorization
  - Direct links to certification pages

- **Multilingual Support**
  - Full internationalization (i18n) support
  - Available languages:
    - 🇬🇧 English (en)
    - 🇩🇪 German (de)
    - 🇪🇸 Spanish (es)
    - 🇫🇷 French (fr)
    - 🇿🇦 Afrikaans (af)
    - 🇿🇦 isiZulu (zu)
    - 🇷🇴 Romanian (ro)
  - Dynamic language switching
  - Localized content and UI elements
  - Extended support for African languages

- **Database Integration**
  - PostgreSQL database for robust data management
  - Automatic data extraction from Security Certification Roadmap
  - Structured certification data model

### Upcoming Features
- **Advanced Visualization**
  - D3.js-powered certification progression paths
  - Domain-specific certification hierarchies
  - Interactive certification relationship mapping
  - Visual prerequisite chains
  - Improved node sizing based on certification difficulty
  - Enhanced graph interaction stability

- **Enhanced Data Analysis**
  - Certification comparison tools
  - Cost-benefit analysis
  - Career path optimization
  - Prerequisites tracking
  - Node size representation refinement for better visualization

- **Certification Study Timer**
  - Track study time for each certification
  - Set study goals and receive progress notifications
  - Generate study schedules based on exam dates
  - Study session analytics

- **Advanced Cost Calculator**
  - Currency conversion support
  - Exam retake costs calculation
  - Provider cost comparison
  - Bundle pricing analysis
  - Regional price variations

- **AI-Powered Study Guide**
  - Custom study guides generation
  - AI-generated practice questions
  - Personalized learning paths
  - Adaptive content difficulty

- **Community Features**
  - User certification reviews
  - Study group formation
  - Study material sharing
  - Success stories and testimonials
  - Mentor matching

- **Progress Tracking Dashboard**
  - Visual progress tracking
  - Certification completion timeline
  - Study milestone achievements
  - Skill gap analysis
  - Performance metrics

- **Mobile-Friendly Features**
  - Study progress notifications
  - Exam date reminders
  - Quick certification lookup
  - Mobile-optimized study materials

- **Certification Data Enrichment** (In Progress)
  - Automated online data verification
  - Historical version tracking
  - Change detection and review
  - Data quality monitoring
  - Automatic updates with admin approval

## Technical Architecture

### Data Flow
```mermaid
sequenceDiagram
    participant User
    participant UI as Streamlit Interface
    participant Filter as Filter System
    participant DB as PostgreSQL Database
    participant Viz as D3.js Visualization

    User->>UI: Select Filters
    UI->>Filter: Apply Criteria
    Filter->>DB: Query Certifications
    DB-->>Filter: Return Matches
    Filter-->>UI: Update Display
    UI->>Viz: Generate Path
    Viz-->>UI: Display Graph
```

### Components
- **Frontend**: Streamlit
- **Database**: PostgreSQL
- **Data Processing**: Python with SQLAlchemy
- **Visualization**: D3.js (planned)
- **Internationalization**: Flask-Babel, Babel

## Setup and Installation

1. Ensure Python 3.11+ is installed
2. Install required packages:
   ```bash
   pip install streamlit sqlalchemy psycopg2-binary flask-babel
   ```
3. Set up PostgreSQL database and environment variables
4. Run the application:
   ```bash
   streamlit run main.py
   ```

## Development

### Current Status
- Basic filtering system implemented
- Database schema established
- Data extraction pipeline working
- Multi-select domain filters added
- D3.js visualization with progress bar implemented
- Full internationalization support added
- Multiple language translations available

### Next Steps
1. Implement certification data enrichment system
2. Fix node sizing in D3.js visualization
3. Enhance graph interaction stability
4. Implement certification progression mapping
5. Enhance data extraction for prerequisites
6. Develop comparison features
7. Complete translations for all pages

### Known Issues
1. Node sizing based on certification difficulty needs improvement
2. Graph interaction stability needs enhancement

### Developer Guidance

#### Code Standards
We follow these Python Enhancement Proposals (PEPs) for code quality:

1. **PEP-8: Style Guide**
   - Use 4 spaces for indentation
   - Maximum line length of 79 characters
   - Two blank lines between top-level functions and classes
   - One blank line between methods within classes
   - Use snake_case for functions and variables
   - Use PascalCase for classes

2. **PEP-257: Docstring Conventions**
   - All modules, functions, classes, and methods must have docstrings
   - Use triple quotes for all docstrings
   - First line should be a summary
   - Multi-line docstrings must have summary line followed by blank line

3. **PEP-484: Type Hints**
   - Use type hints for all function arguments and return values
   - Use Optional[] for values that could be None
   - Use List[], Dict[], etc. from typing module
   - Include type hints in stub files for third-party modules

#### Refactoring Plan

1. **Phase 1: Code Style and Documentation** (2 weeks)
   - Add type hints to all functions
   - Update docstrings to follow PEP-257
   - Apply PEP-8 style guidelines
   - Create stub files for third-party modules

2. **Phase 2: Code Organization** (2 weeks)
   - Reorganize project structure
   - Split large modules into smaller ones
   - Create proper package hierarchy
   - Implement proper error handling

3. **Phase 3: Testing Infrastructure** (2 weeks)
   - Add unit tests for core functionality
   - Implement integration tests
   - Add test coverage reporting
   - Create testing documentation

4. **Phase 4: Performance Optimization** (1 week)
   - Profile code for bottlenecks
   - Optimize database queries
   - Implement caching where appropriate
   - Reduce unnecessary API calls

5. **Phase 5: Security Hardening** (1 week)
   - Security audit of dependencies
   - Implement input validation
   - Add rate limiting
   - Enhance error handling

#### Implementation Priorities
1. Core functionality must remain stable during refactoring
2. Changes must be backward compatible
3. Each phase must include thorough testing
4. Documentation must be updated with changes
5. Performance impact must be monitored


## Contributing

Please read our contributing guidelines for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Migration from Streamlit to React

We are in the process of migrating the frontend from Streamlit to React while maintaining FastAPI as the backend. For more information, see the [Migration Guide](MIGRATION.md).

### Docker Environment

All Docker-related files are located in the `.dockerwrapper` directory. The Docker setup supports both multi-container and single-container deployments.

To start the application in development mode:

```bash
./.dockerwrapper/run.sh up -d
```

This will start the following services:
- FastAPI backend
- Streamlit frontend (legacy)
- React frontend (new)
- PostgreSQL database
- Nginx reverse proxy

You can access the application at:
- Streamlit UI: http://localhost/
- React UI: http://localhost/new/
- FastAPI Swagger: http://localhost/api/docs

### Migration Helper

To help with the migration process, you can use the migration helper script:

```bash
# Show migration status
./.dockerwrapper/migrate-feature.sh --status

# Start working on a feature
./.dockerwrapper/migrate-feature.sh --start-api feature_name
```

For more information, see the [Migration Guide](MIGRATION.md).