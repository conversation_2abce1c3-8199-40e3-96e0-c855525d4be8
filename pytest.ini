[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test
python_functions = test_*
addopts = --cov=. --cov-report=term-missing --tb=short -v
pythonpath = .
# PostgreSQL test configuration
env =
    TEST_DATABASE_URL=postgresql://postgres:postgres@localhost:5432
    ENVIRONMENT=test
# Test markers
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests