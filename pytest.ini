[pytest]
# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Add options
addopts =
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=.
    --cov-report=html
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80
    --durations=10

# Python path
pythonpath = .

# PostgreSQL test configuration
env =
    TEST_DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test
    ENVIRONMENT=test
    DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test

# Test markers
markers =
    unit: Unit tests (fast, isolated)
    integration: Integration tests (database, external services)
    e2e: End-to-end tests (full user workflows)
    performance: Performance and load tests
    security: Security-focused tests
    slow: Tests that take longer than 5 seconds
    api: API endpoint tests
    database: Database-related tests
    auth: Authentication and authorization tests
    career_path: Career path functionality tests
    certification: Certification-related tests

# Filtering
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning