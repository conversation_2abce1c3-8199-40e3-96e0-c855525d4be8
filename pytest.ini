[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test
python_functions = test_*
addopts = --cov=. --cov-report=term-missing --cov-report=html --tb=short -v
pythonpath = .
# PostgreSQL test configuration
env =
    DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test
    TEST_DATABASE_URL=postgresql://postgres:postgres@localhost:5432/certrats_test
    ENVIRONMENT=test
    SECRET_KEY=test_secret_key_for_testing_only
    JWT_SECRET_KEY=test_jwt_secret_key_for_testing_only
    ANTHROPIC_API_KEY=test_anthropic_key
    OPENAI_API_KEY=test_openai_key
    API_V1_STR=/api/v1
    PROJECT_NAME=CertRats Test Environment
    ACCESS_TOKEN_EXPIRE_MINUTES=30
    ALGORITHM=HS256
    TESTING=true
    DEBUG=true
# Test markers
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    api: marks tests as API tests
    auth: marks tests as authentication tests
    database: marks tests as database tests
    performance: marks tests as performance tests