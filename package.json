{"name": "certrats", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@prisma/client": "^5.10.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@tanstack/react-query": "^5.22.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.344.0", "next": "14.2.26", "next-themes": "^0.4.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.50.1", "sonner": "^1.4.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.19", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "14.2.26", "postcss": "^8.4.35", "prisma": "^5.10.2", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}