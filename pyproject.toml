[project]
name = "repl-nix-workspace"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.11"
dependencies = [
    "beautifulsoup4>=4.13.3",
    "flask-migrate>=4.1.0",
    "flask-sqlalchemy>=3.1.1",
    "plotly>=6.0.0",
    "psycopg2-binary>=2.9.10",
    "sqlalchemy>=2.0.38",
    "streamlit-javascript>=0.1.5",
    "streamlit>=1.42.2",
    "trafilatura>=2.0.0",
    "twilio>=9.4.6",
    "openai>=1.63.2",
    "anthropic>=0.46.0",
    "flask-babel>=4.0.0",
    "babel>=2.17.0",
    "authlib>=1.4.1",
    "requests>=2.32.3",
    "flask-login>=0.6.3",
    "pypdf2>=3.0.1",
    "python-magic>=0.4.27",
    "scikit-learn>=1.6.1",
    "reportlab>=4.3.1",
    "numpy>=2.2.3",
    "pydantic>=2.10.6",
    "python-dotenv>=1.0.1",
    "fastapi>=0.115.8",
    "uvicorn>=0.34.0",
    "pytest>=8.3.4",
    "pytest-cov>=6.0.0",
    "pytest-mock>=3.14.0",
    "alembic>=1.14.1",
]
