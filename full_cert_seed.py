#!/usr/bin/env python3
"""
Script to seed all certifications from the normalized_certifications.json file
"""
import psycopg2
from psycopg2.extras import Json
import os
import json
import random

# Valid enum values based on database schema
VALID_TYPES = ['SECURITY', 'CLOUD', 'NETWORKING', 'DEVELOPMENT', 'DEVOPS', 'OTHER']
VALID_LEVELS = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']
VALID_DOMAINS = [
    'SECURITY_ENGINEERING', 'DEFENSIVE_SECURITY', 'SECURITY_MANAGEMENT', 
    'NETWORK_SECURITY', 'IDENTITY_ACCESS_MANAGEMENT', 'ASSET_SECURITY', 
    'SECURITY_TESTING', 'OFFENSIVE_SECURITY'
]

def main():
    """Run the script"""
    print("=== Full Certification Database Seed Script ===")
    
    # Connect to database
    db_url = "***************************************************"
    print(f"Connecting to database: {db_url}")
    
    try:
        conn = psycopg2.connect(db_url)
        cursor = conn.cursor()
        
        # Check current count
        cursor.execute("SELECT COUNT(*) FROM certification_explorer")
        current_count = cursor.fetchone()[0]
        print(f"Current certification count: {current_count}")
        
        # Clear current data
        if current_count > 0:
            print("Clearing existing certification data...")
            cursor.execute("TRUNCATE TABLE certification_explorer RESTART IDENTITY CASCADE")
            conn.commit()
        
        # Load certification data
        certifications = load_certifications()
        
        if not certifications:
            print("No certifications found to load. Exiting.")
            return
            
        print(f"Loaded {len(certifications)} certifications from data file")
        
        # Counters
        success_count = 0
        error_count = 0
        
        # Process each certification
        print("Inserting certifications...")
        
        for cert in certifications:
            try:
                # Map the certification data to valid values
                cert_type = map_certification_type(cert)
                level = map_certification_level(cert)
                domain = map_security_domain(cert)
                name = cert.get('name', '')[:255]  # Ensure name is not too long
                provider = get_provider(cert)[:100]  # Ensure provider is not too long
                description = cert.get('description', '')[:1000]  # Ensure description is not too long
                price = float(cert.get('cost', 0)) if cert.get('cost') else None
                prerequisites = cert.get('prerequisites', [])
                skills = []
                url = cert.get('url', '')[:255]  # Ensure URL is not too long
                
                # Insert certification
                cursor.execute("""
                    INSERT INTO certification_explorer
                    (name, provider, description, type, level, domain, price, prerequisites_list, skills_covered, url)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    name,
                    provider,
                    description,
                    cert_type,
                    level,
                    domain,
                    price,
                    Json(prerequisites) if prerequisites else Json([]),
                    Json(skills),
                    url
                ))
                
                success_count += 1
                
                # Commit every 50 records to avoid transaction timeouts
                if success_count % 50 == 0:
                    conn.commit()
                    print(f"Added {success_count} certifications so far...")
                
            except Exception as e:
                print(f"Error adding certification {cert.get('name', 'Unknown')}: {e}")
                error_count += 1
        
        # Final commit
        conn.commit()
        
        # Verify count
        cursor.execute("SELECT COUNT(*) FROM certification_explorer")
        final_count = cursor.fetchone()[0]
        print(f"Successfully added {success_count} certifications with {error_count} errors")
        print(f"Total certifications in database: {final_count}")
        
        # Close connection
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"An error occurred: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

def load_certifications():
    """Load certification data from JSON file"""
    # Try to load from various paths
    paths = [
        "data/normalized_certifications.json",
        "/app/data/normalized_certifications.json"
    ]
    
    for path in paths:
        try:
            print(f"Trying to load from {path}...")
            with open(path, "r") as f:
                data = json.load(f)
                if isinstance(data, dict):
                    # Convert dict to list if needed
                    cert_list = []
                    for name, details in data.items():
                        cert_data = details.copy()
                        cert_data['name'] = name
                        cert_list.append(cert_data)
                    return cert_list
                else:
                    return data
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Error loading from {path}: {e}")
    
    return []

def get_provider(cert):
    """Extract provider from certification data"""
    if 'organization' in cert and isinstance(cert['organization'], dict) and 'name' in cert['organization']:
        return cert['organization']['name']
    elif 'provider' in cert:
        return cert['provider']
    else:
        return 'Unknown'

def map_certification_type(cert):
    """Map certification data to valid type enum"""
    # Try to extract a category
    category = cert.get('category', '').lower() if cert.get('category') else ''
    
    # Map category to type
    if any(keyword in category for keyword in ['security', 'pentest', 'forensic', 'hacker']):
        return 'SECURITY'
    elif any(keyword in category for keyword in ['cloud', 'aws', 'azure', 'gcp']):
        return 'CLOUD'
    elif any(keyword in category for keyword in ['network', 'cisco', 'juniper']):
        return 'NETWORKING'
    elif any(keyword in category for keyword in ['dev', 'program', 'software', 'code']):
        return 'DEVELOPMENT'
    elif any(keyword in category for keyword in ['devops', 'ops', 'sre', 'reliability']):
        return 'DEVOPS'
    
    # If we can't determine the type, default to SECURITY
    return 'SECURITY'

def map_certification_level(cert):
    """Map certification data to valid level enum"""
    # Try to extract a level
    level = str(cert.get('level', '')).lower() if cert.get('level') is not None else ''
    difficulty = cert.get('difficulty')
    
    # Map based on difficulty if available
    if isinstance(difficulty, (int, float)):
        if difficulty <= 2:
            return 'BEGINNER'
        elif difficulty <= 3:
            return 'INTERMEDIATE'
        else:
            return 'ADVANCED'
    
    # Map based on level string
    if any(keyword in level for keyword in ['beginner', 'foundation', 'fundamental', 'basic']):
        return 'BEGINNER'
    elif any(keyword in level for keyword in ['intermediate', 'associate', 'practitioner']):
        return 'INTERMEDIATE'
    elif any(keyword in level for keyword in ['advanced', 'expert', 'professional', 'specialist']):
        return 'ADVANCED'
    elif any(keyword in level for keyword in ['expert', 'master', 'elite']):
        return 'EXPERT'
    
    # Default to INTERMEDIATE if we can't determine
    return 'INTERMEDIATE'

def map_security_domain(cert):
    """Map certification data to valid domain enum"""
    # Try to extract a domain
    domain = cert.get('domain', '').lower() if cert.get('domain') else ''
    name = cert.get('name', '').lower()
    
    # Map based on domain and name
    if any(keyword in domain or keyword in name for keyword in ['network', 'cisco', 'juniper']):
        return 'NETWORK_SECURITY'
    elif any(keyword in domain or keyword in name for keyword in ['offensive', 'pentest', 'hacker', 'exploit']):
        return 'OFFENSIVE_SECURITY'
    elif any(keyword in domain or keyword in name for keyword in ['defensive', 'defense', 'protection']):
        return 'DEFENSIVE_SECURITY'
    elif any(keyword in domain or keyword in name for keyword in ['management', 'governance', 'risk', 'compliance']):
        return 'SECURITY_MANAGEMENT'
    elif any(keyword in domain or keyword in name for keyword in ['identity', 'access', 'authentication']):
        return 'IDENTITY_ACCESS_MANAGEMENT'
    elif any(keyword in domain or keyword in name for keyword in ['test', 'assessment', 'audit']):
        return 'SECURITY_TESTING'
    elif any(keyword in domain or keyword in name for keyword in ['asset', 'data protection']):
        return 'ASSET_SECURITY'
    
    # Default to SECURITY_ENGINEERING if we can't determine
    return 'SECURITY_ENGINEERING'

if __name__ == "__main__":
    main() 