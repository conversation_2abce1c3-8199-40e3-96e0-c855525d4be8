"""
Shared API endpoints configuration for both backend and frontend

This file defines the centralized API endpoints that are used by both the backend (Python)
and frontend (React) code to ensure consistency between the two systems.

When the API routes change, update only this file and its JavaScript equivalent, 
and both systems will stay in sync.
"""
from typing import Dict, Any

# Base API version and prefix
API_VERSION = "v1"
API_PREFIX = f"/api/{API_VERSION}"

# All endpoint definitions structured as a dictionary
API_ENDPOINTS: Dict[str, Any] = {
    # Auth endpoints
    "AUTH": {
        "PREFIX": f"{API_PREFIX}/auth",
        "LOGIN": f"{API_PREFIX}/auth/login",
        "LOGOUT": f"{API_PREFIX}/auth/logout",
        "ME": f"{API_PREFIX}/auth/me",
    },
    
    # User endpoints
    "USERS": {
        "PREFIX": f"{API_PREFIX}/users",
        "PROFILE": f"{API_PREFIX}/users/profile",
        "PREFERENCES": f"{API_PREFIX}/users/preferences",
        "CERTIFICATIONS": f"{API_PREFIX}/users/certifications",
        "TUTORIAL": f"{API_PREFIX}/users/tutorial",
    },
    
    # Study endpoints
    "STUDY": {
        "PREFIX": f"{API_PREFIX}/study",
        "ESTIMATE": f"{API_PREFIX}/study/estimate",
        "PROGRESS": f"{API_PREFIX}/study/progress",
        "ROI": f"{API_PREFIX}/study/roi",
    },
    
    # Certification endpoints
    "CERTIFICATIONS": {
        "PREFIX": f"{API_PREFIX}/certifications",
        "LIST": f"{API_PREFIX}/certifications",
        "DETAIL": f"{API_PREFIX}/certifications/{{id}}",  # Use with .format(id=id)
        "RELATED": f"{API_PREFIX}/certifications/related",
        "COMPARE": f"{API_PREFIX}/certifications/compare",
        "PROVIDERS": f"{API_PREFIX}/certifications/providers",
    },
    
    # Career path endpoints
    "CAREER": {
        "PREFIX": f"{API_PREFIX}/career",
        "PATHS": f"{API_PREFIX}/career/paths",
        "RECOMMENDATIONS": f"{API_PREFIX}/career/recommendations",
        "ROLES": f"{API_PREFIX}/career/roles",
    },
    
    # Jobs endpoints
    "JOBS": {
        "PREFIX": f"{API_PREFIX}/jobs",
        "SEARCH": f"{API_PREFIX}/jobs/search",
        "DETAIL": f"{API_PREFIX}/jobs/{{id}}",  # Use with .format(id=id)
        "MARKET": f"{API_PREFIX}/jobs/market",
        "CERTIFICATIONS": f"{API_PREFIX}/jobs/{{id}}/certifications",  # Use with .format(id=id)
    },
    
    # Admin endpoints
    "ADMIN": {
        "PREFIX": f"{API_PREFIX}/admin",
        "CERTIFICATIONS": f"{API_PREFIX}/admin/certifications",
        "ORGANIZATIONS": f"{API_PREFIX}/admin/organizations",
        "FEEDBACK": f"{API_PREFIX}/admin/feedback",
        "TRANSLATIONS": f"{API_PREFIX}/admin/translations",
        "ENRICHMENT": f"{API_PREFIX}/admin/enrichment",
        "STATS": f"{API_PREFIX}/admin/stats",
        "JOBS": f"{API_PREFIX}/admin/jobs",
    },
    
    # Health check endpoint
    "HEALTH": f"{API_PREFIX}/health",
}

# Utility function to get a fully formatted endpoint with parameters
def get_endpoint(endpoint_path: str, **params) -> str:
    """
    Get a fully formatted endpoint with parameters replaced.
    
    Args:
        endpoint_path: The endpoint path with placeholders
        params: Parameters to replace in the URL
        
    Returns:
        str: Formatted endpoint with parameters
    """
    try:
        return endpoint_path.format(**params)
    except KeyError as e:
        raise ValueError(f"Missing parameter {e} for endpoint {endpoint_path}")

# Utility to sync with JavaScript version if needed
def generate_js_endpoints(output_path: str = "../shared/api_endpoints.js") -> None:
    """
    Generate the JavaScript version of this endpoints configuration.
    
    Args:
        output_path: Path to write the JavaScript file
    """
    import json
    
    js_content = [
        "/**",
        " * Shared API endpoints configuration for both backend and frontend",
        " * ",
        " * This file defines the centralized API endpoints that are used by both the backend (Python)",
        " * and frontend (React) code to ensure consistency between the two systems.",
        " * ",
        " * When the API routes change, update only this file, and both systems will stay in sync.",
        " */",
        "",
        f"// Base API version and prefix",
        f"const API_VERSION = '{API_VERSION}';",
        f"const API_PREFIX = `/api/${{API_VERSION}}`;",
        "",
        "// All endpoint definitions",
        "const API_ENDPOINTS = " + json.dumps(API_ENDPOINTS, indent=2).replace('"', "'") + ";",
        "",
        "// Export for different environments",
        "if (typeof module !== 'undefined' && module.exports) {",
        "  // Node.js/CommonJS export",
        "  module.exports = {",
        "    API_VERSION,",
        "    API_PREFIX,",
        "    API_ENDPOINTS",
        "  };",
        "} else if (typeof exports !== 'undefined') {",
        "  // ES modules export",
        "  exports.API_VERSION = API_VERSION;",
        "  exports.API_PREFIX = API_PREFIX;",
        "  exports.API_ENDPOINTS = API_ENDPOINTS;",
        "} else if (typeof window !== 'undefined') {",
        "  // Browser global",
        "  window.API_CONFIG = {",
        "    API_VERSION,",
        "    API_PREFIX,",
        "    API_ENDPOINTS",
        "  };",
        "}",
    ]
    
    with open(output_path, "w") as f:
        f.write("\n".join(js_content)) 