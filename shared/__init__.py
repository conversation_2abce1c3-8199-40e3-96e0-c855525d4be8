"""
Shared module for configuration and utilities used by both frontend and backend.

This module contains shared configuration, constants, and utilities that are used
by both the frontend React application and the backend Python API.
"""

# Make the API endpoints accessible from the shared module
try:
    from .api_endpoints import API_VERSION, API_PREFIX, API_ENDPOINTS, get_endpoint
except ImportError:
    # When first setting up the project, the api_endpoints.py file might not exist yet
    API_VERSION = "v1"
    API_PREFIX = f"/api/{API_VERSION}"
    API_ENDPOINTS = {}

    def get_endpoint(endpoint_path, **params):
        """Placeholder function when api_endpoints is not available"""
        return endpoint_path 