/**
 * Shared API endpoints configuration for both backend and frontend
 * 
 * This file defines the centralized API endpoints that are used by both the backend (Python)
 * and frontend (React) code to ensure consistency between the two systems.
 * 
 * When the API routes change, update only this file, and both systems will stay in sync.
 */

// Base API version and prefix
export const API_VERSION = 'v1';
export const API_PREFIX = `/api/${API_VERSION}`;

// Define endpoint types for better type safety
export interface EndpointGroup {
  PREFIX: string;
  [key: string]: string;
}

export interface ApiEndpoints {
  AUTH: EndpointGroup;
  USERS: EndpointGroup;
  STUDY: EndpointGroup;
  CERTIFICATIONS: EndpointGroup;
  CAREER: EndpointGroup;
  JOBS: EndpointGroup;
  ADMIN: EndpointGroup;
  HEALTH: string;
}

// All endpoint definitions
export const API_ENDPOINTS: ApiEndpoints = {
  // Auth endpoints
  AUTH: {
    PREFIX: `${API_PREFIX}/auth`,
    LOGIN: `${API_PREFIX}/auth/login`,
    LOGOUT: `${API_PREFIX}/auth/logout`,
    ME: `${API_PREFIX}/auth/me`,
  },
  
  // User endpoints
  USERS: {
    PREFIX: `${API_PREFIX}/users`,
    PROFILE: `${API_PREFIX}/users/profile`,
    PREFERENCES: `${API_PREFIX}/users/preferences`,
    CERTIFICATIONS: `${API_PREFIX}/users/certifications`,
    TUTORIAL: `${API_PREFIX}/users/tutorial`,
  },
  
  // Study endpoints
  STUDY: {
    PREFIX: `${API_PREFIX}/study`,
    ESTIMATE: `${API_PREFIX}/study/estimate`,
    PROGRESS: `${API_PREFIX}/study/progress`,
    ROI: `${API_PREFIX}/study/roi`,
  },
  
  // Certification endpoints
  CERTIFICATIONS: {
    PREFIX: `${API_PREFIX}/certifications`,
    LIST: `${API_PREFIX}/certifications`,
    DETAIL: `${API_PREFIX}/certifications/:id`, // Replace :id with actual id
    RELATED: `${API_PREFIX}/certifications/related`,
    COMPARE: `${API_PREFIX}/certifications/compare`,
    PROVIDERS: `${API_PREFIX}/certifications/providers`,
  },
  
  // Career path endpoints
  CAREER: {
    PREFIX: `${API_PREFIX}/career`,
    PATHS: `${API_PREFIX}/career/paths`,
    RECOMMENDATIONS: `${API_PREFIX}/career/recommendations`,
    ROLES: `${API_PREFIX}/career/roles`,
  },
  
  // Jobs endpoints
  JOBS: {
    PREFIX: `${API_PREFIX}/jobs`,
    SEARCH: `${API_PREFIX}/jobs/search`,
    DETAIL: `${API_PREFIX}/jobs/:id`, // Replace :id with actual id
    MARKET: `${API_PREFIX}/jobs/market`,
    CERTIFICATIONS: `${API_PREFIX}/jobs/:id/certifications`, // Replace :id with actual id
  },
  
  // Admin endpoints
  ADMIN: {
    PREFIX: `${API_PREFIX}/admin`,
    CERTIFICATIONS: `${API_PREFIX}/admin/certifications`,
    ORGANIZATIONS: `${API_PREFIX}/admin/organizations`,
    FEEDBACK: `${API_PREFIX}/admin/feedback`,
    TRANSLATIONS: `${API_PREFIX}/admin/translations`,
    ENRICHMENT: `${API_PREFIX}/admin/enrichment`,
    STATS: `${API_PREFIX}/admin/stats`,
    JOBS: `${API_PREFIX}/admin/jobs`,
  },
  
  // Health check endpoint
  HEALTH: `${API_PREFIX}/health`,
};

// Utility function to format endpoints with parameters
export function formatEndpoint(endpoint: string, params: Record<string, string | number>): string {
  let formattedEndpoint = endpoint;
  
  Object.entries(params).forEach(([key, value]) => {
    formattedEndpoint = formattedEndpoint.replace(`:${key}`, value.toString());
  });
  
  return formattedEndpoint;
}

export default API_ENDPOINTS; 