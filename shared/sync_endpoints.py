#!/usr/bin/env python3
"""
API Endpoints Synchronization Tool

This script ensures that the API endpoints are kept in sync between
the Python backend and JavaScript/TypeScript frontend.

Usage:
    python sync_endpoints.py --check   # Check if endpoints are in sync
    python sync_endpoints.py --sync    # Sync endpoints from Python to JS/TS
"""
import argparse
import os
import sys
import json
import importlib.util
from typing import Dict, Any, List, Tuple

# Set up paths
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)

# Paths to endpoint files
PYTHON_ENDPOINTS_PATH = os.path.join(SCRIPT_DIR, "api_endpoints.py")
JS_ENDPOINTS_PATH = os.path.join(SCRIPT_DIR, "api_endpoints.js")
TS_ENDPOINTS_PATH = os.path.join(SCRIPT_DIR, "api_endpoints.ts")


def load_python_endpoints() -> Dict[str, Any]:
    """Load the Python API endpoints"""
    # Use importlib to load the module without adding to sys.path
    spec = importlib.util.spec_from_file_location("api_endpoints", PYTHON_ENDPOINTS_PATH)
    if not spec or not spec.loader:
        raise ImportError(f"Could not load Python endpoints from {PYTHON_ENDPOINTS_PATH}")
    
    api_endpoints_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(api_endpoints_module)
    
    return {
        "API_VERSION": api_endpoints_module.API_VERSION,
        "API_PREFIX": api_endpoints_module.API_PREFIX,
        "API_ENDPOINTS": api_endpoints_module.API_ENDPOINTS
    }


def load_js_endpoints() -> Dict[str, Any]:
    """Load the JavaScript API endpoints"""
    import re
    
    if not os.path.exists(JS_ENDPOINTS_PATH):
        return None
    
    with open(JS_ENDPOINTS_PATH, 'r') as f:
        js_content = f.read()
    
    # Extract API_VERSION
    version_match = re.search(r'const API_VERSION = [\'"]([^\'"]+)[\'"]', js_content)
    version = version_match.group(1) if version_match else None
    
    # Extract API_PREFIX
    prefix_match = re.search(r'const API_PREFIX = [\'"]([^\'"]+)[\'"]', js_content)
    prefix = prefix_match.group(1) if prefix_match else None
    
    # Extract API_ENDPOINTS
    endpoints_match = re.search(r'const API_ENDPOINTS = ({[^;]+});', js_content, re.DOTALL)
    endpoints_str = endpoints_match.group(1) if endpoints_match else None
    
    if not all([version, prefix, endpoints_str]):
        return None
    
    # Convert JS object string to Python dict (very basic conversion)
    # This is a simplified approach and might need improvements for complex JS objects
    endpoints_str = endpoints_str.replace("'", '"')
    try:
        endpoints = json.loads(endpoints_str)
    except json.JSONDecodeError:
        print("Error: Could not parse JS endpoints. Format might be too complex for this script.")
        return None
    
    return {
        "API_VERSION": version,
        "API_PREFIX": prefix,
        "API_ENDPOINTS": endpoints
    }


def load_ts_endpoints() -> Dict[str, Any]:
    """Load the TypeScript API endpoints"""
    import re
    
    if not os.path.exists(TS_ENDPOINTS_PATH):
        return None
    
    with open(TS_ENDPOINTS_PATH, 'r') as f:
        ts_content = f.read()
    
    # Extract API_VERSION
    version_match = re.search(r'export const API_VERSION = [\'"]([^\'"]+)[\'"]', ts_content)
    version = version_match.group(1) if version_match else None
    
    # Extract API_PREFIX
    prefix_match = re.search(r'export const API_PREFIX = [\'"]([^\'"]+)[\'"]', ts_content)
    prefix = prefix_match.group(1) if prefix_match else None
    
    # Extract API_ENDPOINTS
    endpoints_match = re.search(r'export const API_ENDPOINTS[^=]*= ({[^;]+};)', ts_content, re.DOTALL)
    endpoints_str = endpoints_match.group(1) if endpoints_match else None
    
    if not all([version, prefix, endpoints_str]):
        return None
    
    # Convert TS object string to Python dict (very basic conversion)
    # This is a simplified approach and might need improvements for complex TS objects
    endpoints_str = endpoints_str.replace("'", '"')
    
    # Remove trailing semicolon
    endpoints_str = endpoints_str.rstrip(';')
    
    try:
        endpoints = json.loads(endpoints_str)
    except json.JSONDecodeError:
        print("Error: Could not parse TS endpoints. Format might be too complex for this script.")
        return None
    
    return {
        "API_VERSION": version,
        "API_PREFIX": prefix,
        "API_ENDPOINTS": endpoints
    }


def compare_endpoints() -> List[str]:
    """Compare the Python, JS, and TS endpoints and return a list of differences"""
    py_endpoints = load_python_endpoints()
    js_endpoints = load_js_endpoints()
    ts_endpoints = load_ts_endpoints()
    
    differences = []
    
    # Check if files exist
    if not js_endpoints:
        differences.append("JavaScript endpoints file missing or invalid")
    if not ts_endpoints:
        differences.append("TypeScript endpoints file missing or invalid")
    
    if not js_endpoints or not ts_endpoints:
        return differences
    
    # Compare versions
    if py_endpoints["API_VERSION"] != js_endpoints["API_VERSION"]:
        differences.append(f"API_VERSION mismatch: Python={py_endpoints['API_VERSION']} JS={js_endpoints['API_VERSION']}")
    if py_endpoints["API_VERSION"] != ts_endpoints["API_VERSION"]:
        differences.append(f"API_VERSION mismatch: Python={py_endpoints['API_VERSION']} TS={ts_endpoints['API_VERSION']}")
    
    # Compare prefixes
    if py_endpoints["API_PREFIX"] != js_endpoints["API_PREFIX"]:
        differences.append(f"API_PREFIX mismatch: Python={py_endpoints['API_PREFIX']} JS={js_endpoints['API_PREFIX']}")
    if py_endpoints["API_PREFIX"] != ts_endpoints["API_PREFIX"]:
        differences.append(f"API_PREFIX mismatch: Python={py_endpoints['API_PREFIX']} TS={ts_endpoints['API_PREFIX']}")
    
    # Compare endpoint structures (limited to top-level keys for simplicity)
    py_keys = set(py_endpoints["API_ENDPOINTS"].keys())
    js_keys = set(js_endpoints["API_ENDPOINTS"].keys())
    ts_keys = set(ts_endpoints["API_ENDPOINTS"].keys())
    
    if py_keys != js_keys:
        differences.append(f"API_ENDPOINTS structure mismatch between Python and JS: {py_keys.symmetric_difference(js_keys)}")
    if py_keys != ts_keys:
        differences.append(f"API_ENDPOINTS structure mismatch between Python and TS: {py_keys.symmetric_difference(ts_keys)}")
    
    return differences


def sync_endpoints() -> bool:
    """Sync endpoints from Python to JS and TS"""
    try:
        # Import Python endpoints
        from api_endpoints import API_VERSION, API_PREFIX, API_ENDPOINTS, generate_js_endpoints
        
        # Generate JavaScript file
        generate_js_endpoints(JS_ENDPOINTS_PATH)
        print(f"✅ Updated JavaScript endpoints at {JS_ENDPOINTS_PATH}")
        
        # Generate TypeScript file
        # Create TypeScript content - a more sophisticated version of the JS content
        ts_content = [
            '/**',
            ' * Shared API endpoints configuration for both backend and frontend',
            ' * ',
            ' * This file defines the centralized API endpoints that are used by both the backend (Python)',
            ' * and frontend (React) code to ensure consistency between the two systems.',
            ' * ',
            ' * When the API routes change, update only this file, and both systems will stay in sync.',
            ' */',
            '',
            f'// Base API version and prefix',
            f'export const API_VERSION = \'{API_VERSION}\';',
            f'export const API_PREFIX = `/api/{API_VERSION}\';',
            '',
            '// Define endpoint types for better type safety',
            'export interface EndpointGroup {',
            '  PREFIX: string;',
            '  [key: string]: string;',
            '}',
            '',
            'export interface ApiEndpoints {',
        ]
        
        # Add interface properties
        for key in API_ENDPOINTS:
            if key == "HEALTH":
                ts_content.append(f'  {key}: string;')
            else:
                ts_content.append(f'  {key}: EndpointGroup;')
        
        # Define the endpoints and utility functions
        endpoints_json = json.dumps(API_ENDPOINTS, indent=2).replace('"', '\'')
        ts_content.extend([
            '}',
            '',
            '// All endpoint definitions',
            f'export const API_ENDPOINTS: ApiEndpoints = {endpoints_json}',
            '',
            '// Utility function to format endpoints with parameters',
            'export function formatEndpoint(endpoint: string, params: Record<string, string | number>): string {',
            '  let formattedEndpoint = endpoint;',
            '  ',
            '  Object.entries(params).forEach(([key, value]) => {',
            '    formattedEndpoint = formattedEndpoint.replace(`:${key}`, value.toString());',
            '  });',
            '  ',
            '  return formattedEndpoint;',
            '}',
            '',
            'export default API_ENDPOINTS;'
        ])
        
        # Write the TypeScript file
        with open(TS_ENDPOINTS_PATH, 'w') as f:
            f.write('\n'.join(ts_content))
        
        print(f"✅ Updated TypeScript endpoints at {TS_ENDPOINTS_PATH}")
        
        return True
    except Exception as e:
        print(f"❌ Error syncing endpoints: {str(e)}")
        return False


def main() -> int:
    """Main entry point"""
    parser = argparse.ArgumentParser(description="API Endpoints Synchronization Tool")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--check", action="store_true", help="Check if endpoints are in sync")
    group.add_argument("--sync", action="store_true", help="Sync endpoints from Python to JS/TS")
    
    args = parser.parse_args()
    
    if args.check:
        differences = compare_endpoints()
        if differences:
            print("❌ Endpoints are out of sync:")
            for diff in differences:
                print(f"  - {diff}")
            print("\nRun 'python sync_endpoints.py --sync' to synchronize them.")
            return 1
        else:
            print("✅ All endpoints are in sync.")
            return 0
    
    if args.sync:
        success = sync_endpoints()
        return 0 if success else 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main()) 