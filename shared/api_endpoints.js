/**
 * Shared API endpoints configuration for both backend and frontend
 * 
 * This file defines the centralized API endpoints that are used by both the backend (Python)
 * and frontend (React) code to ensure consistency between the two systems.
 * 
 * When the API routes change, update only this file, and both systems will stay in sync.
 */

// Base API version and prefix
const API_VERSION = 'v1';
const API_PREFIX = `/api/${API_VERSION}`;

// All endpoint definitions
const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    PREFIX: `${API_PREFIX}/auth`,
    LOGIN: `${API_PREFIX}/auth/login`,
    LOGOUT: `${API_PREFIX}/auth/logout`,
    ME: `${API_PREFIX}/auth/me`,
  },
  
  // User endpoints
  USERS: {
    PREFIX: `${API_PREFIX}/users`,
    PROFILE: `${API_PREFIX}/users/profile`,
    PREFERENCES: `${API_PREFIX}/users/preferences`,
    CERTIFICATIONS: `${API_PREFIX}/users/certifications`,
    TUTORIAL: `${API_PREFIX}/users/tutorial`,
  },
  
  // Study endpoints
  STUDY: {
    PREFIX: `${API_PREFIX}/study`,
    ESTIMATE: `${API_PREFIX}/study/estimate`,
    PROGRESS: `${API_PREFIX}/study/progress`,
    ROI: `${API_PREFIX}/study/roi`,
  },
  
  // Certification endpoints
  CERTIFICATIONS: {
    PREFIX: `${API_PREFIX}/certifications`,
    LIST: `${API_PREFIX}/certifications`,
    DETAIL: `${API_PREFIX}/certifications/:id`, // Replace :id with actual id
    RELATED: `${API_PREFIX}/certifications/related`,
    COMPARE: `${API_PREFIX}/certifications/compare`,
    PROVIDERS: `${API_PREFIX}/certifications/providers`,
  },
  
  // Career path endpoints
  CAREER: {
    PREFIX: `${API_PREFIX}/career`,
    PATHS: `${API_PREFIX}/career/paths`,
    RECOMMENDATIONS: `${API_PREFIX}/career/recommendations`,
    ROLES: `${API_PREFIX}/career/roles`,
  },
  
  // Jobs endpoints
  JOBS: {
    PREFIX: `${API_PREFIX}/jobs`,
    SEARCH: `${API_PREFIX}/jobs/search`,
    DETAIL: `${API_PREFIX}/jobs/:id`, // Replace :id with actual id
    MARKET: `${API_PREFIX}/jobs/market`,
    CERTIFICATIONS: `${API_PREFIX}/jobs/:id/certifications`, // Replace :id with actual id
  },
  
  // Admin endpoints
  ADMIN: {
    PREFIX: `${API_PREFIX}/admin`,
    CERTIFICATIONS: `${API_PREFIX}/admin/certifications`,
    ORGANIZATIONS: `${API_PREFIX}/admin/organizations`,
    FEEDBACK: `${API_PREFIX}/admin/feedback`,
    TRANSLATIONS: `${API_PREFIX}/admin/translations`,
    ENRICHMENT: `${API_PREFIX}/admin/enrichment`,
    STATS: `${API_PREFIX}/admin/stats`,
    JOBS: `${API_PREFIX}/admin/jobs`,
  },
  
  // Health check endpoint
  HEALTH: `${API_PREFIX}/health`,
};

// Export for different environments
if (typeof module !== 'undefined' && module.exports) {
  // Node.js/CommonJS export
  module.exports = {
    API_VERSION,
    API_PREFIX,
    API_ENDPOINTS
  };
} else if (typeof exports !== 'undefined') {
  // ES modules export
  exports.API_VERSION = API_VERSION;
  exports.API_PREFIX = API_PREFIX;
  exports.API_ENDPOINTS = API_ENDPOINTS;
} else if (typeof window !== 'undefined') {
  // Browser global
  window.API_CONFIG = {
    API_VERSION,
    API_PREFIX,
    API_ENDPOINTS
  };
} 