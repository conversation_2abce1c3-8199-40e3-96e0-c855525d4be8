# Shared API Endpoints System

This directory contains shared configurations and utilities that are used by both the frontend (React) and backend (Python) components of the CertRats application.

## API Endpoints

The most important feature is the centralized API endpoints configuration. This ensures that both the frontend and backend use the same API routes, and when a route changes, it only needs to be updated in one place.

### Files

- `api_endpoints.py` - Python version of the API endpoints configuration
- `api_endpoints.ts` - TypeScript version with type safety for React
- `api_endpoints.js` - JavaScript version (alternative to TypeScript)
- `sync_endpoints.py` - Utility to keep the endpoints files in sync

### Using in Python (Backend)

```python
# Import the API configuration
from shared.api_endpoints import API_VERSION, API_PREFIX, API_ENDPOINTS

# Get a specific endpoint
certifications_list_endpoint = API_ENDPOINTS["CERTIFICATIONS"]["LIST"]  # "/api/v1/certifications"

# For endpoints with parameters (e.g., "/api/v1/certifications/{id}")
from shared.api_endpoints import get_endpoint
detail_endpoint = get_endpoint(API_ENDPOINTS["CERTIFICATIONS"]["DETAIL"], id=123)
```

### Using in TypeScript/React (Frontend)

```typescript
// Import the API configuration
import { API_VERSION, API_PREFIX, API_ENDPOINTS, formatEndpoint } from '../../shared/api_endpoints';

// Get a specific endpoint
const certificationsListEndpoint = API_ENDPOINTS.CERTIFICATIONS.LIST;  // "/api/v1/certifications"

// For endpoints with parameters (e.g., "/api/v1/certifications/:id")
const detailEndpoint = formatEndpoint(API_ENDPOINTS.CERTIFICATIONS.DETAIL, { id: 123 });
```

## Keeping Endpoints in Sync

When you need to modify the API endpoints, follow these steps:

1. Make changes to the Python version first (`api_endpoints.py`)
2. Run the sync script to update the JavaScript and TypeScript versions:

   ```bash
   python shared/sync_endpoints.py --sync
   ```

3. Verify that the changes are correct and run any needed tests

## Converting Existing Code

We provide utility scripts to automate the conversion of existing code to use the shared endpoints:

### For Python Backend

```bash
python api/update_api_endpoints.py
```

This will scan the API code for imports of the old `api.constants` module and update them to use the shared endpoints.

### For React Frontend

```bash
node frontend/scripts/update_api_endpoints.js
```

This will scan the frontend code for hardcoded API paths and import statements and update them to use the shared endpoints.

## Benefits

Using the shared API endpoints system provides several advantages:

1. **Single Source of Truth**: All API routes are defined in one place
2. **Consistency**: Frontend and backend use exactly the same routes
3. **Type Safety**: The TypeScript version provides type checking for frontend code
4. **Maintainability**: When a route changes, it only needs to be updated once
5. **Self-Documentation**: The endpoints configuration serves as documentation of available API routes

## Future Improvements

Possible improvements to the system:

1. Set up CI/CD checks to ensure endpoints stay in sync
2. Generate API client code from the endpoints configuration
3. Add OpenAPI/Swagger integration 