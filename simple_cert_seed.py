#!/usr/bin/env python3
"""
Simple script to seed certifications using only basic SQL statements
"""
import psycopg2
from psycopg2.extras import Json
import os
import json

# Valid enum values based on database schema
VALID_TYPES = ['SECURITY', 'CLOUD', 'NETWORKING', 'DEVELOPMENT', 'DEVOPS', 'OTHER']
VALID_LEVELS = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']
VALID_DOMAINS = [
    'SECURITY_ENGINEERING', 'DEFENSIVE_SECURITY', 'SECURITY_MANAGEMENT', 
    'NETWORK_SECURITY', 'IDENTITY_ACCESS_MANAGEMENT', 'ASSET_SECURITY', 
    'SECURITY_TESTING', 'OFFENSIVE_SECURITY'
]

def main():
    """Run the script"""
    print("=== Simple Certification Database Seed Script ===")
    
    # Connect to database
    db_url = "***************************************************"
    print(f"Connecting to database: {db_url}")
    
    try:
        conn = psycopg2.connect(db_url)
        cursor = conn.cursor()
        
        # Check current count
        cursor.execute("SELECT COUNT(*) FROM certification_explorer")
        current_count = cursor.fetchone()[0]
        print(f"Current certification count: {current_count}")
        
        # Clear current data
        if current_count > 0:
            print("Clearing existing certification data...")
            cursor.execute("TRUNCATE TABLE certification_explorer RESTART IDENTITY CASCADE")
            conn.commit()
        
        # Create sample certifications
        sample_certifications = [
            {
                "name": "Certified Ethical Hacker (CEH)",
                "provider": "EC-Council",
                "description": "The Certified Ethical Hacker certification validates skills in finding vulnerabilities in systems.",
                "type": "SECURITY",
                "level": "INTERMEDIATE",
                "domain": "SECURITY_TESTING",
                "price": 1200.00,
                "prerequisites_list": ["Network security knowledge"],
                "skills_covered": ["penetration testing", "vulnerability assessment"],
                "url": "https://www.eccouncil.org/programs/certified-ethical-hacker-ceh/"
            },
            {
                "name": "CompTIA Security+",
                "provider": "CompTIA",
                "description": "CompTIA Security+ is a global certification that validates baseline skills in cybersecurity.",
                "type": "SECURITY",
                "level": "BEGINNER",
                "domain": "SECURITY_ENGINEERING",
                "price": 349.00,
                "prerequisites_list": [],
                "skills_covered": ["basic security concepts", "cryptography"],
                "url": "https://www.comptia.org/certifications/security"
            },
            {
                "name": "Certified Information Systems Security Professional (CISSP)",
                "provider": "ISC2",
                "description": "The CISSP validates advanced knowledge across various security domains.",
                "type": "SECURITY",
                "level": "ADVANCED",
                "domain": "SECURITY_MANAGEMENT",
                "price": 749.00,
                "prerequisites_list": ["5 years experience"],
                "skills_covered": ["security management", "risk assessment"],
                "url": "https://www.isc2.org/Certifications/CISSP"
            },
            {
                "name": "AWS Certified Security - Specialty",
                "provider": "Amazon Web Services",
                "description": "The AWS Security Specialty certification validates expertise in AWS security services.",
                "type": "CLOUD",
                "level": "ADVANCED",
                "domain": "SECURITY_ENGINEERING",
                "price": 300.00,
                "prerequisites_list": ["AWS knowledge"],
                "skills_covered": ["cloud security", "IAM"],
                "url": "https://aws.amazon.com/certification/certified-security-specialty/"
            },
            {
                "name": "Certified Information Security Manager (CISM)",
                "provider": "ISACA",
                "description": "CISM focuses on security management for IT professionals.",
                "type": "SECURITY",
                "level": "ADVANCED",
                "domain": "SECURITY_MANAGEMENT",
                "price": 760.00,
                "prerequisites_list": ["5 years experience"],
                "skills_covered": ["security management", "governance"],
                "url": "https://www.isaca.org/credentialing/cism"
            },
            {
                "name": "Offensive Security Certified Professional (OSCP)",
                "provider": "Offensive Security",
                "description": "The OSCP is a hands-on penetration testing certification.",
                "type": "SECURITY",
                "level": "ADVANCED",
                "domain": "OFFENSIVE_SECURITY",
                "price": 999.00,
                "prerequisites_list": ["Penetration testing knowledge"],
                "skills_covered": ["penetration testing", "exploitation"],
                "url": "https://www.offensive-security.com/pwk-oscp/"
            },
            {
                "name": "Certified Cloud Security Professional (CCSP)",
                "provider": "ISC2",
                "description": "CCSP validates expertise in cloud security design, implementation, and management.",
                "type": "CLOUD",
                "level": "ADVANCED",
                "domain": "SECURITY_ENGINEERING",
                "price": 749.00,
                "prerequisites_list": ["Cloud experience"],
                "skills_covered": ["cloud security", "risk management"],
                "url": "https://www.isc2.org/Certifications/CCSP"
            },
            {
                "name": "GIAC Security Essentials (GSEC)",
                "provider": "GIAC",
                "description": "GSEC validates understanding of security tasks and terminology.",
                "type": "SECURITY",
                "level": "INTERMEDIATE",
                "domain": "SECURITY_ENGINEERING",
                "price": 2499.00,
                "prerequisites_list": [],
                "skills_covered": ["network security", "host security"],
                "url": "https://www.giac.org/certification/security-essentials-gsec"
            },
            {
                "name": "Certified Information Systems Auditor (CISA)",
                "provider": "ISACA",
                "description": "CISA validates skills in information systems auditing, control, and security.",
                "type": "SECURITY",
                "level": "ADVANCED",
                "domain": "SECURITY_MANAGEMENT",
                "price": 760.00,
                "prerequisites_list": ["Audit experience"],
                "skills_covered": ["auditing", "compliance"],
                "url": "https://www.isaca.org/credentialing/cisa"
            },
            {
                "name": "Microsoft Certified: Azure Security Engineer Associate",
                "provider": "Microsoft",
                "description": "Validates skills in implementing security controls and threat protection in Azure.",
                "type": "CLOUD",
                "level": "INTERMEDIATE",
                "domain": "SECURITY_ENGINEERING",
                "price": 165.00,
                "prerequisites_list": ["Azure knowledge"],
                "skills_covered": ["cloud security", "identity management"],
                "url": "https://learn.microsoft.com/en-us/certifications/azure-security-engineer/"
            }
        ]
        
        # Insert sample certifications
        print("Inserting sample certifications...")
        for cert in sample_certifications:
            cursor.execute("""
                INSERT INTO certification_explorer
                (name, provider, description, type, level, domain, price, prerequisites_list, skills_covered, url)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                cert["name"],
                cert["provider"],
                cert["description"],
                cert["type"],
                cert["level"],
                cert["domain"],
                cert["price"],
                Json(cert["prerequisites_list"]),
                Json(cert["skills_covered"]),
                cert["url"]
            ))
        
        # Commit changes
        conn.commit()
        
        # Verify count
        cursor.execute("SELECT COUNT(*) FROM certification_explorer")
        final_count = cursor.fetchone()[0]
        print(f"Successfully added {final_count} sample certifications")
        
        # Close connection
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"An error occurred: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    main() 